// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/data_collector/rewards/service.proto

package rewards

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	casper "github.com/epifi/gamma/api/casper"

	exchanger "github.com/epifi/gamma/api/casper/exchanger"

	external_vendor_redemption "github.com/epifi/gamma/api/casper/external_vendor_redemption"

	redemption "github.com/epifi/gamma/api/casper/redemption"

	rewards "github.com/epifi/gamma/api/rewards"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = casper.CardOfferType(0)

	_ = exchanger.ExchangerOfferOrderState(0)

	_ = external_vendor_redemption.Category(0)

	_ = redemption.OfferRedemptionState(0)

	_ = rewards.RewardType(0)
)

// Validate checks the field values on GetRewardOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardOffersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardOffersRequestMultiError, or nil if none found.
func (m *GetRewardOffersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOffersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetRewardOffersRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOffersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOffersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOffersRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FetchOffersOnlyApplicableToUser

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOffersRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOffersRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOffersRequestValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOffersRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOffersRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOffersRequestValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRewardOffersRequestMultiError(errors)
	}

	return nil
}

// GetRewardOffersRequestMultiError is an error wrapping multiple validation
// errors returned by GetRewardOffersRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRewardOffersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOffersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOffersRequestMultiError) AllErrors() []error { return m }

// GetRewardOffersRequestValidationError is the validation error returned by
// GetRewardOffersRequest.Validate if the designated constraints aren't met.
type GetRewardOffersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOffersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOffersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOffersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOffersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOffersRequestValidationError) ErrorName() string {
	return "GetRewardOffersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOffersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOffersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOffersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOffersRequestValidationError{}

// Validate checks the field values on GetRewardOffersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardOffersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOffersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardOffersResponseMultiError, or nil if none found.
func (m *GetRewardOffersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOffersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOffersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSherlockDeepLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOffersResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOffersResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSherlockDeepLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOffersResponseValidationError{
				field:  "SherlockDeepLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRewardOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardOffersResponseValidationError{
						field:  fmt.Sprintf("RewardOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardOffersResponseValidationError{
						field:  fmt.Sprintf("RewardOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardOffersResponseValidationError{
					field:  fmt.Sprintf("RewardOffers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRewardOffersResponseMultiError(errors)
	}

	return nil
}

// GetRewardOffersResponseMultiError is an error wrapping multiple validation
// errors returned by GetRewardOffersResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRewardOffersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOffersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOffersResponseMultiError) AllErrors() []error { return m }

// GetRewardOffersResponseValidationError is the validation error returned by
// GetRewardOffersResponse.Validate if the designated constraints aren't met.
type GetRewardOffersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOffersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOffersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOffersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOffersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOffersResponseValidationError) ErrorName() string {
	return "GetRewardOffersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOffersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOffersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOffersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOffersResponseValidationError{}

// Validate checks the field values on GetOffersRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetOffersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOffersRequestMultiError, or nil if none found.
func (m *GetOffersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOffersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetOffersRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RedemptionMode

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardOffersType

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersRequestValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersRequestValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOffersRequestMultiError(errors)
	}

	return nil
}

// GetOffersRequestMultiError is an error wrapping multiple validation errors
// returned by GetOffersRequest.ValidateAll() if the designated constraints
// aren't met.
type GetOffersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOffersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOffersRequestMultiError) AllErrors() []error { return m }

// GetOffersRequestValidationError is the validation error returned by
// GetOffersRequest.Validate if the designated constraints aren't met.
type GetOffersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOffersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOffersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOffersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOffersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOffersRequestValidationError) ErrorName() string { return "GetOffersRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetOffersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOffersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOffersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOffersRequestValidationError{}

// Validate checks the field values on GetOffersResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetOffersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOffersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOffersResponseMultiError, or nil if none found.
func (m *GetOffersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOffersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSherlockDeepLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSherlockDeepLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersResponseValidationError{
				field:  "SherlockDeepLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOffersResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOffersResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOffersResponseValidationError{
					field:  fmt.Sprintf("Offers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOffersResponseMultiError(errors)
	}

	return nil
}

// GetOffersResponseMultiError is an error wrapping multiple validation errors
// returned by GetOffersResponse.ValidateAll() if the designated constraints
// aren't met.
type GetOffersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOffersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOffersResponseMultiError) AllErrors() []error { return m }

// GetOffersResponseValidationError is the validation error returned by
// GetOffersResponse.Validate if the designated constraints aren't met.
type GetOffersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOffersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOffersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOffersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOffersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOffersResponseValidationError) ErrorName() string {
	return "GetOffersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOffersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOffersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOffersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOffersResponseValidationError{}

// Validate checks the field values on GetUserRewardsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserRewardsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserRewardsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserRewardsRequestMultiError, or nil if none found.
func (m *GetUserRewardsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserRewardsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetUserRewardsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRewardsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRewardsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRewardsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RewardId

	// no validation rules for RewardType

	// no validation rules for VisibilityType

	if all {
		switch v := interface{}(m.GetStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRewardsRequestValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRewardsRequestValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRewardsRequestValidationError{
				field:  "StartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRewardsRequestValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRewardsRequestValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRewardsRequestValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRewardsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRewardsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRewardsRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RewardOfferId

	if len(errors) > 0 {
		return GetUserRewardsRequestMultiError(errors)
	}

	return nil
}

// GetUserRewardsRequestMultiError is an error wrapping multiple validation
// errors returned by GetUserRewardsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserRewardsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserRewardsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserRewardsRequestMultiError) AllErrors() []error { return m }

// GetUserRewardsRequestValidationError is the validation error returned by
// GetUserRewardsRequest.Validate if the designated constraints aren't met.
type GetUserRewardsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserRewardsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserRewardsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserRewardsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserRewardsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserRewardsRequestValidationError) ErrorName() string {
	return "GetUserRewardsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserRewardsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserRewardsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserRewardsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserRewardsRequestValidationError{}

// Validate checks the field values on GetUserRewardsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserRewardsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserRewardsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserRewardsResponseMultiError, or nil if none found.
func (m *GetUserRewardsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserRewardsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRewardsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRewardsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRewardsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRewardsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRewardsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRewardsResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRewards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserRewardsResponseValidationError{
						field:  fmt.Sprintf("Rewards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserRewardsResponseValidationError{
						field:  fmt.Sprintf("Rewards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserRewardsResponseValidationError{
					field:  fmt.Sprintf("Rewards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetSherlockDeepLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRewardsResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRewardsResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSherlockDeepLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRewardsResponseValidationError{
				field:  "SherlockDeepLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRewardCxWrapperList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserRewardsResponseValidationError{
						field:  fmt.Sprintf("RewardCxWrapperList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserRewardsResponseValidationError{
						field:  fmt.Sprintf("RewardCxWrapperList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserRewardsResponseValidationError{
					field:  fmt.Sprintf("RewardCxWrapperList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetUserRewardsResponseMultiError(errors)
	}

	return nil
}

// GetUserRewardsResponseMultiError is an error wrapping multiple validation
// errors returned by GetUserRewardsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetUserRewardsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserRewardsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserRewardsResponseMultiError) AllErrors() []error { return m }

// GetUserRewardsResponseValidationError is the validation error returned by
// GetUserRewardsResponse.Validate if the designated constraints aren't met.
type GetUserRewardsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserRewardsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserRewardsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserRewardsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserRewardsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserRewardsResponseValidationError) ErrorName() string {
	return "GetUserRewardsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserRewardsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserRewardsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserRewardsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserRewardsResponseValidationError{}

// Validate checks the field values on GetRedeemedOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRedeemedOffersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRedeemedOffersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRedeemedOffersRequestMultiError, or nil if none found.
func (m *GetRedeemedOffersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRedeemedOffersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetRedeemedOffersRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRedeemedOffersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRedeemedOffersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRedeemedOffersRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OfferType

	// no validation rules for Vendor

	// no validation rules for RedemptionState

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRedeemedOffersRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRedeemedOffersRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRedeemedOffersRequestValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUptoDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRedeemedOffersRequestValidationError{
					field:  "UptoDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRedeemedOffersRequestValidationError{
					field:  "UptoDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUptoDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRedeemedOffersRequestValidationError{
				field:  "UptoDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExpiryStatus

	// no validation rules for RedeemedOfferId

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRedeemedOffersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRedeemedOffersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRedeemedOffersRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRedeemedOffersRequestMultiError(errors)
	}

	return nil
}

// GetRedeemedOffersRequestMultiError is an error wrapping multiple validation
// errors returned by GetRedeemedOffersRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRedeemedOffersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRedeemedOffersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRedeemedOffersRequestMultiError) AllErrors() []error { return m }

// GetRedeemedOffersRequestValidationError is the validation error returned by
// GetRedeemedOffersRequest.Validate if the designated constraints aren't met.
type GetRedeemedOffersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRedeemedOffersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRedeemedOffersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRedeemedOffersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRedeemedOffersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRedeemedOffersRequestValidationError) ErrorName() string {
	return "GetRedeemedOffersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRedeemedOffersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRedeemedOffersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRedeemedOffersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRedeemedOffersRequestValidationError{}

// Validate checks the field values on GetRedeemedOffersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRedeemedOffersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRedeemedOffersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRedeemedOffersResponseMultiError, or nil if none found.
func (m *GetRedeemedOffersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRedeemedOffersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRedeemedOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRedeemedOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRedeemedOffersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRedeemedOffersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRedeemedOffersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRedeemedOffersResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRedeemedOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRedeemedOffersResponseValidationError{
						field:  fmt.Sprintf("RedeemedOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRedeemedOffersResponseValidationError{
						field:  fmt.Sprintf("RedeemedOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRedeemedOffersResponseValidationError{
					field:  fmt.Sprintf("RedeemedOffers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetSherlockDeepLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRedeemedOffersResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRedeemedOffersResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSherlockDeepLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRedeemedOffersResponseValidationError{
				field:  "SherlockDeepLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRedeemedOfferCxWrapperList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRedeemedOffersResponseValidationError{
						field:  fmt.Sprintf("RedeemedOfferCxWrapperList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRedeemedOffersResponseValidationError{
						field:  fmt.Sprintf("RedeemedOfferCxWrapperList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRedeemedOffersResponseValidationError{
					field:  fmt.Sprintf("RedeemedOfferCxWrapperList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRedeemedOffersResponseMultiError(errors)
	}

	return nil
}

// GetRedeemedOffersResponseMultiError is an error wrapping multiple validation
// errors returned by GetRedeemedOffersResponse.ValidateAll() if the
// designated constraints aren't met.
type GetRedeemedOffersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRedeemedOffersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRedeemedOffersResponseMultiError) AllErrors() []error { return m }

// GetRedeemedOffersResponseValidationError is the validation error returned by
// GetRedeemedOffersResponse.Validate if the designated constraints aren't met.
type GetRedeemedOffersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRedeemedOffersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRedeemedOffersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRedeemedOffersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRedeemedOffersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRedeemedOffersResponseValidationError) ErrorName() string {
	return "GetRedeemedOffersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRedeemedOffersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRedeemedOffersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRedeemedOffersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRedeemedOffersResponseValidationError{}

// Validate checks the field values on GetRewardOfferByIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardOfferByIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOfferByIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardOfferByIdRequestMultiError, or nil if none found.
func (m *GetRewardOfferByIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOfferByIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetRewardOfferByIdRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOfferByIdRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOfferByIdRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOfferByIdRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RewardOfferId

	if len(errors) > 0 {
		return GetRewardOfferByIdRequestMultiError(errors)
	}

	return nil
}

// GetRewardOfferByIdRequestMultiError is an error wrapping multiple validation
// errors returned by GetRewardOfferByIdRequest.ValidateAll() if the
// designated constraints aren't met.
type GetRewardOfferByIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOfferByIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOfferByIdRequestMultiError) AllErrors() []error { return m }

// GetRewardOfferByIdRequestValidationError is the validation error returned by
// GetRewardOfferByIdRequest.Validate if the designated constraints aren't met.
type GetRewardOfferByIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOfferByIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOfferByIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOfferByIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOfferByIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOfferByIdRequestValidationError) ErrorName() string {
	return "GetRewardOfferByIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOfferByIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOfferByIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOfferByIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOfferByIdRequestValidationError{}

// Validate checks the field values on GetRewardOfferByIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardOfferByIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOfferByIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardOfferByIdResponseMultiError, or nil if none found.
func (m *GetRewardOfferByIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOfferByIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOfferByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOfferByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOfferByIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSherlockDeepLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOfferByIdResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOfferByIdResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSherlockDeepLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOfferByIdResponseValidationError{
				field:  "SherlockDeepLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardOffer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOfferByIdResponseValidationError{
					field:  "RewardOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOfferByIdResponseValidationError{
					field:  "RewardOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardOffer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOfferByIdResponseValidationError{
				field:  "RewardOffer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRewardOfferByIdResponseMultiError(errors)
	}

	return nil
}

// GetRewardOfferByIdResponseMultiError is an error wrapping multiple
// validation errors returned by GetRewardOfferByIdResponse.ValidateAll() if
// the designated constraints aren't met.
type GetRewardOfferByIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOfferByIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOfferByIdResponseMultiError) AllErrors() []error { return m }

// GetRewardOfferByIdResponseValidationError is the validation error returned
// by GetRewardOfferByIdResponse.Validate if the designated constraints aren't met.
type GetRewardOfferByIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOfferByIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOfferByIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOfferByIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOfferByIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOfferByIdResponseValidationError) ErrorName() string {
	return "GetRewardOfferByIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOfferByIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOfferByIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOfferByIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOfferByIdResponseValidationError{}

// Validate checks the field values on GetExchangerOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOffersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOffersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetExchangerOffersRequestMultiError, or nil if none found.
func (m *GetExchangerOffersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetExchangerOffersRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersRequestValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersRequestValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExchangerOffersRequestMultiError(errors)
	}

	return nil
}

// GetExchangerOffersRequestMultiError is an error wrapping multiple validation
// errors returned by GetExchangerOffersRequest.ValidateAll() if the
// designated constraints aren't met.
type GetExchangerOffersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersRequestMultiError) AllErrors() []error { return m }

// GetExchangerOffersRequestValidationError is the validation error returned by
// GetExchangerOffersRequest.Validate if the designated constraints aren't met.
type GetExchangerOffersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersRequestValidationError) ErrorName() string {
	return "GetExchangerOffersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersRequestValidationError{}

// Validate checks the field values on GetExchangerOffersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOffersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOffersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetExchangerOffersResponseMultiError, or nil if none found.
func (m *GetExchangerOffersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSherlockDeepLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSherlockDeepLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersResponseValidationError{
				field:  "SherlockDeepLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExchangerOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetExchangerOffersResponseValidationError{
						field:  fmt.Sprintf("ExchangerOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetExchangerOffersResponseValidationError{
						field:  fmt.Sprintf("ExchangerOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetExchangerOffersResponseValidationError{
					field:  fmt.Sprintf("ExchangerOffers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetExchangerOffersResponseMultiError(errors)
	}

	return nil
}

// GetExchangerOffersResponseMultiError is an error wrapping multiple
// validation errors returned by GetExchangerOffersResponse.ValidateAll() if
// the designated constraints aren't met.
type GetExchangerOffersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersResponseMultiError) AllErrors() []error { return m }

// GetExchangerOffersResponseValidationError is the validation error returned
// by GetExchangerOffersResponse.Validate if the designated constraints aren't met.
type GetExchangerOffersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersResponseValidationError) ErrorName() string {
	return "GetExchangerOffersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersResponseValidationError{}

// Validate checks the field values on GetExchangerOfferOrdersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOfferOrdersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOfferOrdersRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetExchangerOfferOrdersRequestMultiError, or nil if none found.
func (m *GetExchangerOfferOrdersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOfferOrdersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetExchangerOfferOrdersRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RedemptionState

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersRequestValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUptoDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequestValidationError{
					field:  "UptoDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequestValidationError{
					field:  "UptoDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUptoDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersRequestValidationError{
				field:  "UptoDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExchangerOfferOrdersRequestMultiError(errors)
	}

	return nil
}

// GetExchangerOfferOrdersRequestMultiError is an error wrapping multiple
// validation errors returned by GetExchangerOfferOrdersRequest.ValidateAll()
// if the designated constraints aren't met.
type GetExchangerOfferOrdersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOfferOrdersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOfferOrdersRequestMultiError) AllErrors() []error { return m }

// GetExchangerOfferOrdersRequestValidationError is the validation error
// returned by GetExchangerOfferOrdersRequest.Validate if the designated
// constraints aren't met.
type GetExchangerOfferOrdersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOfferOrdersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOfferOrdersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOfferOrdersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOfferOrdersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOfferOrdersRequestValidationError) ErrorName() string {
	return "GetExchangerOfferOrdersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOfferOrdersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOfferOrdersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOfferOrdersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOfferOrdersRequestValidationError{}

// Validate checks the field values on GetExchangerOfferOrdersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOfferOrdersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOfferOrdersResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetExchangerOfferOrdersResponseMultiError, or nil if none found.
func (m *GetExchangerOfferOrdersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOfferOrdersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExchangerOfferOrders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
						field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
						field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetExchangerOfferOrdersResponseValidationError{
					field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetSherlockDeepLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSherlockDeepLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersResponseValidationError{
				field:  "SherlockDeepLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExchangerOfferOrderCxWrapperList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
						field:  fmt.Sprintf("ExchangerOfferOrderCxWrapperList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
						field:  fmt.Sprintf("ExchangerOfferOrderCxWrapperList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetExchangerOfferOrdersResponseValidationError{
					field:  fmt.Sprintf("ExchangerOfferOrderCxWrapperList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetExchangerOfferOrdersResponseMultiError(errors)
	}

	return nil
}

// GetExchangerOfferOrdersResponseMultiError is an error wrapping multiple
// validation errors returned by GetExchangerOfferOrdersResponse.ValidateAll()
// if the designated constraints aren't met.
type GetExchangerOfferOrdersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOfferOrdersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOfferOrdersResponseMultiError) AllErrors() []error { return m }

// GetExchangerOfferOrdersResponseValidationError is the validation error
// returned by GetExchangerOfferOrdersResponse.Validate if the designated
// constraints aren't met.
type GetExchangerOfferOrdersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOfferOrdersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOfferOrdersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOfferOrdersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOfferOrdersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOfferOrdersResponseValidationError) ErrorName() string {
	return "GetExchangerOfferOrdersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOfferOrdersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOfferOrdersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOfferOrdersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOfferOrdersResponseValidationError{}

// Validate checks the field values on
// GetRewardUnitsUtilisationForActorAndOfferInMonthRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRewardUnitsUtilisationForActorAndOfferInMonthRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardUnitsUtilisationForActorAndOfferInMonthRequestMultiError, or nil
// if none found.
func (m *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RewardOfferId

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRewardUnitsUtilisationForActorAndOfferInMonthRequestMultiError(errors)
	}

	return nil
}

// GetRewardUnitsUtilisationForActorAndOfferInMonthRequestMultiError is an
// error wrapping multiple validation errors returned by
// GetRewardUnitsUtilisationForActorAndOfferInMonthRequest.ValidateAll() if
// the designated constraints aren't met.
type GetRewardUnitsUtilisationForActorAndOfferInMonthRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardUnitsUtilisationForActorAndOfferInMonthRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardUnitsUtilisationForActorAndOfferInMonthRequestMultiError) AllErrors() []error {
	return m
}

// GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError is
// the validation error returned by
// GetRewardUnitsUtilisationForActorAndOfferInMonthRequest.Validate if the
// designated constraints aren't met.
type GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError) ErrorName() string {
	return "GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardUnitsUtilisationForActorAndOfferInMonthRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardUnitsUtilisationForActorAndOfferInMonthRequestValidationError{}

// Validate checks the field values on
// GetRewardUnitsUtilisationForActorAndOfferInMonthResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRewardUnitsUtilisationForActorAndOfferInMonthResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardUnitsUtilisationForActorAndOfferInMonthResponseMultiError, or nil
// if none found.
func (m *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUnitsUtilizedAndCappingList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError{
						field:  fmt.Sprintf("UnitsUtilizedAndCappingList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError{
						field:  fmt.Sprintf("UnitsUtilizedAndCappingList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError{
					field:  fmt.Sprintf("UnitsUtilizedAndCappingList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RewardCount

	if all {
		switch v := interface{}(m.GetRewardTypeUtilizationAndCappingTable()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError{
					field:  "RewardTypeUtilizationAndCappingTable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError{
					field:  "RewardTypeUtilizationAndCappingTable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardTypeUtilizationAndCappingTable()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError{
				field:  "RewardTypeUtilizationAndCappingTable",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRewardUnitsUtilisationForActorAndOfferInMonthResponseMultiError(errors)
	}

	return nil
}

// GetRewardUnitsUtilisationForActorAndOfferInMonthResponseMultiError is an
// error wrapping multiple validation errors returned by
// GetRewardUnitsUtilisationForActorAndOfferInMonthResponse.ValidateAll() if
// the designated constraints aren't met.
type GetRewardUnitsUtilisationForActorAndOfferInMonthResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardUnitsUtilisationForActorAndOfferInMonthResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardUnitsUtilisationForActorAndOfferInMonthResponseMultiError) AllErrors() []error {
	return m
}

// GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError is
// the validation error returned by
// GetRewardUnitsUtilisationForActorAndOfferInMonthResponse.Validate if the
// designated constraints aren't met.
type GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError) ErrorName() string {
	return "GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardUnitsUtilisationForActorAndOfferInMonthResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardUnitsUtilisationForActorAndOfferInMonthResponseValidationError{}

// Validate checks the field values on GetRewardOfferTypesOptionsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetRewardOfferTypesOptionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOfferTypesOptionsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRewardOfferTypesOptionsRequestMultiError, or nil if none found.
func (m *GetRewardOfferTypesOptionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOfferTypesOptionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetRewardOfferTypesOptionsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOfferTypesOptionsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOfferTypesOptionsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOfferTypesOptionsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRewardOfferTypesOptionsRequestMultiError(errors)
	}

	return nil
}

// GetRewardOfferTypesOptionsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetRewardOfferTypesOptionsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRewardOfferTypesOptionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOfferTypesOptionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOfferTypesOptionsRequestMultiError) AllErrors() []error { return m }

// GetRewardOfferTypesOptionsRequestValidationError is the validation error
// returned by GetRewardOfferTypesOptionsRequest.Validate if the designated
// constraints aren't met.
type GetRewardOfferTypesOptionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOfferTypesOptionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOfferTypesOptionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOfferTypesOptionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOfferTypesOptionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOfferTypesOptionsRequestValidationError) ErrorName() string {
	return "GetRewardOfferTypesOptionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOfferTypesOptionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOfferTypesOptionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOfferTypesOptionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOfferTypesOptionsRequestValidationError{}

// Validate checks the field values on GetRewardOfferTypesOptionsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetRewardOfferTypesOptionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOfferTypesOptionsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRewardOfferTypesOptionsResponseMultiError, or nil if none found.
func (m *GetRewardOfferTypesOptionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOfferTypesOptionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOfferTypesOptionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOfferTypesOptionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOfferTypesOptionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OfferTypes

	if len(errors) > 0 {
		return GetRewardOfferTypesOptionsResponseMultiError(errors)
	}

	return nil
}

// GetRewardOfferTypesOptionsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetRewardOfferTypesOptionsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRewardOfferTypesOptionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOfferTypesOptionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOfferTypesOptionsResponseMultiError) AllErrors() []error { return m }

// GetRewardOfferTypesOptionsResponseValidationError is the validation error
// returned by GetRewardOfferTypesOptionsResponse.Validate if the designated
// constraints aren't met.
type GetRewardOfferTypesOptionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOfferTypesOptionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOfferTypesOptionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOfferTypesOptionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOfferTypesOptionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOfferTypesOptionsResponseValidationError) ErrorName() string {
	return "GetRewardOfferTypesOptionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOfferTypesOptionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOfferTypesOptionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOfferTypesOptionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOfferTypesOptionsResponseValidationError{}

// Validate checks the field values on GetRewardDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardDetailsRequestMultiError, or nil if none found.
func (m *GetRewardDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetRewardDetailsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RewardOfferType

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardDetailsRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardDetailsRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardDetailsRequestValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardDetailsRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardDetailsRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardDetailsRequestValidationError{
				field:  "ToDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExternalTxnId

	if len(errors) > 0 {
		return GetRewardDetailsRequestMultiError(errors)
	}

	return nil
}

// GetRewardDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetRewardDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRewardDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardDetailsRequestMultiError) AllErrors() []error { return m }

// GetRewardDetailsRequestValidationError is the validation error returned by
// GetRewardDetailsRequest.Validate if the designated constraints aren't met.
type GetRewardDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardDetailsRequestValidationError) ErrorName() string {
	return "GetRewardDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardDetailsRequestValidationError{}

// Validate checks the field values on GetRewardDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardDetailsResponseMultiError, or nil if none found.
func (m *GetRewardDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardDetailsTable()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardDetailsResponseValidationError{
					field:  "RewardDetailsTable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardDetailsResponseValidationError{
					field:  "RewardDetailsTable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardDetailsTable()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardDetailsResponseValidationError{
				field:  "RewardDetailsTable",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRewardDetailsResponseMultiError(errors)
	}

	return nil
}

// GetRewardDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetRewardDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRewardDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardDetailsResponseMultiError) AllErrors() []error { return m }

// GetRewardDetailsResponseValidationError is the validation error returned by
// GetRewardDetailsResponse.Validate if the designated constraints aren't met.
type GetRewardDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardDetailsResponseValidationError) ErrorName() string {
	return "GetRewardDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardDetailsResponseValidationError{}

// Validate checks the field values on GetFiStoreRedemptionsDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFiStoreRedemptionsDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFiStoreRedemptionsDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFiStoreRedemptionsDetailsRequestMultiError, or nil if none found.
func (m *GetFiStoreRedemptionsDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFiStoreRedemptionsDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetFiStoreRedemptionsDetailsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFiStoreRedemptionsDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	// no validation rules for Category

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFiStoreRedemptionsDetailsRequestValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFiStoreRedemptionsDetailsRequestValidationError{
				field:  "ToDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFiStoreRedemptionsDetailsRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFiStoreRedemptionsDetailsRequestMultiError(errors)
	}

	return nil
}

// GetFiStoreRedemptionsDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetFiStoreRedemptionsDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFiStoreRedemptionsDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFiStoreRedemptionsDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFiStoreRedemptionsDetailsRequestMultiError) AllErrors() []error { return m }

// GetFiStoreRedemptionsDetailsRequestValidationError is the validation error
// returned by GetFiStoreRedemptionsDetailsRequest.Validate if the designated
// constraints aren't met.
type GetFiStoreRedemptionsDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFiStoreRedemptionsDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFiStoreRedemptionsDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFiStoreRedemptionsDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFiStoreRedemptionsDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFiStoreRedemptionsDetailsRequestValidationError) ErrorName() string {
	return "GetFiStoreRedemptionsDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFiStoreRedemptionsDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFiStoreRedemptionsDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFiStoreRedemptionsDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFiStoreRedemptionsDetailsRequestValidationError{}

// Validate checks the field values on GetFiStoreRedemptionsDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetFiStoreRedemptionsDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFiStoreRedemptionsDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFiStoreRedemptionsDetailsResponseMultiError, or nil if none found.
func (m *GetFiStoreRedemptionsDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFiStoreRedemptionsDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFiStoreRedemptionsDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFiStoreRewardDetailsTable()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsResponseValidationError{
					field:  "FiStoreRewardDetailsTable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsResponseValidationError{
					field:  "FiStoreRewardDetailsTable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFiStoreRewardDetailsTable()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFiStoreRedemptionsDetailsResponseValidationError{
				field:  "FiStoreRewardDetailsTable",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFiStoreRedemptionsDetailsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFiStoreRedemptionsDetailsResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFiStoreRedemptionsDetailsResponseMultiError(errors)
	}

	return nil
}

// GetFiStoreRedemptionsDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetFiStoreRedemptionsDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFiStoreRedemptionsDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFiStoreRedemptionsDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFiStoreRedemptionsDetailsResponseMultiError) AllErrors() []error { return m }

// GetFiStoreRedemptionsDetailsResponseValidationError is the validation error
// returned by GetFiStoreRedemptionsDetailsResponse.Validate if the designated
// constraints aren't met.
type GetFiStoreRedemptionsDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFiStoreRedemptionsDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFiStoreRedemptionsDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFiStoreRedemptionsDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFiStoreRedemptionsDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFiStoreRedemptionsDetailsResponseValidationError) ErrorName() string {
	return "GetFiStoreRedemptionsDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFiStoreRedemptionsDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFiStoreRedemptionsDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFiStoreRedemptionsDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFiStoreRedemptionsDetailsResponseValidationError{}

// Validate checks the field values on
// GetFiStoreRedemptionAdditionalDetailsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFiStoreRedemptionAdditionalDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFiStoreRedemptionAdditionalDetailsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetFiStoreRedemptionAdditionalDetailsRequestMultiError, or nil if none found.
func (m *GetFiStoreRedemptionAdditionalDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFiStoreRedemptionAdditionalDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetFiStoreRedemptionAdditionalDetailsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFiStoreRedemptionAdditionalDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFiStoreRedemptionAdditionalDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFiStoreRedemptionAdditionalDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RedemptionId

	if len(errors) > 0 {
		return GetFiStoreRedemptionAdditionalDetailsRequestMultiError(errors)
	}

	return nil
}

// GetFiStoreRedemptionAdditionalDetailsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetFiStoreRedemptionAdditionalDetailsRequest.ValidateAll() if the
// designated constraints aren't met.
type GetFiStoreRedemptionAdditionalDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFiStoreRedemptionAdditionalDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFiStoreRedemptionAdditionalDetailsRequestMultiError) AllErrors() []error { return m }

// GetFiStoreRedemptionAdditionalDetailsRequestValidationError is the
// validation error returned by
// GetFiStoreRedemptionAdditionalDetailsRequest.Validate if the designated
// constraints aren't met.
type GetFiStoreRedemptionAdditionalDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFiStoreRedemptionAdditionalDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFiStoreRedemptionAdditionalDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFiStoreRedemptionAdditionalDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFiStoreRedemptionAdditionalDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFiStoreRedemptionAdditionalDetailsRequestValidationError) ErrorName() string {
	return "GetFiStoreRedemptionAdditionalDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFiStoreRedemptionAdditionalDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFiStoreRedemptionAdditionalDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFiStoreRedemptionAdditionalDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFiStoreRedemptionAdditionalDetailsRequestValidationError{}

// Validate checks the field values on
// GetFiStoreRedemptionAdditionalDetailsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFiStoreRedemptionAdditionalDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFiStoreRedemptionAdditionalDetailsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetFiStoreRedemptionAdditionalDetailsResponseMultiError, or nil if none found.
func (m *GetFiStoreRedemptionAdditionalDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFiStoreRedemptionAdditionalDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFiStoreRedemptionAdditionalDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFiStoreRedemptionAdditionalDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFiStoreRedemptionAdditionalDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRewardDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFiStoreRedemptionAdditionalDetailsResponseValidationError{
						field:  fmt.Sprintf("RewardDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFiStoreRedemptionAdditionalDetailsResponseValidationError{
						field:  fmt.Sprintf("RewardDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFiStoreRedemptionAdditionalDetailsResponseValidationError{
					field:  fmt.Sprintf("RewardDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFiStoreRedemptionAdditionalDetailsResponseMultiError(errors)
	}

	return nil
}

// GetFiStoreRedemptionAdditionalDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetFiStoreRedemptionAdditionalDetailsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetFiStoreRedemptionAdditionalDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFiStoreRedemptionAdditionalDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFiStoreRedemptionAdditionalDetailsResponseMultiError) AllErrors() []error { return m }

// GetFiStoreRedemptionAdditionalDetailsResponseValidationError is the
// validation error returned by
// GetFiStoreRedemptionAdditionalDetailsResponse.Validate if the designated
// constraints aren't met.
type GetFiStoreRedemptionAdditionalDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFiStoreRedemptionAdditionalDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFiStoreRedemptionAdditionalDetailsResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetFiStoreRedemptionAdditionalDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFiStoreRedemptionAdditionalDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFiStoreRedemptionAdditionalDetailsResponseValidationError) ErrorName() string {
	return "GetFiStoreRedemptionAdditionalDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFiStoreRedemptionAdditionalDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFiStoreRedemptionAdditionalDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFiStoreRedemptionAdditionalDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFiStoreRedemptionAdditionalDetailsResponseValidationError{}

// Validate checks the field values on GetFiCoinsFiPointsDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFiCoinsFiPointsDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFiCoinsFiPointsDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFiCoinsFiPointsDetailsRequestMultiError, or nil if none found.
func (m *GetFiCoinsFiPointsDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFiCoinsFiPointsDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetFiCoinsFiPointsDetailsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFiCoinsFiPointsDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFiCoinsFiPointsDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFiCoinsFiPointsDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFiCoinsFiPointsDetailsRequestMultiError(errors)
	}

	return nil
}

// GetFiCoinsFiPointsDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetFiCoinsFiPointsDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFiCoinsFiPointsDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFiCoinsFiPointsDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFiCoinsFiPointsDetailsRequestMultiError) AllErrors() []error { return m }

// GetFiCoinsFiPointsDetailsRequestValidationError is the validation error
// returned by GetFiCoinsFiPointsDetailsRequest.Validate if the designated
// constraints aren't met.
type GetFiCoinsFiPointsDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFiCoinsFiPointsDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFiCoinsFiPointsDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFiCoinsFiPointsDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFiCoinsFiPointsDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFiCoinsFiPointsDetailsRequestValidationError) ErrorName() string {
	return "GetFiCoinsFiPointsDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFiCoinsFiPointsDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFiCoinsFiPointsDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFiCoinsFiPointsDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFiCoinsFiPointsDetailsRequestValidationError{}

// Validate checks the field values on GetFiCoinsFiPointsDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFiCoinsFiPointsDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFiCoinsFiPointsDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFiCoinsFiPointsDetailsResponseMultiError, or nil if none found.
func (m *GetFiCoinsFiPointsDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFiCoinsFiPointsDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFiCoinsFiPointsDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFiCoinsFiPointsDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFiCoinsFiPointsDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FiCoinsMigrated

	// no validation rules for FiPointsMigrated

	// no validation rules for FiPointsCurrent

	// no validation rules for ConversionRule

	if len(errors) > 0 {
		return GetFiCoinsFiPointsDetailsResponseMultiError(errors)
	}

	return nil
}

// GetFiCoinsFiPointsDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetFiCoinsFiPointsDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFiCoinsFiPointsDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFiCoinsFiPointsDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFiCoinsFiPointsDetailsResponseMultiError) AllErrors() []error { return m }

// GetFiCoinsFiPointsDetailsResponseValidationError is the validation error
// returned by GetFiCoinsFiPointsDetailsResponse.Validate if the designated
// constraints aren't met.
type GetFiCoinsFiPointsDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFiCoinsFiPointsDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFiCoinsFiPointsDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFiCoinsFiPointsDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFiCoinsFiPointsDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFiCoinsFiPointsDetailsResponseValidationError) ErrorName() string {
	return "GetFiCoinsFiPointsDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFiCoinsFiPointsDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFiCoinsFiPointsDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFiCoinsFiPointsDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFiCoinsFiPointsDetailsResponseValidationError{}

// Validate checks the field values on
// GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingMultiError,
// or nil if none found.
func (m *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RewardType

	// no validation rules for UnitsUtilized

	// no validation rules for Capping

	if len(errors) > 0 {
		return GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingMultiError(errors)
	}

	return nil
}

// GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingMultiError
// is an error wrapping multiple validation errors returned by
// GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping.ValidateAll()
// if the designated constraints aren't met.
type GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingMultiError) AllErrors() []error {
	return m
}

// GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingValidationError
// is the validation error returned by
// GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping.Validate
// if the designated constraints aren't met.
type GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingValidationError) ErrorName() string {
	return "GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCappingValidationError{}
