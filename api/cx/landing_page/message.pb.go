// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/landing_page/message.proto

package landing_page

import (
	issue_category "github.com/epifi/gamma/api/cx/issue_category"
	webui "github.com/epifi/gamma/api/typesv2/webui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Figma: https://www.figma.com/design/E9AP0xTr89nKrXOHGPZiS3/%F0%9F%95%B5%EF%B8%8F%E2%80%8D%E2%99%82%EF%B8%8F-Sherlock-%E2%80%A2-FFF?node-id=3409-9283&node-type=frame&t=kJyeXzJpKI455GEs-0
type RecentQueries struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Title for the entire recent queries section
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Grouping queries by day
	DayWiseList []*RecentQueries_DayWiseList `protobuf:"bytes,2,rep,name=day_wise_list,json=dayWiseList,proto3" json:"day_wise_list,omitempty"`
}

func (x *RecentQueries) Reset() {
	*x = RecentQueries{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_landing_page_message_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecentQueries) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecentQueries) ProtoMessage() {}

func (x *RecentQueries) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_landing_page_message_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecentQueries.ProtoReflect.Descriptor instead.
func (*RecentQueries) Descriptor() ([]byte, []int) {
	return file_api_cx_landing_page_message_proto_rawDescGZIP(), []int{0}
}

func (x *RecentQueries) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *RecentQueries) GetDayWiseList() []*RecentQueries_DayWiseList {
	if x != nil {
		return x.DayWiseList
	}
	return nil
}

type RecentQueries_DayWiseList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Date for grouping
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// Queries for the specified date
	Query []*RecentQueries_DayWiseList_Query `protobuf:"bytes,2,rep,name=query,proto3" json:"query,omitempty"`
}

func (x *RecentQueries_DayWiseList) Reset() {
	*x = RecentQueries_DayWiseList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_landing_page_message_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecentQueries_DayWiseList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecentQueries_DayWiseList) ProtoMessage() {}

func (x *RecentQueries_DayWiseList) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_landing_page_message_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecentQueries_DayWiseList.ProtoReflect.Descriptor instead.
func (*RecentQueries_DayWiseList) Descriptor() ([]byte, []int) {
	return file_api_cx_landing_page_message_proto_rawDescGZIP(), []int{0, 0}
}

func (x *RecentQueries_DayWiseList) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *RecentQueries_DayWiseList) GetQuery() []*RecentQueries_DayWiseList_Query {
	if x != nil {
		return x.Query
	}
	return nil
}

type RecentQueries_DayWiseList_Query struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QueryId     string `protobuf:"bytes,1,opt,name=query_id,json=queryId,proto3" json:"query_id,omitempty"`
	LeftIconUrl string `protobuf:"bytes,2,opt,name=left_icon_url,json=leftIconUrl,proto3" json:"left_icon_url,omitempty"`
	// Primary text for the query
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// Secondary text for additional context
	// For now we are passing the time at which the query was performed
	SubTitle string     `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Cta      *webui.CTA `protobuf:"bytes,5,opt,name=cta,proto3" json:"cta,omitempty"`
	// Category related to the query to be used for filtering SOP when a user clicks on CTA
	IssueCategory *issue_category.IssueCategory `protobuf:"bytes,6,opt,name=issue_category,json=issueCategory,proto3" json:"issue_category,omitempty"`
}

func (x *RecentQueries_DayWiseList_Query) Reset() {
	*x = RecentQueries_DayWiseList_Query{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_landing_page_message_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecentQueries_DayWiseList_Query) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecentQueries_DayWiseList_Query) ProtoMessage() {}

func (x *RecentQueries_DayWiseList_Query) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_landing_page_message_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecentQueries_DayWiseList_Query.ProtoReflect.Descriptor instead.
func (*RecentQueries_DayWiseList_Query) Descriptor() ([]byte, []int) {
	return file_api_cx_landing_page_message_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *RecentQueries_DayWiseList_Query) GetQueryId() string {
	if x != nil {
		return x.QueryId
	}
	return ""
}

func (x *RecentQueries_DayWiseList_Query) GetLeftIconUrl() string {
	if x != nil {
		return x.LeftIconUrl
	}
	return ""
}

func (x *RecentQueries_DayWiseList_Query) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *RecentQueries_DayWiseList_Query) GetSubTitle() string {
	if x != nil {
		return x.SubTitle
	}
	return ""
}

func (x *RecentQueries_DayWiseList_Query) GetCta() *webui.CTA {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *RecentQueries_DayWiseList_Query) GetIssueCategory() *issue_category.IssueCategory {
	if x != nil {
		return x.IssueCategory
	}
	return nil
}

var File_api_cx_landing_page_message_proto protoreflect.FileDescriptor

var file_api_cx_landing_page_message_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x70, 0x61, 0x67, 0x65, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x63, 0x78, 0x2e, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x70, 0x61, 0x67, 0x65, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x69, 0x73, 0x73,
	0x75, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2f, 0x69, 0x73, 0x73, 0x75,
	0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x77, 0x65,
	0x62, 0x75, 0x69, 0x2f, 0x63, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd0, 0x03,
	0x0a, 0x0d, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x4e, 0x0a, 0x0d, 0x64, 0x61, 0x79, 0x5f, 0x77, 0x69, 0x73,
	0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63,
	0x78, 0x2e, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x2e, 0x52,
	0x65, 0x63, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x44, 0x61, 0x79,
	0x57, 0x69, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0b, 0x64, 0x61, 0x79, 0x57, 0x69, 0x73,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0xd8, 0x02, 0x0a, 0x0b, 0x44, 0x61, 0x79, 0x57, 0x69, 0x73,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x46, 0x0a, 0x05, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x78, 0x2e, 0x6c, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x6e,
	0x74, 0x51, 0x75, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x44, 0x61, 0x79, 0x57, 0x69, 0x73, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x1a, 0xec, 0x01, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x69,
	0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c,
	0x65, 0x66, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x28, 0x0a,
	0x03, 0x63, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x43,
	0x54, 0x41, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x47, 0x0a, 0x0e, 0x69, 0x73, 0x73, 0x75, 0x65,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x63, 0x78, 0x2e, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x2e, 0x49, 0x73, 0x73, 0x75, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x52, 0x0d, 0x69, 0x73, 0x73, 0x75, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x78, 0x2e, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5a, 0x2a,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x6c, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_cx_landing_page_message_proto_rawDescOnce sync.Once
	file_api_cx_landing_page_message_proto_rawDescData = file_api_cx_landing_page_message_proto_rawDesc
)

func file_api_cx_landing_page_message_proto_rawDescGZIP() []byte {
	file_api_cx_landing_page_message_proto_rawDescOnce.Do(func() {
		file_api_cx_landing_page_message_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_landing_page_message_proto_rawDescData)
	})
	return file_api_cx_landing_page_message_proto_rawDescData
}

var file_api_cx_landing_page_message_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_cx_landing_page_message_proto_goTypes = []interface{}{
	(*RecentQueries)(nil),                   // 0: cx.landing_page.RecentQueries
	(*RecentQueries_DayWiseList)(nil),       // 1: cx.landing_page.RecentQueries.DayWiseList
	(*RecentQueries_DayWiseList_Query)(nil), // 2: cx.landing_page.RecentQueries.DayWiseList.Query
	(*webui.CTA)(nil),                       // 3: api.typesv2.webui.CTA
	(*issue_category.IssueCategory)(nil),    // 4: cx.issue_category.IssueCategory
}
var file_api_cx_landing_page_message_proto_depIdxs = []int32{
	1, // 0: cx.landing_page.RecentQueries.day_wise_list:type_name -> cx.landing_page.RecentQueries.DayWiseList
	2, // 1: cx.landing_page.RecentQueries.DayWiseList.query:type_name -> cx.landing_page.RecentQueries.DayWiseList.Query
	3, // 2: cx.landing_page.RecentQueries.DayWiseList.Query.cta:type_name -> api.typesv2.webui.CTA
	4, // 3: cx.landing_page.RecentQueries.DayWiseList.Query.issue_category:type_name -> cx.issue_category.IssueCategory
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_cx_landing_page_message_proto_init() }
func file_api_cx_landing_page_message_proto_init() {
	if File_api_cx_landing_page_message_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_cx_landing_page_message_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecentQueries); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_landing_page_message_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecentQueries_DayWiseList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_landing_page_message_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecentQueries_DayWiseList_Query); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_landing_page_message_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_landing_page_message_proto_goTypes,
		DependencyIndexes: file_api_cx_landing_page_message_proto_depIdxs,
		MessageInfos:      file_api_cx_landing_page_message_proto_msgTypes,
	}.Build()
	File_api_cx_landing_page_message_proto = out.File
	file_api_cx_landing_page_message_proto_rawDesc = nil
	file_api_cx_landing_page_message_proto_goTypes = nil
	file_api_cx_landing_page_message_proto_depIdxs = nil
}
