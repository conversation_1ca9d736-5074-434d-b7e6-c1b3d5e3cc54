// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/dispute/enums.proto

package dispute

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Channel int32

const (
	Channel_CHANNEL_UNSPECIFIED Channel = 0
	Channel_UPI                 Channel = 1
	Channel_IMPS                Channel = 2
	Channel_NEFT                Channel = 3
	Channel_RTGS                Channel = 4
	Channel_DEBIT_CARD_ATM      Channel = 5
	Channel_DEBIT_CARD_POS      Channel = 6
	Channel_DEBIT_CARD_ECOM     Channel = 7
	Channel_CHEQUE              Channel = 8
	Channel_INTRA_BANK          Channel = 9
	Channel_CARD                Channel = 10
)

// Enum value maps for Channel.
var (
	Channel_name = map[int32]string{
		0:  "CHANNEL_UNSPECIFIED",
		1:  "UPI",
		2:  "IMPS",
		3:  "NEFT",
		4:  "RTGS",
		5:  "DEBIT_CARD_ATM",
		6:  "DEBIT_CARD_POS",
		7:  "DEBIT_CARD_ECOM",
		8:  "CHEQUE",
		9:  "INTRA_BANK",
		10: "CARD",
	}
	Channel_value = map[string]int32{
		"CHANNEL_UNSPECIFIED": 0,
		"UPI":                 1,
		"IMPS":                2,
		"NEFT":                3,
		"RTGS":                4,
		"DEBIT_CARD_ATM":      5,
		"DEBIT_CARD_POS":      6,
		"DEBIT_CARD_ECOM":     7,
		"CHEQUE":              8,
		"INTRA_BANK":          9,
		"CARD":                10,
	}
)

func (x Channel) Enum() *Channel {
	p := new(Channel)
	*p = x
	return p
}

func (x Channel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Channel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[0].Descriptor()
}

func (Channel) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[0]
}

func (x Channel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Channel.Descriptor instead.
func (Channel) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{0}
}

type Source int32

const (
	Source_SOURCE_UNSPECIFIED Source = 0
	Source_APP                Source = 1
	Source_SHERLOCK           Source = 2
)

// Enum value maps for Source.
var (
	Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "APP",
		2: "SHERLOCK",
	}
	Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED": 0,
		"APP":                1,
		"SHERLOCK":           2,
	}
)

func (x Source) Enum() *Source {
	p := new(Source)
	*p = x
	return p
}

func (x Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Source) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[1].Descriptor()
}

func (Source) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[1]
}

func (x Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Source.Descriptor instead.
func (Source) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{1}
}

type DisputeType int32

const (
	DisputeType_DISPUTE_TYPE_UNSPECIFIED DisputeType = 0
	DisputeType_AUTHORISED               DisputeType = 1
	DisputeType_UNAUTHORISED             DisputeType = 2
	DisputeType_WRONG_BENEFICIARY        DisputeType = 3
	DisputeType_WRONG_ACCOUNT_NUMBER     DisputeType = 4
	DisputeType_WRONG_AMOUNT             DisputeType = 5
)

// Enum value maps for DisputeType.
var (
	DisputeType_name = map[int32]string{
		0: "DISPUTE_TYPE_UNSPECIFIED",
		1: "AUTHORISED",
		2: "UNAUTHORISED",
		3: "WRONG_BENEFICIARY",
		4: "WRONG_ACCOUNT_NUMBER",
		5: "WRONG_AMOUNT",
	}
	DisputeType_value = map[string]int32{
		"DISPUTE_TYPE_UNSPECIFIED": 0,
		"AUTHORISED":               1,
		"UNAUTHORISED":             2,
		"WRONG_BENEFICIARY":        3,
		"WRONG_ACCOUNT_NUMBER":     4,
		"WRONG_AMOUNT":             5,
	}
)

func (x DisputeType) Enum() *DisputeType {
	p := new(DisputeType)
	*p = x
	return p
}

func (x DisputeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[2].Descriptor()
}

func (DisputeType) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[2]
}

func (x DisputeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeType.Descriptor instead.
func (DisputeType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{2}
}

type EscalationMode int32

const (
	EscalationMode_ESCALATION_MODE_UNSPECIFIED EscalationMode = 0
	// dispute needs to be escalated to federal using sftp file
	EscalationMode_SFTP EscalationMode = 1
	// dispute ticket needs to be created on freshdesk
	EscalationMode_FRESHDESK EscalationMode = 2
	// deprecated, do not use
	//
	// Deprecated: Marked as deprecated in api/cx/dispute/enums.proto.
	EscalationMode_ESCALATION_NOT_REQUIRED EscalationMode = 3
	// dispute needs to be raised to npci
	EscalationMode_ESCALATION_MODE_UDIR EscalationMode = 4
	// raising dispute is not allowed for given txn
	EscalationMode_NOT_ALLOWED EscalationMode = 5
	// raising dispute to federal's bank
	EscalationMode_ESCALATION_MODE_FEDERAL_BANK EscalationMode = 6
)

// Enum value maps for EscalationMode.
var (
	EscalationMode_name = map[int32]string{
		0: "ESCALATION_MODE_UNSPECIFIED",
		1: "SFTP",
		2: "FRESHDESK",
		3: "ESCALATION_NOT_REQUIRED",
		4: "ESCALATION_MODE_UDIR",
		5: "NOT_ALLOWED",
		6: "ESCALATION_MODE_FEDERAL_BANK",
	}
	EscalationMode_value = map[string]int32{
		"ESCALATION_MODE_UNSPECIFIED":  0,
		"SFTP":                         1,
		"FRESHDESK":                    2,
		"ESCALATION_NOT_REQUIRED":      3,
		"ESCALATION_MODE_UDIR":         4,
		"NOT_ALLOWED":                  5,
		"ESCALATION_MODE_FEDERAL_BANK": 6,
	}
)

func (x EscalationMode) Enum() *EscalationMode {
	p := new(EscalationMode)
	*p = x
	return p
}

func (x EscalationMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EscalationMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[3].Descriptor()
}

func (EscalationMode) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[3]
}

func (x EscalationMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EscalationMode.Descriptor instead.
func (EscalationMode) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{3}
}

type TransactionStatus int32

const (
	TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED TransactionStatus = 0
	// SUCCESS
	TransactionStatus_SUCCESS TransactionStatus = 1
	// UNKNOWN , IN_PROGRESS , INITIATED , MANUAL_INTERVENTION
	TransactionStatus_PENDING TransactionStatus = 2
	// denotes that transaction is deemed
	// Deemed transaction is a use case only for UPI transactions
	// NPCI sends the status as DEEMED in certain cases like - the remitter bank request times out with NPCI
	// for deemed transaction, generally the transaction goes to success, but for rare cases the transaction might also fail
	// epifi will treat Deemed transaction as success on the client side, on backend  will check the status
	// in intervals with a retry logic
	TransactionStatus_DEEMED_SUCCESS TransactionStatus = 3
)

// Enum value maps for TransactionStatus.
var (
	TransactionStatus_name = map[int32]string{
		0: "TRANSACTION_STATUS_UNSPECIFIED",
		1: "SUCCESS",
		2: "PENDING",
		3: "DEEMED_SUCCESS",
	}
	TransactionStatus_value = map[string]int32{
		"TRANSACTION_STATUS_UNSPECIFIED": 0,
		"SUCCESS":                        1,
		"PENDING":                        2,
		"DEEMED_SUCCESS":                 3,
	}
)

func (x TransactionStatus) Enum() *TransactionStatus {
	p := new(TransactionStatus)
	*p = x
	return p
}

func (x TransactionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[4].Descriptor()
}

func (TransactionStatus) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[4]
}

func (x TransactionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionStatus.Descriptor instead.
func (TransactionStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{4}
}

type Receiver int32

const (
	Receiver_RECEIVER_UNSPECIFIED Receiver = 0
	Receiver_P2P                  Receiver = 1
	Receiver_P2M                  Receiver = 2
	Receiver_NA                   Receiver = 3
)

// Enum value maps for Receiver.
var (
	Receiver_name = map[int32]string{
		0: "RECEIVER_UNSPECIFIED",
		1: "P2P",
		2: "P2M",
		3: "NA",
	}
	Receiver_value = map[string]int32{
		"RECEIVER_UNSPECIFIED": 0,
		"P2P":                  1,
		"P2M":                  2,
		"NA":                   3,
	}
)

func (x Receiver) Enum() *Receiver {
	p := new(Receiver)
	*p = x
	return p
}

func (x Receiver) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Receiver) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[5].Descriptor()
}

func (Receiver) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[5]
}

func (x Receiver) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Receiver.Descriptor instead.
func (Receiver) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{5}
}

type DisputeState int32

const (
	DisputeState_DISPUTE_STATE_UNSPECIFIED DisputeState = 0
	// dispute entry is created in db but dispute is not processed yet
	DisputeState_CREATED DisputeState = 1
	// dispute entry is processed i.e ticket is created for it
	DisputeState_PROCESSED DisputeState = 2
	// dispute was resolved
	DisputeState_RESOLVED DisputeState = 3
	// dispute was accepted and resolved
	DisputeState_RESOLVED_ACCEPTED DisputeState = 4
	// dispute was rejected
	DisputeState_RESOLVED_REJECTED DisputeState = 5
	// dispute is escalated to respective vendor
	DisputeState_ESCALATED DisputeState = 6
	// something went wrong and manual intervention is needed for the dispute
	DisputeState_MANUAL_INTERVENTION DisputeState = 7
	// raise dispute failed due to some system error
	// we will allow user to raise a dispute again if dispute is moved to failed state
	DisputeState_FAILED DisputeState = 8
	// feedback comms is sent to user to know if user has directly received any update on dispute
	DisputeState_WAITING_ON_USER_FEEDBACK DisputeState = 9
)

// Enum value maps for DisputeState.
var (
	DisputeState_name = map[int32]string{
		0: "DISPUTE_STATE_UNSPECIFIED",
		1: "CREATED",
		2: "PROCESSED",
		3: "RESOLVED",
		4: "RESOLVED_ACCEPTED",
		5: "RESOLVED_REJECTED",
		6: "ESCALATED",
		7: "MANUAL_INTERVENTION",
		8: "FAILED",
		9: "WAITING_ON_USER_FEEDBACK",
	}
	DisputeState_value = map[string]int32{
		"DISPUTE_STATE_UNSPECIFIED": 0,
		"CREATED":                   1,
		"PROCESSED":                 2,
		"RESOLVED":                  3,
		"RESOLVED_ACCEPTED":         4,
		"RESOLVED_REJECTED":         5,
		"ESCALATED":                 6,
		"MANUAL_INTERVENTION":       7,
		"FAILED":                    8,
		"WAITING_ON_USER_FEEDBACK":  9,
	}
)

func (x DisputeState) Enum() *DisputeState {
	p := new(DisputeState)
	*p = x
	return p
}

func (x DisputeState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[6].Descriptor()
}

func (DisputeState) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[6]
}

func (x DisputeState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeState.Descriptor instead.
func (DisputeState) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{6}
}

type DisputeTicketState int32

const (
	DisputeTicketState_DISPUTE_TICKET_STATE_UNSPECIFIED DisputeTicketState = 0
	DisputeTicketState_ALREADY_CREATED                  DisputeTicketState = 1
	DisputeTicketState_UPDATE_FAILED                    DisputeTicketState = 2
	DisputeTicketState_UPDATE_SUCCESSFUL                DisputeTicketState = 3
	DisputeTicketState_CREATION_SUCCESSFUL              DisputeTicketState = 4
	DisputeTicketState_CREATION_FAILED                  DisputeTicketState = 5
	DisputeTicketState_PRIVATE_NOTE_ADD_SUCCESS         DisputeTicketState = 6
	DisputeTicketState_PRIVATE_NOTE_ADD_FAILED          DisputeTicketState = 7
)

// Enum value maps for DisputeTicketState.
var (
	DisputeTicketState_name = map[int32]string{
		0: "DISPUTE_TICKET_STATE_UNSPECIFIED",
		1: "ALREADY_CREATED",
		2: "UPDATE_FAILED",
		3: "UPDATE_SUCCESSFUL",
		4: "CREATION_SUCCESSFUL",
		5: "CREATION_FAILED",
		6: "PRIVATE_NOTE_ADD_SUCCESS",
		7: "PRIVATE_NOTE_ADD_FAILED",
	}
	DisputeTicketState_value = map[string]int32{
		"DISPUTE_TICKET_STATE_UNSPECIFIED": 0,
		"ALREADY_CREATED":                  1,
		"UPDATE_FAILED":                    2,
		"UPDATE_SUCCESSFUL":                3,
		"CREATION_SUCCESSFUL":              4,
		"CREATION_FAILED":                  5,
		"PRIVATE_NOTE_ADD_SUCCESS":         6,
		"PRIVATE_NOTE_ADD_FAILED":          7,
	}
)

func (x DisputeTicketState) Enum() *DisputeTicketState {
	p := new(DisputeTicketState)
	*p = x
	return p
}

func (x DisputeTicketState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeTicketState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[7].Descriptor()
}

func (DisputeTicketState) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[7]
}

func (x DisputeTicketState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeTicketState.Descriptor instead.
func (DisputeTicketState) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{7}
}

type DisputeNotificationStatus int32

const (
	DisputeNotificationStatus_DISPUTE_NOTIFICATION_STATUS_UNSPECIFIED DisputeNotificationStatus = 0
	DisputeNotificationStatus_SUCCESSFULLY_QUEUED                     DisputeNotificationStatus = 1
	DisputeNotificationStatus_FAILED_TO_QUEUE                         DisputeNotificationStatus = 2
)

// Enum value maps for DisputeNotificationStatus.
var (
	DisputeNotificationStatus_name = map[int32]string{
		0: "DISPUTE_NOTIFICATION_STATUS_UNSPECIFIED",
		1: "SUCCESSFULLY_QUEUED",
		2: "FAILED_TO_QUEUE",
	}
	DisputeNotificationStatus_value = map[string]int32{
		"DISPUTE_NOTIFICATION_STATUS_UNSPECIFIED": 0,
		"SUCCESSFULLY_QUEUED":                     1,
		"FAILED_TO_QUEUE":                         2,
	}
)

func (x DisputeNotificationStatus) Enum() *DisputeNotificationStatus {
	p := new(DisputeNotificationStatus)
	*p = x
	return p
}

func (x DisputeNotificationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeNotificationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[8].Descriptor()
}

func (DisputeNotificationStatus) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[8]
}

func (x DisputeNotificationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeNotificationStatus.Descriptor instead.
func (DisputeNotificationStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{8}
}

type Provenance int32

const (
	Provenance_PROVENANCE_UNSPECIFIED Provenance = 0
	// Signifies the order was created from the user APP.
	Provenance_USER_APP Provenance = 1
	// Signifies order was conceived in an external system
	// Few scenarios when order provenance is marked external-
	// 1) there was an offline transaction (credit/debit) in user's account and epiFi got notified either
	// using notification from vendor or by statement sync mechanism
	// 2) When an external PSP initiates a payment/collect request against epiFi user's VPA in UPI.
	Provenance_EXTERNAL Provenance = 2
	// Signifies order was conceived from an internal process in the system.
	// Few scenarios when order provenance is marked internal -
	// 1) Deposit creation with add funds flow. In this case, an order with URN_TRANSFER workflow is created from the
	// user's app, which internally on successfully processing creates another order with workflow CREATE_DEPOSIT.
	Provenance_INTERNAL Provenance = 3
	// Signifies that the order was created due to an ATM card transaction
	// Few scenarios when order provenance is marked ATM -
	// 1) Epifi received a notification for atm withdrawal
	Provenance_ATM Provenance = 4
	// Signifies that the order was created due to POS transaction
	// Few scenarios when order provenance is marked POS -
	// 1) Epifi received a notification for POS transaction
	Provenance_POS Provenance = 5
	// Signifies that the order was created due to ECOMM transaction
	// Few scenarios when order provenance is marked ECOMM -
	// 1) Epifi received a notification for ECOMM transaction
	Provenance_ECOMM Provenance = 6
	// Signifies that order was created due to third party application eg. Intent, QR
	Provenance_THIRD_PARTY Provenance = 7
	// Signifies order was created as a result of partner mobile banking payment
	// Few scenarios when order provenance is marked PARTNER_MOBILE_BANKING -
	// 1) User goes to partner mobile banking app and does a IMPS/NEFT/RTGS payment.
	Provenance_PARTNER_MOBILE_BANKING Provenance = 8
	// Signifies order was created due to a appeasement payout from customer support team
	Provenance_PROVENANCE_SHERLOCK Provenance = 9
	Provenance_PROVENANCE_NA       Provenance = 10
	// Signifies order was created as a result of partner net banking payment
	// Few scenarios when order provenance is marked PARTNER_NET_BANKING -
	// 1) User goes to website  and does a IMPS/NEFT/RTGS payment.
	Provenance_PARTNER_NET_BANKING Provenance = 11
	// orders created by vendor bank, like forex markup charge
	Provenance_VENDOR_BANK Provenance = 12
)

// Enum value maps for Provenance.
var (
	Provenance_name = map[int32]string{
		0:  "PROVENANCE_UNSPECIFIED",
		1:  "USER_APP",
		2:  "EXTERNAL",
		3:  "INTERNAL",
		4:  "ATM",
		5:  "POS",
		6:  "ECOMM",
		7:  "THIRD_PARTY",
		8:  "PARTNER_MOBILE_BANKING",
		9:  "PROVENANCE_SHERLOCK",
		10: "PROVENANCE_NA",
		11: "PARTNER_NET_BANKING",
		12: "VENDOR_BANK",
	}
	Provenance_value = map[string]int32{
		"PROVENANCE_UNSPECIFIED": 0,
		"USER_APP":               1,
		"EXTERNAL":               2,
		"INTERNAL":               3,
		"ATM":                    4,
		"POS":                    5,
		"ECOMM":                  6,
		"THIRD_PARTY":            7,
		"PARTNER_MOBILE_BANKING": 8,
		"PROVENANCE_SHERLOCK":    9,
		"PROVENANCE_NA":          10,
		"PARTNER_NET_BANKING":    11,
		"VENDOR_BANK":            12,
	}
)

func (x Provenance) Enum() *Provenance {
	p := new(Provenance)
	*p = x
	return p
}

func (x Provenance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Provenance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[9].Descriptor()
}

func (Provenance) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[9]
}

func (x Provenance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Provenance.Descriptor instead.
func (Provenance) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{9}
}

// dispute config version enum to be used when we want to maintain more than one parallel dispute version in the system
// this can be used for doing a controlled roll-out of breaking dispute changes
type DisputeConfigVersion int32

const (
	DisputeConfigVersion_DISPUTE_CONFIG_VERSION_UNSPECIFIED DisputeConfigVersion = 0
	// intial dispute config
	DisputeConfigVersion_DISPUTE_CONFIG_VERSION_V1 DisputeConfigVersion = 1
	// config version to be used for udir config changes
	DisputeConfigVersion_DISPUTE_CONFIG_VERSION_V2 DisputeConfigVersion = 2
	// config version to be used for supporting multiple question on the app changes
	DisputeConfigVersion_DISPUTE_CONFIG_VERSION_V3 DisputeConfigVersion = 3
	// config version to be used for making both udir and multiple questions flow live in the app together
	// we required this separate version since both version 2 and version 3 were developed in parallel and can go live in any order
	DisputeConfigVersion_DISPUTE_CONFIG_VERSION_V4 DisputeConfigVersion = 4
	// config version to be used to allow querying on previous attempt status field to move dispute to non-UDIR flow when escalating to UDIR fails
	DisputeConfigVersion_DISPUTE_CONFIG_VERSION_V5 DisputeConfigVersion = 5
	// config version to be used for escalating disputes to DMP and Freshdesk
	DisputeConfigVersion_DISPUTE_CONFIG_VERSION_V6 DisputeConfigVersion = 6
	// config version to be used for escalate disputes to Freshdesk, UDIR and DMP
	DisputeConfigVersion_DISPUTE_CONFIG_VERSION_V7 DisputeConfigVersion = 7
	// config version to be used for escalate disputes to Freshdesk, UDIR and DMP
	DisputeConfigVersion_DISPUTE_CONFIG_VERSION_V8 DisputeConfigVersion = 8
)

// Enum value maps for DisputeConfigVersion.
var (
	DisputeConfigVersion_name = map[int32]string{
		0: "DISPUTE_CONFIG_VERSION_UNSPECIFIED",
		1: "DISPUTE_CONFIG_VERSION_V1",
		2: "DISPUTE_CONFIG_VERSION_V2",
		3: "DISPUTE_CONFIG_VERSION_V3",
		4: "DISPUTE_CONFIG_VERSION_V4",
		5: "DISPUTE_CONFIG_VERSION_V5",
		6: "DISPUTE_CONFIG_VERSION_V6",
		7: "DISPUTE_CONFIG_VERSION_V7",
		8: "DISPUTE_CONFIG_VERSION_V8",
	}
	DisputeConfigVersion_value = map[string]int32{
		"DISPUTE_CONFIG_VERSION_UNSPECIFIED": 0,
		"DISPUTE_CONFIG_VERSION_V1":          1,
		"DISPUTE_CONFIG_VERSION_V2":          2,
		"DISPUTE_CONFIG_VERSION_V3":          3,
		"DISPUTE_CONFIG_VERSION_V4":          4,
		"DISPUTE_CONFIG_VERSION_V5":          5,
		"DISPUTE_CONFIG_VERSION_V6":          6,
		"DISPUTE_CONFIG_VERSION_V7":          7,
		"DISPUTE_CONFIG_VERSION_V8":          8,
	}
)

func (x DisputeConfigVersion) Enum() *DisputeConfigVersion {
	p := new(DisputeConfigVersion)
	*p = x
	return p
}

func (x DisputeConfigVersion) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeConfigVersion) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[10].Descriptor()
}

func (DisputeConfigVersion) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[10]
}

func (x DisputeConfigVersion) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeConfigVersion.Descriptor instead.
func (DisputeConfigVersion) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{10}
}

type UDIRComplaintResponseAction int32

const (
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_UNSPECIFIED                         UDIRComplaintResponseAction = 0
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_REFUND_REVERSAL_CONFIRMATION        UDIRComplaintResponseAction = 1
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_CHARGEBACK_ACCEPTANCE               UDIRComplaintResponseAction = 2
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_ACCEPTANCE              UDIRComplaintResponseAction = 3
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_CONTINUATION            UDIRComplaintResponseAction = 4
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_VERDICT                 UDIRComplaintResponseAction = 5
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_WITHDRAWN               UDIRComplaintResponseAction = 6
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_ACCEPTANCE          UDIRComplaintResponseAction = 7
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_RAISE                   UDIRComplaintResponseAction = 8
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_CHARGEBACK_RAISE                    UDIRComplaintResponseAction = 9
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_CREDIT_ADJUSTMENT                   UDIRComplaintResponseAction = 10
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_DEBIT_REVERSAL_CONFIRMATION         UDIRComplaintResponseAction = 11
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_CHARGEBACK_ACCEPTANCE      UDIRComplaintResponseAction = 12
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_ACCEPTANCE UDIRComplaintResponseAction = 13
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_ARBITRATION_RAISE          UDIRComplaintResponseAction = 14
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_CHARGEBACK_RAISE           UDIRComplaintResponseAction = 15
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_RAISE              UDIRComplaintResponseAction = 16
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_ACCEPT             UDIRComplaintResponseAction = 17
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_REPRESENTMENT      UDIRComplaintResponseAction = 18
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_RAISE      UDIRComplaintResponseAction = 19
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_DECLINED   UDIRComplaintResponseAction = 20
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_RE_PRESENTMENT_RAISE       UDIRComplaintResponseAction = 21
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_MANUAL_ADJUSTMENT                   UDIRComplaintResponseAction = 22
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_RAISE               UDIRComplaintResponseAction = 23
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_DECLINED            UDIRComplaintResponseAction = 24
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_RESPONSE_TO_COMPLAINT               UDIRComplaintResponseAction = 25
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_RE_PRESENTMENT_RAISE                UDIRComplaintResponseAction = 26
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_RET                                 UDIRComplaintResponseAction = 27
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_TCC                                 UDIRComplaintResponseAction = 28
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_CHARGEBACK_ACCEPTANCE  UDIRComplaintResponseAction = 29
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_REPRESENTMENT          UDIRComplaintResponseAction = 30
	UDIRComplaintResponseAction_UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_CHARGEBACK_RAISE       UDIRComplaintResponseAction = 31
)

// Enum value maps for UDIRComplaintResponseAction.
var (
	UDIRComplaintResponseAction_name = map[int32]string{
		0:  "UDIR_COMPLAINT_RESPONSE_ACTION_UNSPECIFIED",
		1:  "UDIR_COMPLAINT_RESPONSE_ACTION_REFUND_REVERSAL_CONFIRMATION",
		2:  "UDIR_COMPLAINT_RESPONSE_ACTION_CHARGEBACK_ACCEPTANCE",
		3:  "UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_ACCEPTANCE",
		4:  "UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_CONTINUATION",
		5:  "UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_VERDICT",
		6:  "UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_WITHDRAWN",
		7:  "UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_ACCEPTANCE",
		8:  "UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_RAISE",
		9:  "UDIR_COMPLAINT_RESPONSE_ACTION_CHARGEBACK_RAISE",
		10: "UDIR_COMPLAINT_RESPONSE_ACTION_CREDIT_ADJUSTMENT",
		11: "UDIR_COMPLAINT_RESPONSE_ACTION_DEBIT_REVERSAL_CONFIRMATION",
		12: "UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_CHARGEBACK_ACCEPTANCE",
		13: "UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_ACCEPTANCE",
		14: "UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_ARBITRATION_RAISE",
		15: "UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_CHARGEBACK_RAISE",
		16: "UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_RAISE",
		17: "UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_ACCEPT",
		18: "UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_REPRESENTMENT",
		19: "UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_RAISE",
		20: "UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_DECLINED",
		21: "UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_RE_PRESENTMENT_RAISE",
		22: "UDIR_COMPLAINT_RESPONSE_ACTION_MANUAL_ADJUSTMENT",
		23: "UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_RAISE",
		24: "UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_DECLINED",
		25: "UDIR_COMPLAINT_RESPONSE_ACTION_RESPONSE_TO_COMPLAINT",
		26: "UDIR_COMPLAINT_RESPONSE_ACTION_RE_PRESENTMENT_RAISE",
		27: "UDIR_COMPLAINT_RESPONSE_ACTION_RET",
		28: "UDIR_COMPLAINT_RESPONSE_ACTION_TCC",
		29: "UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_CHARGEBACK_ACCEPTANCE",
		30: "UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_REPRESENTMENT",
		31: "UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_CHARGEBACK_RAISE",
	}
	UDIRComplaintResponseAction_value = map[string]int32{
		"UDIR_COMPLAINT_RESPONSE_ACTION_UNSPECIFIED":                         0,
		"UDIR_COMPLAINT_RESPONSE_ACTION_REFUND_REVERSAL_CONFIRMATION":        1,
		"UDIR_COMPLAINT_RESPONSE_ACTION_CHARGEBACK_ACCEPTANCE":               2,
		"UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_ACCEPTANCE":              3,
		"UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_CONTINUATION":            4,
		"UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_VERDICT":                 5,
		"UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_WITHDRAWN":               6,
		"UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_ACCEPTANCE":          7,
		"UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_RAISE":                   8,
		"UDIR_COMPLAINT_RESPONSE_ACTION_CHARGEBACK_RAISE":                    9,
		"UDIR_COMPLAINT_RESPONSE_ACTION_CREDIT_ADJUSTMENT":                   10,
		"UDIR_COMPLAINT_RESPONSE_ACTION_DEBIT_REVERSAL_CONFIRMATION":         11,
		"UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_CHARGEBACK_ACCEPTANCE":      12,
		"UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_ACCEPTANCE": 13,
		"UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_ARBITRATION_RAISE":          14,
		"UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_CHARGEBACK_RAISE":           15,
		"UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_RAISE":              16,
		"UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_ACCEPT":             17,
		"UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_REPRESENTMENT":      18,
		"UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_RAISE":      19,
		"UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_DECLINED":   20,
		"UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_RE_PRESENTMENT_RAISE":       21,
		"UDIR_COMPLAINT_RESPONSE_ACTION_MANUAL_ADJUSTMENT":                   22,
		"UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_RAISE":               23,
		"UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_DECLINED":            24,
		"UDIR_COMPLAINT_RESPONSE_ACTION_RESPONSE_TO_COMPLAINT":               25,
		"UDIR_COMPLAINT_RESPONSE_ACTION_RE_PRESENTMENT_RAISE":                26,
		"UDIR_COMPLAINT_RESPONSE_ACTION_RET":                                 27,
		"UDIR_COMPLAINT_RESPONSE_ACTION_TCC":                                 28,
		"UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_CHARGEBACK_ACCEPTANCE":  29,
		"UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_REPRESENTMENT":          30,
		"UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_CHARGEBACK_RAISE":       31,
	}
)

func (x UDIRComplaintResponseAction) Enum() *UDIRComplaintResponseAction {
	p := new(UDIRComplaintResponseAction)
	*p = x
	return p
}

func (x UDIRComplaintResponseAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UDIRComplaintResponseAction) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[11].Descriptor()
}

func (UDIRComplaintResponseAction) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[11]
}

func (x UDIRComplaintResponseAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UDIRComplaintResponseAction.Descriptor instead.
func (UDIRComplaintResponseAction) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{11}
}

type UDIRComplaintResponseReason int32

const (
	UDIRComplaintResponseReason_UDIR_COMPLAINT_RESPONSE_REASON_UNSPECIFIED UDIRComplaintResponseReason = 0
	// refund reversal confirmation
	UDIRComplaintResponseReason_UDIR_COMPLAINT_RESPONSE_REASON_CUSTOMER_ACCOUNT_CREDITED UDIRComplaintResponseReason = 1
)

// Enum value maps for UDIRComplaintResponseReason.
var (
	UDIRComplaintResponseReason_name = map[int32]string{
		0: "UDIR_COMPLAINT_RESPONSE_REASON_UNSPECIFIED",
		1: "UDIR_COMPLAINT_RESPONSE_REASON_CUSTOMER_ACCOUNT_CREDITED",
	}
	UDIRComplaintResponseReason_value = map[string]int32{
		"UDIR_COMPLAINT_RESPONSE_REASON_UNSPECIFIED":               0,
		"UDIR_COMPLAINT_RESPONSE_REASON_CUSTOMER_ACCOUNT_CREDITED": 1,
	}
)

func (x UDIRComplaintResponseReason) Enum() *UDIRComplaintResponseReason {
	p := new(UDIRComplaintResponseReason)
	*p = x
	return p
}

func (x UDIRComplaintResponseReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UDIRComplaintResponseReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[12].Descriptor()
}

func (UDIRComplaintResponseReason) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[12]
}

func (x UDIRComplaintResponseReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UDIRComplaintResponseReason.Descriptor instead.
func (UDIRComplaintResponseReason) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{12}
}

// enum to represent dispute stage to be shown to user in the app
// this will contain possible list of derived statues to be shown to user
type DisputeStageForApp int32

const (
	DisputeStageForApp_DISPUTE_STAGE_FOR_APP_UNSPECIFIED            DisputeStageForApp = 0
	DisputeStageForApp_DISPUTE_STAGE_FOR_APP_COMPLAINT_RAISED       DisputeStageForApp = 1
	DisputeStageForApp_DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS DisputeStageForApp = 2
	DisputeStageForApp_DISPUTE_STAGE_FOR_APP_CHARGEBACK_IN_PROGRESS DisputeStageForApp = 3
	DisputeStageForApp_DISPUTE_STAGE_FOR_APP_COMPLAINT_RESOLVED     DisputeStageForApp = 4
)

// Enum value maps for DisputeStageForApp.
var (
	DisputeStageForApp_name = map[int32]string{
		0: "DISPUTE_STAGE_FOR_APP_UNSPECIFIED",
		1: "DISPUTE_STAGE_FOR_APP_COMPLAINT_RAISED",
		2: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS",
		3: "DISPUTE_STAGE_FOR_APP_CHARGEBACK_IN_PROGRESS",
		4: "DISPUTE_STAGE_FOR_APP_COMPLAINT_RESOLVED",
	}
	DisputeStageForApp_value = map[string]int32{
		"DISPUTE_STAGE_FOR_APP_UNSPECIFIED":            0,
		"DISPUTE_STAGE_FOR_APP_COMPLAINT_RAISED":       1,
		"DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS": 2,
		"DISPUTE_STAGE_FOR_APP_CHARGEBACK_IN_PROGRESS": 3,
		"DISPUTE_STAGE_FOR_APP_COMPLAINT_RESOLVED":     4,
	}
)

func (x DisputeStageForApp) Enum() *DisputeStageForApp {
	p := new(DisputeStageForApp)
	*p = x
	return p
}

func (x DisputeStageForApp) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeStageForApp) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[13].Descriptor()
}

func (DisputeStageForApp) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[13]
}

func (x DisputeStageForApp) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeStageForApp.Descriptor instead.
func (DisputeStageForApp) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{13}
}

// fields which can be updated in the FederalDmpDisputeDetails table
type FederalDmpDisputeDetailsFieldMask int32

const (
	FederalDmpDisputeDetailsFieldMask_FEDERAL_DMP_DISPUTE_DETAILS_FIELD_MASK_UNSPECIFIED FederalDmpDisputeDetailsFieldMask = 0
	// dispute case number uniquely identifies a raised dispute in Federal's DMP system
	FederalDmpDisputeDetailsFieldMask_FEDERAL_DMP_DISPUTE_DETAILS_FIELD_MASK_DISPUTE_CASE_NUMBER FederalDmpDisputeDetailsFieldMask = 1
	// raw field values in response to DMP requests
	FederalDmpDisputeDetailsFieldMask_FEDERAL_DMP_DISPUTE_DETAILS_FIELD_MASK_FEDERAL_DMP_RAW_DISPUTE_EVENT FederalDmpDisputeDetailsFieldMask = 2
	// work flow status of transaction on which dispute is raised
	FederalDmpDisputeDetailsFieldMask_FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_WORK_FLOW_STATUS FederalDmpDisputeDetailsFieldMask = 3
	// comment on work flow
	FederalDmpDisputeDetailsFieldMask_FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_WORK_FLOW_COMMENTS FederalDmpDisputeDetailsFieldMask = 4
	// order of the workflow status against a transaction on which dispute is raised.
	FederalDmpDisputeDetailsFieldMask_FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_STATUS_ORDER FederalDmpDisputeDetailsFieldMask = 5
	// transaction number
	FederalDmpDisputeDetailsFieldMask_FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_TRANSACTION_NUMBER FederalDmpDisputeDetailsFieldMask = 6
	// Name of the department that updated the status of transaction on which dispute is raised
	FederalDmpDisputeDetailsFieldMask_FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_DEPARTMENT FederalDmpDisputeDetailsFieldMask = 7
	// Name of the user that updated the status of transaction on which dispute is raised
	FederalDmpDisputeDetailsFieldMask_FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_USER_NAME FederalDmpDisputeDetailsFieldMask = 8
)

// Enum value maps for FederalDmpDisputeDetailsFieldMask.
var (
	FederalDmpDisputeDetailsFieldMask_name = map[int32]string{
		0: "FEDERAL_DMP_DISPUTE_DETAILS_FIELD_MASK_UNSPECIFIED",
		1: "FEDERAL_DMP_DISPUTE_DETAILS_FIELD_MASK_DISPUTE_CASE_NUMBER",
		2: "FEDERAL_DMP_DISPUTE_DETAILS_FIELD_MASK_FEDERAL_DMP_RAW_DISPUTE_EVENT",
		3: "FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_WORK_FLOW_STATUS",
		4: "FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_WORK_FLOW_COMMENTS",
		5: "FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_STATUS_ORDER",
		6: "FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_TRANSACTION_NUMBER",
		7: "FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_DEPARTMENT",
		8: "FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_USER_NAME",
	}
	FederalDmpDisputeDetailsFieldMask_value = map[string]int32{
		"FEDERAL_DMP_DISPUTE_DETAILS_FIELD_MASK_UNSPECIFIED":                   0,
		"FEDERAL_DMP_DISPUTE_DETAILS_FIELD_MASK_DISPUTE_CASE_NUMBER":           1,
		"FEDERAL_DMP_DISPUTE_DETAILS_FIELD_MASK_FEDERAL_DMP_RAW_DISPUTE_EVENT": 2,
		"FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_WORK_FLOW_STATUS":              3,
		"FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_WORK_FLOW_COMMENTS":            4,
		"FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_STATUS_ORDER":                  5,
		"FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_TRANSACTION_NUMBER":            6,
		"FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_DEPARTMENT":                    7,
		"FEDERAL_DMP_DISPUTE_DETAILS_FILED_MASK_USER_NAME":                     8,
	}
)

func (x FederalDmpDisputeDetailsFieldMask) Enum() *FederalDmpDisputeDetailsFieldMask {
	p := new(FederalDmpDisputeDetailsFieldMask)
	*p = x
	return p
}

func (x FederalDmpDisputeDetailsFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FederalDmpDisputeDetailsFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[14].Descriptor()
}

func (FederalDmpDisputeDetailsFieldMask) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[14]
}

func (x FederalDmpDisputeDetailsFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FederalDmpDisputeDetailsFieldMask.Descriptor instead.
func (FederalDmpDisputeDetailsFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{14}
}

// PreviousAttemptStatus denotes any past dispute attempt status for a txn id
// this status can be related to dispute creation/escalation/processing/resolution
// this status is not directly tied with dispute state enum
// these are custom statuses which are defined on use-case basis
// so when dispute config table is queried next time we can pass this additional
// info in the query and we can return different escalation mode in query response
type PreviousAttemptStatus int32

const (
	// unspecified
	PreviousAttemptStatus_PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED PreviousAttemptStatus = 0
	// escalating dispute to UDIR system failed for a given txn id
	// primary use case is to escalate dispute to non-UDIR flow when escalating to UDIR system fails
	PreviousAttemptStatus_PREVIOUS_ATTEMPT_STATUS_ESCALATING_TO_UDIR_FAILED PreviousAttemptStatus = 1
	// Federal bank escalation was found for given txn id
	// Federal bank DMP APIs does not allow to re-raise dispute on
	// same txn id if one dispute is already registered on their end by DMP API
	// primary use case is to escalate dispute to non-Federal Bank escalation mode like Freshdesk in case
	// where such past Federal bank attempt is found for given txn id
	PreviousAttemptStatus_PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE PreviousAttemptStatus = 2
)

// Enum value maps for PreviousAttemptStatus.
var (
	PreviousAttemptStatus_name = map[int32]string{
		0: "PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED",
		1: "PREVIOUS_ATTEMPT_STATUS_ESCALATING_TO_UDIR_FAILED",
		2: "PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE",
	}
	PreviousAttemptStatus_value = map[string]int32{
		"PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED":                               0,
		"PREVIOUS_ATTEMPT_STATUS_ESCALATING_TO_UDIR_FAILED":                 1,
		"PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE": 2,
	}
)

func (x PreviousAttemptStatus) Enum() *PreviousAttemptStatus {
	p := new(PreviousAttemptStatus)
	*p = x
	return p
}

func (x PreviousAttemptStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PreviousAttemptStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[15].Descriptor()
}

func (PreviousAttemptStatus) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[15]
}

func (x PreviousAttemptStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PreviousAttemptStatus.Descriptor instead.
func (PreviousAttemptStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{15}
}

// CX side mapping for complaint dispute state enum
type UDIRComplaintDisputeState int32

const (
	UDIRComplaintDisputeState_UDIR_COMPLAINT_DISPUTE_STATE_UNSPECIFIED UDIRComplaintDisputeState = 0
	// request to raise dispute is initiated and is IN_PROGRESS
	UDIRComplaintDisputeState_UDIR_COMPLAINT_DISPUTE_STATE_RAISE_IN_PROGRESS UDIRComplaintDisputeState = 1
	// request to raise dispute failed
	UDIRComplaintDisputeState_UDIR_COMPLAINT_DISPUTE_STATE_RAISE_FAILED UDIRComplaintDisputeState = 2
	// current status of raised dispute is in progress
	UDIRComplaintDisputeState_UDIR_COMPLAINT_DISPUTE_STATE_IN_PROGRESS UDIRComplaintDisputeState = 3
	// raised dispute is resolved
	UDIRComplaintDisputeState_UDIR_COMPLAINT_DISPUTE_STATE_RESOLVED UDIRComplaintDisputeState = 4
)

// Enum value maps for UDIRComplaintDisputeState.
var (
	UDIRComplaintDisputeState_name = map[int32]string{
		0: "UDIR_COMPLAINT_DISPUTE_STATE_UNSPECIFIED",
		1: "UDIR_COMPLAINT_DISPUTE_STATE_RAISE_IN_PROGRESS",
		2: "UDIR_COMPLAINT_DISPUTE_STATE_RAISE_FAILED",
		3: "UDIR_COMPLAINT_DISPUTE_STATE_IN_PROGRESS",
		4: "UDIR_COMPLAINT_DISPUTE_STATE_RESOLVED",
	}
	UDIRComplaintDisputeState_value = map[string]int32{
		"UDIR_COMPLAINT_DISPUTE_STATE_UNSPECIFIED":       0,
		"UDIR_COMPLAINT_DISPUTE_STATE_RAISE_IN_PROGRESS": 1,
		"UDIR_COMPLAINT_DISPUTE_STATE_RAISE_FAILED":      2,
		"UDIR_COMPLAINT_DISPUTE_STATE_IN_PROGRESS":       3,
		"UDIR_COMPLAINT_DISPUTE_STATE_RESOLVED":          4,
	}
)

func (x UDIRComplaintDisputeState) Enum() *UDIRComplaintDisputeState {
	p := new(UDIRComplaintDisputeState)
	*p = x
	return p
}

func (x UDIRComplaintDisputeState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UDIRComplaintDisputeState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[16].Descriptor()
}

func (UDIRComplaintDisputeState) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[16]
}

func (x UDIRComplaintDisputeState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UDIRComplaintDisputeState.Descriptor instead.
func (UDIRComplaintDisputeState) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{16}
}

type FileContentType int32

const (
	FileContentType_FILE_CONTENT_TYPE_UNSPECIFIED FileContentType = 0
	FileContentType_FILE_CONTENT_TYPE_CSV         FileContentType = 1
	FileContentType_FILE_CONTENT_TYPE_TXT         FileContentType = 2
	FileContentType_FILE_CONTENT_TYPE_JPEG        FileContentType = 3
	FileContentType_FILE_CONTENT_TYPE_XLS         FileContentType = 4
	FileContentType_FILE_CONTENT_TYPE_XLSX        FileContentType = 5
	FileContentType_FILE_CONTENT_TYPE_PDF         FileContentType = 6
	FileContentType_FILE_CONTENT_TYPE_DOC         FileContentType = 7
	FileContentType_FILE_CONTENT_TYPE_DOCX        FileContentType = 8
)

// Enum value maps for FileContentType.
var (
	FileContentType_name = map[int32]string{
		0: "FILE_CONTENT_TYPE_UNSPECIFIED",
		1: "FILE_CONTENT_TYPE_CSV",
		2: "FILE_CONTENT_TYPE_TXT",
		3: "FILE_CONTENT_TYPE_JPEG",
		4: "FILE_CONTENT_TYPE_XLS",
		5: "FILE_CONTENT_TYPE_XLSX",
		6: "FILE_CONTENT_TYPE_PDF",
		7: "FILE_CONTENT_TYPE_DOC",
		8: "FILE_CONTENT_TYPE_DOCX",
	}
	FileContentType_value = map[string]int32{
		"FILE_CONTENT_TYPE_UNSPECIFIED": 0,
		"FILE_CONTENT_TYPE_CSV":         1,
		"FILE_CONTENT_TYPE_TXT":         2,
		"FILE_CONTENT_TYPE_JPEG":        3,
		"FILE_CONTENT_TYPE_XLS":         4,
		"FILE_CONTENT_TYPE_XLSX":        5,
		"FILE_CONTENT_TYPE_PDF":         6,
		"FILE_CONTENT_TYPE_DOC":         7,
		"FILE_CONTENT_TYPE_DOCX":        8,
	}
)

func (x FileContentType) Enum() *FileContentType {
	p := new(FileContentType)
	*p = x
	return p
}

func (x FileContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[17].Descriptor()
}

func (FileContentType) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[17]
}

func (x FileContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileContentType.Descriptor instead.
func (FileContentType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{17}
}

type Organisation int32

const (
	Organisation_ORGANISATION_UNSPECIFIED  Organisation = 0
	Organisation_ORGANISATION_FI           Organisation = 1
	Organisation_ORGANISATION_FEDERAL_BANK Organisation = 2
)

// Enum value maps for Organisation.
var (
	Organisation_name = map[int32]string{
		0: "ORGANISATION_UNSPECIFIED",
		1: "ORGANISATION_FI",
		2: "ORGANISATION_FEDERAL_BANK",
	}
	Organisation_value = map[string]int32{
		"ORGANISATION_UNSPECIFIED":  0,
		"ORGANISATION_FI":           1,
		"ORGANISATION_FEDERAL_BANK": 2,
	}
)

func (x Organisation) Enum() *Organisation {
	p := new(Organisation)
	*p = x
	return p
}

func (x Organisation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Organisation) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[18].Descriptor()
}

func (Organisation) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[18]
}

func (x Organisation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Organisation.Descriptor instead.
func (Organisation) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{18}
}

// The workflow status of the transaction.
type WorkFlowStatus int32

const (
	WorkFlowStatus_WORK_FLOW_STATUS_UNSPECIFIED                   WorkFlowStatus = 0
	WorkFlowStatus_WORK_FLOW_STATUS_OPENED                        WorkFlowStatus = 1
	WorkFlowStatus_WORK_FLOW_STATUS_OPENED_FOR_CHARGEBACK         WorkFlowStatus = 2
	WorkFlowStatus_WORK_FLOW_STATUS_INITIATED_FOR_CHARGE_BACK     WorkFlowStatus = 3
	WorkFlowStatus_WORK_FLOW_STATUS_RAISED_FOR_CHARGE_BACK        WorkFlowStatus = 4
	WorkFlowStatus_WORK_FLOW_STATUS_CHARGEBACK_APPROVED           WorkFlowStatus = 5
	WorkFlowStatus_WORK_FLOW_STATUS_CHARGEBACK_REJECTED           WorkFlowStatus = 6
	WorkFlowStatus_WORK_FLOW_STATUS_OPENED_FOR_PRE_ARBITRATION    WorkFlowStatus = 7
	WorkFlowStatus_WORK_FLOW_STATUS_INITIATED_FOR_PRE_ARBITRATION WorkFlowStatus = 8
	WorkFlowStatus_WORK_FLOW_STATUS_RAISED_FOR_PRE_ARBITRATION    WorkFlowStatus = 9
	WorkFlowStatus_WORK_FLOW_STATUS_PRE_ARBITRATION_APPROVED      WorkFlowStatus = 10
	WorkFlowStatus_WORK_FLOW_STATUS_PRE_ARBITRATION_REJECTED      WorkFlowStatus = 11
	WorkFlowStatus_WORK_FLOW_STATUS_CLOSED                        WorkFlowStatus = 12
	WorkFlowStatus_WORK_FLOW_STATUS_AWAITING_RESPONSE_FROM_BRANCH WorkFlowStatus = 13
	WorkFlowStatus_WORK_FLOW_STATUS_DISPUTE_REGISTERED            WorkFlowStatus = 14
	WorkFlowStatus_WORK_FLOW_STATUS_LIABILITY_COMPUTED            WorkFlowStatus = 15
	WorkFlowStatus_WORK_FLOW_STATUS_LIABILITY_REJECTED            WorkFlowStatus = 16
	WorkFlowStatus_WORK_FLOW_STATUS_LIABILITY_APPROVED            WorkFlowStatus = 17
	WorkFlowStatus_WORK_FLOW_STATUS_RECOMMENDED_FOR_ACCEPTING     WorkFlowStatus = 18
	WorkFlowStatus_WORK_FLOW_STATUS_RECHECK_F                     WorkFlowStatus = 19
	WorkFlowStatus_WORK_FLOW_STATUS_ACCEPTED                      WorkFlowStatus = 20
	WorkFlowStatus_WORK_FLOW_STATUS_RECOMMENDED_FOR_REJECTING     WorkFlowStatus = 21
	WorkFlowStatus_WORK_FLOW_STATUS_RECHECK_NF                    WorkFlowStatus = 22
	WorkFlowStatus_WORK_FLOW_STATUS_REJECTED                      WorkFlowStatus = 23
	WorkFlowStatus_WORK_FLOW_STATUS_RAISE_CHARGE_BACK             WorkFlowStatus = 24
)

// Enum value maps for WorkFlowStatus.
var (
	WorkFlowStatus_name = map[int32]string{
		0:  "WORK_FLOW_STATUS_UNSPECIFIED",
		1:  "WORK_FLOW_STATUS_OPENED",
		2:  "WORK_FLOW_STATUS_OPENED_FOR_CHARGEBACK",
		3:  "WORK_FLOW_STATUS_INITIATED_FOR_CHARGE_BACK",
		4:  "WORK_FLOW_STATUS_RAISED_FOR_CHARGE_BACK",
		5:  "WORK_FLOW_STATUS_CHARGEBACK_APPROVED",
		6:  "WORK_FLOW_STATUS_CHARGEBACK_REJECTED",
		7:  "WORK_FLOW_STATUS_OPENED_FOR_PRE_ARBITRATION",
		8:  "WORK_FLOW_STATUS_INITIATED_FOR_PRE_ARBITRATION",
		9:  "WORK_FLOW_STATUS_RAISED_FOR_PRE_ARBITRATION",
		10: "WORK_FLOW_STATUS_PRE_ARBITRATION_APPROVED",
		11: "WORK_FLOW_STATUS_PRE_ARBITRATION_REJECTED",
		12: "WORK_FLOW_STATUS_CLOSED",
		13: "WORK_FLOW_STATUS_AWAITING_RESPONSE_FROM_BRANCH",
		14: "WORK_FLOW_STATUS_DISPUTE_REGISTERED",
		15: "WORK_FLOW_STATUS_LIABILITY_COMPUTED",
		16: "WORK_FLOW_STATUS_LIABILITY_REJECTED",
		17: "WORK_FLOW_STATUS_LIABILITY_APPROVED",
		18: "WORK_FLOW_STATUS_RECOMMENDED_FOR_ACCEPTING",
		19: "WORK_FLOW_STATUS_RECHECK_F",
		20: "WORK_FLOW_STATUS_ACCEPTED",
		21: "WORK_FLOW_STATUS_RECOMMENDED_FOR_REJECTING",
		22: "WORK_FLOW_STATUS_RECHECK_NF",
		23: "WORK_FLOW_STATUS_REJECTED",
		24: "WORK_FLOW_STATUS_RAISE_CHARGE_BACK",
	}
	WorkFlowStatus_value = map[string]int32{
		"WORK_FLOW_STATUS_UNSPECIFIED":                   0,
		"WORK_FLOW_STATUS_OPENED":                        1,
		"WORK_FLOW_STATUS_OPENED_FOR_CHARGEBACK":         2,
		"WORK_FLOW_STATUS_INITIATED_FOR_CHARGE_BACK":     3,
		"WORK_FLOW_STATUS_RAISED_FOR_CHARGE_BACK":        4,
		"WORK_FLOW_STATUS_CHARGEBACK_APPROVED":           5,
		"WORK_FLOW_STATUS_CHARGEBACK_REJECTED":           6,
		"WORK_FLOW_STATUS_OPENED_FOR_PRE_ARBITRATION":    7,
		"WORK_FLOW_STATUS_INITIATED_FOR_PRE_ARBITRATION": 8,
		"WORK_FLOW_STATUS_RAISED_FOR_PRE_ARBITRATION":    9,
		"WORK_FLOW_STATUS_PRE_ARBITRATION_APPROVED":      10,
		"WORK_FLOW_STATUS_PRE_ARBITRATION_REJECTED":      11,
		"WORK_FLOW_STATUS_CLOSED":                        12,
		"WORK_FLOW_STATUS_AWAITING_RESPONSE_FROM_BRANCH": 13,
		"WORK_FLOW_STATUS_DISPUTE_REGISTERED":            14,
		"WORK_FLOW_STATUS_LIABILITY_COMPUTED":            15,
		"WORK_FLOW_STATUS_LIABILITY_REJECTED":            16,
		"WORK_FLOW_STATUS_LIABILITY_APPROVED":            17,
		"WORK_FLOW_STATUS_RECOMMENDED_FOR_ACCEPTING":     18,
		"WORK_FLOW_STATUS_RECHECK_F":                     19,
		"WORK_FLOW_STATUS_ACCEPTED":                      20,
		"WORK_FLOW_STATUS_RECOMMENDED_FOR_REJECTING":     21,
		"WORK_FLOW_STATUS_RECHECK_NF":                    22,
		"WORK_FLOW_STATUS_REJECTED":                      23,
		"WORK_FLOW_STATUS_RAISE_CHARGE_BACK":             24,
	}
)

func (x WorkFlowStatus) Enum() *WorkFlowStatus {
	p := new(WorkFlowStatus)
	*p = x
	return p
}

func (x WorkFlowStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkFlowStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_dispute_enums_proto_enumTypes[19].Descriptor()
}

func (WorkFlowStatus) Type() protoreflect.EnumType {
	return &file_api_cx_dispute_enums_proto_enumTypes[19]
}

func (x WorkFlowStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkFlowStatus.Descriptor instead.
func (WorkFlowStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_dispute_enums_proto_rawDescGZIP(), []int{19}
}

var File_api_cx_dispute_enums_proto protoreflect.FileDescriptor

var file_api_cx_dispute_enums_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x63, 0x78,
	0x2e, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x2a, 0xac, 0x01, 0x0a, 0x07, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a,
	0x03, 0x55, 0x50, 0x49, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x4d, 0x50, 0x53, 0x10, 0x02,
	0x12, 0x08, 0x0a, 0x04, 0x4e, 0x45, 0x46, 0x54, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x52, 0x54,
	0x47, 0x53, 0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x41, 0x54, 0x4d, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x45, 0x42, 0x49,
	0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f,
	0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x45, 0x43, 0x4f, 0x4d, 0x10,
	0x07, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x48, 0x45, 0x51, 0x55, 0x45, 0x10, 0x08, 0x12, 0x0e, 0x0a,
	0x0a, 0x49, 0x4e, 0x54, 0x52, 0x41, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x09, 0x12, 0x08, 0x0a,
	0x04, 0x43, 0x41, 0x52, 0x44, 0x10, 0x0a, 0x2a, 0x37, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x50, 0x50,
	0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x48, 0x45, 0x52, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x02,
	0x2a, 0x90, 0x01, 0x0a, 0x0b, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1c, 0x0a, 0x18, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e,
	0x0a, 0x0a, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x53, 0x45, 0x44, 0x10, 0x01, 0x12, 0x10,
	0x0a, 0x0c, 0x55, 0x4e, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x53, 0x45, 0x44, 0x10, 0x02,
	0x12, 0x15, 0x0a, 0x11, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49,
	0x43, 0x49, 0x41, 0x52, 0x59, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x57, 0x52, 0x4f, 0x4e, 0x47,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10,
	0x04, 0x12, 0x10, 0x0a, 0x0c, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e,
	0x54, 0x10, 0x05, 0x2a, 0xb8, 0x01, 0x0a, 0x0e, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x46, 0x54, 0x50, 0x10,
	0x01, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x52, 0x45, 0x53, 0x48, 0x44, 0x45, 0x53, 0x4b, 0x10, 0x02,
	0x12, 0x1f, 0x0a, 0x17, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x03, 0x1a, 0x02, 0x08,
	0x01, 0x12, 0x18, 0x0a, 0x14, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x44, 0x49, 0x52, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x4e,
	0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c,
	0x45, 0x53, 0x43, 0x41, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f,
	0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x06, 0x2a, 0x65,
	0x0a, 0x11, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x02, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x45, 0x45, 0x4d, 0x45, 0x44, 0x5f, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x10, 0x03, 0x2a, 0x3e, 0x0a, 0x08, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x72, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x50,
	0x32, 0x50, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x32, 0x4d, 0x10, 0x02, 0x12, 0x06, 0x0a,
	0x02, 0x4e, 0x41, 0x10, 0x03, 0x2a, 0xd7, 0x01, 0x0a, 0x0c, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x53, 0x4f, 0x4c, 0x56, 0x45, 0x44, 0x10, 0x03, 0x12,
	0x15, 0x0a, 0x11, 0x52, 0x45, 0x53, 0x4f, 0x4c, 0x56, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x45,
	0x50, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x53, 0x4f, 0x4c, 0x56,
	0x45, 0x44, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0d, 0x0a,
	0x09, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41, 0x54, 0x45, 0x44, 0x10, 0x06, 0x12, 0x17, 0x0a, 0x13,
	0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x08, 0x12, 0x1c, 0x0a, 0x18, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x4e, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x09, 0x2a,
	0xe2, 0x01, 0x0a, 0x12, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x20, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54,
	0x45, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f,
	0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46,
	0x55, 0x4c, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x52, 0x49,
	0x56, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x06, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x52, 0x49, 0x56, 0x41,
	0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x07, 0x2a, 0x76, 0x0a, 0x19, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x2b, 0x0a, 0x27, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17,
	0x0a, 0x13, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x4c, 0x59, 0x5f, 0x51,
	0x55, 0x45, 0x55, 0x45, 0x44, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x51, 0x55, 0x45, 0x55, 0x45, 0x10, 0x02, 0x2a, 0xf2, 0x01, 0x0a,
	0x0a, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x50,
	0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x41, 0x50, 0x50, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x03, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x54, 0x4d, 0x10, 0x04, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x4f,
	0x53, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x43, 0x4f, 0x4d, 0x4d, 0x10, 0x06, 0x12, 0x0f,
	0x0a, 0x0b, 0x54, 0x48, 0x49, 0x52, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x59, 0x10, 0x07, 0x12,
	0x1a, 0x0a, 0x16, 0x50, 0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c,
	0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x08, 0x12, 0x17, 0x0a, 0x13, 0x50,
	0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x48, 0x45, 0x52, 0x4c, 0x4f,
	0x43, 0x4b, 0x10, 0x09, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x4e, 0x41, 0x10, 0x0a, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x41, 0x52, 0x54, 0x4e,
	0x45, 0x52, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x0b,
	0x12, 0x0f, 0x0a, 0x0b, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10,
	0x0c, 0x2a, 0xb6, 0x02, 0x0a, 0x14, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x22, 0x44, 0x49,
	0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x5f, 0x56, 0x45, 0x52,
	0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x4f,
	0x4e, 0x46, 0x49, 0x47, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x31, 0x10,
	0x01, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4e,
	0x46, 0x49, 0x47, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0x02,
	0x12, 0x1d, 0x0a, 0x19, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x47, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x33, 0x10, 0x03, 0x12,
	0x1d, 0x0a, 0x19, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49,
	0x47, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x34, 0x10, 0x04, 0x12, 0x1d,
	0x0a, 0x19, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47,
	0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x35, 0x10, 0x05, 0x12, 0x1d, 0x0a,
	0x19, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x5f,
	0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x36, 0x10, 0x06, 0x12, 0x1d, 0x0a, 0x19,
	0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x5f, 0x56,
	0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x37, 0x10, 0x07, 0x12, 0x1d, 0x0a, 0x19, 0x44,
	0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x5f, 0x56, 0x45,
	0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x38, 0x10, 0x08, 0x2a, 0x97, 0x0f, 0x0a, 0x1b, 0x55,
	0x44, 0x49, 0x52, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x2a, 0x55, 0x44,
	0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x3f, 0x0a, 0x3b, 0x55, 0x44,
	0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x46,
	0x55, 0x4e, 0x44, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e,
	0x46, 0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x38, 0x0a, 0x34, 0x55,
	0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x48,
	0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41,
	0x4e, 0x43, 0x45, 0x10, 0x02, 0x12, 0x39, 0x0a, 0x35, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x03,
	0x12, 0x3b, 0x0a, 0x37, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49,
	0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x49, 0x4e, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x36, 0x0a,
	0x32, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x45, 0x52, 0x44,
	0x49, 0x43, 0x54, 0x10, 0x05, 0x12, 0x38, 0x0a, 0x34, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x4e, 0x10, 0x06, 0x12,
	0x3d, 0x0a, 0x39, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e,
	0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x07, 0x12, 0x34,
	0x0a, 0x30, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54,
	0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41, 0x49,
	0x53, 0x45, 0x10, 0x08, 0x12, 0x33, 0x0a, 0x2f, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x10, 0x09, 0x12, 0x34, 0x0a, 0x30, 0x55, 0x44, 0x49,
	0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x41, 0x44, 0x4a, 0x55, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0a, 0x12,
	0x3e, 0x0a, 0x3a, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e,
	0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x41, 0x4c,
	0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0b, 0x12,
	0x41, 0x0a, 0x3d, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e,
	0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x44, 0x49, 0x46, 0x46, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47,
	0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41, 0x4e, 0x43, 0x45,
	0x10, 0x0c, 0x12, 0x46, 0x0a, 0x42, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x49, 0x46, 0x46, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x50, 0x52,
	0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43,
	0x43, 0x45, 0x50, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x0d, 0x12, 0x3d, 0x0a, 0x39, 0x55, 0x44,
	0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x49, 0x46,
	0x46, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x10, 0x0e, 0x12, 0x3c, 0x0a, 0x38, 0x55, 0x44, 0x49,
	0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x49, 0x46, 0x46,
	0x45, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x52, 0x41, 0x49, 0x53, 0x45, 0x10, 0x0f, 0x12, 0x39, 0x0a, 0x35, 0x55, 0x44, 0x49, 0x52, 0x5f,
	0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e,
	0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x52, 0x41, 0x55, 0x44, 0x5f,
	0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45,
	0x10, 0x10, 0x12, 0x3a, 0x0a, 0x36, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x52, 0x41, 0x55, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47,
	0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x10, 0x11, 0x12, 0x41,
	0x0a, 0x3d, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54,
	0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x52, 0x41, 0x55, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x52, 0x45, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x12, 0x12, 0x41, 0x0a, 0x3d, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41,
	0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x49, 0x46, 0x46, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x45,
	0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41, 0x49,
	0x53, 0x45, 0x10, 0x13, 0x12, 0x44, 0x0a, 0x40, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x49, 0x46, 0x46, 0x45, 0x52, 0x45, 0x44, 0x5f,
	0x50, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x44, 0x45, 0x43, 0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x14, 0x12, 0x40, 0x0a, 0x3c, 0x55, 0x44,
	0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x49, 0x46,
	0x46, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x10, 0x15, 0x12, 0x34, 0x0a, 0x30,
	0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d,
	0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x44, 0x4a, 0x55, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x16, 0x12, 0x38, 0x0a, 0x34, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x10, 0x17, 0x12, 0x3b, 0x0a, 0x37,
	0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50,
	0x52, 0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44,
	0x45, 0x43, 0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x18, 0x12, 0x38, 0x0a, 0x34, 0x55, 0x44, 0x49,
	0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e,
	0x54, 0x10, 0x19, 0x12, 0x37, 0x0a, 0x33, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x10, 0x1a, 0x12, 0x26, 0x0a, 0x22,
	0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x54, 0x10, 0x1b, 0x12, 0x26, 0x0a, 0x22, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x43, 0x43, 0x10, 0x1c, 0x12, 0x45, 0x0a, 0x41,
	0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57,
	0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x52,
	0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41, 0x4e, 0x43,
	0x45, 0x10, 0x1d, 0x12, 0x3d, 0x0a, 0x39, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x1e, 0x12, 0x40, 0x0a, 0x3c, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x41, 0x49,
	0x53, 0x45, 0x10, 0x1f, 0x2a, 0x8b, 0x01, 0x0a, 0x1b, 0x55, 0x44, 0x49, 0x52, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x2a, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x3c, 0x0a, 0x38, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x45, 0x44,
	0x10, 0x01, 0x2a, 0xf9, 0x01, 0x0a, 0x12, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x12, 0x25, 0x0a, 0x21, 0x44, 0x49, 0x53,
	0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x41,
	0x50, 0x50, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x2a, 0x0a, 0x26, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41,
	0x49, 0x4e, 0x54, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x44, 0x10, 0x01, 0x12, 0x30, 0x0a, 0x2c,
	0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x4c, 0x55, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x30,
	0x0a, 0x2c, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41,
	0x43, 0x4b, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x03,
	0x12, 0x2c, 0x0a, 0x28, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41,
	0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x4c, 0x56, 0x45, 0x44, 0x10, 0x04, 0x2a, 0xc6,
	0x04, 0x0a, 0x21, 0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x44, 0x6d, 0x70, 0x44, 0x69, 0x73,
	0x70, 0x75, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4d, 0x61, 0x73, 0x6b, 0x12, 0x36, 0x0a, 0x32, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f,
	0x44, 0x4d, 0x50, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x3e, 0x0a, 0x3a,
	0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x44, 0x4d, 0x50, 0x5f, 0x44, 0x49, 0x53, 0x50,
	0x55, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43,
	0x41, 0x53, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x01, 0x12, 0x48, 0x0a, 0x44,
	0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x44, 0x4d, 0x50, 0x5f, 0x44, 0x49, 0x53, 0x50,
	0x55, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x44,
	0x4d, 0x50, 0x5f, 0x52, 0x41, 0x57, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x45,
	0x56, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x3b, 0x0a, 0x37, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41,
	0x4c, 0x5f, 0x44, 0x4d, 0x50, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x10, 0x03, 0x12, 0x3d, 0x0a, 0x39, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x44,
	0x4d, 0x50, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49,
	0x4c, 0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x57, 0x4f,
	0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x53,
	0x10, 0x04, 0x12, 0x37, 0x0a, 0x33, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x44, 0x4d,
	0x50, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0x05, 0x12, 0x3d, 0x0a, 0x39, 0x46,
	0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x44, 0x4d, 0x50, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55,
	0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x06, 0x12, 0x35, 0x0a, 0x31, 0x46, 0x45,
	0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x44, 0x4d, 0x50, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54,
	0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x50, 0x41, 0x52, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x07, 0x12, 0x34, 0x0a, 0x30, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x44, 0x4d, 0x50,
	0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x08, 0x2a, 0xbe, 0x01, 0x0a, 0x15, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x6f, 0x75, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x27, 0x0a, 0x23, 0x50, 0x52, 0x45, 0x56, 0x49, 0x4f, 0x55, 0x53, 0x5f, 0x41, 0x54,
	0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x35, 0x0a, 0x31, 0x50, 0x52,
	0x45, 0x56, 0x49, 0x4f, 0x55, 0x53, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41, 0x54, 0x49, 0x4e, 0x47,
	0x5f, 0x54, 0x4f, 0x5f, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x45, 0x0a, 0x41, 0x50, 0x52, 0x45, 0x56, 0x49, 0x4f, 0x55, 0x53, 0x5f, 0x41, 0x54,
	0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x45, 0x44,
	0x45, 0x52, 0x41, 0x4c, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c,
	0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x10, 0x02, 0x2a, 0x85, 0x02, 0x0a, 0x19, 0x55, 0x44, 0x49,
	0x52, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x28, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43,
	0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x32, 0x0a, 0x2e, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52,
	0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x2d, 0x0a, 0x29, 0x55, 0x44, 0x49, 0x52,
	0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55,
	0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x2c, 0x0a, 0x28, 0x55, 0x44, 0x49, 0x52, 0x5f,
	0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52,
	0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x55, 0x44, 0x49, 0x52, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x4c, 0x56, 0x45, 0x44, 0x10, 0x04,
	0x2a, 0x8f, 0x02, 0x0a, 0x0f, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e,
	0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x53, 0x56,
	0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45,
	0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x58, 0x54, 0x10, 0x02, 0x12, 0x1a, 0x0a,
	0x16, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4a, 0x50, 0x45, 0x47, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x58,
	0x4c, 0x53, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e,
	0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x58, 0x4c, 0x53, 0x58, 0x10, 0x05,
	0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x44, 0x46, 0x10, 0x06, 0x12, 0x19, 0x0a, 0x15, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x44, 0x4f, 0x43, 0x10, 0x07, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x58,
	0x10, 0x08, 0x2a, 0x60, 0x0a, 0x0c, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x18, 0x4f, 0x52, 0x47, 0x41, 0x4e, 0x49, 0x53, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x52, 0x47, 0x41, 0x4e, 0x49, 0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x49, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x52, 0x47, 0x41, 0x4e, 0x49, 0x53,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x42, 0x41,
	0x4e, 0x4b, 0x10, 0x02, 0x2a, 0x9c, 0x08, 0x0a, 0x0e, 0x57, 0x6f, 0x72, 0x6b, 0x46, 0x6c, 0x6f,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x1c, 0x57, 0x4f, 0x52, 0x4b, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x57, 0x4f, 0x52,
	0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x50,
	0x45, 0x4e, 0x45, 0x44, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x45,
	0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b,
	0x10, 0x02, 0x12, 0x2e, 0x0a, 0x2a, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x42, 0x41, 0x43, 0x4b,
	0x10, 0x03, 0x12, 0x2b, 0x0a, 0x27, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x44, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x04, 0x12,
	0x28, 0x0a, 0x24, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41,
	0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x05, 0x12, 0x28, 0x0a, 0x24, 0x57, 0x4f, 0x52,
	0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x48,
	0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x10, 0x06, 0x12, 0x2f, 0x0a, 0x2b, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x45, 0x44, 0x5f, 0x46,
	0x4f, 0x52, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x07, 0x12, 0x32, 0x0a, 0x2e, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54,
	0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x08, 0x12, 0x2f, 0x0a, 0x2b, 0x57, 0x4f, 0x52, 0x4b,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x41, 0x49,
	0x53, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x42, 0x49,
	0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x2d, 0x0a, 0x29, 0x57, 0x4f, 0x52,
	0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52,
	0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x50,
	0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x2d, 0x0a, 0x29, 0x57, 0x4f, 0x52, 0x4b,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x45,
	0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x4a,
	0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x0b, 0x12, 0x1b, 0x0a, 0x17, 0x57, 0x4f, 0x52, 0x4b, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x4f, 0x53,
	0x45, 0x44, 0x10, 0x0c, 0x12, 0x32, 0x0a, 0x2e, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e,
	0x47, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f,
	0x42, 0x52, 0x41, 0x4e, 0x43, 0x48, 0x10, 0x0d, 0x12, 0x27, 0x0a, 0x23, 0x57, 0x4f, 0x52, 0x4b,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x49, 0x53,
	0x50, 0x55, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x45, 0x44, 0x10,
	0x0e, 0x12, 0x27, 0x0a, 0x23, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45, 0x44, 0x10, 0x0f, 0x12, 0x27, 0x0a, 0x23, 0x57, 0x4f,
	0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c,
	0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x10, 0x10, 0x12, 0x27, 0x0a, 0x23, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x11, 0x12, 0x2e, 0x0a, 0x2a,
	0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x52, 0x45, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x44, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x12, 0x12, 0x1e, 0x0a, 0x1a,
	0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x52, 0x45, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x10, 0x13, 0x12, 0x1d, 0x0a, 0x19,
	0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x45, 0x44, 0x10, 0x14, 0x12, 0x2e, 0x0a, 0x2a, 0x57,
	0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x52, 0x45, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x44, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f,
	0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x15, 0x12, 0x1f, 0x0a, 0x1b, 0x57,
	0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x52, 0x45, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x4e, 0x46, 0x10, 0x16, 0x12, 0x1d, 0x0a, 0x19,
	0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x17, 0x12, 0x26, 0x0a, 0x22, 0x57,
	0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x52, 0x41, 0x49, 0x53, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x42, 0x41, 0x43,
	0x4b, 0x10, 0x18, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x5a, 0x25, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x69, 0x73, 0x70,
	0x75, 0x74, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_dispute_enums_proto_rawDescOnce sync.Once
	file_api_cx_dispute_enums_proto_rawDescData = file_api_cx_dispute_enums_proto_rawDesc
)

func file_api_cx_dispute_enums_proto_rawDescGZIP() []byte {
	file_api_cx_dispute_enums_proto_rawDescOnce.Do(func() {
		file_api_cx_dispute_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_dispute_enums_proto_rawDescData)
	})
	return file_api_cx_dispute_enums_proto_rawDescData
}

var file_api_cx_dispute_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 20)
var file_api_cx_dispute_enums_proto_goTypes = []interface{}{
	(Channel)(0),                           // 0: cx.dispute.Channel
	(Source)(0),                            // 1: cx.dispute.Source
	(DisputeType)(0),                       // 2: cx.dispute.DisputeType
	(EscalationMode)(0),                    // 3: cx.dispute.EscalationMode
	(TransactionStatus)(0),                 // 4: cx.dispute.TransactionStatus
	(Receiver)(0),                          // 5: cx.dispute.Receiver
	(DisputeState)(0),                      // 6: cx.dispute.DisputeState
	(DisputeTicketState)(0),                // 7: cx.dispute.DisputeTicketState
	(DisputeNotificationStatus)(0),         // 8: cx.dispute.DisputeNotificationStatus
	(Provenance)(0),                        // 9: cx.dispute.Provenance
	(DisputeConfigVersion)(0),              // 10: cx.dispute.DisputeConfigVersion
	(UDIRComplaintResponseAction)(0),       // 11: cx.dispute.UDIRComplaintResponseAction
	(UDIRComplaintResponseReason)(0),       // 12: cx.dispute.UDIRComplaintResponseReason
	(DisputeStageForApp)(0),                // 13: cx.dispute.DisputeStageForApp
	(FederalDmpDisputeDetailsFieldMask)(0), // 14: cx.dispute.FederalDmpDisputeDetailsFieldMask
	(PreviousAttemptStatus)(0),             // 15: cx.dispute.PreviousAttemptStatus
	(UDIRComplaintDisputeState)(0),         // 16: cx.dispute.UDIRComplaintDisputeState
	(FileContentType)(0),                   // 17: cx.dispute.FileContentType
	(Organisation)(0),                      // 18: cx.dispute.Organisation
	(WorkFlowStatus)(0),                    // 19: cx.dispute.WorkFlowStatus
}
var file_api_cx_dispute_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_cx_dispute_enums_proto_init() }
func file_api_cx_dispute_enums_proto_init() {
	if File_api_cx_dispute_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_dispute_enums_proto_rawDesc,
			NumEnums:      20,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_dispute_enums_proto_goTypes,
		DependencyIndexes: file_api_cx_dispute_enums_proto_depIdxs,
		EnumInfos:         file_api_cx_dispute_enums_proto_enumTypes,
	}.Build()
	File_api_cx_dispute_enums_proto = out.File
	file_api_cx_dispute_enums_proto_rawDesc = nil
	file_api_cx_dispute_enums_proto_goTypes = nil
	file_api_cx_dispute_enums_proto_depIdxs = nil
}
