// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/audit_log/audit_log.proto

package audit_log

import (
	casbin "github.com/epifi/gamma/api/casbin"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AccessStatus int32

const (
	AccessStatus_ACCESS_GRANTED_UNSPECIFIED AccessStatus = 0
	AccessStatus_ALLOWED                    AccessStatus = 1
	AccessStatus_DENIED                     AccessStatus = 2
)

// Enum value maps for AccessStatus.
var (
	AccessStatus_name = map[int32]string{
		0: "ACCESS_GRANTED_UNSPECIFIED",
		1: "ALLOWED",
		2: "DENIED",
	}
	AccessStatus_value = map[string]int32{
		"ACCESS_GRANTED_UNSPECIFIED": 0,
		"ALLOWED":                    1,
		"DENIED":                     2,
	}
)

func (x AccessStatus) Enum() *AccessStatus {
	p := new(AccessStatus)
	*p = x
	return p
}

func (x AccessStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccessStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_audit_log_audit_log_proto_enumTypes[0].Descriptor()
}

func (AccessStatus) Type() protoreflect.EnumType {
	return &file_api_cx_audit_log_audit_log_proto_enumTypes[0]
}

func (x AccessStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccessStatus.Descriptor instead.
func (AccessStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_audit_log_audit_log_proto_rawDescGZIP(), []int{0}
}

type Action int32

const (
	Action_ACTION_UNSPECIFIED      Action = 0
	Action_FETCH                   Action = 1
	Action_UPDATE                  Action = 2
	Action_DELETE                  Action = 3
	Action_BLOCK                   Action = 4
	Action_SUSPEND                 Action = 5
	Action_FREEZE                  Action = 6
	Action_VIEW                    Action = 7
	Action_CREATE                  Action = 8
	Action_CALL                    Action = 9
	Action_ENABLE                  Action = 10
	Action_DISABLE                 Action = 11
	Action_VERIFY                  Action = 12
	Action_SKIP                    Action = 13
	Action_EXECUTE                 Action = 14
	Action_RESET                   Action = 15
	Action_MERGE                   Action = 16
	Action_SEND                    Action = 17
	Action_ALLOW                   Action = 18
	Action_FETCH_BULK              Action = 19
	Action_DOWNLOAD                Action = 20
	Action_UPDATE_FREE_REPLACEMENT Action = 21
	Action_MARK_DISPUTE            Action = 22
)

// Enum value maps for Action.
var (
	Action_name = map[int32]string{
		0:  "ACTION_UNSPECIFIED",
		1:  "FETCH",
		2:  "UPDATE",
		3:  "DELETE",
		4:  "BLOCK",
		5:  "SUSPEND",
		6:  "FREEZE",
		7:  "VIEW",
		8:  "CREATE",
		9:  "CALL",
		10: "ENABLE",
		11: "DISABLE",
		12: "VERIFY",
		13: "SKIP",
		14: "EXECUTE",
		15: "RESET",
		16: "MERGE",
		17: "SEND",
		18: "ALLOW",
		19: "FETCH_BULK",
		20: "DOWNLOAD",
		21: "UPDATE_FREE_REPLACEMENT",
		22: "MARK_DISPUTE",
	}
	Action_value = map[string]int32{
		"ACTION_UNSPECIFIED":      0,
		"FETCH":                   1,
		"UPDATE":                  2,
		"DELETE":                  3,
		"BLOCK":                   4,
		"SUSPEND":                 5,
		"FREEZE":                  6,
		"VIEW":                    7,
		"CREATE":                  8,
		"CALL":                    9,
		"ENABLE":                  10,
		"DISABLE":                 11,
		"VERIFY":                  12,
		"SKIP":                    13,
		"EXECUTE":                 14,
		"RESET":                   15,
		"MERGE":                   16,
		"SEND":                    17,
		"ALLOW":                   18,
		"FETCH_BULK":              19,
		"DOWNLOAD":                20,
		"UPDATE_FREE_REPLACEMENT": 21,
		"MARK_DISPUTE":            22,
	}
)

func (x Action) Enum() *Action {
	p := new(Action)
	*p = x
	return p
}

func (x Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Action) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_audit_log_audit_log_proto_enumTypes[1].Descriptor()
}

func (Action) Type() protoreflect.EnumType {
	return &file_api_cx_audit_log_audit_log_proto_enumTypes[1]
}

func (x Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Action.Descriptor instead.
func (Action) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_audit_log_audit_log_proto_rawDescGZIP(), []int{1}
}

type Object int32

const (
	Object_OBJECT_UNSPECIFIED              Object = 0
	Object_TRANSACTIONS                    Object = 1
	Object_TRANSACTIONS_DISPUTE            Object = 2
	Object_AGENT                           Object = 3
	Object_RULES                           Object = 4
	Object_DEBIT_CARD                      Object = 5
	Object_SAVINGS_ACCOUNT                 Object = 6
	Object_DISPUTE_STATUS                  Object = 7
	Object_TICKET                          Object = 8
	Object_USER                            Object = 9
	Object_DEPOSITS                        Object = 10
	Object_PROFILE                         Object = 11
	Object_KYC                             Object = 12
	Object_UPI                             Object = 13
	Object_REWARDS                         Object = 14
	Object_OFFERS                          Object = 15
	Object_DISPUTE_CHANNEL                 Object = 16
	Object_DISPUTE_QUESTION                Object = 17
	Object_ONBOARDING                      Object = 18
	Object_ONBOARDING_SCORES               Object = 19
	Object_COMMUNICATIONS                  Object = 20
	Object_APP_LOGS                        Object = 21
	Object_AUTH_ACTION                     Object = 22
	Object_MOBILE_PROMPT                   Object = 23
	Object_EMAIL_VERIFICATION              Object = 24
	Object_TRANSACTION_AMOUNT              Object = 25
	Object_LAST_FIVE_PAN_CHARACTERS        Object = 26
	Object_MOTHERS_NAME                    Object = 27
	Object_FATHERS_NAME                    Object = 28
	Object_DOB                             Object = 29
	Object_PERMANENT_ADDRESS_PIN_CODE      Object = 30
	Object_DB_STATE_ENTITIES               Object = 31
	Object_DB_STATE_ENTITY_PARAMETERS      Object = 32
	Object_DB_STATE_ENTITY_DATA            Object = 33
	Object_DEVELOPER_ACTIONS               Object = 34
	Object_LIVENESS_VIDEO                  Object = 35
	Object_PAYOUT                          Object = 36
	Object_WAITLIST_USER                   Object = 37
	Object_RE_OOBE                         Object = 38
	Object_VKYC                            Object = 39
	Object_CALL_RECORDING                  Object = 40
	Object_CALL_TRANSCRIPT                 Object = 41
	Object_FM_IMAGE                        Object = 42
	Object_PULL_APP_LOGS_NOTIFICATION      Object = 43
	Object_TICKET_CONVERSATIONS            Object = 44
	Object_TICKET_GROUP                    Object = 45
	Object_LIVNESS_RETRY                   Object = 46
	Object_LIVENESS                        Object = 47
	Object_FIT_RULE                        Object = 48
	Object_FIT_SUBSCRIPTION                Object = 49
	Object_ACCOUNT_CLOSURE_STATUS          Object = 50
	Object_ADMIN_ACTIONS                   Object = 51
	Object_REFERRAL_DETAILS                Object = 52
	Object_REFEREE_DETAILS                 Object = 53
	Object_ALL_DISPUTES                    Object = 54
	Object_UPDATE_DISPUTES                 Object = 55
	Object_LIVENESS_ISSUE                  Object = 56
	Object_FACEMATCH_ISSUE                 Object = 57
	Object_SHERLOCK_USER                   Object = 58
	Object_CONNECTED_ACCOUNT_DETAILS       Object = 59
	Object_WEALTH_ONBOARDING               Object = 60
	Object_WEALTH_RISK_OPS                 Object = 61
	Object_INVESTMENT                      Object = 62
	Object_RISKOPS_PENDING_REVIEW_COUNT    Object = 63
	Object_FACEMATCH_IMAGE                 Object = 64
	Object_BULK_TICKET_JOB                 Object = 65
	Object_CALL_DETAILS                    Object = 66
	Object_ACCOUNT_FREEZE_DETAILS          Object = 67
	Object_P2P_INVESTMENT                  Object = 68
	Object_ACCOUNT_VALIDATION              Object = 69
	Object_SALARY_DATA_OPS                 Object = 70
	Object_SHERLOCK_BANNER                 Object = 71
	Object_LIVENESS_SAMPLE_ISSUE           Object = 72
	Object_AFU_LIVENESS_ISSUE              Object = 73
	Object_AFU_FACEMATCH_ISSUE             Object = 74
	Object_PAN_NAME_MATCH_ISSUE            Object = 75
	Object_FACEMATCH_SAMPLE_ISSUE          Object = 76
	Object_PAN_NAME_SAMPLE_ISSUE           Object = 77
	Object_LIVENESS_AND_FACEMATCH_ISSUE    Object = 78
	Object_L2_LIVENESS_ISSUE               Object = 79
	Object_L2_FACEMATCH_ISSUE              Object = 80
	Object_USER_ISSUE_INFO                 Object = 81
	Object_PAY_INTERNATIONAL_FUND_TRANSFER Object = 82
	Object_SHERLOCK_FEEDBACK               Object = 83
	Object_PRE_APPROVED_LOAN               Object = 84
	Object_FIREFLY                         Object = 85
	Object_USER_REVIEW                     Object = 86
	Object_US_STOCKS_OPS                   Object = 87
	Object_REVIEW_CASES                    Object = 88
	Object_REVIEW_CASE_FILTERS             Object = 89
	Object_TRANSACTION_DATA_FOR_REVIEW     Object = 90
	Object_TRANSACTION_REVIEW_FILTERS      Object = 91
	Object_TRANSACTION_REVIEW_ACTION       Object = 92
	Object_REVIEW_CASE_DETAILS             Object = 93
	Object_TIERING                         Object = 94
	Object_ALFRED                          Object = 95
	Object_REVIEW_ANNOTATIONS              Object = 96
	Object_REVIEW_COMMENTS                 Object = 97
	Object_REVIEW_ALLOWED_ANNOTATIONS      Object = 98
	Object_CREATE_ALLOWED_ANNOTATION       Object = 99
	Object_CREATE_COMMENT                  Object = 100
	Object_CREATE_ANNOTATION               Object = 101
	Object_REVIEW_ACTION_FORM_ELEMENTS     Object = 102
	Object_REVIEW_ACTION                   Object = 103
	Object_DISPUTE_DOCUMENT_DETAILS        Object = 104
	Object_DISPUTE_CORRESPONDENCE_DETAILS  Object = 105
	Object_CLOSED_ACCOUNT_DATA             Object = 106
	Object_UPDATE_RULE                     Object = 107
	Object_EPAN                            Object = 108
	Object_CREATE_RULE                     Object = 109
	Object_LIST_RULE                       Object = 110
	Object_USER_GROUP_MAPPINGS             Object = 111
	Object_AFU_DATA                        Object = 112
	Object_RELATED_CASES                   Object = 113
	Object_SALARY_PROGRAM                  Object = 114
	Object_ACCOUNT_STATEMENT               Object = 115
	Object_SHERLOCK_ACTOR_ACTIVITY         Object = 116
	Object_COMPLIANCE                      Object = 117
	Object_SCREENER_CHECKS                 Object = 118
	Object_PRODUCTS                        Object = 119
	Object_ISSUE_CONFIG                    Object = 120
	Object_QUESTIONNAIRE_RESPONSES         Object = 121
	Object_RULE_TAG                        Object = 122
	Object_VKYC_CALL_DETAILS               Object = 123
	Object_ISSUE_CATEGORIES                Object = 124
	Object_AVAILABLE_TABS                  Object = 125
	Object_USER_DETAILS_TAB                Object = 126
	Object_NOMINEES                        Object = 127
)

// Enum value maps for Object.
var (
	Object_name = map[int32]string{
		0:   "OBJECT_UNSPECIFIED",
		1:   "TRANSACTIONS",
		2:   "TRANSACTIONS_DISPUTE",
		3:   "AGENT",
		4:   "RULES",
		5:   "DEBIT_CARD",
		6:   "SAVINGS_ACCOUNT",
		7:   "DISPUTE_STATUS",
		8:   "TICKET",
		9:   "USER",
		10:  "DEPOSITS",
		11:  "PROFILE",
		12:  "KYC",
		13:  "UPI",
		14:  "REWARDS",
		15:  "OFFERS",
		16:  "DISPUTE_CHANNEL",
		17:  "DISPUTE_QUESTION",
		18:  "ONBOARDING",
		19:  "ONBOARDING_SCORES",
		20:  "COMMUNICATIONS",
		21:  "APP_LOGS",
		22:  "AUTH_ACTION",
		23:  "MOBILE_PROMPT",
		24:  "EMAIL_VERIFICATION",
		25:  "TRANSACTION_AMOUNT",
		26:  "LAST_FIVE_PAN_CHARACTERS",
		27:  "MOTHERS_NAME",
		28:  "FATHERS_NAME",
		29:  "DOB",
		30:  "PERMANENT_ADDRESS_PIN_CODE",
		31:  "DB_STATE_ENTITIES",
		32:  "DB_STATE_ENTITY_PARAMETERS",
		33:  "DB_STATE_ENTITY_DATA",
		34:  "DEVELOPER_ACTIONS",
		35:  "LIVENESS_VIDEO",
		36:  "PAYOUT",
		37:  "WAITLIST_USER",
		38:  "RE_OOBE",
		39:  "VKYC",
		40:  "CALL_RECORDING",
		41:  "CALL_TRANSCRIPT",
		42:  "FM_IMAGE",
		43:  "PULL_APP_LOGS_NOTIFICATION",
		44:  "TICKET_CONVERSATIONS",
		45:  "TICKET_GROUP",
		46:  "LIVNESS_RETRY",
		47:  "LIVENESS",
		48:  "FIT_RULE",
		49:  "FIT_SUBSCRIPTION",
		50:  "ACCOUNT_CLOSURE_STATUS",
		51:  "ADMIN_ACTIONS",
		52:  "REFERRAL_DETAILS",
		53:  "REFEREE_DETAILS",
		54:  "ALL_DISPUTES",
		55:  "UPDATE_DISPUTES",
		56:  "LIVENESS_ISSUE",
		57:  "FACEMATCH_ISSUE",
		58:  "SHERLOCK_USER",
		59:  "CONNECTED_ACCOUNT_DETAILS",
		60:  "WEALTH_ONBOARDING",
		61:  "WEALTH_RISK_OPS",
		62:  "INVESTMENT",
		63:  "RISKOPS_PENDING_REVIEW_COUNT",
		64:  "FACEMATCH_IMAGE",
		65:  "BULK_TICKET_JOB",
		66:  "CALL_DETAILS",
		67:  "ACCOUNT_FREEZE_DETAILS",
		68:  "P2P_INVESTMENT",
		69:  "ACCOUNT_VALIDATION",
		70:  "SALARY_DATA_OPS",
		71:  "SHERLOCK_BANNER",
		72:  "LIVENESS_SAMPLE_ISSUE",
		73:  "AFU_LIVENESS_ISSUE",
		74:  "AFU_FACEMATCH_ISSUE",
		75:  "PAN_NAME_MATCH_ISSUE",
		76:  "FACEMATCH_SAMPLE_ISSUE",
		77:  "PAN_NAME_SAMPLE_ISSUE",
		78:  "LIVENESS_AND_FACEMATCH_ISSUE",
		79:  "L2_LIVENESS_ISSUE",
		80:  "L2_FACEMATCH_ISSUE",
		81:  "USER_ISSUE_INFO",
		82:  "PAY_INTERNATIONAL_FUND_TRANSFER",
		83:  "SHERLOCK_FEEDBACK",
		84:  "PRE_APPROVED_LOAN",
		85:  "FIREFLY",
		86:  "USER_REVIEW",
		87:  "US_STOCKS_OPS",
		88:  "REVIEW_CASES",
		89:  "REVIEW_CASE_FILTERS",
		90:  "TRANSACTION_DATA_FOR_REVIEW",
		91:  "TRANSACTION_REVIEW_FILTERS",
		92:  "TRANSACTION_REVIEW_ACTION",
		93:  "REVIEW_CASE_DETAILS",
		94:  "TIERING",
		95:  "ALFRED",
		96:  "REVIEW_ANNOTATIONS",
		97:  "REVIEW_COMMENTS",
		98:  "REVIEW_ALLOWED_ANNOTATIONS",
		99:  "CREATE_ALLOWED_ANNOTATION",
		100: "CREATE_COMMENT",
		101: "CREATE_ANNOTATION",
		102: "REVIEW_ACTION_FORM_ELEMENTS",
		103: "REVIEW_ACTION",
		104: "DISPUTE_DOCUMENT_DETAILS",
		105: "DISPUTE_CORRESPONDENCE_DETAILS",
		106: "CLOSED_ACCOUNT_DATA",
		107: "UPDATE_RULE",
		108: "EPAN",
		109: "CREATE_RULE",
		110: "LIST_RULE",
		111: "USER_GROUP_MAPPINGS",
		112: "AFU_DATA",
		113: "RELATED_CASES",
		114: "SALARY_PROGRAM",
		115: "ACCOUNT_STATEMENT",
		116: "SHERLOCK_ACTOR_ACTIVITY",
		117: "COMPLIANCE",
		118: "SCREENER_CHECKS",
		119: "PRODUCTS",
		120: "ISSUE_CONFIG",
		121: "QUESTIONNAIRE_RESPONSES",
		122: "RULE_TAG",
		123: "VKYC_CALL_DETAILS",
		124: "ISSUE_CATEGORIES",
		125: "AVAILABLE_TABS",
		126: "USER_DETAILS_TAB",
		127: "NOMINEES",
	}
	Object_value = map[string]int32{
		"OBJECT_UNSPECIFIED":              0,
		"TRANSACTIONS":                    1,
		"TRANSACTIONS_DISPUTE":            2,
		"AGENT":                           3,
		"RULES":                           4,
		"DEBIT_CARD":                      5,
		"SAVINGS_ACCOUNT":                 6,
		"DISPUTE_STATUS":                  7,
		"TICKET":                          8,
		"USER":                            9,
		"DEPOSITS":                        10,
		"PROFILE":                         11,
		"KYC":                             12,
		"UPI":                             13,
		"REWARDS":                         14,
		"OFFERS":                          15,
		"DISPUTE_CHANNEL":                 16,
		"DISPUTE_QUESTION":                17,
		"ONBOARDING":                      18,
		"ONBOARDING_SCORES":               19,
		"COMMUNICATIONS":                  20,
		"APP_LOGS":                        21,
		"AUTH_ACTION":                     22,
		"MOBILE_PROMPT":                   23,
		"EMAIL_VERIFICATION":              24,
		"TRANSACTION_AMOUNT":              25,
		"LAST_FIVE_PAN_CHARACTERS":        26,
		"MOTHERS_NAME":                    27,
		"FATHERS_NAME":                    28,
		"DOB":                             29,
		"PERMANENT_ADDRESS_PIN_CODE":      30,
		"DB_STATE_ENTITIES":               31,
		"DB_STATE_ENTITY_PARAMETERS":      32,
		"DB_STATE_ENTITY_DATA":            33,
		"DEVELOPER_ACTIONS":               34,
		"LIVENESS_VIDEO":                  35,
		"PAYOUT":                          36,
		"WAITLIST_USER":                   37,
		"RE_OOBE":                         38,
		"VKYC":                            39,
		"CALL_RECORDING":                  40,
		"CALL_TRANSCRIPT":                 41,
		"FM_IMAGE":                        42,
		"PULL_APP_LOGS_NOTIFICATION":      43,
		"TICKET_CONVERSATIONS":            44,
		"TICKET_GROUP":                    45,
		"LIVNESS_RETRY":                   46,
		"LIVENESS":                        47,
		"FIT_RULE":                        48,
		"FIT_SUBSCRIPTION":                49,
		"ACCOUNT_CLOSURE_STATUS":          50,
		"ADMIN_ACTIONS":                   51,
		"REFERRAL_DETAILS":                52,
		"REFEREE_DETAILS":                 53,
		"ALL_DISPUTES":                    54,
		"UPDATE_DISPUTES":                 55,
		"LIVENESS_ISSUE":                  56,
		"FACEMATCH_ISSUE":                 57,
		"SHERLOCK_USER":                   58,
		"CONNECTED_ACCOUNT_DETAILS":       59,
		"WEALTH_ONBOARDING":               60,
		"WEALTH_RISK_OPS":                 61,
		"INVESTMENT":                      62,
		"RISKOPS_PENDING_REVIEW_COUNT":    63,
		"FACEMATCH_IMAGE":                 64,
		"BULK_TICKET_JOB":                 65,
		"CALL_DETAILS":                    66,
		"ACCOUNT_FREEZE_DETAILS":          67,
		"P2P_INVESTMENT":                  68,
		"ACCOUNT_VALIDATION":              69,
		"SALARY_DATA_OPS":                 70,
		"SHERLOCK_BANNER":                 71,
		"LIVENESS_SAMPLE_ISSUE":           72,
		"AFU_LIVENESS_ISSUE":              73,
		"AFU_FACEMATCH_ISSUE":             74,
		"PAN_NAME_MATCH_ISSUE":            75,
		"FACEMATCH_SAMPLE_ISSUE":          76,
		"PAN_NAME_SAMPLE_ISSUE":           77,
		"LIVENESS_AND_FACEMATCH_ISSUE":    78,
		"L2_LIVENESS_ISSUE":               79,
		"L2_FACEMATCH_ISSUE":              80,
		"USER_ISSUE_INFO":                 81,
		"PAY_INTERNATIONAL_FUND_TRANSFER": 82,
		"SHERLOCK_FEEDBACK":               83,
		"PRE_APPROVED_LOAN":               84,
		"FIREFLY":                         85,
		"USER_REVIEW":                     86,
		"US_STOCKS_OPS":                   87,
		"REVIEW_CASES":                    88,
		"REVIEW_CASE_FILTERS":             89,
		"TRANSACTION_DATA_FOR_REVIEW":     90,
		"TRANSACTION_REVIEW_FILTERS":      91,
		"TRANSACTION_REVIEW_ACTION":       92,
		"REVIEW_CASE_DETAILS":             93,
		"TIERING":                         94,
		"ALFRED":                          95,
		"REVIEW_ANNOTATIONS":              96,
		"REVIEW_COMMENTS":                 97,
		"REVIEW_ALLOWED_ANNOTATIONS":      98,
		"CREATE_ALLOWED_ANNOTATION":       99,
		"CREATE_COMMENT":                  100,
		"CREATE_ANNOTATION":               101,
		"REVIEW_ACTION_FORM_ELEMENTS":     102,
		"REVIEW_ACTION":                   103,
		"DISPUTE_DOCUMENT_DETAILS":        104,
		"DISPUTE_CORRESPONDENCE_DETAILS":  105,
		"CLOSED_ACCOUNT_DATA":             106,
		"UPDATE_RULE":                     107,
		"EPAN":                            108,
		"CREATE_RULE":                     109,
		"LIST_RULE":                       110,
		"USER_GROUP_MAPPINGS":             111,
		"AFU_DATA":                        112,
		"RELATED_CASES":                   113,
		"SALARY_PROGRAM":                  114,
		"ACCOUNT_STATEMENT":               115,
		"SHERLOCK_ACTOR_ACTIVITY":         116,
		"COMPLIANCE":                      117,
		"SCREENER_CHECKS":                 118,
		"PRODUCTS":                        119,
		"ISSUE_CONFIG":                    120,
		"QUESTIONNAIRE_RESPONSES":         121,
		"RULE_TAG":                        122,
		"VKYC_CALL_DETAILS":               123,
		"ISSUE_CATEGORIES":                124,
		"AVAILABLE_TABS":                  125,
		"USER_DETAILS_TAB":                126,
		"NOMINEES":                        127,
	}
)

func (x Object) Enum() *Object {
	p := new(Object)
	*p = x
	return p
}

func (x Object) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Object) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_audit_log_audit_log_proto_enumTypes[2].Descriptor()
}

func (Object) Type() protoreflect.EnumType {
	return &file_api_cx_audit_log_audit_log_proto_enumTypes[2]
}

func (x Object) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Object.Descriptor instead.
func (Object) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_audit_log_audit_log_proto_rawDescGZIP(), []int{2}
}

type AuditLogFieldMask int32

const (
	AuditLogFieldMask_AUDIT_LOG_FIELD_UNSPECIFIED AuditLogFieldMask = 0
	AuditLogFieldMask_ACCESS_STATUS               AuditLogFieldMask = 1
	AuditLogFieldMask_PARAMETERS                  AuditLogFieldMask = 2
	AuditLogFieldMask_ACTION                      AuditLogFieldMask = 3
	AuditLogFieldMask_OBJECT                      AuditLogFieldMask = 4
)

// Enum value maps for AuditLogFieldMask.
var (
	AuditLogFieldMask_name = map[int32]string{
		0: "AUDIT_LOG_FIELD_UNSPECIFIED",
		1: "ACCESS_STATUS",
		2: "PARAMETERS",
		3: "ACTION",
		4: "OBJECT",
	}
	AuditLogFieldMask_value = map[string]int32{
		"AUDIT_LOG_FIELD_UNSPECIFIED": 0,
		"ACCESS_STATUS":               1,
		"PARAMETERS":                  2,
		"ACTION":                      3,
		"OBJECT":                      4,
	}
)

func (x AuditLogFieldMask) Enum() *AuditLogFieldMask {
	p := new(AuditLogFieldMask)
	*p = x
	return p
}

func (x AuditLogFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuditLogFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_audit_log_audit_log_proto_enumTypes[3].Descriptor()
}

func (AuditLogFieldMask) Type() protoreflect.EnumType {
	return &file_api_cx_audit_log_audit_log_proto_enumTypes[3]
}

func (x AuditLogFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuditLogFieldMask.Descriptor instead.
func (AuditLogFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_audit_log_audit_log_proto_rawDescGZIP(), []int{3}
}

type AuditLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// audit log id
	Id           string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	AgentEmail   string             `protobuf:"bytes,2,opt,name=agent_email,json=agentEmail,proto3" json:"agent_email,omitempty"`
	TicketId     int64              `protobuf:"varint,3,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	AccessLevel  casbin.AccessLevel `protobuf:"varint,4,opt,name=access_level,json=accessLevel,proto3,enum=casbin.AccessLevel" json:"access_level,omitempty"`
	AccessStatus AccessStatus       `protobuf:"varint,5,opt,name=access_status,json=accessStatus,proto3,enum=cx.audit_log.AccessStatus" json:"access_status,omitempty"`
	Action       Action             `protobuf:"varint,6,opt,name=action,proto3,enum=cx.audit_log.Action" json:"action,omitempty"`
	Object       Object             `protobuf:"varint,7,opt,name=object,proto3,enum=cx.audit_log.Object" json:"object,omitempty"`
	Parameters   string             `protobuf:"bytes,8,opt,name=parameters,proto3" json:"parameters,omitempty"`
	// access time of request
	AccessedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=accessed_at,json=accessedAt,proto3" json:"accessed_at,omitempty"`
}

func (x *AuditLog) Reset() {
	*x = AuditLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_audit_log_audit_log_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuditLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditLog) ProtoMessage() {}

func (x *AuditLog) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_audit_log_audit_log_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditLog.ProtoReflect.Descriptor instead.
func (*AuditLog) Descriptor() ([]byte, []int) {
	return file_api_cx_audit_log_audit_log_proto_rawDescGZIP(), []int{0}
}

func (x *AuditLog) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AuditLog) GetAgentEmail() string {
	if x != nil {
		return x.AgentEmail
	}
	return ""
}

func (x *AuditLog) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

func (x *AuditLog) GetAccessLevel() casbin.AccessLevel {
	if x != nil {
		return x.AccessLevel
	}
	return casbin.AccessLevel(0)
}

func (x *AuditLog) GetAccessStatus() AccessStatus {
	if x != nil {
		return x.AccessStatus
	}
	return AccessStatus_ACCESS_GRANTED_UNSPECIFIED
}

func (x *AuditLog) GetAction() Action {
	if x != nil {
		return x.Action
	}
	return Action_ACTION_UNSPECIFIED
}

func (x *AuditLog) GetObject() Object {
	if x != nil {
		return x.Object
	}
	return Object_OBJECT_UNSPECIFIED
}

func (x *AuditLog) GetParameters() string {
	if x != nil {
		return x.Parameters
	}
	return ""
}

func (x *AuditLog) GetAccessedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.AccessedAt
	}
	return nil
}

// AuditLogsFilter is used in dao method to filter out audit logs
type AuditLogsFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// to get audit logs for a specific agent
	AgentEmail string `protobuf:"bytes,1,opt,name=agent_email,json=agentEmail,proto3" json:"agent_email,omitempty"`
	// to get audit logs of a specific ticket
	TicketId int64 `protobuf:"varint,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	// to get audit logs for a specific object
	Object Object `protobuf:"varint,3,opt,name=object,proto3,enum=cx.audit_log.Object" json:"object,omitempty"`
	// to get audit logs for a specific action
	Action Action `protobuf:"varint,4,opt,name=action,proto3,enum=cx.audit_log.Action" json:"action,omitempty"`
	// to get audit logs with given access_status
	AccessStatus AccessStatus `protobuf:"varint,5,opt,name=access_status,json=accessStatus,proto3,enum=cx.audit_log.AccessStatus" json:"access_status,omitempty"`
	// to get audit logs within range of ticket ids
	FromTicketId int64 `protobuf:"varint,6,opt,name=from_ticket_id,json=fromTicketId,proto3" json:"from_ticket_id,omitempty"`
	ToTicketId   int64 `protobuf:"varint,7,opt,name=to_ticket_id,json=toTicketId,proto3" json:"to_ticket_id,omitempty"`
	// number of audit logs required, cannot be greater than 50 and by default 10 is used
	ResponseLimit int64 `protobuf:"varint,8,opt,name=response_limit,json=responseLimit,proto3" json:"response_limit,omitempty"`
	// timestamp till which audit logs are fetched
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// specifies if we need the latest audit logs if true, or the earliest
	IsDescending bool `protobuf:"varint,10,opt,name=is_descending,json=isDescending,proto3" json:"is_descending,omitempty"`
	// offset required based on last fetched audit logs
	Offset int64 `protobuf:"varint,11,opt,name=offset,proto3" json:"offset,omitempty"`
}

func (x *AuditLogsFilter) Reset() {
	*x = AuditLogsFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_audit_log_audit_log_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuditLogsFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditLogsFilter) ProtoMessage() {}

func (x *AuditLogsFilter) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_audit_log_audit_log_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditLogsFilter.ProtoReflect.Descriptor instead.
func (*AuditLogsFilter) Descriptor() ([]byte, []int) {
	return file_api_cx_audit_log_audit_log_proto_rawDescGZIP(), []int{1}
}

func (x *AuditLogsFilter) GetAgentEmail() string {
	if x != nil {
		return x.AgentEmail
	}
	return ""
}

func (x *AuditLogsFilter) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

func (x *AuditLogsFilter) GetObject() Object {
	if x != nil {
		return x.Object
	}
	return Object_OBJECT_UNSPECIFIED
}

func (x *AuditLogsFilter) GetAction() Action {
	if x != nil {
		return x.Action
	}
	return Action_ACTION_UNSPECIFIED
}

func (x *AuditLogsFilter) GetAccessStatus() AccessStatus {
	if x != nil {
		return x.AccessStatus
	}
	return AccessStatus_ACCESS_GRANTED_UNSPECIFIED
}

func (x *AuditLogsFilter) GetFromTicketId() int64 {
	if x != nil {
		return x.FromTicketId
	}
	return 0
}

func (x *AuditLogsFilter) GetToTicketId() int64 {
	if x != nil {
		return x.ToTicketId
	}
	return 0
}

func (x *AuditLogsFilter) GetResponseLimit() int64 {
	if x != nil {
		return x.ResponseLimit
	}
	return 0
}

func (x *AuditLogsFilter) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *AuditLogsFilter) GetIsDescending() bool {
	if x != nil {
		return x.IsDescending
	}
	return false
}

func (x *AuditLogsFilter) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

var File_api_cx_audit_log_audit_log_proto protoreflect.FileDescriptor

var file_api_cx_audit_log_audit_log_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x6c,
	0x6f, 0x67, 0x2f, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0c, 0x63, 0x78, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x67,
	0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x62, 0x69, 0x6e, 0x2f, 0x63, 0x61, 0x73,
	0x62, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8a, 0x03, 0x0a, 0x08, 0x41,
	0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x61,
	0x73, 0x62, 0x69, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x3f, 0x0a,
	0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f,
	0x6c, 0x6f, 0x67, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c,
	0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x63, 0x78, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x06,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63,
	0x78, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x52, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x65, 0x64, 0x41, 0x74, 0x22, 0xd3, 0x03, 0x0a, 0x0f, 0x41, 0x75, 0x64, 0x69,
	0x74, 0x4c, 0x6f, 0x67, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x06, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x78, 0x2e, 0x61,
	0x75, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52,
	0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x75, 0x64,
	0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x63,
	0x78, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c,
	0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c,
	0x74, 0x6f, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x25,
	0x0a, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x44, 0x65, 0x73, 0x63, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x2a, 0x47, 0x0a,
	0x0c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a,
	0x1a, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x47, 0x52, 0x41, 0x4e, 0x54, 0x45, 0x44, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45,
	0x4e, 0x49, 0x45, 0x44, 0x10, 0x02, 0x2a, 0xbb, 0x02, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x45, 0x54,
	0x43, 0x48, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x02,
	0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05,
	0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x53, 0x50, 0x45,
	0x4e, 0x44, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x10, 0x06,
	0x12, 0x08, 0x0a, 0x04, 0x56, 0x49, 0x45, 0x57, 0x10, 0x07, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x10, 0x08, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x41, 0x4c, 0x4c, 0x10, 0x09,
	0x12, 0x0a, 0x0a, 0x06, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x0a, 0x12, 0x0b, 0x0a, 0x07,
	0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x0b, 0x12, 0x0a, 0x0a, 0x06, 0x56, 0x45, 0x52,
	0x49, 0x46, 0x59, 0x10, 0x0c, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x4b, 0x49, 0x50, 0x10, 0x0d, 0x12,
	0x0b, 0x0a, 0x07, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x45, 0x10, 0x0e, 0x12, 0x09, 0x0a, 0x05,
	0x52, 0x45, 0x53, 0x45, 0x54, 0x10, 0x0f, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x45, 0x52, 0x47, 0x45,
	0x10, 0x10, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x45, 0x4e, 0x44, 0x10, 0x11, 0x12, 0x09, 0x0a, 0x05,
	0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x10, 0x12, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x45, 0x54, 0x43, 0x48,
	0x5f, 0x42, 0x55, 0x4c, 0x4b, 0x10, 0x13, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x4f, 0x57, 0x4e, 0x4c,
	0x4f, 0x41, 0x44, 0x10, 0x14, 0x12, 0x1b, 0x0a, 0x17, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f,
	0x46, 0x52, 0x45, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x15, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55,
	0x54, 0x45, 0x10, 0x16, 0x2a, 0x88, 0x15, 0x0a, 0x06, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12,
	0x16, 0x0a, 0x12, 0x4f, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54,
	0x45, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x09,
	0x0a, 0x05, 0x52, 0x55, 0x4c, 0x45, 0x53, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x45, 0x42,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x41, 0x56,
	0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x06, 0x12, 0x12,
	0x0a, 0x0e, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x10, 0x07, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x10, 0x08, 0x12, 0x08,
	0x0a, 0x04, 0x55, 0x53, 0x45, 0x52, 0x10, 0x09, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x53, 0x10, 0x0a, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c,
	0x45, 0x10, 0x0b, 0x12, 0x07, 0x0a, 0x03, 0x4b, 0x59, 0x43, 0x10, 0x0c, 0x12, 0x07, 0x0a, 0x03,
	0x55, 0x50, 0x49, 0x10, 0x0d, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53,
	0x10, 0x0e, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x10, 0x0f, 0x12, 0x13,
	0x0a, 0x0f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45,
	0x4c, 0x10, 0x10, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x11, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x4e, 0x42,
	0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x12, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x4e, 0x42,
	0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x53, 0x10, 0x13,
	0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4f, 0x4d, 0x4d, 0x55, 0x4e, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x53, 0x10, 0x14, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x50, 0x50, 0x5f, 0x4c, 0x4f, 0x47, 0x53,
	0x10, 0x15, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x16, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x50, 0x52,
	0x4f, 0x4d, 0x50, 0x54, 0x10, 0x17, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x18, 0x12, 0x16,
	0x0a, 0x12, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4d,
	0x4f, 0x55, 0x4e, 0x54, 0x10, 0x19, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x46,
	0x49, 0x56, 0x45, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x41, 0x43, 0x54, 0x45,
	0x52, 0x53, 0x10, 0x1a, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x10, 0x1b, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x41, 0x54, 0x48, 0x45, 0x52,
	0x53, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x1c, 0x12, 0x07, 0x0a, 0x03, 0x44, 0x4f, 0x42, 0x10,
	0x1d, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x41,
	0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10,
	0x1e, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4e,
	0x54, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x1f, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41,
	0x4d, 0x45, 0x54, 0x45, 0x52, 0x53, 0x10, 0x20, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x10, 0x21, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x45, 0x56, 0x45, 0x4c, 0x4f, 0x50, 0x45, 0x52, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x22, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x23, 0x12, 0x0a, 0x0a,
	0x06, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x10, 0x24, 0x12, 0x11, 0x0a, 0x0d, 0x57, 0x41, 0x49,
	0x54, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x25, 0x12, 0x0b, 0x0a, 0x07,
	0x52, 0x45, 0x5f, 0x4f, 0x4f, 0x42, 0x45, 0x10, 0x26, 0x12, 0x08, 0x0a, 0x04, 0x56, 0x4b, 0x59,
	0x43, 0x10, 0x27, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x45, 0x43, 0x4f,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x28, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x4c, 0x4c, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x10, 0x29, 0x12, 0x0c, 0x0a, 0x08,
	0x46, 0x4d, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x2a, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x55,
	0x4c, 0x4c, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x4c, 0x4f, 0x47, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x2b, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x49,
	0x43, 0x4b, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x53, 0x10, 0x2c, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x47,
	0x52, 0x4f, 0x55, 0x50, 0x10, 0x2d, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x49, 0x56, 0x4e, 0x45, 0x53,
	0x53, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x10, 0x2e, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x2f, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x49, 0x54, 0x5f, 0x52,
	0x55, 0x4c, 0x45, 0x10, 0x30, 0x12, 0x14, 0x0a, 0x10, 0x46, 0x49, 0x54, 0x5f, 0x53, 0x55, 0x42,
	0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x31, 0x12, 0x1a, 0x0a, 0x16, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x32, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x44, 0x4d, 0x49, 0x4e,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x33, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45,
	0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x34,
	0x12, 0x13, 0x0a, 0x0f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x53, 0x10, 0x35, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x49, 0x53,
	0x50, 0x55, 0x54, 0x45, 0x53, 0x10, 0x36, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x53, 0x10, 0x37, 0x12, 0x12, 0x0a, 0x0e,
	0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10, 0x38,
	0x12, 0x13, 0x0a, 0x0f, 0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53,
	0x53, 0x55, 0x45, 0x10, 0x39, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x48, 0x45, 0x52, 0x4c, 0x4f, 0x43,
	0x4b, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x3a, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4e, 0x4e,
	0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x3b, 0x12, 0x15, 0x0a, 0x11, 0x57, 0x45, 0x41, 0x4c, 0x54,
	0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x3c, 0x12, 0x13,
	0x0a, 0x0f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x4f, 0x50,
	0x53, 0x10, 0x3d, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x3e, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x49, 0x53, 0x4b, 0x4f, 0x50, 0x53, 0x5f, 0x50,
	0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x10, 0x3f, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x40, 0x12, 0x13, 0x0a, 0x0f, 0x42, 0x55,
	0x4c, 0x4b, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x4a, 0x4f, 0x42, 0x10, 0x41, 0x12,
	0x10, 0x0a, 0x0c, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x42, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45,
	0x45, 0x5a, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x43, 0x12, 0x12, 0x0a,
	0x0e, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x44, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x45, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x41, 0x4c,
	0x41, 0x52, 0x59, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x4f, 0x50, 0x53, 0x10, 0x46, 0x12, 0x13,
	0x0a, 0x0f, 0x53, 0x48, 0x45, 0x52, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45,
	0x52, 0x10, 0x47, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f,
	0x53, 0x41, 0x4d, 0x50, 0x4c, 0x45, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10, 0x48, 0x12, 0x16,
	0x0a, 0x12, 0x41, 0x46, 0x55, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x49,
	0x53, 0x53, 0x55, 0x45, 0x10, 0x49, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x46, 0x55, 0x5f, 0x46, 0x41,
	0x43, 0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10, 0x4a, 0x12,
	0x18, 0x0a, 0x14, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10, 0x4b, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x41, 0x43,
	0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x41, 0x4d, 0x50, 0x4c, 0x45, 0x5f, 0x49, 0x53,
	0x53, 0x55, 0x45, 0x10, 0x4c, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x53, 0x41, 0x4d, 0x50, 0x4c, 0x45, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10, 0x4d,
	0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45,
	0x10, 0x4e, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x32, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53,
	0x53, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10, 0x4f, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x32, 0x5f,
	0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10,
	0x50, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x10, 0x51, 0x12, 0x23, 0x0a, 0x1f, 0x50, 0x41, 0x59, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x10, 0x52, 0x12, 0x15, 0x0a, 0x11, 0x53,
	0x48, 0x45, 0x52, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b,
	0x10, 0x53, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56,
	0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x54, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x49, 0x52,
	0x45, 0x46, 0x4c, 0x59, 0x10, 0x55, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x52,
	0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x56, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4f, 0x50, 0x53, 0x10, 0x57, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x45,
	0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x53, 0x10, 0x58, 0x12, 0x17, 0x0a, 0x13,
	0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x54,
	0x45, 0x52, 0x53, 0x10, 0x59, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x52, 0x45,
	0x56, 0x49, 0x45, 0x57, 0x10, 0x5a, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x46, 0x49, 0x4c,
	0x54, 0x45, 0x52, 0x53, 0x10, 0x5b, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x5c, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x43, 0x41, 0x53, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x5d, 0x12, 0x0b,
	0x0a, 0x07, 0x54, 0x49, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x5e, 0x12, 0x0a, 0x0a, 0x06, 0x41,
	0x4c, 0x46, 0x52, 0x45, 0x44, 0x10, 0x5f, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x60, 0x12,
	0x13, 0x0a, 0x0f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e,
	0x54, 0x53, 0x10, 0x61, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x41,
	0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x5f, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x53, 0x10, 0x62, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x41,
	0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x5f, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x63, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4f,
	0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x64, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x5f, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x65, 0x12, 0x1f,
	0x0a, 0x1b, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x66, 0x12,
	0x11, 0x0a, 0x0d, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x67, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x44, 0x4f,
	0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x68,
	0x12, 0x22, 0x0a, 0x1e, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x52, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49,
	0x4c, 0x53, 0x10, 0x69, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x6a, 0x12, 0x0f, 0x0a,
	0x0b, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x10, 0x6b, 0x12, 0x08,
	0x0a, 0x04, 0x45, 0x50, 0x41, 0x4e, 0x10, 0x6c, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x10, 0x6d, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x49, 0x53,
	0x54, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x10, 0x6e, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x53, 0x10,
	0x6f, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x46, 0x55, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x70, 0x12,
	0x11, 0x0a, 0x0d, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x53,
	0x10, 0x71, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x41, 0x4d, 0x10, 0x72, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x73, 0x12, 0x1b, 0x0a,
	0x17, 0x53, 0x48, 0x45, 0x52, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x10, 0x74, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x4f,
	0x4d, 0x50, 0x4c, 0x49, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x75, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x53, 0x10, 0x76, 0x12,
	0x0c, 0x0a, 0x08, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x77, 0x12, 0x10, 0x0a,
	0x0c, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0x78, 0x12,
	0x1b, 0x0a, 0x17, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x53, 0x10, 0x79, 0x12, 0x0c, 0x0a, 0x08,
	0x52, 0x55, 0x4c, 0x45, 0x5f, 0x54, 0x41, 0x47, 0x10, 0x7a, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x4b,
	0x59, 0x43, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x7b, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x49, 0x45, 0x53, 0x10, 0x7c, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x56, 0x41, 0x49, 0x4c,
	0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x41, 0x42, 0x53, 0x10, 0x7d, 0x12, 0x14, 0x0a, 0x10, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x54, 0x41, 0x42, 0x10,
	0x7e, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x4f, 0x4d, 0x49, 0x4e, 0x45, 0x45, 0x53, 0x10, 0x7f, 0x2a,
	0x6f, 0x0a, 0x11, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4d, 0x61, 0x73, 0x6b, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x55, 0x44, 0x49, 0x54, 0x5f, 0x4c, 0x4f,
	0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x41, 0x52, 0x41,
	0x4d, 0x45, 0x54, 0x45, 0x52, 0x53, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x10, 0x04,
	0x42, 0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x78, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x5a, 0x27, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x61, 0x75, 0x64, 0x69, 0x74,
	0x5f, 0x6c, 0x6f, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_audit_log_audit_log_proto_rawDescOnce sync.Once
	file_api_cx_audit_log_audit_log_proto_rawDescData = file_api_cx_audit_log_audit_log_proto_rawDesc
)

func file_api_cx_audit_log_audit_log_proto_rawDescGZIP() []byte {
	file_api_cx_audit_log_audit_log_proto_rawDescOnce.Do(func() {
		file_api_cx_audit_log_audit_log_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_audit_log_audit_log_proto_rawDescData)
	})
	return file_api_cx_audit_log_audit_log_proto_rawDescData
}

var file_api_cx_audit_log_audit_log_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_cx_audit_log_audit_log_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_cx_audit_log_audit_log_proto_goTypes = []interface{}{
	(AccessStatus)(0),             // 0: cx.audit_log.AccessStatus
	(Action)(0),                   // 1: cx.audit_log.Action
	(Object)(0),                   // 2: cx.audit_log.Object
	(AuditLogFieldMask)(0),        // 3: cx.audit_log.AuditLogFieldMask
	(*AuditLog)(nil),              // 4: cx.audit_log.AuditLog
	(*AuditLogsFilter)(nil),       // 5: cx.audit_log.AuditLogsFilter
	(casbin.AccessLevel)(0),       // 6: casbin.AccessLevel
	(*timestamppb.Timestamp)(nil), // 7: google.protobuf.Timestamp
}
var file_api_cx_audit_log_audit_log_proto_depIdxs = []int32{
	6, // 0: cx.audit_log.AuditLog.access_level:type_name -> casbin.AccessLevel
	0, // 1: cx.audit_log.AuditLog.access_status:type_name -> cx.audit_log.AccessStatus
	1, // 2: cx.audit_log.AuditLog.action:type_name -> cx.audit_log.Action
	2, // 3: cx.audit_log.AuditLog.object:type_name -> cx.audit_log.Object
	7, // 4: cx.audit_log.AuditLog.accessed_at:type_name -> google.protobuf.Timestamp
	2, // 5: cx.audit_log.AuditLogsFilter.object:type_name -> cx.audit_log.Object
	1, // 6: cx.audit_log.AuditLogsFilter.action:type_name -> cx.audit_log.Action
	0, // 7: cx.audit_log.AuditLogsFilter.access_status:type_name -> cx.audit_log.AccessStatus
	7, // 8: cx.audit_log.AuditLogsFilter.updated_at:type_name -> google.protobuf.Timestamp
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_api_cx_audit_log_audit_log_proto_init() }
func file_api_cx_audit_log_audit_log_proto_init() {
	if File_api_cx_audit_log_audit_log_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_cx_audit_log_audit_log_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuditLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_audit_log_audit_log_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuditLogsFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_audit_log_audit_log_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_audit_log_audit_log_proto_goTypes,
		DependencyIndexes: file_api_cx_audit_log_audit_log_proto_depIdxs,
		EnumInfos:         file_api_cx_audit_log_audit_log_proto_enumTypes,
		MessageInfos:      file_api_cx_audit_log_audit_log_proto_msgTypes,
	}.Build()
	File_api_cx_audit_log_audit_log_proto = out.File
	file_api_cx_audit_log_audit_log_proto_rawDesc = nil
	file_api_cx_audit_log_audit_log_proto_goTypes = nil
	file_api_cx_audit_log_audit_log_proto_depIdxs = nil
}
