//go:generate gen_sql -types=UserLead,PersonalDetails,AdditionalDetails

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/leads/user_lead.proto

package leads

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserLead struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId           string                 `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ClientRequestId   string                 `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	ProductType       ProductType            `protobuf:"varint,4,opt,name=product_type,json=productType,proto3,enum=leads.ProductType" json:"product_type,omitempty"`
	ClientId          string                 `protobuf:"bytes,5,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Pan               string                 `protobuf:"bytes,6,opt,name=pan,proto3" json:"pan,omitempty"`
	MobileNumber      string                 `protobuf:"bytes,7,opt,name=mobile_number,json=mobileNumber,proto3" json:"mobile_number,omitempty"`
	Email             string                 `protobuf:"bytes,8,opt,name=email,proto3" json:"email,omitempty"`
	PersonalDetails   *PersonalDetails       `protobuf:"bytes,9,opt,name=personal_details,json=personalDetails,proto3" json:"personal_details,omitempty"`
	AdditionalDetails *AdditionalDetails     `protobuf:"bytes,10,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
	LeadStatus        UserLeadStatus         `protobuf:"varint,11,opt,name=lead_status,json=leadStatus,proto3,enum=leads.UserLeadStatus" json:"lead_status,omitempty"`
	CompletedAt       *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	ExpiredAt         *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *UserLead) Reset() {
	*x = UserLead{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_user_lead_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserLead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserLead) ProtoMessage() {}

func (x *UserLead) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_user_lead_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserLead.ProtoReflect.Descriptor instead.
func (*UserLead) Descriptor() ([]byte, []int) {
	return file_api_leads_user_lead_proto_rawDescGZIP(), []int{0}
}

func (x *UserLead) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UserLead) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UserLead) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *UserLead) GetProductType() ProductType {
	if x != nil {
		return x.ProductType
	}
	return ProductType_PRODUCT_TYPE_UNSPECIFIED
}

func (x *UserLead) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *UserLead) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *UserLead) GetMobileNumber() string {
	if x != nil {
		return x.MobileNumber
	}
	return ""
}

func (x *UserLead) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserLead) GetPersonalDetails() *PersonalDetails {
	if x != nil {
		return x.PersonalDetails
	}
	return nil
}

func (x *UserLead) GetAdditionalDetails() *AdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

func (x *UserLead) GetLeadStatus() UserLeadStatus {
	if x != nil {
		return x.LeadStatus
	}
	return UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED
}

func (x *UserLead) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *UserLead) GetExpiredAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiredAt
	}
	return nil
}

func (x *UserLead) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *UserLead) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// PersonalDetails contains personal details of the user
type PersonalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentAddress    *typesv2.PostalAddress `protobuf:"bytes,1,opt,name=current_address,json=currentAddress,proto3" json:"current_address,omitempty"`
	Name              *common.Name           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	EmploymentDetails *EmploymentDetails     `protobuf:"bytes,3,opt,name=employment_details,json=employmentDetails,proto3" json:"employment_details,omitempty"`
	Dob               *date.Date             `protobuf:"bytes,4,opt,name=dob,proto3" json:"dob,omitempty"`
}

func (x *PersonalDetails) Reset() {
	*x = PersonalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_user_lead_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PersonalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonalDetails) ProtoMessage() {}

func (x *PersonalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_user_lead_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonalDetails.ProtoReflect.Descriptor instead.
func (*PersonalDetails) Descriptor() ([]byte, []int) {
	return file_api_leads_user_lead_proto_rawDescGZIP(), []int{1}
}

func (x *PersonalDetails) GetCurrentAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.CurrentAddress
	}
	return nil
}

func (x *PersonalDetails) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *PersonalDetails) GetEmploymentDetails() *EmploymentDetails {
	if x != nil {
		return x.EmploymentDetails
	}
	return nil
}

func (x *PersonalDetails) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

// AdditionalDetails contains product specific details
// Whenever we are adding a new product, we can add
// their specific details in this oneof
type AdditionalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Details:
	//
	//	*AdditionalDetails_FiPersonalLoanDetails
	Details       isAdditionalDetails_Details `protobuf_oneof:"details"`
	UtmParameters *UtmParameters              `protobuf:"bytes,2,opt,name=utm_parameters,json=utmParameters,proto3" json:"utm_parameters,omitempty"`
}

func (x *AdditionalDetails) Reset() {
	*x = AdditionalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_user_lead_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalDetails) ProtoMessage() {}

func (x *AdditionalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_user_lead_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalDetails.ProtoReflect.Descriptor instead.
func (*AdditionalDetails) Descriptor() ([]byte, []int) {
	return file_api_leads_user_lead_proto_rawDescGZIP(), []int{2}
}

func (m *AdditionalDetails) GetDetails() isAdditionalDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *AdditionalDetails) GetFiPersonalLoanDetails() *FiPersonalLoanDetails {
	if x, ok := x.GetDetails().(*AdditionalDetails_FiPersonalLoanDetails); ok {
		return x.FiPersonalLoanDetails
	}
	return nil
}

func (x *AdditionalDetails) GetUtmParameters() *UtmParameters {
	if x != nil {
		return x.UtmParameters
	}
	return nil
}

type isAdditionalDetails_Details interface {
	isAdditionalDetails_Details()
}

type AdditionalDetails_FiPersonalLoanDetails struct {
	FiPersonalLoanDetails *FiPersonalLoanDetails `protobuf:"bytes,1,opt,name=fi_personal_loan_details,json=fiPersonalLoanDetails,proto3,oneof"`
}

func (*AdditionalDetails_FiPersonalLoanDetails) isAdditionalDetails_Details() {}

type FiPersonalLoanDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanRequirement *LoanRequirement `protobuf:"bytes,1,opt,name=loan_requirement,json=loanRequirement,proto3" json:"loan_requirement,omitempty"`
	// type of evaluation performed for the lead before creating it in the system
	EvaluationType FiLoansEvaluationType `protobuf:"varint,2,opt,name=evaluation_type,json=evaluationType,proto3,enum=leads.FiLoansEvaluationType" json:"evaluation_type,omitempty"`
}

func (x *FiPersonalLoanDetails) Reset() {
	*x = FiPersonalLoanDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_user_lead_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiPersonalLoanDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiPersonalLoanDetails) ProtoMessage() {}

func (x *FiPersonalLoanDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_user_lead_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiPersonalLoanDetails.ProtoReflect.Descriptor instead.
func (*FiPersonalLoanDetails) Descriptor() ([]byte, []int) {
	return file_api_leads_user_lead_proto_rawDescGZIP(), []int{3}
}

func (x *FiPersonalLoanDetails) GetLoanRequirement() *LoanRequirement {
	if x != nil {
		return x.LoanRequirement
	}
	return nil
}

func (x *FiPersonalLoanDetails) GetEvaluationType() FiLoansEvaluationType {
	if x != nil {
		return x.EvaluationType
	}
	return FiLoansEvaluationType_FI_LOANS_EVALUATION_TYPE_UNSPECIFIED
}

// UtmParameters contains tracking parameters for marketing attribution
type UtmParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UtmContent string `protobuf:"bytes,1,opt,name=utm_content,json=utmContent,proto3" json:"utm_content,omitempty"`
}

func (x *UtmParameters) Reset() {
	*x = UtmParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_user_lead_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UtmParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UtmParameters) ProtoMessage() {}

func (x *UtmParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_user_lead_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UtmParameters.ProtoReflect.Descriptor instead.
func (*UtmParameters) Descriptor() ([]byte, []int) {
	return file_api_leads_user_lead_proto_rawDescGZIP(), []int{4}
}

func (x *UtmParameters) GetUtmContent() string {
	if x != nil {
		return x.UtmContent
	}
	return ""
}

type EmploymentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmploymentType typesv2.EmploymentType `protobuf:"varint,1,opt,name=employment_type,json=employmentType,proto3,enum=api.typesv2.EmploymentType" json:"employment_type,omitempty"`
	MonthlyIncome  *money.Money           `protobuf:"bytes,2,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
}

func (x *EmploymentDetails) Reset() {
	*x = EmploymentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_user_lead_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmploymentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmploymentDetails) ProtoMessage() {}

func (x *EmploymentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_user_lead_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmploymentDetails.ProtoReflect.Descriptor instead.
func (*EmploymentDetails) Descriptor() ([]byte, []int) {
	return file_api_leads_user_lead_proto_rawDescGZIP(), []int{5}
}

func (x *EmploymentDetails) GetEmploymentType() typesv2.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return typesv2.EmploymentType(0)
}

func (x *EmploymentDetails) GetMonthlyIncome() *money.Money {
	if x != nil {
		return x.MonthlyIncome
	}
	return nil
}

type LoanRequirement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DesiredLoanAmount *money.Money `protobuf:"bytes,1,opt,name=desired_loan_amount,json=desiredLoanAmount,proto3" json:"desired_loan_amount,omitempty"`
}

func (x *LoanRequirement) Reset() {
	*x = LoanRequirement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_user_lead_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanRequirement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanRequirement) ProtoMessage() {}

func (x *LoanRequirement) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_user_lead_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanRequirement.ProtoReflect.Descriptor instead.
func (*LoanRequirement) Descriptor() ([]byte, []int) {
	return file_api_leads_user_lead_proto_rawDescGZIP(), []int{6}
}

func (x *LoanRequirement) GetDesiredLoanAmount() *money.Money {
	if x != nil {
		return x.DesiredLoanAmount
	}
	return nil
}

type FiLoansLeadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of available lender types for the user to apply on Fi app
	// populated only if evaluation type in request is BASIC and lead is accepted
	AvailableLenderTypes []LenderType `protobuf:"varint,1,rep,packed,name=available_lender_types,json=availableLenderTypes,proto3,enum=leads.LenderType" json:"available_lender_types,omitempty"`
}

func (x *FiLoansLeadResponse) Reset() {
	*x = FiLoansLeadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_user_lead_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiLoansLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiLoansLeadResponse) ProtoMessage() {}

func (x *FiLoansLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_user_lead_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiLoansLeadResponse.ProtoReflect.Descriptor instead.
func (*FiLoansLeadResponse) Descriptor() ([]byte, []int) {
	return file_api_leads_user_lead_proto_rawDescGZIP(), []int{7}
}

func (x *FiLoansLeadResponse) GetAvailableLenderTypes() []LenderType {
	if x != nil {
		return x.AvailableLenderTypes
	}
	return nil
}

var File_api_leads_user_lead_proto protoreflect.FileDescriptor

var file_api_leads_user_lead_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x6c, 0x65, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x1a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e,
	0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e,
	0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb6, 0x05, 0x0a, 0x08, 0x55, 0x73, 0x65,
	0x72, 0x4c, 0x65, 0x61, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x0c,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x12, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70,
	0x61, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x41, 0x0a,
	0x10, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e,
	0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x0f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x47, 0x0a, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x36, 0x0a, 0x0b, 0x6c, 0x65, 0x61,
	0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x65, 0x61, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x6c, 0x65, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x22, 0xf2, 0x01, 0x0a, 0x0f, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x43, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73,
	0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x47, 0x0a, 0x12, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x45, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x22, 0xb4, 0x01, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x57, 0x0a, 0x18,
	0x66, 0x69, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x61, 0x6e,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x46, 0x69, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61,
	0x6c, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x15,
	0x66, 0x69, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3b, 0x0a, 0x0e, 0x75, 0x74, 0x6d, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x55, 0x74, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x52, 0x0d, 0x75, 0x74, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xa1, 0x01,
	0x0a, 0x15, 0x46, 0x69, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x61, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x41, 0x0a, 0x10, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x6c, 0x6f, 0x61, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x45, 0x0a, 0x0f, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x46, 0x69, 0x4c, 0x6f,
	0x61, 0x6e, 0x73, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x30, 0x0a, 0x0d, 0x55, 0x74, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x74, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x74, 0x6d, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x22, 0x94, 0x01, 0x0a, 0x11, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x44, 0x0a, 0x0f, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x39, 0x0a, 0x0e, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6d, 0x6f, 0x6e,
	0x74, 0x68, 0x6c, 0x79, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x22, 0x55, 0x0a, 0x0f, 0x4c, 0x6f,
	0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x42, 0x0a,
	0x13, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11,
	0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x5e, 0x0a, 0x13, 0x46, 0x69, 0x4c, 0x6f, 0x61, 0x6e, 0x73, 0x4c, 0x65, 0x61, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x16, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2e, 0x4c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x14, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x42, 0x44, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x5a, 0x20, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_leads_user_lead_proto_rawDescOnce sync.Once
	file_api_leads_user_lead_proto_rawDescData = file_api_leads_user_lead_proto_rawDesc
)

func file_api_leads_user_lead_proto_rawDescGZIP() []byte {
	file_api_leads_user_lead_proto_rawDescOnce.Do(func() {
		file_api_leads_user_lead_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_leads_user_lead_proto_rawDescData)
	})
	return file_api_leads_user_lead_proto_rawDescData
}

var file_api_leads_user_lead_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_leads_user_lead_proto_goTypes = []interface{}{
	(*UserLead)(nil),              // 0: leads.UserLead
	(*PersonalDetails)(nil),       // 1: leads.PersonalDetails
	(*AdditionalDetails)(nil),     // 2: leads.AdditionalDetails
	(*FiPersonalLoanDetails)(nil), // 3: leads.FiPersonalLoanDetails
	(*UtmParameters)(nil),         // 4: leads.UtmParameters
	(*EmploymentDetails)(nil),     // 5: leads.EmploymentDetails
	(*LoanRequirement)(nil),       // 6: leads.LoanRequirement
	(*FiLoansLeadResponse)(nil),   // 7: leads.FiLoansLeadResponse
	(ProductType)(0),              // 8: leads.ProductType
	(UserLeadStatus)(0),           // 9: leads.UserLeadStatus
	(*timestamppb.Timestamp)(nil), // 10: google.protobuf.Timestamp
	(*typesv2.PostalAddress)(nil), // 11: api.typesv2.PostalAddress
	(*common.Name)(nil),           // 12: api.typesv2.common.Name
	(*date.Date)(nil),             // 13: google.type.Date
	(FiLoansEvaluationType)(0),    // 14: leads.FiLoansEvaluationType
	(typesv2.EmploymentType)(0),   // 15: api.typesv2.EmploymentType
	(*money.Money)(nil),           // 16: google.type.Money
	(LenderType)(0),               // 17: leads.LenderType
}
var file_api_leads_user_lead_proto_depIdxs = []int32{
	8,  // 0: leads.UserLead.product_type:type_name -> leads.ProductType
	1,  // 1: leads.UserLead.personal_details:type_name -> leads.PersonalDetails
	2,  // 2: leads.UserLead.additional_details:type_name -> leads.AdditionalDetails
	9,  // 3: leads.UserLead.lead_status:type_name -> leads.UserLeadStatus
	10, // 4: leads.UserLead.completed_at:type_name -> google.protobuf.Timestamp
	10, // 5: leads.UserLead.expired_at:type_name -> google.protobuf.Timestamp
	10, // 6: leads.UserLead.created_at:type_name -> google.protobuf.Timestamp
	10, // 7: leads.UserLead.updated_at:type_name -> google.protobuf.Timestamp
	11, // 8: leads.PersonalDetails.current_address:type_name -> api.typesv2.PostalAddress
	12, // 9: leads.PersonalDetails.name:type_name -> api.typesv2.common.Name
	5,  // 10: leads.PersonalDetails.employment_details:type_name -> leads.EmploymentDetails
	13, // 11: leads.PersonalDetails.dob:type_name -> google.type.Date
	3,  // 12: leads.AdditionalDetails.fi_personal_loan_details:type_name -> leads.FiPersonalLoanDetails
	4,  // 13: leads.AdditionalDetails.utm_parameters:type_name -> leads.UtmParameters
	6,  // 14: leads.FiPersonalLoanDetails.loan_requirement:type_name -> leads.LoanRequirement
	14, // 15: leads.FiPersonalLoanDetails.evaluation_type:type_name -> leads.FiLoansEvaluationType
	15, // 16: leads.EmploymentDetails.employment_type:type_name -> api.typesv2.EmploymentType
	16, // 17: leads.EmploymentDetails.monthly_income:type_name -> google.type.Money
	16, // 18: leads.LoanRequirement.desired_loan_amount:type_name -> google.type.Money
	17, // 19: leads.FiLoansLeadResponse.available_lender_types:type_name -> leads.LenderType
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_api_leads_user_lead_proto_init() }
func file_api_leads_user_lead_proto_init() {
	if File_api_leads_user_lead_proto != nil {
		return
	}
	file_api_leads_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_leads_user_lead_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserLead); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_leads_user_lead_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PersonalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_leads_user_lead_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdditionalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_leads_user_lead_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiPersonalLoanDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_leads_user_lead_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UtmParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_leads_user_lead_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmploymentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_leads_user_lead_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanRequirement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_leads_user_lead_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiLoansLeadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_leads_user_lead_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*AdditionalDetails_FiPersonalLoanDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_leads_user_lead_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_leads_user_lead_proto_goTypes,
		DependencyIndexes: file_api_leads_user_lead_proto_depIdxs,
		MessageInfos:      file_api_leads_user_lead_proto_msgTypes,
	}.Build()
	File_api_leads_user_lead_proto = out.File
	file_api_leads_user_lead_proto_rawDesc = nil
	file_api_leads_user_lead_proto_goTypes = nil
	file_api_leads_user_lead_proto_depIdxs = nil
}
