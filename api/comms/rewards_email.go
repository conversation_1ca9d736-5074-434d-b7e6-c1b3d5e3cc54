//nolint:dupl,gocritic
package comms

import (
	"fmt"
	"strings"
)

func (o *EmailOption_TieringRewardsSummaryEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.TieringRewardsSummaryEmailOption.GetEmailType()
}

func (o *EmailOption_TieringRewardsSummaryEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("rewards summary email option is nil")
	}
	switch o.TieringRewardsSummaryEmailOption.GetOption().(type) {
	case *TieringRewardsSummaryEmailOption_TieringRewardsSummaryOptionV1:
		option := o.TieringRewardsSummaryEmailOption.GetTieringRewardsSummaryOptionV1()
		// replace the placeholders in the template with the actual values
		// header details
		msg := strings.Replace(templateBody, "{#headerDetails.title#}", option.GetHeaderDetails().GetTitle(), 1)
		msg = strings.Replace(msg, "{#headerDetails.date#}", option.GetHeaderDetails().GetDate(), 1)

		// body details
		// progress ring details
		msg = strings.Replace(msg, "{#bodyDetails.progressRingDetails.title.text#}", option.GetBodyDetails().GetProgressRingDetails().GetTitle().GetText(), 1)
		msg = strings.Replace(msg, "{#bodyDetails.progressRingDetails.title.iconUrlIdentifier#}", option.GetBodyDetails().GetProgressRingDetails().GetTitle().GetIconUrlIdentifier(), 1)
		msg = strings.Replace(msg, "{#bodyDetails.progressRingDetails.progress#}", option.GetBodyDetails().GetProgressRingDetails().GetProgress(), 1)
		msg = strings.Replace(msg, "{#bodyDetails.progressRingDetails.ringColour#}", option.GetBodyDetails().GetProgressRingDetails().GetRingColour(), 1)

		// title
		msg = strings.Replace(msg, "{#bodyDetails.title.text#}", option.GetBodyDetails().GetTitle().GetText(), 1)
		// reward details
		// fi coins
		msg = strings.Replace(msg, "{#bodyDetails.rewardDetails.fiCoins.rewardType.text#}", option.GetBodyDetails().GetRewardDetails().GetFiCoins().GetRewardType().GetText(), 1)
		msg = strings.Replace(msg, "{#bodyDetails.rewardDetails.fiCoins.rewardType.iconUrlIdentifier#}", option.GetBodyDetails().GetRewardDetails().GetFiCoins().GetRewardType().GetIconUrlIdentifier(), 1)
		msg = strings.Replace(msg, "{#bodyDetails.rewardDetails.fiCoins.rewardAmount.text#}", option.GetBodyDetails().GetRewardDetails().GetFiCoins().GetRewardAmount().GetText(), 1)
		msg = strings.Replace(msg, "{#bodyDetails.rewardDetails.fiCoins.rewardAmount.iconUrlIdentifier#}", option.GetBodyDetails().GetRewardDetails().GetFiCoins().GetRewardAmount().GetIconUrlIdentifier(), 1)
		msg = strings.Replace(msg, "{#bodyDetails.rewardDetails.fiCoins.rewardCap.text#}", option.GetBodyDetails().GetRewardDetails().GetFiCoins().GetRewardCap().GetText(), 1)
		msg = strings.Replace(msg, "{#bodyDetails.rewardDetails.fiCoins.displayType#}", option.GetBodyDetails().GetRewardDetails().GetFiCoins().GetDisplayType(), 2)
		// cash
		msg = strings.Replace(msg, "{#bodyDetails.rewardDetails.cash.rewardType.text#}", option.GetBodyDetails().GetRewardDetails().GetCash().GetRewardType().GetText(), 1)
		msg = strings.Replace(msg, "{#bodyDetails.rewardDetails.cash.rewardType.iconUrlIdentifier#}", option.GetBodyDetails().GetRewardDetails().GetCash().GetRewardType().GetIconUrlIdentifier(), 1)
		msg = strings.Replace(msg, "{#bodyDetails.rewardDetails.cash.rewardAmount.text#}", option.GetBodyDetails().GetRewardDetails().GetCash().GetRewardAmount().GetText(), 1)
		msg = strings.Replace(msg, "{#bodyDetails.rewardDetails.cash.rewardAmount.iconUrlIdentifier#}", option.GetBodyDetails().GetRewardDetails().GetCash().GetRewardAmount().GetIconUrlIdentifier(), 1)
		msg = strings.Replace(msg, "{#bodyDetails.rewardDetails.cash.rewardCap.text#}", option.GetBodyDetails().GetRewardDetails().GetCash().GetRewardCap().GetText(), 1)
		msg = strings.Replace(msg, "{#bodyDetails.rewardDetails.cash.displayType#}", option.GetBodyDetails().GetRewardDetails().GetCash().GetDisplayType(), 2)
		// descriptions
		var finalDescriptionsAsComponents string
		for _, description := range option.GetBodyDetails().GetDescriptions() {
			finalDescriptionsAsComponents += fmt.Sprintf(`<tr>
                                    <td style="line-height: 0; vertical-align: top">
                                        <img src="%s" width="24" height="24"
											 style="margin: 0 10px 0 0;">
                                    </td>
                                    <td>
                                        <div style="font-family: Gilroy, sans-serif;">%s</div>
                                    </td>
                              </tr>`, description.GetIconUrlIdentifier(), description.GetText())
		}
		msg = strings.Replace(msg, "{#bodyDetails.descriptions#}", finalDescriptionsAsComponents, 1)
		// cta text
		msg = strings.Replace(msg, "{#bodyDetails.ctaText#}", option.GetBodyDetails().GetCtaText(), 2)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for rewards summary email template")
}

func (o *EmailOption_TieringRewardsSummaryEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.TieringRewardsSummaryEmailOption.GetOption().(type) {
	case *TieringRewardsSummaryEmailOption_TieringRewardsSummaryOptionV1:
		return o.TieringRewardsSummaryEmailOption.GetTieringRewardsSummaryOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *EmailOption_TieringRewardsSummaryEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("rewards summary email option is nil")
	}
	switch o.TieringRewardsSummaryEmailOption.GetOption().(type) {
	case *TieringRewardsSummaryEmailOption_TieringRewardsSummaryOptionV1:
		date := o.TieringRewardsSummaryEmailOption.GetTieringRewardsSummaryOptionV1().GetHeaderDetails().GetDate()
		// replace the placeholders in the subject with the actual values
		sub := strings.Replace(subject, "{#date#}", date, 1)
		return sub, nil
	}
	return "", fmt.Errorf("no valid version found for rewards summary email template")
}
