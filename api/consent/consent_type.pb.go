//go:generate gen_sql -types=ConsentProvenance

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/consent/consent_type.proto

package consent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents the type of consent that an actor can provide
// To be in sync with api/frontend/consent/consent_type.proto
type ConsentType int32

const (
	ConsentType_ConsentType_UNSPECIFIED ConsentType = 0
	ConsentType_TnC                     ConsentType = 1
	ConsentType_cKYC                    ConsentType = 2
	ConsentType_eKYC                    ConsentType = 3
	ConsentType_Waitlist                ConsentType = 4
	ConsentType_UPI                     ConsentType = 5
	ConsentType_FI_TNC                  ConsentType = 6
	ConsentType_FED_TNC                 ConsentType = 7
	ConsentType_FI_PRIVACY_POLICY       ConsentType = 8
	ConsentType_FI_WEALTH_TNC           ConsentType = 9
	ConsentType_CREDIT_REPORT_TNC       ConsentType = 10
	ConsentType_HIGH_RISK_DEVICE        ConsentType = 11
	// consent to share some of the user's data with p2p vendor
	ConsentType_FI_P2P_INVESTMENT ConsentType = 12
	// consent to share some of the user's data with preapprovedloan vendor
	ConsentType_FI_PRE_APPROVED_LOAN ConsentType = 13
	// consent to migrate the old vpa to new tpap vpa
	ConsentType_VPA_MIGRATION ConsentType = 14
	// consent that user has gone through the secure usage guidelines
	ConsentType_SECURE_USAGE_GUIDELINES   ConsentType = 15
	ConsentType_CREDIT_REPORT_UPDATED_TNC ConsentType = 16
	// user have given consent for discrepancy in income and occupation
	ConsentType_INCOME_OCCUPATION_DISCREPANCY ConsentType = 17
	// consent for importing user's MF holdings with the vendor
	ConsentType_FI_WEALTH_MF_HOLDINGS_IMPORT_MF_CENTRAL_TNC ConsentType = 18
	// consent to share some of the user's data with credit card vendor
	ConsentType_FI_CREDIT_CARD ConsentType = 19
	// consent to the credit card emi tnc
	ConsentType_FI_CREDIT_CARD_EMI_TNC ConsentType = 20
	// consent for credit card key fact statements
	ConsentType_FI_CREDIT_CARD_KFS ConsentType = 21
	// consent for fi credit card most important tnc
	ConsentType_FI_CREDIT_CARD_MOST_IMP_TNC ConsentType = 22
	// consent for fi credit card TnC
	ConsentType_FI_CREDIT_CARD_TNC ConsentType = 23
	// consent to use aadhaar number E.g. Use aadhaar number to
	// set / reset upi pin
	ConsentType_AADHAAR_NUMBER_BASED_PIN_SET ConsentType = 24
	// consent to share PAN and DOB of the user with IDFC bank for Pre-approved loans journey
	ConsentType_PL_IDFC_PAN_DOB    ConsentType = 25
	ConsentType_FED_STORAGE_POLICY ConsentType = 26
	ConsentType_INITIATE_BKYC      ConsentType = 27
	// consent to confirm bkyc record, downloaded by agent from UIDAI
	ConsentType_CONFIRM_BKYC_RECORD ConsentType = 28
	// This consent includes tncs of fi wealth and mf central. It also includes the sharing of data from tech to wealth.
	ConsentType_FI_WEALTH_MF_HOLDINGS_IMPORT_MF_CENTRAL_TNC_V2 ConsentType = 29
	// This consent will tell users that their data for mf holdings will be available to them for limited time.
	// Mainly this consent will be for fi-lite users.
	ConsentType_MF_HOLDINGS_DATA_AVAILABILITY_FOR_LIMITED_TIME ConsentType = 30
	// Consent to open a fixed deposit for the purpose of onboarding on a secured credit card program
	ConsentType_SECURED_CREDIT_CARD_OPEN_FIXED_DEPOSIT ConsentType = 31
	// consent of user which enables fi to show credit report to user for a limited time
	ConsentType_CREDIT_REPORT_LIMITED_TIME_TNC ConsentType = 32
	// consent of user to delete fi account details when savings account is already closed
	ConsentType_FI_CLOSED_SAVINGS_ACCOUNT_DELETION ConsentType = 33
	// consent of the user to store the PAN and DOB in personal loans flow with Liquiloans
	ConsentType_PL_LL_PAN_DOB ConsentType = 34
	// user's consent to allow Epifi Tech fetching their EPF info from EPFO for the purpose of calculating your net worth
	ConsentType_EPF_TNC ConsentType = 35
	// This consent will tell users that their personal data will be used by Epifi and its partners for providing services
	ConsentType_EPIFI_INSIGHTS_TNC ConsentType = 36
	// This consent will tell users that their credit report data will be pulled from the vendor (eg. Experian)
	ConsentType_CREDIT_REPORT_DATA_PULL ConsentType = 37
	// user consent to allow idfc to auto-deduct the emi amount every month from user's account
	ConsentType_PL_IDFC_MANDATE ConsentType = 38
	// This consent will tell users that their credit report data will be pulled by Liquiloans vendor
	ConsentType_PL_LL_CREDIT_REPORT_DATA_PULL  ConsentType = 39
	ConsentType_CONFIRM_PROFILE_UPDATE_AT_BANK ConsentType = 40
	ConsentType_REJECT_PROFILE_UPDATE_AT_BANK  ConsentType = 41
	// consent to use device biometric unlock method
	ConsentType_DEVICE_UNLOCK_BIOMETRIC ConsentType = 42
	// consent of user which enables fi to show connected accounts data to user for a limited time
	ConsentType_CONNECTED_ACCOUNTS_LIMITED_TIME_TNC ConsentType = 43
	// consent allows epifi wealth to import and use financial data via account aggregator Finvu
	ConsentType_CONNECTED_ACCOUNTS_FI_WEALTH_AND_FINVU_TNC ConsentType = 44
	// consent allows epifi wealth to import and use financial data via account aggregator OneMoney
	ConsentType_CONNECTED_ACCOUNTS_FI_WEALTH_AND_ONEMONEY_TNC ConsentType = 45
	// BKYC_CUSTOMER_DUE_DILIGENCE consent is taken when the agent checks the details of the customer whose KYC is carried out with the bank.
	ConsentType_BKYC_CUSTOMER_DUE_DILIGENCE ConsentType = 46
	// consent is taken when user submits the savings account closure request
	ConsentType_SA_CLOSURE_REQUEST ConsentType = 47
	// this consent allows fi and its partners to process and use user input manual asset data for providing services to user
	ConsentType_MANUAL_ASSET_FORM_TNC ConsentType = 48
	// consent taken in screener itr intimation stage
	ConsentType_ITR_INTIMATION ConsentType = 49
	// consent taken from user for epifi wealth data sharing to epifi tech
	// use-case: we are taking consent from user to share connected accounts data to epifi tech for loan eligibility
	ConsentType_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP ConsentType = 50
	// This consent will inform users that their data (credit bureau report, kyc info etc) can be pulled by Moneyview lender.
	ConsentType_DATA_PULL_BY_MONEYVIEW_LENDER           ConsentType = 51
	ConsentType_CIBIL_REPORT_TNC                        ConsentType = 52
	ConsentType_UNSECURED_CREDIT_CARD_MOST_IMP_TNC      ConsentType = 53
	ConsentType_UNSECURED_CREDIT_CARD_KFS               ConsentType = 54
	ConsentType_UNSECURED_CREDIT_CARD_FEES              ConsentType = 55
	ConsentType_SECURED_CREDIT_CARD_MOST_IMP_TNC        ConsentType = 56
	ConsentType_SECURED_CREDIT_CARD_KFS                 ConsentType = 57
	ConsentType_MASS_UNSECURED_CREDIT_CARD_MOST_IMP_TNC ConsentType = 58
	ConsentType_MASS_UNSECURED_CREDIT_CARD_KFS          ConsentType = 59
	ConsentType_CREDIT_REPORT_CHECK                     ConsentType = 60
	ConsentType_OPEN_FIXED_DEPOSIT                      ConsentType = 61
	// consent taken from user for epifi wealth data sharing to epifi tech
	// use-case: we are taking consent from user to share connected accounts data to epifi tech for screener
	ConsentType_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_SCREENER_GROUP ConsentType = 62
	// This consent will inform users that their employment data can be pulled by ABFL lender.
	ConsentType_LOANS_ABFL_EMPLOYMENT_DATA ConsentType = 63
	// This consent will inform users that their itr data can be used by lenders.
	ConsentType_ITR_INTIMATION_PROCESSING_AND_SHARING_WITH_LENDERS ConsentType = 64
	// For user to agree to the risk profile decided by Epifi Wealth as an investment advisory
	ConsentType_EPIFI_WEALTH_INVESTMENT_RISK_PROFILE ConsentType = 65
	// Consent to enable UPI / TPAP for the accounts connected via AA
	ConsentType_LINK_CONNECTED_ACCOUNTS_VIA_UPI ConsentType = 66
	// NRI Account onboarding consents
	// https://fi.money/tnc/nr
	ConsentType_CONSENT_FI_NRE_NRO_ACCOUNTS ConsentType = 70
	// I confirm that I’m not resident in India
	ConsentType_CONSENT_NON_INDIAN_RESIDENCY ConsentType = 67
	// I’m not (nor am related to) a politically exposed person.
	ConsentType_CONSENT_NOT_POLITICALLY_EXPOSED ConsentType = 68
	// https://fi.money/tnc/fatca
	// "I declare that I am neither a citizen of USA nor a tax resident of any country other than India even though
	// my residence/mailing address/telephone number is of a country other than India. I have understood the information
	// requirements of the FATCA/CRS Form (read along with the FATCA/CRS Instructions) and hereby confirm that all
	// information provided by me is true, correct, and complete."
	ConsentType_CONSENT_FATCA_CRS ConsentType = 76
	// "I acknowledge that the services provided here are currently part of a regulatory sandbox test &
	// consent to participate in the testing phase."
	ConsentType_CONSENT_NON_RESIDENT_ACCOUNT_TESTING ConsentType = 69
	// "I also want to open a NRO account Learn More"
	// If user has given the consent then we can trigger the nro account creation after nre account creation
	// https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=1643-12243&t=kJsS7G6jzYrRzpVg-0
	ConsentType_CONSENT_NRO_ACCOUNT_CREATION ConsentType = 75
	// "I confirm this is my current communication address"
	// This consent is taken when user confirms the communication address
	ConsentType_CONSENT_CONFIRM_COMMUNICATION_ADDRESS ConsentType = 71
	// Take user's consent to use their AA data for lending.
	ConsentType_CONSENT_USE_AA_DATA_FOR_LENDING ConsentType = 72
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=50454-25879&t=JHvB4WZY6LQsc5vI-4
	// Consent taken to indicate that the user has agreed to the revised offer terms
	ConsentType_LENDING_CONFIRM_REVISED_OFFER_TERMS ConsentType = 73
	// "I consent Federal Bank to fetch my financial data using the licensed Account Aggregator One Money for the purpose of loan underwriting."
	ConsentType_CONSENT_FEDERAL_AA_DATA_SHARE ConsentType = 74
	// I'm an Indian citizen; India is my country of tax residence
	ConsentType_CONSENT_INDIAN_RESIDENCY ConsentType = 77
	// Consent taken to indicate Stock Guardian can download the CKYC data for the user
	ConsentType_CONSENT_STOCK_GUARDIAN_TSP_CKYC_DOWNLOAD ConsentType = 78
	// Consent taken to indicate Stock Guardian can do a hard pull of the user's credit report
	ConsentType_CONSENT_STOCK_GUARDIAN_TSP_TYPE_CREDIT_REPORT_HARD_PULL ConsentType = 79
	// Consent taken to indicate Stock Guardian can use the user's data for promotions
	ConsentType_CONSENT_STOCK_GUARDIAN_TSP_DATA_USAGE_FOR_PROMOTIONS ConsentType = 80
	// Consent to record user has aggred to the terms and conditions of the SMS fetcher POC
	//
	// Deprecated: Marked as deprecated in api/consent/consent_type.proto.
	ConsentType_CONSENT_SMS_FETCHER_POC_WEALTH_TNC ConsentType = 81
	// consent to sg ckyc record fetch
	ConsentType_CONSENT_LOANS_SG_CKYC_RECORD_FETCH ConsentType = 82
	// consent to sg credit bureau fetch and info
	ConsentType_CONSENT_LOANS_SG_CREDIT_BUREAU_INFO ConsentType = 83
	// consent to sg to data use by associated partners
	ConsentType_CONSENT_LOANS_SG_DATA_USE_BY_ASSOCIATED_PARTNERS ConsentType = 84
	// consent to open a savings account with a minimum balance
	ConsentType_CONSENT_OPEN_MIN_BALANCE_SAVINGS_ACCOUNT ConsentType = 85
	// consent to allow Epifi Wealth to process user's SMS data
	ConsentType_CONSENT_SMS_DATA_PROCESSING_CONSENT ConsentType = 86
	// Consent to record user has agreed to the terms and conditions of the SMS scanner
	// If a user provides their consent, we will scan their sms to provide insights on their finances
	// Note: This is separate from CONSENT_SMS_FETCHER_POC_WEALTH_TNC consent which was used just for POC purposes
	ConsentType_CONSENT_SMS_FETCHER_WEALTH_TNC ConsentType = 87
	// Pre Bre Lenden consent to let lenden(innofin) check bureau and validate pan.
	ConsentType_CONSENT_LENDEN_CREDIT_REPORT_CHECK_AND_PAN_VALIDATION ConsentType = 88
	// Pre Bre Lenden privacy policy.
	ConsentType_CONSENT_PRE_BRE_LENDEN_PRIVACY_POLICY_AND_TNC ConsentType = 89
	// Consent to let Lenden(innofin) share users data with other vendors like loan data,personal details and credit data
	ConsentType_CONSENT_LENDEN_DATA_SHARING ConsentType = 90
	// declaration of annual income by user.
	ConsentType_CONSENT_DECLARATION_OF_ANNUAL_INCOME ConsentType = 91
	// Consent that user has agreed to current e-sign document
	// Consent Text: "I confirm that I have read and understood the Key Fact statement and Loan Agreement."
	ConsentType_CONSENT_LOANS_LDC_KFS_AND_LOAN_AGREEMENT_UNDERSTOOD ConsentType = 92
	// Consent to let user share their location
	// Consent text: "I allow my location to be shared for loan agreement purpose"
	ConsentType_CONSENT_LOANS_LDC_KFS_ALLOW_LOCATION ConsentType = 93
	// Consent that user has understood the e-sign document of the loan
	// Consent Text: "I confirm the information is presented in the language I understand."
	ConsentType_CONSENT_LOANS_LDC_KFS_AND_LOAN_AGREEMENT_LANGUAGE_UNDERSTOOD ConsentType = 94
	// consent to share user data associated with wealth to other epifi groups
	// This is a blanket consent and can be used by anyone i.e. EPIFI_TECH etc
	// This is a v2 config for in-app only use case of EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP
	// The current use case is to use it for share aa and sms data
	// Owned by Wealth entity
	ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2 ConsentType = 95
	// This consent allows Epifi Group to access data of a user present with Epifi Wealth
	// at a point in time via a user specified action.
	// This differs from the CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2 consent,
	// which allows Epifi Group to access user's Epifi Wealth data without requiring any
	// explicit action from them.
	ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP ConsentType = 96
	// This consent will inform users that their data can be shared with ABFL lender.
	ConsentType_CONSENT_TYPE_DATA_PULL_BY_PWA_ABFL_LENDER ConsentType = 97
	// This consent will inform users that their data can be shared with Federal lender.
	ConsentType_CONSENT_TYPE_DATA_SHARING_WITH_FEDERAL ConsentType = 98
	// Consent taken to allow Federal to pull user's credit report
	ConsentType_CONSENT_TYPE_CREDIT_REPORT_PULL_BY_FEDERAL_LENDER ConsentType = 99
	// Consent to allow Innofin Solutions Pvt Ltd (Lenden Club) to debit the EMI amount from the user's bank account as per the provided mandate
	ConsentType_CONSENT_TYPE_LDC_PAYMENT_MANDATE ConsentType = 100
	// Consent to allow Innofin Solutions Pvt Ltd (Lenden Club) to fetch user's Aadhaar data from DigiLocker
	ConsentType_CONSENT_TYPE_LDC_AADHAAR_DATA_PULL ConsentType = 101
	// Consent allowing Lenden Club to fetch user's CKYC data and confirming that the user is
	// not a politically exposed person and is not a citizen of any country other than India
	ConsentType_CONSENT_TYPE_LDC_POLITICAL_EXPOSURE_CITIZENSHIP ConsentType = 102
	// Consent allowing Lenden Club to fetch user's CKYC data
	ConsentType_CONSENT_TYPE_LDC_CKYC_DATA_PULL ConsentType = 103
	// User consents to filing form 60 as a declaration
	ConsentType_CONSENT_TYPE_FORM_60_DECLARATION ConsentType = 104
	ConsentType_CONSENT_TYPE_LDC_MODIFY_ROI      ConsentType = 105
	// User declars that he/she is not differently abled person
	ConsentType_CONSENT_TYPE_DISABILITY ConsentType = 106
	// User declars if the account will not be used to receive any scholarship fund
	ConsentType_CONSENT_TYPE_SCHOLARSHIP ConsentType = 107
	// User declars if the account will not be used for DBT
	ConsentType_CONSENT_TYPE_DIRECT_BENEFIT_TRANSFER ConsentType = 108
	// User consent to use Fi MCP
	ConsentType_FI_MCP_AUTH ConsentType = 109
	// user consent as per new SEBI guidelines for wealth (mutual funds)
	// figma: https://www.figma.com/design/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=21053-3867&t=O4Q25jFCOhOqUxLT-4
	ConsentType_FI_WEALTH_MITC_TNC ConsentType = 110
)

// Enum value maps for ConsentType.
var (
	ConsentType_name = map[int32]string{
		0:   "ConsentType_UNSPECIFIED",
		1:   "TnC",
		2:   "cKYC",
		3:   "eKYC",
		4:   "Waitlist",
		5:   "UPI",
		6:   "FI_TNC",
		7:   "FED_TNC",
		8:   "FI_PRIVACY_POLICY",
		9:   "FI_WEALTH_TNC",
		10:  "CREDIT_REPORT_TNC",
		11:  "HIGH_RISK_DEVICE",
		12:  "FI_P2P_INVESTMENT",
		13:  "FI_PRE_APPROVED_LOAN",
		14:  "VPA_MIGRATION",
		15:  "SECURE_USAGE_GUIDELINES",
		16:  "CREDIT_REPORT_UPDATED_TNC",
		17:  "INCOME_OCCUPATION_DISCREPANCY",
		18:  "FI_WEALTH_MF_HOLDINGS_IMPORT_MF_CENTRAL_TNC",
		19:  "FI_CREDIT_CARD",
		20:  "FI_CREDIT_CARD_EMI_TNC",
		21:  "FI_CREDIT_CARD_KFS",
		22:  "FI_CREDIT_CARD_MOST_IMP_TNC",
		23:  "FI_CREDIT_CARD_TNC",
		24:  "AADHAAR_NUMBER_BASED_PIN_SET",
		25:  "PL_IDFC_PAN_DOB",
		26:  "FED_STORAGE_POLICY",
		27:  "INITIATE_BKYC",
		28:  "CONFIRM_BKYC_RECORD",
		29:  "FI_WEALTH_MF_HOLDINGS_IMPORT_MF_CENTRAL_TNC_V2",
		30:  "MF_HOLDINGS_DATA_AVAILABILITY_FOR_LIMITED_TIME",
		31:  "SECURED_CREDIT_CARD_OPEN_FIXED_DEPOSIT",
		32:  "CREDIT_REPORT_LIMITED_TIME_TNC",
		33:  "FI_CLOSED_SAVINGS_ACCOUNT_DELETION",
		34:  "PL_LL_PAN_DOB",
		35:  "EPF_TNC",
		36:  "EPIFI_INSIGHTS_TNC",
		37:  "CREDIT_REPORT_DATA_PULL",
		38:  "PL_IDFC_MANDATE",
		39:  "PL_LL_CREDIT_REPORT_DATA_PULL",
		40:  "CONFIRM_PROFILE_UPDATE_AT_BANK",
		41:  "REJECT_PROFILE_UPDATE_AT_BANK",
		42:  "DEVICE_UNLOCK_BIOMETRIC",
		43:  "CONNECTED_ACCOUNTS_LIMITED_TIME_TNC",
		44:  "CONNECTED_ACCOUNTS_FI_WEALTH_AND_FINVU_TNC",
		45:  "CONNECTED_ACCOUNTS_FI_WEALTH_AND_ONEMONEY_TNC",
		46:  "BKYC_CUSTOMER_DUE_DILIGENCE",
		47:  "SA_CLOSURE_REQUEST",
		48:  "MANUAL_ASSET_FORM_TNC",
		49:  "ITR_INTIMATION",
		50:  "EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP",
		51:  "DATA_PULL_BY_MONEYVIEW_LENDER",
		52:  "CIBIL_REPORT_TNC",
		53:  "UNSECURED_CREDIT_CARD_MOST_IMP_TNC",
		54:  "UNSECURED_CREDIT_CARD_KFS",
		55:  "UNSECURED_CREDIT_CARD_FEES",
		56:  "SECURED_CREDIT_CARD_MOST_IMP_TNC",
		57:  "SECURED_CREDIT_CARD_KFS",
		58:  "MASS_UNSECURED_CREDIT_CARD_MOST_IMP_TNC",
		59:  "MASS_UNSECURED_CREDIT_CARD_KFS",
		60:  "CREDIT_REPORT_CHECK",
		61:  "OPEN_FIXED_DEPOSIT",
		62:  "EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_SCREENER_GROUP",
		63:  "LOANS_ABFL_EMPLOYMENT_DATA",
		64:  "ITR_INTIMATION_PROCESSING_AND_SHARING_WITH_LENDERS",
		65:  "EPIFI_WEALTH_INVESTMENT_RISK_PROFILE",
		66:  "LINK_CONNECTED_ACCOUNTS_VIA_UPI",
		70:  "CONSENT_FI_NRE_NRO_ACCOUNTS",
		67:  "CONSENT_NON_INDIAN_RESIDENCY",
		68:  "CONSENT_NOT_POLITICALLY_EXPOSED",
		76:  "CONSENT_FATCA_CRS",
		69:  "CONSENT_NON_RESIDENT_ACCOUNT_TESTING",
		75:  "CONSENT_NRO_ACCOUNT_CREATION",
		71:  "CONSENT_CONFIRM_COMMUNICATION_ADDRESS",
		72:  "CONSENT_USE_AA_DATA_FOR_LENDING",
		73:  "LENDING_CONFIRM_REVISED_OFFER_TERMS",
		74:  "CONSENT_FEDERAL_AA_DATA_SHARE",
		77:  "CONSENT_INDIAN_RESIDENCY",
		78:  "CONSENT_STOCK_GUARDIAN_TSP_CKYC_DOWNLOAD",
		79:  "CONSENT_STOCK_GUARDIAN_TSP_TYPE_CREDIT_REPORT_HARD_PULL",
		80:  "CONSENT_STOCK_GUARDIAN_TSP_DATA_USAGE_FOR_PROMOTIONS",
		81:  "CONSENT_SMS_FETCHER_POC_WEALTH_TNC",
		82:  "CONSENT_LOANS_SG_CKYC_RECORD_FETCH",
		83:  "CONSENT_LOANS_SG_CREDIT_BUREAU_INFO",
		84:  "CONSENT_LOANS_SG_DATA_USE_BY_ASSOCIATED_PARTNERS",
		85:  "CONSENT_OPEN_MIN_BALANCE_SAVINGS_ACCOUNT",
		86:  "CONSENT_SMS_DATA_PROCESSING_CONSENT",
		87:  "CONSENT_SMS_FETCHER_WEALTH_TNC",
		88:  "CONSENT_LENDEN_CREDIT_REPORT_CHECK_AND_PAN_VALIDATION",
		89:  "CONSENT_PRE_BRE_LENDEN_PRIVACY_POLICY_AND_TNC",
		90:  "CONSENT_LENDEN_DATA_SHARING",
		91:  "CONSENT_DECLARATION_OF_ANNUAL_INCOME",
		92:  "CONSENT_LOANS_LDC_KFS_AND_LOAN_AGREEMENT_UNDERSTOOD",
		93:  "CONSENT_LOANS_LDC_KFS_ALLOW_LOCATION",
		94:  "CONSENT_LOANS_LDC_KFS_AND_LOAN_AGREEMENT_LANGUAGE_UNDERSTOOD",
		95:  "CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2",
		96:  "CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP",
		97:  "CONSENT_TYPE_DATA_PULL_BY_PWA_ABFL_LENDER",
		98:  "CONSENT_TYPE_DATA_SHARING_WITH_FEDERAL",
		99:  "CONSENT_TYPE_CREDIT_REPORT_PULL_BY_FEDERAL_LENDER",
		100: "CONSENT_TYPE_LDC_PAYMENT_MANDATE",
		101: "CONSENT_TYPE_LDC_AADHAAR_DATA_PULL",
		102: "CONSENT_TYPE_LDC_POLITICAL_EXPOSURE_CITIZENSHIP",
		103: "CONSENT_TYPE_LDC_CKYC_DATA_PULL",
		104: "CONSENT_TYPE_FORM_60_DECLARATION",
		105: "CONSENT_TYPE_LDC_MODIFY_ROI",
		106: "CONSENT_TYPE_DISABILITY",
		107: "CONSENT_TYPE_SCHOLARSHIP",
		108: "CONSENT_TYPE_DIRECT_BENEFIT_TRANSFER",
		109: "FI_MCP_AUTH",
		110: "FI_WEALTH_MITC_TNC",
	}
	ConsentType_value = map[string]int32{
		"ConsentType_UNSPECIFIED":       0,
		"TnC":                           1,
		"cKYC":                          2,
		"eKYC":                          3,
		"Waitlist":                      4,
		"UPI":                           5,
		"FI_TNC":                        6,
		"FED_TNC":                       7,
		"FI_PRIVACY_POLICY":             8,
		"FI_WEALTH_TNC":                 9,
		"CREDIT_REPORT_TNC":             10,
		"HIGH_RISK_DEVICE":              11,
		"FI_P2P_INVESTMENT":             12,
		"FI_PRE_APPROVED_LOAN":          13,
		"VPA_MIGRATION":                 14,
		"SECURE_USAGE_GUIDELINES":       15,
		"CREDIT_REPORT_UPDATED_TNC":     16,
		"INCOME_OCCUPATION_DISCREPANCY": 17,
		"FI_WEALTH_MF_HOLDINGS_IMPORT_MF_CENTRAL_TNC": 18,
		"FI_CREDIT_CARD":                                                   19,
		"FI_CREDIT_CARD_EMI_TNC":                                           20,
		"FI_CREDIT_CARD_KFS":                                               21,
		"FI_CREDIT_CARD_MOST_IMP_TNC":                                      22,
		"FI_CREDIT_CARD_TNC":                                               23,
		"AADHAAR_NUMBER_BASED_PIN_SET":                                     24,
		"PL_IDFC_PAN_DOB":                                                  25,
		"FED_STORAGE_POLICY":                                               26,
		"INITIATE_BKYC":                                                    27,
		"CONFIRM_BKYC_RECORD":                                              28,
		"FI_WEALTH_MF_HOLDINGS_IMPORT_MF_CENTRAL_TNC_V2":                   29,
		"MF_HOLDINGS_DATA_AVAILABILITY_FOR_LIMITED_TIME":                   30,
		"SECURED_CREDIT_CARD_OPEN_FIXED_DEPOSIT":                           31,
		"CREDIT_REPORT_LIMITED_TIME_TNC":                                   32,
		"FI_CLOSED_SAVINGS_ACCOUNT_DELETION":                               33,
		"PL_LL_PAN_DOB":                                                    34,
		"EPF_TNC":                                                          35,
		"EPIFI_INSIGHTS_TNC":                                               36,
		"CREDIT_REPORT_DATA_PULL":                                          37,
		"PL_IDFC_MANDATE":                                                  38,
		"PL_LL_CREDIT_REPORT_DATA_PULL":                                    39,
		"CONFIRM_PROFILE_UPDATE_AT_BANK":                                   40,
		"REJECT_PROFILE_UPDATE_AT_BANK":                                    41,
		"DEVICE_UNLOCK_BIOMETRIC":                                          42,
		"CONNECTED_ACCOUNTS_LIMITED_TIME_TNC":                              43,
		"CONNECTED_ACCOUNTS_FI_WEALTH_AND_FINVU_TNC":                       44,
		"CONNECTED_ACCOUNTS_FI_WEALTH_AND_ONEMONEY_TNC":                    45,
		"BKYC_CUSTOMER_DUE_DILIGENCE":                                      46,
		"SA_CLOSURE_REQUEST":                                               47,
		"MANUAL_ASSET_FORM_TNC":                                            48,
		"ITR_INTIMATION":                                                   49,
		"EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP":                       50,
		"DATA_PULL_BY_MONEYVIEW_LENDER":                                    51,
		"CIBIL_REPORT_TNC":                                                 52,
		"UNSECURED_CREDIT_CARD_MOST_IMP_TNC":                               53,
		"UNSECURED_CREDIT_CARD_KFS":                                        54,
		"UNSECURED_CREDIT_CARD_FEES":                                       55,
		"SECURED_CREDIT_CARD_MOST_IMP_TNC":                                 56,
		"SECURED_CREDIT_CARD_KFS":                                          57,
		"MASS_UNSECURED_CREDIT_CARD_MOST_IMP_TNC":                          58,
		"MASS_UNSECURED_CREDIT_CARD_KFS":                                   59,
		"CREDIT_REPORT_CHECK":                                              60,
		"OPEN_FIXED_DEPOSIT":                                               61,
		"EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_SCREENER_GROUP":              62,
		"LOANS_ABFL_EMPLOYMENT_DATA":                                       63,
		"ITR_INTIMATION_PROCESSING_AND_SHARING_WITH_LENDERS":               64,
		"EPIFI_WEALTH_INVESTMENT_RISK_PROFILE":                             65,
		"LINK_CONNECTED_ACCOUNTS_VIA_UPI":                                  66,
		"CONSENT_FI_NRE_NRO_ACCOUNTS":                                      70,
		"CONSENT_NON_INDIAN_RESIDENCY":                                     67,
		"CONSENT_NOT_POLITICALLY_EXPOSED":                                  68,
		"CONSENT_FATCA_CRS":                                                76,
		"CONSENT_NON_RESIDENT_ACCOUNT_TESTING":                             69,
		"CONSENT_NRO_ACCOUNT_CREATION":                                     75,
		"CONSENT_CONFIRM_COMMUNICATION_ADDRESS":                            71,
		"CONSENT_USE_AA_DATA_FOR_LENDING":                                  72,
		"LENDING_CONFIRM_REVISED_OFFER_TERMS":                              73,
		"CONSENT_FEDERAL_AA_DATA_SHARE":                                    74,
		"CONSENT_INDIAN_RESIDENCY":                                         77,
		"CONSENT_STOCK_GUARDIAN_TSP_CKYC_DOWNLOAD":                         78,
		"CONSENT_STOCK_GUARDIAN_TSP_TYPE_CREDIT_REPORT_HARD_PULL":          79,
		"CONSENT_STOCK_GUARDIAN_TSP_DATA_USAGE_FOR_PROMOTIONS":             80,
		"CONSENT_SMS_FETCHER_POC_WEALTH_TNC":                               81,
		"CONSENT_LOANS_SG_CKYC_RECORD_FETCH":                               82,
		"CONSENT_LOANS_SG_CREDIT_BUREAU_INFO":                              83,
		"CONSENT_LOANS_SG_DATA_USE_BY_ASSOCIATED_PARTNERS":                 84,
		"CONSENT_OPEN_MIN_BALANCE_SAVINGS_ACCOUNT":                         85,
		"CONSENT_SMS_DATA_PROCESSING_CONSENT":                              86,
		"CONSENT_SMS_FETCHER_WEALTH_TNC":                                   87,
		"CONSENT_LENDEN_CREDIT_REPORT_CHECK_AND_PAN_VALIDATION":            88,
		"CONSENT_PRE_BRE_LENDEN_PRIVACY_POLICY_AND_TNC":                    89,
		"CONSENT_LENDEN_DATA_SHARING":                                      90,
		"CONSENT_DECLARATION_OF_ANNUAL_INCOME":                             91,
		"CONSENT_LOANS_LDC_KFS_AND_LOAN_AGREEMENT_UNDERSTOOD":              92,
		"CONSENT_LOANS_LDC_KFS_ALLOW_LOCATION":                             93,
		"CONSENT_LOANS_LDC_KFS_AND_LOAN_AGREEMENT_LANGUAGE_UNDERSTOOD":     94,
		"CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2":            95,
		"CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP": 96,
		"CONSENT_TYPE_DATA_PULL_BY_PWA_ABFL_LENDER":                        97,
		"CONSENT_TYPE_DATA_SHARING_WITH_FEDERAL":                           98,
		"CONSENT_TYPE_CREDIT_REPORT_PULL_BY_FEDERAL_LENDER":                99,
		"CONSENT_TYPE_LDC_PAYMENT_MANDATE":                                 100,
		"CONSENT_TYPE_LDC_AADHAAR_DATA_PULL":                               101,
		"CONSENT_TYPE_LDC_POLITICAL_EXPOSURE_CITIZENSHIP":                  102,
		"CONSENT_TYPE_LDC_CKYC_DATA_PULL":                                  103,
		"CONSENT_TYPE_FORM_60_DECLARATION":                                 104,
		"CONSENT_TYPE_LDC_MODIFY_ROI":                                      105,
		"CONSENT_TYPE_DISABILITY":                                          106,
		"CONSENT_TYPE_SCHOLARSHIP":                                         107,
		"CONSENT_TYPE_DIRECT_BENEFIT_TRANSFER":                             108,
		"FI_MCP_AUTH":                                                      109,
		"FI_WEALTH_MITC_TNC":                                               110,
	}
)

func (x ConsentType) Enum() *ConsentType {
	p := new(ConsentType)
	*p = x
	return p
}

func (x ConsentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConsentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_consent_consent_type_proto_enumTypes[0].Descriptor()
}

func (ConsentType) Type() protoreflect.EnumType {
	return &file_api_consent_consent_type_proto_enumTypes[0]
}

func (x ConsentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConsentType.Descriptor instead.
func (ConsentType) EnumDescriptor() ([]byte, []int) {
	return file_api_consent_consent_type_proto_rawDescGZIP(), []int{0}
}

// ConsentProvenance represents the source of consent provided by the user
type ConsentProvenance int32

const (
	ConsentProvenance_CONSENT_PROVENANCE_UNSPECIFIED ConsentProvenance = 0
	// this is an explicit consent provided by the user in the app
	ConsentProvenance_CONSENT_PROVENANCE_IN_APP ConsentProvenance = 1
	// this is an implicit consent taken by just notifying the user via email
	// e.g: we've shared "Quick Update of Terms & Conditions" email to users for accessing their
	// connected accounts data (consent-type: EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP)
	ConsentProvenance_CONSENT_PROVENANCE_EMAIL_IMPLICIT ConsentProvenance = 2
)

// Enum value maps for ConsentProvenance.
var (
	ConsentProvenance_name = map[int32]string{
		0: "CONSENT_PROVENANCE_UNSPECIFIED",
		1: "CONSENT_PROVENANCE_IN_APP",
		2: "CONSENT_PROVENANCE_EMAIL_IMPLICIT",
	}
	ConsentProvenance_value = map[string]int32{
		"CONSENT_PROVENANCE_UNSPECIFIED":    0,
		"CONSENT_PROVENANCE_IN_APP":         1,
		"CONSENT_PROVENANCE_EMAIL_IMPLICIT": 2,
	}
)

func (x ConsentProvenance) Enum() *ConsentProvenance {
	p := new(ConsentProvenance)
	*p = x
	return p
}

func (x ConsentProvenance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConsentProvenance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_consent_consent_type_proto_enumTypes[1].Descriptor()
}

func (ConsentProvenance) Type() protoreflect.EnumType {
	return &file_api_consent_consent_type_proto_enumTypes[1]
}

func (x ConsentProvenance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConsentProvenance.Descriptor instead.
func (ConsentProvenance) EnumDescriptor() ([]byte, []int) {
	return file_api_consent_consent_type_proto_rawDescGZIP(), []int{1}
}

var File_api_consent_consent_type_proto protoreflect.FileDescriptor

var file_api_consent_consent_type_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x07, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x2a, 0x9e, 0x1e, 0x0a, 0x0b, 0x43, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x6e, 0x43, 0x10, 0x01, 0x12,
	0x08, 0x0a, 0x04, 0x63, 0x4b, 0x59, 0x43, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x65, 0x4b, 0x59,
	0x43, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x10,
	0x04, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x50, 0x49, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x49,
	0x5f, 0x54, 0x4e, 0x43, 0x10, 0x06, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x45, 0x44, 0x5f, 0x54, 0x4e,
	0x43, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x43,
	0x59, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x10, 0x08, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49,
	0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x09, 0x12, 0x15, 0x0a,
	0x11, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x54,
	0x4e, 0x43, 0x10, 0x0a, 0x12, 0x14, 0x0a, 0x10, 0x48, 0x49, 0x47, 0x48, 0x5f, 0x52, 0x49, 0x53,
	0x4b, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x0b, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49,
	0x5f, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x0c, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x49, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52,
	0x4f, 0x56, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x0d, 0x12, 0x11, 0x0a, 0x0d, 0x56,
	0x50, 0x41, 0x5f, 0x4d, 0x49, 0x47, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0e, 0x12, 0x1b,
	0x0a, 0x17, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x55, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x47,
	0x55, 0x49, 0x44, 0x45, 0x4c, 0x49, 0x4e, 0x45, 0x53, 0x10, 0x0f, 0x12, 0x1d, 0x0a, 0x19, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x10, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x4e,
	0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x4f, 0x43, 0x43, 0x55, 0x50, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x44, 0x49, 0x53, 0x43, 0x52, 0x45, 0x50, 0x41, 0x4e, 0x43, 0x59, 0x10, 0x11, 0x12, 0x2f, 0x0a,
	0x2b, 0x46, 0x49, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4d, 0x46, 0x5f, 0x48, 0x4f,
	0x4c, 0x44, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4d, 0x46,
	0x5f, 0x43, 0x45, 0x4e, 0x54, 0x52, 0x41, 0x4c, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x12, 0x12, 0x12,
	0x0a, 0x0e, 0x46, 0x49, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x10, 0x13, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x49, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x45, 0x4d, 0x49, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x14, 0x12, 0x16,
	0x0a, 0x12, 0x46, 0x49, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x4b, 0x46, 0x53, 0x10, 0x15, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x49, 0x5f, 0x43, 0x52, 0x45,
	0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4d, 0x4f, 0x53, 0x54, 0x5f, 0x49, 0x4d,
	0x50, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x16, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x5f, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x17, 0x12,
	0x20, 0x0a, 0x1c, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45,
	0x52, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x44, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x10,
	0x18, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x4c, 0x5f, 0x49, 0x44, 0x46, 0x43, 0x5f, 0x50, 0x41, 0x4e,
	0x5f, 0x44, 0x4f, 0x42, 0x10, 0x19, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x45, 0x44, 0x5f, 0x53, 0x54,
	0x4f, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x10, 0x1a, 0x12, 0x11,
	0x0a, 0x0d, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x42, 0x4b, 0x59, 0x43, 0x10,
	0x1b, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x5f, 0x42, 0x4b, 0x59,
	0x43, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x10, 0x1c, 0x12, 0x32, 0x0a, 0x2e, 0x46, 0x49,
	0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4d, 0x46, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x49,
	0x4e, 0x47, 0x53, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4d, 0x46, 0x5f, 0x43, 0x45,
	0x4e, 0x54, 0x52, 0x41, 0x4c, 0x5f, 0x54, 0x4e, 0x43, 0x5f, 0x56, 0x32, 0x10, 0x1d, 0x12, 0x32,
	0x0a, 0x2e, 0x4d, 0x46, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x45, 0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45,
	0x10, 0x1e, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x46,
	0x49, 0x58, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x10, 0x1f, 0x12, 0x22,
	0x0a, 0x1e, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f,
	0x4c, 0x49, 0x4d, 0x49, 0x54, 0x45, 0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x54, 0x4e, 0x43,
	0x10, 0x20, 0x12, 0x26, 0x0a, 0x22, 0x46, 0x49, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x5f,
	0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x21, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4c,
	0x5f, 0x4c, 0x4c, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x44, 0x4f, 0x42, 0x10, 0x22, 0x12, 0x0b, 0x0a,
	0x07, 0x45, 0x50, 0x46, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x23, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x50,
	0x49, 0x46, 0x49, 0x5f, 0x49, 0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x53, 0x5f, 0x54, 0x4e, 0x43,
	0x10, 0x24, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x50, 0x55, 0x4c, 0x4c, 0x10, 0x25, 0x12,
	0x13, 0x0a, 0x0f, 0x50, 0x4c, 0x5f, 0x49, 0x44, 0x46, 0x43, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41,
	0x54, 0x45, 0x10, 0x26, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x4c, 0x5f, 0x4c, 0x4c, 0x5f, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x50, 0x55, 0x4c, 0x4c, 0x10, 0x27, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4e, 0x46, 0x49,
	0x52, 0x4d, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x41, 0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x28, 0x12, 0x21, 0x0a, 0x1d, 0x52,
	0x45, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x29, 0x12, 0x1b,
	0x0a, 0x17, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x4c, 0x4f, 0x43, 0x4b, 0x5f,
	0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x10, 0x2a, 0x12, 0x27, 0x0a, 0x23, 0x43,
	0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x53, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x45, 0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x54,
	0x4e, 0x43, 0x10, 0x2b, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x46, 0x49, 0x5f, 0x57, 0x45,
	0x41, 0x4c, 0x54, 0x48, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x4e, 0x56, 0x55, 0x5f, 0x54,
	0x4e, 0x43, 0x10, 0x2c, 0x12, 0x31, 0x0a, 0x2d, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x46, 0x49, 0x5f, 0x57, 0x45,
	0x41, 0x4c, 0x54, 0x48, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4f, 0x4e, 0x45, 0x4d, 0x4f, 0x4e, 0x45,
	0x59, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x2d, 0x12, 0x1f, 0x0a, 0x1b, 0x42, 0x4b, 0x59, 0x43, 0x5f,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x44, 0x55, 0x45, 0x5f, 0x44, 0x49, 0x4c,
	0x49, 0x47, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x2e, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x41, 0x5f, 0x43,
	0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x2f,
	0x12, 0x19, 0x0a, 0x15, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x30, 0x12, 0x12, 0x0a, 0x0e, 0x49,
	0x54, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x31, 0x12,
	0x2e, 0x0a, 0x2a, 0x45, 0x50, 0x49, 0x46, 0x49, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x49, 0x54,
	0x48, 0x5f, 0x45, 0x50, 0x49, 0x46, 0x49, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x32, 0x12,
	0x21, 0x0a, 0x1d, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x50, 0x55, 0x4c, 0x4c, 0x5f, 0x42, 0x59, 0x5f,
	0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x52,
	0x10, 0x33, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x49, 0x42, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x34, 0x12, 0x26, 0x0a, 0x22, 0x55, 0x4e, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x4d, 0x4f, 0x53, 0x54, 0x5f, 0x49, 0x4d, 0x50, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x35,
	0x12, 0x1d, 0x0a, 0x19, 0x55, 0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4b, 0x46, 0x53, 0x10, 0x36, 0x12,
	0x1e, 0x0a, 0x1a, 0x55, 0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45,
	0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x45, 0x45, 0x53, 0x10, 0x37, 0x12,
	0x24, 0x0a, 0x20, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4d, 0x4f, 0x53, 0x54, 0x5f, 0x49, 0x4d, 0x50, 0x5f,
	0x54, 0x4e, 0x43, 0x10, 0x38, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44,
	0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4b, 0x46, 0x53,
	0x10, 0x39, 0x12, 0x2b, 0x0a, 0x27, 0x4d, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x45, 0x43,
	0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x4d, 0x4f, 0x53, 0x54, 0x5f, 0x49, 0x4d, 0x50, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x3a, 0x12,
	0x22, 0x0a, 0x1e, 0x4d, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45,
	0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4b, 0x46,
	0x53, 0x10, 0x3b, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x3c, 0x12, 0x16, 0x0a, 0x12,
	0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53,
	0x49, 0x54, 0x10, 0x3d, 0x12, 0x37, 0x0a, 0x33, 0x45, 0x50, 0x49, 0x46, 0x49, 0x5f, 0x57, 0x45,
	0x41, 0x4c, 0x54, 0x48, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x49, 0x4e,
	0x47, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x45, 0x50, 0x49, 0x46, 0x49, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x3e, 0x12, 0x1e, 0x0a,
	0x1a, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x41, 0x42, 0x46, 0x4c, 0x5f, 0x45, 0x4d, 0x50, 0x4c,
	0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x3f, 0x12, 0x36, 0x0a,
	0x32, 0x49, 0x54, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x53,
	0x48, 0x41, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x4c, 0x45, 0x4e, 0x44,
	0x45, 0x52, 0x53, 0x10, 0x40, 0x12, 0x28, 0x0a, 0x24, 0x45, 0x50, 0x49, 0x46, 0x49, 0x5f, 0x57,
	0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x41, 0x12,
	0x23, 0x0a, 0x1f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x56, 0x49, 0x41, 0x5f, 0x55,
	0x50, 0x49, 0x10, 0x42, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f,
	0x46, 0x49, 0x5f, 0x4e, 0x52, 0x45, 0x5f, 0x4e, 0x52, 0x4f, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x53, 0x10, 0x46, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54,
	0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x49,
	0x44, 0x45, 0x4e, 0x43, 0x59, 0x10, 0x43, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x4f, 0x4e, 0x53, 0x45,
	0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c,
	0x4c, 0x59, 0x5f, 0x45, 0x58, 0x50, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x44, 0x12, 0x15, 0x0a, 0x11,
	0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x54, 0x43, 0x41, 0x5f, 0x43, 0x52,
	0x53, 0x10, 0x4c, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x4e,
	0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x54, 0x45, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x45, 0x12, 0x20, 0x0a,
	0x1c, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x52, 0x4f, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x4b, 0x12,
	0x29, 0x0a, 0x25, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49,
	0x52, 0x4d, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x55, 0x4e, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x47, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x41, 0x41, 0x5f, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x48, 0x12,
	0x27, 0x0a, 0x23, 0x4c, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49,
	0x52, 0x4d, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x53, 0x45, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x5f, 0x54, 0x45, 0x52, 0x4d, 0x53, 0x10, 0x49, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x4f, 0x4e, 0x53,
	0x45, 0x4e, 0x54, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x41, 0x41, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x45, 0x10, 0x4a, 0x12, 0x1c, 0x0a, 0x18, 0x43,
	0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x52, 0x45,
	0x53, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x59, 0x10, 0x4d, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x4f, 0x4e,
	0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x47, 0x55, 0x41, 0x52, 0x44,
	0x49, 0x41, 0x4e, 0x5f, 0x54, 0x53, 0x50, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x44, 0x4f, 0x57,
	0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x4e, 0x12, 0x3b, 0x0a, 0x37, 0x43, 0x4f, 0x4e, 0x53, 0x45,
	0x4e, 0x54, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41,
	0x4e, 0x5f, 0x54, 0x53, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x55,
	0x4c, 0x4c, 0x10, 0x4f, 0x12, 0x38, 0x0a, 0x34, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f,
	0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x54,
	0x53, 0x50, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x55, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x50, 0x12, 0x2a,
	0x0a, 0x22, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x4d, 0x53, 0x5f, 0x46, 0x45,
	0x54, 0x43, 0x48, 0x45, 0x52, 0x5f, 0x50, 0x4f, 0x43, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48,
	0x5f, 0x54, 0x4e, 0x43, 0x10, 0x51, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x53, 0x47, 0x5f, 0x43,
	0x4b, 0x59, 0x43, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48,
	0x10, 0x52, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x53, 0x5f, 0x53, 0x47, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x42, 0x55,
	0x52, 0x45, 0x41, 0x55, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x53, 0x12, 0x34, 0x0a, 0x30, 0x43,
	0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x53, 0x47, 0x5f,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x42, 0x59, 0x5f, 0x41, 0x53, 0x53, 0x4f,
	0x43, 0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x53, 0x10,
	0x54, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x50, 0x45,
	0x4e, 0x5f, 0x4d, 0x49, 0x4e, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x41,
	0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x55, 0x12,
	0x27, 0x0a, 0x23, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x4d, 0x53, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x43,
	0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x56, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4e, 0x53,
	0x45, 0x4e, 0x54, 0x5f, 0x53, 0x4d, 0x53, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x52, 0x5f,
	0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x57, 0x12, 0x39, 0x0a, 0x35,
	0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x4e, 0x5f, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x48, 0x45,
	0x43, 0x4b, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x58, 0x12, 0x31, 0x0a, 0x2d, 0x43, 0x4f, 0x4e, 0x53, 0x45,
	0x4e, 0x54, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x42, 0x52, 0x45, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x45,
	0x4e, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x43, 0x59, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59,
	0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x59, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x4e, 0x5f, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x5a, 0x12, 0x28, 0x0a, 0x24, 0x43,
	0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x4f, 0x46, 0x5f, 0x41, 0x4e, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x43,
	0x4f, 0x4d, 0x45, 0x10, 0x5b, 0x12, 0x37, 0x0a, 0x33, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54,
	0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4c, 0x44, 0x43, 0x5f, 0x4b, 0x46, 0x53, 0x5f, 0x41,
	0x4e, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x55, 0x4e, 0x44, 0x45, 0x52, 0x53, 0x54, 0x4f, 0x4f, 0x44, 0x10, 0x5c, 0x12, 0x28,
	0x0a, 0x24, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f,
	0x4c, 0x44, 0x43, 0x5f, 0x4b, 0x46, 0x53, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x4c, 0x4f,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x5d, 0x12, 0x40, 0x0a, 0x3c, 0x43, 0x4f, 0x4e, 0x53,
	0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4c, 0x44, 0x43, 0x5f, 0x4b, 0x46,
	0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x47, 0x52, 0x45, 0x45,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x4e,
	0x44, 0x45, 0x52, 0x53, 0x54, 0x4f, 0x4f, 0x44, 0x10, 0x5e, 0x12, 0x39, 0x0a, 0x35, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x45, 0x50, 0x49, 0x46, 0x49, 0x5f, 0x57, 0x45, 0x41, 0x4c,
	0x54, 0x48, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x49, 0x4e, 0x47, 0x5f,
	0x57, 0x49, 0x54, 0x48, 0x5f, 0x45, 0x50, 0x49, 0x46, 0x49, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50,
	0x5f, 0x56, 0x32, 0x10, 0x5f, 0x12, 0x44, 0x0a, 0x40, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x4e, 0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x45,
	0x50, 0x49, 0x46, 0x49, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x53, 0x48, 0x41, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x45, 0x50,
	0x49, 0x46, 0x49, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x60, 0x12, 0x2d, 0x0a, 0x29, 0x43,
	0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x50, 0x55, 0x4c, 0x4c, 0x5f, 0x42, 0x59, 0x5f, 0x50, 0x57, 0x41, 0x5f, 0x41, 0x42, 0x46,
	0x4c, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x10, 0x61, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x53, 0x48, 0x41, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x46, 0x45, 0x44,
	0x45, 0x52, 0x41, 0x4c, 0x10, 0x62, 0x12, 0x35, 0x0a, 0x31, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x50, 0x55, 0x4c, 0x4c, 0x5f, 0x42, 0x59, 0x5f, 0x46, 0x45, 0x44,
	0x45, 0x52, 0x41, 0x4c, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x10, 0x63, 0x12, 0x24, 0x0a,
	0x20, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x44,
	0x43, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x64, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4c, 0x44, 0x43, 0x5f, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x50, 0x55, 0x4c, 0x4c, 0x10, 0x65, 0x12, 0x33, 0x0a, 0x2f, 0x43,
	0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x44, 0x43, 0x5f,
	0x50, 0x4f, 0x4c, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x45, 0x58, 0x50, 0x4f, 0x53, 0x55,
	0x52, 0x45, 0x5f, 0x43, 0x49, 0x54, 0x49, 0x5a, 0x45, 0x4e, 0x53, 0x48, 0x49, 0x50, 0x10, 0x66,
	0x12, 0x23, 0x0a, 0x1f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4c, 0x44, 0x43, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x50,
	0x55, 0x4c, 0x4c, 0x10, 0x67, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x36, 0x30, 0x5f, 0x44, 0x45,
	0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x68, 0x12, 0x1f, 0x0a, 0x1b, 0x43,
	0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x44, 0x43, 0x5f,
	0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x5f, 0x52, 0x4f, 0x49, 0x10, 0x69, 0x12, 0x1b, 0x0a, 0x17,
	0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x53,
	0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x6a, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f, 0x4e,
	0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x43, 0x48, 0x4f, 0x4c, 0x41,
	0x52, 0x53, 0x48, 0x49, 0x50, 0x10, 0x6b, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x4f, 0x4e, 0x53, 0x45,
	0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x42,
	0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x10,
	0x6c, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x49, 0x5f, 0x4d, 0x43, 0x50, 0x5f, 0x41, 0x55, 0x54, 0x48,
	0x10, 0x6d, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f,
	0x4d, 0x49, 0x54, 0x43, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x6e, 0x2a, 0x7d, 0x0a, 0x11, 0x43, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x45,
	0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x50,
	0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50,
	0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52,
	0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x49,
	0x4d, 0x50, 0x4c, 0x49, 0x43, 0x49, 0x54, 0x10, 0x02, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5a,
	0x22, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_consent_consent_type_proto_rawDescOnce sync.Once
	file_api_consent_consent_type_proto_rawDescData = file_api_consent_consent_type_proto_rawDesc
)

func file_api_consent_consent_type_proto_rawDescGZIP() []byte {
	file_api_consent_consent_type_proto_rawDescOnce.Do(func() {
		file_api_consent_consent_type_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_consent_consent_type_proto_rawDescData)
	})
	return file_api_consent_consent_type_proto_rawDescData
}

var file_api_consent_consent_type_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_consent_consent_type_proto_goTypes = []interface{}{
	(ConsentType)(0),       // 0: consent.ConsentType
	(ConsentProvenance)(0), // 1: consent.ConsentProvenance
}
var file_api_consent_consent_type_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_consent_consent_type_proto_init() }
func file_api_consent_consent_type_proto_init() {
	if File_api_consent_consent_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_consent_consent_type_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_consent_consent_type_proto_goTypes,
		DependencyIndexes: file_api_consent_consent_type_proto_depIdxs,
		EnumInfos:         file_api_consent_consent_type_proto_enumTypes,
	}.Build()
	File_api_consent_consent_type_proto = out.File
	file_api_consent_consent_type_proto_rawDesc = nil
	file_api_consent_consent_type_proto_goTypes = nil
	file_api_consent_consent_type_proto_depIdxs = nil
}
