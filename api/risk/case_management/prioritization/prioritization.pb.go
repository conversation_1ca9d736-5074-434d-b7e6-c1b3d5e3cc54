// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/prioritization/prioritization.proto

package prioritization

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	case_management "github.com/epifi/gamma/api/risk/case_management"
	risk "github.com/epifi/gamma/api/vendorgateway/risk"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PrioritizationModelType is the type of the prioritization model
type PrioritizationModelType int32

const (
	PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_UNSPECIFIED PrioritizationModelType = 0
	// legacy prioritization model based on the rule count and profile score
	PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_LEGACY PrioritizationModelType = 1
	// model to take the precision information of the various entities and calculate the model score.
	PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_PRECISION PrioritizationModelType = 2
	// model to send the alerts to data science model and get the confidence score.
	PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_DS_PRECISION PrioritizationModelType = 3
)

// Enum value maps for PrioritizationModelType.
var (
	PrioritizationModelType_name = map[int32]string{
		0: "PRIORITIZATION_MODEL_TYPE_UNSPECIFIED",
		1: "PRIORITIZATION_MODEL_TYPE_LEGACY",
		2: "PRIORITIZATION_MODEL_TYPE_PRECISION",
		3: "PRIORITIZATION_MODEL_TYPE_DS_PRECISION",
	}
	PrioritizationModelType_value = map[string]int32{
		"PRIORITIZATION_MODEL_TYPE_UNSPECIFIED":  0,
		"PRIORITIZATION_MODEL_TYPE_LEGACY":       1,
		"PRIORITIZATION_MODEL_TYPE_PRECISION":    2,
		"PRIORITIZATION_MODEL_TYPE_DS_PRECISION": 3,
	}
)

func (x PrioritizationModelType) Enum() *PrioritizationModelType {
	p := new(PrioritizationModelType)
	*p = x
	return p
}

func (x PrioritizationModelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PrioritizationModelType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_prioritization_prioritization_proto_enumTypes[0].Descriptor()
}

func (PrioritizationModelType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_prioritization_prioritization_proto_enumTypes[0]
}

func (x PrioritizationModelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PrioritizationModelType.Descriptor instead.
func (PrioritizationModelType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_prioritization_prioritization_proto_rawDescGZIP(), []int{0}
}

type ModelTypeParameter_ModelType int32

const (
	ModelTypeParameter_MODEL_TYPE_UNSPECIFIED                          ModelTypeParameter_ModelType = 0
	ModelTypeParameter_MODEL_TYPE_ALL_FEATURE_MODEL                    ModelTypeParameter_ModelType = 1
	ModelTypeParameter_MODEL_TYPE_USER_ONBOARDING_RISK_DETECTION_MODEL ModelTypeParameter_ModelType = 2
)

// Enum value maps for ModelTypeParameter_ModelType.
var (
	ModelTypeParameter_ModelType_name = map[int32]string{
		0: "MODEL_TYPE_UNSPECIFIED",
		1: "MODEL_TYPE_ALL_FEATURE_MODEL",
		2: "MODEL_TYPE_USER_ONBOARDING_RISK_DETECTION_MODEL",
	}
	ModelTypeParameter_ModelType_value = map[string]int32{
		"MODEL_TYPE_UNSPECIFIED":                          0,
		"MODEL_TYPE_ALL_FEATURE_MODEL":                    1,
		"MODEL_TYPE_USER_ONBOARDING_RISK_DETECTION_MODEL": 2,
	}
)

func (x ModelTypeParameter_ModelType) Enum() *ModelTypeParameter_ModelType {
	p := new(ModelTypeParameter_ModelType)
	*p = x
	return p
}

func (x ModelTypeParameter_ModelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelTypeParameter_ModelType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_prioritization_prioritization_proto_enumTypes[1].Descriptor()
}

func (ModelTypeParameter_ModelType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_prioritization_prioritization_proto_enumTypes[1]
}

func (x ModelTypeParameter_ModelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelTypeParameter_ModelType.Descriptor instead.
func (ModelTypeParameter_ModelType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_prioritization_prioritization_proto_rawDescGZIP(), []int{1, 0}
}

type WatchListTypeParameter_WatchListType int32

const (
	WatchListTypeParameter_WATCH_LIST_TYPE_UNSPECIFIED WatchListTypeParameter_WatchListType = 0
	WatchListTypeParameter_WATCH_LIST_TYPE_ACTOR       WatchListTypeParameter_WatchListType = 1
)

// Enum value maps for WatchListTypeParameter_WatchListType.
var (
	WatchListTypeParameter_WatchListType_name = map[int32]string{
		0: "WATCH_LIST_TYPE_UNSPECIFIED",
		1: "WATCH_LIST_TYPE_ACTOR",
	}
	WatchListTypeParameter_WatchListType_value = map[string]int32{
		"WATCH_LIST_TYPE_UNSPECIFIED": 0,
		"WATCH_LIST_TYPE_ACTOR":       1,
	}
)

func (x WatchListTypeParameter_WatchListType) Enum() *WatchListTypeParameter_WatchListType {
	p := new(WatchListTypeParameter_WatchListType)
	*p = x
	return p
}

func (x WatchListTypeParameter_WatchListType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WatchListTypeParameter_WatchListType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_prioritization_prioritization_proto_enumTypes[2].Descriptor()
}

func (WatchListTypeParameter_WatchListType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_prioritization_prioritization_proto_enumTypes[2]
}

func (x WatchListTypeParameter_WatchListType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WatchListTypeParameter_WatchListType.Descriptor instead.
func (WatchListTypeParameter_WatchListType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_prioritization_prioritization_proto_rawDescGZIP(), []int{2, 0}
}

// PrioritizationRequestElement is a unit object to get the prioritization information like precision, count etc. This will
// be common object between different implementation. whether it is a rule, model or a watchlist.
type PrioritizationRequestElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Parameter:
	//
	//	*PrioritizationRequestElement_RuleTypeParameter
	//	*PrioritizationRequestElement_ModelTypeParameter
	//	*PrioritizationRequestElement_WatchListTypeParameter
	Parameter isPrioritizationRequestElement_Parameter `protobuf_oneof:"parameter"`
}

func (x *PrioritizationRequestElement) Reset() {
	*x = PrioritizationRequestElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrioritizationRequestElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrioritizationRequestElement) ProtoMessage() {}

func (x *PrioritizationRequestElement) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrioritizationRequestElement.ProtoReflect.Descriptor instead.
func (*PrioritizationRequestElement) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_prioritization_prioritization_proto_rawDescGZIP(), []int{0}
}

func (m *PrioritizationRequestElement) GetParameter() isPrioritizationRequestElement_Parameter {
	if m != nil {
		return m.Parameter
	}
	return nil
}

func (x *PrioritizationRequestElement) GetRuleTypeParameter() *RuleTypeParameter {
	if x, ok := x.GetParameter().(*PrioritizationRequestElement_RuleTypeParameter); ok {
		return x.RuleTypeParameter
	}
	return nil
}

func (x *PrioritizationRequestElement) GetModelTypeParameter() *ModelTypeParameter {
	if x, ok := x.GetParameter().(*PrioritizationRequestElement_ModelTypeParameter); ok {
		return x.ModelTypeParameter
	}
	return nil
}

func (x *PrioritizationRequestElement) GetWatchListTypeParameter() *WatchListTypeParameter {
	if x, ok := x.GetParameter().(*PrioritizationRequestElement_WatchListTypeParameter); ok {
		return x.WatchListTypeParameter
	}
	return nil
}

type isPrioritizationRequestElement_Parameter interface {
	isPrioritizationRequestElement_Parameter()
}

type PrioritizationRequestElement_RuleTypeParameter struct {
	RuleTypeParameter *RuleTypeParameter `protobuf:"bytes,1,opt,name=rule_type_parameter,json=ruleTypeParameter,proto3,oneof"`
}

type PrioritizationRequestElement_ModelTypeParameter struct {
	ModelTypeParameter *ModelTypeParameter `protobuf:"bytes,2,opt,name=model_type_parameter,json=modelTypeParameter,proto3,oneof"`
}

type PrioritizationRequestElement_WatchListTypeParameter struct {
	WatchListTypeParameter *WatchListTypeParameter `protobuf:"bytes,3,opt,name=watch_list_type_parameter,json=watchListTypeParameter,proto3,oneof"`
}

func (*PrioritizationRequestElement_RuleTypeParameter) isPrioritizationRequestElement_Parameter() {}

func (*PrioritizationRequestElement_ModelTypeParameter) isPrioritizationRequestElement_Parameter() {}

func (*PrioritizationRequestElement_WatchListTypeParameter) isPrioritizationRequestElement_Parameter() {
}

// ModelTypeParameter is the object to fetch the model related prioritization information like precision, count etc.
type ModelTypeParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelType ModelTypeParameter_ModelType `protobuf:"varint,1,opt,name=model_type,json=modelType,proto3,enum=risk.case_management.prioritization.ModelTypeParameter_ModelType" json:"model_type,omitempty"`
	ActorId   string                       `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *ModelTypeParameter) Reset() {
	*x = ModelTypeParameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelTypeParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelTypeParameter) ProtoMessage() {}

func (x *ModelTypeParameter) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelTypeParameter.ProtoReflect.Descriptor instead.
func (*ModelTypeParameter) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_prioritization_prioritization_proto_rawDescGZIP(), []int{1}
}

func (x *ModelTypeParameter) GetModelType() ModelTypeParameter_ModelType {
	if x != nil {
		return x.ModelType
	}
	return ModelTypeParameter_MODEL_TYPE_UNSPECIFIED
}

func (x *ModelTypeParameter) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

// WatchListTypeParameter is the object to fetch the watchlist related prioritization information like precision, count
// etc.
type WatchListTypeParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WatchListType WatchListTypeParameter_WatchListType `protobuf:"varint,1,opt,name=watch_list_type,json=watchListType,proto3,enum=risk.case_management.prioritization.WatchListTypeParameter_WatchListType" json:"watch_list_type,omitempty"`
	// actor id to be searched in the watchlist
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *WatchListTypeParameter) Reset() {
	*x = WatchListTypeParameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchListTypeParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchListTypeParameter) ProtoMessage() {}

func (x *WatchListTypeParameter) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchListTypeParameter.ProtoReflect.Descriptor instead.
func (*WatchListTypeParameter) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_prioritization_prioritization_proto_rawDescGZIP(), []int{2}
}

func (x *WatchListTypeParameter) GetWatchListType() WatchListTypeParameter_WatchListType {
	if x != nil {
		return x.WatchListType
	}
	return WatchListTypeParameter_WATCH_LIST_TYPE_UNSPECIFIED
}

func (x *WatchListTypeParameter) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

// RuleTypeParameter is the object to fetch the rule related prioritization information like precision, count etc.
type RuleTypeParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rule object needed to be evaluated
	Rule *case_management.Rule `protobuf:"bytes,1,opt,name=rule,proto3" json:"rule,omitempty"`
	// all the alerts that we need to search the current rule on.
	Alerts []*case_management.AlertWithRuleDetails `protobuf:"bytes,2,rep,name=alerts,proto3" json:"alerts,omitempty"`
}

func (x *RuleTypeParameter) Reset() {
	*x = RuleTypeParameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleTypeParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleTypeParameter) ProtoMessage() {}

func (x *RuleTypeParameter) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleTypeParameter.ProtoReflect.Descriptor instead.
func (*RuleTypeParameter) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_prioritization_prioritization_proto_rawDescGZIP(), []int{3}
}

func (x *RuleTypeParameter) GetRule() *case_management.Rule {
	if x != nil {
		return x.Rule
	}
	return nil
}

func (x *RuleTypeParameter) GetAlerts() []*case_management.AlertWithRuleDetails {
	if x != nil {
		return x.Alerts
	}
	return nil
}

// InputParameter is the information like rules, model etc. against which model needs to fetch the prioritization
// information like precision, occurrence etc.
type InputParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Alerts         []*case_management.AlertWithRuleDetails             `protobuf:"bytes,1,rep,name=alerts,proto3" json:"alerts,omitempty"`
	WatchListTypes []WatchListTypeParameter_WatchListType              `protobuf:"varint,2,rep,packed,name=watch_list_types,json=watchListTypes,proto3,enum=risk.case_management.prioritization.WatchListTypeParameter_WatchListType" json:"watch_list_types,omitempty"`
	ModelTypes     []ModelTypeParameter_ModelType                      `protobuf:"varint,3,rep,packed,name=model_types,json=modelTypes,proto3,enum=risk.case_management.prioritization.ModelTypeParameter_ModelType" json:"model_types,omitempty"`
	ActorId        string                                              `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	CaseDetails    *risk.GetCasePrioritisationScoreRequest_CaseDetails `protobuf:"bytes,5,opt,name=case_details,json=caseDetails,proto3" json:"case_details,omitempty"`
}

func (x *InputParameter) Reset() {
	*x = InputParameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputParameter) ProtoMessage() {}

func (x *InputParameter) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputParameter.ProtoReflect.Descriptor instead.
func (*InputParameter) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_prioritization_prioritization_proto_rawDescGZIP(), []int{4}
}

func (x *InputParameter) GetAlerts() []*case_management.AlertWithRuleDetails {
	if x != nil {
		return x.Alerts
	}
	return nil
}

func (x *InputParameter) GetWatchListTypes() []WatchListTypeParameter_WatchListType {
	if x != nil {
		return x.WatchListTypes
	}
	return nil
}

func (x *InputParameter) GetModelTypes() []ModelTypeParameter_ModelType {
	if x != nil {
		return x.ModelTypes
	}
	return nil
}

func (x *InputParameter) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *InputParameter) GetCaseDetails() *risk.GetCasePrioritisationScoreRequest_CaseDetails {
	if x != nil {
		return x.CaseDetails
	}
	return nil
}

// PrioritizationModelComputedParameter are the combination of computed parameters which will gets used in coming up at
// the combined confidence score for the prioritization model. these are calculated against the rule or a model or a
// watchlist etc. Once it calculated it gets feeds into the prioritization model to get the cumulative score.
// currently precision and occurrence are two of the parameters, in future it can have recall etc. as well depending
// on the requirements.
type PrioritizationModelComputedParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Precision  float32 `protobuf:"fixed32,1,opt,name=precision,proto3" json:"precision,omitempty"`
	Occurrence int32   `protobuf:"varint,2,opt,name=occurrence,proto3" json:"occurrence,omitempty"`
}

func (x *PrioritizationModelComputedParameter) Reset() {
	*x = PrioritizationModelComputedParameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrioritizationModelComputedParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrioritizationModelComputedParameter) ProtoMessage() {}

func (x *PrioritizationModelComputedParameter) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrioritizationModelComputedParameter.ProtoReflect.Descriptor instead.
func (*PrioritizationModelComputedParameter) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_prioritization_prioritization_proto_rawDescGZIP(), []int{5}
}

func (x *PrioritizationModelComputedParameter) GetPrecision() float32 {
	if x != nil {
		return x.Precision
	}
	return 0
}

func (x *PrioritizationModelComputedParameter) GetOccurrence() int32 {
	if x != nil {
		return x.Occurrence
	}
	return 0
}

var File_api_risk_case_management_prioritization_prioritization_proto protoreflect.FileDescriptor

var file_api_risk_case_management_prioritization_prioritization_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x23,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x6c,
	0x65, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfc, 0x02,
	0x0a, 0x1c, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x68,
	0x0a, 0x13, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x11, 0x72, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x6b, 0x0a, 0x14, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x48,
	0x00, 0x52, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x78, 0x0a, 0x19, 0x77, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x57,
	0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x16, 0x77, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x42,
	0x0b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x22, 0xa4, 0x02, 0x0a,
	0x12, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x12, 0x6a, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x00, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x22, 0x7e, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1a, 0x0a, 0x16, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c,
	0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x10, 0x01, 0x12, 0x33,
	0x0a, 0x2f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x49, 0x53,
	0x4b, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45,
	0x4c, 0x10, 0x02, 0x22, 0x86, 0x02, 0x0a, 0x16, 0x57, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x7b,
	0x0a, 0x0f, 0x77, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x49, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x57, 0x61,
	0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x77, 0x61,
	0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22,
	0x4b, 0x0a, 0x0d, 0x57, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1f, 0x0a, 0x1b, 0x57, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x19, 0x0a, 0x15, 0x57, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x01, 0x22, 0x87, 0x01, 0x0a,
	0x11, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x12, 0x2e, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x72, 0x75,
	0x6c, 0x65, 0x12, 0x42, 0x0a, 0x06, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x57,
	0x69, 0x74, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x06,
	0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x22, 0xae, 0x03, 0x0a, 0x0e, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x06, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x57, 0x69, 0x74, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x06, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x12, 0x73, 0x0a,
	0x10, 0x77, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x49, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x57, 0x61,
	0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0e, 0x77, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x62, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x64, 0x0a, 0x0c, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43,
	0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x63, 0x61, 0x73, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x64, 0x0a, 0x24, 0x50, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f,
	0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12,
	0x1c, 0x0a, 0x09, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x09, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a,
	0x0a, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2a, 0xbf, 0x01,
	0x0a, 0x17, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x25, 0x50, 0x52, 0x49,
	0x4f, 0x52, 0x49, 0x54, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45,
	0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49, 0x54, 0x49,
	0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4c, 0x45, 0x47, 0x41, 0x43, 0x59, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x50, 0x52,
	0x49, 0x4f, 0x52, 0x49, 0x54, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x4f, 0x44,
	0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f,
	0x4e, 0x10, 0x02, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49, 0x54, 0x49, 0x5a,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x44, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x42,
	0x80, 0x01, 0x0a, 0x3e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5a, 0x3e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_prioritization_prioritization_proto_rawDescOnce sync.Once
	file_api_risk_case_management_prioritization_prioritization_proto_rawDescData = file_api_risk_case_management_prioritization_prioritization_proto_rawDesc
)

func file_api_risk_case_management_prioritization_prioritization_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_prioritization_prioritization_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_prioritization_prioritization_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_prioritization_prioritization_proto_rawDescData)
	})
	return file_api_risk_case_management_prioritization_prioritization_proto_rawDescData
}

var file_api_risk_case_management_prioritization_prioritization_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_risk_case_management_prioritization_prioritization_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_risk_case_management_prioritization_prioritization_proto_goTypes = []interface{}{
	(PrioritizationModelType)(0),                               // 0: risk.case_management.prioritization.PrioritizationModelType
	(ModelTypeParameter_ModelType)(0),                          // 1: risk.case_management.prioritization.ModelTypeParameter.ModelType
	(WatchListTypeParameter_WatchListType)(0),                  // 2: risk.case_management.prioritization.WatchListTypeParameter.WatchListType
	(*PrioritizationRequestElement)(nil),                       // 3: risk.case_management.prioritization.PrioritizationRequestElement
	(*ModelTypeParameter)(nil),                                 // 4: risk.case_management.prioritization.ModelTypeParameter
	(*WatchListTypeParameter)(nil),                             // 5: risk.case_management.prioritization.WatchListTypeParameter
	(*RuleTypeParameter)(nil),                                  // 6: risk.case_management.prioritization.RuleTypeParameter
	(*InputParameter)(nil),                                     // 7: risk.case_management.prioritization.InputParameter
	(*PrioritizationModelComputedParameter)(nil),               // 8: risk.case_management.prioritization.PrioritizationModelComputedParameter
	(*case_management.Rule)(nil),                               // 9: risk.case_management.Rule
	(*case_management.AlertWithRuleDetails)(nil),               // 10: risk.case_management.AlertWithRuleDetails
	(*risk.GetCasePrioritisationScoreRequest_CaseDetails)(nil), // 11: vendorgateway.risk.GetCasePrioritisationScoreRequest.CaseDetails
}
var file_api_risk_case_management_prioritization_prioritization_proto_depIdxs = []int32{
	6,  // 0: risk.case_management.prioritization.PrioritizationRequestElement.rule_type_parameter:type_name -> risk.case_management.prioritization.RuleTypeParameter
	4,  // 1: risk.case_management.prioritization.PrioritizationRequestElement.model_type_parameter:type_name -> risk.case_management.prioritization.ModelTypeParameter
	5,  // 2: risk.case_management.prioritization.PrioritizationRequestElement.watch_list_type_parameter:type_name -> risk.case_management.prioritization.WatchListTypeParameter
	1,  // 3: risk.case_management.prioritization.ModelTypeParameter.model_type:type_name -> risk.case_management.prioritization.ModelTypeParameter.ModelType
	2,  // 4: risk.case_management.prioritization.WatchListTypeParameter.watch_list_type:type_name -> risk.case_management.prioritization.WatchListTypeParameter.WatchListType
	9,  // 5: risk.case_management.prioritization.RuleTypeParameter.rule:type_name -> risk.case_management.Rule
	10, // 6: risk.case_management.prioritization.RuleTypeParameter.alerts:type_name -> risk.case_management.AlertWithRuleDetails
	10, // 7: risk.case_management.prioritization.InputParameter.alerts:type_name -> risk.case_management.AlertWithRuleDetails
	2,  // 8: risk.case_management.prioritization.InputParameter.watch_list_types:type_name -> risk.case_management.prioritization.WatchListTypeParameter.WatchListType
	1,  // 9: risk.case_management.prioritization.InputParameter.model_types:type_name -> risk.case_management.prioritization.ModelTypeParameter.ModelType
	11, // 10: risk.case_management.prioritization.InputParameter.case_details:type_name -> vendorgateway.risk.GetCasePrioritisationScoreRequest.CaseDetails
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_prioritization_prioritization_proto_init() }
func file_api_risk_case_management_prioritization_prioritization_proto_init() {
	if File_api_risk_case_management_prioritization_prioritization_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrioritizationRequestElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelTypeParameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatchListTypeParameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleTypeParameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputParameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrioritizationModelComputedParameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_case_management_prioritization_prioritization_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*PrioritizationRequestElement_RuleTypeParameter)(nil),
		(*PrioritizationRequestElement_ModelTypeParameter)(nil),
		(*PrioritizationRequestElement_WatchListTypeParameter)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_prioritization_prioritization_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_case_management_prioritization_prioritization_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_prioritization_prioritization_proto_depIdxs,
		EnumInfos:         file_api_risk_case_management_prioritization_prioritization_proto_enumTypes,
		MessageInfos:      file_api_risk_case_management_prioritization_prioritization_proto_msgTypes,
	}.Build()
	File_api_risk_case_management_prioritization_prioritization_proto = out.File
	file_api_risk_case_management_prioritization_prioritization_proto_rawDesc = nil
	file_api_risk_case_management_prioritization_prioritization_proto_goTypes = nil
	file_api_risk_case_management_prioritization_prioritization_proto_depIdxs = nil
}
