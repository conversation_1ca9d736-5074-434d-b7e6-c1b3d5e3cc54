syntax = "proto3";

package risk.case_management.prioritization;

import "api/risk/case_management/alert.proto";
import "api/risk/case_management/rule.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management/prioritization";
option java_package = "com.github.epifi.gamma.api.risk.case_management.prioritization";

/*
    PrioritizationRequestElement is a unit object to get the prioritization information like precision, count etc. This will
    be common object between different implementation. whether it is a rule, model or a watchlist.
*/
message PrioritizationRequestElement {
  oneof parameter {
    RuleTypeParameter rule_type_parameter = 1;
    ModelTypeParameter model_type_parameter = 2;
    WatchListTypeParameter watch_list_type_parameter = 3;
  }
}

// ModelTypeParameter is the object to fetch the model related prioritization information like precision, count etc.
message ModelTypeParameter {
  enum ModelType {
    MODEL_TYPE_UNSPECIFIED = 0;
    MODEL_TYPE_ALL_FEATURE_MODEL = 1;
    MODEL_TYPE_USER_ONBOARDING_RISK_DETECTION_MODEL = 2;
  }
  ModelType model_type = 1 [(validate.rules).enum = {not_in: [0]}];
  string actor_id = 2 [(validate.rules).string.min_len = 1];
}

// WatchListTypeParameter is the object to fetch the watchlist related prioritization information like precision, count
// etc.
message WatchListTypeParameter {
  enum WatchListType {
    WATCH_LIST_TYPE_UNSPECIFIED = 0;
    WATCH_LIST_TYPE_ACTOR = 1;
  }
  WatchListType watch_list_type = 1 [(validate.rules).enum = {not_in: [0]}];
  // actor id to be searched in the watchlist
  string actor_id = 2 [(validate.rules).string.min_len = 1];
}

// RuleTypeParameter is the object to fetch the rule related prioritization information like precision, count etc.
message RuleTypeParameter {
  // rule object needed to be evaluated
  Rule rule = 1;
  // all the alerts that we need to search the current rule on.
  repeated AlertWithRuleDetails alerts = 2;
}

// PrioritizationModelType is the type of the prioritization model
enum PrioritizationModelType {
  PRIORITIZATION_MODEL_TYPE_UNSPECIFIED = 0;

  // legacy prioritization model based on the rule count and profile score
  PRIORITIZATION_MODEL_TYPE_LEGACY = 1;

  // model to take the precision information of the various entities and calculate the model score.
  PRIORITIZATION_MODEL_TYPE_PRECISION = 2;

  // model to send the alerts to data science model and get the confidence score.
  PRIORITIZATION_MODEL_TYPE_DS_PRECISION = 3;
}

// InputParameter is the information like rules, model etc. against which model needs to fetch the prioritization
// information like precision, occurrence etc.
message InputParameter {
  repeated AlertWithRuleDetails alerts = 1;

  repeated WatchListTypeParameter.WatchListType watch_list_types = 2;

  repeated ModelTypeParameter.ModelType model_types = 3;

  string actor_id = 4;
}

// PrioritizationModelComputedParameter are the combination of computed parameters which will gets used in coming up at
// the combined confidence score for the prioritization model. these are calculated against the rule or a model or a
// watchlist etc. Once it calculated it gets feeds into the prioritization model to get the cumulative score.
// currently precision and occurrence are two of the parameters, in future it can have recall etc. as well depending
// on the requirements.
message PrioritizationModelComputedParameter {
  float precision = 1;

  int32 occurrence = 2;
}

