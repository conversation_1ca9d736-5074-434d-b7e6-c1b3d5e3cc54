// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/case_management/prioritization/prioritization.proto

package prioritization

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PrioritizationRequestElement with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PrioritizationRequestElement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrioritizationRequestElement with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PrioritizationRequestElementMultiError, or nil if none found.
func (m *PrioritizationRequestElement) ValidateAll() error {
	return m.validate(true)
}

func (m *PrioritizationRequestElement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Parameter.(type) {
	case *PrioritizationRequestElement_RuleTypeParameter:
		if v == nil {
			err := PrioritizationRequestElementValidationError{
				field:  "Parameter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRuleTypeParameter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PrioritizationRequestElementValidationError{
						field:  "RuleTypeParameter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PrioritizationRequestElementValidationError{
						field:  "RuleTypeParameter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRuleTypeParameter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PrioritizationRequestElementValidationError{
					field:  "RuleTypeParameter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PrioritizationRequestElement_ModelTypeParameter:
		if v == nil {
			err := PrioritizationRequestElementValidationError{
				field:  "Parameter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetModelTypeParameter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PrioritizationRequestElementValidationError{
						field:  "ModelTypeParameter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PrioritizationRequestElementValidationError{
						field:  "ModelTypeParameter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetModelTypeParameter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PrioritizationRequestElementValidationError{
					field:  "ModelTypeParameter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PrioritizationRequestElement_WatchListTypeParameter:
		if v == nil {
			err := PrioritizationRequestElementValidationError{
				field:  "Parameter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWatchListTypeParameter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PrioritizationRequestElementValidationError{
						field:  "WatchListTypeParameter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PrioritizationRequestElementValidationError{
						field:  "WatchListTypeParameter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWatchListTypeParameter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PrioritizationRequestElementValidationError{
					field:  "WatchListTypeParameter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return PrioritizationRequestElementMultiError(errors)
	}

	return nil
}

// PrioritizationRequestElementMultiError is an error wrapping multiple
// validation errors returned by PrioritizationRequestElement.ValidateAll() if
// the designated constraints aren't met.
type PrioritizationRequestElementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrioritizationRequestElementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrioritizationRequestElementMultiError) AllErrors() []error { return m }

// PrioritizationRequestElementValidationError is the validation error returned
// by PrioritizationRequestElement.Validate if the designated constraints
// aren't met.
type PrioritizationRequestElementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrioritizationRequestElementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrioritizationRequestElementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrioritizationRequestElementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrioritizationRequestElementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrioritizationRequestElementValidationError) ErrorName() string {
	return "PrioritizationRequestElementValidationError"
}

// Error satisfies the builtin error interface
func (e PrioritizationRequestElementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrioritizationRequestElement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrioritizationRequestElementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrioritizationRequestElementValidationError{}

// Validate checks the field values on ModelTypeParameter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModelTypeParameter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModelTypeParameter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModelTypeParameterMultiError, or nil if none found.
func (m *ModelTypeParameter) ValidateAll() error {
	return m.validate(true)
}

func (m *ModelTypeParameter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _ModelTypeParameter_ModelType_NotInLookup[m.GetModelType()]; ok {
		err := ModelTypeParameterValidationError{
			field:  "ModelType",
			reason: "value must not be in list [MODEL_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := ModelTypeParameterValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ModelTypeParameterMultiError(errors)
	}

	return nil
}

// ModelTypeParameterMultiError is an error wrapping multiple validation errors
// returned by ModelTypeParameter.ValidateAll() if the designated constraints
// aren't met.
type ModelTypeParameterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModelTypeParameterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModelTypeParameterMultiError) AllErrors() []error { return m }

// ModelTypeParameterValidationError is the validation error returned by
// ModelTypeParameter.Validate if the designated constraints aren't met.
type ModelTypeParameterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModelTypeParameterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModelTypeParameterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModelTypeParameterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModelTypeParameterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModelTypeParameterValidationError) ErrorName() string {
	return "ModelTypeParameterValidationError"
}

// Error satisfies the builtin error interface
func (e ModelTypeParameterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModelTypeParameter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModelTypeParameterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModelTypeParameterValidationError{}

var _ModelTypeParameter_ModelType_NotInLookup = map[ModelTypeParameter_ModelType]struct{}{
	0: {},
}

// Validate checks the field values on WatchListTypeParameter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WatchListTypeParameter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatchListTypeParameter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WatchListTypeParameterMultiError, or nil if none found.
func (m *WatchListTypeParameter) ValidateAll() error {
	return m.validate(true)
}

func (m *WatchListTypeParameter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _WatchListTypeParameter_WatchListType_NotInLookup[m.GetWatchListType()]; ok {
		err := WatchListTypeParameterValidationError{
			field:  "WatchListType",
			reason: "value must not be in list [WATCH_LIST_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := WatchListTypeParameterValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return WatchListTypeParameterMultiError(errors)
	}

	return nil
}

// WatchListTypeParameterMultiError is an error wrapping multiple validation
// errors returned by WatchListTypeParameter.ValidateAll() if the designated
// constraints aren't met.
type WatchListTypeParameterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatchListTypeParameterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatchListTypeParameterMultiError) AllErrors() []error { return m }

// WatchListTypeParameterValidationError is the validation error returned by
// WatchListTypeParameter.Validate if the designated constraints aren't met.
type WatchListTypeParameterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatchListTypeParameterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatchListTypeParameterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatchListTypeParameterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatchListTypeParameterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatchListTypeParameterValidationError) ErrorName() string {
	return "WatchListTypeParameterValidationError"
}

// Error satisfies the builtin error interface
func (e WatchListTypeParameterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatchListTypeParameter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatchListTypeParameterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatchListTypeParameterValidationError{}

var _WatchListTypeParameter_WatchListType_NotInLookup = map[WatchListTypeParameter_WatchListType]struct{}{
	0: {},
}

// Validate checks the field values on RuleTypeParameter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RuleTypeParameter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RuleTypeParameter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RuleTypeParameterMultiError, or nil if none found.
func (m *RuleTypeParameter) ValidateAll() error {
	return m.validate(true)
}

func (m *RuleTypeParameter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleTypeParameterValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleTypeParameterValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleTypeParameterValidationError{
				field:  "Rule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAlerts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleTypeParameterValidationError{
						field:  fmt.Sprintf("Alerts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleTypeParameterValidationError{
						field:  fmt.Sprintf("Alerts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleTypeParameterValidationError{
					field:  fmt.Sprintf("Alerts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RuleTypeParameterMultiError(errors)
	}

	return nil
}

// RuleTypeParameterMultiError is an error wrapping multiple validation errors
// returned by RuleTypeParameter.ValidateAll() if the designated constraints
// aren't met.
type RuleTypeParameterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RuleTypeParameterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RuleTypeParameterMultiError) AllErrors() []error { return m }

// RuleTypeParameterValidationError is the validation error returned by
// RuleTypeParameter.Validate if the designated constraints aren't met.
type RuleTypeParameterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RuleTypeParameterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RuleTypeParameterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RuleTypeParameterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RuleTypeParameterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RuleTypeParameterValidationError) ErrorName() string {
	return "RuleTypeParameterValidationError"
}

// Error satisfies the builtin error interface
func (e RuleTypeParameterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRuleTypeParameter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RuleTypeParameterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RuleTypeParameterValidationError{}

// Validate checks the field values on InputParameter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InputParameter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InputParameter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in InputParameterMultiError,
// or nil if none found.
func (m *InputParameter) ValidateAll() error {
	return m.validate(true)
}

func (m *InputParameter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAlerts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InputParameterValidationError{
						field:  fmt.Sprintf("Alerts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InputParameterValidationError{
						field:  fmt.Sprintf("Alerts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InputParameterValidationError{
					field:  fmt.Sprintf("Alerts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetCaseDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InputParameterValidationError{
					field:  "CaseDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InputParameterValidationError{
					field:  "CaseDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCaseDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InputParameterValidationError{
				field:  "CaseDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InputParameterMultiError(errors)
	}

	return nil
}

// InputParameterMultiError is an error wrapping multiple validation errors
// returned by InputParameter.ValidateAll() if the designated constraints
// aren't met.
type InputParameterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InputParameterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InputParameterMultiError) AllErrors() []error { return m }

// InputParameterValidationError is the validation error returned by
// InputParameter.Validate if the designated constraints aren't met.
type InputParameterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InputParameterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InputParameterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InputParameterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InputParameterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InputParameterValidationError) ErrorName() string { return "InputParameterValidationError" }

// Error satisfies the builtin error interface
func (e InputParameterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInputParameter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InputParameterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InputParameterValidationError{}

// Validate checks the field values on PrioritizationModelComputedParameter
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PrioritizationModelComputedParameter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrioritizationModelComputedParameter
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PrioritizationModelComputedParameterMultiError, or nil if none found.
func (m *PrioritizationModelComputedParameter) ValidateAll() error {
	return m.validate(true)
}

func (m *PrioritizationModelComputedParameter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Precision

	// no validation rules for Occurrence

	if len(errors) > 0 {
		return PrioritizationModelComputedParameterMultiError(errors)
	}

	return nil
}

// PrioritizationModelComputedParameterMultiError is an error wrapping multiple
// validation errors returned by
// PrioritizationModelComputedParameter.ValidateAll() if the designated
// constraints aren't met.
type PrioritizationModelComputedParameterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrioritizationModelComputedParameterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrioritizationModelComputedParameterMultiError) AllErrors() []error { return m }

// PrioritizationModelComputedParameterValidationError is the validation error
// returned by PrioritizationModelComputedParameter.Validate if the designated
// constraints aren't met.
type PrioritizationModelComputedParameterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrioritizationModelComputedParameterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrioritizationModelComputedParameterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrioritizationModelComputedParameterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrioritizationModelComputedParameterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrioritizationModelComputedParameterValidationError) ErrorName() string {
	return "PrioritizationModelComputedParameterValidationError"
}

// Error satisfies the builtin error interface
func (e PrioritizationModelComputedParameterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrioritizationModelComputedParameter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrioritizationModelComputedParameterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrioritizationModelComputedParameterValidationError{}
