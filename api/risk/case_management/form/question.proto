//go:generate gen_sql -types=QuestionOptions,MultiChoiceOptions,StoredConditionalQuestions,TextOptions,FileOptions

syntax = "proto3";

package risk.case_management.form;

import "api/risk/case_management/form/enums.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management/form";
option java_package = "com.github.epifi.gamma.api.risk.case_management.form";

// Question stores templates for questions that can be sent to users in questionnaire.
// On question update, a new record will be created with the same code and the version will be increased by 1.
// i.e., question can be uniquely identified by code and version.
message Question {
  string id = 1;

  // Limited length code to identify a question.
  // Remains constant throughout a question's lifecycle.
  string code = 2 [(validate.rules).string.min_len = 1];

  // Versioning on incremental question updates.
  int32 version = 3;

  // Question text
  string text = 4 [(validate.rules).string.min_len = 1];

  // Placeholder for question response.
  string placeholder = 5;

  // Optional help text to be shown with question.
  string tip = 6;

  // Optional description for internal use.
  string description = 7;

  // Specifies type of question for type of response accepted by question.
  // A question type might be multi choice, text or file upload.
  QuestionType type = 8 [(validate.rules).enum = {not_in: [0]}];

  // Optional params varying by question type.
  // It can store validations on response or additional params required for a question type.
  QuestionOptions options = 9;

  google.protobuf.Timestamp created_at = 10;

  google.protobuf.Timestamp updated_at = 11;

  google.protobuf.Timestamp deleted_at = 12;
}


// QuestionEntityMapping is a mapping table between question code and entities.
// Ordering between questions is only defined on entity and entity id level.
message QuestionEntityMapping {
  string id = 1;

  // Mapped question code
  string question_code = 2 [(validate.rules).string.min_len = 1];

  // Entity type of the mapping required
  EntityType entity_type = 3 [(validate.rules).enum = {not_in: [0]}];

  // Entity id of the mapping
  string entity_id = 5 [(validate.rules).string.min_len = 1];

  // Entity level score to order questions.
  int32 entity_order_score = 6 [(validate.rules).int32 = {gte: 0, lte: 100}];

  // Indicates whether a question is mandatory wrt an entity.
  bool is_mandatory = 7;

  google.protobuf.Timestamp created_at = 8;

  google.protobuf.Timestamp updated_at = 9;

  google.protobuf.Timestamp deleted_at = 10;
}


message QuestionOptions {
  oneof Options {
    MultiChoiceOptions multi_choice = 1;

    FileOptions file_options = 2;

    TextOptions text_options = 3;
  }
}

message MultiChoiceOptions {
  // choices in multi choice question type mapped against conditional questions.
  map<string, StoredConditionalQuestions> choice_conditional_questions_map = 1;

  bool is_multi_select = 2;

  // It is intended for deterministic ordering of choices.
  repeated string choices = 3;
}

message FileOptions {
  repeated FileContentType allowed_content_types = 1;
}

message TextOptions {
  // Max characters limit for text response.
  uint32 max_chars_limit = 1;
}

// ExtendedQuestion is enriched with additional info such as conditional questions.
message ExtendedQuestion {
  Question question = 1;

  bool is_mandatory = 2;

  map<string, ConditionalQuestions> conditional_questions = 3;

  message ConditionalQuestions {
    repeated ExtendedQuestion questions = 1;
  }
}

// StoredConditionalQuestions is used to store conditional questions against choices of multi choice questions.
message StoredConditionalQuestions {
  repeated ConditionalQuestion questions = 1 [(validate.rules).repeated.min_items = 1];

  message ConditionalQuestion {
    string question_code = 1 [(validate.rules).string.min_len = 1];
    bool is_mandatory = 2;
  }
}

message QuestionIdentifiers {
  oneof identifiers {
    EntityIdentifiers entity_identifiers = 1;
    QuestionIdentifiers question_identifiers = 2;
  }
}

message EntityIdentifier {
  EntityType type = 1;
  string id = 2;
}

message EntityIdentifiers {
  repeated EntityIdentifier identifiers = 1;
}

message QuestionCodeIdentifiers {
  repeated QuestionCodeIdentifier identifiers = 1;
}

message QuestionCodeIdentifier {
  string code = 1;

  bool is_mandatory = 2;
}
