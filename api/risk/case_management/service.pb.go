// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/service.proto

package case_management

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	order "github.com/epifi/gamma/api/order"
	payment "github.com/epifi/gamma/api/order/payment"
	enums "github.com/epifi/gamma/api/risk/case_management/enums"
	form "github.com/epifi/gamma/api/risk/case_management/form"
	review "github.com/epifi/gamma/api/risk/case_management/review"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AggregateOption defines how data should be aggregated
type AggregateOption int32

const (
	AggregateOption_AGGREGATE_OPTION_UNSPECIFIED      AggregateOption = 0
	AggregateOption_AGGREGATE_OPTION_MONTH_OVER_MONTH AggregateOption = 1
	AggregateOption_AGGREGATE_OPTION_WEEK_OVER_WEEK   AggregateOption = 2
	AggregateOption_AGGREGATE_OPTION_DAY_WISE         AggregateOption = 3
)

// Enum value maps for AggregateOption.
var (
	AggregateOption_name = map[int32]string{
		0: "AGGREGATE_OPTION_UNSPECIFIED",
		1: "AGGREGATE_OPTION_MONTH_OVER_MONTH",
		2: "AGGREGATE_OPTION_WEEK_OVER_WEEK",
		3: "AGGREGATE_OPTION_DAY_WISE",
	}
	AggregateOption_value = map[string]int32{
		"AGGREGATE_OPTION_UNSPECIFIED":      0,
		"AGGREGATE_OPTION_MONTH_OVER_MONTH": 1,
		"AGGREGATE_OPTION_WEEK_OVER_WEEK":   2,
		"AGGREGATE_OPTION_DAY_WISE":         3,
	}
)

func (x AggregateOption) Enum() *AggregateOption {
	p := new(AggregateOption)
	*p = x
	return p
}

func (x AggregateOption) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AggregateOption) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[0].Descriptor()
}

func (AggregateOption) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[0]
}

func (x AggregateOption) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AggregateOption.Descriptor instead.
func (AggregateOption) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{0}
}

type ListAllowedAnnotationsResponse_Status int32

const (
	ListAllowedAnnotationsResponse_OK ListAllowedAnnotationsResponse_Status = 0
	// if request validation fails
	ListAllowedAnnotationsResponse_INVALID_ARGUMENT ListAllowedAnnotationsResponse_Status = 3
	// No annotations found with given filters
	ListAllowedAnnotationsResponse_RECORD_NOT_FOUND ListAllowedAnnotationsResponse_Status = 5
	// System faced internal errors while processing the request
	ListAllowedAnnotationsResponse_INTERNAL ListAllowedAnnotationsResponse_Status = 13
)

// Enum value maps for ListAllowedAnnotationsResponse_Status.
var (
	ListAllowedAnnotationsResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	ListAllowedAnnotationsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x ListAllowedAnnotationsResponse_Status) Enum() *ListAllowedAnnotationsResponse_Status {
	p := new(ListAllowedAnnotationsResponse_Status)
	*p = x
	return p
}

func (x ListAllowedAnnotationsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAllowedAnnotationsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[1].Descriptor()
}

func (ListAllowedAnnotationsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[1]
}

func (x ListAllowedAnnotationsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAllowedAnnotationsResponse_Status.Descriptor instead.
func (ListAllowedAnnotationsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{1, 0}
}

type CreateAnnotationsResponse_Status int32

const (
	CreateAnnotationsResponse_OK CreateAnnotationsResponse_Status = 0
	// if request validation fails
	CreateAnnotationsResponse_INVALID_ARGUMENT CreateAnnotationsResponse_Status = 3
	// System faced internal errors while processing the request
	CreateAnnotationsResponse_INTERNAL CreateAnnotationsResponse_Status = 13
)

// Enum value maps for CreateAnnotationsResponse_Status.
var (
	CreateAnnotationsResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	CreateAnnotationsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x CreateAnnotationsResponse_Status) Enum() *CreateAnnotationsResponse_Status {
	p := new(CreateAnnotationsResponse_Status)
	*p = x
	return p
}

func (x CreateAnnotationsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateAnnotationsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[2].Descriptor()
}

func (CreateAnnotationsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[2]
}

func (x CreateAnnotationsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateAnnotationsResponse_Status.Descriptor instead.
func (CreateAnnotationsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{3, 0}
}

type GetAllTagsResponse_Status int32

const (
	GetAllTagsResponse_Ok GetAllTagsResponse_Status = 0
	// Tags not found
	GetAllTagsResponse_NOT_FOUND GetAllTagsResponse_Status = 5
	// ISE response due to some internal error in the rpc
	GetAllTagsResponse_INTERNAL GetAllTagsResponse_Status = 13
)

// Enum value maps for GetAllTagsResponse_Status.
var (
	GetAllTagsResponse_Status_name = map[int32]string{
		0:  "Ok",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetAllTagsResponse_Status_value = map[string]int32{
		"Ok":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetAllTagsResponse_Status) Enum() *GetAllTagsResponse_Status {
	p := new(GetAllTagsResponse_Status)
	*p = x
	return p
}

func (x GetAllTagsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAllTagsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[3].Descriptor()
}

func (GetAllTagsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[3]
}

func (x GetAllTagsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAllTagsResponse_Status.Descriptor instead.
func (GetAllTagsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{5, 0}
}

type GetPrioritizedCaseResponse_Status int32

const (
	GetPrioritizedCaseResponse_OK GetPrioritizedCaseResponse_Status = 0
	// Invalid argument passed in the request
	GetPrioritizedCaseResponse_INVALID_ARGUMENT GetPrioritizedCaseResponse_Status = 3
	// Case not found for the analyst
	GetPrioritizedCaseResponse_NOT_FOUND GetPrioritizedCaseResponse_Status = 5
	// ISE resposne due to some internal error in the rpc
	GetPrioritizedCaseResponse_INTERNAL_SERVER_ERROR GetPrioritizedCaseResponse_Status = 13
)

// Enum value maps for GetPrioritizedCaseResponse_Status.
var (
	GetPrioritizedCaseResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "NOT_FOUND",
		13: "INTERNAL_SERVER_ERROR",
	}
	GetPrioritizedCaseResponse_Status_value = map[string]int32{
		"OK":                    0,
		"INVALID_ARGUMENT":      3,
		"NOT_FOUND":             5,
		"INTERNAL_SERVER_ERROR": 13,
	}
)

func (x GetPrioritizedCaseResponse_Status) Enum() *GetPrioritizedCaseResponse_Status {
	p := new(GetPrioritizedCaseResponse_Status)
	*p = x
	return p
}

func (x GetPrioritizedCaseResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetPrioritizedCaseResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[4].Descriptor()
}

func (GetPrioritizedCaseResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[4]
}

func (x GetPrioritizedCaseResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetPrioritizedCaseResponse_Status.Descriptor instead.
func (GetPrioritizedCaseResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{7, 0}
}

type UpdateCaseResponse_Status int32

const (
	UpdateCaseResponse_OK UpdateCaseResponse_Status = 0
	// Invalid argument passed in the request
	UpdateCaseResponse_INVALID_ARGUMENT UpdateCaseResponse_Status = 3
	// ISE resposne due to some internal error in the rpc
	UpdateCaseResponse_INTERNAL_SERVER_ERROR UpdateCaseResponse_Status = 13
)

// Enum value maps for UpdateCaseResponse_Status.
var (
	UpdateCaseResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL_SERVER_ERROR",
	}
	UpdateCaseResponse_Status_value = map[string]int32{
		"OK":                    0,
		"INVALID_ARGUMENT":      3,
		"INTERNAL_SERVER_ERROR": 13,
	}
)

func (x UpdateCaseResponse_Status) Enum() *UpdateCaseResponse_Status {
	p := new(UpdateCaseResponse_Status)
	*p = x
	return p
}

func (x UpdateCaseResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateCaseResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[5].Descriptor()
}

func (UpdateCaseResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[5]
}

func (x UpdateCaseResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateCaseResponse_Status.Descriptor instead.
func (UpdateCaseResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{9, 0}
}

type CreateAllowedAnnotationResponse_Status int32

const (
	CreateAllowedAnnotationResponse_OK CreateAllowedAnnotationResponse_Status = 0
	// ISE resposne due to some internal error in the rpc
	CreateAllowedAnnotationResponse_INTERNAL_SERVER_ERROR CreateAllowedAnnotationResponse_Status = 1
	// Invalid argument passed in the request
	CreateAllowedAnnotationResponse_INVALID_ARGUMENT CreateAllowedAnnotationResponse_Status = 2
)

// Enum value maps for CreateAllowedAnnotationResponse_Status.
var (
	CreateAllowedAnnotationResponse_Status_name = map[int32]string{
		0: "OK",
		1: "INTERNAL_SERVER_ERROR",
		2: "INVALID_ARGUMENT",
	}
	CreateAllowedAnnotationResponse_Status_value = map[string]int32{
		"OK":                    0,
		"INTERNAL_SERVER_ERROR": 1,
		"INVALID_ARGUMENT":      2,
	}
)

func (x CreateAllowedAnnotationResponse_Status) Enum() *CreateAllowedAnnotationResponse_Status {
	p := new(CreateAllowedAnnotationResponse_Status)
	*p = x
	return p
}

func (x CreateAllowedAnnotationResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateAllowedAnnotationResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[6].Descriptor()
}

func (CreateAllowedAnnotationResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[6]
}

func (x CreateAllowedAnnotationResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateAllowedAnnotationResponse_Status.Descriptor instead.
func (CreateAllowedAnnotationResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{11, 0}
}

type GetTransactionDetailsForReviewResponse_Status int32

const (
	GetTransactionDetailsForReviewResponse_OK GetTransactionDetailsForReviewResponse_Status = 0
	// ISE resposne due to some internal error in the rpc
	GetTransactionDetailsForReviewResponse_INTERNAL_SERVER_ERROR GetTransactionDetailsForReviewResponse_Status = 1
	// Record not found for the given identifier
	GetTransactionDetailsForReviewResponse_RECORD_NOT_FOUND GetTransactionDetailsForReviewResponse_Status = 2
	// Invalid argument passed in the request
	GetTransactionDetailsForReviewResponse_INVALID_ARGUMENT GetTransactionDetailsForReviewResponse_Status = 3
)

// Enum value maps for GetTransactionDetailsForReviewResponse_Status.
var (
	GetTransactionDetailsForReviewResponse_Status_name = map[int32]string{
		0: "OK",
		1: "INTERNAL_SERVER_ERROR",
		2: "RECORD_NOT_FOUND",
		3: "INVALID_ARGUMENT",
	}
	GetTransactionDetailsForReviewResponse_Status_value = map[string]int32{
		"OK":                    0,
		"INTERNAL_SERVER_ERROR": 1,
		"RECORD_NOT_FOUND":      2,
		"INVALID_ARGUMENT":      3,
	}
)

func (x GetTransactionDetailsForReviewResponse_Status) Enum() *GetTransactionDetailsForReviewResponse_Status {
	p := new(GetTransactionDetailsForReviewResponse_Status)
	*p = x
	return p
}

func (x GetTransactionDetailsForReviewResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetTransactionDetailsForReviewResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[7].Descriptor()
}

func (GetTransactionDetailsForReviewResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[7]
}

func (x GetTransactionDetailsForReviewResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetTransactionDetailsForReviewResponse_Status.Descriptor instead.
func (GetTransactionDetailsForReviewResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{13, 0}
}

type ListCasesResponse_Status int32

const (
	ListCasesResponse_OK ListCasesResponse_Status = 0
	// System faced internal errors while processing the request
	ListCasesResponse_INTERNAL ListCasesResponse_Status = 13
	// No cases found with given filters
	ListCasesResponse_RECORD_NOT_FOUND ListCasesResponse_Status = 5
)

// Enum value maps for ListCasesResponse_Status.
var (
	ListCasesResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		5:  "RECORD_NOT_FOUND",
	}
	ListCasesResponse_Status_value = map[string]int32{
		"OK":               0,
		"INTERNAL":         13,
		"RECORD_NOT_FOUND": 5,
	}
)

func (x ListCasesResponse_Status) Enum() *ListCasesResponse_Status {
	p := new(ListCasesResponse_Status)
	*p = x
	return p
}

func (x ListCasesResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCasesResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[8].Descriptor()
}

func (ListCasesResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[8]
}

func (x ListCasesResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCasesResponse_Status.Descriptor instead.
func (ListCasesResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{17, 0}
}

type CreateAlertsResponse_Status int32

const (
	CreateAlertsResponse_OK CreateAlertsResponse_Status = 0
	// System faced internal errors while processing the request
	CreateAlertsResponse_INTERNAL CreateAlertsResponse_Status = 13
	// if request validation fails
	CreateAlertsResponse_INVALID_ARGUMENT CreateAlertsResponse_Status = 3
)

// Enum value maps for CreateAlertsResponse_Status.
var (
	CreateAlertsResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		3:  "INVALID_ARGUMENT",
	}
	CreateAlertsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INTERNAL":         13,
		"INVALID_ARGUMENT": 3,
	}
)

func (x CreateAlertsResponse_Status) Enum() *CreateAlertsResponse_Status {
	p := new(CreateAlertsResponse_Status)
	*p = x
	return p
}

func (x CreateAlertsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateAlertsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[9].Descriptor()
}

func (CreateAlertsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[9]
}

func (x CreateAlertsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateAlertsResponse_Status.Descriptor instead.
func (CreateAlertsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{19, 0}
}

type GetReviewDetailsResponse_Status int32

const (
	GetReviewDetailsResponse_OK GetReviewDetailsResponse_Status = 0
	// System faced internal errors while processing the request
	GetReviewDetailsResponse_INTERNAL GetReviewDetailsResponse_Status = 13
	// if request validation fails
	GetReviewDetailsResponse_INVALID_ARGUMENT GetReviewDetailsResponse_Status = 3
)

// Enum value maps for GetReviewDetailsResponse_Status.
var (
	GetReviewDetailsResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		3:  "INVALID_ARGUMENT",
	}
	GetReviewDetailsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INTERNAL":         13,
		"INVALID_ARGUMENT": 3,
	}
)

func (x GetReviewDetailsResponse_Status) Enum() *GetReviewDetailsResponse_Status {
	p := new(GetReviewDetailsResponse_Status)
	*p = x
	return p
}

func (x GetReviewDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetReviewDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[10].Descriptor()
}

func (GetReviewDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[10]
}

func (x GetReviewDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetReviewDetailsResponse_Status.Descriptor instead.
func (GetReviewDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{22, 0}
}

type GetLinkedAlertsResponse_Status int32

const (
	GetLinkedAlertsResponse_OK GetLinkedAlertsResponse_Status = 0
	// System faced internal errors while processing the request
	GetLinkedAlertsResponse_INTERNAL GetLinkedAlertsResponse_Status = 13
	// No alerts are found with given filters
	GetLinkedAlertsResponse_RECORD_NOT_FOUND GetLinkedAlertsResponse_Status = 5
)

// Enum value maps for GetLinkedAlertsResponse_Status.
var (
	GetLinkedAlertsResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		5:  "RECORD_NOT_FOUND",
	}
	GetLinkedAlertsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INTERNAL":         13,
		"RECORD_NOT_FOUND": 5,
	}
)

func (x GetLinkedAlertsResponse_Status) Enum() *GetLinkedAlertsResponse_Status {
	p := new(GetLinkedAlertsResponse_Status)
	*p = x
	return p
}

func (x GetLinkedAlertsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetLinkedAlertsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[11].Descriptor()
}

func (GetLinkedAlertsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[11]
}

func (x GetLinkedAlertsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetLinkedAlertsResponse_Status.Descriptor instead.
func (GetLinkedAlertsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{24, 0}
}

type PerformActionResponse_Status int32

const (
	PerformActionResponse_OK PerformActionResponse_Status = 0
	// System faced internal errors while processing the request
	PerformActionResponse_INTERNAL PerformActionResponse_Status = 13
	// if request validation fails
	PerformActionResponse_INVALID_ARGUMENT PerformActionResponse_Status = 3
)

// Enum value maps for PerformActionResponse_Status.
var (
	PerformActionResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		3:  "INVALID_ARGUMENT",
	}
	PerformActionResponse_Status_value = map[string]int32{
		"OK":               0,
		"INTERNAL":         13,
		"INVALID_ARGUMENT": 3,
	}
)

func (x PerformActionResponse_Status) Enum() *PerformActionResponse_Status {
	p := new(PerformActionResponse_Status)
	*p = x
	return p
}

func (x PerformActionResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PerformActionResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[12].Descriptor()
}

func (PerformActionResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[12]
}

func (x PerformActionResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PerformActionResponse_Status.Descriptor instead.
func (PerformActionResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{26, 0}
}

type CreateCommentResponse_Status int32

const (
	CreateCommentResponse_OK CreateCommentResponse_Status = 0
	// if request validation fails
	CreateCommentResponse_INVALID_ARGUMENT CreateCommentResponse_Status = 3
	// System faced internal errors while processing the request
	CreateCommentResponse_INTERNAL CreateCommentResponse_Status = 13
)

// Enum value maps for CreateCommentResponse_Status.
var (
	CreateCommentResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	CreateCommentResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x CreateCommentResponse_Status) Enum() *CreateCommentResponse_Status {
	p := new(CreateCommentResponse_Status)
	*p = x
	return p
}

func (x CreateCommentResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateCommentResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[13].Descriptor()
}

func (CreateCommentResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[13]
}

func (x CreateCommentResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateCommentResponse_Status.Descriptor instead.
func (CreateCommentResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{28, 0}
}

type ListCommentsResponse_Status int32

const (
	ListCommentsResponse_OK ListCommentsResponse_Status = 0
	// if request validation fails
	ListCommentsResponse_INVALID_ARGUMENT ListCommentsResponse_Status = 3
	// Record not found for the given identifier
	ListCommentsResponse_RECORD_NOT_FOUND ListCommentsResponse_Status = 5
	// System faced internal errors while processing the request
	ListCommentsResponse_INTERNAL ListCommentsResponse_Status = 13
)

// Enum value maps for ListCommentsResponse_Status.
var (
	ListCommentsResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	ListCommentsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x ListCommentsResponse_Status) Enum() *ListCommentsResponse_Status {
	p := new(ListCommentsResponse_Status)
	*p = x
	return p
}

func (x ListCommentsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCommentsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[14].Descriptor()
}

func (ListCommentsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[14]
}

func (x ListCommentsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCommentsResponse_Status.Descriptor instead.
func (ListCommentsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{30, 0}
}

type GetAllowedAnnotationsResponse_Status int32

const (
	GetAllowedAnnotationsResponse_OK GetAllowedAnnotationsResponse_Status = 0
	// if request validation fails
	GetAllowedAnnotationsResponse_INVALID_ARGUMENT GetAllowedAnnotationsResponse_Status = 3
	// No cases found with given filters
	GetAllowedAnnotationsResponse_RECORD_NOT_FOUND GetAllowedAnnotationsResponse_Status = 5
	// System faced internal errors while processing the request
	GetAllowedAnnotationsResponse_INTERNAL GetAllowedAnnotationsResponse_Status = 13
)

// Enum value maps for GetAllowedAnnotationsResponse_Status.
var (
	GetAllowedAnnotationsResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetAllowedAnnotationsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetAllowedAnnotationsResponse_Status) Enum() *GetAllowedAnnotationsResponse_Status {
	p := new(GetAllowedAnnotationsResponse_Status)
	*p = x
	return p
}

func (x GetAllowedAnnotationsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAllowedAnnotationsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[15].Descriptor()
}

func (GetAllowedAnnotationsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[15]
}

func (x GetAllowedAnnotationsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAllowedAnnotationsResponse_Status.Descriptor instead.
func (GetAllowedAnnotationsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{32, 0}
}

type CreateAnnotationResponse_Status int32

const (
	CreateAnnotationResponse_OK CreateAnnotationResponse_Status = 0
	// System faced internal errors while processing the request
	CreateAnnotationResponse_INTERNAL CreateAnnotationResponse_Status = 13
	// if request validation fails
	CreateAnnotationResponse_INVALID_ARGUMENT CreateAnnotationResponse_Status = 3
)

// Enum value maps for CreateAnnotationResponse_Status.
var (
	CreateAnnotationResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		3:  "INVALID_ARGUMENT",
	}
	CreateAnnotationResponse_Status_value = map[string]int32{
		"OK":               0,
		"INTERNAL":         13,
		"INVALID_ARGUMENT": 3,
	}
)

func (x CreateAnnotationResponse_Status) Enum() *CreateAnnotationResponse_Status {
	p := new(CreateAnnotationResponse_Status)
	*p = x
	return p
}

func (x CreateAnnotationResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateAnnotationResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[16].Descriptor()
}

func (CreateAnnotationResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[16]
}

func (x CreateAnnotationResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateAnnotationResponse_Status.Descriptor instead.
func (CreateAnnotationResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{34, 0}
}

type ListAnnotationsResponse_Status int32

const (
	ListAnnotationsResponse_OK ListAnnotationsResponse_Status = 0
	// System faced internal errors while processing the request
	ListAnnotationsResponse_INTERNAL ListAnnotationsResponse_Status = 13
	// if request validation fails
	ListAnnotationsResponse_INVALID_ARGUMENT ListAnnotationsResponse_Status = 3
	// No annotations are found with given filters
	ListAnnotationsResponse_RECORD_NOT_FOUND ListAnnotationsResponse_Status = 5
)

// Enum value maps for ListAnnotationsResponse_Status.
var (
	ListAnnotationsResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
	}
	ListAnnotationsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INTERNAL":         13,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
	}
)

func (x ListAnnotationsResponse_Status) Enum() *ListAnnotationsResponse_Status {
	p := new(ListAnnotationsResponse_Status)
	*p = x
	return p
}

func (x ListAnnotationsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAnnotationsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[17].Descriptor()
}

func (ListAnnotationsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[17]
}

func (x ListAnnotationsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAnnotationsResponse_Status.Descriptor instead.
func (ListAnnotationsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{36, 0}
}

type CreateRuleResponse_Status int32

const (
	CreateRuleResponse_OK CreateRuleResponse_Status = 0
	// if request validation fails
	CreateRuleResponse_INVALID_ARGUMENT CreateRuleResponse_Status = 3
	// System faced internal errors while processing the request
	CreateRuleResponse_INTERNAL CreateRuleResponse_Status = 13
)

// Enum value maps for CreateRuleResponse_Status.
var (
	CreateRuleResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	CreateRuleResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x CreateRuleResponse_Status) Enum() *CreateRuleResponse_Status {
	p := new(CreateRuleResponse_Status)
	*p = x
	return p
}

func (x CreateRuleResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateRuleResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[18].Descriptor()
}

func (CreateRuleResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[18]
}

func (x CreateRuleResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateRuleResponse_Status.Descriptor instead.
func (CreateRuleResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{38, 0}
}

type ListRulesResponse_Status int32

const (
	ListRulesResponse_OK ListRulesResponse_Status = 0
	// if request validation fails
	ListRulesResponse_INVALID_ARGUMENT ListRulesResponse_Status = 3
	// No cases found with given filters
	ListRulesResponse_RECORD_NOT_FOUND ListRulesResponse_Status = 5
	// System faced internal errors while processing the request
	ListRulesResponse_INTERNAL ListRulesResponse_Status = 13
)

// Enum value maps for ListRulesResponse_Status.
var (
	ListRulesResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	ListRulesResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x ListRulesResponse_Status) Enum() *ListRulesResponse_Status {
	p := new(ListRulesResponse_Status)
	*p = x
	return p
}

func (x ListRulesResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListRulesResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[19].Descriptor()
}

func (ListRulesResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[19]
}

func (x ListRulesResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListRulesResponse_Status.Descriptor instead.
func (ListRulesResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{40, 0}
}

type UpdateRuleResponse_Status int32

const (
	UpdateRuleResponse_OK UpdateRuleResponse_Status = 0
	// Invalid argument passed in the request
	UpdateRuleResponse_INVALID_ARGUMENT UpdateRuleResponse_Status = 3
	// System faced internal errors while processing the request
	UpdateRuleResponse_INTERNAL UpdateRuleResponse_Status = 13
)

// Enum value maps for UpdateRuleResponse_Status.
var (
	UpdateRuleResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	UpdateRuleResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x UpdateRuleResponse_Status) Enum() *UpdateRuleResponse_Status {
	p := new(UpdateRuleResponse_Status)
	*p = x
	return p
}

func (x UpdateRuleResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateRuleResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[20].Descriptor()
}

func (UpdateRuleResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[20]
}

func (x UpdateRuleResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateRuleResponse_Status.Descriptor instead.
func (UpdateRuleResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{42, 0}
}

type ListSortedCasesResponse_Status int32

const (
	ListSortedCasesResponse_OK ListSortedCasesResponse_Status = 0
	// System faced internal errors while processing the request
	ListSortedCasesResponse_INTERNAL ListSortedCasesResponse_Status = 13
	// No cases found with given filters
	ListSortedCasesResponse_RECORD_NOT_FOUND ListSortedCasesResponse_Status = 5
)

// Enum value maps for ListSortedCasesResponse_Status.
var (
	ListSortedCasesResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		5:  "RECORD_NOT_FOUND",
	}
	ListSortedCasesResponse_Status_value = map[string]int32{
		"OK":               0,
		"INTERNAL":         13,
		"RECORD_NOT_FOUND": 5,
	}
)

func (x ListSortedCasesResponse_Status) Enum() *ListSortedCasesResponse_Status {
	p := new(ListSortedCasesResponse_Status)
	*p = x
	return p
}

func (x ListSortedCasesResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListSortedCasesResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[21].Descriptor()
}

func (ListSortedCasesResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[21]
}

func (x ListSortedCasesResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListSortedCasesResponse_Status.Descriptor instead.
func (ListSortedCasesResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{44, 0}
}

type GetFiUserRelationshipResponse_Status int32

const (
	GetFiUserRelationshipResponse_OK GetFiUserRelationshipResponse_Status = 0
	// Invalid argument passed in the request
	GetFiUserRelationshipResponse_INVALID_ARGUMENT GetFiUserRelationshipResponse_Status = 3
	// No information found for the actor
	GetFiUserRelationshipResponse_RECORD_NOT_FOUND GetFiUserRelationshipResponse_Status = 5
	// System faced internal errors while processing the request
	GetFiUserRelationshipResponse_INTERNAL GetFiUserRelationshipResponse_Status = 13
)

// Enum value maps for GetFiUserRelationshipResponse_Status.
var (
	GetFiUserRelationshipResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetFiUserRelationshipResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetFiUserRelationshipResponse_Status) Enum() *GetFiUserRelationshipResponse_Status {
	p := new(GetFiUserRelationshipResponse_Status)
	*p = x
	return p
}

func (x GetFiUserRelationshipResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetFiUserRelationshipResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[22].Descriptor()
}

func (GetFiUserRelationshipResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[22]
}

func (x GetFiUserRelationshipResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetFiUserRelationshipResponse_Status.Descriptor instead.
func (GetFiUserRelationshipResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{46, 0}
}

type GetFormResponse_Status int32

const (
	// Success
	GetFormResponse_OK GetFormResponse_Status = 0
	// Form does not exist for given id.
	GetFormResponse_NOT_FOUND GetFormResponse_Status = 5
	// Internal error
	GetFormResponse_INTERNAL GetFormResponse_Status = 13
)

// Enum value maps for GetFormResponse_Status.
var (
	GetFormResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetFormResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetFormResponse_Status) Enum() *GetFormResponse_Status {
	p := new(GetFormResponse_Status)
	*p = x
	return p
}

func (x GetFormResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetFormResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[23].Descriptor()
}

func (GetFormResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[23]
}

func (x GetFormResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetFormResponse_Status.Descriptor instead.
func (GetFormResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{48, 0}
}

type SubmitFormResponse_Status int32

const (
	// Success
	SubmitFormResponse_OK SubmitFormResponse_Status = 0
	// Invalid argument passed in the request
	SubmitFormResponse_INVALID_ARGUMENT SubmitFormResponse_Status = 3
	// Form does not exist for given id.
	SubmitFormResponse_NOT_FOUND SubmitFormResponse_Status = 5
	// Internal error
	SubmitFormResponse_INTERNAL SubmitFormResponse_Status = 13
	// Form has expired and can no longer be submitted.
	SubmitFormResponse_EXPIRED SubmitFormResponse_Status = 100
	// Form is already submitted and can't be resubmitted.
	SubmitFormResponse_ALREADY_SUBMITTED SubmitFormResponse_Status = 101
)

// Enum value maps for SubmitFormResponse_Status.
var (
	SubmitFormResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		5:   "NOT_FOUND",
		13:  "INTERNAL",
		100: "EXPIRED",
		101: "ALREADY_SUBMITTED",
	}
	SubmitFormResponse_Status_value = map[string]int32{
		"OK":                0,
		"INVALID_ARGUMENT":  3,
		"NOT_FOUND":         5,
		"INTERNAL":          13,
		"EXPIRED":           100,
		"ALREADY_SUBMITTED": 101,
	}
)

func (x SubmitFormResponse_Status) Enum() *SubmitFormResponse_Status {
	p := new(SubmitFormResponse_Status)
	*p = x
	return p
}

func (x SubmitFormResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubmitFormResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[24].Descriptor()
}

func (SubmitFormResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[24]
}

func (x SubmitFormResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubmitFormResponse_Status.Descriptor instead.
func (SubmitFormResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{50, 0}
}

type ListFormsResponse_Status int32

const (
	ListFormsResponse_OK ListFormsResponse_Status = 0
	// Invalid argument passed in the request
	ListFormsResponse_INVALID_ARGUMENT ListFormsResponse_Status = 3
	// No matching form found
	ListFormsResponse_NOT_FOUND ListFormsResponse_Status = 5
	// System faced internal errors while processing the request
	ListFormsResponse_INTERNAL ListFormsResponse_Status = 13
)

// Enum value maps for ListFormsResponse_Status.
var (
	ListFormsResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	ListFormsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"NOT_FOUND":        5,
		"INTERNAL":         13,
	}
)

func (x ListFormsResponse_Status) Enum() *ListFormsResponse_Status {
	p := new(ListFormsResponse_Status)
	*p = x
	return p
}

func (x ListFormsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListFormsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[25].Descriptor()
}

func (ListFormsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[25]
}

func (x ListFormsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListFormsResponse_Status.Descriptor instead.
func (ListFormsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{52, 0}
}

type GetAlertsResponse_Status int32

const (
	GetAlertsResponse_OK GetAlertsResponse_Status = 0
	// Invalid argument passed in the request
	GetAlertsResponse_INVALID_ARGUMENT GetAlertsResponse_Status = 3
	// No alerts found
	GetAlertsResponse_NOT_FOUND GetAlertsResponse_Status = 5
	// System faced internal errors while processing the request
	GetAlertsResponse_INTERNAL GetAlertsResponse_Status = 13
)

// Enum value maps for GetAlertsResponse_Status.
var (
	GetAlertsResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetAlertsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"NOT_FOUND":        5,
		"INTERNAL":         13,
	}
)

func (x GetAlertsResponse_Status) Enum() *GetAlertsResponse_Status {
	p := new(GetAlertsResponse_Status)
	*p = x
	return p
}

func (x GetAlertsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAlertsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[26].Descriptor()
}

func (GetAlertsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[26]
}

func (x GetAlertsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAlertsResponse_Status.Descriptor instead.
func (GetAlertsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{54, 0}
}

type GetFormsForActorResponse_Status int32

const (
	// Success
	GetFormsForActorResponse_OK GetFormsForActorResponse_Status = 0
	// No forms exist for given actor
	GetFormsForActorResponse_NOT_FOUND GetFormsForActorResponse_Status = 5
	// Internal error
	GetFormsForActorResponse_INTERNAL GetFormsForActorResponse_Status = 13
)

// Enum value maps for GetFormsForActorResponse_Status.
var (
	GetFormsForActorResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetFormsForActorResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetFormsForActorResponse_Status) Enum() *GetFormsForActorResponse_Status {
	p := new(GetFormsForActorResponse_Status)
	*p = x
	return p
}

func (x GetFormsForActorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetFormsForActorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[27].Descriptor()
}

func (GetFormsForActorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[27]
}

func (x GetFormsForActorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetFormsForActorResponse_Status.Descriptor instead.
func (GetFormsForActorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{56, 0}
}

type CreateSuggestedActionForRuleResponse_Status int32

const (
	CreateSuggestedActionForRuleResponse_OK               CreateSuggestedActionForRuleResponse_Status = 0
	CreateSuggestedActionForRuleResponse_NOT_FOUND        CreateSuggestedActionForRuleResponse_Status = 5
	CreateSuggestedActionForRuleResponse_INVALID_ARGUMENT CreateSuggestedActionForRuleResponse_Status = 3
	CreateSuggestedActionForRuleResponse_INTERNAL         CreateSuggestedActionForRuleResponse_Status = 13
)

// Enum value maps for CreateSuggestedActionForRuleResponse_Status.
var (
	CreateSuggestedActionForRuleResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	CreateSuggestedActionForRuleResponse_Status_value = map[string]int32{
		"OK":               0,
		"NOT_FOUND":        5,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x CreateSuggestedActionForRuleResponse_Status) Enum() *CreateSuggestedActionForRuleResponse_Status {
	p := new(CreateSuggestedActionForRuleResponse_Status)
	*p = x
	return p
}

func (x CreateSuggestedActionForRuleResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateSuggestedActionForRuleResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[28].Descriptor()
}

func (CreateSuggestedActionForRuleResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[28]
}

func (x CreateSuggestedActionForRuleResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateSuggestedActionForRuleResponse_Status.Descriptor instead.
func (CreateSuggestedActionForRuleResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{58, 0}
}

type CreateRuleReviewTypeMappingResponse_Status int32

const (
	CreateRuleReviewTypeMappingResponse_OK               CreateRuleReviewTypeMappingResponse_Status = 0
	CreateRuleReviewTypeMappingResponse_NOT_FOUND        CreateRuleReviewTypeMappingResponse_Status = 5
	CreateRuleReviewTypeMappingResponse_INVALID_ARGUMENT CreateRuleReviewTypeMappingResponse_Status = 3
	CreateRuleReviewTypeMappingResponse_INTERNAL         CreateRuleReviewTypeMappingResponse_Status = 13
)

// Enum value maps for CreateRuleReviewTypeMappingResponse_Status.
var (
	CreateRuleReviewTypeMappingResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	CreateRuleReviewTypeMappingResponse_Status_value = map[string]int32{
		"OK":               0,
		"NOT_FOUND":        5,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x CreateRuleReviewTypeMappingResponse_Status) Enum() *CreateRuleReviewTypeMappingResponse_Status {
	p := new(CreateRuleReviewTypeMappingResponse_Status)
	*p = x
	return p
}

func (x CreateRuleReviewTypeMappingResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateRuleReviewTypeMappingResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[29].Descriptor()
}

func (CreateRuleReviewTypeMappingResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[29]
}

func (x CreateRuleReviewTypeMappingResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateRuleReviewTypeMappingResponse_Status.Descriptor instead.
func (CreateRuleReviewTypeMappingResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{60, 0}
}

type GetTransactionBlocksResponse_Status int32

const (
	GetTransactionBlocksResponse_OK GetTransactionBlocksResponse_Status = 0
	// Invalid argument passed in the request
	GetTransactionBlocksResponse_INVALID_ARGUMENT GetTransactionBlocksResponse_Status = 3
	// No transaction blocks found with given filters
	GetTransactionBlocksResponse_RECORD_NOT_FOUND GetTransactionBlocksResponse_Status = 5
	// System faced internal errors while processing the request
	GetTransactionBlocksResponse_INTERNAL GetTransactionBlocksResponse_Status = 13
)

// Enum value maps for GetTransactionBlocksResponse_Status.
var (
	GetTransactionBlocksResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetTransactionBlocksResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetTransactionBlocksResponse_Status) Enum() *GetTransactionBlocksResponse_Status {
	p := new(GetTransactionBlocksResponse_Status)
	*p = x
	return p
}

func (x GetTransactionBlocksResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetTransactionBlocksResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[30].Descriptor()
}

func (GetTransactionBlocksResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[30]
}

func (x GetTransactionBlocksResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetTransactionBlocksResponse_Status.Descriptor instead.
func (GetTransactionBlocksResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{62, 0}
}

type CreateTransactionBlockResponse_Status int32

const (
	CreateTransactionBlockResponse_OK CreateTransactionBlockResponse_Status = 0
	// Invalid argument passed in the request
	CreateTransactionBlockResponse_INVALID_ARGUMENT CreateTransactionBlockResponse_Status = 3
	// System faced internal errors while processing the request
	CreateTransactionBlockResponse_INTERNAL CreateTransactionBlockResponse_Status = 13
)

// Enum value maps for CreateTransactionBlockResponse_Status.
var (
	CreateTransactionBlockResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	CreateTransactionBlockResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x CreateTransactionBlockResponse_Status) Enum() *CreateTransactionBlockResponse_Status {
	p := new(CreateTransactionBlockResponse_Status)
	*p = x
	return p
}

func (x CreateTransactionBlockResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateTransactionBlockResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_service_proto_enumTypes[31].Descriptor()
}

func (CreateTransactionBlockResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_service_proto_enumTypes[31]
}

func (x CreateTransactionBlockResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateTransactionBlockResponse_Status.Descriptor instead.
func (CreateTransactionBlockResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{64, 0}
}

type ListAllowedAnnotationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filters *review.AllowedAnnotationFilters `protobuf:"bytes,1,opt,name=filters,proto3" json:"filters,omitempty"`
}

func (x *ListAllowedAnnotationsRequest) Reset() {
	*x = ListAllowedAnnotationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAllowedAnnotationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAllowedAnnotationsRequest) ProtoMessage() {}

func (x *ListAllowedAnnotationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAllowedAnnotationsRequest.ProtoReflect.Descriptor instead.
func (*ListAllowedAnnotationsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListAllowedAnnotationsRequest) GetFilters() *review.AllowedAnnotationFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

type ListAllowedAnnotationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Types that are assignable to Response:
	//
	//	*ListAllowedAnnotationsResponse_UiElementAllowedAnnotations
	Response isListAllowedAnnotationsResponse_Response `protobuf_oneof:"response"`
}

func (x *ListAllowedAnnotationsResponse) Reset() {
	*x = ListAllowedAnnotationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAllowedAnnotationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAllowedAnnotationsResponse) ProtoMessage() {}

func (x *ListAllowedAnnotationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAllowedAnnotationsResponse.ProtoReflect.Descriptor instead.
func (*ListAllowedAnnotationsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListAllowedAnnotationsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (m *ListAllowedAnnotationsResponse) GetResponse() isListAllowedAnnotationsResponse_Response {
	if m != nil {
		return m.Response
	}
	return nil
}

func (x *ListAllowedAnnotationsResponse) GetUiElementAllowedAnnotations() *review.UIElementAllowedAnnotations {
	if x, ok := x.GetResponse().(*ListAllowedAnnotationsResponse_UiElementAllowedAnnotations); ok {
		return x.UiElementAllowedAnnotations
	}
	return nil
}

type isListAllowedAnnotationsResponse_Response interface {
	isListAllowedAnnotationsResponse_Response()
}

type ListAllowedAnnotationsResponse_UiElementAllowedAnnotations struct {
	// For filter: UI Element. List of annotation types mapped against ui element with annotation values.
	UiElementAllowedAnnotations *review.UIElementAllowedAnnotations `protobuf:"bytes,2,opt,name=ui_element_allowed_annotations,json=uiElementAllowedAnnotations,proto3,oneof"`
}

func (*ListAllowedAnnotationsResponse_UiElementAllowedAnnotations) isListAllowedAnnotationsResponse_Response() {
}

type CreateAnnotationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Annotations []*review.Annotation `protobuf:"bytes,1,rep,name=annotations,proto3" json:"annotations,omitempty"`
}

func (x *CreateAnnotationsRequest) Reset() {
	*x = CreateAnnotationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAnnotationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAnnotationsRequest) ProtoMessage() {}

func (x *CreateAnnotationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAnnotationsRequest.ProtoReflect.Descriptor instead.
func (*CreateAnnotationsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateAnnotationsRequest) GetAnnotations() []*review.Annotation {
	if x != nil {
		return x.Annotations
	}
	return nil
}

type CreateAnnotationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateAnnotationsResponse) Reset() {
	*x = CreateAnnotationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAnnotationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAnnotationsResponse) ProtoMessage() {}

func (x *CreateAnnotationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAnnotationsResponse.ProtoReflect.Descriptor instead.
func (*CreateAnnotationsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreateAnnotationsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetAllTagsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetAllTagsRequest) Reset() {
	*x = GetAllTagsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllTagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllTagsRequest) ProtoMessage() {}

func (x *GetAllTagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllTagsRequest.ProtoReflect.Descriptor instead.
func (*GetAllTagsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{4}
}

type GetAllTagsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Tags   []string    `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *GetAllTagsResponse) Reset() {
	*x = GetAllTagsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllTagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllTagsResponse) ProtoMessage() {}

func (x *GetAllTagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllTagsResponse.ProtoReflect.Descriptor instead.
func (*GetAllTagsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetAllTagsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAllTagsResponse) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

type GetPrioritizedCaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Analyst email is required to fetch analyst groups and infer queue from group.
	AnalystEmail string `protobuf:"bytes,1,opt,name=analyst_email,json=analystEmail,proto3" json:"analyst_email,omitempty"`
	// deprecated, use review_types going forward
	//
	// Deprecated: Marked as deprecated in api/risk/case_management/service.proto.
	ReviewType  review.ReviewType   `protobuf:"varint,2,opt,name=review_type,json=reviewType,proto3,enum=risk.case_management.review.ReviewType" json:"review_type,omitempty"`
	ReviewTypes []review.ReviewType `protobuf:"varint,3,rep,packed,name=review_types,json=reviewTypes,proto3,enum=risk.case_management.review.ReviewType" json:"review_types,omitempty"`
}

func (x *GetPrioritizedCaseRequest) Reset() {
	*x = GetPrioritizedCaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPrioritizedCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrioritizedCaseRequest) ProtoMessage() {}

func (x *GetPrioritizedCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrioritizedCaseRequest.ProtoReflect.Descriptor instead.
func (*GetPrioritizedCaseRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetPrioritizedCaseRequest) GetAnalystEmail() string {
	if x != nil {
		return x.AnalystEmail
	}
	return ""
}

// Deprecated: Marked as deprecated in api/risk/case_management/service.proto.
func (x *GetPrioritizedCaseRequest) GetReviewType() review.ReviewType {
	if x != nil {
		return x.ReviewType
	}
	return review.ReviewType(0)
}

func (x *GetPrioritizedCaseRequest) GetReviewTypes() []review.ReviewType {
	if x != nil {
		return x.ReviewTypes
	}
	return nil
}

type GetPrioritizedCaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Case   *review.Case `protobuf:"bytes,2,opt,name=case,proto3" json:"case,omitempty"`
}

func (x *GetPrioritizedCaseResponse) Reset() {
	*x = GetPrioritizedCaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPrioritizedCaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrioritizedCaseResponse) ProtoMessage() {}

func (x *GetPrioritizedCaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrioritizedCaseResponse.ProtoReflect.Descriptor instead.
func (*GetPrioritizedCaseResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetPrioritizedCaseResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPrioritizedCaseResponse) GetCase() *review.Case {
	if x != nil {
		return x.Case
	}
	return nil
}

type UpdateCaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// case details which needs to be updated. Case id is mandatory.
	Case *review.Case `protobuf:"bytes,1,opt,name=case,proto3" json:"case,omitempty"`
	// If empty field mask is passed all fields will be updated
	UpdateMasks []review.CaseFieldMask `protobuf:"varint,2,rep,packed,name=update_masks,json=updateMasks,proto3,enum=risk.case_management.review.CaseFieldMask" json:"update_masks,omitempty"`
}

func (x *UpdateCaseRequest) Reset() {
	*x = UpdateCaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCaseRequest) ProtoMessage() {}

func (x *UpdateCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCaseRequest.ProtoReflect.Descriptor instead.
func (*UpdateCaseRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateCaseRequest) GetCase() *review.Case {
	if x != nil {
		return x.Case
	}
	return nil
}

func (x *UpdateCaseRequest) GetUpdateMasks() []review.CaseFieldMask {
	if x != nil {
		return x.UpdateMasks
	}
	return nil
}

type UpdateCaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Case   *review.Case `protobuf:"bytes,2,opt,name=case,proto3" json:"case,omitempty"`
}

func (x *UpdateCaseResponse) Reset() {
	*x = UpdateCaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCaseResponse) ProtoMessage() {}

func (x *UpdateCaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCaseResponse.ProtoReflect.Descriptor instead.
func (*UpdateCaseResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateCaseResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateCaseResponse) GetCase() *review.Case {
	if x != nil {
		return x.Case
	}
	return nil
}

type CreateAllowedAnnotationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// annotation that needs to be created in the db
	AllowedAnnotation *review.AllowedAnnotation `protobuf:"bytes,1,opt,name=allowed_annotation,json=allowedAnnotation,proto3" json:"allowed_annotation,omitempty"`
}

func (x *CreateAllowedAnnotationRequest) Reset() {
	*x = CreateAllowedAnnotationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAllowedAnnotationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAllowedAnnotationRequest) ProtoMessage() {}

func (x *CreateAllowedAnnotationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAllowedAnnotationRequest.ProtoReflect.Descriptor instead.
func (*CreateAllowedAnnotationRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{10}
}

func (x *CreateAllowedAnnotationRequest) GetAllowedAnnotation() *review.AllowedAnnotation {
	if x != nil {
		return x.AllowedAnnotation
	}
	return nil
}

type CreateAllowedAnnotationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateAllowedAnnotationResponse) Reset() {
	*x = CreateAllowedAnnotationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAllowedAnnotationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAllowedAnnotationResponse) ProtoMessage() {}

func (x *CreateAllowedAnnotationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAllowedAnnotationResponse.ProtoReflect.Descriptor instead.
func (*CreateAllowedAnnotationResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{11}
}

func (x *CreateAllowedAnnotationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetTransactionDetailsForReviewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaseId string `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// field by which transactions needs to be sorted
	SortBy order.OrderFieldMask `protobuf:"varint,2,opt,name=sort_by,json=sortBy,proto3,enum=order.OrderFieldMask" json:"sort_by,omitempty"`
	// set of filters to be applied in addition to actor_id while fetching the transactions.
	Filters     *TransactionFilters     `protobuf:"bytes,3,opt,name=filters,proto3" json:"filters,omitempty"`
	PageContext *rpc.PageContextRequest `protobuf:"bytes,6,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetTransactionDetailsForReviewRequest) Reset() {
	*x = GetTransactionDetailsForReviewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionDetailsForReviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionDetailsForReviewRequest) ProtoMessage() {}

func (x *GetTransactionDetailsForReviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionDetailsForReviewRequest.ProtoReflect.Descriptor instead.
func (*GetTransactionDetailsForReviewRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetTransactionDetailsForReviewRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *GetTransactionDetailsForReviewRequest) GetSortBy() order.OrderFieldMask {
	if x != nil {
		return x.SortBy
	}
	return order.OrderFieldMask(0)
}

func (x *GetTransactionDetailsForReviewRequest) GetFilters() *TransactionFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *GetTransactionDetailsForReviewRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetTransactionDetailsForReviewResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status             *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TransactionDetails []*TransactionDetail     `protobuf:"bytes,2,rep,name=transaction_details,json=transactionDetails,proto3" json:"transaction_details,omitempty"`
	PageContext        *rpc.PageContextResponse `protobuf:"bytes,5,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetTransactionDetailsForReviewResponse) Reset() {
	*x = GetTransactionDetailsForReviewResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionDetailsForReviewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionDetailsForReviewResponse) ProtoMessage() {}

func (x *GetTransactionDetailsForReviewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionDetailsForReviewResponse.ProtoReflect.Descriptor instead.
func (*GetTransactionDetailsForReviewResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetTransactionDetailsForReviewResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTransactionDetailsForReviewResponse) GetTransactionDetails() []*TransactionDetail {
	if x != nil {
		return x.TransactionDetails
	}
	return nil
}

func (x *GetTransactionDetailsForReviewResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

// TransactionDetail stores the details required for reviewing a user's transactions. It can have order, transaction and other details in addition like category, tripped rules and alert details etc.
type TransactionDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderWithTransactions *order.OrderWithTransactions `protobuf:"bytes,1,opt,name=order_with_transactions,json=orderWithTransactions,proto3" json:"order_with_transactions,omitempty"`
	// merchant_details are available only for p2m transactions
	MerchantDetails *MerchantDetails `protobuf:"bytes,2,opt,name=merchant_details,json=merchantDetails,proto3" json:"merchant_details,omitempty"`
	// type of receiver (transaction is p2p or p2m)
	ReceiverType string `protobuf:"bytes,4,opt,name=receiver_type,json=receiverType,proto3" json:"receiver_type,omitempty"`
	// type of transaction (credit/debit)
	AccountingEntry string `protobuf:"bytes,5,opt,name=accounting_entry,json=accountingEntry,proto3" json:"accounting_entry,omitempty"`
}

func (x *TransactionDetail) Reset() {
	*x = TransactionDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionDetail) ProtoMessage() {}

func (x *TransactionDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionDetail.ProtoReflect.Descriptor instead.
func (*TransactionDetail) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{14}
}

func (x *TransactionDetail) GetOrderWithTransactions() *order.OrderWithTransactions {
	if x != nil {
		return x.OrderWithTransactions
	}
	return nil
}

func (x *TransactionDetail) GetMerchantDetails() *MerchantDetails {
	if x != nil {
		return x.MerchantDetails
	}
	return nil
}

func (x *TransactionDetail) GetReceiverType() string {
	if x != nil {
		return x.ReceiverType
	}
	return ""
}

func (x *TransactionDetail) GetAccountingEntry() string {
	if x != nil {
		return x.AccountingEntry
	}
	return ""
}

type MerchantDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MccCode      string `protobuf:"bytes,1,opt,name=mcc_code,json=mccCode,proto3" json:"mcc_code,omitempty"`
	MerchantName string `protobuf:"bytes,2,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name,omitempty"`
}

func (x *MerchantDetails) Reset() {
	*x = MerchantDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerchantDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantDetails) ProtoMessage() {}

func (x *MerchantDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantDetails.ProtoReflect.Descriptor instead.
func (*MerchantDetails) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{15}
}

func (x *MerchantDetails) GetMccCode() string {
	if x != nil {
		return x.MccCode
	}
	return ""
}

func (x *MerchantDetails) GetMerchantName() string {
	if x != nil {
		return x.MerchantName
	}
	return ""
}

type ListCasesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of supported filters
	Filters *review.CaseFilters `protobuf:"bytes,1,opt,name=filters,proto3" json:"filters,omitempty"`
	// max supported page size is 100
	PageContext *rpc.PageContextRequest `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *ListCasesRequest) Reset() {
	*x = ListCasesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCasesRequest) ProtoMessage() {}

func (x *ListCasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCasesRequest.ProtoReflect.Descriptor instead.
func (*ListCasesRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{16}
}

func (x *ListCasesRequest) GetFilters() *review.CaseFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *ListCasesRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type ListCasesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Cases       []*review.Case           `protobuf:"bytes,2,rep,name=cases,proto3" json:"cases,omitempty"`
	PageContext *rpc.PageContextResponse `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *ListCasesResponse) Reset() {
	*x = ListCasesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCasesResponse) ProtoMessage() {}

func (x *ListCasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCasesResponse.ProtoReflect.Descriptor instead.
func (*ListCasesResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{17}
}

func (x *ListCasesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListCasesResponse) GetCases() []*review.Case {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *ListCasesResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type CreateAlertsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of alerts to be ingested
	// at max 1000 cases can be passed in request
	Alerts []*RawAlert `protobuf:"bytes,1,rep,name=alerts,proto3" json:"alerts,omitempty"`
}

func (x *CreateAlertsRequest) Reset() {
	*x = CreateAlertsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAlertsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAlertsRequest) ProtoMessage() {}

func (x *CreateAlertsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAlertsRequest.ProtoReflect.Descriptor instead.
func (*CreateAlertsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{18}
}

func (x *CreateAlertsRequest) GetAlerts() []*RawAlert {
	if x != nil {
		return x.Alerts
	}
	return nil
}

type CreateAlertsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// number of alerts for which creation failed
	FailureCount uint32                          `protobuf:"varint,2,opt,name=failure_count,json=failureCount,proto3" json:"failure_count,omitempty"`
	Failures     []*CreateAlertsResponse_Failure `protobuf:"bytes,3,rep,name=failures,proto3" json:"failures,omitempty"`
}

func (x *CreateAlertsResponse) Reset() {
	*x = CreateAlertsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAlertsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAlertsResponse) ProtoMessage() {}

func (x *CreateAlertsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAlertsResponse.ProtoReflect.Descriptor instead.
func (*CreateAlertsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{19}
}

func (x *CreateAlertsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateAlertsResponse) GetFailureCount() uint32 {
	if x != nil {
		return x.FailureCount
	}
	return 0
}

func (x *CreateAlertsResponse) GetFailures() []*CreateAlertsResponse_Failure {
	if x != nil {
		return x.Failures
	}
	return nil
}

type TransactionFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromTime        *timestamppb.Timestamp                      `protobuf:"bytes,1,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
	ToTime          *timestamppb.Timestamp                      `protobuf:"bytes,2,opt,name=to_time,json=toTime,proto3" json:"to_time,omitempty"`
	Statuses        []review.TransactionReviewOrderStatusFilter `protobuf:"varint,3,rep,packed,name=statuses,proto3,enum=risk.case_management.review.TransactionReviewOrderStatusFilter" json:"statuses,omitempty"`
	AccountingEntry payment.AccountingEntryType                 `protobuf:"varint,4,opt,name=accounting_entry,json=accountingEntry,proto3,enum=order.payment.AccountingEntryType" json:"accounting_entry,omitempty"`
}

func (x *TransactionFilters) Reset() {
	*x = TransactionFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionFilters) ProtoMessage() {}

func (x *TransactionFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionFilters.ProtoReflect.Descriptor instead.
func (*TransactionFilters) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{20}
}

func (x *TransactionFilters) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

func (x *TransactionFilters) GetToTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ToTime
	}
	return nil
}

func (x *TransactionFilters) GetStatuses() []review.TransactionReviewOrderStatusFilter {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *TransactionFilters) GetAccountingEntry() payment.AccountingEntryType {
	if x != nil {
		return x.AccountingEntry
	}
	return payment.AccountingEntryType(0)
}

type GetReviewDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// case for which review details are being fetched, Mandatory for fetching the details
	CaseId string `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// Mandatory to pass at least one field mask, if field mask list is empty will return INVALID_ARGUMENT status code
	FieldMasks []review.ReviewDetailsFieldMask `protobuf:"varint,2,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=risk.case_management.review.ReviewDetailsFieldMask" json:"field_masks,omitempty"`
}

func (x *GetReviewDetailsRequest) Reset() {
	*x = GetReviewDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReviewDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReviewDetailsRequest) ProtoMessage() {}

func (x *GetReviewDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReviewDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetReviewDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetReviewDetailsRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *GetReviewDetailsRequest) GetFieldMasks() []review.ReviewDetailsFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

type GetReviewDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// case details for case_id passed in request
	Case *review.Case `protobuf:"bytes,2,opt,name=case,proto3" json:"case,omitempty"`
	// list of all the actions done against the case
	// not adding pagination support since number of actions shouldn't be very high
	// in worst case if there are many actions against the case, we will return latest 100 actions
	// If pagination is needed in future, support for new RPC with pagination will be added to fetch other actions
	Actions []*review.Action `protobuf:"bytes,3,rep,name=actions,proto3" json:"actions,omitempty"`
	// list of all the present/past cases against actor related to current case
	// pagination is not supported currently since number of cases against an actor should be very less
	// in worst case if there are many cases against an actor we will return latest 100 cases
	RelatedCases []*review.Case `protobuf:"bytes,4,rep,name=related_cases,json=relatedCases,proto3" json:"related_cases,omitempty"`
	// list of all the alerts(along with rule details) linked to the given case
	// pagination is not supported currently since number of alerts linked to a case should be fairly low
	// in worst case if there are many alerts linked to a case we will return latest 100 cases
	Alerts []*AlertWithRuleDetails `protobuf:"bytes,5,rep,name=alerts,proto3" json:"alerts,omitempty"`
	// firehose id of the actor corresponding to the case
	// will be only populated for transaction review type
	FirehoseId string `protobuf:"bytes,6,opt,name=firehose_id,json=firehoseId,proto3" json:"firehose_id,omitempty"`
	// Aggregated alerts for an actor in the past 180 days.
	// A Maximum of 100 alert aggregates will be sent in response.
	AlertAggregatesForActorWithRule []*AlertAggregateForActorWithRule `protobuf:"bytes,7,rep,name=alert_aggregates_for_actor_with_rule,json=alertAggregatesForActorWithRule,proto3" json:"alert_aggregates_for_actor_with_rule,omitempty"`
}

func (x *GetReviewDetailsResponse) Reset() {
	*x = GetReviewDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReviewDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReviewDetailsResponse) ProtoMessage() {}

func (x *GetReviewDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReviewDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetReviewDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetReviewDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetReviewDetailsResponse) GetCase() *review.Case {
	if x != nil {
		return x.Case
	}
	return nil
}

func (x *GetReviewDetailsResponse) GetActions() []*review.Action {
	if x != nil {
		return x.Actions
	}
	return nil
}

func (x *GetReviewDetailsResponse) GetRelatedCases() []*review.Case {
	if x != nil {
		return x.RelatedCases
	}
	return nil
}

func (x *GetReviewDetailsResponse) GetAlerts() []*AlertWithRuleDetails {
	if x != nil {
		return x.Alerts
	}
	return nil
}

func (x *GetReviewDetailsResponse) GetFirehoseId() string {
	if x != nil {
		return x.FirehoseId
	}
	return ""
}

func (x *GetReviewDetailsResponse) GetAlertAggregatesForActorWithRule() []*AlertAggregateForActorWithRule {
	if x != nil {
		return x.AlertAggregatesForActorWithRule
	}
	return nil
}

type GetLinkedAlertsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifier for which alerts are being fetched
	// we are only supporting case_id identifier here for now since we want to restrict access to this information without a case present
	// rest of the identifiers for different levels will be derived from case and underlying alert details
	//
	// Types that are assignable to Identifier:
	//
	//	*GetLinkedAlertsRequest_CaseId
	Identifier isGetLinkedAlertsRequest_Identifier `protobuf_oneof:"identifier"`
	// level at which alerts are required, ex: all alerts against a case vs against an actor
	InformationLevel enums.InformationLevel `protobuf:"varint,2,opt,name=information_level,json=informationLevel,proto3,enum=risk.case_management.enums.InformationLevel" json:"information_level,omitempty"`
	// max supported page size is 50
	PageContext *rpc.PageContextRequest `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetLinkedAlertsRequest) Reset() {
	*x = GetLinkedAlertsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLinkedAlertsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLinkedAlertsRequest) ProtoMessage() {}

func (x *GetLinkedAlertsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLinkedAlertsRequest.ProtoReflect.Descriptor instead.
func (*GetLinkedAlertsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{23}
}

func (m *GetLinkedAlertsRequest) GetIdentifier() isGetLinkedAlertsRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetLinkedAlertsRequest) GetCaseId() string {
	if x, ok := x.GetIdentifier().(*GetLinkedAlertsRequest_CaseId); ok {
		return x.CaseId
	}
	return ""
}

func (x *GetLinkedAlertsRequest) GetInformationLevel() enums.InformationLevel {
	if x != nil {
		return x.InformationLevel
	}
	return enums.InformationLevel(0)
}

func (x *GetLinkedAlertsRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type isGetLinkedAlertsRequest_Identifier interface {
	isGetLinkedAlertsRequest_Identifier()
}

type GetLinkedAlertsRequest_CaseId struct {
	CaseId string `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3,oneof"`
}

func (*GetLinkedAlertsRequest_CaseId) isGetLinkedAlertsRequest_Identifier() {}

type GetLinkedAlertsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Alerts      []*AlertWithRuleDetails  `protobuf:"bytes,2,rep,name=alerts,proto3" json:"alerts,omitempty"`
	PageContext *rpc.PageContextResponse `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetLinkedAlertsResponse) Reset() {
	*x = GetLinkedAlertsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLinkedAlertsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLinkedAlertsResponse) ProtoMessage() {}

func (x *GetLinkedAlertsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLinkedAlertsResponse.ProtoReflect.Descriptor instead.
func (*GetLinkedAlertsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetLinkedAlertsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLinkedAlertsResponse) GetAlerts() []*AlertWithRuleDetails {
	if x != nil {
		return x.Alerts
	}
	return nil
}

func (x *GetLinkedAlertsResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type PerformActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// case against which action is being taken
	CaseId string `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// type of action to be performed on the case
	ActionType review.ActionType `protobuf:"varint,2,opt,name=action_type,json=actionType,proto3,enum=risk.case_management.review.ActionType" json:"action_type,omitempty"`
	// addtion action parameters if required
	ActionParameters *review.ActionParameters `protobuf:"bytes,3,opt,name=action_parameters,json=actionParameters,proto3" json:"action_parameters,omitempty"`
	// source for the action
	Source review.ActionSource `protobuf:"varint,4,opt,name=source,proto3,enum=risk.case_management.review.ActionSource" json:"source,omitempty"`
	// email of analyst, mandatory for actions from source as review_flow
	AnalystEmail string `protobuf:"bytes,5,opt,name=analyst_email,json=analystEmail,proto3" json:"analyst_email,omitempty"`
}

func (x *PerformActionRequest) Reset() {
	*x = PerformActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PerformActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerformActionRequest) ProtoMessage() {}

func (x *PerformActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerformActionRequest.ProtoReflect.Descriptor instead.
func (*PerformActionRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{25}
}

func (x *PerformActionRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *PerformActionRequest) GetActionType() review.ActionType {
	if x != nil {
		return x.ActionType
	}
	return review.ActionType(0)
}

func (x *PerformActionRequest) GetActionParameters() *review.ActionParameters {
	if x != nil {
		return x.ActionParameters
	}
	return nil
}

func (x *PerformActionRequest) GetSource() review.ActionSource {
	if x != nil {
		return x.Source
	}
	return review.ActionSource(0)
}

func (x *PerformActionRequest) GetAnalystEmail() string {
	if x != nil {
		return x.AnalystEmail
	}
	return ""
}

type PerformActionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *PerformActionResponse) Reset() {
	*x = PerformActionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PerformActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerformActionResponse) ProtoMessage() {}

func (x *PerformActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerformActionResponse.ProtoReflect.Descriptor instead.
func (*PerformActionResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{26}
}

func (x *PerformActionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateCommentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Comment *review.Comment `protobuf:"bytes,1,opt,name=comment,proto3" json:"comment,omitempty"`
}

func (x *CreateCommentRequest) Reset() {
	*x = CreateCommentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCommentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCommentRequest) ProtoMessage() {}

func (x *CreateCommentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCommentRequest.ProtoReflect.Descriptor instead.
func (*CreateCommentRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{27}
}

func (x *CreateCommentRequest) GetComment() *review.Comment {
	if x != nil {
		return x.Comment
	}
	return nil
}

type CreateCommentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateCommentResponse) Reset() {
	*x = CreateCommentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCommentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCommentResponse) ProtoMessage() {}

func (x *CreateCommentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCommentResponse.ProtoReflect.Descriptor instead.
func (*CreateCommentResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{28}
}

func (x *CreateCommentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ListCommentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/risk/case_management/service.proto.
	Query          *review.CommentQuery   `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	CommentFilters *review.CommentFilters `protobuf:"bytes,2,opt,name=comment_filters,json=commentFilters,proto3" json:"comment_filters,omitempty"`
}

func (x *ListCommentsRequest) Reset() {
	*x = ListCommentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCommentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCommentsRequest) ProtoMessage() {}

func (x *ListCommentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCommentsRequest.ProtoReflect.Descriptor instead.
func (*ListCommentsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{29}
}

// Deprecated: Marked as deprecated in api/risk/case_management/service.proto.
func (x *ListCommentsRequest) GetQuery() *review.CommentQuery {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *ListCommentsRequest) GetCommentFilters() *review.CommentFilters {
	if x != nil {
		return x.CommentFilters
	}
	return nil
}

type ListCommentsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Comments []*review.Comment `protobuf:"bytes,2,rep,name=comments,proto3" json:"comments,omitempty"`
}

func (x *ListCommentsResponse) Reset() {
	*x = ListCommentsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCommentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCommentsResponse) ProtoMessage() {}

func (x *ListCommentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCommentsResponse.ProtoReflect.Descriptor instead.
func (*ListCommentsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{30}
}

func (x *ListCommentsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListCommentsResponse) GetComments() []*review.Comment {
	if x != nil {
		return x.Comments
	}
	return nil
}

type GetAllowedAnnotationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query *review.AllowedAnnotationQuery `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
}

func (x *GetAllowedAnnotationsRequest) Reset() {
	*x = GetAllowedAnnotationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllowedAnnotationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllowedAnnotationsRequest) ProtoMessage() {}

func (x *GetAllowedAnnotationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllowedAnnotationsRequest.ProtoReflect.Descriptor instead.
func (*GetAllowedAnnotationsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{31}
}

func (x *GetAllowedAnnotationsRequest) GetQuery() *review.AllowedAnnotationQuery {
	if x != nil {
		return x.Query
	}
	return nil
}

type GetAllowedAnnotationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status             *rpc.Status                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	AllowedAnnotations []*review.AllowedAnnotation `protobuf:"bytes,2,rep,name=allowed_annotations,json=allowedAnnotations,proto3" json:"allowed_annotations,omitempty"`
}

func (x *GetAllowedAnnotationsResponse) Reset() {
	*x = GetAllowedAnnotationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllowedAnnotationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllowedAnnotationsResponse) ProtoMessage() {}

func (x *GetAllowedAnnotationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllowedAnnotationsResponse.ProtoReflect.Descriptor instead.
func (*GetAllowedAnnotationsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{32}
}

func (x *GetAllowedAnnotationsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAllowedAnnotationsResponse) GetAllowedAnnotations() []*review.AllowedAnnotation {
	if x != nil {
		return x.AllowedAnnotations
	}
	return nil
}

type CreateAnnotationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Annotation *review.Annotation `protobuf:"bytes,1,opt,name=annotation,proto3" json:"annotation,omitempty"`
}

func (x *CreateAnnotationRequest) Reset() {
	*x = CreateAnnotationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAnnotationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAnnotationRequest) ProtoMessage() {}

func (x *CreateAnnotationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAnnotationRequest.ProtoReflect.Descriptor instead.
func (*CreateAnnotationRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{33}
}

func (x *CreateAnnotationRequest) GetAnnotation() *review.Annotation {
	if x != nil {
		return x.Annotation
	}
	return nil
}

type CreateAnnotationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateAnnotationResponse) Reset() {
	*x = CreateAnnotationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAnnotationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAnnotationResponse) ProtoMessage() {}

func (x *CreateAnnotationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAnnotationResponse.ProtoReflect.Descriptor instead.
func (*CreateAnnotationResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{34}
}

func (x *CreateAnnotationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ListAnnotationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/risk/case_management/service.proto.
	Query             *AnnotationQuery   `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	AnnotationFilters *AnnotationFilters `protobuf:"bytes,2,opt,name=annotation_filters,json=annotationFilters,proto3" json:"annotation_filters,omitempty"`
}

func (x *ListAnnotationsRequest) Reset() {
	*x = ListAnnotationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAnnotationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAnnotationsRequest) ProtoMessage() {}

func (x *ListAnnotationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAnnotationsRequest.ProtoReflect.Descriptor instead.
func (*ListAnnotationsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{35}
}

// Deprecated: Marked as deprecated in api/risk/case_management/service.proto.
func (x *ListAnnotationsRequest) GetQuery() *AnnotationQuery {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *ListAnnotationsRequest) GetAnnotationFilters() *AnnotationFilters {
	if x != nil {
		return x.AnnotationFilters
	}
	return nil
}

type ListAnnotationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Annotations []*CombinedAnnotation `protobuf:"bytes,2,rep,name=annotations,proto3" json:"annotations,omitempty"`
}

func (x *ListAnnotationsResponse) Reset() {
	*x = ListAnnotationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAnnotationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAnnotationsResponse) ProtoMessage() {}

func (x *ListAnnotationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAnnotationsResponse.ProtoReflect.Descriptor instead.
func (*ListAnnotationsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{36}
}

func (x *ListAnnotationsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListAnnotationsResponse) GetAnnotations() []*CombinedAnnotation {
	if x != nil {
		return x.Annotations
	}
	return nil
}

type CreateRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rule *Rule `protobuf:"bytes,1,opt,name=rule,proto3" json:"rule,omitempty"`
}

func (x *CreateRuleRequest) Reset() {
	*x = CreateRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRuleRequest) ProtoMessage() {}

func (x *CreateRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRuleRequest.ProtoReflect.Descriptor instead.
func (*CreateRuleRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{37}
}

func (x *CreateRuleRequest) GetRule() *Rule {
	if x != nil {
		return x.Rule
	}
	return nil
}

type CreateRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateRuleResponse) Reset() {
	*x = CreateRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRuleResponse) ProtoMessage() {}

func (x *CreateRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRuleResponse.ProtoReflect.Descriptor instead.
func (*CreateRuleResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{38}
}

func (x *CreateRuleResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ListRulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/risk/case_management/service.proto.
	ExternalIds []string `protobuf:"bytes,1,rep,name=externalIds,proto3" json:"externalIds,omitempty"`
	// Types that are assignable to FilterByValue:
	//
	//	*ListRulesRequest_ExternalId
	//	*ListRulesRequest_RuleName
	//	*ListRulesRequest_RuleGroup
	FilterByValue isListRulesRequest_FilterByValue `protobuf_oneof:"filter_by_value"`
	// accepted rule field masks:
	// RULE_FIELD_MASK_EXTERNAL_ID
	// RULE_FIELD_MASK_NAME
	// RULE_FIELD_MASK_RULE_GROUP
	FilterBy RuleFieldMask `protobuf:"varint,5,opt,name=filter_by,json=filterBy,proto3,enum=risk.case_management.RuleFieldMask" json:"filter_by,omitempty"`
}

func (x *ListRulesRequest) Reset() {
	*x = ListRulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRulesRequest) ProtoMessage() {}

func (x *ListRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRulesRequest.ProtoReflect.Descriptor instead.
func (*ListRulesRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{39}
}

// Deprecated: Marked as deprecated in api/risk/case_management/service.proto.
func (x *ListRulesRequest) GetExternalIds() []string {
	if x != nil {
		return x.ExternalIds
	}
	return nil
}

func (m *ListRulesRequest) GetFilterByValue() isListRulesRequest_FilterByValue {
	if m != nil {
		return m.FilterByValue
	}
	return nil
}

func (x *ListRulesRequest) GetExternalId() string {
	if x, ok := x.GetFilterByValue().(*ListRulesRequest_ExternalId); ok {
		return x.ExternalId
	}
	return ""
}

func (x *ListRulesRequest) GetRuleName() string {
	if x, ok := x.GetFilterByValue().(*ListRulesRequest_RuleName); ok {
		return x.RuleName
	}
	return ""
}

func (x *ListRulesRequest) GetRuleGroup() RuleGroup {
	if x, ok := x.GetFilterByValue().(*ListRulesRequest_RuleGroup); ok {
		return x.RuleGroup
	}
	return RuleGroup_RULE_GROUP_UNSPECIFIED
}

func (x *ListRulesRequest) GetFilterBy() RuleFieldMask {
	if x != nil {
		return x.FilterBy
	}
	return RuleFieldMask_RULE_FIELD_MASK_UNSPECIFIED
}

type isListRulesRequest_FilterByValue interface {
	isListRulesRequest_FilterByValue()
}

type ListRulesRequest_ExternalId struct {
	ExternalId string `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3,oneof"`
}

type ListRulesRequest_RuleName struct {
	RuleName string `protobuf:"bytes,3,opt,name=rule_name,json=ruleName,proto3,oneof"`
}

type ListRulesRequest_RuleGroup struct {
	RuleGroup RuleGroup `protobuf:"varint,4,opt,name=rule_group,json=ruleGroup,proto3,enum=risk.case_management.RuleGroup,oneof"`
}

func (*ListRulesRequest_ExternalId) isListRulesRequest_FilterByValue() {}

func (*ListRulesRequest_RuleName) isListRulesRequest_FilterByValue() {}

func (*ListRulesRequest_RuleGroup) isListRulesRequest_FilterByValue() {}

type ListRulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Rules  []*Rule     `protobuf:"bytes,2,rep,name=rules,proto3" json:"rules,omitempty"`
}

func (x *ListRulesResponse) Reset() {
	*x = ListRulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRulesResponse) ProtoMessage() {}

func (x *ListRulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRulesResponse.ProtoReflect.Descriptor instead.
func (*ListRulesResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{40}
}

func (x *ListRulesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListRulesResponse) GetRules() []*Rule {
	if x != nil {
		return x.Rules
	}
	return nil
}

type UpdateRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rule        *Rule           `protobuf:"bytes,1,opt,name=rule,proto3" json:"rule,omitempty"`
	UpdateMasks []RuleFieldMask `protobuf:"varint,2,rep,packed,name=update_masks,json=updateMasks,proto3,enum=risk.case_management.RuleFieldMask" json:"update_masks,omitempty"`
}

func (x *UpdateRuleRequest) Reset() {
	*x = UpdateRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRuleRequest) ProtoMessage() {}

func (x *UpdateRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRuleRequest.ProtoReflect.Descriptor instead.
func (*UpdateRuleRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{41}
}

func (x *UpdateRuleRequest) GetRule() *Rule {
	if x != nil {
		return x.Rule
	}
	return nil
}

func (x *UpdateRuleRequest) GetUpdateMasks() []RuleFieldMask {
	if x != nil {
		return x.UpdateMasks
	}
	return nil
}

type UpdateRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateRuleResponse) Reset() {
	*x = UpdateRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRuleResponse) ProtoMessage() {}

func (x *UpdateRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRuleResponse.ProtoReflect.Descriptor instead.
func (*UpdateRuleResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{42}
}

func (x *UpdateRuleResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ListSortedCasesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// supported filters
	Filters *review.SortableCaseFilters `protobuf:"bytes,1,opt,name=filters,proto3" json:"filters,omitempty"`
	// field to sort cases on and sorting order
	SortBy *review.CaseSortBy `protobuf:"bytes,2,opt,name=sort_by,json=sortBy,proto3" json:"sort_by,omitempty"`
	// max supported page size is 100
	PageContextRequest *rpc.PageContextRequest `protobuf:"bytes,3,opt,name=page_context_request,json=pageContextRequest,proto3" json:"page_context_request,omitempty"`
}

func (x *ListSortedCasesRequest) Reset() {
	*x = ListSortedCasesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSortedCasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSortedCasesRequest) ProtoMessage() {}

func (x *ListSortedCasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSortedCasesRequest.ProtoReflect.Descriptor instead.
func (*ListSortedCasesRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{43}
}

func (x *ListSortedCasesRequest) GetFilters() *review.SortableCaseFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *ListSortedCasesRequest) GetSortBy() *review.CaseSortBy {
	if x != nil {
		return x.SortBy
	}
	return nil
}

func (x *ListSortedCasesRequest) GetPageContextRequest() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContextRequest
	}
	return nil
}

type ListSortedCasesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status              *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Cases               []*review.Case           `protobuf:"bytes,2,rep,name=cases,proto3" json:"cases,omitempty"`
	PageContextResponse *rpc.PageContextResponse `protobuf:"bytes,3,opt,name=page_context_response,json=pageContextResponse,proto3" json:"page_context_response,omitempty"`
}

func (x *ListSortedCasesResponse) Reset() {
	*x = ListSortedCasesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSortedCasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSortedCasesResponse) ProtoMessage() {}

func (x *ListSortedCasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSortedCasesResponse.ProtoReflect.Descriptor instead.
func (*ListSortedCasesResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{44}
}

func (x *ListSortedCasesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListSortedCasesResponse) GetCases() []*review.Case {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *ListSortedCasesResponse) GetPageContextResponse() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContextResponse
	}
	return nil
}

type GetFiUserRelationshipRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetFiUserRelationshipRequest) Reset() {
	*x = GetFiUserRelationshipRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFiUserRelationshipRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFiUserRelationshipRequest) ProtoMessage() {}

func (x *GetFiUserRelationshipRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFiUserRelationshipRequest.ProtoReflect.Descriptor instead.
func (*GetFiUserRelationshipRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{45}
}

func (x *GetFiUserRelationshipRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetFiUserRelationshipResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// key could be a product type like salary tier, is CC user etc.
	// value could be like tier type like infinite, true etc.
	FiUserRelationship map[string]string `protobuf:"bytes,2,rep,name=fi_user_relationship,json=fiUserRelationship,proto3" json:"fi_user_relationship,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetFiUserRelationshipResponse) Reset() {
	*x = GetFiUserRelationshipResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFiUserRelationshipResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFiUserRelationshipResponse) ProtoMessage() {}

func (x *GetFiUserRelationshipResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFiUserRelationshipResponse.ProtoReflect.Descriptor instead.
func (*GetFiUserRelationshipResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{46}
}

func (x *GetFiUserRelationshipResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFiUserRelationshipResponse) GetFiUserRelationship() map[string]string {
	if x != nil {
		return x.FiUserRelationship
	}
	return nil
}

type GetFormRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FormId string `protobuf:"bytes,1,opt,name=form_id,json=formId,proto3" json:"form_id,omitempty"`
}

func (x *GetFormRequest) Reset() {
	*x = GetFormRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFormRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFormRequest) ProtoMessage() {}

func (x *GetFormRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFormRequest.ProtoReflect.Descriptor instead.
func (*GetFormRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{47}
}

func (x *GetFormRequest) GetFormId() string {
	if x != nil {
		return x.FormId
	}
	return ""
}

type GetFormResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of questions to be shown to user
	Questions []*form.ExtendedQuestion `protobuf:"bytes,4,rep,name=questions,proto3" json:"questions,omitempty"`
	Form      *form.Form               `protobuf:"bytes,5,opt,name=form,proto3" json:"form,omitempty"`
}

func (x *GetFormResponse) Reset() {
	*x = GetFormResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFormResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFormResponse) ProtoMessage() {}

func (x *GetFormResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFormResponse.ProtoReflect.Descriptor instead.
func (*GetFormResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{48}
}

func (x *GetFormResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFormResponse) GetQuestions() []*form.ExtendedQuestion {
	if x != nil {
		return x.Questions
	}
	return nil
}

func (x *GetFormResponse) GetForm() *form.Form {
	if x != nil {
		return x.Form
	}
	return nil
}

type SubmitFormRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FormId string `protobuf:"bytes,1,opt,name=form_id,json=formId,proto3" json:"form_id,omitempty"`
	// list of responses submitted by user for questionnaire in form.
	Responses []*form.QuestionResponse `protobuf:"bytes,2,rep,name=responses,proto3" json:"responses,omitempty"`
}

func (x *SubmitFormRequest) Reset() {
	*x = SubmitFormRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitFormRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitFormRequest) ProtoMessage() {}

func (x *SubmitFormRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitFormRequest.ProtoReflect.Descriptor instead.
func (*SubmitFormRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{49}
}

func (x *SubmitFormRequest) GetFormId() string {
	if x != nil {
		return x.FormId
	}
	return ""
}

func (x *SubmitFormRequest) GetResponses() []*form.QuestionResponse {
	if x != nil {
		return x.Responses
	}
	return nil
}

type SubmitFormResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SubmitFormResponse) Reset() {
	*x = SubmitFormResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitFormResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitFormResponse) ProtoMessage() {}

func (x *SubmitFormResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitFormResponse.ProtoReflect.Descriptor instead.
func (*SubmitFormResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{50}
}

func (x *SubmitFormResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ListFormsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filters    *form.FormFilters            `protobuf:"bytes,1,opt,name=filters,proto3" json:"filters,omitempty"`
	FieldMasks []form.ExtendedFormFieldMask `protobuf:"varint,3,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=risk.case_management.form.ExtendedFormFieldMask" json:"field_masks,omitempty"`
}

func (x *ListFormsRequest) Reset() {
	*x = ListFormsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListFormsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFormsRequest) ProtoMessage() {}

func (x *ListFormsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFormsRequest.ProtoReflect.Descriptor instead.
func (*ListFormsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{51}
}

func (x *ListFormsRequest) GetFilters() *form.FormFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *ListFormsRequest) GetFieldMasks() []form.ExtendedFormFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

type ListFormsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status          `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Forms  []*form.ExtendedForm `protobuf:"bytes,2,rep,name=forms,proto3" json:"forms,omitempty"`
}

func (x *ListFormsResponse) Reset() {
	*x = ListFormsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListFormsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFormsResponse) ProtoMessage() {}

func (x *ListFormsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFormsResponse.ProtoReflect.Descriptor instead.
func (*ListFormsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{52}
}

func (x *ListFormsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListFormsResponse) GetForms() []*form.ExtendedForm {
	if x != nil {
		return x.Forms
	}
	return nil
}

type GetAlertsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Max supported limit is 200
	Limit int32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *GetAlertsRequest) Reset() {
	*x = GetAlertsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertsRequest) ProtoMessage() {}

func (x *GetAlertsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertsRequest.ProtoReflect.Descriptor instead.
func (*GetAlertsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{53}
}

func (x *GetAlertsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAlertsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GetAlertsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of all the alerts(along with rule details) linked to the given actor
	// pagination is not supported currently since number of alerts linked to a case should be fairly low
	// alerts will be sorted by creation timestamp in desc order
	// max number of alerts returned will be equal to the limit passed in the request
	Alerts []*AlertWithRuleDetails `protobuf:"bytes,2,rep,name=alerts,proto3" json:"alerts,omitempty"`
}

func (x *GetAlertsResponse) Reset() {
	*x = GetAlertsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertsResponse) ProtoMessage() {}

func (x *GetAlertsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertsResponse.ProtoReflect.Descriptor instead.
func (*GetAlertsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{54}
}

func (x *GetAlertsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAlertsResponse) GetAlerts() []*AlertWithRuleDetails {
	if x != nil {
		return x.Alerts
	}
	return nil
}

type GetFormsForActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Response limit, send 0 for no limit
	Limit int32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *GetFormsForActorRequest) Reset() {
	*x = GetFormsForActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFormsForActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFormsForActorRequest) ProtoMessage() {}

func (x *GetFormsForActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFormsForActorRequest.ProtoReflect.Descriptor instead.
func (*GetFormsForActorRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{55}
}

func (x *GetFormsForActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetFormsForActorRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GetFormsForActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Form   []*form.Form `protobuf:"bytes,2,rep,name=form,proto3" json:"form,omitempty"`
}

func (x *GetFormsForActorResponse) Reset() {
	*x = GetFormsForActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFormsForActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFormsForActorResponse) ProtoMessage() {}

func (x *GetFormsForActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFormsForActorResponse.ProtoReflect.Descriptor instead.
func (*GetFormsForActorResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{56}
}

func (x *GetFormsForActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFormsForActorResponse) GetForm() []*form.Form {
	if x != nil {
		return x.Form
	}
	return nil
}

type CreateSuggestedActionForRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rule_id, against which the suggested action is to be created
	RuleId              string                   `protobuf:"bytes,1,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
	SuggestedActionType SuggestedActionType      `protobuf:"varint,2,opt,name=suggested_action_type,json=suggestedActionType,proto3,enum=risk.case_management.SuggestedActionType" json:"suggested_action_type,omitempty"`
	ActionParameters    *review.ActionParameters `protobuf:"bytes,3,opt,name=action_parameters,json=actionParameters,proto3" json:"action_parameters,omitempty"`
}

func (x *CreateSuggestedActionForRuleRequest) Reset() {
	*x = CreateSuggestedActionForRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSuggestedActionForRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSuggestedActionForRuleRequest) ProtoMessage() {}

func (x *CreateSuggestedActionForRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSuggestedActionForRuleRequest.ProtoReflect.Descriptor instead.
func (*CreateSuggestedActionForRuleRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{57}
}

func (x *CreateSuggestedActionForRuleRequest) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *CreateSuggestedActionForRuleRequest) GetSuggestedActionType() SuggestedActionType {
	if x != nil {
		return x.SuggestedActionType
	}
	return SuggestedActionType_SUGGESTED_ACTION_TYPE_UNSPECIFIED
}

func (x *CreateSuggestedActionForRuleRequest) GetActionParameters() *review.ActionParameters {
	if x != nil {
		return x.ActionParameters
	}
	return nil
}

type CreateSuggestedActionForRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SuggestedAction *SuggestedAction `protobuf:"bytes,2,opt,name=suggested_action,json=suggestedAction,proto3" json:"suggested_action,omitempty"`
}

func (x *CreateSuggestedActionForRuleResponse) Reset() {
	*x = CreateSuggestedActionForRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSuggestedActionForRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSuggestedActionForRuleResponse) ProtoMessage() {}

func (x *CreateSuggestedActionForRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSuggestedActionForRuleResponse.ProtoReflect.Descriptor instead.
func (*CreateSuggestedActionForRuleResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{58}
}

func (x *CreateSuggestedActionForRuleResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateSuggestedActionForRuleResponse) GetSuggestedAction() *SuggestedAction {
	if x != nil {
		return x.SuggestedAction
	}
	return nil
}

type CreateRuleReviewTypeMappingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rule_id, against which the suggested action is to be created
	RuleId     string            `protobuf:"bytes,1,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
	ReviewType review.ReviewType `protobuf:"varint,2,opt,name=review_type,json=reviewType,proto3,enum=risk.case_management.review.ReviewType" json:"review_type,omitempty"`
}

func (x *CreateRuleReviewTypeMappingRequest) Reset() {
	*x = CreateRuleReviewTypeMappingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRuleReviewTypeMappingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRuleReviewTypeMappingRequest) ProtoMessage() {}

func (x *CreateRuleReviewTypeMappingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRuleReviewTypeMappingRequest.ProtoReflect.Descriptor instead.
func (*CreateRuleReviewTypeMappingRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{59}
}

func (x *CreateRuleReviewTypeMappingRequest) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *CreateRuleReviewTypeMappingRequest) GetReviewType() review.ReviewType {
	if x != nil {
		return x.ReviewType
	}
	return review.ReviewType(0)
}

type CreateRuleReviewTypeMappingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateRuleReviewTypeMappingResponse) Reset() {
	*x = CreateRuleReviewTypeMappingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRuleReviewTypeMappingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRuleReviewTypeMappingResponse) ProtoMessage() {}

func (x *CreateRuleReviewTypeMappingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRuleReviewTypeMappingResponse.ProtoReflect.Descriptor instead.
func (*CreateRuleReviewTypeMappingResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{60}
}

func (x *CreateRuleReviewTypeMappingResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// GetTransactionBlocksRequest is used to request transaction blocks for a given actor or alert ID
type GetTransactionBlocksRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifier for which transaction blocks are being fetched
	//
	// Types that are assignable to Identifier:
	//
	//	*GetTransactionBlocksRequest_ActorId
	//	*GetTransactionBlocksRequest_AlertId
	Identifier isGetTransactionBlocksRequest_Identifier `protobuf_oneof:"identifier"`
	// Optional block type filter
	BlockType TransactionBlockType `protobuf:"varint,3,opt,name=block_type,json=blockType,proto3,enum=risk.case_management.TransactionBlockType" json:"block_type,omitempty"`
	// Maximum number of transaction blocks to return
	Limit int32 `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *GetTransactionBlocksRequest) Reset() {
	*x = GetTransactionBlocksRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionBlocksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionBlocksRequest) ProtoMessage() {}

func (x *GetTransactionBlocksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionBlocksRequest.ProtoReflect.Descriptor instead.
func (*GetTransactionBlocksRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{61}
}

func (m *GetTransactionBlocksRequest) GetIdentifier() isGetTransactionBlocksRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetTransactionBlocksRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*GetTransactionBlocksRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *GetTransactionBlocksRequest) GetAlertId() string {
	if x, ok := x.GetIdentifier().(*GetTransactionBlocksRequest_AlertId); ok {
		return x.AlertId
	}
	return ""
}

func (x *GetTransactionBlocksRequest) GetBlockType() TransactionBlockType {
	if x != nil {
		return x.BlockType
	}
	return TransactionBlockType_TRANSACTION_BLOCK_TYPE_UNSPECIFIED
}

func (x *GetTransactionBlocksRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type isGetTransactionBlocksRequest_Identifier interface {
	isGetTransactionBlocksRequest_Identifier()
}

type GetTransactionBlocksRequest_ActorId struct {
	// Actor ID to fetch transaction blocks for
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3,oneof"`
}

type GetTransactionBlocksRequest_AlertId struct {
	// Alert ID to fetch transaction blocks for
	AlertId string `protobuf:"bytes,2,opt,name=alert_id,json=alertId,proto3,oneof"`
}

func (*GetTransactionBlocksRequest_ActorId) isGetTransactionBlocksRequest_Identifier() {}

func (*GetTransactionBlocksRequest_AlertId) isGetTransactionBlocksRequest_Identifier() {}

// GetTransactionBlocksResponse contains the transaction blocks matching the request criteria
type GetTransactionBlocksResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of transaction blocks
	TransactionBlocks []*TransactionBlock `protobuf:"bytes,2,rep,name=transaction_blocks,json=transactionBlocks,proto3" json:"transaction_blocks,omitempty"`
}

func (x *GetTransactionBlocksResponse) Reset() {
	*x = GetTransactionBlocksResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionBlocksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionBlocksResponse) ProtoMessage() {}

func (x *GetTransactionBlocksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionBlocksResponse.ProtoReflect.Descriptor instead.
func (*GetTransactionBlocksResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{62}
}

func (x *GetTransactionBlocksResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTransactionBlocksResponse) GetTransactionBlocks() []*TransactionBlock {
	if x != nil {
		return x.TransactionBlocks
	}
	return nil
}

// CreateTransactionBlockRequest is used to create a new transaction block
type CreateTransactionBlockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Transaction blocks to create
	TransactionBlocks []*TransactionBlock `protobuf:"bytes,1,rep,name=transaction_blocks,json=transactionBlocks,proto3" json:"transaction_blocks,omitempty"`
}

func (x *CreateTransactionBlockRequest) Reset() {
	*x = CreateTransactionBlockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTransactionBlockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTransactionBlockRequest) ProtoMessage() {}

func (x *CreateTransactionBlockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTransactionBlockRequest.ProtoReflect.Descriptor instead.
func (*CreateTransactionBlockRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{63}
}

func (x *CreateTransactionBlockRequest) GetTransactionBlocks() []*TransactionBlock {
	if x != nil {
		return x.TransactionBlocks
	}
	return nil
}

// CreateTransactionBlockResponse contains the created transaction block
type CreateTransactionBlockResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Created transaction blocks
	TransactionBlocks []*TransactionBlock `protobuf:"bytes,2,rep,name=transaction_blocks,json=transactionBlocks,proto3" json:"transaction_blocks,omitempty"`
}

func (x *CreateTransactionBlockResponse) Reset() {
	*x = CreateTransactionBlockResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTransactionBlockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTransactionBlockResponse) ProtoMessage() {}

func (x *CreateTransactionBlockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTransactionBlockResponse.ProtoReflect.Descriptor instead.
func (*CreateTransactionBlockResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{64}
}

func (x *CreateTransactionBlockResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateTransactionBlockResponse) GetTransactionBlocks() []*TransactionBlock {
	if x != nil {
		return x.TransactionBlocks
	}
	return nil
}

type MonthlyTransactionCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year  int32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	Month int32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
	Count int64 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *MonthlyTransactionCount) Reset() {
	*x = MonthlyTransactionCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthlyTransactionCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyTransactionCount) ProtoMessage() {}

func (x *MonthlyTransactionCount) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyTransactionCount.ProtoReflect.Descriptor instead.
func (*MonthlyTransactionCount) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{65}
}

func (x *MonthlyTransactionCount) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *MonthlyTransactionCount) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *MonthlyTransactionCount) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// TimeAggregatedCount provides strictly-typed time keys with a single active
// time granularity via oneof.
// supports monthly, weekly, and daily aggregation based on AggregateOption
type TimeAggregatedCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Strictly-typed time key. Exactly one of the keys will be set depending on
	// the aggregation granularity used to compute this record.
	//
	// Types that are assignable to Time:
	//
	//	*TimeAggregatedCount_MonthKey
	//	*TimeAggregatedCount_WeekKey
	//	*TimeAggregatedCount_DayKey
	Time isTimeAggregatedCount_Time `protobuf_oneof:"time"`
	// Aggregated count/value for the time period
	Count int64 `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	// Human readable time period label (e.g., "2024-01", "2024-W15", "2024-01-15")
	TimePeriodLabel string `protobuf:"bytes,5,opt,name=time_period_label,json=timePeriodLabel,proto3" json:"time_period_label,omitempty"`
}

func (x *TimeAggregatedCount) Reset() {
	*x = TimeAggregatedCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeAggregatedCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeAggregatedCount) ProtoMessage() {}

func (x *TimeAggregatedCount) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeAggregatedCount.ProtoReflect.Descriptor instead.
func (*TimeAggregatedCount) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{66}
}

func (m *TimeAggregatedCount) GetTime() isTimeAggregatedCount_Time {
	if m != nil {
		return m.Time
	}
	return nil
}

func (x *TimeAggregatedCount) GetMonthKey() *MonthKey {
	if x, ok := x.GetTime().(*TimeAggregatedCount_MonthKey); ok {
		return x.MonthKey
	}
	return nil
}

func (x *TimeAggregatedCount) GetWeekKey() *WeekKey {
	if x, ok := x.GetTime().(*TimeAggregatedCount_WeekKey); ok {
		return x.WeekKey
	}
	return nil
}

func (x *TimeAggregatedCount) GetDayKey() *DayKey {
	if x, ok := x.GetTime().(*TimeAggregatedCount_DayKey); ok {
		return x.DayKey
	}
	return nil
}

func (x *TimeAggregatedCount) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *TimeAggregatedCount) GetTimePeriodLabel() string {
	if x != nil {
		return x.TimePeriodLabel
	}
	return ""
}

type isTimeAggregatedCount_Time interface {
	isTimeAggregatedCount_Time()
}

type TimeAggregatedCount_MonthKey struct {
	// Monthly aggregation key: year + month
	MonthKey *MonthKey `protobuf:"bytes,1,opt,name=month_key,json=monthKey,proto3,oneof"`
}

type TimeAggregatedCount_WeekKey struct {
	// Weekly aggregation key: year + ISO week_of_year
	WeekKey *WeekKey `protobuf:"bytes,2,opt,name=week_key,json=weekKey,proto3,oneof"`
}

type TimeAggregatedCount_DayKey struct {
	// Daily aggregation key: year + month + day
	DayKey *DayKey `protobuf:"bytes,3,opt,name=day_key,json=dayKey,proto3,oneof"`
}

func (*TimeAggregatedCount_MonthKey) isTimeAggregatedCount_Time() {}

func (*TimeAggregatedCount_WeekKey) isTimeAggregatedCount_Time() {}

func (*TimeAggregatedCount_DayKey) isTimeAggregatedCount_Time() {}

// MonthKey represents a calendar month within a specific year
type MonthKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year  int32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	Month int32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
}

func (x *MonthKey) Reset() {
	*x = MonthKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthKey) ProtoMessage() {}

func (x *MonthKey) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthKey.ProtoReflect.Descriptor instead.
func (*MonthKey) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{67}
}

func (x *MonthKey) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *MonthKey) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

// WeekKey represents an ISO week within a specific year
type WeekKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year  int32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	Month int32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
	Week  int32 `protobuf:"varint,3,opt,name=week,proto3" json:"week,omitempty"`
}

func (x *WeekKey) Reset() {
	*x = WeekKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeekKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeekKey) ProtoMessage() {}

func (x *WeekKey) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeekKey.ProtoReflect.Descriptor instead.
func (*WeekKey) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{68}
}

func (x *WeekKey) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *WeekKey) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *WeekKey) GetWeek() int32 {
	if x != nil {
		return x.Week
	}
	return 0
}

// DayKey represents a specific calendar day within a month and year
type DayKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year  int32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	Month int32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
	Day   int32 `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`
}

func (x *DayKey) Reset() {
	*x = DayKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DayKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DayKey) ProtoMessage() {}

func (x *DayKey) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DayKey.ProtoReflect.Descriptor instead.
func (*DayKey) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{69}
}

func (x *DayKey) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *DayKey) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *DayKey) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

type GetRiskTransactionAggregatedMetricsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor_id for whom the metrics are to be fetched
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// aggregate option for month over month, week over week, or day wise
	// for P0 we would only support month over month by default
	AggregateOption AggregateOption `protobuf:"varint,2,opt,name=aggregate_option,json=aggregateOption,proto3,enum=risk.case_management.AggregateOption" json:"aggregate_option,omitempty"`
	// date after which the data for metrics would be fetched
	// if not provided, defaults to 1 year ago
	FilterAfterDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=filter_after_date,json=filterAfterDate,proto3" json:"filter_after_date,omitempty"`
}

func (x *GetRiskTransactionAggregatedMetricsRequest) Reset() {
	*x = GetRiskTransactionAggregatedMetricsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRiskTransactionAggregatedMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRiskTransactionAggregatedMetricsRequest) ProtoMessage() {}

func (x *GetRiskTransactionAggregatedMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRiskTransactionAggregatedMetricsRequest.ProtoReflect.Descriptor instead.
func (*GetRiskTransactionAggregatedMetricsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{70}
}

func (x *GetRiskTransactionAggregatedMetricsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRiskTransactionAggregatedMetricsRequest) GetAggregateOption() AggregateOption {
	if x != nil {
		return x.AggregateOption
	}
	return AggregateOption_AGGREGATE_OPTION_UNSPECIFIED
}

func (x *GetRiskTransactionAggregatedMetricsRequest) GetFilterAfterDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FilterAfterDate
	}
	return nil
}

type RiskTransactionAggregatedMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Total credit transactions aggregated by time period
	CreditCounts []*TimeAggregatedCount `protobuf:"bytes,1,rep,name=credit_counts,json=creditCounts,proto3" json:"credit_counts,omitempty"`
	// Count of new P2P credit counterparties aggregated by time period
	LifetimeP2PCreditCounterparties []*TimeAggregatedCount `protobuf:"bytes,2,rep,name=lifetime_p2p_credit_counterparties,json=lifetimeP2pCreditCounterparties,proto3" json:"lifetime_p2p_credit_counterparties,omitempty"`
	// Average transaction value aggregated by time period
	Credits_25KPlus []*TimeAggregatedCount `protobuf:"bytes,4,rep,name=credits_25k_plus,json=credits25kPlus,proto3" json:"credits_25k_plus,omitempty"`
	// Aggregation type used for this response
	AggregationType AggregateOption `protobuf:"varint,6,opt,name=aggregation_type,json=aggregationType,proto3,enum=risk.case_management.AggregateOption" json:"aggregation_type,omitempty"`
}

func (x *RiskTransactionAggregatedMetrics) Reset() {
	*x = RiskTransactionAggregatedMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskTransactionAggregatedMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskTransactionAggregatedMetrics) ProtoMessage() {}

func (x *RiskTransactionAggregatedMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskTransactionAggregatedMetrics.ProtoReflect.Descriptor instead.
func (*RiskTransactionAggregatedMetrics) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{71}
}

func (x *RiskTransactionAggregatedMetrics) GetCreditCounts() []*TimeAggregatedCount {
	if x != nil {
		return x.CreditCounts
	}
	return nil
}

func (x *RiskTransactionAggregatedMetrics) GetLifetimeP2PCreditCounterparties() []*TimeAggregatedCount {
	if x != nil {
		return x.LifetimeP2PCreditCounterparties
	}
	return nil
}

func (x *RiskTransactionAggregatedMetrics) GetCredits_25KPlus() []*TimeAggregatedCount {
	if x != nil {
		return x.Credits_25KPlus
	}
	return nil
}

func (x *RiskTransactionAggregatedMetrics) GetAggregationType() AggregateOption {
	if x != nil {
		return x.AggregationType
	}
	return AggregateOption_AGGREGATE_OPTION_UNSPECIFIED
}

type GetRiskTransactionAggregatedMetricsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Aggregated metrics data
	Metrics *RiskTransactionAggregatedMetrics `protobuf:"bytes,2,opt,name=metrics,proto3" json:"metrics,omitempty"`
}

func (x *GetRiskTransactionAggregatedMetricsResponse) Reset() {
	*x = GetRiskTransactionAggregatedMetricsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRiskTransactionAggregatedMetricsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRiskTransactionAggregatedMetricsResponse) ProtoMessage() {}

func (x *GetRiskTransactionAggregatedMetricsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRiskTransactionAggregatedMetricsResponse.ProtoReflect.Descriptor instead.
func (*GetRiskTransactionAggregatedMetricsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{72}
}

func (x *GetRiskTransactionAggregatedMetricsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRiskTransactionAggregatedMetricsResponse) GetMetrics() *RiskTransactionAggregatedMetrics {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type CreateAlertsResponse_Failure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// details of failed alert
	Alert *RawAlert `protobuf:"bytes,1,opt,name=alert,proto3" json:"alert,omitempty"`
	// reason for failure
	// a string will be returned describing the failure
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *CreateAlertsResponse_Failure) Reset() {
	*x = CreateAlertsResponse_Failure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_service_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAlertsResponse_Failure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAlertsResponse_Failure) ProtoMessage() {}

func (x *CreateAlertsResponse_Failure) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_service_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAlertsResponse_Failure.ProtoReflect.Descriptor instead.
func (*CreateAlertsResponse_Failure) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_service_proto_rawDescGZIP(), []int{19, 0}
}

func (x *CreateAlertsResponse_Failure) GetAlert() *RawAlert {
	if x != nil {
		return x.Alert
	}
	return nil
}

func (x *CreateAlertsResponse_Failure) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

var File_api_risk_case_management_service_proto protoreflect.FileDescriptor

var file_api_risk_case_management_service_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x15,
	0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61,
	0x6c, 0x65, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x75,
	0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x23, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x73,
	0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63,
	0x2f, 0x70, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7a, 0x0a, 0x1d, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x59, 0x0a, 0x07,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x9e, 0x02, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x7f, 0x0a, 0x1e, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x48, 0x00, 0x52, 0x1b, 0x75, 0x69, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52,
	0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f,
	0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x42, 0x0a, 0x0a, 0x08,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x65, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0x76, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41,
	0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x13, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x7c, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x22, 0x2d, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x6b, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xe3, 0x01, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x0d, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x4c, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x22, 0xca, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69,
	0x7a, 0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x04, 0x63, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x2e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x04, 0x63, 0x61, 0x73, 0x65, 0x22, 0x50, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x53,
	0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x0d, 0x22, 0xa3, 0x01,
	0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x04, 0x63, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e,
	0x43, 0x61, 0x73, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04,
	0x63, 0x61, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d,
	0x61, 0x73, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61,
	0x73, 0x6b, 0x73, 0x22, 0xb3, 0x01, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x35, 0x0a, 0x04, 0x63, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x61, 0x73, 0x65,
	0x52, 0x04, 0x63, 0x61, 0x73, 0x65, 0x22, 0x41, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x19,
	0x0a, 0x15, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45,
	0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x0d, 0x22, 0x7f, 0x0a, 0x1e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5d, 0x0a, 0x12, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x89, 0x01, 0x0a, 0x1f, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x41, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x01,
	0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x22, 0xf9, 0x01, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x20, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x12, 0x2e, 0x0a, 0x07, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x06, 0x73, 0x6f, 0x72, 0x74,
	0x42, 0x79, 0x12, 0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x22, 0xbd, 0x02, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x58, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3b, 0x0a, 0x0c,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x57, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x03, 0x22, 0x8b, 0x02, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x54, 0x0a, 0x17, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x15, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x69,
	0x74, 0x68, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x50,
	0x0a, 0x10, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x0f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x22, 0x51, 0x0a, 0x0f, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x63, 0x63, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x63, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x92, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3a, 0x0a, 0x0c,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0xe4, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x2e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x0c,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43,
	0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x22,
	0x5a, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x06, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x61,
	0x77, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x08, 0x01,
	0x10, 0xe8, 0x07, 0x52, 0x06, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x22, 0xbf, 0x02, 0x0a, 0x14,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4e,
	0x0a, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6c,
	0x65, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x52, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x1a, 0x57,
	0x0a, 0x07, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x34, 0x0a, 0x05, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x52, 0x61, 0x77, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x22, 0xae, 0x02,
	0x0a, 0x12, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x33, 0x0a,
	0x07, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x06, 0x74, 0x6f, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x5b, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12,
	0x4d, 0x0a, 0x10, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e,
	0x74, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x22, 0x9b,
	0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x5e, 0x0a, 0x0b,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x33, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01,
	0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x22, 0x9e, 0x04, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35,
	0x0a, 0x04, 0x63, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x52,
	0x04, 0x63, 0x61, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x07, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x46, 0x0a, 0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x0c,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x06,
	0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x57, 0x69, 0x74, 0x68, 0x52, 0x75, 0x6c,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x06, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x72, 0x65, 0x68, 0x6f, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x72, 0x65, 0x68, 0x6f, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x83, 0x01, 0x0a, 0x24, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x61, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x34, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x57, 0x69,
	0x74, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x1f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x57,
	0x69, 0x74, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x22, 0xd8, 0x01,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x63, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x59, 0x0a, 0x11, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x49, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x10, 0x69, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x3a,
	0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xf5, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x42, 0x0a, 0x06, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x57, 0x69, 0x74, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x06, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x12, 0x3b, 0x0a,
	0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45,
	0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05,
	0x22, 0xe3, 0x02, 0x0a, 0x14, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x0b, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x5a, 0x0a, 0x11, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x10, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x4b, 0x0a, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x0d, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x72, 0x0a, 0x15, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72,
	0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x22, 0x56, 0x0a, 0x14, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3e, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x22, 0x72, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52,
	0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xb0, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43,
	0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x05, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x12, 0x54, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0xc9, 0x01, 0x0a, 0x14, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x03, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x69, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x22, 0xf1, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5f, 0x0a, 0x13, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03,
	0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x22, 0x62, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x47, 0x0a, 0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x75, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x22,
	0xb1, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x05, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x56, 0x0a, 0x12, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x52, 0x11, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x22, 0xd6, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d,
	0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x22, 0x43, 0x0a, 0x11,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2e, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x72, 0x75, 0x6c,
	0x65, 0x22, 0x6f, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x34, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c,
	0x10, 0x0d, 0x22, 0x91, 0x02, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x73, 0x12, 0x21, 0x0a,
	0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x40, 0x0a, 0x0a, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x48, 0x00, 0x52, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x40, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x42, 0x79, 0x42, 0x11, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x62, 0x79,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xb6, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x30, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75,
	0x6c, 0x65, 0x73, 0x22, 0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x52,
	0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10,
	0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22,
	0x8b, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x04, 0x72, 0x75, 0x6c, 0x65, 0x12, 0x46, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x22, 0x6f, 0x0a,
	0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03,
	0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x85,
	0x02, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x43, 0x61, 0x73,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4a, 0x0a, 0x07, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x43, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x4a, 0x0a, 0x07, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x62, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x53, 0x6f, 0x72, 0x74, 0x42, 0x79, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x6f, 0x72, 0x74, 0x42,
	0x79, 0x12, 0x53, 0x0a, 0x14, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x12, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xfb, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x12, 0x4c, 0x0a, 0x15, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x13, 0x70, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x34,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x14,
	0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0x05, 0x22, 0x42, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x46, 0x69, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xd6, 0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74,
	0x46, 0x69, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68,
	0x69, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x7d, 0x0a, 0x14, 0x66, 0x69, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x46, 0x69, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x68, 0x69, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x66, 0x69, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x1a, 0x45,
	0x0a, 0x17, 0x46, 0x69, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x68, 0x69, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x14, 0x0a,
	0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x0d, 0x22, 0x32, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x66,
	0x6f, 0x72, 0x6d, 0x49, 0x64, 0x22, 0xe5, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72,
	0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49,
	0x0a, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x0a, 0x04, 0x66, 0x6f, 0x72,
	0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66,
	0x6f, 0x72, 0x6d, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0x2d,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x80, 0x01,
	0x0a, 0x11, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x66,
	0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73,
	0x22, 0xa2, 0x01, 0x0a, 0x12, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x67, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x0d, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x64, 0x12, 0x15,
	0x0a, 0x11, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54,
	0x54, 0x45, 0x44, 0x10, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4a, 0x0a, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x51, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64,
	0x46, 0x6f, 0x72, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x22, 0xbc, 0x01, 0x0a, 0x11, 0x4c, 0x69,
	0x73, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x05, 0x66, 0x6f,
	0x72, 0x6d, 0x73, 0x22, 0x43, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x43, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41,
	0x6c, 0x65, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xc1, 0x01,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x42, 0x0a, 0x06, 0x61, 0x6c, 0x65, 0x72,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x57, 0x69, 0x74, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x06, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x22, 0x43, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x0d, 0x22, 0x53, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x46, 0x6f, 0x72,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xa3, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x0a, 0x04, 0x66, 0x6f, 0x72, 0x6d,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0x2d, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x8c, 0x02, 0x0a,
	0x23, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06,
	0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x67, 0x0a, 0x15, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x67,
	0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x13, 0x73, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x5a, 0x0a, 0x11, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x10, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x22, 0xe2, 0x01, 0x0a, 0x24,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x50, 0x0a, 0x10, 0x73, 0x75, 0x67,
	0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x67, 0x67, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x73, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x43, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d,
	0x22, 0x9a, 0x01, 0x0a, 0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x0b, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x22, 0x8f, 0x01,
	0x0a, 0x23, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x43, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09,
	0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22,
	0xd8, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x24, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x48, 0x00, 0x52, 0x07, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x0a, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x0c, 0x0a, 0x0a,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xe6, 0x01, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x55, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x22, 0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12,
	0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x0d, 0x22, 0x80, 0x01, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x08, 0x01, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x22, 0xd2, 0x01, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x55,
	0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x59, 0x0a, 0x17, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x93, 0x02, 0x0a, 0x13, 0x54, 0x69, 0x6d, 0x65, 0x41,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d,
	0x0a, 0x09, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x4b, 0x65,
	0x79, 0x48, 0x00, 0x52, 0x08, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x3a, 0x0a,
	0x08, 0x77, 0x65, 0x65, 0x6b, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x65, 0x6b, 0x4b, 0x65, 0x79, 0x48, 0x00,
	0x52, 0x07, 0x77, 0x65, 0x65, 0x6b, 0x4b, 0x65, 0x79, 0x12, 0x37, 0x0a, 0x07, 0x64, 0x61, 0x79,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x44, 0x61, 0x79, 0x4b, 0x65, 0x79, 0x48, 0x00, 0x52, 0x06, 0x64, 0x61, 0x79, 0x4b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x42, 0x06, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x4c, 0x0a, 0x08,
	0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06, 0x18, 0xb4, 0x10,
	0x28, 0xb2, 0x0f, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x12, 0x1f, 0x0a, 0x05, 0x6d, 0x6f, 0x6e,
	0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18,
	0x0c, 0x28, 0x01, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x22, 0x6a, 0x0a, 0x07, 0x57, 0x65,
	0x65, 0x6b, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06, 0x18, 0xb4, 0x10, 0x28, 0xb2, 0x0f,
	0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x12, 0x1f, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x0c, 0x28, 0x01,
	0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x04, 0x77, 0x65, 0x65, 0x6b, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x35, 0x28, 0x01,
	0x52, 0x04, 0x77, 0x65, 0x65, 0x6b, 0x22, 0x67, 0x0a, 0x06, 0x44, 0x61, 0x79, 0x4b, 0x65, 0x79,
	0x12, 0x1f, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b,
	0xfa, 0x42, 0x08, 0x1a, 0x06, 0x18, 0xb4, 0x10, 0x28, 0xb2, 0x0f, 0x52, 0x04, 0x79, 0x65, 0x61,
	0x72, 0x12, 0x1f, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x0c, 0x28, 0x01, 0x52, 0x05, 0x6d, 0x6f, 0x6e,
	0x74, 0x68, 0x12, 0x1b, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x1f, 0x28, 0x01, 0x52, 0x03, 0x64, 0x61, 0x79, 0x22,
	0xea, 0x01, 0x0a, 0x2a, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x50, 0x0a, 0x10, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x61,
	0x66, 0x74, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x41, 0x66, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x22, 0x91, 0x03, 0x0a,
	0x20, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x12, 0x4e, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x12, 0x76, 0x0a, 0x22, 0x6c, 0x69, 0x66, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x32,
	0x70, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x61, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x1f, 0x6c, 0x69, 0x66, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x50, 0x32, 0x70, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x61, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x10, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x73, 0x5f, 0x32, 0x35, 0x6b, 0x5f, 0x70, 0x6c, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0e,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x32, 0x35, 0x6b, 0x50, 0x6c, 0x75, 0x73, 0x12, 0x50,
	0x0a, 0x10, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x22, 0xa4, 0x01, 0x0a, 0x2b, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65,
	0x64, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x50, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69,
	0x73, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x07,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2a, 0x9e, 0x01, 0x0a, 0x0f, 0x41, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x1c, 0x41,
	0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a,
	0x21, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x4e,
	0x54, 0x48, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54,
	0x45, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x5f, 0x4f, 0x56,
	0x45, 0x52, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x47, 0x47,
	0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x41,
	0x59, 0x5f, 0x57, 0x49, 0x53, 0x45, 0x10, 0x03, 0x32, 0x96, 0x1d, 0x0a, 0x0e, 0x43, 0x61, 0x73,
	0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x5c, 0x0a, 0x09, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x26, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x0c, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x12, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x9b, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x12, 0x3b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x71,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x2d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x6e, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x41, 0x6c,
	0x65, 0x72, 0x74, 0x73, 0x12, 0x2c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4c,
	0x69, 0x6e, 0x6b, 0x65, 0x64, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e,
	0x6b, 0x65, 0x64, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x68, 0x0a, 0x0d, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72,
	0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x0d, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2a, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85, 0x01, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x32, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x03, 0x88, 0x02, 0x01, 0x12, 0x71, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2c, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x26, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x75, 0x6c, 0x65, 0x12, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x34, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x5f, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x12, 0x27,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x77, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69,
	0x7a, 0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x12, 0x2f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x0f, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x2c, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x43,
	0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x43, 0x61, 0x73,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x46, 0x69, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x68, 0x69, 0x70, 0x12, 0x32, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46,
	0x69, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x46, 0x69, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x68, 0x69, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a,
	0x07, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x0a, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x46,
	0x6f, 0x72, 0x6d, 0x12, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x73, 0x12, 0x26, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x46,
	0x6f, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x73, 0x12, 0x26, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x5f, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x61, 0x67, 0x73,
	0x12, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x61,
	0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x74, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x16, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x71, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x12, 0x2d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72,
	0x6d, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x95, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x67,
	0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x52,
	0x75, 0x6c, 0x65, 0x12, 0x39, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x46, 0x6f, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x1b, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54,
	0x79, 0x70, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x38, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65,
	0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x7d, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x31, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83,
	0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x33, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xaa, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x40, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x64, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_service_proto_rawDescOnce sync.Once
	file_api_risk_case_management_service_proto_rawDescData = file_api_risk_case_management_service_proto_rawDesc
)

func file_api_risk_case_management_service_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_service_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_service_proto_rawDescData)
	})
	return file_api_risk_case_management_service_proto_rawDescData
}

var file_api_risk_case_management_service_proto_enumTypes = make([]protoimpl.EnumInfo, 32)
var file_api_risk_case_management_service_proto_msgTypes = make([]protoimpl.MessageInfo, 75)
var file_api_risk_case_management_service_proto_goTypes = []interface{}{
	(AggregateOption)(0),                                // 0: risk.case_management.AggregateOption
	(ListAllowedAnnotationsResponse_Status)(0),          // 1: risk.case_management.ListAllowedAnnotationsResponse.Status
	(CreateAnnotationsResponse_Status)(0),               // 2: risk.case_management.CreateAnnotationsResponse.Status
	(GetAllTagsResponse_Status)(0),                      // 3: risk.case_management.GetAllTagsResponse.Status
	(GetPrioritizedCaseResponse_Status)(0),              // 4: risk.case_management.GetPrioritizedCaseResponse.Status
	(UpdateCaseResponse_Status)(0),                      // 5: risk.case_management.UpdateCaseResponse.Status
	(CreateAllowedAnnotationResponse_Status)(0),         // 6: risk.case_management.CreateAllowedAnnotationResponse.Status
	(GetTransactionDetailsForReviewResponse_Status)(0),  // 7: risk.case_management.GetTransactionDetailsForReviewResponse.Status
	(ListCasesResponse_Status)(0),                       // 8: risk.case_management.ListCasesResponse.Status
	(CreateAlertsResponse_Status)(0),                    // 9: risk.case_management.CreateAlertsResponse.Status
	(GetReviewDetailsResponse_Status)(0),                // 10: risk.case_management.GetReviewDetailsResponse.Status
	(GetLinkedAlertsResponse_Status)(0),                 // 11: risk.case_management.GetLinkedAlertsResponse.Status
	(PerformActionResponse_Status)(0),                   // 12: risk.case_management.PerformActionResponse.Status
	(CreateCommentResponse_Status)(0),                   // 13: risk.case_management.CreateCommentResponse.Status
	(ListCommentsResponse_Status)(0),                    // 14: risk.case_management.ListCommentsResponse.Status
	(GetAllowedAnnotationsResponse_Status)(0),           // 15: risk.case_management.GetAllowedAnnotationsResponse.Status
	(CreateAnnotationResponse_Status)(0),                // 16: risk.case_management.CreateAnnotationResponse.Status
	(ListAnnotationsResponse_Status)(0),                 // 17: risk.case_management.ListAnnotationsResponse.Status
	(CreateRuleResponse_Status)(0),                      // 18: risk.case_management.CreateRuleResponse.Status
	(ListRulesResponse_Status)(0),                       // 19: risk.case_management.ListRulesResponse.Status
	(UpdateRuleResponse_Status)(0),                      // 20: risk.case_management.UpdateRuleResponse.Status
	(ListSortedCasesResponse_Status)(0),                 // 21: risk.case_management.ListSortedCasesResponse.Status
	(GetFiUserRelationshipResponse_Status)(0),           // 22: risk.case_management.GetFiUserRelationshipResponse.Status
	(GetFormResponse_Status)(0),                         // 23: risk.case_management.GetFormResponse.Status
	(SubmitFormResponse_Status)(0),                      // 24: risk.case_management.SubmitFormResponse.Status
	(ListFormsResponse_Status)(0),                       // 25: risk.case_management.ListFormsResponse.Status
	(GetAlertsResponse_Status)(0),                       // 26: risk.case_management.GetAlertsResponse.Status
	(GetFormsForActorResponse_Status)(0),                // 27: risk.case_management.GetFormsForActorResponse.Status
	(CreateSuggestedActionForRuleResponse_Status)(0),    // 28: risk.case_management.CreateSuggestedActionForRuleResponse.Status
	(CreateRuleReviewTypeMappingResponse_Status)(0),     // 29: risk.case_management.CreateRuleReviewTypeMappingResponse.Status
	(GetTransactionBlocksResponse_Status)(0),            // 30: risk.case_management.GetTransactionBlocksResponse.Status
	(CreateTransactionBlockResponse_Status)(0),          // 31: risk.case_management.CreateTransactionBlockResponse.Status
	(*ListAllowedAnnotationsRequest)(nil),               // 32: risk.case_management.ListAllowedAnnotationsRequest
	(*ListAllowedAnnotationsResponse)(nil),              // 33: risk.case_management.ListAllowedAnnotationsResponse
	(*CreateAnnotationsRequest)(nil),                    // 34: risk.case_management.CreateAnnotationsRequest
	(*CreateAnnotationsResponse)(nil),                   // 35: risk.case_management.CreateAnnotationsResponse
	(*GetAllTagsRequest)(nil),                           // 36: risk.case_management.GetAllTagsRequest
	(*GetAllTagsResponse)(nil),                          // 37: risk.case_management.GetAllTagsResponse
	(*GetPrioritizedCaseRequest)(nil),                   // 38: risk.case_management.GetPrioritizedCaseRequest
	(*GetPrioritizedCaseResponse)(nil),                  // 39: risk.case_management.GetPrioritizedCaseResponse
	(*UpdateCaseRequest)(nil),                           // 40: risk.case_management.UpdateCaseRequest
	(*UpdateCaseResponse)(nil),                          // 41: risk.case_management.UpdateCaseResponse
	(*CreateAllowedAnnotationRequest)(nil),              // 42: risk.case_management.CreateAllowedAnnotationRequest
	(*CreateAllowedAnnotationResponse)(nil),             // 43: risk.case_management.CreateAllowedAnnotationResponse
	(*GetTransactionDetailsForReviewRequest)(nil),       // 44: risk.case_management.GetTransactionDetailsForReviewRequest
	(*GetTransactionDetailsForReviewResponse)(nil),      // 45: risk.case_management.GetTransactionDetailsForReviewResponse
	(*TransactionDetail)(nil),                           // 46: risk.case_management.TransactionDetail
	(*MerchantDetails)(nil),                             // 47: risk.case_management.MerchantDetails
	(*ListCasesRequest)(nil),                            // 48: risk.case_management.ListCasesRequest
	(*ListCasesResponse)(nil),                           // 49: risk.case_management.ListCasesResponse
	(*CreateAlertsRequest)(nil),                         // 50: risk.case_management.CreateAlertsRequest
	(*CreateAlertsResponse)(nil),                        // 51: risk.case_management.CreateAlertsResponse
	(*TransactionFilters)(nil),                          // 52: risk.case_management.TransactionFilters
	(*GetReviewDetailsRequest)(nil),                     // 53: risk.case_management.GetReviewDetailsRequest
	(*GetReviewDetailsResponse)(nil),                    // 54: risk.case_management.GetReviewDetailsResponse
	(*GetLinkedAlertsRequest)(nil),                      // 55: risk.case_management.GetLinkedAlertsRequest
	(*GetLinkedAlertsResponse)(nil),                     // 56: risk.case_management.GetLinkedAlertsResponse
	(*PerformActionRequest)(nil),                        // 57: risk.case_management.PerformActionRequest
	(*PerformActionResponse)(nil),                       // 58: risk.case_management.PerformActionResponse
	(*CreateCommentRequest)(nil),                        // 59: risk.case_management.CreateCommentRequest
	(*CreateCommentResponse)(nil),                       // 60: risk.case_management.CreateCommentResponse
	(*ListCommentsRequest)(nil),                         // 61: risk.case_management.ListCommentsRequest
	(*ListCommentsResponse)(nil),                        // 62: risk.case_management.ListCommentsResponse
	(*GetAllowedAnnotationsRequest)(nil),                // 63: risk.case_management.GetAllowedAnnotationsRequest
	(*GetAllowedAnnotationsResponse)(nil),               // 64: risk.case_management.GetAllowedAnnotationsResponse
	(*CreateAnnotationRequest)(nil),                     // 65: risk.case_management.CreateAnnotationRequest
	(*CreateAnnotationResponse)(nil),                    // 66: risk.case_management.CreateAnnotationResponse
	(*ListAnnotationsRequest)(nil),                      // 67: risk.case_management.ListAnnotationsRequest
	(*ListAnnotationsResponse)(nil),                     // 68: risk.case_management.ListAnnotationsResponse
	(*CreateRuleRequest)(nil),                           // 69: risk.case_management.CreateRuleRequest
	(*CreateRuleResponse)(nil),                          // 70: risk.case_management.CreateRuleResponse
	(*ListRulesRequest)(nil),                            // 71: risk.case_management.ListRulesRequest
	(*ListRulesResponse)(nil),                           // 72: risk.case_management.ListRulesResponse
	(*UpdateRuleRequest)(nil),                           // 73: risk.case_management.UpdateRuleRequest
	(*UpdateRuleResponse)(nil),                          // 74: risk.case_management.UpdateRuleResponse
	(*ListSortedCasesRequest)(nil),                      // 75: risk.case_management.ListSortedCasesRequest
	(*ListSortedCasesResponse)(nil),                     // 76: risk.case_management.ListSortedCasesResponse
	(*GetFiUserRelationshipRequest)(nil),                // 77: risk.case_management.GetFiUserRelationshipRequest
	(*GetFiUserRelationshipResponse)(nil),               // 78: risk.case_management.GetFiUserRelationshipResponse
	(*GetFormRequest)(nil),                              // 79: risk.case_management.GetFormRequest
	(*GetFormResponse)(nil),                             // 80: risk.case_management.GetFormResponse
	(*SubmitFormRequest)(nil),                           // 81: risk.case_management.SubmitFormRequest
	(*SubmitFormResponse)(nil),                          // 82: risk.case_management.SubmitFormResponse
	(*ListFormsRequest)(nil),                            // 83: risk.case_management.ListFormsRequest
	(*ListFormsResponse)(nil),                           // 84: risk.case_management.ListFormsResponse
	(*GetAlertsRequest)(nil),                            // 85: risk.case_management.GetAlertsRequest
	(*GetAlertsResponse)(nil),                           // 86: risk.case_management.GetAlertsResponse
	(*GetFormsForActorRequest)(nil),                     // 87: risk.case_management.GetFormsForActorRequest
	(*GetFormsForActorResponse)(nil),                    // 88: risk.case_management.GetFormsForActorResponse
	(*CreateSuggestedActionForRuleRequest)(nil),         // 89: risk.case_management.CreateSuggestedActionForRuleRequest
	(*CreateSuggestedActionForRuleResponse)(nil),        // 90: risk.case_management.CreateSuggestedActionForRuleResponse
	(*CreateRuleReviewTypeMappingRequest)(nil),          // 91: risk.case_management.CreateRuleReviewTypeMappingRequest
	(*CreateRuleReviewTypeMappingResponse)(nil),         // 92: risk.case_management.CreateRuleReviewTypeMappingResponse
	(*GetTransactionBlocksRequest)(nil),                 // 93: risk.case_management.GetTransactionBlocksRequest
	(*GetTransactionBlocksResponse)(nil),                // 94: risk.case_management.GetTransactionBlocksResponse
	(*CreateTransactionBlockRequest)(nil),               // 95: risk.case_management.CreateTransactionBlockRequest
	(*CreateTransactionBlockResponse)(nil),              // 96: risk.case_management.CreateTransactionBlockResponse
	(*MonthlyTransactionCount)(nil),                     // 97: risk.case_management.MonthlyTransactionCount
	(*TimeAggregatedCount)(nil),                         // 98: risk.case_management.TimeAggregatedCount
	(*MonthKey)(nil),                                    // 99: risk.case_management.MonthKey
	(*WeekKey)(nil),                                     // 100: risk.case_management.WeekKey
	(*DayKey)(nil),                                      // 101: risk.case_management.DayKey
	(*GetRiskTransactionAggregatedMetricsRequest)(nil),  // 102: risk.case_management.GetRiskTransactionAggregatedMetricsRequest
	(*RiskTransactionAggregatedMetrics)(nil),            // 103: risk.case_management.RiskTransactionAggregatedMetrics
	(*GetRiskTransactionAggregatedMetricsResponse)(nil), // 104: risk.case_management.GetRiskTransactionAggregatedMetricsResponse
	(*CreateAlertsResponse_Failure)(nil),                // 105: risk.case_management.CreateAlertsResponse.Failure
	nil,                                                 // 106: risk.case_management.GetFiUserRelationshipResponse.FiUserRelationshipEntry
	(*review.AllowedAnnotationFilters)(nil),             // 107: risk.case_management.review.AllowedAnnotationFilters
	(*rpc.Status)(nil),                                  // 108: rpc.Status
	(*review.UIElementAllowedAnnotations)(nil),          // 109: risk.case_management.review.UIElementAllowedAnnotations
	(*review.Annotation)(nil),                           // 110: risk.case_management.review.Annotation
	(review.ReviewType)(0),                              // 111: risk.case_management.review.ReviewType
	(*review.Case)(nil),                                 // 112: risk.case_management.review.Case
	(review.CaseFieldMask)(0),                           // 113: risk.case_management.review.CaseFieldMask
	(*review.AllowedAnnotation)(nil),                    // 114: risk.case_management.review.AllowedAnnotation
	(order.OrderFieldMask)(0),                           // 115: order.OrderFieldMask
	(*rpc.PageContextRequest)(nil),                      // 116: rpc.PageContextRequest
	(*rpc.PageContextResponse)(nil),                     // 117: rpc.PageContextResponse
	(*order.OrderWithTransactions)(nil),                 // 118: order.OrderWithTransactions
	(*review.CaseFilters)(nil),                          // 119: risk.case_management.review.CaseFilters
	(*RawAlert)(nil),                                    // 120: risk.case_management.RawAlert
	(*timestamppb.Timestamp)(nil),                       // 121: google.protobuf.Timestamp
	(review.TransactionReviewOrderStatusFilter)(0),      // 122: risk.case_management.review.TransactionReviewOrderStatusFilter
	(payment.AccountingEntryType)(0),                    // 123: order.payment.AccountingEntryType
	(review.ReviewDetailsFieldMask)(0),                  // 124: risk.case_management.review.ReviewDetailsFieldMask
	(*review.Action)(nil),                               // 125: risk.case_management.review.Action
	(*AlertWithRuleDetails)(nil),                        // 126: risk.case_management.AlertWithRuleDetails
	(*AlertAggregateForActorWithRule)(nil),              // 127: risk.case_management.AlertAggregateForActorWithRule
	(enums.InformationLevel)(0),                         // 128: risk.case_management.enums.InformationLevel
	(review.ActionType)(0),                              // 129: risk.case_management.review.ActionType
	(*review.ActionParameters)(nil),                     // 130: risk.case_management.review.ActionParameters
	(review.ActionSource)(0),                            // 131: risk.case_management.review.ActionSource
	(*review.Comment)(nil),                              // 132: risk.case_management.review.Comment
	(*review.CommentQuery)(nil),                         // 133: risk.case_management.review.CommentQuery
	(*review.CommentFilters)(nil),                       // 134: risk.case_management.review.CommentFilters
	(*review.AllowedAnnotationQuery)(nil),               // 135: risk.case_management.review.AllowedAnnotationQuery
	(*AnnotationQuery)(nil),                             // 136: risk.case_management.AnnotationQuery
	(*AnnotationFilters)(nil),                           // 137: risk.case_management.AnnotationFilters
	(*CombinedAnnotation)(nil),                          // 138: risk.case_management.CombinedAnnotation
	(*Rule)(nil),                                        // 139: risk.case_management.Rule
	(RuleGroup)(0),                                      // 140: risk.case_management.RuleGroup
	(RuleFieldMask)(0),                                  // 141: risk.case_management.RuleFieldMask
	(*review.SortableCaseFilters)(nil),                  // 142: risk.case_management.review.SortableCaseFilters
	(*review.CaseSortBy)(nil),                           // 143: risk.case_management.review.CaseSortBy
	(*form.ExtendedQuestion)(nil),                       // 144: risk.case_management.form.ExtendedQuestion
	(*form.Form)(nil),                                   // 145: risk.case_management.form.Form
	(*form.QuestionResponse)(nil),                       // 146: risk.case_management.form.QuestionResponse
	(*form.FormFilters)(nil),                            // 147: risk.case_management.form.FormFilters
	(form.ExtendedFormFieldMask)(0),                     // 148: risk.case_management.form.ExtendedFormFieldMask
	(*form.ExtendedForm)(nil),                           // 149: risk.case_management.form.ExtendedForm
	(SuggestedActionType)(0),                            // 150: risk.case_management.SuggestedActionType
	(*SuggestedAction)(nil),                             // 151: risk.case_management.SuggestedAction
	(TransactionBlockType)(0),                           // 152: risk.case_management.TransactionBlockType
	(*TransactionBlock)(nil),                            // 153: risk.case_management.TransactionBlock
}
var file_api_risk_case_management_service_proto_depIdxs = []int32{
	107, // 0: risk.case_management.ListAllowedAnnotationsRequest.filters:type_name -> risk.case_management.review.AllowedAnnotationFilters
	108, // 1: risk.case_management.ListAllowedAnnotationsResponse.status:type_name -> rpc.Status
	109, // 2: risk.case_management.ListAllowedAnnotationsResponse.ui_element_allowed_annotations:type_name -> risk.case_management.review.UIElementAllowedAnnotations
	110, // 3: risk.case_management.CreateAnnotationsRequest.annotations:type_name -> risk.case_management.review.Annotation
	108, // 4: risk.case_management.CreateAnnotationsResponse.status:type_name -> rpc.Status
	108, // 5: risk.case_management.GetAllTagsResponse.status:type_name -> rpc.Status
	111, // 6: risk.case_management.GetPrioritizedCaseRequest.review_type:type_name -> risk.case_management.review.ReviewType
	111, // 7: risk.case_management.GetPrioritizedCaseRequest.review_types:type_name -> risk.case_management.review.ReviewType
	108, // 8: risk.case_management.GetPrioritizedCaseResponse.status:type_name -> rpc.Status
	112, // 9: risk.case_management.GetPrioritizedCaseResponse.case:type_name -> risk.case_management.review.Case
	112, // 10: risk.case_management.UpdateCaseRequest.case:type_name -> risk.case_management.review.Case
	113, // 11: risk.case_management.UpdateCaseRequest.update_masks:type_name -> risk.case_management.review.CaseFieldMask
	108, // 12: risk.case_management.UpdateCaseResponse.status:type_name -> rpc.Status
	112, // 13: risk.case_management.UpdateCaseResponse.case:type_name -> risk.case_management.review.Case
	114, // 14: risk.case_management.CreateAllowedAnnotationRequest.allowed_annotation:type_name -> risk.case_management.review.AllowedAnnotation
	108, // 15: risk.case_management.CreateAllowedAnnotationResponse.status:type_name -> rpc.Status
	115, // 16: risk.case_management.GetTransactionDetailsForReviewRequest.sort_by:type_name -> order.OrderFieldMask
	52,  // 17: risk.case_management.GetTransactionDetailsForReviewRequest.filters:type_name -> risk.case_management.TransactionFilters
	116, // 18: risk.case_management.GetTransactionDetailsForReviewRequest.page_context:type_name -> rpc.PageContextRequest
	108, // 19: risk.case_management.GetTransactionDetailsForReviewResponse.status:type_name -> rpc.Status
	46,  // 20: risk.case_management.GetTransactionDetailsForReviewResponse.transaction_details:type_name -> risk.case_management.TransactionDetail
	117, // 21: risk.case_management.GetTransactionDetailsForReviewResponse.page_context:type_name -> rpc.PageContextResponse
	118, // 22: risk.case_management.TransactionDetail.order_with_transactions:type_name -> order.OrderWithTransactions
	47,  // 23: risk.case_management.TransactionDetail.merchant_details:type_name -> risk.case_management.MerchantDetails
	119, // 24: risk.case_management.ListCasesRequest.filters:type_name -> risk.case_management.review.CaseFilters
	116, // 25: risk.case_management.ListCasesRequest.page_context:type_name -> rpc.PageContextRequest
	108, // 26: risk.case_management.ListCasesResponse.status:type_name -> rpc.Status
	112, // 27: risk.case_management.ListCasesResponse.cases:type_name -> risk.case_management.review.Case
	117, // 28: risk.case_management.ListCasesResponse.page_context:type_name -> rpc.PageContextResponse
	120, // 29: risk.case_management.CreateAlertsRequest.alerts:type_name -> risk.case_management.RawAlert
	108, // 30: risk.case_management.CreateAlertsResponse.status:type_name -> rpc.Status
	105, // 31: risk.case_management.CreateAlertsResponse.failures:type_name -> risk.case_management.CreateAlertsResponse.Failure
	121, // 32: risk.case_management.TransactionFilters.from_time:type_name -> google.protobuf.Timestamp
	121, // 33: risk.case_management.TransactionFilters.to_time:type_name -> google.protobuf.Timestamp
	122, // 34: risk.case_management.TransactionFilters.statuses:type_name -> risk.case_management.review.TransactionReviewOrderStatusFilter
	123, // 35: risk.case_management.TransactionFilters.accounting_entry:type_name -> order.payment.AccountingEntryType
	124, // 36: risk.case_management.GetReviewDetailsRequest.field_masks:type_name -> risk.case_management.review.ReviewDetailsFieldMask
	108, // 37: risk.case_management.GetReviewDetailsResponse.status:type_name -> rpc.Status
	112, // 38: risk.case_management.GetReviewDetailsResponse.case:type_name -> risk.case_management.review.Case
	125, // 39: risk.case_management.GetReviewDetailsResponse.actions:type_name -> risk.case_management.review.Action
	112, // 40: risk.case_management.GetReviewDetailsResponse.related_cases:type_name -> risk.case_management.review.Case
	126, // 41: risk.case_management.GetReviewDetailsResponse.alerts:type_name -> risk.case_management.AlertWithRuleDetails
	127, // 42: risk.case_management.GetReviewDetailsResponse.alert_aggregates_for_actor_with_rule:type_name -> risk.case_management.AlertAggregateForActorWithRule
	128, // 43: risk.case_management.GetLinkedAlertsRequest.information_level:type_name -> risk.case_management.enums.InformationLevel
	116, // 44: risk.case_management.GetLinkedAlertsRequest.page_context:type_name -> rpc.PageContextRequest
	108, // 45: risk.case_management.GetLinkedAlertsResponse.status:type_name -> rpc.Status
	126, // 46: risk.case_management.GetLinkedAlertsResponse.alerts:type_name -> risk.case_management.AlertWithRuleDetails
	117, // 47: risk.case_management.GetLinkedAlertsResponse.page_context:type_name -> rpc.PageContextResponse
	129, // 48: risk.case_management.PerformActionRequest.action_type:type_name -> risk.case_management.review.ActionType
	130, // 49: risk.case_management.PerformActionRequest.action_parameters:type_name -> risk.case_management.review.ActionParameters
	131, // 50: risk.case_management.PerformActionRequest.source:type_name -> risk.case_management.review.ActionSource
	108, // 51: risk.case_management.PerformActionResponse.status:type_name -> rpc.Status
	132, // 52: risk.case_management.CreateCommentRequest.comment:type_name -> risk.case_management.review.Comment
	108, // 53: risk.case_management.CreateCommentResponse.status:type_name -> rpc.Status
	133, // 54: risk.case_management.ListCommentsRequest.query:type_name -> risk.case_management.review.CommentQuery
	134, // 55: risk.case_management.ListCommentsRequest.comment_filters:type_name -> risk.case_management.review.CommentFilters
	108, // 56: risk.case_management.ListCommentsResponse.status:type_name -> rpc.Status
	132, // 57: risk.case_management.ListCommentsResponse.comments:type_name -> risk.case_management.review.Comment
	135, // 58: risk.case_management.GetAllowedAnnotationsRequest.query:type_name -> risk.case_management.review.AllowedAnnotationQuery
	108, // 59: risk.case_management.GetAllowedAnnotationsResponse.status:type_name -> rpc.Status
	114, // 60: risk.case_management.GetAllowedAnnotationsResponse.allowed_annotations:type_name -> risk.case_management.review.AllowedAnnotation
	110, // 61: risk.case_management.CreateAnnotationRequest.annotation:type_name -> risk.case_management.review.Annotation
	108, // 62: risk.case_management.CreateAnnotationResponse.status:type_name -> rpc.Status
	136, // 63: risk.case_management.ListAnnotationsRequest.query:type_name -> risk.case_management.AnnotationQuery
	137, // 64: risk.case_management.ListAnnotationsRequest.annotation_filters:type_name -> risk.case_management.AnnotationFilters
	108, // 65: risk.case_management.ListAnnotationsResponse.status:type_name -> rpc.Status
	138, // 66: risk.case_management.ListAnnotationsResponse.annotations:type_name -> risk.case_management.CombinedAnnotation
	139, // 67: risk.case_management.CreateRuleRequest.rule:type_name -> risk.case_management.Rule
	108, // 68: risk.case_management.CreateRuleResponse.status:type_name -> rpc.Status
	140, // 69: risk.case_management.ListRulesRequest.rule_group:type_name -> risk.case_management.RuleGroup
	141, // 70: risk.case_management.ListRulesRequest.filter_by:type_name -> risk.case_management.RuleFieldMask
	108, // 71: risk.case_management.ListRulesResponse.status:type_name -> rpc.Status
	139, // 72: risk.case_management.ListRulesResponse.rules:type_name -> risk.case_management.Rule
	139, // 73: risk.case_management.UpdateRuleRequest.rule:type_name -> risk.case_management.Rule
	141, // 74: risk.case_management.UpdateRuleRequest.update_masks:type_name -> risk.case_management.RuleFieldMask
	108, // 75: risk.case_management.UpdateRuleResponse.status:type_name -> rpc.Status
	142, // 76: risk.case_management.ListSortedCasesRequest.filters:type_name -> risk.case_management.review.SortableCaseFilters
	143, // 77: risk.case_management.ListSortedCasesRequest.sort_by:type_name -> risk.case_management.review.CaseSortBy
	116, // 78: risk.case_management.ListSortedCasesRequest.page_context_request:type_name -> rpc.PageContextRequest
	108, // 79: risk.case_management.ListSortedCasesResponse.status:type_name -> rpc.Status
	112, // 80: risk.case_management.ListSortedCasesResponse.cases:type_name -> risk.case_management.review.Case
	117, // 81: risk.case_management.ListSortedCasesResponse.page_context_response:type_name -> rpc.PageContextResponse
	108, // 82: risk.case_management.GetFiUserRelationshipResponse.status:type_name -> rpc.Status
	106, // 83: risk.case_management.GetFiUserRelationshipResponse.fi_user_relationship:type_name -> risk.case_management.GetFiUserRelationshipResponse.FiUserRelationshipEntry
	108, // 84: risk.case_management.GetFormResponse.status:type_name -> rpc.Status
	144, // 85: risk.case_management.GetFormResponse.questions:type_name -> risk.case_management.form.ExtendedQuestion
	145, // 86: risk.case_management.GetFormResponse.form:type_name -> risk.case_management.form.Form
	146, // 87: risk.case_management.SubmitFormRequest.responses:type_name -> risk.case_management.form.QuestionResponse
	108, // 88: risk.case_management.SubmitFormResponse.status:type_name -> rpc.Status
	147, // 89: risk.case_management.ListFormsRequest.filters:type_name -> risk.case_management.form.FormFilters
	148, // 90: risk.case_management.ListFormsRequest.field_masks:type_name -> risk.case_management.form.ExtendedFormFieldMask
	108, // 91: risk.case_management.ListFormsResponse.status:type_name -> rpc.Status
	149, // 92: risk.case_management.ListFormsResponse.forms:type_name -> risk.case_management.form.ExtendedForm
	108, // 93: risk.case_management.GetAlertsResponse.status:type_name -> rpc.Status
	126, // 94: risk.case_management.GetAlertsResponse.alerts:type_name -> risk.case_management.AlertWithRuleDetails
	108, // 95: risk.case_management.GetFormsForActorResponse.status:type_name -> rpc.Status
	145, // 96: risk.case_management.GetFormsForActorResponse.form:type_name -> risk.case_management.form.Form
	150, // 97: risk.case_management.CreateSuggestedActionForRuleRequest.suggested_action_type:type_name -> risk.case_management.SuggestedActionType
	130, // 98: risk.case_management.CreateSuggestedActionForRuleRequest.action_parameters:type_name -> risk.case_management.review.ActionParameters
	108, // 99: risk.case_management.CreateSuggestedActionForRuleResponse.status:type_name -> rpc.Status
	151, // 100: risk.case_management.CreateSuggestedActionForRuleResponse.suggested_action:type_name -> risk.case_management.SuggestedAction
	111, // 101: risk.case_management.CreateRuleReviewTypeMappingRequest.review_type:type_name -> risk.case_management.review.ReviewType
	108, // 102: risk.case_management.CreateRuleReviewTypeMappingResponse.status:type_name -> rpc.Status
	152, // 103: risk.case_management.GetTransactionBlocksRequest.block_type:type_name -> risk.case_management.TransactionBlockType
	108, // 104: risk.case_management.GetTransactionBlocksResponse.status:type_name -> rpc.Status
	153, // 105: risk.case_management.GetTransactionBlocksResponse.transaction_blocks:type_name -> risk.case_management.TransactionBlock
	153, // 106: risk.case_management.CreateTransactionBlockRequest.transaction_blocks:type_name -> risk.case_management.TransactionBlock
	108, // 107: risk.case_management.CreateTransactionBlockResponse.status:type_name -> rpc.Status
	153, // 108: risk.case_management.CreateTransactionBlockResponse.transaction_blocks:type_name -> risk.case_management.TransactionBlock
	99,  // 109: risk.case_management.TimeAggregatedCount.month_key:type_name -> risk.case_management.MonthKey
	100, // 110: risk.case_management.TimeAggregatedCount.week_key:type_name -> risk.case_management.WeekKey
	101, // 111: risk.case_management.TimeAggregatedCount.day_key:type_name -> risk.case_management.DayKey
	0,   // 112: risk.case_management.GetRiskTransactionAggregatedMetricsRequest.aggregate_option:type_name -> risk.case_management.AggregateOption
	121, // 113: risk.case_management.GetRiskTransactionAggregatedMetricsRequest.filter_after_date:type_name -> google.protobuf.Timestamp
	98,  // 114: risk.case_management.RiskTransactionAggregatedMetrics.credit_counts:type_name -> risk.case_management.TimeAggregatedCount
	98,  // 115: risk.case_management.RiskTransactionAggregatedMetrics.lifetime_p2p_credit_counterparties:type_name -> risk.case_management.TimeAggregatedCount
	98,  // 116: risk.case_management.RiskTransactionAggregatedMetrics.credits_25k_plus:type_name -> risk.case_management.TimeAggregatedCount
	0,   // 117: risk.case_management.RiskTransactionAggregatedMetrics.aggregation_type:type_name -> risk.case_management.AggregateOption
	108, // 118: risk.case_management.GetRiskTransactionAggregatedMetricsResponse.status:type_name -> rpc.Status
	103, // 119: risk.case_management.GetRiskTransactionAggregatedMetricsResponse.metrics:type_name -> risk.case_management.RiskTransactionAggregatedMetrics
	120, // 120: risk.case_management.CreateAlertsResponse.Failure.alert:type_name -> risk.case_management.RawAlert
	48,  // 121: risk.case_management.CaseManagement.ListCases:input_type -> risk.case_management.ListCasesRequest
	50,  // 122: risk.case_management.CaseManagement.CreateAlerts:input_type -> risk.case_management.CreateAlertsRequest
	44,  // 123: risk.case_management.CaseManagement.GetTransactionDetailsForReview:input_type -> risk.case_management.GetTransactionDetailsForReviewRequest
	53,  // 124: risk.case_management.CaseManagement.GetReviewDetails:input_type -> risk.case_management.GetReviewDetailsRequest
	55,  // 125: risk.case_management.CaseManagement.GetLinkedAlerts:input_type -> risk.case_management.GetLinkedAlertsRequest
	57,  // 126: risk.case_management.CaseManagement.PerformAction:input_type -> risk.case_management.PerformActionRequest
	59,  // 127: risk.case_management.CaseManagement.CreateComment:input_type -> risk.case_management.CreateCommentRequest
	61,  // 128: risk.case_management.CaseManagement.ListComments:input_type -> risk.case_management.ListCommentsRequest
	63,  // 129: risk.case_management.CaseManagement.GetAllowedAnnotations:input_type -> risk.case_management.GetAllowedAnnotationsRequest
	65,  // 130: risk.case_management.CaseManagement.CreateAnnotation:input_type -> risk.case_management.CreateAnnotationRequest
	67,  // 131: risk.case_management.CaseManagement.ListAnnotations:input_type -> risk.case_management.ListAnnotationsRequest
	69,  // 132: risk.case_management.CaseManagement.CreateRule:input_type -> risk.case_management.CreateRuleRequest
	71,  // 133: risk.case_management.CaseManagement.ListRules:input_type -> risk.case_management.ListRulesRequest
	73,  // 134: risk.case_management.CaseManagement.UpdateRule:input_type -> risk.case_management.UpdateRuleRequest
	42,  // 135: risk.case_management.CaseManagement.CreateAllowedAnnotation:input_type -> risk.case_management.CreateAllowedAnnotationRequest
	40,  // 136: risk.case_management.CaseManagement.UpdateCase:input_type -> risk.case_management.UpdateCaseRequest
	38,  // 137: risk.case_management.CaseManagement.GetPrioritizedCase:input_type -> risk.case_management.GetPrioritizedCaseRequest
	75,  // 138: risk.case_management.CaseManagement.ListSortedCases:input_type -> risk.case_management.ListSortedCasesRequest
	77,  // 139: risk.case_management.CaseManagement.GetFiUserRelationship:input_type -> risk.case_management.GetFiUserRelationshipRequest
	79,  // 140: risk.case_management.CaseManagement.GetForm:input_type -> risk.case_management.GetFormRequest
	81,  // 141: risk.case_management.CaseManagement.SubmitForm:input_type -> risk.case_management.SubmitFormRequest
	83,  // 142: risk.case_management.CaseManagement.ListForms:input_type -> risk.case_management.ListFormsRequest
	85,  // 143: risk.case_management.CaseManagement.GetAlerts:input_type -> risk.case_management.GetAlertsRequest
	36,  // 144: risk.case_management.CaseManagement.GetAllTags:input_type -> risk.case_management.GetAllTagsRequest
	34,  // 145: risk.case_management.CaseManagement.CreateAnnotations:input_type -> risk.case_management.CreateAnnotationsRequest
	32,  // 146: risk.case_management.CaseManagement.ListAllowedAnnotations:input_type -> risk.case_management.ListAllowedAnnotationsRequest
	87,  // 147: risk.case_management.CaseManagement.GetFormsForActor:input_type -> risk.case_management.GetFormsForActorRequest
	89,  // 148: risk.case_management.CaseManagement.CreateSuggestedActionForRule:input_type -> risk.case_management.CreateSuggestedActionForRuleRequest
	91,  // 149: risk.case_management.CaseManagement.CreateRuleReviewTypeMapping:input_type -> risk.case_management.CreateRuleReviewTypeMappingRequest
	93,  // 150: risk.case_management.CaseManagement.GetTransactionBlocks:input_type -> risk.case_management.GetTransactionBlocksRequest
	95,  // 151: risk.case_management.CaseManagement.CreateTransactionBlock:input_type -> risk.case_management.CreateTransactionBlockRequest
	102, // 152: risk.case_management.CaseManagement.GetRiskTransactionAggregatedMetrics:input_type -> risk.case_management.GetRiskTransactionAggregatedMetricsRequest
	49,  // 153: risk.case_management.CaseManagement.ListCases:output_type -> risk.case_management.ListCasesResponse
	51,  // 154: risk.case_management.CaseManagement.CreateAlerts:output_type -> risk.case_management.CreateAlertsResponse
	45,  // 155: risk.case_management.CaseManagement.GetTransactionDetailsForReview:output_type -> risk.case_management.GetTransactionDetailsForReviewResponse
	54,  // 156: risk.case_management.CaseManagement.GetReviewDetails:output_type -> risk.case_management.GetReviewDetailsResponse
	56,  // 157: risk.case_management.CaseManagement.GetLinkedAlerts:output_type -> risk.case_management.GetLinkedAlertsResponse
	58,  // 158: risk.case_management.CaseManagement.PerformAction:output_type -> risk.case_management.PerformActionResponse
	60,  // 159: risk.case_management.CaseManagement.CreateComment:output_type -> risk.case_management.CreateCommentResponse
	62,  // 160: risk.case_management.CaseManagement.ListComments:output_type -> risk.case_management.ListCommentsResponse
	64,  // 161: risk.case_management.CaseManagement.GetAllowedAnnotations:output_type -> risk.case_management.GetAllowedAnnotationsResponse
	66,  // 162: risk.case_management.CaseManagement.CreateAnnotation:output_type -> risk.case_management.CreateAnnotationResponse
	68,  // 163: risk.case_management.CaseManagement.ListAnnotations:output_type -> risk.case_management.ListAnnotationsResponse
	70,  // 164: risk.case_management.CaseManagement.CreateRule:output_type -> risk.case_management.CreateRuleResponse
	72,  // 165: risk.case_management.CaseManagement.ListRules:output_type -> risk.case_management.ListRulesResponse
	74,  // 166: risk.case_management.CaseManagement.UpdateRule:output_type -> risk.case_management.UpdateRuleResponse
	43,  // 167: risk.case_management.CaseManagement.CreateAllowedAnnotation:output_type -> risk.case_management.CreateAllowedAnnotationResponse
	41,  // 168: risk.case_management.CaseManagement.UpdateCase:output_type -> risk.case_management.UpdateCaseResponse
	39,  // 169: risk.case_management.CaseManagement.GetPrioritizedCase:output_type -> risk.case_management.GetPrioritizedCaseResponse
	76,  // 170: risk.case_management.CaseManagement.ListSortedCases:output_type -> risk.case_management.ListSortedCasesResponse
	78,  // 171: risk.case_management.CaseManagement.GetFiUserRelationship:output_type -> risk.case_management.GetFiUserRelationshipResponse
	80,  // 172: risk.case_management.CaseManagement.GetForm:output_type -> risk.case_management.GetFormResponse
	82,  // 173: risk.case_management.CaseManagement.SubmitForm:output_type -> risk.case_management.SubmitFormResponse
	84,  // 174: risk.case_management.CaseManagement.ListForms:output_type -> risk.case_management.ListFormsResponse
	86,  // 175: risk.case_management.CaseManagement.GetAlerts:output_type -> risk.case_management.GetAlertsResponse
	37,  // 176: risk.case_management.CaseManagement.GetAllTags:output_type -> risk.case_management.GetAllTagsResponse
	35,  // 177: risk.case_management.CaseManagement.CreateAnnotations:output_type -> risk.case_management.CreateAnnotationsResponse
	33,  // 178: risk.case_management.CaseManagement.ListAllowedAnnotations:output_type -> risk.case_management.ListAllowedAnnotationsResponse
	88,  // 179: risk.case_management.CaseManagement.GetFormsForActor:output_type -> risk.case_management.GetFormsForActorResponse
	90,  // 180: risk.case_management.CaseManagement.CreateSuggestedActionForRule:output_type -> risk.case_management.CreateSuggestedActionForRuleResponse
	92,  // 181: risk.case_management.CaseManagement.CreateRuleReviewTypeMapping:output_type -> risk.case_management.CreateRuleReviewTypeMappingResponse
	94,  // 182: risk.case_management.CaseManagement.GetTransactionBlocks:output_type -> risk.case_management.GetTransactionBlocksResponse
	96,  // 183: risk.case_management.CaseManagement.CreateTransactionBlock:output_type -> risk.case_management.CreateTransactionBlockResponse
	104, // 184: risk.case_management.CaseManagement.GetRiskTransactionAggregatedMetrics:output_type -> risk.case_management.GetRiskTransactionAggregatedMetricsResponse
	153, // [153:185] is the sub-list for method output_type
	121, // [121:153] is the sub-list for method input_type
	121, // [121:121] is the sub-list for extension type_name
	121, // [121:121] is the sub-list for extension extendee
	0,   // [0:121] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_service_proto_init() }
func file_api_risk_case_management_service_proto_init() {
	if File_api_risk_case_management_service_proto != nil {
		return
	}
	file_api_risk_case_management_alert_proto_init()
	file_api_risk_case_management_combined_annotation_proto_init()
	file_api_risk_case_management_rule_proto_init()
	file_api_risk_case_management_suggested_action_proto_init()
	file_api_risk_case_management_transaction_block_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_case_management_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAllowedAnnotationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAllowedAnnotationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAnnotationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAnnotationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllTagsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllTagsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPrioritizedCaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPrioritizedCaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAllowedAnnotationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAllowedAnnotationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionDetailsForReviewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionDetailsForReviewResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MerchantDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCasesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCasesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAlertsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAlertsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReviewDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReviewDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLinkedAlertsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLinkedAlertsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PerformActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PerformActionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCommentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCommentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCommentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCommentsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllowedAnnotationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllowedAnnotationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAnnotationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAnnotationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAnnotationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAnnotationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSortedCasesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSortedCasesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFiUserRelationshipRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFiUserRelationshipResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFormRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFormResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitFormRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitFormResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListFormsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListFormsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFormsForActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFormsForActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSuggestedActionForRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSuggestedActionForRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRuleReviewTypeMappingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRuleReviewTypeMappingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionBlocksRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionBlocksResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTransactionBlockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTransactionBlockResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonthlyTransactionCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeAggregatedCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonthKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeekKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DayKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRiskTransactionAggregatedMetricsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskTransactionAggregatedMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRiskTransactionAggregatedMetricsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_service_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAlertsResponse_Failure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_case_management_service_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*ListAllowedAnnotationsResponse_UiElementAllowedAnnotations)(nil),
	}
	file_api_risk_case_management_service_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*GetLinkedAlertsRequest_CaseId)(nil),
	}
	file_api_risk_case_management_service_proto_msgTypes[39].OneofWrappers = []interface{}{
		(*ListRulesRequest_ExternalId)(nil),
		(*ListRulesRequest_RuleName)(nil),
		(*ListRulesRequest_RuleGroup)(nil),
	}
	file_api_risk_case_management_service_proto_msgTypes[61].OneofWrappers = []interface{}{
		(*GetTransactionBlocksRequest_ActorId)(nil),
		(*GetTransactionBlocksRequest_AlertId)(nil),
	}
	file_api_risk_case_management_service_proto_msgTypes[66].OneofWrappers = []interface{}{
		(*TimeAggregatedCount_MonthKey)(nil),
		(*TimeAggregatedCount_WeekKey)(nil),
		(*TimeAggregatedCount_DayKey)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_service_proto_rawDesc,
			NumEnums:      32,
			NumMessages:   75,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_risk_case_management_service_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_service_proto_depIdxs,
		EnumInfos:         file_api_risk_case_management_service_proto_enumTypes,
		MessageInfos:      file_api_risk_case_management_service_proto_msgTypes,
	}.Build()
	File_api_risk_case_management_service_proto = out.File
	file_api_risk_case_management_service_proto_rawDesc = nil
	file_api_risk_case_management_service_proto_goTypes = nil
	file_api_risk_case_management_service_proto_depIdxs = nil
}
