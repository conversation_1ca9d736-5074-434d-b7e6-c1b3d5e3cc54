// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/firefly/v2/consumer
package consumer

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessCreditCardOnboardingStateUpdateEventMethod = "ProcessCreditCardOnboardingStateUpdateEvent"
	ProcessCreditCardsDispatchedCsvFileMethod         = "ProcessCreditCardsDispatchedCsvFile"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &ProcessCreditCardsDispatchedCsvFileRequest{}
var _ queue.ConsumerRequest = &ProcessCreditCardOnboardingStateUpdateEventRequest{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCreditCardsDispatchedCsvFileRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCreditCardOnboardingStateUpdateEventRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterProcessCreditCardOnboardingStateUpdateEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCreditCardOnboardingStateUpdateEventMethodToSubscriber(subscriber queue.Subscriber, srv CreditCardConsumerServer) {
	subscriber.RegisterService(&CreditCardConsumer_ServiceDesc, srv, ProcessCreditCardOnboardingStateUpdateEventMethod)
}

// RegisterProcessCreditCardsDispatchedCsvFileMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCreditCardsDispatchedCsvFileMethodToSubscriber(subscriber queue.Subscriber, srv CreditCardConsumerServer) {
	subscriber.RegisterService(&CreditCardConsumer_ServiceDesc, srv, ProcessCreditCardsDispatchedCsvFileMethod)
}
