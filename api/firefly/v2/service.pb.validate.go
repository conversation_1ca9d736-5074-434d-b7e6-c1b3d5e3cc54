// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/firefly/v2/service.proto

package v2

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/firefly/v2/enums"

	typesv2 "github.com/epifi/gamma/api/typesv2"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.CardState(0)

	_ = typesv2.CreditCardSdkTokenType(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on GetCreditCardOffersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditCardOffersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditCardOffersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCreditCardOffersRequestMultiError, or nil if none found.
func (m *GetCreditCardOffersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditCardOffersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetCardProgram()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditCardOffersRequestValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditCardOffersRequestValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardProgram()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditCardOffersRequestValidationError{
				field:  "CardProgram",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCreditCardOffersRequestMultiError(errors)
	}

	return nil
}

// GetCreditCardOffersRequestMultiError is an error wrapping multiple
// validation errors returned by GetCreditCardOffersRequest.ValidateAll() if
// the designated constraints aren't met.
type GetCreditCardOffersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditCardOffersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditCardOffersRequestMultiError) AllErrors() []error { return m }

// GetCreditCardOffersRequestValidationError is the validation error returned
// by GetCreditCardOffersRequest.Validate if the designated constraints aren't met.
type GetCreditCardOffersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditCardOffersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditCardOffersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditCardOffersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditCardOffersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditCardOffersRequestValidationError) ErrorName() string {
	return "GetCreditCardOffersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditCardOffersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditCardOffersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditCardOffersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditCardOffersRequestValidationError{}

// Validate checks the field values on GetCreditCardOffersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditCardOffersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditCardOffersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCreditCardOffersResponseMultiError, or nil if none found.
func (m *GetCreditCardOffersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditCardOffersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditCardOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditCardOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditCardOffersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCreditCardOffersResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCreditCardOffersResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCreditCardOffersResponseValidationError{
					field:  fmt.Sprintf("Offers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCreditCardOffersResponseMultiError(errors)
	}

	return nil
}

// GetCreditCardOffersResponseMultiError is an error wrapping multiple
// validation errors returned by GetCreditCardOffersResponse.ValidateAll() if
// the designated constraints aren't met.
type GetCreditCardOffersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditCardOffersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditCardOffersResponseMultiError) AllErrors() []error { return m }

// GetCreditCardOffersResponseValidationError is the validation error returned
// by GetCreditCardOffersResponse.Validate if the designated constraints
// aren't met.
type GetCreditCardOffersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditCardOffersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditCardOffersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditCardOffersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditCardOffersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditCardOffersResponseValidationError) ErrorName() string {
	return "GetCreditCardOffersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditCardOffersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditCardOffersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditCardOffersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditCardOffersResponseValidationError{}

// Validate checks the field values on GetLandingInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLandingInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLandingInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLandingInfoRequestMultiError, or nil if none found.
func (m *GetLandingInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLandingInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetLandingInfoRequestMultiError(errors)
	}

	return nil
}

// GetLandingInfoRequestMultiError is an error wrapping multiple validation
// errors returned by GetLandingInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLandingInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLandingInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLandingInfoRequestMultiError) AllErrors() []error { return m }

// GetLandingInfoRequestValidationError is the validation error returned by
// GetLandingInfoRequest.Validate if the designated constraints aren't met.
type GetLandingInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLandingInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLandingInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLandingInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLandingInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLandingInfoRequestValidationError) ErrorName() string {
	return "GetLandingInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLandingInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLandingInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLandingInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLandingInfoRequestValidationError{}

// Validate checks the field values on GetLandingInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLandingInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLandingInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLandingInfoResponseMultiError, or nil if none found.
func (m *GetLandingInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLandingInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingInfoResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingInfoResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingInfoResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLandingInfoResponseMultiError(errors)
	}

	return nil
}

// GetLandingInfoResponseMultiError is an error wrapping multiple validation
// errors returned by GetLandingInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLandingInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLandingInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLandingInfoResponseMultiError) AllErrors() []error { return m }

// GetLandingInfoResponseValidationError is the validation error returned by
// GetLandingInfoResponse.Validate if the designated constraints aren't met.
type GetLandingInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLandingInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLandingInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLandingInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLandingInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLandingInfoResponseValidationError) ErrorName() string {
	return "GetLandingInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLandingInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLandingInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLandingInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLandingInfoResponseValidationError{}

// Validate checks the field values on GetCreditCardsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditCardsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditCardsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCreditCardsRequestMultiError, or nil if none found.
func (m *GetCreditCardsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditCardsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *GetCreditCardsRequest_ExternalUserId:
		if v == nil {
			err := GetCreditCardsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ExternalUserId
	case *GetCreditCardsRequest_ActorId:
		if v == nil {
			err := GetCreditCardsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetCreditCardsRequestMultiError(errors)
	}

	return nil
}

// GetCreditCardsRequestMultiError is an error wrapping multiple validation
// errors returned by GetCreditCardsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCreditCardsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditCardsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditCardsRequestMultiError) AllErrors() []error { return m }

// GetCreditCardsRequestValidationError is the validation error returned by
// GetCreditCardsRequest.Validate if the designated constraints aren't met.
type GetCreditCardsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditCardsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditCardsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditCardsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditCardsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditCardsRequestValidationError) ErrorName() string {
	return "GetCreditCardsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditCardsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditCardsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditCardsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditCardsRequestValidationError{}

// Validate checks the field values on GetCreditCardsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditCardsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditCardsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCreditCardsResponseMultiError, or nil if none found.
func (m *GetCreditCardsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditCardsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditCardsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditCardsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditCardsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCreditCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCreditCardsResponseValidationError{
						field:  fmt.Sprintf("CreditCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCreditCardsResponseValidationError{
						field:  fmt.Sprintf("CreditCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCreditCardsResponseValidationError{
					field:  fmt.Sprintf("CreditCards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCreditCardsResponseMultiError(errors)
	}

	return nil
}

// GetCreditCardsResponseMultiError is an error wrapping multiple validation
// errors returned by GetCreditCardsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCreditCardsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditCardsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditCardsResponseMultiError) AllErrors() []error { return m }

// GetCreditCardsResponseValidationError is the validation error returned by
// GetCreditCardsResponse.Validate if the designated constraints aren't met.
type GetCreditCardsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditCardsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditCardsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditCardsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditCardsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditCardsResponseValidationError) ErrorName() string {
	return "GetCreditCardsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditCardsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditCardsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditCardsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditCardsResponseValidationError{}

// Validate checks the field values on GetCreditCardTrackingDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCreditCardTrackingDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditCardTrackingDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCreditCardTrackingDetailsRequestMultiError, or nil if none found.
func (m *GetCreditCardTrackingDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditCardTrackingDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *GetCreditCardTrackingDetailsRequest_ExternalUserId:
		if v == nil {
			err := GetCreditCardTrackingDetailsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ExternalUserId
	case *GetCreditCardTrackingDetailsRequest_ActorId:
		if v == nil {
			err := GetCreditCardTrackingDetailsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetCreditCardTrackingDetailsRequestMultiError(errors)
	}

	return nil
}

// GetCreditCardTrackingDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetCreditCardTrackingDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCreditCardTrackingDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditCardTrackingDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditCardTrackingDetailsRequestMultiError) AllErrors() []error { return m }

// GetCreditCardTrackingDetailsRequestValidationError is the validation error
// returned by GetCreditCardTrackingDetailsRequest.Validate if the designated
// constraints aren't met.
type GetCreditCardTrackingDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditCardTrackingDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditCardTrackingDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditCardTrackingDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditCardTrackingDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditCardTrackingDetailsRequestValidationError) ErrorName() string {
	return "GetCreditCardTrackingDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditCardTrackingDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditCardTrackingDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditCardTrackingDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditCardTrackingDetailsRequestValidationError{}

// Validate checks the field values on GetCreditCardTrackingDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCreditCardTrackingDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditCardTrackingDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCreditCardTrackingDetailsResponseMultiError, or nil if none found.
func (m *GetCreditCardTrackingDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditCardTrackingDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditCardTrackingDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditCardTrackingDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditCardTrackingDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTrackingDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditCardTrackingDetailsResponseValidationError{
					field:  "TrackingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditCardTrackingDetailsResponseValidationError{
					field:  "TrackingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTrackingDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditCardTrackingDetailsResponseValidationError{
				field:  "TrackingDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCreditCardTrackingDetailsResponseMultiError(errors)
	}

	return nil
}

// GetCreditCardTrackingDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetCreditCardTrackingDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCreditCardTrackingDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditCardTrackingDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditCardTrackingDetailsResponseMultiError) AllErrors() []error { return m }

// GetCreditCardTrackingDetailsResponseValidationError is the validation error
// returned by GetCreditCardTrackingDetailsResponse.Validate if the designated
// constraints aren't met.
type GetCreditCardTrackingDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditCardTrackingDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditCardTrackingDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditCardTrackingDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditCardTrackingDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditCardTrackingDetailsResponseValidationError) ErrorName() string {
	return "GetCreditCardTrackingDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditCardTrackingDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditCardTrackingDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditCardTrackingDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditCardTrackingDetailsResponseValidationError{}

// Validate checks the field values on GenerateCreditCardSdkAuthTokenRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GenerateCreditCardSdkAuthTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateCreditCardSdkAuthTokenRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GenerateCreditCardSdkAuthTokenRequestMultiError, or nil if none found.
func (m *GenerateCreditCardSdkAuthTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateCreditCardSdkAuthTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetDeviceInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "DeviceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "DeviceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateCreditCardSdkAuthTokenRequestValidationError{
				field:  "DeviceInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateCreditCardSdkAuthTokenRequestMultiError(errors)
	}

	return nil
}

// GenerateCreditCardSdkAuthTokenRequestMultiError is an error wrapping
// multiple validation errors returned by
// GenerateCreditCardSdkAuthTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateCreditCardSdkAuthTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateCreditCardSdkAuthTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateCreditCardSdkAuthTokenRequestMultiError) AllErrors() []error { return m }

// GenerateCreditCardSdkAuthTokenRequestValidationError is the validation error
// returned by GenerateCreditCardSdkAuthTokenRequest.Validate if the
// designated constraints aren't met.
type GenerateCreditCardSdkAuthTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateCreditCardSdkAuthTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateCreditCardSdkAuthTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateCreditCardSdkAuthTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateCreditCardSdkAuthTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateCreditCardSdkAuthTokenRequestValidationError) ErrorName() string {
	return "GenerateCreditCardSdkAuthTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateCreditCardSdkAuthTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateCreditCardSdkAuthTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateCreditCardSdkAuthTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateCreditCardSdkAuthTokenRequestValidationError{}

// Validate checks the field values on GenerateCreditCardSdkAuthTokenResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GenerateCreditCardSdkAuthTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GenerateCreditCardSdkAuthTokenResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GenerateCreditCardSdkAuthTokenResponseMultiError, or nil if none found.
func (m *GenerateCreditCardSdkAuthTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateCreditCardSdkAuthTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateCreditCardSdkAuthTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AuthToken

	// no validation rules for TokenType

	if len(errors) > 0 {
		return GenerateCreditCardSdkAuthTokenResponseMultiError(errors)
	}

	return nil
}

// GenerateCreditCardSdkAuthTokenResponseMultiError is an error wrapping
// multiple validation errors returned by
// GenerateCreditCardSdkAuthTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateCreditCardSdkAuthTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateCreditCardSdkAuthTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateCreditCardSdkAuthTokenResponseMultiError) AllErrors() []error { return m }

// GenerateCreditCardSdkAuthTokenResponseValidationError is the validation
// error returned by GenerateCreditCardSdkAuthTokenResponse.Validate if the
// designated constraints aren't met.
type GenerateCreditCardSdkAuthTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateCreditCardSdkAuthTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateCreditCardSdkAuthTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateCreditCardSdkAuthTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateCreditCardSdkAuthTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateCreditCardSdkAuthTokenResponseValidationError) ErrorName() string {
	return "GenerateCreditCardSdkAuthTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateCreditCardSdkAuthTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateCreditCardSdkAuthTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateCreditCardSdkAuthTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateCreditCardSdkAuthTokenResponseValidationError{}

// Validate checks the field values on GetCardRequestRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardRequestRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardRequestRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCardRequestRequestMultiError, or nil if none found.
func (m *GetCardRequestRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardRequestRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestType

	switch v := m.Identifier.(type) {
	case *GetCardRequestRequest_ExternalUserId:
		if v == nil {
			err := GetCardRequestRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ExternalUserId
	case *GetCardRequestRequest_ActorId:
		if v == nil {
			err := GetCardRequestRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	case *GetCardRequestRequest_RequestId:
		if v == nil {
			err := GetCardRequestRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for RequestId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetCardRequestRequestMultiError(errors)
	}

	return nil
}

// GetCardRequestRequestMultiError is an error wrapping multiple validation
// errors returned by GetCardRequestRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCardRequestRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardRequestRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardRequestRequestMultiError) AllErrors() []error { return m }

// GetCardRequestRequestValidationError is the validation error returned by
// GetCardRequestRequest.Validate if the designated constraints aren't met.
type GetCardRequestRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardRequestRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardRequestRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardRequestRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardRequestRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardRequestRequestValidationError) ErrorName() string {
	return "GetCardRequestRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardRequestRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardRequestRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardRequestRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardRequestRequestValidationError{}

// Validate checks the field values on GetCardRequestResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardRequestResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardRequestResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCardRequestResponseMultiError, or nil if none found.
func (m *GetCardRequestResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardRequestResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardRequestResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardRequestResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardRequestResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardRequestResponseValidationError{
					field:  "CardRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardRequestResponseValidationError{
					field:  "CardRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardRequestResponseValidationError{
				field:  "CardRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCardRequestResponseMultiError(errors)
	}

	return nil
}

// GetCardRequestResponseMultiError is an error wrapping multiple validation
// errors returned by GetCardRequestResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCardRequestResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardRequestResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardRequestResponseMultiError) AllErrors() []error { return m }

// GetCardRequestResponseValidationError is the validation error returned by
// GetCardRequestResponse.Validate if the designated constraints aren't met.
type GetCardRequestResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardRequestResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardRequestResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardRequestResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardRequestResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardRequestResponseValidationError) ErrorName() string {
	return "GetCardRequestResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardRequestResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardRequestResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardRequestResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardRequestResponseValidationError{}

// Validate checks the field values on FetchCreditCardEligibilityRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchCreditCardEligibilityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCreditCardEligibilityRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FetchCreditCardEligibilityRequestMultiError, or nil if none found.
func (m *FetchCreditCardEligibilityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCreditCardEligibilityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return FetchCreditCardEligibilityRequestMultiError(errors)
	}

	return nil
}

// FetchCreditCardEligibilityRequestMultiError is an error wrapping multiple
// validation errors returned by
// FetchCreditCardEligibilityRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchCreditCardEligibilityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCreditCardEligibilityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCreditCardEligibilityRequestMultiError) AllErrors() []error { return m }

// FetchCreditCardEligibilityRequestValidationError is the validation error
// returned by FetchCreditCardEligibilityRequest.Validate if the designated
// constraints aren't met.
type FetchCreditCardEligibilityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCreditCardEligibilityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCreditCardEligibilityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCreditCardEligibilityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCreditCardEligibilityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCreditCardEligibilityRequestValidationError) ErrorName() string {
	return "FetchCreditCardEligibilityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCreditCardEligibilityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCreditCardEligibilityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCreditCardEligibilityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCreditCardEligibilityRequestValidationError{}

// Validate checks the field values on FetchCreditCardEligibilityResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchCreditCardEligibilityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCreditCardEligibilityResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FetchCreditCardEligibilityResponseMultiError, or nil if none found.
func (m *FetchCreditCardEligibilityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCreditCardEligibilityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCreditCardEligibilityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCreditCardEligibilityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCreditCardEligibilityResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsUserCcEligible

	if len(errors) > 0 {
		return FetchCreditCardEligibilityResponseMultiError(errors)
	}

	return nil
}

// FetchCreditCardEligibilityResponseMultiError is an error wrapping multiple
// validation errors returned by
// FetchCreditCardEligibilityResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchCreditCardEligibilityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCreditCardEligibilityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCreditCardEligibilityResponseMultiError) AllErrors() []error { return m }

// FetchCreditCardEligibilityResponseValidationError is the validation error
// returned by FetchCreditCardEligibilityResponse.Validate if the designated
// constraints aren't met.
type FetchCreditCardEligibilityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCreditCardEligibilityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCreditCardEligibilityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCreditCardEligibilityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCreditCardEligibilityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCreditCardEligibilityResponseValidationError) ErrorName() string {
	return "FetchCreditCardEligibilityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCreditCardEligibilityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCreditCardEligibilityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCreditCardEligibilityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCreditCardEligibilityResponseValidationError{}
