// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/firefly/v2/service.proto

package v2

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	FireflyV2_GenerateCreditCardSdkAuthToken_FullMethodName = "/api.firefly.v2.FireflyV2/GenerateCreditCardSdkAuthToken"
	FireflyV2_GetCreditCardTrackingDetails_FullMethodName   = "/api.firefly.v2.FireflyV2/GetCreditCardTrackingDetails"
	FireflyV2_GetCreditCards_FullMethodName                 = "/api.firefly.v2.FireflyV2/GetCreditCards"
	FireflyV2_GetLandingInfo_FullMethodName                 = "/api.firefly.v2.FireflyV2/GetLandingInfo"
	FireflyV2_GetCreditCardOffers_FullMethodName            = "/api.firefly.v2.FireflyV2/GetCreditCardOffers"
	FireflyV2_GetCardRequest_FullMethodName                 = "/api.firefly.v2.FireflyV2/GetCardRequest"
	FireflyV2_FetchCreditCardEligibility_FullMethodName     = "/api.firefly.v2.FireflyV2/FetchCreditCardEligibility"
)

// FireflyV2Client is the client API for FireflyV2 service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FireflyV2Client interface {
	// RPC to generate an authentication token for client to authenticate with credit card sdk
	// It checks if the user is already onboarded or has started onboarding,
	// based on which makes a decision to call different vg endpoints to generate the auth token
	GenerateCreditCardSdkAuthToken(ctx context.Context, in *GenerateCreditCardSdkAuthTokenRequest, opts ...grpc.CallOption) (*GenerateCreditCardSdkAuthTokenResponse, error)
	// RPC to fetch the tracking details of the credit card
	GetCreditCardTrackingDetails(ctx context.Context, in *GetCreditCardTrackingDetailsRequest, opts ...grpc.CallOption) (*GetCreditCardTrackingDetailsResponse, error)
	// RPC to fetch the credit cards for an actor_id/external_user_id
	GetCreditCards(ctx context.Context, in *GetCreditCardsRequest, opts ...grpc.CallOption) (*GetCreditCardsResponse, error)
	// GetLandingInfo rpc for checking the current card state of the user and redirecting user to the corresponding
	// screen based on their card states, if card is not created yet we will redirect the user to the next screen based on their onboarding journey.
	GetLandingInfo(ctx context.Context, in *GetLandingInfoRequest, opts ...grpc.CallOption) (*GetLandingInfoResponse, error)
	// RPC to fetch active credit card offer
	GetCreditCardOffers(ctx context.Context, in *GetCreditCardOffersRequest, opts ...grpc.CallOption) (*GetCreditCardOffersResponse, error)
	// RPC to fetch the status of a card request
	GetCardRequest(ctx context.Context, in *GetCardRequestRequest, opts ...grpc.CallOption) (*GetCardRequestResponse, error)
	// RPC to check if user is eligible for credit card v2
	// Returns true if user is NOT a CC user and is eligible for getting one
	// Returns status ALREADY_EXISTS if user is already a CC user
	FetchCreditCardEligibility(ctx context.Context, in *FetchCreditCardEligibilityRequest, opts ...grpc.CallOption) (*FetchCreditCardEligibilityResponse, error)
}

type fireflyV2Client struct {
	cc grpc.ClientConnInterface
}

func NewFireflyV2Client(cc grpc.ClientConnInterface) FireflyV2Client {
	return &fireflyV2Client{cc}
}

func (c *fireflyV2Client) GenerateCreditCardSdkAuthToken(ctx context.Context, in *GenerateCreditCardSdkAuthTokenRequest, opts ...grpc.CallOption) (*GenerateCreditCardSdkAuthTokenResponse, error) {
	out := new(GenerateCreditCardSdkAuthTokenResponse)
	err := c.cc.Invoke(ctx, FireflyV2_GenerateCreditCardSdkAuthToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fireflyV2Client) GetCreditCardTrackingDetails(ctx context.Context, in *GetCreditCardTrackingDetailsRequest, opts ...grpc.CallOption) (*GetCreditCardTrackingDetailsResponse, error) {
	out := new(GetCreditCardTrackingDetailsResponse)
	err := c.cc.Invoke(ctx, FireflyV2_GetCreditCardTrackingDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fireflyV2Client) GetCreditCards(ctx context.Context, in *GetCreditCardsRequest, opts ...grpc.CallOption) (*GetCreditCardsResponse, error) {
	out := new(GetCreditCardsResponse)
	err := c.cc.Invoke(ctx, FireflyV2_GetCreditCards_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fireflyV2Client) GetLandingInfo(ctx context.Context, in *GetLandingInfoRequest, opts ...grpc.CallOption) (*GetLandingInfoResponse, error) {
	out := new(GetLandingInfoResponse)
	err := c.cc.Invoke(ctx, FireflyV2_GetLandingInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fireflyV2Client) GetCreditCardOffers(ctx context.Context, in *GetCreditCardOffersRequest, opts ...grpc.CallOption) (*GetCreditCardOffersResponse, error) {
	out := new(GetCreditCardOffersResponse)
	err := c.cc.Invoke(ctx, FireflyV2_GetCreditCardOffers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fireflyV2Client) GetCardRequest(ctx context.Context, in *GetCardRequestRequest, opts ...grpc.CallOption) (*GetCardRequestResponse, error) {
	out := new(GetCardRequestResponse)
	err := c.cc.Invoke(ctx, FireflyV2_GetCardRequest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fireflyV2Client) FetchCreditCardEligibility(ctx context.Context, in *FetchCreditCardEligibilityRequest, opts ...grpc.CallOption) (*FetchCreditCardEligibilityResponse, error) {
	out := new(FetchCreditCardEligibilityResponse)
	err := c.cc.Invoke(ctx, FireflyV2_FetchCreditCardEligibility_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FireflyV2Server is the server API for FireflyV2 service.
// All implementations should embed UnimplementedFireflyV2Server
// for forward compatibility
type FireflyV2Server interface {
	// RPC to generate an authentication token for client to authenticate with credit card sdk
	// It checks if the user is already onboarded or has started onboarding,
	// based on which makes a decision to call different vg endpoints to generate the auth token
	GenerateCreditCardSdkAuthToken(context.Context, *GenerateCreditCardSdkAuthTokenRequest) (*GenerateCreditCardSdkAuthTokenResponse, error)
	// RPC to fetch the tracking details of the credit card
	GetCreditCardTrackingDetails(context.Context, *GetCreditCardTrackingDetailsRequest) (*GetCreditCardTrackingDetailsResponse, error)
	// RPC to fetch the credit cards for an actor_id/external_user_id
	GetCreditCards(context.Context, *GetCreditCardsRequest) (*GetCreditCardsResponse, error)
	// GetLandingInfo rpc for checking the current card state of the user and redirecting user to the corresponding
	// screen based on their card states, if card is not created yet we will redirect the user to the next screen based on their onboarding journey.
	GetLandingInfo(context.Context, *GetLandingInfoRequest) (*GetLandingInfoResponse, error)
	// RPC to fetch active credit card offer
	GetCreditCardOffers(context.Context, *GetCreditCardOffersRequest) (*GetCreditCardOffersResponse, error)
	// RPC to fetch the status of a card request
	GetCardRequest(context.Context, *GetCardRequestRequest) (*GetCardRequestResponse, error)
	// RPC to check if user is eligible for credit card v2
	// Returns true if user is NOT a CC user and is eligible for getting one
	// Returns status ALREADY_EXISTS if user is already a CC user
	FetchCreditCardEligibility(context.Context, *FetchCreditCardEligibilityRequest) (*FetchCreditCardEligibilityResponse, error)
}

// UnimplementedFireflyV2Server should be embedded to have forward compatible implementations.
type UnimplementedFireflyV2Server struct {
}

func (UnimplementedFireflyV2Server) GenerateCreditCardSdkAuthToken(context.Context, *GenerateCreditCardSdkAuthTokenRequest) (*GenerateCreditCardSdkAuthTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateCreditCardSdkAuthToken not implemented")
}
func (UnimplementedFireflyV2Server) GetCreditCardTrackingDetails(context.Context, *GetCreditCardTrackingDetailsRequest) (*GetCreditCardTrackingDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCreditCardTrackingDetails not implemented")
}
func (UnimplementedFireflyV2Server) GetCreditCards(context.Context, *GetCreditCardsRequest) (*GetCreditCardsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCreditCards not implemented")
}
func (UnimplementedFireflyV2Server) GetLandingInfo(context.Context, *GetLandingInfoRequest) (*GetLandingInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLandingInfo not implemented")
}
func (UnimplementedFireflyV2Server) GetCreditCardOffers(context.Context, *GetCreditCardOffersRequest) (*GetCreditCardOffersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCreditCardOffers not implemented")
}
func (UnimplementedFireflyV2Server) GetCardRequest(context.Context, *GetCardRequestRequest) (*GetCardRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardRequest not implemented")
}
func (UnimplementedFireflyV2Server) FetchCreditCardEligibility(context.Context, *FetchCreditCardEligibilityRequest) (*FetchCreditCardEligibilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchCreditCardEligibility not implemented")
}

// UnsafeFireflyV2Server may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FireflyV2Server will
// result in compilation errors.
type UnsafeFireflyV2Server interface {
	mustEmbedUnimplementedFireflyV2Server()
}

func RegisterFireflyV2Server(s grpc.ServiceRegistrar, srv FireflyV2Server) {
	s.RegisterService(&FireflyV2_ServiceDesc, srv)
}

func _FireflyV2_GenerateCreditCardSdkAuthToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateCreditCardSdkAuthTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FireflyV2Server).GenerateCreditCardSdkAuthToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FireflyV2_GenerateCreditCardSdkAuthToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FireflyV2Server).GenerateCreditCardSdkAuthToken(ctx, req.(*GenerateCreditCardSdkAuthTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FireflyV2_GetCreditCardTrackingDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCreditCardTrackingDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FireflyV2Server).GetCreditCardTrackingDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FireflyV2_GetCreditCardTrackingDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FireflyV2Server).GetCreditCardTrackingDetails(ctx, req.(*GetCreditCardTrackingDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FireflyV2_GetCreditCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCreditCardsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FireflyV2Server).GetCreditCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FireflyV2_GetCreditCards_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FireflyV2Server).GetCreditCards(ctx, req.(*GetCreditCardsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FireflyV2_GetLandingInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLandingInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FireflyV2Server).GetLandingInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FireflyV2_GetLandingInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FireflyV2Server).GetLandingInfo(ctx, req.(*GetLandingInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FireflyV2_GetCreditCardOffers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCreditCardOffersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FireflyV2Server).GetCreditCardOffers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FireflyV2_GetCreditCardOffers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FireflyV2Server).GetCreditCardOffers(ctx, req.(*GetCreditCardOffersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FireflyV2_GetCardRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FireflyV2Server).GetCardRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FireflyV2_GetCardRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FireflyV2Server).GetCardRequest(ctx, req.(*GetCardRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FireflyV2_FetchCreditCardEligibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchCreditCardEligibilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FireflyV2Server).FetchCreditCardEligibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FireflyV2_FetchCreditCardEligibility_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FireflyV2Server).FetchCreditCardEligibility(ctx, req.(*FetchCreditCardEligibilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FireflyV2_ServiceDesc is the grpc.ServiceDesc for FireflyV2 service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FireflyV2_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.firefly.v2.FireflyV2",
	HandlerType: (*FireflyV2Server)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateCreditCardSdkAuthToken",
			Handler:    _FireflyV2_GenerateCreditCardSdkAuthToken_Handler,
		},
		{
			MethodName: "GetCreditCardTrackingDetails",
			Handler:    _FireflyV2_GetCreditCardTrackingDetails_Handler,
		},
		{
			MethodName: "GetCreditCards",
			Handler:    _FireflyV2_GetCreditCards_Handler,
		},
		{
			MethodName: "GetLandingInfo",
			Handler:    _FireflyV2_GetLandingInfo_Handler,
		},
		{
			MethodName: "GetCreditCardOffers",
			Handler:    _FireflyV2_GetCreditCardOffers_Handler,
		},
		{
			MethodName: "GetCardRequest",
			Handler:    _FireflyV2_GetCardRequest_Handler,
		},
		{
			MethodName: "FetchCreditCardEligibility",
			Handler:    _FireflyV2_FetchCreditCardEligibility_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/firefly/v2/service.proto",
}
