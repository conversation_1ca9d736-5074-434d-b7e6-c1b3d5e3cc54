// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/firefly/v2/service.proto

package v2

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	enums "github.com/epifi/gamma/api/firefly/v2/enums"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetCreditCardOffersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId     string               `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Vendor      vendorgateway.Vendor `protobuf:"varint,2,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	CardProgram *typesv2.CardProgram `protobuf:"bytes,3,opt,name=card_program,json=cardProgram,proto3" json:"card_program,omitempty"`
}

func (x *GetCreditCardOffersRequest) Reset() {
	*x = GetCreditCardOffersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardOffersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardOffersRequest) ProtoMessage() {}

func (x *GetCreditCardOffersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardOffersRequest.ProtoReflect.Descriptor instead.
func (*GetCreditCardOffersRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetCreditCardOffersRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetCreditCardOffersRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *GetCreditCardOffersRequest) GetCardProgram() *typesv2.CardProgram {
	if x != nil {
		return x.CardProgram
	}
	return nil
}

type GetCreditCardOffersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Offers []*CreditCardOffer `protobuf:"bytes,2,rep,name=offers,proto3" json:"offers,omitempty"`
}

func (x *GetCreditCardOffersResponse) Reset() {
	*x = GetCreditCardOffersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardOffersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardOffersResponse) ProtoMessage() {}

func (x *GetCreditCardOffersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardOffersResponse.ProtoReflect.Descriptor instead.
func (*GetCreditCardOffersResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetCreditCardOffersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCreditCardOffersResponse) GetOffers() []*CreditCardOffer {
	if x != nil {
		return x.Offers
	}
	return nil
}

type GetLandingInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetLandingInfoRequest) Reset() {
	*x = GetLandingInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLandingInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLandingInfoRequest) ProtoMessage() {}

func (x *GetLandingInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLandingInfoRequest.ProtoReflect.Descriptor instead.
func (*GetLandingInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetLandingInfoRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetLandingInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// next action for the user based on the current card state of the user
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *GetLandingInfoResponse) Reset() {
	*x = GetLandingInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLandingInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLandingInfoResponse) ProtoMessage() {}

func (x *GetLandingInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLandingInfoResponse.ProtoReflect.Descriptor instead.
func (*GetLandingInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetLandingInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLandingInfoResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type GetCreditCardsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetCreditCardsRequest_ExternalUserId
	//	*GetCreditCardsRequest_ActorId
	Identifier isGetCreditCardsRequest_Identifier `protobuf_oneof:"identifier"`
	// by default, it will return only `CREATED` i.e. valid active cards
	StateFilters []enums.CardState `protobuf:"varint,3,rep,packed,name=state_filters,json=stateFilters,proto3,enum=api.firefly.v2.enums.CardState" json:"state_filters,omitempty"`
}

func (x *GetCreditCardsRequest) Reset() {
	*x = GetCreditCardsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardsRequest) ProtoMessage() {}

func (x *GetCreditCardsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardsRequest.ProtoReflect.Descriptor instead.
func (*GetCreditCardsRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{4}
}

func (m *GetCreditCardsRequest) GetIdentifier() isGetCreditCardsRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetCreditCardsRequest) GetExternalUserId() string {
	if x, ok := x.GetIdentifier().(*GetCreditCardsRequest_ExternalUserId); ok {
		return x.ExternalUserId
	}
	return ""
}

func (x *GetCreditCardsRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*GetCreditCardsRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *GetCreditCardsRequest) GetStateFilters() []enums.CardState {
	if x != nil {
		return x.StateFilters
	}
	return nil
}

type isGetCreditCardsRequest_Identifier interface {
	isGetCreditCardsRequest_Identifier()
}

type GetCreditCardsRequest_ExternalUserId struct {
	ExternalUserId string `protobuf:"bytes,1,opt,name=external_user_id,json=externalUserId,proto3,oneof"`
}

type GetCreditCardsRequest_ActorId struct {
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3,oneof"`
}

func (*GetCreditCardsRequest_ExternalUserId) isGetCreditCardsRequest_Identifier() {}

func (*GetCreditCardsRequest_ActorId) isGetCreditCardsRequest_Identifier() {}

type GetCreditCardsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CreditCards []*CreditCard `protobuf:"bytes,2,rep,name=credit_cards,json=creditCards,proto3" json:"credit_cards,omitempty"`
}

func (x *GetCreditCardsResponse) Reset() {
	*x = GetCreditCardsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardsResponse) ProtoMessage() {}

func (x *GetCreditCardsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardsResponse.ProtoReflect.Descriptor instead.
func (*GetCreditCardsResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetCreditCardsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCreditCardsResponse) GetCreditCards() []*CreditCard {
	if x != nil {
		return x.CreditCards
	}
	return nil
}

type GetCreditCardTrackingDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetCreditCardTrackingDetailsRequest_ExternalUserId
	//	*GetCreditCardTrackingDetailsRequest_ActorId
	Identifier isGetCreditCardTrackingDetailsRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetCreditCardTrackingDetailsRequest) Reset() {
	*x = GetCreditCardTrackingDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardTrackingDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardTrackingDetailsRequest) ProtoMessage() {}

func (x *GetCreditCardTrackingDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardTrackingDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetCreditCardTrackingDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{6}
}

func (m *GetCreditCardTrackingDetailsRequest) GetIdentifier() isGetCreditCardTrackingDetailsRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetCreditCardTrackingDetailsRequest) GetExternalUserId() string {
	if x, ok := x.GetIdentifier().(*GetCreditCardTrackingDetailsRequest_ExternalUserId); ok {
		return x.ExternalUserId
	}
	return ""
}

func (x *GetCreditCardTrackingDetailsRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*GetCreditCardTrackingDetailsRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

type isGetCreditCardTrackingDetailsRequest_Identifier interface {
	isGetCreditCardTrackingDetailsRequest_Identifier()
}

type GetCreditCardTrackingDetailsRequest_ExternalUserId struct {
	ExternalUserId string `protobuf:"bytes,1,opt,name=external_user_id,json=externalUserId,proto3,oneof"`
}

type GetCreditCardTrackingDetailsRequest_ActorId struct {
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3,oneof"`
}

func (*GetCreditCardTrackingDetailsRequest_ExternalUserId) isGetCreditCardTrackingDetailsRequest_Identifier() {
}

func (*GetCreditCardTrackingDetailsRequest_ActorId) isGetCreditCardTrackingDetailsRequest_Identifier() {
}

type GetCreditCardTrackingDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status                  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TrackingDetails *CardDeliveryTrackingDetails `protobuf:"bytes,2,opt,name=tracking_details,json=trackingDetails,proto3" json:"tracking_details,omitempty"`
}

func (x *GetCreditCardTrackingDetailsResponse) Reset() {
	*x = GetCreditCardTrackingDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditCardTrackingDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditCardTrackingDetailsResponse) ProtoMessage() {}

func (x *GetCreditCardTrackingDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditCardTrackingDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetCreditCardTrackingDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetCreditCardTrackingDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCreditCardTrackingDetailsResponse) GetTrackingDetails() *CardDeliveryTrackingDetails {
	if x != nil {
		return x.TrackingDetails
	}
	return nil
}

type GenerateCreditCardSdkAuthTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for the actor requesting the token.
	ActorId    string         `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	DeviceInfo *common.Device `protobuf:"bytes,2,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"`
}

func (x *GenerateCreditCardSdkAuthTokenRequest) Reset() {
	*x = GenerateCreditCardSdkAuthTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateCreditCardSdkAuthTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateCreditCardSdkAuthTokenRequest) ProtoMessage() {}

func (x *GenerateCreditCardSdkAuthTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateCreditCardSdkAuthTokenRequest.ProtoReflect.Descriptor instead.
func (*GenerateCreditCardSdkAuthTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{8}
}

func (x *GenerateCreditCardSdkAuthTokenRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GenerateCreditCardSdkAuthTokenRequest) GetDeviceInfo() *common.Device {
	if x != nil {
		return x.DeviceInfo
	}
	return nil
}

type GenerateCreditCardSdkAuthTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Generated authentication token.
	AuthToken string `protobuf:"bytes,2,opt,name=auth_token,json=authToken,proto3" json:"auth_token,omitempty"`
	// Token type of the auth_token
	TokenType typesv2.CreditCardSdkTokenType `protobuf:"varint,3,opt,name=token_type,json=tokenType,proto3,enum=api.typesv2.CreditCardSdkTokenType" json:"token_type,omitempty"`
}

func (x *GenerateCreditCardSdkAuthTokenResponse) Reset() {
	*x = GenerateCreditCardSdkAuthTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateCreditCardSdkAuthTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateCreditCardSdkAuthTokenResponse) ProtoMessage() {}

func (x *GenerateCreditCardSdkAuthTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateCreditCardSdkAuthTokenResponse.ProtoReflect.Descriptor instead.
func (*GenerateCreditCardSdkAuthTokenResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{9}
}

func (x *GenerateCreditCardSdkAuthTokenResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GenerateCreditCardSdkAuthTokenResponse) GetAuthToken() string {
	if x != nil {
		return x.AuthToken
	}
	return ""
}

func (x *GenerateCreditCardSdkAuthTokenResponse) GetTokenType() typesv2.CreditCardSdkTokenType {
	if x != nil {
		return x.TokenType
	}
	return typesv2.CreditCardSdkTokenType(0)
}

type GetCardRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetCardRequestRequest_ExternalUserId
	//	*GetCardRequestRequest_ActorId
	//	*GetCardRequestRequest_RequestId
	Identifier isGetCardRequestRequest_Identifier `protobuf_oneof:"identifier"`
	// Filter by request type, mandatory for actor_id and external_user_id identifiers
	RequestType enums.CardRequestType `protobuf:"varint,4,opt,name=request_type,json=requestType,proto3,enum=api.firefly.v2.enums.CardRequestType" json:"request_type,omitempty"`
}

func (x *GetCardRequestRequest) Reset() {
	*x = GetCardRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCardRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardRequestRequest) ProtoMessage() {}

func (x *GetCardRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardRequestRequest.ProtoReflect.Descriptor instead.
func (*GetCardRequestRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{10}
}

func (m *GetCardRequestRequest) GetIdentifier() isGetCardRequestRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetCardRequestRequest) GetExternalUserId() string {
	if x, ok := x.GetIdentifier().(*GetCardRequestRequest_ExternalUserId); ok {
		return x.ExternalUserId
	}
	return ""
}

func (x *GetCardRequestRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*GetCardRequestRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *GetCardRequestRequest) GetRequestId() string {
	if x, ok := x.GetIdentifier().(*GetCardRequestRequest_RequestId); ok {
		return x.RequestId
	}
	return ""
}

func (x *GetCardRequestRequest) GetRequestType() enums.CardRequestType {
	if x != nil {
		return x.RequestType
	}
	return enums.CardRequestType(0)
}

type isGetCardRequestRequest_Identifier interface {
	isGetCardRequestRequest_Identifier()
}

type GetCardRequestRequest_ExternalUserId struct {
	ExternalUserId string `protobuf:"bytes,1,opt,name=external_user_id,json=externalUserId,proto3,oneof"`
}

type GetCardRequestRequest_ActorId struct {
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3,oneof"`
}

type GetCardRequestRequest_RequestId struct {
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3,oneof"`
}

func (*GetCardRequestRequest_ExternalUserId) isGetCardRequestRequest_Identifier() {}

func (*GetCardRequestRequest_ActorId) isGetCardRequestRequest_Identifier() {}

func (*GetCardRequestRequest_RequestId) isGetCardRequestRequest_Identifier() {}

type GetCardRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Latest card request details
	CardRequest *CardRequest `protobuf:"bytes,2,opt,name=card_request,json=cardRequest,proto3" json:"card_request,omitempty"`
}

func (x *GetCardRequestResponse) Reset() {
	*x = GetCardRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCardRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardRequestResponse) ProtoMessage() {}

func (x *GetCardRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardRequestResponse.ProtoReflect.Descriptor instead.
func (*GetCardRequestResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetCardRequestResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCardRequestResponse) GetCardRequest() *CardRequest {
	if x != nil {
		return x.CardRequest
	}
	return nil
}

type FetchCreditCardEligibilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *FetchCreditCardEligibilityRequest) Reset() {
	*x = FetchCreditCardEligibilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchCreditCardEligibilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchCreditCardEligibilityRequest) ProtoMessage() {}

func (x *FetchCreditCardEligibilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchCreditCardEligibilityRequest.ProtoReflect.Descriptor instead.
func (*FetchCreditCardEligibilityRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{12}
}

func (x *FetchCreditCardEligibilityRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type FetchCreditCardEligibilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// will be true if user is NOT a CC user and is eligible for v2 credit card
	// status will be ALREADY_EXISTS if user is already a CC user
	IsUserCcEligible bool `protobuf:"varint,2,opt,name=is_user_cc_eligible,json=isUserCcEligible,proto3" json:"is_user_cc_eligible,omitempty"`
}

func (x *FetchCreditCardEligibilityResponse) Reset() {
	*x = FetchCreditCardEligibilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_v2_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchCreditCardEligibilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchCreditCardEligibilityResponse) ProtoMessage() {}

func (x *FetchCreditCardEligibilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_v2_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchCreditCardEligibilityResponse.ProtoReflect.Descriptor instead.
func (*FetchCreditCardEligibilityResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_v2_service_proto_rawDescGZIP(), []int{13}
}

func (x *FetchCreditCardEligibilityResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchCreditCardEligibilityResponse) GetIsUserCcEligible() bool {
	if x != nil {
		return x.IsUserCcEligible
	}
	return false
}

var File_api_firefly_v2_service_proto protoreflect.FileDescriptor

var file_api_firefly_v2_service_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x76, 0x32,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e,
	0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x1a, 0x21,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x76, 0x32, 0x2f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x76,
	0x32, 0x2f, 0x63, 0x63, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x76, 0x32,
	0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x76, 0x32, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x01, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x3b, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x22, 0x7b, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x22,
	0x32, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x22, 0x7b, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xb4, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x7c, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x73, 0x22, 0x7c, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x10,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x22, 0xa3, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x56, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x72,
	0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x7f, 0x0a, 0x25, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53,
	0x64, 0x6b, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3b, 0x0a,
	0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0a,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xb0, 0x01, 0x0a, 0x26, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x53, 0x64, 0x6b, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x42, 0x0a, 0x0a, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x64, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0xd9, 0x01,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x48, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x7d, 0x0a, 0x16, 0x47, 0x65, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x2e,
	0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x63, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x3e, 0x0a, 0x21, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x78, 0x0a, 0x22, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x63, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x10, 0x69, 0x73, 0x55, 0x73, 0x65, 0x72, 0x43, 0x63, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62,
	0x6c, 0x65, 0x32, 0xc2, 0x06, 0x0a, 0x09, 0x46, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x56, 0x32,
	0x12, 0x8f, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x64, 0x6b, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x64, 0x6b, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x64,
	0x6b, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x73,
	0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76,
	0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x5f, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e,
	0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x6e, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x5f, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x83, 0x01, 0x0a, 0x1a, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76,
	0x32, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32,
	0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2f, 0x76, 0x32, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_firefly_v2_service_proto_rawDescOnce sync.Once
	file_api_firefly_v2_service_proto_rawDescData = file_api_firefly_v2_service_proto_rawDesc
)

func file_api_firefly_v2_service_proto_rawDescGZIP() []byte {
	file_api_firefly_v2_service_proto_rawDescOnce.Do(func() {
		file_api_firefly_v2_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_firefly_v2_service_proto_rawDescData)
	})
	return file_api_firefly_v2_service_proto_rawDescData
}

var file_api_firefly_v2_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_api_firefly_v2_service_proto_goTypes = []interface{}{
	(*GetCreditCardOffersRequest)(nil),             // 0: api.firefly.v2.GetCreditCardOffersRequest
	(*GetCreditCardOffersResponse)(nil),            // 1: api.firefly.v2.GetCreditCardOffersResponse
	(*GetLandingInfoRequest)(nil),                  // 2: api.firefly.v2.GetLandingInfoRequest
	(*GetLandingInfoResponse)(nil),                 // 3: api.firefly.v2.GetLandingInfoResponse
	(*GetCreditCardsRequest)(nil),                  // 4: api.firefly.v2.GetCreditCardsRequest
	(*GetCreditCardsResponse)(nil),                 // 5: api.firefly.v2.GetCreditCardsResponse
	(*GetCreditCardTrackingDetailsRequest)(nil),    // 6: api.firefly.v2.GetCreditCardTrackingDetailsRequest
	(*GetCreditCardTrackingDetailsResponse)(nil),   // 7: api.firefly.v2.GetCreditCardTrackingDetailsResponse
	(*GenerateCreditCardSdkAuthTokenRequest)(nil),  // 8: api.firefly.v2.GenerateCreditCardSdkAuthTokenRequest
	(*GenerateCreditCardSdkAuthTokenResponse)(nil), // 9: api.firefly.v2.GenerateCreditCardSdkAuthTokenResponse
	(*GetCardRequestRequest)(nil),                  // 10: api.firefly.v2.GetCardRequestRequest
	(*GetCardRequestResponse)(nil),                 // 11: api.firefly.v2.GetCardRequestResponse
	(*FetchCreditCardEligibilityRequest)(nil),      // 12: api.firefly.v2.FetchCreditCardEligibilityRequest
	(*FetchCreditCardEligibilityResponse)(nil),     // 13: api.firefly.v2.FetchCreditCardEligibilityResponse
	(vendorgateway.Vendor)(0),                      // 14: vendorgateway.Vendor
	(*typesv2.CardProgram)(nil),                    // 15: api.typesv2.CardProgram
	(*rpc.Status)(nil),                             // 16: rpc.Status
	(*CreditCardOffer)(nil),                        // 17: api.firefly.v2.CreditCardOffer
	(*deeplink.Deeplink)(nil),                      // 18: frontend.deeplink.Deeplink
	(enums.CardState)(0),                           // 19: api.firefly.v2.enums.CardState
	(*CreditCard)(nil),                             // 20: api.firefly.v2.CreditCard
	(*CardDeliveryTrackingDetails)(nil),            // 21: api.firefly.v2.CardDeliveryTrackingDetails
	(*common.Device)(nil),                          // 22: api.typesv2.common.Device
	(typesv2.CreditCardSdkTokenType)(0),            // 23: api.typesv2.CreditCardSdkTokenType
	(enums.CardRequestType)(0),                     // 24: api.firefly.v2.enums.CardRequestType
	(*CardRequest)(nil),                            // 25: api.firefly.v2.CardRequest
}
var file_api_firefly_v2_service_proto_depIdxs = []int32{
	14, // 0: api.firefly.v2.GetCreditCardOffersRequest.vendor:type_name -> vendorgateway.Vendor
	15, // 1: api.firefly.v2.GetCreditCardOffersRequest.card_program:type_name -> api.typesv2.CardProgram
	16, // 2: api.firefly.v2.GetCreditCardOffersResponse.status:type_name -> rpc.Status
	17, // 3: api.firefly.v2.GetCreditCardOffersResponse.offers:type_name -> api.firefly.v2.CreditCardOffer
	16, // 4: api.firefly.v2.GetLandingInfoResponse.status:type_name -> rpc.Status
	18, // 5: api.firefly.v2.GetLandingInfoResponse.next_action:type_name -> frontend.deeplink.Deeplink
	19, // 6: api.firefly.v2.GetCreditCardsRequest.state_filters:type_name -> api.firefly.v2.enums.CardState
	16, // 7: api.firefly.v2.GetCreditCardsResponse.status:type_name -> rpc.Status
	20, // 8: api.firefly.v2.GetCreditCardsResponse.credit_cards:type_name -> api.firefly.v2.CreditCard
	16, // 9: api.firefly.v2.GetCreditCardTrackingDetailsResponse.status:type_name -> rpc.Status
	21, // 10: api.firefly.v2.GetCreditCardTrackingDetailsResponse.tracking_details:type_name -> api.firefly.v2.CardDeliveryTrackingDetails
	22, // 11: api.firefly.v2.GenerateCreditCardSdkAuthTokenRequest.device_info:type_name -> api.typesv2.common.Device
	16, // 12: api.firefly.v2.GenerateCreditCardSdkAuthTokenResponse.status:type_name -> rpc.Status
	23, // 13: api.firefly.v2.GenerateCreditCardSdkAuthTokenResponse.token_type:type_name -> api.typesv2.CreditCardSdkTokenType
	24, // 14: api.firefly.v2.GetCardRequestRequest.request_type:type_name -> api.firefly.v2.enums.CardRequestType
	16, // 15: api.firefly.v2.GetCardRequestResponse.status:type_name -> rpc.Status
	25, // 16: api.firefly.v2.GetCardRequestResponse.card_request:type_name -> api.firefly.v2.CardRequest
	16, // 17: api.firefly.v2.FetchCreditCardEligibilityResponse.status:type_name -> rpc.Status
	8,  // 18: api.firefly.v2.FireflyV2.GenerateCreditCardSdkAuthToken:input_type -> api.firefly.v2.GenerateCreditCardSdkAuthTokenRequest
	6,  // 19: api.firefly.v2.FireflyV2.GetCreditCardTrackingDetails:input_type -> api.firefly.v2.GetCreditCardTrackingDetailsRequest
	4,  // 20: api.firefly.v2.FireflyV2.GetCreditCards:input_type -> api.firefly.v2.GetCreditCardsRequest
	2,  // 21: api.firefly.v2.FireflyV2.GetLandingInfo:input_type -> api.firefly.v2.GetLandingInfoRequest
	0,  // 22: api.firefly.v2.FireflyV2.GetCreditCardOffers:input_type -> api.firefly.v2.GetCreditCardOffersRequest
	10, // 23: api.firefly.v2.FireflyV2.GetCardRequest:input_type -> api.firefly.v2.GetCardRequestRequest
	12, // 24: api.firefly.v2.FireflyV2.FetchCreditCardEligibility:input_type -> api.firefly.v2.FetchCreditCardEligibilityRequest
	9,  // 25: api.firefly.v2.FireflyV2.GenerateCreditCardSdkAuthToken:output_type -> api.firefly.v2.GenerateCreditCardSdkAuthTokenResponse
	7,  // 26: api.firefly.v2.FireflyV2.GetCreditCardTrackingDetails:output_type -> api.firefly.v2.GetCreditCardTrackingDetailsResponse
	5,  // 27: api.firefly.v2.FireflyV2.GetCreditCards:output_type -> api.firefly.v2.GetCreditCardsResponse
	3,  // 28: api.firefly.v2.FireflyV2.GetLandingInfo:output_type -> api.firefly.v2.GetLandingInfoResponse
	1,  // 29: api.firefly.v2.FireflyV2.GetCreditCardOffers:output_type -> api.firefly.v2.GetCreditCardOffersResponse
	11, // 30: api.firefly.v2.FireflyV2.GetCardRequest:output_type -> api.firefly.v2.GetCardRequestResponse
	13, // 31: api.firefly.v2.FireflyV2.FetchCreditCardEligibility:output_type -> api.firefly.v2.FetchCreditCardEligibilityResponse
	25, // [25:32] is the sub-list for method output_type
	18, // [18:25] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_api_firefly_v2_service_proto_init() }
func file_api_firefly_v2_service_proto_init() {
	if File_api_firefly_v2_service_proto != nil {
		return
	}
	file_api_firefly_v2_card_request_proto_init()
	file_api_firefly_v2_cc_offer_proto_init()
	file_api_firefly_v2_credit_card_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_firefly_v2_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardOffersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_v2_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardOffersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_v2_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLandingInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_v2_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLandingInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_v2_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_v2_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_v2_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardTrackingDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_v2_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditCardTrackingDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_v2_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateCreditCardSdkAuthTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_v2_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateCreditCardSdkAuthTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_v2_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCardRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_v2_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCardRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_v2_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchCreditCardEligibilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_v2_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchCreditCardEligibilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_firefly_v2_service_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*GetCreditCardsRequest_ExternalUserId)(nil),
		(*GetCreditCardsRequest_ActorId)(nil),
	}
	file_api_firefly_v2_service_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*GetCreditCardTrackingDetailsRequest_ExternalUserId)(nil),
		(*GetCreditCardTrackingDetailsRequest_ActorId)(nil),
	}
	file_api_firefly_v2_service_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*GetCardRequestRequest_ExternalUserId)(nil),
		(*GetCardRequestRequest_ActorId)(nil),
		(*GetCardRequestRequest_RequestId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_firefly_v2_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_firefly_v2_service_proto_goTypes,
		DependencyIndexes: file_api_firefly_v2_service_proto_depIdxs,
		MessageInfos:      file_api_firefly_v2_service_proto_msgTypes,
	}.Build()
	File_api_firefly_v2_service_proto = out.File
	file_api_firefly_v2_service_proto_rawDesc = nil
	file_api_firefly_v2_service_proto_goTypes = nil
	file_api_firefly_v2_service_proto_depIdxs = nil
}
