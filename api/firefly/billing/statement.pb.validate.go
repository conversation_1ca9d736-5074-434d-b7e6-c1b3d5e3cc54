// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/firefly/billing/statement.proto

package billing

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/firefly/enums"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.TransactionOrigin(0)

	_ = typesv2.TransactionTransferType(0)
)

// Validate checks the field values on Statement with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Statement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Statement with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StatementMultiError, or nil
// if none found.
func (m *Statement) ValidateAll() error {
	return m.validate(true)
}

func (m *Statement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUserDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementValidationError{
					field:  "UserDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementValidationError{
					field:  "UserDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementValidationError{
				field:  "UserDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementValidationError{
				field:  "ToDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StatementValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StatementValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StatementValidationError{
					field:  fmt.Sprintf("Transactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StatementMultiError(errors)
	}

	return nil
}

// StatementMultiError is an error wrapping multiple validation errors returned
// by Statement.ValidateAll() if the designated constraints aren't met.
type StatementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatementMultiError) AllErrors() []error { return m }

// StatementValidationError is the validation error returned by
// Statement.Validate if the designated constraints aren't met.
type StatementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatementValidationError) ErrorName() string { return "StatementValidationError" }

// Error satisfies the builtin error interface
func (e StatementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatementValidationError{}

// Validate checks the field values on StatementSummary with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *StatementSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StatementSummary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StatementSummaryMultiError, or nil if none found.
func (m *StatementSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *StatementSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatementDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "StatementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "StatementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatementDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementSummaryValidationError{
				field:  "StatementDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaymentDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "PaymentDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "PaymentDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementSummaryValidationError{
				field:  "PaymentDueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAvailableLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "AvailableLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "AvailableLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvailableLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementSummaryValidationError{
				field:  "AvailableLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalAmountDue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "TotalAmountDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "TotalAmountDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalAmountDue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementSummaryValidationError{
				field:  "TotalAmountDue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinAmountDue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "MinAmountDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "MinAmountDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmountDue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementSummaryValidationError{
				field:  "MinAmountDue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOpeningBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "OpeningBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "OpeningBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOpeningBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementSummaryValidationError{
				field:  "OpeningBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSpends()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "Spends",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "Spends",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSpends()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementSummaryValidationError{
				field:  "Spends",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "InterestCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "InterestCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementSummaryValidationError{
				field:  "InterestCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFees()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "Fees",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "Fees",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFees()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementSummaryValidationError{
				field:  "Fees",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRepaymentAndRefunds()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "RepaymentAndRefunds",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "RepaymentAndRefunds",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRepaymentAndRefunds()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementSummaryValidationError{
				field:  "RepaymentAndRefunds",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSpendConvertedToEmi()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "SpendConvertedToEmi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementSummaryValidationError{
					field:  "SpendConvertedToEmi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSpendConvertedToEmi()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementSummaryValidationError{
				field:  "SpendConvertedToEmi",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StatementSummaryMultiError(errors)
	}

	return nil
}

// StatementSummaryMultiError is an error wrapping multiple validation errors
// returned by StatementSummary.ValidateAll() if the designated constraints
// aren't met.
type StatementSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatementSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatementSummaryMultiError) AllErrors() []error { return m }

// StatementSummaryValidationError is the validation error returned by
// StatementSummary.Validate if the designated constraints aren't met.
type StatementSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatementSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatementSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatementSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatementSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatementSummaryValidationError) ErrorName() string { return "StatementSummaryValidationError" }

// Error satisfies the builtin error interface
func (e StatementSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatementSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatementSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatementSummaryValidationError{}

// Validate checks the field values on StatementUserDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StatementUserDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StatementUserDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StatementUserDetailMultiError, or nil if none found.
func (m *StatementUserDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *StatementUserDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for PhoneNumber

	// no validation rules for Email

	// no validation rules for Address

	// no validation rules for MaskedCreditCardNumber

	if len(errors) > 0 {
		return StatementUserDetailMultiError(errors)
	}

	return nil
}

// StatementUserDetailMultiError is an error wrapping multiple validation
// errors returned by StatementUserDetail.ValidateAll() if the designated
// constraints aren't met.
type StatementUserDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatementUserDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatementUserDetailMultiError) AllErrors() []error { return m }

// StatementUserDetailValidationError is the validation error returned by
// StatementUserDetail.Validate if the designated constraints aren't met.
type StatementUserDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatementUserDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatementUserDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatementUserDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatementUserDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatementUserDetailValidationError) ErrorName() string {
	return "StatementUserDetailValidationError"
}

// Error satisfies the builtin error interface
func (e StatementUserDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatementUserDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatementUserDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatementUserDetailValidationError{}

// Validate checks the field values on StatementTransaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StatementTransaction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StatementTransaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StatementTransactionMultiError, or nil if none found.
func (m *StatementTransaction) ValidateAll() error {
	return m.validate(true)
}

func (m *StatementTransaction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementTransactionValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementTransactionValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementTransactionValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTransactionTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementTransactionValidationError{
					field:  "TransactionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementTransactionValidationError{
					field:  "TransactionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementTransactionValidationError{
				field:  "TransactionTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MerchantName

	// no validation rules for Location

	// no validation rules for TransactionOrigin

	// no validation rules for PaymentMethod

	// no validation rules for Category

	// no validation rules for TransactionTransferType

	// no validation rules for RewardCoins

	// no validation rules for RewardPoints

	if len(errors) > 0 {
		return StatementTransactionMultiError(errors)
	}

	return nil
}

// StatementTransactionMultiError is an error wrapping multiple validation
// errors returned by StatementTransaction.ValidateAll() if the designated
// constraints aren't met.
type StatementTransactionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatementTransactionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatementTransactionMultiError) AllErrors() []error { return m }

// StatementTransactionValidationError is the validation error returned by
// StatementTransaction.Validate if the designated constraints aren't met.
type StatementTransactionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatementTransactionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatementTransactionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatementTransactionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatementTransactionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatementTransactionValidationError) ErrorName() string {
	return "StatementTransactionValidationError"
}

// Error satisfies the builtin error interface
func (e StatementTransactionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatementTransaction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatementTransactionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatementTransactionValidationError{}

// Validate checks the field values on FeeBreakDown with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FeeBreakDown) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeeBreakDown with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FeeBreakDownMultiError, or
// nil if none found.
func (m *FeeBreakDown) ValidateAll() error {
	return m.validate(true)
}

func (m *FeeBreakDown) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetFeeBreakDownComponents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FeeBreakDownValidationError{
						field:  fmt.Sprintf("FeeBreakDownComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FeeBreakDownValidationError{
						field:  fmt.Sprintf("FeeBreakDownComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FeeBreakDownValidationError{
					field:  fmt.Sprintf("FeeBreakDownComponents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FeeBreakDownMultiError(errors)
	}

	return nil
}

// FeeBreakDownMultiError is an error wrapping multiple validation errors
// returned by FeeBreakDown.ValidateAll() if the designated constraints aren't met.
type FeeBreakDownMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeeBreakDownMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeeBreakDownMultiError) AllErrors() []error { return m }

// FeeBreakDownValidationError is the validation error returned by
// FeeBreakDown.Validate if the designated constraints aren't met.
type FeeBreakDownValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeeBreakDownValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeeBreakDownValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeeBreakDownValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeeBreakDownValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeeBreakDownValidationError) ErrorName() string { return "FeeBreakDownValidationError" }

// Error satisfies the builtin error interface
func (e FeeBreakDownValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeeBreakDown.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeeBreakDownValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeeBreakDownValidationError{}

// Validate checks the field values on FeeBreakDownComponents with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FeeBreakDownComponents) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeeBreakDownComponents with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FeeBreakDownComponentsMultiError, or nil if none found.
func (m *FeeBreakDownComponents) ValidateAll() error {
	return m.validate(true)
}

func (m *FeeBreakDownComponents) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FeeType

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeeBreakDownComponentsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeeBreakDownComponentsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeeBreakDownComponentsValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FeeAmountType

	if len(errors) > 0 {
		return FeeBreakDownComponentsMultiError(errors)
	}

	return nil
}

// FeeBreakDownComponentsMultiError is an error wrapping multiple validation
// errors returned by FeeBreakDownComponents.ValidateAll() if the designated
// constraints aren't met.
type FeeBreakDownComponentsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeeBreakDownComponentsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeeBreakDownComponentsMultiError) AllErrors() []error { return m }

// FeeBreakDownComponentsValidationError is the validation error returned by
// FeeBreakDownComponents.Validate if the designated constraints aren't met.
type FeeBreakDownComponentsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeeBreakDownComponentsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeeBreakDownComponentsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeeBreakDownComponentsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeeBreakDownComponentsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeeBreakDownComponentsValidationError) ErrorName() string {
	return "FeeBreakDownComponentsValidationError"
}

// Error satisfies the builtin error interface
func (e FeeBreakDownComponentsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeeBreakDownComponents.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeeBreakDownComponentsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeeBreakDownComponentsValidationError{}

// Validate checks the field values on ExtraRewardsInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExtraRewardsInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExtraRewardsInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExtraRewardsInfoMultiError, or nil if none found.
func (m *ExtraRewardsInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ExtraRewardsInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	// no validation rules for RewardCoinsEarned

	// no validation rules for RewardTypeLogo

	if len(errors) > 0 {
		return ExtraRewardsInfoMultiError(errors)
	}

	return nil
}

// ExtraRewardsInfoMultiError is an error wrapping multiple validation errors
// returned by ExtraRewardsInfo.ValidateAll() if the designated constraints
// aren't met.
type ExtraRewardsInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExtraRewardsInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExtraRewardsInfoMultiError) AllErrors() []error { return m }

// ExtraRewardsInfoValidationError is the validation error returned by
// ExtraRewardsInfo.Validate if the designated constraints aren't met.
type ExtraRewardsInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExtraRewardsInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExtraRewardsInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExtraRewardsInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExtraRewardsInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExtraRewardsInfoValidationError) ErrorName() string { return "ExtraRewardsInfoValidationError" }

// Error satisfies the builtin error interface
func (e ExtraRewardsInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExtraRewardsInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExtraRewardsInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExtraRewardsInfoValidationError{}

// Validate checks the field values on EmiSummary with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EmiSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmiSummary with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EmiSummaryMultiError, or
// nil if none found.
func (m *EmiSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *EmiSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NumberOfActiveEmi

	if all {
		switch v := interface{}(m.GetDueAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiSummaryValidationError{
					field:  "DueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiSummaryValidationError{
					field:  "DueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDueAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiSummaryValidationError{
				field:  "DueAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetEmiDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EmiSummaryValidationError{
						field:  fmt.Sprintf("EmiDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EmiSummaryValidationError{
						field:  fmt.Sprintf("EmiDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EmiSummaryValidationError{
					field:  fmt.Sprintf("EmiDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EmiSummaryMultiError(errors)
	}

	return nil
}

// EmiSummaryMultiError is an error wrapping multiple validation errors
// returned by EmiSummary.ValidateAll() if the designated constraints aren't met.
type EmiSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmiSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmiSummaryMultiError) AllErrors() []error { return m }

// EmiSummaryValidationError is the validation error returned by
// EmiSummary.Validate if the designated constraints aren't met.
type EmiSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmiSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmiSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmiSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmiSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmiSummaryValidationError) ErrorName() string { return "EmiSummaryValidationError" }

// Error satisfies the builtin error interface
func (e EmiSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmiSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmiSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmiSummaryValidationError{}

// Validate checks the field values on EmiDetail with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EmiDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmiDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EmiDetailMultiError, or nil
// if none found.
func (m *EmiDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *EmiDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MerchantName

	// no validation rules for InstallmentNumber

	// no validation rules for TotalInstallments

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiDetailValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiDetailValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiDetailValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EmiDetailMultiError(errors)
	}

	return nil
}

// EmiDetailMultiError is an error wrapping multiple validation errors returned
// by EmiDetail.ValidateAll() if the designated constraints aren't met.
type EmiDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmiDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmiDetailMultiError) AllErrors() []error { return m }

// EmiDetailValidationError is the validation error returned by
// EmiDetail.Validate if the designated constraints aren't met.
type EmiDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmiDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmiDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmiDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmiDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmiDetailValidationError) ErrorName() string { return "EmiDetailValidationError" }

// Error satisfies the builtin error interface
func (e EmiDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmiDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmiDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmiDetailValidationError{}
