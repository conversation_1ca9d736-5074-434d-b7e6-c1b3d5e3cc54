// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/wealthonboarding/service.proto

package wealthonboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = typesv2.Gender(0)
)

// Validate checks the field values on GetOnboardingStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOnboardingStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOnboardingStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOnboardingStatusRequestMultiError, or nil if none found.
func (m *GetOnboardingStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOnboardingStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for OnboardingType

	if len(errors) > 0 {
		return GetOnboardingStatusRequestMultiError(errors)
	}

	return nil
}

// GetOnboardingStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetOnboardingStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type GetOnboardingStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOnboardingStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOnboardingStatusRequestMultiError) AllErrors() []error { return m }

// GetOnboardingStatusRequestValidationError is the validation error returned
// by GetOnboardingStatusRequest.Validate if the designated constraints aren't met.
type GetOnboardingStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOnboardingStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOnboardingStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOnboardingStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOnboardingStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOnboardingStatusRequestValidationError) ErrorName() string {
	return "GetOnboardingStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOnboardingStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOnboardingStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOnboardingStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOnboardingStatusRequestValidationError{}

// Validate checks the field values on GetOnboardingStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOnboardingStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOnboardingStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOnboardingStatusResponseMultiError, or nil if none found.
func (m *GetOnboardingStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOnboardingStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOnboardingStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOnboardingStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOnboardingStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OnboardingId

	// no validation rules for OnboardingStatus

	// no validation rules for CurrentStep

	// no validation rules for Eligibility

	if len(errors) > 0 {
		return GetOnboardingStatusResponseMultiError(errors)
	}

	return nil
}

// GetOnboardingStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetOnboardingStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type GetOnboardingStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOnboardingStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOnboardingStatusResponseMultiError) AllErrors() []error { return m }

// GetOnboardingStatusResponseValidationError is the validation error returned
// by GetOnboardingStatusResponse.Validate if the designated constraints
// aren't met.
type GetOnboardingStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOnboardingStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOnboardingStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOnboardingStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOnboardingStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOnboardingStatusResponseValidationError) ErrorName() string {
	return "GetOnboardingStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOnboardingStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOnboardingStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOnboardingStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOnboardingStatusResponseValidationError{}

// Validate checks the field values on GetNextOnboardingStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNextOnboardingStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNextOnboardingStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetNextOnboardingStatusRequestMultiError, or nil if none found.
func (m *GetNextOnboardingStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNextOnboardingStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for WealthFlow

	// no validation rules for OnboardingType

	// no validation rules for EntryPoint

	if len(errors) > 0 {
		return GetNextOnboardingStatusRequestMultiError(errors)
	}

	return nil
}

// GetNextOnboardingStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetNextOnboardingStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type GetNextOnboardingStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNextOnboardingStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNextOnboardingStatusRequestMultiError) AllErrors() []error { return m }

// GetNextOnboardingStatusRequestValidationError is the validation error
// returned by GetNextOnboardingStatusRequest.Validate if the designated
// constraints aren't met.
type GetNextOnboardingStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNextOnboardingStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNextOnboardingStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNextOnboardingStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNextOnboardingStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNextOnboardingStatusRequestValidationError) ErrorName() string {
	return "GetNextOnboardingStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNextOnboardingStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNextOnboardingStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNextOnboardingStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNextOnboardingStatusRequestValidationError{}

// Validate checks the field values on GetNextOnboardingStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNextOnboardingStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNextOnboardingStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetNextOnboardingStatusResponseMultiError, or nil if none found.
func (m *GetNextOnboardingStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNextOnboardingStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextOnboardingStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextOnboardingStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextOnboardingStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextStep()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextOnboardingStatusResponseValidationError{
					field:  "NextStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextOnboardingStatusResponseValidationError{
					field:  "NextStep",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextStep()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextOnboardingStatusResponseValidationError{
				field:  "NextStep",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OnboardingStatus

	if len(errors) > 0 {
		return GetNextOnboardingStatusResponseMultiError(errors)
	}

	return nil
}

// GetNextOnboardingStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetNextOnboardingStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type GetNextOnboardingStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNextOnboardingStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNextOnboardingStatusResponseMultiError) AllErrors() []error { return m }

// GetNextOnboardingStatusResponseValidationError is the validation error
// returned by GetNextOnboardingStatusResponse.Validate if the designated
// constraints aren't met.
type GetNextOnboardingStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNextOnboardingStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNextOnboardingStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNextOnboardingStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNextOnboardingStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNextOnboardingStatusResponseValidationError) ErrorName() string {
	return "GetNextOnboardingStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNextOnboardingStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNextOnboardingStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNextOnboardingStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNextOnboardingStatusResponseValidationError{}

// Validate checks the field values on CollectDataFromCustomerRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectDataFromCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectDataFromCustomerRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CollectDataFromCustomerRequestMultiError, or nil if none found.
func (m *CollectDataFromCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectDataFromCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Gender

	// no validation rules for MaritalStatus

	if all {
		switch v := interface{}(m.GetPan()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectDataFromCustomerRequestValidationError{
					field:  "Pan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectDataFromCustomerRequestValidationError{
					field:  "Pan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPan()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectDataFromCustomerRequestValidationError{
				field:  "Pan",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPoi()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectDataFromCustomerRequestValidationError{
					field:  "Poi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectDataFromCustomerRequestValidationError{
					field:  "Poi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPoi()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectDataFromCustomerRequestValidationError{
				field:  "Poi",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPoa()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectDataFromCustomerRequestValidationError{
					field:  "Poa",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectDataFromCustomerRequestValidationError{
					field:  "Poa",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPoa()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectDataFromCustomerRequestValidationError{
				field:  "Poa",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSignature()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectDataFromCustomerRequestValidationError{
					field:  "Signature",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectDataFromCustomerRequestValidationError{
					field:  "Signature",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSignature()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectDataFromCustomerRequestValidationError{
				field:  "Signature",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ResidentialStatus

	// no validation rules for PoliticallyExposedStatus

	// no validation rules for Nationality

	// no validation rules for IncomeSlab

	// no validation rules for HasDigilockerAccount

	// no validation rules for OnboardingType

	// no validation rules for Income

	if all {
		switch v := interface{}(m.GetNomineeDeclarationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectDataFromCustomerRequestValidationError{
					field:  "NomineeDeclarationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectDataFromCustomerRequestValidationError{
					field:  "NomineeDeclarationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNomineeDeclarationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectDataFromCustomerRequestValidationError{
				field:  "NomineeDeclarationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SkipSigningAdvisoryAgreement

	// no validation rules for UserSubmittedPan

	if all {
		switch v := interface{}(m.GetUserSubmittedDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectDataFromCustomerRequestValidationError{
					field:  "UserSubmittedDob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectDataFromCustomerRequestValidationError{
					field:  "UserSubmittedDob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserSubmittedDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectDataFromCustomerRequestValidationError{
				field:  "UserSubmittedDob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserSubmittedName

	// no validation rules for RiskProfilingConsentId

	// no validation rules for InvestmentRiskProfileId

	if len(errors) > 0 {
		return CollectDataFromCustomerRequestMultiError(errors)
	}

	return nil
}

// CollectDataFromCustomerRequestMultiError is an error wrapping multiple
// validation errors returned by CollectDataFromCustomerRequest.ValidateAll()
// if the designated constraints aren't met.
type CollectDataFromCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectDataFromCustomerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectDataFromCustomerRequestMultiError) AllErrors() []error { return m }

// CollectDataFromCustomerRequestValidationError is the validation error
// returned by CollectDataFromCustomerRequest.Validate if the designated
// constraints aren't met.
type CollectDataFromCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectDataFromCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectDataFromCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectDataFromCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectDataFromCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectDataFromCustomerRequestValidationError) ErrorName() string {
	return "CollectDataFromCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CollectDataFromCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectDataFromCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectDataFromCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectDataFromCustomerRequestValidationError{}

// Validate checks the field values on CollectDataFromCustomerResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectDataFromCustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectDataFromCustomerResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CollectDataFromCustomerResponseMultiError, or nil if none found.
func (m *CollectDataFromCustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectDataFromCustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectDataFromCustomerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectDataFromCustomerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectDataFromCustomerResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CollectDataFromCustomerResponseMultiError(errors)
	}

	return nil
}

// CollectDataFromCustomerResponseMultiError is an error wrapping multiple
// validation errors returned by CollectDataFromCustomerResponse.ValidateAll()
// if the designated constraints aren't met.
type CollectDataFromCustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectDataFromCustomerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectDataFromCustomerResponseMultiError) AllErrors() []error { return m }

// CollectDataFromCustomerResponseValidationError is the validation error
// returned by CollectDataFromCustomerResponse.Validate if the designated
// constraints aren't met.
type CollectDataFromCustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectDataFromCustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectDataFromCustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectDataFromCustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectDataFromCustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectDataFromCustomerResponseValidationError) ErrorName() string {
	return "CollectDataFromCustomerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CollectDataFromCustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectDataFromCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectDataFromCustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectDataFromCustomerResponseValidationError{}

// Validate checks the field values on CollectDataFromCustomerWithStreamRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CollectDataFromCustomerWithStreamRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CollectDataFromCustomerWithStreamRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CollectDataFromCustomerWithStreamRequestMultiError, or nil if none found.
func (m *CollectDataFromCustomerWithStreamRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectDataFromCustomerWithStreamRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.CustomerData.(type) {
	case *CollectDataFromCustomerWithStreamRequest_BasicInfo:
		if v == nil {
			err := CollectDataFromCustomerWithStreamRequestValidationError{
				field:  "CustomerData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBasicInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectDataFromCustomerWithStreamRequestValidationError{
						field:  "BasicInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectDataFromCustomerWithStreamRequestValidationError{
						field:  "BasicInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBasicInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectDataFromCustomerWithStreamRequestValidationError{
					field:  "BasicInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CollectDataFromCustomerWithStreamRequest_PanImgChunk:
		if v == nil {
			err := CollectDataFromCustomerWithStreamRequestValidationError{
				field:  "CustomerData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PanImgChunk
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CollectDataFromCustomerWithStreamRequestMultiError(errors)
	}

	return nil
}

// CollectDataFromCustomerWithStreamRequestMultiError is an error wrapping
// multiple validation errors returned by
// CollectDataFromCustomerWithStreamRequest.ValidateAll() if the designated
// constraints aren't met.
type CollectDataFromCustomerWithStreamRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectDataFromCustomerWithStreamRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectDataFromCustomerWithStreamRequestMultiError) AllErrors() []error { return m }

// CollectDataFromCustomerWithStreamRequestValidationError is the validation
// error returned by CollectDataFromCustomerWithStreamRequest.Validate if the
// designated constraints aren't met.
type CollectDataFromCustomerWithStreamRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectDataFromCustomerWithStreamRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectDataFromCustomerWithStreamRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectDataFromCustomerWithStreamRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectDataFromCustomerWithStreamRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectDataFromCustomerWithStreamRequestValidationError) ErrorName() string {
	return "CollectDataFromCustomerWithStreamRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CollectDataFromCustomerWithStreamRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectDataFromCustomerWithStreamRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectDataFromCustomerWithStreamRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectDataFromCustomerWithStreamRequestValidationError{}

// Validate checks the field values on BasicCustomerInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BasicCustomerInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BasicCustomerInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BasicCustomerInfoMultiError, or nil if none found.
func (m *BasicCustomerInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *BasicCustomerInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for OnboardingType

	// no validation rules for ProofType

	// no validation rules for ImgType

	if len(errors) > 0 {
		return BasicCustomerInfoMultiError(errors)
	}

	return nil
}

// BasicCustomerInfoMultiError is an error wrapping multiple validation errors
// returned by BasicCustomerInfo.ValidateAll() if the designated constraints
// aren't met.
type BasicCustomerInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BasicCustomerInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BasicCustomerInfoMultiError) AllErrors() []error { return m }

// BasicCustomerInfoValidationError is the validation error returned by
// BasicCustomerInfo.Validate if the designated constraints aren't met.
type BasicCustomerInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BasicCustomerInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BasicCustomerInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BasicCustomerInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BasicCustomerInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BasicCustomerInfoValidationError) ErrorName() string {
	return "BasicCustomerInfoValidationError"
}

// Error satisfies the builtin error interface
func (e BasicCustomerInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBasicCustomerInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BasicCustomerInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BasicCustomerInfoValidationError{}

// Validate checks the field values on
// CollectDataFromCustomerWithStreamResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CollectDataFromCustomerWithStreamResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CollectDataFromCustomerWithStreamResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CollectDataFromCustomerWithStreamResponseMultiError, or nil if none found.
func (m *CollectDataFromCustomerWithStreamResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectDataFromCustomerWithStreamResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectDataFromCustomerWithStreamResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectDataFromCustomerWithStreamResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectDataFromCustomerWithStreamResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CollectDataFromCustomerWithStreamResponseMultiError(errors)
	}

	return nil
}

// CollectDataFromCustomerWithStreamResponseMultiError is an error wrapping
// multiple validation errors returned by
// CollectDataFromCustomerWithStreamResponse.ValidateAll() if the designated
// constraints aren't met.
type CollectDataFromCustomerWithStreamResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectDataFromCustomerWithStreamResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectDataFromCustomerWithStreamResponseMultiError) AllErrors() []error { return m }

// CollectDataFromCustomerWithStreamResponseValidationError is the validation
// error returned by CollectDataFromCustomerWithStreamResponse.Validate if the
// designated constraints aren't met.
type CollectDataFromCustomerWithStreamResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectDataFromCustomerWithStreamResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectDataFromCustomerWithStreamResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectDataFromCustomerWithStreamResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectDataFromCustomerWithStreamResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectDataFromCustomerWithStreamResponseValidationError) ErrorName() string {
	return "CollectDataFromCustomerWithStreamResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CollectDataFromCustomerWithStreamResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectDataFromCustomerWithStreamResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectDataFromCustomerWithStreamResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectDataFromCustomerWithStreamResponseValidationError{}

// Validate checks the field values on GetInvestmentOnbStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentOnbStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentOnbStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetInvestmentOnbStatusRequestMultiError, or nil if none found.
func (m *GetInvestmentOnbStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentOnbStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetInvestmentOnbStatusRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentOnbStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetInvestmentOnbStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type GetInvestmentOnbStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentOnbStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentOnbStatusRequestMultiError) AllErrors() []error { return m }

// GetInvestmentOnbStatusRequestValidationError is the validation error
// returned by GetInvestmentOnbStatusRequest.Validate if the designated
// constraints aren't met.
type GetInvestmentOnbStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentOnbStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentOnbStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentOnbStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentOnbStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentOnbStatusRequestValidationError) ErrorName() string {
	return "GetInvestmentOnbStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentOnbStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentOnbStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentOnbStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentOnbStatusRequestValidationError{}

// Validate checks the field values on UpdateOnbStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateOnbStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateOnbStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateOnbStatusRequestMultiError, or nil if none found.
func (m *UpdateOnbStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateOnbStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for OnboardingStatus

	// no validation rules for OnboardingType

	if len(errors) > 0 {
		return UpdateOnbStatusRequestMultiError(errors)
	}

	return nil
}

// UpdateOnbStatusRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateOnbStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateOnbStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateOnbStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateOnbStatusRequestMultiError) AllErrors() []error { return m }

// UpdateOnbStatusRequestValidationError is the validation error returned by
// UpdateOnbStatusRequest.Validate if the designated constraints aren't met.
type UpdateOnbStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateOnbStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateOnbStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateOnbStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateOnbStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateOnbStatusRequestValidationError) ErrorName() string {
	return "UpdateOnbStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateOnbStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateOnbStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateOnbStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateOnbStatusRequestValidationError{}

// Validate checks the field values on GetInvestmentOnbStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentOnbStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentOnbStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetInvestmentOnbStatusResponseMultiError, or nil if none found.
func (m *GetInvestmentOnbStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentOnbStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentOnbStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentOnbStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentOnbStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetInvestmentOnbInfo()))
		i := 0
		for key := range m.GetInvestmentOnbInfo() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetInvestmentOnbInfo()[key]
			_ = val

			// no validation rules for InvestmentOnbInfo[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetInvestmentOnbStatusResponseValidationError{
							field:  fmt.Sprintf("InvestmentOnbInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetInvestmentOnbStatusResponseValidationError{
							field:  fmt.Sprintf("InvestmentOnbInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetInvestmentOnbStatusResponseValidationError{
						field:  fmt.Sprintf("InvestmentOnbInfo[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetInvestmentOnbStatusResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentOnbStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetInvestmentOnbStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type GetInvestmentOnbStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentOnbStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentOnbStatusResponseMultiError) AllErrors() []error { return m }

// GetInvestmentOnbStatusResponseValidationError is the validation error
// returned by GetInvestmentOnbStatusResponse.Validate if the designated
// constraints aren't met.
type GetInvestmentOnbStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentOnbStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentOnbStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentOnbStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentOnbStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentOnbStatusResponseValidationError) ErrorName() string {
	return "GetInvestmentOnbStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentOnbStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentOnbStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentOnbStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentOnbStatusResponseValidationError{}

// Validate checks the field values on UpdateOnbStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateOnbStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateOnbStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateOnbStatusResponseMultiError, or nil if none found.
func (m *UpdateOnbStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateOnbStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateOnbStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateOnbStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateOnbStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateOnbStatusResponseMultiError(errors)
	}

	return nil
}

// UpdateOnbStatusResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateOnbStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateOnbStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateOnbStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateOnbStatusResponseMultiError) AllErrors() []error { return m }

// UpdateOnbStatusResponseValidationError is the validation error returned by
// UpdateOnbStatusResponse.Validate if the designated constraints aren't met.
type UpdateOnbStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateOnbStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateOnbStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateOnbStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateOnbStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateOnbStatusResponseValidationError) ErrorName() string {
	return "UpdateOnbStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateOnbStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateOnbStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateOnbStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateOnbStatusResponseValidationError{}

// Validate checks the field values on InvestmentOnbData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InvestmentOnbData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentOnbData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentOnbDataMultiError, or nil if none found.
func (m *InvestmentOnbData) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentOnbData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OnboardingStatus

	// no validation rules for OnboardingStep

	if len(errors) > 0 {
		return InvestmentOnbDataMultiError(errors)
	}

	return nil
}

// InvestmentOnbDataMultiError is an error wrapping multiple validation errors
// returned by InvestmentOnbData.ValidateAll() if the designated constraints
// aren't met.
type InvestmentOnbDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentOnbDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentOnbDataMultiError) AllErrors() []error { return m }

// InvestmentOnbDataValidationError is the validation error returned by
// InvestmentOnbData.Validate if the designated constraints aren't met.
type InvestmentOnbDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentOnbDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentOnbDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentOnbDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentOnbDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentOnbDataValidationError) ErrorName() string {
	return "InvestmentOnbDataValidationError"
}

// Error satisfies the builtin error interface
func (e InvestmentOnbDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentOnbData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentOnbDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentOnbDataValidationError{}

// Validate checks the field values on GetInvestmentDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestmentDataRequestMultiError, or nil if none found.
func (m *GetInvestmentDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetInvestmentDataRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentDataRequestMultiError is an error wrapping multiple validation
// errors returned by GetInvestmentDataRequest.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentDataRequestMultiError) AllErrors() []error { return m }

// GetInvestmentDataRequestValidationError is the validation error returned by
// GetInvestmentDataRequest.Validate if the designated constraints aren't met.
type GetInvestmentDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentDataRequestValidationError) ErrorName() string {
	return "GetInvestmentDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentDataRequestValidationError{}

// Validate checks the field values on GetInvestmentDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestmentDataResponseMultiError, or nil if none found.
func (m *GetInvestmentDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetInvestmentDetailInfo()))
		i := 0
		for key := range m.GetInvestmentDetailInfo() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetInvestmentDetailInfo()[key]
			_ = val

			// no validation rules for InvestmentDetailInfo[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetInvestmentDataResponseValidationError{
							field:  fmt.Sprintf("InvestmentDetailInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetInvestmentDataResponseValidationError{
							field:  fmt.Sprintf("InvestmentDetailInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetInvestmentDataResponseValidationError{
						field:  fmt.Sprintf("InvestmentDetailInfo[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetInvestmentDataResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentDataResponseMultiError is an error wrapping multiple validation
// errors returned by GetInvestmentDataResponse.ValidateAll() if the
// designated constraints aren't met.
type GetInvestmentDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentDataResponseMultiError) AllErrors() []error { return m }

// GetInvestmentDataResponseValidationError is the validation error returned by
// GetInvestmentDataResponse.Validate if the designated constraints aren't met.
type GetInvestmentDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentDataResponseValidationError) ErrorName() string {
	return "GetInvestmentDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentDataResponseValidationError{}

// Validate checks the field values on GetOnboardingTroubleshootDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetOnboardingTroubleshootDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetOnboardingTroubleshootDetailsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetOnboardingTroubleshootDetailsRequestMultiError, or nil if none found.
func (m *GetOnboardingTroubleshootDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOnboardingTroubleshootDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for OnboardingType

	if len(errors) > 0 {
		return GetOnboardingTroubleshootDetailsRequestMultiError(errors)
	}

	return nil
}

// GetOnboardingTroubleshootDetailsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetOnboardingTroubleshootDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOnboardingTroubleshootDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOnboardingTroubleshootDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOnboardingTroubleshootDetailsRequestMultiError) AllErrors() []error { return m }

// GetOnboardingTroubleshootDetailsRequestValidationError is the validation
// error returned by GetOnboardingTroubleshootDetailsRequest.Validate if the
// designated constraints aren't met.
type GetOnboardingTroubleshootDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOnboardingTroubleshootDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOnboardingTroubleshootDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOnboardingTroubleshootDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOnboardingTroubleshootDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOnboardingTroubleshootDetailsRequestValidationError) ErrorName() string {
	return "GetOnboardingTroubleshootDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOnboardingTroubleshootDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOnboardingTroubleshootDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOnboardingTroubleshootDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOnboardingTroubleshootDetailsRequestValidationError{}

// Validate checks the field values on GetOnboardingTroubleshootDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetOnboardingTroubleshootDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetOnboardingTroubleshootDetailsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetOnboardingTroubleshootDetailsResponseMultiError, or nil if none found.
func (m *GetOnboardingTroubleshootDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOnboardingTroubleshootDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOnboardingTroubleshootDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOnboardingTroubleshootDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOnboardingTroubleshootDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOnboardingTroubleshootDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOnboardingTroubleshootDetailsResponseValidationError{
					field:  "OnboardingTroubleshootDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOnboardingTroubleshootDetailsResponseValidationError{
					field:  "OnboardingTroubleshootDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOnboardingTroubleshootDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOnboardingTroubleshootDetailsResponseValidationError{
				field:  "OnboardingTroubleshootDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOnboardingTroubleshootDetailsResponseMultiError(errors)
	}

	return nil
}

// GetOnboardingTroubleshootDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetOnboardingTroubleshootDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetOnboardingTroubleshootDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOnboardingTroubleshootDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOnboardingTroubleshootDetailsResponseMultiError) AllErrors() []error { return m }

// GetOnboardingTroubleshootDetailsResponseValidationError is the validation
// error returned by GetOnboardingTroubleshootDetailsResponse.Validate if the
// designated constraints aren't met.
type GetOnboardingTroubleshootDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOnboardingTroubleshootDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOnboardingTroubleshootDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOnboardingTroubleshootDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOnboardingTroubleshootDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOnboardingTroubleshootDetailsResponseValidationError) ErrorName() string {
	return "GetOnboardingTroubleshootDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOnboardingTroubleshootDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOnboardingTroubleshootDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOnboardingTroubleshootDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOnboardingTroubleshootDetailsResponseValidationError{}

// Validate checks the field values on DownloadDigilockerDocsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DownloadDigilockerDocsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadDigilockerDocsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DownloadDigilockerDocsRequestMultiError, or nil if none found.
func (m *DownloadDigilockerDocsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadDigilockerDocsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AuthCode

	if len(errors) > 0 {
		return DownloadDigilockerDocsRequestMultiError(errors)
	}

	return nil
}

// DownloadDigilockerDocsRequestMultiError is an error wrapping multiple
// validation errors returned by DownloadDigilockerDocsRequest.ValidateAll()
// if the designated constraints aren't met.
type DownloadDigilockerDocsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadDigilockerDocsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadDigilockerDocsRequestMultiError) AllErrors() []error { return m }

// DownloadDigilockerDocsRequestValidationError is the validation error
// returned by DownloadDigilockerDocsRequest.Validate if the designated
// constraints aren't met.
type DownloadDigilockerDocsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadDigilockerDocsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadDigilockerDocsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadDigilockerDocsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadDigilockerDocsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadDigilockerDocsRequestValidationError) ErrorName() string {
	return "DownloadDigilockerDocsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DownloadDigilockerDocsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadDigilockerDocsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadDigilockerDocsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadDigilockerDocsRequestValidationError{}

// Validate checks the field values on DownloadDigilockerDocsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DownloadDigilockerDocsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadDigilockerDocsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DownloadDigilockerDocsResponseMultiError, or nil if none found.
func (m *DownloadDigilockerDocsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadDigilockerDocsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DownloadDigilockerDocsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DownloadDigilockerDocsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DownloadDigilockerDocsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DownloadDigilockerDocsResponseMultiError(errors)
	}

	return nil
}

// DownloadDigilockerDocsResponseMultiError is an error wrapping multiple
// validation errors returned by DownloadDigilockerDocsResponse.ValidateAll()
// if the designated constraints aren't met.
type DownloadDigilockerDocsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadDigilockerDocsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadDigilockerDocsResponseMultiError) AllErrors() []error { return m }

// DownloadDigilockerDocsResponseValidationError is the validation error
// returned by DownloadDigilockerDocsResponse.Validate if the designated
// constraints aren't met.
type DownloadDigilockerDocsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadDigilockerDocsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadDigilockerDocsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadDigilockerDocsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadDigilockerDocsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadDigilockerDocsResponseValidationError) ErrorName() string {
	return "DownloadDigilockerDocsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DownloadDigilockerDocsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadDigilockerDocsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadDigilockerDocsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadDigilockerDocsResponseValidationError{}

// Validate checks the field values on GetInvestmentDataV2Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentDataV2Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentDataV2Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestmentDataV2RequestMultiError, or nil if none found.
func (m *GetInvestmentDataV2Request) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentDataV2Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetInvestmentDataV2RequestMultiError(errors)
	}

	return nil
}

// GetInvestmentDataV2RequestMultiError is an error wrapping multiple
// validation errors returned by GetInvestmentDataV2Request.ValidateAll() if
// the designated constraints aren't met.
type GetInvestmentDataV2RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentDataV2RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentDataV2RequestMultiError) AllErrors() []error { return m }

// GetInvestmentDataV2RequestValidationError is the validation error returned
// by GetInvestmentDataV2Request.Validate if the designated constraints aren't met.
type GetInvestmentDataV2RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentDataV2RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentDataV2RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentDataV2RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentDataV2RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentDataV2RequestValidationError) ErrorName() string {
	return "GetInvestmentDataV2RequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentDataV2RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentDataV2Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentDataV2RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentDataV2RequestValidationError{}

// Validate checks the field values on GetInvestmentDataV2Response with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentDataV2Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentDataV2Response with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestmentDataV2ResponseMultiError, or nil if none found.
func (m *GetInvestmentDataV2Response) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentDataV2Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentDataV2ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentDataV2ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentDataV2ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetInvestmentDetailInfo()))
		i := 0
		for key := range m.GetInvestmentDetailInfo() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetInvestmentDetailInfo()[key]
			_ = val

			// no validation rules for InvestmentDetailInfo[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetInvestmentDataV2ResponseValidationError{
							field:  fmt.Sprintf("InvestmentDetailInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetInvestmentDataV2ResponseValidationError{
							field:  fmt.Sprintf("InvestmentDetailInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetInvestmentDataV2ResponseValidationError{
						field:  fmt.Sprintf("InvestmentDetailInfo[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetInvestmentDataV2ResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentDataV2ResponseMultiError is an error wrapping multiple
// validation errors returned by GetInvestmentDataV2Response.ValidateAll() if
// the designated constraints aren't met.
type GetInvestmentDataV2ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentDataV2ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentDataV2ResponseMultiError) AllErrors() []error { return m }

// GetInvestmentDataV2ResponseValidationError is the validation error returned
// by GetInvestmentDataV2Response.Validate if the designated constraints
// aren't met.
type GetInvestmentDataV2ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentDataV2ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentDataV2ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentDataV2ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentDataV2ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentDataV2ResponseValidationError) ErrorName() string {
	return "GetInvestmentDataV2ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentDataV2ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentDataV2Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentDataV2ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentDataV2ResponseValidationError{}

// Validate checks the field values on InitiateDocumentExtractionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitiateDocumentExtractionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateDocumentExtractionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiateDocumentExtractionRequestMultiError, or nil if none found.
func (m *InitiateDocumentExtractionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateDocumentExtractionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for DocumentType

	if len(errors) > 0 {
		return InitiateDocumentExtractionRequestMultiError(errors)
	}

	return nil
}

// InitiateDocumentExtractionRequestMultiError is an error wrapping multiple
// validation errors returned by
// InitiateDocumentExtractionRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateDocumentExtractionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateDocumentExtractionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateDocumentExtractionRequestMultiError) AllErrors() []error { return m }

// InitiateDocumentExtractionRequestValidationError is the validation error
// returned by InitiateDocumentExtractionRequest.Validate if the designated
// constraints aren't met.
type InitiateDocumentExtractionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateDocumentExtractionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateDocumentExtractionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateDocumentExtractionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateDocumentExtractionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateDocumentExtractionRequestValidationError) ErrorName() string {
	return "InitiateDocumentExtractionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateDocumentExtractionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateDocumentExtractionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateDocumentExtractionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateDocumentExtractionRequestValidationError{}

// Validate checks the field values on InitiateDocumentExtractionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitiateDocumentExtractionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateDocumentExtractionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiateDocumentExtractionResponseMultiError, or nil if none found.
func (m *InitiateDocumentExtractionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateDocumentExtractionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateDocumentExtractionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateDocumentExtractionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateDocumentExtractionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DocumentUrl

	if len(errors) > 0 {
		return InitiateDocumentExtractionResponseMultiError(errors)
	}

	return nil
}

// InitiateDocumentExtractionResponseMultiError is an error wrapping multiple
// validation errors returned by
// InitiateDocumentExtractionResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateDocumentExtractionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateDocumentExtractionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateDocumentExtractionResponseMultiError) AllErrors() []error { return m }

// InitiateDocumentExtractionResponseValidationError is the validation error
// returned by InitiateDocumentExtractionResponse.Validate if the designated
// constraints aren't met.
type InitiateDocumentExtractionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateDocumentExtractionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateDocumentExtractionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateDocumentExtractionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateDocumentExtractionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateDocumentExtractionResponseValidationError) ErrorName() string {
	return "InitiateDocumentExtractionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateDocumentExtractionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateDocumentExtractionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateDocumentExtractionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateDocumentExtractionResponseValidationError{}

// Validate checks the field values on GetDocumentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDocumentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDocumentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDocumentRequestMultiError, or nil if none found.
func (m *GetDocumentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDocumentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for DocumentType

	if len(errors) > 0 {
		return GetDocumentRequestMultiError(errors)
	}

	return nil
}

// GetDocumentRequestMultiError is an error wrapping multiple validation errors
// returned by GetDocumentRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDocumentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDocumentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDocumentRequestMultiError) AllErrors() []error { return m }

// GetDocumentRequestValidationError is the validation error returned by
// GetDocumentRequest.Validate if the designated constraints aren't met.
type GetDocumentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDocumentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDocumentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDocumentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDocumentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDocumentRequestValidationError) ErrorName() string {
	return "GetDocumentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDocumentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDocumentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDocumentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDocumentRequestValidationError{}

// Validate checks the field values on GetDocumentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDocumentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDocumentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDocumentResponseMultiError, or nil if none found.
func (m *GetDocumentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDocumentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDocumentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDocumentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDocumentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DocumentUrl

	if len(errors) > 0 {
		return GetDocumentResponseMultiError(errors)
	}

	return nil
}

// GetDocumentResponseMultiError is an error wrapping multiple validation
// errors returned by GetDocumentResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDocumentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDocumentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDocumentResponseMultiError) AllErrors() []error { return m }

// GetDocumentResponseValidationError is the validation error returned by
// GetDocumentResponse.Validate if the designated constraints aren't met.
type GetDocumentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDocumentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDocumentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDocumentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDocumentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDocumentResponseValidationError) ErrorName() string {
	return "GetDocumentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDocumentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDocumentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDocumentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDocumentResponseValidationError{}

// Validate checks the field values on GetOrCreateUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrCreateUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrCreateUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrCreateUserRequestMultiError, or nil if none found.
func (m *GetOrCreateUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrCreateUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetOrCreateUserRequestMultiError(errors)
	}

	return nil
}

// GetOrCreateUserRequestMultiError is an error wrapping multiple validation
// errors returned by GetOrCreateUserRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOrCreateUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrCreateUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrCreateUserRequestMultiError) AllErrors() []error { return m }

// GetOrCreateUserRequestValidationError is the validation error returned by
// GetOrCreateUserRequest.Validate if the designated constraints aren't met.
type GetOrCreateUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrCreateUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrCreateUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrCreateUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrCreateUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrCreateUserRequestValidationError) ErrorName() string {
	return "GetOrCreateUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrCreateUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrCreateUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrCreateUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrCreateUserRequestValidationError{}

// Validate checks the field values on GetOrCreateUserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrCreateUserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrCreateUserResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrCreateUserResponseMultiError, or nil if none found.
func (m *GetOrCreateUserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrCreateUserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrCreateUserResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrCreateUserResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrCreateUserResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrCreateUserResponseValidationError{
					field:  "UserDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrCreateUserResponseValidationError{
					field:  "UserDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrCreateUserResponseValidationError{
				field:  "UserDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOrCreateUserResponseMultiError(errors)
	}

	return nil
}

// GetOrCreateUserResponseMultiError is an error wrapping multiple validation
// errors returned by GetOrCreateUserResponse.ValidateAll() if the designated
// constraints aren't met.
type GetOrCreateUserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrCreateUserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrCreateUserResponseMultiError) AllErrors() []error { return m }

// GetOrCreateUserResponseValidationError is the validation error returned by
// GetOrCreateUserResponse.Validate if the designated constraints aren't met.
type GetOrCreateUserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrCreateUserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrCreateUserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrCreateUserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrCreateUserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrCreateUserResponseValidationError) ErrorName() string {
	return "GetOrCreateUserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrCreateUserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrCreateUserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrCreateUserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrCreateUserResponseValidationError{}

// Validate checks the field values on GetOrCreateUserResponse_UserDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetOrCreateUserResponse_UserDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrCreateUserResponse_UserDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetOrCreateUserResponse_UserDetailsMultiError, or nil if none found.
func (m *GetOrCreateUserResponse_UserDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrCreateUserResponse_UserDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrCreateUserResponse_UserDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrCreateUserResponse_UserDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrCreateUserResponse_UserDetailsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrCreateUserResponse_UserDetailsValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrCreateUserResponse_UserDetailsValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrCreateUserResponse_UserDetailsValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrCreateUserResponse_UserDetailsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrCreateUserResponse_UserDetailsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrCreateUserResponse_UserDetailsValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPanDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrCreateUserResponse_UserDetailsValidationError{
					field:  "PanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrCreateUserResponse_UserDetailsValidationError{
					field:  "PanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPanDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrCreateUserResponse_UserDetailsValidationError{
				field:  "PanDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOrCreateUserResponse_UserDetailsMultiError(errors)
	}

	return nil
}

// GetOrCreateUserResponse_UserDetailsMultiError is an error wrapping multiple
// validation errors returned by
// GetOrCreateUserResponse_UserDetails.ValidateAll() if the designated
// constraints aren't met.
type GetOrCreateUserResponse_UserDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrCreateUserResponse_UserDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrCreateUserResponse_UserDetailsMultiError) AllErrors() []error { return m }

// GetOrCreateUserResponse_UserDetailsValidationError is the validation error
// returned by GetOrCreateUserResponse_UserDetails.Validate if the designated
// constraints aren't met.
type GetOrCreateUserResponse_UserDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrCreateUserResponse_UserDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrCreateUserResponse_UserDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrCreateUserResponse_UserDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrCreateUserResponse_UserDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrCreateUserResponse_UserDetailsValidationError) ErrorName() string {
	return "GetOrCreateUserResponse_UserDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrCreateUserResponse_UserDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrCreateUserResponse_UserDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrCreateUserResponse_UserDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrCreateUserResponse_UserDetailsValidationError{}
