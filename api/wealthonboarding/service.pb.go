// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/wealthonboarding/service.proto

package wealthonboarding

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InitiateDocumentExtractionResponse_Status int32

const (
	// initiation successful
	InitiateDocumentExtractionResponse_OK InitiateDocumentExtractionResponse_Status = 0
	// docket is not downloaded for the user
	InitiateDocumentExtractionResponse_DOCKET_NOT_DOWNLOADED InitiateDocumentExtractionResponse_Status = 101
)

// Enum value maps for InitiateDocumentExtractionResponse_Status.
var (
	InitiateDocumentExtractionResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "DOCKET_NOT_DOWNLOADED",
	}
	InitiateDocumentExtractionResponse_Status_value = map[string]int32{
		"OK":                    0,
		"DOCKET_NOT_DOWNLOADED": 101,
	}
)

func (x InitiateDocumentExtractionResponse_Status) Enum() *InitiateDocumentExtractionResponse_Status {
	p := new(InitiateDocumentExtractionResponse_Status)
	*p = x
	return p
}

func (x InitiateDocumentExtractionResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InitiateDocumentExtractionResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_wealthonboarding_service_proto_enumTypes[0].Descriptor()
}

func (InitiateDocumentExtractionResponse_Status) Type() protoreflect.EnumType {
	return &file_api_wealthonboarding_service_proto_enumTypes[0]
}

func (x InitiateDocumentExtractionResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InitiateDocumentExtractionResponse_Status.Descriptor instead.
func (InitiateDocumentExtractionResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{23, 0}
}

type GetDocumentResponse_Status int32

const (
	// initiation successful
	GetDocumentResponse_OK GetDocumentResponse_Status = 0
	// Getting the document is either impossible or not supported
	GetDocumentResponse_NOT_FOUND GetDocumentResponse_Status = 5
	// document fetch is in progress
	GetDocumentResponse_OPERATION_IN_PROGRESS GetDocumentResponse_Status = 101
	// Deprecated: Returning NOT_FOUND in case actor ID is not present in wealth onboarding details
	// invalid actor id in request
	// actor is not present in WONB system
	//
	// Deprecated: Marked as deprecated in api/wealthonboarding/service.proto.
	GetDocumentResponse_INVALID_ARGUMENT_ACTOR_NOT_PRESENT GetDocumentResponse_Status = 102
	// Deprecated: Returning NOT_STARTED in case workflow is not started
	// document extraction workflow for the requested document type is not found
	//
	// Deprecated: Marked as deprecated in api/wealthonboarding/service.proto.
	GetDocumentResponse_DOCUMENT_EXTRACTION_WORKFLOW_NOT_FOUND GetDocumentResponse_Status = 103
	// Document fetch is possible/supported but has not been started
	GetDocumentResponse_NOT_STARTED GetDocumentResponse_Status = 104
)

// Enum value maps for GetDocumentResponse_Status.
var (
	GetDocumentResponse_Status_name = map[int32]string{
		0:   "OK",
		5:   "NOT_FOUND",
		101: "OPERATION_IN_PROGRESS",
		102: "INVALID_ARGUMENT_ACTOR_NOT_PRESENT",
		103: "DOCUMENT_EXTRACTION_WORKFLOW_NOT_FOUND",
		104: "NOT_STARTED",
	}
	GetDocumentResponse_Status_value = map[string]int32{
		"OK":                                     0,
		"NOT_FOUND":                              5,
		"OPERATION_IN_PROGRESS":                  101,
		"INVALID_ARGUMENT_ACTOR_NOT_PRESENT":     102,
		"DOCUMENT_EXTRACTION_WORKFLOW_NOT_FOUND": 103,
		"NOT_STARTED":                            104,
	}
)

func (x GetDocumentResponse_Status) Enum() *GetDocumentResponse_Status {
	p := new(GetDocumentResponse_Status)
	*p = x
	return p
}

func (x GetDocumentResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetDocumentResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_wealthonboarding_service_proto_enumTypes[1].Descriptor()
}

func (GetDocumentResponse_Status) Type() protoreflect.EnumType {
	return &file_api_wealthonboarding_service_proto_enumTypes[1]
}

func (x GetDocumentResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetDocumentResponse_Status.Descriptor instead.
func (GetDocumentResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{25, 0}
}

type GetOnboardingStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id of the customer for which onboarding status is being fetched
	ActorId        string         `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	OnboardingType OnboardingType `protobuf:"varint,2,opt,name=onboarding_type,json=onboardingType,proto3,enum=wealthonboarding.OnboardingType" json:"onboarding_type,omitempty"`
}

func (x *GetOnboardingStatusRequest) Reset() {
	*x = GetOnboardingStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnboardingStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnboardingStatusRequest) ProtoMessage() {}

func (x *GetOnboardingStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnboardingStatusRequest.ProtoReflect.Descriptor instead.
func (*GetOnboardingStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetOnboardingStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetOnboardingStatusRequest) GetOnboardingType() OnboardingType {
	if x != nil {
		return x.OnboardingType
	}
	return OnboardingType_ONBOARDING_TYPE_UNSPECIFIED
}

type GetOnboardingStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status           *rpc.Status      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	OnboardingId     string           `protobuf:"bytes,2,opt,name=onboarding_id,json=onboardingId,proto3" json:"onboarding_id,omitempty"`
	OnboardingStatus OnboardingStatus `protobuf:"varint,3,opt,name=onboarding_status,json=onboardingStatus,proto3,enum=wealthonboarding.OnboardingStatus" json:"onboarding_status,omitempty"`
	CurrentStep      OnboardingStep   `protobuf:"varint,4,opt,name=current_step,json=currentStep,proto3,enum=wealthonboarding.OnboardingStep" json:"current_step,omitempty"`
	// OnboardingEligibility can be used to identify whether a user is able to wealth onboard or not
	Eligibility OnboardingEligibility `protobuf:"varint,5,opt,name=eligibility,proto3,enum=wealthonboarding.OnboardingEligibility" json:"eligibility,omitempty"`
}

func (x *GetOnboardingStatusResponse) Reset() {
	*x = GetOnboardingStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnboardingStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnboardingStatusResponse) ProtoMessage() {}

func (x *GetOnboardingStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnboardingStatusResponse.ProtoReflect.Descriptor instead.
func (*GetOnboardingStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetOnboardingStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOnboardingStatusResponse) GetOnboardingId() string {
	if x != nil {
		return x.OnboardingId
	}
	return ""
}

func (x *GetOnboardingStatusResponse) GetOnboardingStatus() OnboardingStatus {
	if x != nil {
		return x.OnboardingStatus
	}
	return OnboardingStatus_ONBOARDING_STATUS_UNSPECIFIED
}

func (x *GetOnboardingStatusResponse) GetCurrentStep() OnboardingStep {
	if x != nil {
		return x.CurrentStep
	}
	return OnboardingStep_ONBOARDING_STEP_UNSPECIFIED
}

func (x *GetOnboardingStatusResponse) GetEligibility() OnboardingEligibility {
	if x != nil {
		return x.Eligibility
	}
	return OnboardingEligibility_ONBOARDING_ELIGIBILITY_UNSPECIFIED
}

type GetNextOnboardingStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id of the customer for which onboarding status is being fetched
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// depending on this returned deep links will change
	WealthFlow WealthFlow `protobuf:"varint,2,opt,name=wealth_flow,json=wealthFlow,proto3,enum=wealthonboarding.WealthFlow" json:"wealth_flow,omitempty"`
	// onboarding type
	OnboardingType OnboardingType `protobuf:"varint,3,opt,name=onboarding_type,json=onboardingType,proto3,enum=wealthonboarding.OnboardingType" json:"onboarding_type,omitempty"`
	// entrypoint of flow
	EntryPoint OnboardingStepEntrypoint `protobuf:"varint,4,opt,name=entry_point,json=entryPoint,proto3,enum=wealthonboarding.OnboardingStepEntrypoint" json:"entry_point,omitempty"`
}

func (x *GetNextOnboardingStatusRequest) Reset() {
	*x = GetNextOnboardingStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextOnboardingStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextOnboardingStatusRequest) ProtoMessage() {}

func (x *GetNextOnboardingStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextOnboardingStatusRequest.ProtoReflect.Descriptor instead.
func (*GetNextOnboardingStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetNextOnboardingStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetNextOnboardingStatusRequest) GetWealthFlow() WealthFlow {
	if x != nil {
		return x.WealthFlow
	}
	return WealthFlow_WEALTH_FLOW_UNSPECIFIED
}

func (x *GetNextOnboardingStatusRequest) GetOnboardingType() OnboardingType {
	if x != nil {
		return x.OnboardingType
	}
	return OnboardingType_ONBOARDING_TYPE_UNSPECIFIED
}

func (x *GetNextOnboardingStatusRequest) GetEntryPoint() OnboardingStepEntrypoint {
	if x != nil {
		return x.EntryPoint
	}
	return OnboardingStepEntrypoint_ONBOARDING_STEP_ENTRYPOINT_UNSPECIFIED
}

type GetNextOnboardingStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// deeplink for the for the user to perform the step if user intervention is needed in the step
	NextStep *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_step,json=nextStep,proto3" json:"next_step,omitempty"`
	// current onboarding status
	OnboardingStatus OnboardingStatus `protobuf:"varint,3,opt,name=onboarding_status,json=onboardingStatus,proto3,enum=wealthonboarding.OnboardingStatus" json:"onboarding_status,omitempty"`
}

func (x *GetNextOnboardingStatusResponse) Reset() {
	*x = GetNextOnboardingStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextOnboardingStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextOnboardingStatusResponse) ProtoMessage() {}

func (x *GetNextOnboardingStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextOnboardingStatusResponse.ProtoReflect.Descriptor instead.
func (*GetNextOnboardingStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetNextOnboardingStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetNextOnboardingStatusResponse) GetNextStep() *deeplink.Deeplink {
	if x != nil {
		return x.NextStep
	}
	return nil
}

func (x *GetNextOnboardingStatusResponse) GetOnboardingStatus() OnboardingStatus {
	if x != nil {
		return x.OnboardingStatus
	}
	return OnboardingStatus_ONBOARDING_STATUS_UNSPECIFIED
}

type CollectDataFromCustomerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId       string                 `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Gender        typesv2.Gender         `protobuf:"varint,2,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	MaritalStatus typesv2.MaritalStatus  `protobuf:"varint,3,opt,name=marital_status,json=maritalStatus,proto3,enum=api.typesv2.MaritalStatus" json:"marital_status,omitempty"`
	Pan           *typesv2.DocumentProof `protobuf:"bytes,4,opt,name=pan,proto3" json:"pan,omitempty"`
	// pan will be used as POI always
	//
	// Deprecated: Marked as deprecated in api/wealthonboarding/service.proto.
	Poi       *typesv2.DocumentProof `protobuf:"bytes,5,opt,name=poi,proto3" json:"poi,omitempty"`
	Poa       *typesv2.DocumentProof `protobuf:"bytes,6,opt,name=poa,proto3" json:"poa,omitempty"`
	Signature *common.Image          `protobuf:"bytes,7,opt,name=signature,proto3" json:"signature,omitempty"`
	// directly collecting as confirmation from customer as it is part of agreement
	//
	// Deprecated: Marked as deprecated in api/wealthonboarding/service.proto.
	ResidentialStatus typesv2.ResidentialStatus `protobuf:"varint,8,opt,name=residential_status,json=residentialStatus,proto3,enum=api.typesv2.ResidentialStatus" json:"residential_status,omitempty"`
	// directly collecting as confirmation from customer as it is part of agreement
	//
	// Deprecated: Marked as deprecated in api/wealthonboarding/service.proto.
	PoliticallyExposedStatus typesv2.PoliticallyExposedStatus `protobuf:"varint,9,opt,name=politically_exposed_status,json=politicallyExposedStatus,proto3,enum=api.typesv2.PoliticallyExposedStatus" json:"politically_exposed_status,omitempty"`
	// directly collecting as confirmation from customer as it is part of agreement
	//
	// Deprecated: Marked as deprecated in api/wealthonboarding/service.proto.
	Nationality typesv2.Nationality `protobuf:"varint,10,opt,name=nationality,proto3,enum=api.typesv2.Nationality" json:"nationality,omitempty"`
	// Deprecated: Marked as deprecated in api/wealthonboarding/service.proto.
	IncomeSlab typesv2.IncomeSlab `protobuf:"varint,11,opt,name=income_slab,json=incomeSlab,proto3,enum=api.typesv2.IncomeSlab" json:"income_slab,omitempty"` // use income instead
	// to be populated as false if user selects No Digilocker Account option on digilocker screen
	HasDigilockerAccount common.BooleanEnum `protobuf:"varint,12,opt,name=has_digilocker_account,json=hasDigilockerAccount,proto3,enum=api.typesv2.common.BooleanEnum" json:"has_digilocker_account,omitempty"`
	// to be used to identify the onboarding details for which the data is being collected
	OnboardingType            OnboardingType             `protobuf:"varint,13,opt,name=onboarding_type,json=onboardingType,proto3,enum=wealthonboarding.OnboardingType" json:"onboarding_type,omitempty"`
	Income                    int32                      `protobuf:"varint,14,opt,name=income,proto3" json:"income,omitempty"`
	NomineeDeclarationDetails *NomineeDeclarationDetails `protobuf:"bytes,15,opt,name=nominee_declaration_details,json=nomineeDeclarationDetails,proto3" json:"nominee_declaration_details,omitempty"`
	// should be passed as true when user click on 'skip now' button on WEALTH_ONBOARDING_IA_AGREEMENT_SCREEN
	SkipSigningAdvisoryAgreement common.BooleanEnum `protobuf:"varint,16,opt,name=skip_signing_advisory_agreement,json=skipSigningAdvisoryAgreement,proto3,enum=api.typesv2.common.BooleanEnum" json:"skip_signing_advisory_agreement,omitempty"`
	// figma: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?type=design&node-id=11054-3372&mode=design&t=U3KCdmlbslxDGtO8-4
	UserSubmittedPan string     `protobuf:"bytes,17,opt,name=user_submitted_pan,json=userSubmittedPan,proto3" json:"user_submitted_pan,omitempty"`
	UserSubmittedDob *date.Date `protobuf:"bytes,18,opt,name=user_submitted_dob,json=userSubmittedDob,proto3" json:"user_submitted_dob,omitempty"`
	// figma: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?type=design&node-id=11054-3372&mode=design&t=U3KCdmlbslxDGtO8-4
	UserSubmittedName string `protobuf:"bytes,19,opt,name=user_submitted_name,json=userSubmittedName,proto3" json:"user_submitted_name,omitempty"`
	// consent_id in consent table in case the EPIFI_WEALTH_INVESTMENT_RISK_PROFILE consent is saved for user
	RiskProfilingConsentId string `protobuf:"bytes,20,opt,name=risk_profiling_consent_id,json=riskProfilingConsentId,proto3" json:"risk_profiling_consent_id,omitempty"`
	// reference of entry in table investment_risk_profile
	InvestmentRiskProfileId string `protobuf:"bytes,21,opt,name=investment_risk_profile_id,json=investmentRiskProfileId,proto3" json:"investment_risk_profile_id,omitempty"`
}

func (x *CollectDataFromCustomerRequest) Reset() {
	*x = CollectDataFromCustomerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectDataFromCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectDataFromCustomerRequest) ProtoMessage() {}

func (x *CollectDataFromCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectDataFromCustomerRequest.ProtoReflect.Descriptor instead.
func (*CollectDataFromCustomerRequest) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{4}
}

func (x *CollectDataFromCustomerRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CollectDataFromCustomerRequest) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *CollectDataFromCustomerRequest) GetMaritalStatus() typesv2.MaritalStatus {
	if x != nil {
		return x.MaritalStatus
	}
	return typesv2.MaritalStatus(0)
}

func (x *CollectDataFromCustomerRequest) GetPan() *typesv2.DocumentProof {
	if x != nil {
		return x.Pan
	}
	return nil
}

// Deprecated: Marked as deprecated in api/wealthonboarding/service.proto.
func (x *CollectDataFromCustomerRequest) GetPoi() *typesv2.DocumentProof {
	if x != nil {
		return x.Poi
	}
	return nil
}

func (x *CollectDataFromCustomerRequest) GetPoa() *typesv2.DocumentProof {
	if x != nil {
		return x.Poa
	}
	return nil
}

func (x *CollectDataFromCustomerRequest) GetSignature() *common.Image {
	if x != nil {
		return x.Signature
	}
	return nil
}

// Deprecated: Marked as deprecated in api/wealthonboarding/service.proto.
func (x *CollectDataFromCustomerRequest) GetResidentialStatus() typesv2.ResidentialStatus {
	if x != nil {
		return x.ResidentialStatus
	}
	return typesv2.ResidentialStatus(0)
}

// Deprecated: Marked as deprecated in api/wealthonboarding/service.proto.
func (x *CollectDataFromCustomerRequest) GetPoliticallyExposedStatus() typesv2.PoliticallyExposedStatus {
	if x != nil {
		return x.PoliticallyExposedStatus
	}
	return typesv2.PoliticallyExposedStatus(0)
}

// Deprecated: Marked as deprecated in api/wealthonboarding/service.proto.
func (x *CollectDataFromCustomerRequest) GetNationality() typesv2.Nationality {
	if x != nil {
		return x.Nationality
	}
	return typesv2.Nationality(0)
}

// Deprecated: Marked as deprecated in api/wealthonboarding/service.proto.
func (x *CollectDataFromCustomerRequest) GetIncomeSlab() typesv2.IncomeSlab {
	if x != nil {
		return x.IncomeSlab
	}
	return typesv2.IncomeSlab(0)
}

func (x *CollectDataFromCustomerRequest) GetHasDigilockerAccount() common.BooleanEnum {
	if x != nil {
		return x.HasDigilockerAccount
	}
	return common.BooleanEnum(0)
}

func (x *CollectDataFromCustomerRequest) GetOnboardingType() OnboardingType {
	if x != nil {
		return x.OnboardingType
	}
	return OnboardingType_ONBOARDING_TYPE_UNSPECIFIED
}

func (x *CollectDataFromCustomerRequest) GetIncome() int32 {
	if x != nil {
		return x.Income
	}
	return 0
}

func (x *CollectDataFromCustomerRequest) GetNomineeDeclarationDetails() *NomineeDeclarationDetails {
	if x != nil {
		return x.NomineeDeclarationDetails
	}
	return nil
}

func (x *CollectDataFromCustomerRequest) GetSkipSigningAdvisoryAgreement() common.BooleanEnum {
	if x != nil {
		return x.SkipSigningAdvisoryAgreement
	}
	return common.BooleanEnum(0)
}

func (x *CollectDataFromCustomerRequest) GetUserSubmittedPan() string {
	if x != nil {
		return x.UserSubmittedPan
	}
	return ""
}

func (x *CollectDataFromCustomerRequest) GetUserSubmittedDob() *date.Date {
	if x != nil {
		return x.UserSubmittedDob
	}
	return nil
}

func (x *CollectDataFromCustomerRequest) GetUserSubmittedName() string {
	if x != nil {
		return x.UserSubmittedName
	}
	return ""
}

func (x *CollectDataFromCustomerRequest) GetRiskProfilingConsentId() string {
	if x != nil {
		return x.RiskProfilingConsentId
	}
	return ""
}

func (x *CollectDataFromCustomerRequest) GetInvestmentRiskProfileId() string {
	if x != nil {
		return x.InvestmentRiskProfileId
	}
	return ""
}

type CollectDataFromCustomerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CollectDataFromCustomerResponse) Reset() {
	*x = CollectDataFromCustomerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectDataFromCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectDataFromCustomerResponse) ProtoMessage() {}

func (x *CollectDataFromCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectDataFromCustomerResponse.ProtoReflect.Descriptor instead.
func (*CollectDataFromCustomerResponse) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{5}
}

func (x *CollectDataFromCustomerResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CollectDataFromCustomerWithStreamRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to CustomerData:
	//
	//	*CollectDataFromCustomerWithStreamRequest_BasicInfo
	//	*CollectDataFromCustomerWithStreamRequest_PanImgChunk
	CustomerData isCollectDataFromCustomerWithStreamRequest_CustomerData `protobuf_oneof:"CustomerData"`
}

func (x *CollectDataFromCustomerWithStreamRequest) Reset() {
	*x = CollectDataFromCustomerWithStreamRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectDataFromCustomerWithStreamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectDataFromCustomerWithStreamRequest) ProtoMessage() {}

func (x *CollectDataFromCustomerWithStreamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectDataFromCustomerWithStreamRequest.ProtoReflect.Descriptor instead.
func (*CollectDataFromCustomerWithStreamRequest) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{6}
}

func (m *CollectDataFromCustomerWithStreamRequest) GetCustomerData() isCollectDataFromCustomerWithStreamRequest_CustomerData {
	if m != nil {
		return m.CustomerData
	}
	return nil
}

func (x *CollectDataFromCustomerWithStreamRequest) GetBasicInfo() *BasicCustomerInfo {
	if x, ok := x.GetCustomerData().(*CollectDataFromCustomerWithStreamRequest_BasicInfo); ok {
		return x.BasicInfo
	}
	return nil
}

func (x *CollectDataFromCustomerWithStreamRequest) GetPanImgChunk() []byte {
	if x, ok := x.GetCustomerData().(*CollectDataFromCustomerWithStreamRequest_PanImgChunk); ok {
		return x.PanImgChunk
	}
	return nil
}

type isCollectDataFromCustomerWithStreamRequest_CustomerData interface {
	isCollectDataFromCustomerWithStreamRequest_CustomerData()
}

type CollectDataFromCustomerWithStreamRequest_BasicInfo struct {
	// first chunk of the stream must be this
	// later chunks can contain chunks of streaming data (image, video etc.)
	BasicInfo *BasicCustomerInfo `protobuf:"bytes,1,opt,name=basic_info,json=basicInfo,proto3,oneof"`
}

type CollectDataFromCustomerWithStreamRequest_PanImgChunk struct {
	// base 64 encoded PAN image data as bytes
	PanImgChunk []byte `protobuf:"bytes,2,opt,name=pan_img_chunk,json=panImgChunk,proto3,oneof"`
}

func (*CollectDataFromCustomerWithStreamRequest_BasicInfo) isCollectDataFromCustomerWithStreamRequest_CustomerData() {
}

func (*CollectDataFromCustomerWithStreamRequest_PanImgChunk) isCollectDataFromCustomerWithStreamRequest_CustomerData() {
}

type BasicCustomerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// to be used to identify the onboarding details for which the data is being collected
	OnboardingType OnboardingType            `protobuf:"varint,2,opt,name=onboarding_type,json=onboardingType,proto3,enum=wealthonboarding.OnboardingType" json:"onboarding_type,omitempty"`
	ProofType      typesv2.DocumentProofType `protobuf:"varint,3,opt,name=proof_type,json=proofType,proto3,enum=api.typesv2.DocumentProofType" json:"proof_type,omitempty"`
	ImgType        common.ImageType          `protobuf:"varint,4,opt,name=img_type,json=imgType,proto3,enum=api.typesv2.common.ImageType" json:"img_type,omitempty"`
}

func (x *BasicCustomerInfo) Reset() {
	*x = BasicCustomerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BasicCustomerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BasicCustomerInfo) ProtoMessage() {}

func (x *BasicCustomerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BasicCustomerInfo.ProtoReflect.Descriptor instead.
func (*BasicCustomerInfo) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{7}
}

func (x *BasicCustomerInfo) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *BasicCustomerInfo) GetOnboardingType() OnboardingType {
	if x != nil {
		return x.OnboardingType
	}
	return OnboardingType_ONBOARDING_TYPE_UNSPECIFIED
}

func (x *BasicCustomerInfo) GetProofType() typesv2.DocumentProofType {
	if x != nil {
		return x.ProofType
	}
	return typesv2.DocumentProofType(0)
}

func (x *BasicCustomerInfo) GetImgType() common.ImageType {
	if x != nil {
		return x.ImgType
	}
	return common.ImageType(0)
}

type CollectDataFromCustomerWithStreamResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CollectDataFromCustomerWithStreamResponse) Reset() {
	*x = CollectDataFromCustomerWithStreamResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectDataFromCustomerWithStreamResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectDataFromCustomerWithStreamResponse) ProtoMessage() {}

func (x *CollectDataFromCustomerWithStreamResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectDataFromCustomerWithStreamResponse.ProtoReflect.Descriptor instead.
func (*CollectDataFromCustomerWithStreamResponse) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{8}
}

func (x *CollectDataFromCustomerWithStreamResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetInvestmentOnbStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of actor ids for which investment status is being fetched
	ActorIds []string `protobuf:"bytes,1,rep,name=actor_ids,json=actorIds,proto3" json:"actor_ids,omitempty"`
}

func (x *GetInvestmentOnbStatusRequest) Reset() {
	*x = GetInvestmentOnbStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentOnbStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentOnbStatusRequest) ProtoMessage() {}

func (x *GetInvestmentOnbStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentOnbStatusRequest.ProtoReflect.Descriptor instead.
func (*GetInvestmentOnbStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetInvestmentOnbStatusRequest) GetActorIds() []string {
	if x != nil {
		return x.ActorIds
	}
	return nil
}

type UpdateOnbStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id of the user for which onboarding status is being updated
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// onboarding status to update
	OnboardingStatus OnboardingStatus `protobuf:"varint,2,opt,name=onboarding_status,json=onboardingStatus,proto3,enum=wealthonboarding.OnboardingStatus" json:"onboarding_status,omitempty"`
	// onboarding type wealth/pre-investment
	OnboardingType OnboardingType `protobuf:"varint,3,opt,name=onboarding_type,json=onboardingType,proto3,enum=wealthonboarding.OnboardingType" json:"onboarding_type,omitempty"`
}

func (x *UpdateOnbStatusRequest) Reset() {
	*x = UpdateOnbStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOnbStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOnbStatusRequest) ProtoMessage() {}

func (x *UpdateOnbStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOnbStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateOnbStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateOnbStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UpdateOnbStatusRequest) GetOnboardingStatus() OnboardingStatus {
	if x != nil {
		return x.OnboardingStatus
	}
	return OnboardingStatus_ONBOARDING_STATUS_UNSPECIFIED
}

func (x *UpdateOnbStatusRequest) GetOnboardingType() OnboardingType {
	if x != nil {
		return x.OnboardingType
	}
	return OnboardingType_ONBOARDING_TYPE_UNSPECIFIED
}

type GetInvestmentOnbStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// investment onboarding data is mapped to an actor, key is the actor id
	InvestmentOnbInfo map[string]*InvestmentOnbData `protobuf:"bytes,2,rep,name=investment_onb_info,json=investmentOnbInfo,proto3" json:"investment_onb_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetInvestmentOnbStatusResponse) Reset() {
	*x = GetInvestmentOnbStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentOnbStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentOnbStatusResponse) ProtoMessage() {}

func (x *GetInvestmentOnbStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentOnbStatusResponse.ProtoReflect.Descriptor instead.
func (*GetInvestmentOnbStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetInvestmentOnbStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetInvestmentOnbStatusResponse) GetInvestmentOnbInfo() map[string]*InvestmentOnbData {
	if x != nil {
		return x.InvestmentOnbInfo
	}
	return nil
}

type UpdateOnbStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateOnbStatusResponse) Reset() {
	*x = UpdateOnbStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOnbStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOnbStatusResponse) ProtoMessage() {}

func (x *UpdateOnbStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOnbStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateOnbStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateOnbStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type InvestmentOnbData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// current investment status of the user
	OnboardingStatus OnboardingStatus `protobuf:"varint,1,opt,name=onboarding_status,json=onboardingStatus,proto3,enum=wealthonboarding.OnboardingStatus" json:"onboarding_status,omitempty"`
	// current investment step of the user
	OnboardingStep OnboardingStep `protobuf:"varint,2,opt,name=onboarding_step,json=onboardingStep,proto3,enum=wealthonboarding.OnboardingStep" json:"onboarding_step,omitempty"`
}

func (x *InvestmentOnbData) Reset() {
	*x = InvestmentOnbData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvestmentOnbData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvestmentOnbData) ProtoMessage() {}

func (x *InvestmentOnbData) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvestmentOnbData.ProtoReflect.Descriptor instead.
func (*InvestmentOnbData) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{13}
}

func (x *InvestmentOnbData) GetOnboardingStatus() OnboardingStatus {
	if x != nil {
		return x.OnboardingStatus
	}
	return OnboardingStatus_ONBOARDING_STATUS_UNSPECIFIED
}

func (x *InvestmentOnbData) GetOnboardingStep() OnboardingStep {
	if x != nil {
		return x.OnboardingStep
	}
	return OnboardingStep_ONBOARDING_STEP_UNSPECIFIED
}

type GetInvestmentDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of actor ids for which investment details are to be fetched
	ActorIds []string `protobuf:"bytes,1,rep,name=actor_ids,json=actorIds,proto3" json:"actor_ids,omitempty"`
}

func (x *GetInvestmentDataRequest) Reset() {
	*x = GetInvestmentDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentDataRequest) ProtoMessage() {}

func (x *GetInvestmentDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentDataRequest.ProtoReflect.Descriptor instead.
func (*GetInvestmentDataRequest) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetInvestmentDataRequest) GetActorIds() []string {
	if x != nil {
		return x.ActorIds
	}
	return nil
}

type GetInvestmentDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// InvestmentDetailData would be null if an error occurs while fetching the specific detail for an actor
	InvestmentDetailInfo map[string]*PreInvestmentDetail `protobuf:"bytes,2,rep,name=investment_detail_info,json=investmentDetailInfo,proto3" json:"investment_detail_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetInvestmentDataResponse) Reset() {
	*x = GetInvestmentDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentDataResponse) ProtoMessage() {}

func (x *GetInvestmentDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentDataResponse.ProtoReflect.Descriptor instead.
func (*GetInvestmentDataResponse) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetInvestmentDataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetInvestmentDataResponse) GetInvestmentDetailInfo() map[string]*PreInvestmentDetail {
	if x != nil {
		return x.InvestmentDetailInfo
	}
	return nil
}

type GetOnboardingTroubleshootDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// onboarding type
	OnboardingType OnboardingType `protobuf:"varint,2,opt,name=onboarding_type,json=onboardingType,proto3,enum=wealthonboarding.OnboardingType" json:"onboarding_type,omitempty"`
}

func (x *GetOnboardingTroubleshootDetailsRequest) Reset() {
	*x = GetOnboardingTroubleshootDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnboardingTroubleshootDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnboardingTroubleshootDetailsRequest) ProtoMessage() {}

func (x *GetOnboardingTroubleshootDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnboardingTroubleshootDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetOnboardingTroubleshootDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetOnboardingTroubleshootDetailsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetOnboardingTroubleshootDetailsRequest) GetOnboardingType() OnboardingType {
	if x != nil {
		return x.OnboardingType
	}
	return OnboardingType_ONBOARDING_TYPE_UNSPECIFIED
}

type GetOnboardingTroubleshootDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Overall onboarding troubleshoot details
	OnboardingTroubleshootDetails *OnboardingTroubleshootDetails `protobuf:"bytes,2,opt,name=onboarding_troubleshoot_details,json=onboardingTroubleshootDetails,proto3" json:"onboarding_troubleshoot_details,omitempty"`
}

func (x *GetOnboardingTroubleshootDetailsResponse) Reset() {
	*x = GetOnboardingTroubleshootDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnboardingTroubleshootDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnboardingTroubleshootDetailsResponse) ProtoMessage() {}

func (x *GetOnboardingTroubleshootDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnboardingTroubleshootDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetOnboardingTroubleshootDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetOnboardingTroubleshootDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOnboardingTroubleshootDetailsResponse) GetOnboardingTroubleshootDetails() *OnboardingTroubleshootDetails {
	if x != nil {
		return x.OnboardingTroubleshootDetails
	}
	return nil
}

type DownloadDigilockerDocsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// authorization code from when user logs into DigiLocker and grants us permission
	AuthCode string `protobuf:"bytes,2,opt,name=auth_code,json=authCode,proto3" json:"auth_code,omitempty"`
}

func (x *DownloadDigilockerDocsRequest) Reset() {
	*x = DownloadDigilockerDocsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadDigilockerDocsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadDigilockerDocsRequest) ProtoMessage() {}

func (x *DownloadDigilockerDocsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadDigilockerDocsRequest.ProtoReflect.Descriptor instead.
func (*DownloadDigilockerDocsRequest) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{18}
}

func (x *DownloadDigilockerDocsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DownloadDigilockerDocsRequest) GetAuthCode() string {
	if x != nil {
		return x.AuthCode
	}
	return ""
}

type DownloadDigilockerDocsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DownloadDigilockerDocsResponse) Reset() {
	*x = DownloadDigilockerDocsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadDigilockerDocsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadDigilockerDocsResponse) ProtoMessage() {}

func (x *DownloadDigilockerDocsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadDigilockerDocsResponse.ProtoReflect.Descriptor instead.
func (*DownloadDigilockerDocsResponse) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{19}
}

func (x *DownloadDigilockerDocsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetInvestmentDataV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of actor ids for which investment details and status are to be fetched
	ActorIds []string `protobuf:"bytes,1,rep,name=actor_ids,json=actorIds,proto3" json:"actor_ids,omitempty"`
}

func (x *GetInvestmentDataV2Request) Reset() {
	*x = GetInvestmentDataV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentDataV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentDataV2Request) ProtoMessage() {}

func (x *GetInvestmentDataV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentDataV2Request.ProtoReflect.Descriptor instead.
func (*GetInvestmentDataV2Request) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetInvestmentDataV2Request) GetActorIds() []string {
	if x != nil {
		return x.ActorIds
	}
	return nil
}

type GetInvestmentDataV2Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status               *rpc.Status                        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	InvestmentDetailInfo map[string]*PreInvestmentDetailsV2 `protobuf:"bytes,2,rep,name=investment_detail_info,json=investmentDetailInfo,proto3" json:"investment_detail_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetInvestmentDataV2Response) Reset() {
	*x = GetInvestmentDataV2Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentDataV2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentDataV2Response) ProtoMessage() {}

func (x *GetInvestmentDataV2Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentDataV2Response.ProtoReflect.Descriptor instead.
func (*GetInvestmentDataV2Response) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetInvestmentDataV2Response) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetInvestmentDataV2Response) GetInvestmentDetailInfo() map[string]*PreInvestmentDetailsV2 {
	if x != nil {
		return x.InvestmentDetailInfo
	}
	return nil
}

type InitiateDocumentExtractionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// currently only pan card is supported
	DocumentType typesv2.DocumentProofType `protobuf:"varint,2,opt,name=document_type,json=documentType,proto3,enum=api.typesv2.DocumentProofType" json:"document_type,omitempty"`
}

func (x *InitiateDocumentExtractionRequest) Reset() {
	*x = InitiateDocumentExtractionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateDocumentExtractionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateDocumentExtractionRequest) ProtoMessage() {}

func (x *InitiateDocumentExtractionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateDocumentExtractionRequest.ProtoReflect.Descriptor instead.
func (*InitiateDocumentExtractionRequest) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{22}
}

func (x *InitiateDocumentExtractionRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *InitiateDocumentExtractionRequest) GetDocumentType() typesv2.DocumentProofType {
	if x != nil {
		return x.DocumentType
	}
	return typesv2.DocumentProofType(0)
}

type InitiateDocumentExtractionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// signed s3 url to download requested document,
	// if document already exists document url will be returned in the response
	DocumentUrl string `protobuf:"bytes,2,opt,name=document_url,json=documentUrl,proto3" json:"document_url,omitempty"`
}

func (x *InitiateDocumentExtractionResponse) Reset() {
	*x = InitiateDocumentExtractionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateDocumentExtractionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateDocumentExtractionResponse) ProtoMessage() {}

func (x *InitiateDocumentExtractionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateDocumentExtractionResponse.ProtoReflect.Descriptor instead.
func (*InitiateDocumentExtractionResponse) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{23}
}

func (x *InitiateDocumentExtractionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *InitiateDocumentExtractionResponse) GetDocumentUrl() string {
	if x != nil {
		return x.DocumentUrl
	}
	return ""
}

type GetDocumentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// currently only pan card is supported
	DocumentType typesv2.DocumentProofType `protobuf:"varint,2,opt,name=document_type,json=documentType,proto3,enum=api.typesv2.DocumentProofType" json:"document_type,omitempty"`
}

func (x *GetDocumentRequest) Reset() {
	*x = GetDocumentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDocumentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDocumentRequest) ProtoMessage() {}

func (x *GetDocumentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDocumentRequest.ProtoReflect.Descriptor instead.
func (*GetDocumentRequest) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetDocumentRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetDocumentRequest) GetDocumentType() typesv2.DocumentProofType {
	if x != nil {
		return x.DocumentType
	}
	return typesv2.DocumentProofType(0)
}

type GetDocumentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// signed s3 url to download requested document
	DocumentUrl string `protobuf:"bytes,2,opt,name=document_url,json=documentUrl,proto3" json:"document_url,omitempty"`
}

func (x *GetDocumentResponse) Reset() {
	*x = GetDocumentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDocumentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDocumentResponse) ProtoMessage() {}

func (x *GetDocumentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDocumentResponse.ProtoReflect.Descriptor instead.
func (*GetDocumentResponse) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetDocumentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDocumentResponse) GetDocumentUrl() string {
	if x != nil {
		return x.DocumentUrl
	}
	return ""
}

type GetOrCreateUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetOrCreateUserRequest) Reset() {
	*x = GetOrCreateUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrCreateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrCreateUserRequest) ProtoMessage() {}

func (x *GetOrCreateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrCreateUserRequest.ProtoReflect.Descriptor instead.
func (*GetOrCreateUserRequest) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetOrCreateUserRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

// GetOrCreateUserResponse returns details required by consumers of the RPC. Details should be added to the response on
// a need basis
type GetOrCreateUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status                          `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	UserDetails *GetOrCreateUserResponse_UserDetails `protobuf:"bytes,2,opt,name=user_details,json=userDetails,proto3" json:"user_details,omitempty"`
}

func (x *GetOrCreateUserResponse) Reset() {
	*x = GetOrCreateUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrCreateUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrCreateUserResponse) ProtoMessage() {}

func (x *GetOrCreateUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrCreateUserResponse.ProtoReflect.Descriptor instead.
func (*GetOrCreateUserResponse) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetOrCreateUserResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOrCreateUserResponse) GetUserDetails() *GetOrCreateUserResponse_UserDetails {
	if x != nil {
		return x.UserDetails
	}
	return nil
}

type GetOrCreateUserResponse_UserDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        *common.Name           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Gender      typesv2.Gender         `protobuf:"varint,3,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	PhoneNumber *common.PhoneNumber    `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Email       string                 `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	Dob         *date.Date             `protobuf:"bytes,6,opt,name=dob,proto3" json:"dob,omitempty"`
	PanDetails  *typesv2.DocumentProof `protobuf:"bytes,7,opt,name=pan_details,json=panDetails,proto3" json:"pan_details,omitempty"`
}

func (x *GetOrCreateUserResponse_UserDetails) Reset() {
	*x = GetOrCreateUserResponse_UserDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wealthonboarding_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrCreateUserResponse_UserDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrCreateUserResponse_UserDetails) ProtoMessage() {}

func (x *GetOrCreateUserResponse_UserDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_wealthonboarding_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrCreateUserResponse_UserDetails.ProtoReflect.Descriptor instead.
func (*GetOrCreateUserResponse_UserDetails) Descriptor() ([]byte, []int) {
	return file_api_wealthonboarding_service_proto_rawDescGZIP(), []int{27, 0}
}

func (x *GetOrCreateUserResponse_UserDetails) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetOrCreateUserResponse_UserDetails) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *GetOrCreateUserResponse_UserDetails) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *GetOrCreateUserResponse_UserDetails) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *GetOrCreateUserResponse_UserDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetOrCreateUserResponse_UserDetails) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *GetOrCreateUserResponse_UserDetails) GetPanDetails() *typesv2.DocumentProof {
	if x != nil {
		return x.PanDetails
	}
	return nil
}

var File_api_wealthonboarding_service_proto protoreflect.FileDescriptor

var file_api_wealthonboarding_service_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f,
	0x73, 0x6c, 0x61, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63,
	0x61, 0x6c, 0x6c, 0x79, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x65, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70,
	0x69, 0x2f, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2f, 0x74, 0x72, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x82, 0x01,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x0f, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x20, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x22, 0xc8, 0x02, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x11,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x0a,
	0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x65, 0x70, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x65, 0x70, 0x12, 0x49, 0x0a, 0x0b, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x0b, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0x92, 0x02,
	0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0b, 0x77,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x0a,
	0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x49, 0x0a, 0x0f, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x77, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x65, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x22, 0xd1, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x09, 0x6e,
	0x65, 0x78, 0x74, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x6e, 0x65, 0x78,
	0x74, 0x53, 0x74, 0x65, 0x70, 0x12, 0x4f, 0x0a, 0x11, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x22, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xd2, 0x0a, 0x0a, 0x1e, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x12, 0x41, 0x0a, 0x0e, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x52, 0x03, 0x70,
	0x61, 0x6e, 0x12, 0x30, 0x0a, 0x03, 0x70, 0x6f, 0x69, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x03, 0x70, 0x6f, 0x69, 0x12, 0x2c, 0x0a, 0x03, 0x70, 0x6f, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x52, 0x03, 0x70,
	0x6f, 0x61, 0x12, 0x37, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x51, 0x0a, 0x12, 0x72,
	0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x72, 0x65, 0x73,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x67,
	0x0a, 0x1a, 0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x6c, 0x79, 0x5f, 0x65, 0x78,
	0x70, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x6c, 0x79, 0x45, 0x78, 0x70, 0x6f,
	0x73, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x02, 0x18, 0x01, 0x52, 0x18, 0x70,
	0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x6c, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0b, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x3c, 0x0a, 0x0b, 0x69, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x5f, 0x73, 0x6c, 0x61, 0x62, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x53, 0x6c, 0x61, 0x62, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x69, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x53, 0x6c, 0x61, 0x62, 0x12, 0x55, 0x0a, 0x16, 0x68, 0x61, 0x73, 0x5f, 0x64, 0x69, 0x67,
	0x69, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65,
	0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x14, 0x68, 0x61, 0x73, 0x44, 0x69, 0x67, 0x69, 0x6c,
	0x6f, 0x63, 0x6b, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x0f,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12,
	0x6b, 0x0a, 0x1b, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x44,
	0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x19, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x66, 0x0a, 0x1f,
	0x73, 0x6b, 0x69, 0x70, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x76,
	0x69, 0x73, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65,
	0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x1c, 0x73, 0x6b, 0x69, 0x70, 0x53, 0x69, 0x67, 0x6e,
	0x69, 0x6e, 0x67, 0x41, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x61, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x75, 0x73, 0x65, 0x72, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x50,
	0x61, 0x6e, 0x12, 0x3f, 0x0a, 0x12, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x62, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x10, 0x75, 0x73, 0x65, 0x72, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64,
	0x44, 0x6f, 0x62, 0x12, 0x2e, 0x0a, 0x13, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x75, 0x73, 0x65, 0x72, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x72, 0x69, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3b,
	0x0a, 0x1a, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x17, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x69,
	0x73, 0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x46, 0x0a, 0x1f, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0xa6, 0x01, 0x0a, 0x28, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x57,
	0x69, 0x74, 0x68, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x44, 0x0a, 0x0a, 0x62, 0x61, 0x73, 0x69, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x09, 0x62, 0x61, 0x73,
	0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x61, 0x6e, 0x5f, 0x69, 0x6d,
	0x67, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52,
	0x0b, 0x70, 0x61, 0x6e, 0x49, 0x6d, 0x67, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x42, 0x0e, 0x0a, 0x0c,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x22, 0xf2, 0x01, 0x0a,
	0x11, 0x42, 0x61, 0x73, 0x69, 0x63, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x49, 0x0a,
	0x0f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6f,
	0x66, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6f, 0x66, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x69, 0x6d, 0x67, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x69, 0x6d, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x50, 0x0a, 0x29, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x46, 0x72, 0x6f, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68,
	0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x3c, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x73, 0x22, 0xcf, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x62, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x11, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x22, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x0f, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x22, 0xa9, 0x02, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x77, 0x0a, 0x13, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x6e, 0x62, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x62, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x11, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x62,
	0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x69, 0x0a, 0x16, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x4f, 0x6e, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x62,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x3e, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x62, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0xaf, 0x01, 0x0a, 0x11, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e,
	0x62, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4f, 0x0a, 0x11, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x22, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x0f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x20, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x65,
	0x70, 0x52, 0x0e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x65,
	0x70, 0x22, 0x37, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x73, 0x22, 0xad, 0x02, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x7b, 0x0a,
	0x16, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e,
	0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x6e, 0x0a, 0x19, 0x49, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3b, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x65, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8f, 0x01, 0x0a, 0x27, 0x47,
	0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x49, 0x0a, 0x0f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x77, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x22, 0xc8, 0x01, 0x0a,
	0x28, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x72,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x77,
	0x0a, 0x1f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x72, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x73, 0x68, 0x6f, 0x6f,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x1d, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x73, 0x68, 0x6f, 0x6f, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x57, 0x0a, 0x1d, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x44, 0x69, 0x67, 0x69, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x44, 0x6f, 0x63,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65,
	0x22, 0x45, 0x0a, 0x1e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x69, 0x67, 0x69,
	0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x44, 0x6f, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x39, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x49, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x56, 0x32, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x73, 0x22, 0xb4, 0x02, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x7d, 0x0a, 0x16, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x56, 0x32, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x14, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x71, 0x0a, 0x19, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x56, 0x32, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x83, 0x01, 0x0a, 0x21, 0x49, 0x6e,
	0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0d, 0x64, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x99, 0x01, 0x0a, 0x22, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x22, 0x2b,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x19, 0x0a, 0x15, 0x44, 0x4f, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x44,
	0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x45, 0x44, 0x10, 0x65, 0x22, 0x74, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0d,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x87, 0x02, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72,
	0x6c, 0x22, 0xa7, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x65, 0x12, 0x2a,
	0x0a, 0x22, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x50, 0x52, 0x45,
	0x53, 0x45, 0x4e, 0x54, 0x10, 0x66, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x2e, 0x0a, 0x26, 0x44, 0x4f,
	0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x45, 0x58, 0x54, 0x52, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x67, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x4f,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x45, 0x44, 0x10, 0x68, 0x22, 0x33, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x22, 0xcf, 0x03, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x58, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b,
	0x75, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0xb4, 0x02, 0x0a, 0x0b,
	0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x06, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x3b, 0x0a, 0x0b, 0x70, 0x61, 0x6e, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x52, 0x0a, 0x70, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x32, 0xdc, 0x0c, 0x0a, 0x10, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x74, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c,
	0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x77,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x65, 0x70, 0x12, 0x30, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x80, 0x01,
	0x0a, 0x17, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x6f,
	0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x30, 0x2e, 0x77, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x77, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0xa0, 0x01, 0x0a, 0x21, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x46, 0x72, 0x6f, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68,
	0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x3a, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x57, 0x69, 0x74, 0x68, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x57, 0x69, 0x74,
	0x68, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x28, 0x01, 0x12, 0x7b, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f, 0x2e,
	0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e,
	0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30,
	0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f,
	0x6e, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x66, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x62, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x28, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x62,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e,
	0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x2e,
	0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x77, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x73,
	0x68, 0x6f, 0x6f, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x2e, 0x77, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x73, 0x68,
	0x6f, 0x6f, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x7b, 0x0a, 0x16, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x69,
	0x67, 0x69, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x2f, 0x2e, 0x77,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x69, 0x67, 0x69, 0x6c, 0x6f, 0x63, 0x6b,
	0x65, 0x72, 0x44, 0x6f, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e,
	0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x69, 0x67, 0x69, 0x6c, 0x6f, 0x63,
	0x6b, 0x65, 0x72, 0x44, 0x6f, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x72, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x56, 0x32, 0x12, 0x2c, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x56, 0x32, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x1a, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x33, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69,
	0x61, 0x74, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a,
	0x0b, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x2e, 0x77,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x25, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x28, 0x2e, 0x77,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_wealthonboarding_service_proto_rawDescOnce sync.Once
	file_api_wealthonboarding_service_proto_rawDescData = file_api_wealthonboarding_service_proto_rawDesc
)

func file_api_wealthonboarding_service_proto_rawDescGZIP() []byte {
	file_api_wealthonboarding_service_proto_rawDescOnce.Do(func() {
		file_api_wealthonboarding_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_wealthonboarding_service_proto_rawDescData)
	})
	return file_api_wealthonboarding_service_proto_rawDescData
}

var file_api_wealthonboarding_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_wealthonboarding_service_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_api_wealthonboarding_service_proto_goTypes = []interface{}{
	(InitiateDocumentExtractionResponse_Status)(0),    // 0: wealthonboarding.InitiateDocumentExtractionResponse.Status
	(GetDocumentResponse_Status)(0),                   // 1: wealthonboarding.GetDocumentResponse.Status
	(*GetOnboardingStatusRequest)(nil),                // 2: wealthonboarding.GetOnboardingStatusRequest
	(*GetOnboardingStatusResponse)(nil),               // 3: wealthonboarding.GetOnboardingStatusResponse
	(*GetNextOnboardingStatusRequest)(nil),            // 4: wealthonboarding.GetNextOnboardingStatusRequest
	(*GetNextOnboardingStatusResponse)(nil),           // 5: wealthonboarding.GetNextOnboardingStatusResponse
	(*CollectDataFromCustomerRequest)(nil),            // 6: wealthonboarding.CollectDataFromCustomerRequest
	(*CollectDataFromCustomerResponse)(nil),           // 7: wealthonboarding.CollectDataFromCustomerResponse
	(*CollectDataFromCustomerWithStreamRequest)(nil),  // 8: wealthonboarding.CollectDataFromCustomerWithStreamRequest
	(*BasicCustomerInfo)(nil),                         // 9: wealthonboarding.BasicCustomerInfo
	(*CollectDataFromCustomerWithStreamResponse)(nil), // 10: wealthonboarding.CollectDataFromCustomerWithStreamResponse
	(*GetInvestmentOnbStatusRequest)(nil),             // 11: wealthonboarding.GetInvestmentOnbStatusRequest
	(*UpdateOnbStatusRequest)(nil),                    // 12: wealthonboarding.UpdateOnbStatusRequest
	(*GetInvestmentOnbStatusResponse)(nil),            // 13: wealthonboarding.GetInvestmentOnbStatusResponse
	(*UpdateOnbStatusResponse)(nil),                   // 14: wealthonboarding.UpdateOnbStatusResponse
	(*InvestmentOnbData)(nil),                         // 15: wealthonboarding.InvestmentOnbData
	(*GetInvestmentDataRequest)(nil),                  // 16: wealthonboarding.GetInvestmentDataRequest
	(*GetInvestmentDataResponse)(nil),                 // 17: wealthonboarding.GetInvestmentDataResponse
	(*GetOnboardingTroubleshootDetailsRequest)(nil),   // 18: wealthonboarding.GetOnboardingTroubleshootDetailsRequest
	(*GetOnboardingTroubleshootDetailsResponse)(nil),  // 19: wealthonboarding.GetOnboardingTroubleshootDetailsResponse
	(*DownloadDigilockerDocsRequest)(nil),             // 20: wealthonboarding.DownloadDigilockerDocsRequest
	(*DownloadDigilockerDocsResponse)(nil),            // 21: wealthonboarding.DownloadDigilockerDocsResponse
	(*GetInvestmentDataV2Request)(nil),                // 22: wealthonboarding.GetInvestmentDataV2Request
	(*GetInvestmentDataV2Response)(nil),               // 23: wealthonboarding.GetInvestmentDataV2Response
	(*InitiateDocumentExtractionRequest)(nil),         // 24: wealthonboarding.InitiateDocumentExtractionRequest
	(*InitiateDocumentExtractionResponse)(nil),        // 25: wealthonboarding.InitiateDocumentExtractionResponse
	(*GetDocumentRequest)(nil),                        // 26: wealthonboarding.GetDocumentRequest
	(*GetDocumentResponse)(nil),                       // 27: wealthonboarding.GetDocumentResponse
	(*GetOrCreateUserRequest)(nil),                    // 28: wealthonboarding.GetOrCreateUserRequest
	(*GetOrCreateUserResponse)(nil),                   // 29: wealthonboarding.GetOrCreateUserResponse
	nil,                                               // 30: wealthonboarding.GetInvestmentOnbStatusResponse.InvestmentOnbInfoEntry
	nil,                                               // 31: wealthonboarding.GetInvestmentDataResponse.InvestmentDetailInfoEntry
	nil,                                               // 32: wealthonboarding.GetInvestmentDataV2Response.InvestmentDetailInfoEntry
	(*GetOrCreateUserResponse_UserDetails)(nil),       // 33: wealthonboarding.GetOrCreateUserResponse.UserDetails
	(OnboardingType)(0),                               // 34: wealthonboarding.OnboardingType
	(*rpc.Status)(nil),                                // 35: rpc.Status
	(OnboardingStatus)(0),                             // 36: wealthonboarding.OnboardingStatus
	(OnboardingStep)(0),                               // 37: wealthonboarding.OnboardingStep
	(OnboardingEligibility)(0),                        // 38: wealthonboarding.OnboardingEligibility
	(WealthFlow)(0),                                   // 39: wealthonboarding.WealthFlow
	(OnboardingStepEntrypoint)(0),                     // 40: wealthonboarding.OnboardingStepEntrypoint
	(*deeplink.Deeplink)(nil),                         // 41: frontend.deeplink.Deeplink
	(typesv2.Gender)(0),                               // 42: api.typesv2.Gender
	(typesv2.MaritalStatus)(0),                        // 43: api.typesv2.MaritalStatus
	(*typesv2.DocumentProof)(nil),                     // 44: api.typesv2.DocumentProof
	(*common.Image)(nil),                              // 45: api.typesv2.common.Image
	(typesv2.ResidentialStatus)(0),                    // 46: api.typesv2.ResidentialStatus
	(typesv2.PoliticallyExposedStatus)(0),             // 47: api.typesv2.PoliticallyExposedStatus
	(typesv2.Nationality)(0),                          // 48: api.typesv2.Nationality
	(typesv2.IncomeSlab)(0),                           // 49: api.typesv2.IncomeSlab
	(common.BooleanEnum)(0),                           // 50: api.typesv2.common.BooleanEnum
	(*NomineeDeclarationDetails)(nil),                 // 51: wealthonboarding.NomineeDeclarationDetails
	(*date.Date)(nil),                                 // 52: google.type.Date
	(typesv2.DocumentProofType)(0),                    // 53: api.typesv2.DocumentProofType
	(common.ImageType)(0),                             // 54: api.typesv2.common.ImageType
	(*OnboardingTroubleshootDetails)(nil),             // 55: wealthonboarding.OnboardingTroubleshootDetails
	(*PreInvestmentDetail)(nil),                       // 56: wealthonboarding.PreInvestmentDetail
	(*PreInvestmentDetailsV2)(nil),                    // 57: wealthonboarding.PreInvestmentDetailsV2
	(*common.Name)(nil),                               // 58: api.typesv2.common.Name
	(*common.PhoneNumber)(nil),                        // 59: api.typesv2.common.PhoneNumber
}
var file_api_wealthonboarding_service_proto_depIdxs = []int32{
	34, // 0: wealthonboarding.GetOnboardingStatusRequest.onboarding_type:type_name -> wealthonboarding.OnboardingType
	35, // 1: wealthonboarding.GetOnboardingStatusResponse.status:type_name -> rpc.Status
	36, // 2: wealthonboarding.GetOnboardingStatusResponse.onboarding_status:type_name -> wealthonboarding.OnboardingStatus
	37, // 3: wealthonboarding.GetOnboardingStatusResponse.current_step:type_name -> wealthonboarding.OnboardingStep
	38, // 4: wealthonboarding.GetOnboardingStatusResponse.eligibility:type_name -> wealthonboarding.OnboardingEligibility
	39, // 5: wealthonboarding.GetNextOnboardingStatusRequest.wealth_flow:type_name -> wealthonboarding.WealthFlow
	34, // 6: wealthonboarding.GetNextOnboardingStatusRequest.onboarding_type:type_name -> wealthonboarding.OnboardingType
	40, // 7: wealthonboarding.GetNextOnboardingStatusRequest.entry_point:type_name -> wealthonboarding.OnboardingStepEntrypoint
	35, // 8: wealthonboarding.GetNextOnboardingStatusResponse.status:type_name -> rpc.Status
	41, // 9: wealthonboarding.GetNextOnboardingStatusResponse.next_step:type_name -> frontend.deeplink.Deeplink
	36, // 10: wealthonboarding.GetNextOnboardingStatusResponse.onboarding_status:type_name -> wealthonboarding.OnboardingStatus
	42, // 11: wealthonboarding.CollectDataFromCustomerRequest.gender:type_name -> api.typesv2.Gender
	43, // 12: wealthonboarding.CollectDataFromCustomerRequest.marital_status:type_name -> api.typesv2.MaritalStatus
	44, // 13: wealthonboarding.CollectDataFromCustomerRequest.pan:type_name -> api.typesv2.DocumentProof
	44, // 14: wealthonboarding.CollectDataFromCustomerRequest.poi:type_name -> api.typesv2.DocumentProof
	44, // 15: wealthonboarding.CollectDataFromCustomerRequest.poa:type_name -> api.typesv2.DocumentProof
	45, // 16: wealthonboarding.CollectDataFromCustomerRequest.signature:type_name -> api.typesv2.common.Image
	46, // 17: wealthonboarding.CollectDataFromCustomerRequest.residential_status:type_name -> api.typesv2.ResidentialStatus
	47, // 18: wealthonboarding.CollectDataFromCustomerRequest.politically_exposed_status:type_name -> api.typesv2.PoliticallyExposedStatus
	48, // 19: wealthonboarding.CollectDataFromCustomerRequest.nationality:type_name -> api.typesv2.Nationality
	49, // 20: wealthonboarding.CollectDataFromCustomerRequest.income_slab:type_name -> api.typesv2.IncomeSlab
	50, // 21: wealthonboarding.CollectDataFromCustomerRequest.has_digilocker_account:type_name -> api.typesv2.common.BooleanEnum
	34, // 22: wealthonboarding.CollectDataFromCustomerRequest.onboarding_type:type_name -> wealthonboarding.OnboardingType
	51, // 23: wealthonboarding.CollectDataFromCustomerRequest.nominee_declaration_details:type_name -> wealthonboarding.NomineeDeclarationDetails
	50, // 24: wealthonboarding.CollectDataFromCustomerRequest.skip_signing_advisory_agreement:type_name -> api.typesv2.common.BooleanEnum
	52, // 25: wealthonboarding.CollectDataFromCustomerRequest.user_submitted_dob:type_name -> google.type.Date
	35, // 26: wealthonboarding.CollectDataFromCustomerResponse.status:type_name -> rpc.Status
	9,  // 27: wealthonboarding.CollectDataFromCustomerWithStreamRequest.basic_info:type_name -> wealthonboarding.BasicCustomerInfo
	34, // 28: wealthonboarding.BasicCustomerInfo.onboarding_type:type_name -> wealthonboarding.OnboardingType
	53, // 29: wealthonboarding.BasicCustomerInfo.proof_type:type_name -> api.typesv2.DocumentProofType
	54, // 30: wealthonboarding.BasicCustomerInfo.img_type:type_name -> api.typesv2.common.ImageType
	35, // 31: wealthonboarding.CollectDataFromCustomerWithStreamResponse.status:type_name -> rpc.Status
	36, // 32: wealthonboarding.UpdateOnbStatusRequest.onboarding_status:type_name -> wealthonboarding.OnboardingStatus
	34, // 33: wealthonboarding.UpdateOnbStatusRequest.onboarding_type:type_name -> wealthonboarding.OnboardingType
	35, // 34: wealthonboarding.GetInvestmentOnbStatusResponse.status:type_name -> rpc.Status
	30, // 35: wealthonboarding.GetInvestmentOnbStatusResponse.investment_onb_info:type_name -> wealthonboarding.GetInvestmentOnbStatusResponse.InvestmentOnbInfoEntry
	35, // 36: wealthonboarding.UpdateOnbStatusResponse.status:type_name -> rpc.Status
	36, // 37: wealthonboarding.InvestmentOnbData.onboarding_status:type_name -> wealthonboarding.OnboardingStatus
	37, // 38: wealthonboarding.InvestmentOnbData.onboarding_step:type_name -> wealthonboarding.OnboardingStep
	35, // 39: wealthonboarding.GetInvestmentDataResponse.status:type_name -> rpc.Status
	31, // 40: wealthonboarding.GetInvestmentDataResponse.investment_detail_info:type_name -> wealthonboarding.GetInvestmentDataResponse.InvestmentDetailInfoEntry
	34, // 41: wealthonboarding.GetOnboardingTroubleshootDetailsRequest.onboarding_type:type_name -> wealthonboarding.OnboardingType
	35, // 42: wealthonboarding.GetOnboardingTroubleshootDetailsResponse.status:type_name -> rpc.Status
	55, // 43: wealthonboarding.GetOnboardingTroubleshootDetailsResponse.onboarding_troubleshoot_details:type_name -> wealthonboarding.OnboardingTroubleshootDetails
	35, // 44: wealthonboarding.DownloadDigilockerDocsResponse.status:type_name -> rpc.Status
	35, // 45: wealthonboarding.GetInvestmentDataV2Response.status:type_name -> rpc.Status
	32, // 46: wealthonboarding.GetInvestmentDataV2Response.investment_detail_info:type_name -> wealthonboarding.GetInvestmentDataV2Response.InvestmentDetailInfoEntry
	53, // 47: wealthonboarding.InitiateDocumentExtractionRequest.document_type:type_name -> api.typesv2.DocumentProofType
	35, // 48: wealthonboarding.InitiateDocumentExtractionResponse.status:type_name -> rpc.Status
	53, // 49: wealthonboarding.GetDocumentRequest.document_type:type_name -> api.typesv2.DocumentProofType
	35, // 50: wealthonboarding.GetDocumentResponse.status:type_name -> rpc.Status
	35, // 51: wealthonboarding.GetOrCreateUserResponse.status:type_name -> rpc.Status
	33, // 52: wealthonboarding.GetOrCreateUserResponse.user_details:type_name -> wealthonboarding.GetOrCreateUserResponse.UserDetails
	15, // 53: wealthonboarding.GetInvestmentOnbStatusResponse.InvestmentOnbInfoEntry.value:type_name -> wealthonboarding.InvestmentOnbData
	56, // 54: wealthonboarding.GetInvestmentDataResponse.InvestmentDetailInfoEntry.value:type_name -> wealthonboarding.PreInvestmentDetail
	57, // 55: wealthonboarding.GetInvestmentDataV2Response.InvestmentDetailInfoEntry.value:type_name -> wealthonboarding.PreInvestmentDetailsV2
	58, // 56: wealthonboarding.GetOrCreateUserResponse.UserDetails.name:type_name -> api.typesv2.common.Name
	42, // 57: wealthonboarding.GetOrCreateUserResponse.UserDetails.gender:type_name -> api.typesv2.Gender
	59, // 58: wealthonboarding.GetOrCreateUserResponse.UserDetails.phone_number:type_name -> api.typesv2.common.PhoneNumber
	52, // 59: wealthonboarding.GetOrCreateUserResponse.UserDetails.dob:type_name -> google.type.Date
	44, // 60: wealthonboarding.GetOrCreateUserResponse.UserDetails.pan_details:type_name -> api.typesv2.DocumentProof
	2,  // 61: wealthonboarding.WealthOnboarding.GetOnboardingStatus:input_type -> wealthonboarding.GetOnboardingStatusRequest
	4,  // 62: wealthonboarding.WealthOnboarding.GetNextOnboardingStep:input_type -> wealthonboarding.GetNextOnboardingStatusRequest
	6,  // 63: wealthonboarding.WealthOnboarding.CollectDataFromCustomer:input_type -> wealthonboarding.CollectDataFromCustomerRequest
	8,  // 64: wealthonboarding.WealthOnboarding.CollectDataFromCustomerWithStream:input_type -> wealthonboarding.CollectDataFromCustomerWithStreamRequest
	11, // 65: wealthonboarding.WealthOnboarding.GetInvestmentOnbStatus:input_type -> wealthonboarding.GetInvestmentOnbStatusRequest
	12, // 66: wealthonboarding.WealthOnboarding.UpdateOnbStatus:input_type -> wealthonboarding.UpdateOnbStatusRequest
	16, // 67: wealthonboarding.WealthOnboarding.GetInvestmentData:input_type -> wealthonboarding.GetInvestmentDataRequest
	18, // 68: wealthonboarding.WealthOnboarding.GetOnboardingTroubleshootDetails:input_type -> wealthonboarding.GetOnboardingTroubleshootDetailsRequest
	20, // 69: wealthonboarding.WealthOnboarding.DownloadDigilockerDocs:input_type -> wealthonboarding.DownloadDigilockerDocsRequest
	22, // 70: wealthonboarding.WealthOnboarding.GetInvestmentDataV2:input_type -> wealthonboarding.GetInvestmentDataV2Request
	24, // 71: wealthonboarding.WealthOnboarding.InitiateDocumentExtraction:input_type -> wealthonboarding.InitiateDocumentExtractionRequest
	26, // 72: wealthonboarding.WealthOnboarding.GetDocument:input_type -> wealthonboarding.GetDocumentRequest
	28, // 73: wealthonboarding.WealthOnboarding.GetOrCreateUser:input_type -> wealthonboarding.GetOrCreateUserRequest
	3,  // 74: wealthonboarding.WealthOnboarding.GetOnboardingStatus:output_type -> wealthonboarding.GetOnboardingStatusResponse
	5,  // 75: wealthonboarding.WealthOnboarding.GetNextOnboardingStep:output_type -> wealthonboarding.GetNextOnboardingStatusResponse
	7,  // 76: wealthonboarding.WealthOnboarding.CollectDataFromCustomer:output_type -> wealthonboarding.CollectDataFromCustomerResponse
	10, // 77: wealthonboarding.WealthOnboarding.CollectDataFromCustomerWithStream:output_type -> wealthonboarding.CollectDataFromCustomerWithStreamResponse
	13, // 78: wealthonboarding.WealthOnboarding.GetInvestmentOnbStatus:output_type -> wealthonboarding.GetInvestmentOnbStatusResponse
	14, // 79: wealthonboarding.WealthOnboarding.UpdateOnbStatus:output_type -> wealthonboarding.UpdateOnbStatusResponse
	17, // 80: wealthonboarding.WealthOnboarding.GetInvestmentData:output_type -> wealthonboarding.GetInvestmentDataResponse
	19, // 81: wealthonboarding.WealthOnboarding.GetOnboardingTroubleshootDetails:output_type -> wealthonboarding.GetOnboardingTroubleshootDetailsResponse
	21, // 82: wealthonboarding.WealthOnboarding.DownloadDigilockerDocs:output_type -> wealthonboarding.DownloadDigilockerDocsResponse
	23, // 83: wealthonboarding.WealthOnboarding.GetInvestmentDataV2:output_type -> wealthonboarding.GetInvestmentDataV2Response
	25, // 84: wealthonboarding.WealthOnboarding.InitiateDocumentExtraction:output_type -> wealthonboarding.InitiateDocumentExtractionResponse
	27, // 85: wealthonboarding.WealthOnboarding.GetDocument:output_type -> wealthonboarding.GetDocumentResponse
	29, // 86: wealthonboarding.WealthOnboarding.GetOrCreateUser:output_type -> wealthonboarding.GetOrCreateUserResponse
	74, // [74:87] is the sub-list for method output_type
	61, // [61:74] is the sub-list for method input_type
	61, // [61:61] is the sub-list for extension type_name
	61, // [61:61] is the sub-list for extension extendee
	0,  // [0:61] is the sub-list for field type_name
}

func init() { file_api_wealthonboarding_service_proto_init() }
func file_api_wealthonboarding_service_proto_init() {
	if File_api_wealthonboarding_service_proto != nil {
		return
	}
	file_api_wealthonboarding_data_proto_init()
	file_api_wealthonboarding_enums_proto_init()
	file_api_wealthonboarding_preinvestment_proto_init()
	file_api_wealthonboarding_troubleshoot_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_wealthonboarding_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnboardingStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnboardingStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextOnboardingStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextOnboardingStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectDataFromCustomerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectDataFromCustomerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectDataFromCustomerWithStreamRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BasicCustomerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectDataFromCustomerWithStreamResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentOnbStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOnbStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentOnbStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOnbStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvestmentOnbData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnboardingTroubleshootDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnboardingTroubleshootDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadDigilockerDocsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadDigilockerDocsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentDataV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentDataV2Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateDocumentExtractionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateDocumentExtractionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDocumentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDocumentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrCreateUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrCreateUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wealthonboarding_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrCreateUserResponse_UserDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_wealthonboarding_service_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*CollectDataFromCustomerWithStreamRequest_BasicInfo)(nil),
		(*CollectDataFromCustomerWithStreamRequest_PanImgChunk)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wealthonboarding_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wealthonboarding_service_proto_goTypes,
		DependencyIndexes: file_api_wealthonboarding_service_proto_depIdxs,
		EnumInfos:         file_api_wealthonboarding_service_proto_enumTypes,
		MessageInfos:      file_api_wealthonboarding_service_proto_msgTypes,
	}.Build()
	File_api_wealthonboarding_service_proto = out.File
	file_api_wealthonboarding_service_proto_rawDesc = nil
	file_api_wealthonboarding_service_proto_goTypes = nil
	file_api_wealthonboarding_service_proto_depIdxs = nil
}
