package ckyc

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

func (o *CkycDataType) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := CkycDataType_value[val]
	if !ok {
		return fmt.Errorf("unexpected value: %s", val)
	}

	*o = CkycDataType(valInt)
	return nil
}

func (o CkycDataType) Value() (driver.Value, error) {
	return o.String(), nil
}

func (o *CkycPayload) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, o)
}

func (o *CkycPayload) Value() (driver.Value, error) {
	return protojson.Marshal(o)
}
