syntax = "proto3";

package stockguardian.sgauth;

import "api/rpc/status.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgauth";

service Auth {
  // CreateAuthToken generates an authentication token using client credentials
  rpc CreateAuthToken (CreateAuthTokenRequest) returns (CreateAuthTokenResponse);
}

message CreateAuthTokenRequest {
  // Client ID provided by StockGuardian to the partner
  string client_id = 1;
  // Client secret provided by StockGuardian to the partner
  string client_secret = 2;
}

message CreateAuthTokenResponse {
  // RPC status
  rpc.Status status = 1;
  // Auth token
  string auth_token = 2;
  // Expiry time of the auth token in seconds
  int64 expiry_in_seconds = 3;
}
