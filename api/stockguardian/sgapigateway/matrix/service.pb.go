// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/stockguardian/sgapigateway/matrix/service.proto

package matrix

import (
	rpc "github.com/epifi/be-common/api/rpc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateCustomerApplicationDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId string `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	// Types that are assignable to ApplicationData:
	//
	//	*UpdateCustomerApplicationDetailsRequest_UserCurrentPincode
	ApplicationData isUpdateCustomerApplicationDetailsRequest_ApplicationData `protobuf_oneof:"applicationData"`
}

func (x *UpdateCustomerApplicationDetailsRequest) Reset() {
	*x = UpdateCustomerApplicationDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCustomerApplicationDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerApplicationDetailsRequest) ProtoMessage() {}

func (x *UpdateCustomerApplicationDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerApplicationDetailsRequest.ProtoReflect.Descriptor instead.
func (*UpdateCustomerApplicationDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateCustomerApplicationDetailsRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (m *UpdateCustomerApplicationDetailsRequest) GetApplicationData() isUpdateCustomerApplicationDetailsRequest_ApplicationData {
	if m != nil {
		return m.ApplicationData
	}
	return nil
}

func (x *UpdateCustomerApplicationDetailsRequest) GetUserCurrentPincode() string {
	if x, ok := x.GetApplicationData().(*UpdateCustomerApplicationDetailsRequest_UserCurrentPincode); ok {
		return x.UserCurrentPincode
	}
	return ""
}

type isUpdateCustomerApplicationDetailsRequest_ApplicationData interface {
	isUpdateCustomerApplicationDetailsRequest_ApplicationData()
}

type UpdateCustomerApplicationDetailsRequest_UserCurrentPincode struct {
	UserCurrentPincode string `protobuf:"bytes,2,opt,name=user_current_pincode,json=userCurrentPincode,proto3,oneof"`
}

func (*UpdateCustomerApplicationDetailsRequest_UserCurrentPincode) isUpdateCustomerApplicationDetailsRequest_ApplicationData() {
}

type UpdateCustomerApplicationDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateCustomerApplicationDetailsResponse) Reset() {
	*x = UpdateCustomerApplicationDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCustomerApplicationDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerApplicationDetailsResponse) ProtoMessage() {}

func (x *UpdateCustomerApplicationDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerApplicationDetailsResponse.ProtoReflect.Descriptor instead.
func (*UpdateCustomerApplicationDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateCustomerApplicationDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type StartCustomerApplicationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantId string `protobuf:"bytes,1,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// maps to matrix.OrchestrationFlow
	OrchFlow        string           `protobuf:"bytes,3,opt,name=orch_flow,json=orchFlow,proto3" json:"orch_flow,omitempty"`
	ApplicationData *ApplicationData `protobuf:"bytes,5,opt,name=application_data,json=applicationData,proto3" json:"application_data,omitempty"`
}

func (x *StartCustomerApplicationRequest) Reset() {
	*x = StartCustomerApplicationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartCustomerApplicationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartCustomerApplicationRequest) ProtoMessage() {}

func (x *StartCustomerApplicationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartCustomerApplicationRequest.ProtoReflect.Descriptor instead.
func (*StartCustomerApplicationRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{2}
}

func (x *StartCustomerApplicationRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *StartCustomerApplicationRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *StartCustomerApplicationRequest) GetOrchFlow() string {
	if x != nil {
		return x.OrchFlow
	}
	return ""
}

func (x *StartCustomerApplicationRequest) GetApplicationData() *ApplicationData {
	if x != nil {
		return x.ApplicationData
	}
	return nil
}

type StartCustomerApplicationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CurrentStage   Stage           `protobuf:"varint,2,opt,name=current_stage,json=currentStage,proto3,enum=stockguardian.sgapigateway.matrix.Stage" json:"current_stage,omitempty"`
	StageStatus    StageStatus     `protobuf:"varint,3,opt,name=stage_status,json=stageStatus,proto3,enum=stockguardian.sgapigateway.matrix.StageStatus" json:"stage_status,omitempty"`
	NextAction     Action          `protobuf:"varint,4,opt,name=next_action,json=nextAction,proto3,enum=stockguardian.sgapigateway.matrix.Action" json:"next_action,omitempty"`
	ActionMetadata *ActionMetadata `protobuf:"bytes,5,opt,name=action_metadata,json=actionMetadata,proto3" json:"action_metadata,omitempty"`
	ApplicationId  string          `protobuf:"bytes,6,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *StartCustomerApplicationResponse) Reset() {
	*x = StartCustomerApplicationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartCustomerApplicationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartCustomerApplicationResponse) ProtoMessage() {}

func (x *StartCustomerApplicationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartCustomerApplicationResponse.ProtoReflect.Descriptor instead.
func (*StartCustomerApplicationResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{3}
}

func (x *StartCustomerApplicationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *StartCustomerApplicationResponse) GetCurrentStage() Stage {
	if x != nil {
		return x.CurrentStage
	}
	return Stage_STAGE_UNSPECIFIED
}

func (x *StartCustomerApplicationResponse) GetStageStatus() StageStatus {
	if x != nil {
		return x.StageStatus
	}
	return StageStatus_STAGE_STATUS_UNSPECIFIED
}

func (x *StartCustomerApplicationResponse) GetNextAction() Action {
	if x != nil {
		return x.NextAction
	}
	return Action_ACTION_UNSPECIFIED
}

func (x *StartCustomerApplicationResponse) GetActionMetadata() *ActionMetadata {
	if x != nil {
		return x.ActionMetadata
	}
	return nil
}

func (x *StartCustomerApplicationResponse) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type GetCustomerApplicationStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to ApplicationIdentifier:
	//
	//	*GetCustomerApplicationStatusRequest_ApplicationId
	//	*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId_
	ApplicationIdentifier isGetCustomerApplicationStatusRequest_ApplicationIdentifier `protobuf_oneof:"ApplicationIdentifier"`
}

func (x *GetCustomerApplicationStatusRequest) Reset() {
	*x = GetCustomerApplicationStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerApplicationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerApplicationStatusRequest) ProtoMessage() {}

func (x *GetCustomerApplicationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerApplicationStatusRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerApplicationStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{4}
}

func (m *GetCustomerApplicationStatusRequest) GetApplicationIdentifier() isGetCustomerApplicationStatusRequest_ApplicationIdentifier {
	if m != nil {
		return m.ApplicationIdentifier
	}
	return nil
}

func (x *GetCustomerApplicationStatusRequest) GetApplicationId() string {
	if x, ok := x.GetApplicationIdentifier().(*GetCustomerApplicationStatusRequest_ApplicationId); ok {
		return x.ApplicationId
	}
	return ""
}

func (x *GetCustomerApplicationStatusRequest) GetApplicantIdAndClientReqId() *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId {
	if x, ok := x.GetApplicationIdentifier().(*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId_); ok {
		return x.ApplicantIdAndClientReqId
	}
	return nil
}

type isGetCustomerApplicationStatusRequest_ApplicationIdentifier interface {
	isGetCustomerApplicationStatusRequest_ApplicationIdentifier()
}

type GetCustomerApplicationStatusRequest_ApplicationId struct {
	ApplicationId string `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3,oneof"`
}

type GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId_ struct {
	ApplicantIdAndClientReqId *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId `protobuf:"bytes,2,opt,name=applicant_id_and_client_req_id,json=applicantIdAndClientReqId,proto3,oneof"`
}

func (*GetCustomerApplicationStatusRequest_ApplicationId) isGetCustomerApplicationStatusRequest_ApplicationIdentifier() {
}

func (*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId_) isGetCustomerApplicationStatusRequest_ApplicationIdentifier() {
}

type GetCustomerApplicationStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status            *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ApplicationStatus ApplicationStatus `protobuf:"varint,2,opt,name=application_status,json=applicationStatus,proto3,enum=stockguardian.sgapigateway.matrix.ApplicationStatus" json:"application_status,omitempty"`
	CurrentStage      Stage             `protobuf:"varint,3,opt,name=current_stage,json=currentStage,proto3,enum=stockguardian.sgapigateway.matrix.Stage" json:"current_stage,omitempty"`
	StageStatus       StageStatus       `protobuf:"varint,4,opt,name=stage_status,json=stageStatus,proto3,enum=stockguardian.sgapigateway.matrix.StageStatus" json:"stage_status,omitempty"`
	NextAction        Action            `protobuf:"varint,5,opt,name=next_action,json=nextAction,proto3,enum=stockguardian.sgapigateway.matrix.Action" json:"next_action,omitempty"`
	ActionMetadata    *ActionMetadata   `protobuf:"bytes,6,opt,name=action_metadata,json=actionMetadata,proto3" json:"action_metadata,omitempty"`
	ApplicationId     string            `protobuf:"bytes,7,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *GetCustomerApplicationStatusResponse) Reset() {
	*x = GetCustomerApplicationStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerApplicationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerApplicationStatusResponse) ProtoMessage() {}

func (x *GetCustomerApplicationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerApplicationStatusResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerApplicationStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetCustomerApplicationStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCustomerApplicationStatusResponse) GetApplicationStatus() ApplicationStatus {
	if x != nil {
		return x.ApplicationStatus
	}
	return ApplicationStatus_APPLICATION_STATUS_UNSPECIFIED
}

func (x *GetCustomerApplicationStatusResponse) GetCurrentStage() Stage {
	if x != nil {
		return x.CurrentStage
	}
	return Stage_STAGE_UNSPECIFIED
}

func (x *GetCustomerApplicationStatusResponse) GetStageStatus() StageStatus {
	if x != nil {
		return x.StageStatus
	}
	return StageStatus_STAGE_STATUS_UNSPECIFIED
}

func (x *GetCustomerApplicationStatusResponse) GetNextAction() Action {
	if x != nil {
		return x.NextAction
	}
	return Action_ACTION_UNSPECIFIED
}

func (x *GetCustomerApplicationStatusResponse) GetActionMetadata() *ActionMetadata {
	if x != nil {
		return x.ActionMetadata
	}
	return nil
}

func (x *GetCustomerApplicationStatusResponse) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type ActionMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Metadata:
	//
	//	*ActionMetadata_VkycMetadata
	//	*ActionMetadata_DigilockerKycMetadata
	Metadata isActionMetadata_Metadata `protobuf_oneof:"Metadata"`
}

func (x *ActionMetadata) Reset() {
	*x = ActionMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActionMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionMetadata) ProtoMessage() {}

func (x *ActionMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionMetadata.ProtoReflect.Descriptor instead.
func (*ActionMetadata) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{6}
}

func (m *ActionMetadata) GetMetadata() isActionMetadata_Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

func (x *ActionMetadata) GetVkycMetadata() *VkycMetadata {
	if x, ok := x.GetMetadata().(*ActionMetadata_VkycMetadata); ok {
		return x.VkycMetadata
	}
	return nil
}

func (x *ActionMetadata) GetDigilockerKycMetadata() *DigilockerKycMetadata {
	if x, ok := x.GetMetadata().(*ActionMetadata_DigilockerKycMetadata); ok {
		return x.DigilockerKycMetadata
	}
	return nil
}

type isActionMetadata_Metadata interface {
	isActionMetadata_Metadata()
}

type ActionMetadata_VkycMetadata struct {
	VkycMetadata *VkycMetadata `protobuf:"bytes,1,opt,name=vkyc_metadata,json=vkycMetadata,proto3,oneof"`
}

type ActionMetadata_DigilockerKycMetadata struct {
	DigilockerKycMetadata *DigilockerKycMetadata `protobuf:"bytes,2,opt,name=digilocker_kyc_metadata,json=digilockerKycMetadata,proto3,oneof"`
}

func (*ActionMetadata_VkycMetadata) isActionMetadata_Metadata() {}

func (*ActionMetadata_DigilockerKycMetadata) isActionMetadata_Metadata() {}

type DigilockerKycMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OauthUrl string `protobuf:"bytes,1,opt,name=oauth_url,json=oauthUrl,proto3" json:"oauth_url,omitempty"`
}

func (x *DigilockerKycMetadata) Reset() {
	*x = DigilockerKycMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DigilockerKycMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DigilockerKycMetadata) ProtoMessage() {}

func (x *DigilockerKycMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DigilockerKycMetadata.ProtoReflect.Descriptor instead.
func (*DigilockerKycMetadata) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{7}
}

func (x *DigilockerKycMetadata) GetOauthUrl() string {
	if x != nil {
		return x.OauthUrl
	}
	return ""
}

type VkycMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId string `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	CallId        string `protobuf:"bytes,2,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	// Keeping the status as string, since we just need to pass it to clients
	CallStatus string `protobuf:"bytes,3,opt,name=call_status,json=callStatus,proto3" json:"call_status,omitempty"`
}

func (x *VkycMetadata) Reset() {
	*x = VkycMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VkycMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VkycMetadata) ProtoMessage() {}

func (x *VkycMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VkycMetadata.ProtoReflect.Descriptor instead.
func (*VkycMetadata) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{8}
}

func (x *VkycMetadata) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *VkycMetadata) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *VkycMetadata) GetCallStatus() string {
	if x != nil {
		return x.CallStatus
	}
	return ""
}

type GetCustomerApplicationDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId string `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	Stage         Stage  `protobuf:"varint,2,opt,name=stage,proto3,enum=stockguardian.sgapigateway.matrix.Stage" json:"stage,omitempty"`
}

func (x *GetCustomerApplicationDetailsRequest) Reset() {
	*x = GetCustomerApplicationDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerApplicationDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerApplicationDetailsRequest) ProtoMessage() {}

func (x *GetCustomerApplicationDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerApplicationDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerApplicationDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetCustomerApplicationDetailsRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetCustomerApplicationDetailsRequest) GetStage() Stage {
	if x != nil {
		return x.Stage
	}
	return Stage_STAGE_UNSPECIFIED
}

type GetCustomerApplicationDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status             *rpc.Status                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ApplicationDetails *CustomerApplicationDetails `protobuf:"bytes,2,opt,name=application_details,json=applicationDetails,proto3" json:"application_details,omitempty"`
}

func (x *GetCustomerApplicationDetailsResponse) Reset() {
	*x = GetCustomerApplicationDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerApplicationDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerApplicationDetailsResponse) ProtoMessage() {}

func (x *GetCustomerApplicationDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerApplicationDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerApplicationDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetCustomerApplicationDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCustomerApplicationDetailsResponse) GetApplicationDetails() *CustomerApplicationDetails {
	if x != nil {
		return x.ApplicationDetails
	}
	return nil
}

type CustomerApplicationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID of the customer application the stage is linked to
	CustomerApplicationId string `protobuf:"bytes,1,opt,name=customer_application_id,json=customerApplicationId,proto3" json:"customer_application_id,omitempty"`
	// Request ID sent by client
	ClientRequestId string `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// Stage of the application: EKYC, CKYC, VKYC, CROSS_DATA_VALIDATION
	Stage Stage `protobuf:"varint,3,opt,name=stage,proto3,enum=stockguardian.sgapigateway.matrix.Stage" json:"stage,omitempty"`
	// Stage status - Initiated, InProgress, Completed, Failed, Expired
	Status StageStatus `protobuf:"varint,4,opt,name=status,proto3,enum=stockguardian.sgapigateway.matrix.StageStatus" json:"status,omitempty"`
	// Granular failure status - CKYC_SEARCH_FAILED, EKYC_DATA_VALIDATION_FAILED
	FailureReason FailureReason          `protobuf:"varint,5,opt,name=failure_reason,json=failureReason,proto3,enum=stockguardian.sgapigateway.matrix.FailureReason" json:"failure_reason,omitempty"`
	CompletedAt   *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	DeletedAtUnix int64                  `protobuf:"varint,7,opt,name=deleted_at_unix,json=deletedAtUnix,proto3" json:"deleted_at_unix,omitempty"`
}

func (x *CustomerApplicationDetails) Reset() {
	*x = CustomerApplicationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerApplicationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerApplicationDetails) ProtoMessage() {}

func (x *CustomerApplicationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerApplicationDetails.ProtoReflect.Descriptor instead.
func (*CustomerApplicationDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{11}
}

func (x *CustomerApplicationDetails) GetCustomerApplicationId() string {
	if x != nil {
		return x.CustomerApplicationId
	}
	return ""
}

func (x *CustomerApplicationDetails) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *CustomerApplicationDetails) GetStage() Stage {
	if x != nil {
		return x.Stage
	}
	return Stage_STAGE_UNSPECIFIED
}

func (x *CustomerApplicationDetails) GetStatus() StageStatus {
	if x != nil {
		return x.Status
	}
	return StageStatus_STAGE_STATUS_UNSPECIFIED
}

func (x *CustomerApplicationDetails) GetFailureReason() FailureReason {
	if x != nil {
		return x.FailureReason
	}
	return FailureReason_FAILURE_REASON_UNSPECIFIED
}

func (x *CustomerApplicationDetails) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *CustomerApplicationDetails) GetDeletedAtUnix() int64 {
	if x != nil {
		return x.DeletedAtUnix
	}
	return 0
}

type SkipCustomerApplicationStageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerApplicationId string `protobuf:"bytes,1,opt,name=customer_application_id,json=customerApplicationId,proto3" json:"customer_application_id,omitempty"`
	ClientReqId           string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	Stage                 Stage  `protobuf:"varint,3,opt,name=stage,proto3,enum=stockguardian.sgapigateway.matrix.Stage" json:"stage,omitempty"`
}

func (x *SkipCustomerApplicationStageRequest) Reset() {
	*x = SkipCustomerApplicationStageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SkipCustomerApplicationStageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkipCustomerApplicationStageRequest) ProtoMessage() {}

func (x *SkipCustomerApplicationStageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkipCustomerApplicationStageRequest.ProtoReflect.Descriptor instead.
func (*SkipCustomerApplicationStageRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{12}
}

func (x *SkipCustomerApplicationStageRequest) GetCustomerApplicationId() string {
	if x != nil {
		return x.CustomerApplicationId
	}
	return ""
}

func (x *SkipCustomerApplicationStageRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *SkipCustomerApplicationStageRequest) GetStage() Stage {
	if x != nil {
		return x.Stage
	}
	return Stage_STAGE_UNSPECIFIED
}

type SkipCustomerApplicationStageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the skip operation
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SkipCustomerApplicationStageResponse) Reset() {
	*x = SkipCustomerApplicationStageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SkipCustomerApplicationStageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkipCustomerApplicationStageResponse) ProtoMessage() {}

func (x *SkipCustomerApplicationStageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkipCustomerApplicationStageResponse.ProtoReflect.Descriptor instead.
func (*SkipCustomerApplicationStageResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{13}
}

func (x *SkipCustomerApplicationStageResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ResetCustomerApplicationStageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerApplicationId string `protobuf:"bytes,1,opt,name=customer_application_id,json=customerApplicationId,proto3" json:"customer_application_id,omitempty"`
	ClientReqId           string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	Stage                 Stage  `protobuf:"varint,3,opt,name=stage,proto3,enum=stockguardian.sgapigateway.matrix.Stage" json:"stage,omitempty"`
}

func (x *ResetCustomerApplicationStageRequest) Reset() {
	*x = ResetCustomerApplicationStageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetCustomerApplicationStageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetCustomerApplicationStageRequest) ProtoMessage() {}

func (x *ResetCustomerApplicationStageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetCustomerApplicationStageRequest.ProtoReflect.Descriptor instead.
func (*ResetCustomerApplicationStageRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{14}
}

func (x *ResetCustomerApplicationStageRequest) GetCustomerApplicationId() string {
	if x != nil {
		return x.CustomerApplicationId
	}
	return ""
}

func (x *ResetCustomerApplicationStageRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *ResetCustomerApplicationStageRequest) GetStage() Stage {
	if x != nil {
		return x.Stage
	}
	return Stage_STAGE_UNSPECIFIED
}

type ResetCustomerApplicationStageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the reset operation
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ResetCustomerApplicationStageResponse) Reset() {
	*x = ResetCustomerApplicationStageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetCustomerApplicationStageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetCustomerApplicationStageResponse) ProtoMessage() {}

func (x *ResetCustomerApplicationStageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetCustomerApplicationStageResponse.ProtoReflect.Descriptor instead.
func (*ResetCustomerApplicationStageResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{15}
}

func (x *ResetCustomerApplicationStageResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantId string `protobuf:"bytes,1,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) Reset() {
	*x = GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) ProtoMessage() {}

func (x *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId.ProtoReflect.Descriptor instead.
func (*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

var File_api_stockguardian_sgapigateway_matrix_service_proto protoreflect.FileDescriptor

var file_api_stockguardian_sgapigateway_matrix_service_proto_rawDesc = []byte{
	0x0a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2f, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3c,
	0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61,
	0x6e, 0x2f, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6d,
	0x61, 0x74, 0x72, 0x69, 0x78, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70,
	0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2f,
	0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6d, 0x61, 0x74,
	0x72, 0x69, 0x78, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x97, 0x01, 0x0a, 0x27, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x14, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x12, 0x75, 0x73, 0x65, 0x72, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x50, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x22, 0x4f, 0x0a, 0x28, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xe4, 0x01, 0x0a, 0x1f,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x63, 0x68, 0x5f, 0x66,
	0x6c, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x63, 0x68, 0x46,
	0x6c, 0x6f, 0x77, 0x12, 0x5d, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67,
	0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69,
	0x78, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x22, 0xb8, 0x03, 0x0a, 0x20, 0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4d, 0x0a, 0x0d,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x51, 0x0a, 0x0c, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2e, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61,
	0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d,
	0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a,
	0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a,
	0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x0f, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xf3, 0x02,
	0x0a, 0x23, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0xa5,
	0x01, 0x0a, 0x1e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x5f,
	0x61, 0x6e, 0x64, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x60, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67,
	0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x48, 0x00, 0x52, 0x19, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x1a, 0x62, 0x0a, 0x19, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x6e, 0x74, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x42, 0x17, 0x0a, 0x15, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x22, 0xa1, 0x04, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x63, 0x0a, 0x12, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67,
	0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69,
	0x78, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4d, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67,
	0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69,
	0x78, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x51, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70,
	0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x73, 0x74, 0x61,
	0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67,
	0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69,
	0x78, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67,
	0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69,
	0x78, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xe8, 0x01, 0x0a, 0x0e, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x56, 0x0a, 0x0d, 0x76, 0x6b,
	0x79, 0x63, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61,
	0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d,
	0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x56, 0x6b, 0x79, 0x63, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x52, 0x0c, 0x76, 0x6b, 0x79, 0x63, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x72, 0x0a, 0x17, 0x64, 0x69, 0x67, 0x69, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72,
	0x5f, 0x6b, 0x79, 0x63, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x44, 0x69, 0x67, 0x69, 0x6c, 0x6f, 0x63, 0x6b,
	0x65, 0x72, 0x4b, 0x79, 0x63, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x15, 0x64, 0x69, 0x67, 0x69, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x4b, 0x79, 0x63, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x0a, 0x0a, 0x08, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x34, 0x0a, 0x15, 0x44, 0x69, 0x67, 0x69, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72,
	0x4b, 0x79, 0x63, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x6f,
	0x61, 0x75, 0x74, 0x68, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6f, 0x61, 0x75, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x22, 0x6f, 0x0a, 0x0c, 0x56, 0x6b, 0x79, 0x63,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x6c,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x8d, 0x01, 0x0a, 0x24, 0x47, 0x65,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x22, 0xbc, 0x01, 0x0a, 0x25, 0x47, 0x65,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6e, 0x0a, 0x13, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61,
	0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xc8, 0x03, 0x0a, 0x1a, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x46, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70,
	0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x57, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70,
	0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e,
	0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0c,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x78, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x55,
	0x6e, 0x69, 0x78, 0x22, 0xc1, 0x01, 0x0a, 0x23, 0x53, 0x6b, 0x69, 0x70, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x17, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x22, 0x4b, 0x0a, 0x24, 0x53, 0x6b, 0x69, 0x70, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0xc2, 0x01, 0x0a, 0x24, 0x52, 0x65, 0x73, 0x65, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a,
	0x17, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x22, 0x4c, 0x0a, 0x25, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0xba, 0x08, 0x0a, 0x06, 0x4d, 0x61, 0x74, 0x72,
	0x69, 0x78, 0x12, 0xaf, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x46, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70,
	0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb2, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x47, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x48, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e,
	0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74,
	0x72, 0x69, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa3, 0x01, 0x0a, 0x18, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xaf, 0x01, 0x0a, 0x1c, 0x53, 0x6b, 0x69, 0x70, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x12, 0x46, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61,
	0x74, 0x72, 0x69, 0x78, 0x2e, 0x53, 0x6b, 0x69, 0x70, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53, 0x6b, 0x69,
	0x70, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0xb2, 0x01, 0x0a, 0x1d, 0x52, 0x65, 0x73, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x12, 0x47, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x48, 0x2e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61,
	0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78,
	0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xbb, 0x01, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4a, 0x2e, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70,
	0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67,
	0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x7c, 0x0a, 0x3c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x61,
	0x74, 0x72, 0x69, 0x78, 0x5a, 0x3c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2f, 0x73,
	0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6d, 0x61, 0x74, 0x72,
	0x69, 0x78, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescOnce sync.Once
	file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescData = file_api_stockguardian_sgapigateway_matrix_service_proto_rawDesc
)

func file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescGZIP() []byte {
	file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescOnce.Do(func() {
		file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescData)
	})
	return file_api_stockguardian_sgapigateway_matrix_service_proto_rawDescData
}

var file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_api_stockguardian_sgapigateway_matrix_service_proto_goTypes = []interface{}{
	(*UpdateCustomerApplicationDetailsRequest)(nil),                       // 0: stockguardian.sgapigateway.matrix.UpdateCustomerApplicationDetailsRequest
	(*UpdateCustomerApplicationDetailsResponse)(nil),                      // 1: stockguardian.sgapigateway.matrix.UpdateCustomerApplicationDetailsResponse
	(*StartCustomerApplicationRequest)(nil),                               // 2: stockguardian.sgapigateway.matrix.StartCustomerApplicationRequest
	(*StartCustomerApplicationResponse)(nil),                              // 3: stockguardian.sgapigateway.matrix.StartCustomerApplicationResponse
	(*GetCustomerApplicationStatusRequest)(nil),                           // 4: stockguardian.sgapigateway.matrix.GetCustomerApplicationStatusRequest
	(*GetCustomerApplicationStatusResponse)(nil),                          // 5: stockguardian.sgapigateway.matrix.GetCustomerApplicationStatusResponse
	(*ActionMetadata)(nil),                                                // 6: stockguardian.sgapigateway.matrix.ActionMetadata
	(*DigilockerKycMetadata)(nil),                                         // 7: stockguardian.sgapigateway.matrix.DigilockerKycMetadata
	(*VkycMetadata)(nil),                                                  // 8: stockguardian.sgapigateway.matrix.VkycMetadata
	(*GetCustomerApplicationDetailsRequest)(nil),                          // 9: stockguardian.sgapigateway.matrix.GetCustomerApplicationDetailsRequest
	(*GetCustomerApplicationDetailsResponse)(nil),                         // 10: stockguardian.sgapigateway.matrix.GetCustomerApplicationDetailsResponse
	(*CustomerApplicationDetails)(nil),                                    // 11: stockguardian.sgapigateway.matrix.CustomerApplicationDetails
	(*SkipCustomerApplicationStageRequest)(nil),                           // 12: stockguardian.sgapigateway.matrix.SkipCustomerApplicationStageRequest
	(*SkipCustomerApplicationStageResponse)(nil),                          // 13: stockguardian.sgapigateway.matrix.SkipCustomerApplicationStageResponse
	(*ResetCustomerApplicationStageRequest)(nil),                          // 14: stockguardian.sgapigateway.matrix.ResetCustomerApplicationStageRequest
	(*ResetCustomerApplicationStageResponse)(nil),                         // 15: stockguardian.sgapigateway.matrix.ResetCustomerApplicationStageResponse
	(*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId)(nil), // 16: stockguardian.sgapigateway.matrix.GetCustomerApplicationStatusRequest.ApplicantIdAndClientReqId
	(*rpc.Status)(nil),                                                    // 17: rpc.Status
	(*ApplicationData)(nil),                                               // 18: stockguardian.sgapigateway.matrix.ApplicationData
	(Stage)(0),                                                            // 19: stockguardian.sgapigateway.matrix.Stage
	(StageStatus)(0),                                                      // 20: stockguardian.sgapigateway.matrix.StageStatus
	(Action)(0),                                                           // 21: stockguardian.sgapigateway.matrix.Action
	(ApplicationStatus)(0),                                                // 22: stockguardian.sgapigateway.matrix.ApplicationStatus
	(FailureReason)(0),                                                    // 23: stockguardian.sgapigateway.matrix.FailureReason
	(*timestamppb.Timestamp)(nil),                                         // 24: google.protobuf.Timestamp
}
var file_api_stockguardian_sgapigateway_matrix_service_proto_depIdxs = []int32{
	17, // 0: stockguardian.sgapigateway.matrix.UpdateCustomerApplicationDetailsResponse.status:type_name -> rpc.Status
	18, // 1: stockguardian.sgapigateway.matrix.StartCustomerApplicationRequest.application_data:type_name -> stockguardian.sgapigateway.matrix.ApplicationData
	17, // 2: stockguardian.sgapigateway.matrix.StartCustomerApplicationResponse.status:type_name -> rpc.Status
	19, // 3: stockguardian.sgapigateway.matrix.StartCustomerApplicationResponse.current_stage:type_name -> stockguardian.sgapigateway.matrix.Stage
	20, // 4: stockguardian.sgapigateway.matrix.StartCustomerApplicationResponse.stage_status:type_name -> stockguardian.sgapigateway.matrix.StageStatus
	21, // 5: stockguardian.sgapigateway.matrix.StartCustomerApplicationResponse.next_action:type_name -> stockguardian.sgapigateway.matrix.Action
	6,  // 6: stockguardian.sgapigateway.matrix.StartCustomerApplicationResponse.action_metadata:type_name -> stockguardian.sgapigateway.matrix.ActionMetadata
	16, // 7: stockguardian.sgapigateway.matrix.GetCustomerApplicationStatusRequest.applicant_id_and_client_req_id:type_name -> stockguardian.sgapigateway.matrix.GetCustomerApplicationStatusRequest.ApplicantIdAndClientReqId
	17, // 8: stockguardian.sgapigateway.matrix.GetCustomerApplicationStatusResponse.status:type_name -> rpc.Status
	22, // 9: stockguardian.sgapigateway.matrix.GetCustomerApplicationStatusResponse.application_status:type_name -> stockguardian.sgapigateway.matrix.ApplicationStatus
	19, // 10: stockguardian.sgapigateway.matrix.GetCustomerApplicationStatusResponse.current_stage:type_name -> stockguardian.sgapigateway.matrix.Stage
	20, // 11: stockguardian.sgapigateway.matrix.GetCustomerApplicationStatusResponse.stage_status:type_name -> stockguardian.sgapigateway.matrix.StageStatus
	21, // 12: stockguardian.sgapigateway.matrix.GetCustomerApplicationStatusResponse.next_action:type_name -> stockguardian.sgapigateway.matrix.Action
	6,  // 13: stockguardian.sgapigateway.matrix.GetCustomerApplicationStatusResponse.action_metadata:type_name -> stockguardian.sgapigateway.matrix.ActionMetadata
	8,  // 14: stockguardian.sgapigateway.matrix.ActionMetadata.vkyc_metadata:type_name -> stockguardian.sgapigateway.matrix.VkycMetadata
	7,  // 15: stockguardian.sgapigateway.matrix.ActionMetadata.digilocker_kyc_metadata:type_name -> stockguardian.sgapigateway.matrix.DigilockerKycMetadata
	19, // 16: stockguardian.sgapigateway.matrix.GetCustomerApplicationDetailsRequest.stage:type_name -> stockguardian.sgapigateway.matrix.Stage
	17, // 17: stockguardian.sgapigateway.matrix.GetCustomerApplicationDetailsResponse.status:type_name -> rpc.Status
	11, // 18: stockguardian.sgapigateway.matrix.GetCustomerApplicationDetailsResponse.application_details:type_name -> stockguardian.sgapigateway.matrix.CustomerApplicationDetails
	19, // 19: stockguardian.sgapigateway.matrix.CustomerApplicationDetails.stage:type_name -> stockguardian.sgapigateway.matrix.Stage
	20, // 20: stockguardian.sgapigateway.matrix.CustomerApplicationDetails.status:type_name -> stockguardian.sgapigateway.matrix.StageStatus
	23, // 21: stockguardian.sgapigateway.matrix.CustomerApplicationDetails.failure_reason:type_name -> stockguardian.sgapigateway.matrix.FailureReason
	24, // 22: stockguardian.sgapigateway.matrix.CustomerApplicationDetails.completed_at:type_name -> google.protobuf.Timestamp
	19, // 23: stockguardian.sgapigateway.matrix.SkipCustomerApplicationStageRequest.stage:type_name -> stockguardian.sgapigateway.matrix.Stage
	17, // 24: stockguardian.sgapigateway.matrix.SkipCustomerApplicationStageResponse.status:type_name -> rpc.Status
	19, // 25: stockguardian.sgapigateway.matrix.ResetCustomerApplicationStageRequest.stage:type_name -> stockguardian.sgapigateway.matrix.Stage
	17, // 26: stockguardian.sgapigateway.matrix.ResetCustomerApplicationStageResponse.status:type_name -> rpc.Status
	4,  // 27: stockguardian.sgapigateway.matrix.Matrix.GetCustomerApplicationStatus:input_type -> stockguardian.sgapigateway.matrix.GetCustomerApplicationStatusRequest
	9,  // 28: stockguardian.sgapigateway.matrix.Matrix.GetCustomerApplicationDetails:input_type -> stockguardian.sgapigateway.matrix.GetCustomerApplicationDetailsRequest
	2,  // 29: stockguardian.sgapigateway.matrix.Matrix.StartCustomerApplication:input_type -> stockguardian.sgapigateway.matrix.StartCustomerApplicationRequest
	12, // 30: stockguardian.sgapigateway.matrix.Matrix.SkipCustomerApplicationStage:input_type -> stockguardian.sgapigateway.matrix.SkipCustomerApplicationStageRequest
	14, // 31: stockguardian.sgapigateway.matrix.Matrix.ResetCustomerApplicationStage:input_type -> stockguardian.sgapigateway.matrix.ResetCustomerApplicationStageRequest
	0,  // 32: stockguardian.sgapigateway.matrix.Matrix.UpdateCustomerApplicationDetails:input_type -> stockguardian.sgapigateway.matrix.UpdateCustomerApplicationDetailsRequest
	5,  // 33: stockguardian.sgapigateway.matrix.Matrix.GetCustomerApplicationStatus:output_type -> stockguardian.sgapigateway.matrix.GetCustomerApplicationStatusResponse
	10, // 34: stockguardian.sgapigateway.matrix.Matrix.GetCustomerApplicationDetails:output_type -> stockguardian.sgapigateway.matrix.GetCustomerApplicationDetailsResponse
	3,  // 35: stockguardian.sgapigateway.matrix.Matrix.StartCustomerApplication:output_type -> stockguardian.sgapigateway.matrix.StartCustomerApplicationResponse
	13, // 36: stockguardian.sgapigateway.matrix.Matrix.SkipCustomerApplicationStage:output_type -> stockguardian.sgapigateway.matrix.SkipCustomerApplicationStageResponse
	15, // 37: stockguardian.sgapigateway.matrix.Matrix.ResetCustomerApplicationStage:output_type -> stockguardian.sgapigateway.matrix.ResetCustomerApplicationStageResponse
	1,  // 38: stockguardian.sgapigateway.matrix.Matrix.UpdateCustomerApplicationDetails:output_type -> stockguardian.sgapigateway.matrix.UpdateCustomerApplicationDetailsResponse
	33, // [33:39] is the sub-list for method output_type
	27, // [27:33] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_api_stockguardian_sgapigateway_matrix_service_proto_init() }
func file_api_stockguardian_sgapigateway_matrix_service_proto_init() {
	if File_api_stockguardian_sgapigateway_matrix_service_proto != nil {
		return
	}
	file_api_stockguardian_sgapigateway_matrix_application_data_proto_init()
	file_api_stockguardian_sgapigateway_matrix_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCustomerApplicationDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCustomerApplicationDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartCustomerApplicationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartCustomerApplicationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerApplicationStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerApplicationStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActionMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DigilockerKycMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VkycMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerApplicationDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerApplicationDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerApplicationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SkipCustomerApplicationStageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SkipCustomerApplicationStageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetCustomerApplicationStageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetCustomerApplicationStageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*UpdateCustomerApplicationDetailsRequest_UserCurrentPincode)(nil),
	}
	file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*GetCustomerApplicationStatusRequest_ApplicationId)(nil),
		(*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId_)(nil),
	}
	file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*ActionMetadata_VkycMetadata)(nil),
		(*ActionMetadata_DigilockerKycMetadata)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_stockguardian_sgapigateway_matrix_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_stockguardian_sgapigateway_matrix_service_proto_goTypes,
		DependencyIndexes: file_api_stockguardian_sgapigateway_matrix_service_proto_depIdxs,
		MessageInfos:      file_api_stockguardian_sgapigateway_matrix_service_proto_msgTypes,
	}.Build()
	File_api_stockguardian_sgapigateway_matrix_service_proto = out.File
	file_api_stockguardian_sgapigateway_matrix_service_proto_rawDesc = nil
	file_api_stockguardian_sgapigateway_matrix_service_proto_goTypes = nil
	file_api_stockguardian_sgapigateway_matrix_service_proto_depIdxs = nil
}
