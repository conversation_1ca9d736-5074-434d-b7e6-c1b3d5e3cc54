// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/stockguardian/sgapigateway/matrix/service.proto

package matrix

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UpdateCustomerApplicationDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateCustomerApplicationDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateCustomerApplicationDetailsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UpdateCustomerApplicationDetailsRequestMultiError, or nil if none found.
func (m *UpdateCustomerApplicationDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerApplicationDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	switch v := m.ApplicationData.(type) {
	case *UpdateCustomerApplicationDetailsRequest_UserCurrentPincode:
		if v == nil {
			err := UpdateCustomerApplicationDetailsRequestValidationError{
				field:  "ApplicationData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for UserCurrentPincode
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return UpdateCustomerApplicationDetailsRequestMultiError(errors)
	}

	return nil
}

// UpdateCustomerApplicationDetailsRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateCustomerApplicationDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerApplicationDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerApplicationDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerApplicationDetailsRequestMultiError) AllErrors() []error { return m }

// UpdateCustomerApplicationDetailsRequestValidationError is the validation
// error returned by UpdateCustomerApplicationDetailsRequest.Validate if the
// designated constraints aren't met.
type UpdateCustomerApplicationDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerApplicationDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerApplicationDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerApplicationDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerApplicationDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerApplicationDetailsRequestValidationError) ErrorName() string {
	return "UpdateCustomerApplicationDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerApplicationDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerApplicationDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerApplicationDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerApplicationDetailsRequestValidationError{}

// Validate checks the field values on UpdateCustomerApplicationDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateCustomerApplicationDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateCustomerApplicationDetailsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateCustomerApplicationDetailsResponseMultiError, or nil if none found.
func (m *UpdateCustomerApplicationDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerApplicationDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCustomerApplicationDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCustomerApplicationDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCustomerApplicationDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCustomerApplicationDetailsResponseMultiError(errors)
	}

	return nil
}

// UpdateCustomerApplicationDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// UpdateCustomerApplicationDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerApplicationDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerApplicationDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerApplicationDetailsResponseMultiError) AllErrors() []error { return m }

// UpdateCustomerApplicationDetailsResponseValidationError is the validation
// error returned by UpdateCustomerApplicationDetailsResponse.Validate if the
// designated constraints aren't met.
type UpdateCustomerApplicationDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerApplicationDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerApplicationDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerApplicationDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerApplicationDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerApplicationDetailsResponseValidationError) ErrorName() string {
	return "UpdateCustomerApplicationDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerApplicationDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerApplicationDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerApplicationDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerApplicationDetailsResponseValidationError{}

// Validate checks the field values on StartCustomerApplicationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartCustomerApplicationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartCustomerApplicationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StartCustomerApplicationRequestMultiError, or nil if none found.
func (m *StartCustomerApplicationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StartCustomerApplicationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicantId

	// no validation rules for ClientReqId

	// no validation rules for OrchFlow

	if all {
		switch v := interface{}(m.GetApplicationData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartCustomerApplicationRequestValidationError{
					field:  "ApplicationData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartCustomerApplicationRequestValidationError{
					field:  "ApplicationData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApplicationData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartCustomerApplicationRequestValidationError{
				field:  "ApplicationData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StartCustomerApplicationRequestMultiError(errors)
	}

	return nil
}

// StartCustomerApplicationRequestMultiError is an error wrapping multiple
// validation errors returned by StartCustomerApplicationRequest.ValidateAll()
// if the designated constraints aren't met.
type StartCustomerApplicationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartCustomerApplicationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartCustomerApplicationRequestMultiError) AllErrors() []error { return m }

// StartCustomerApplicationRequestValidationError is the validation error
// returned by StartCustomerApplicationRequest.Validate if the designated
// constraints aren't met.
type StartCustomerApplicationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartCustomerApplicationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartCustomerApplicationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartCustomerApplicationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartCustomerApplicationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartCustomerApplicationRequestValidationError) ErrorName() string {
	return "StartCustomerApplicationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StartCustomerApplicationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartCustomerApplicationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartCustomerApplicationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartCustomerApplicationRequestValidationError{}

// Validate checks the field values on StartCustomerApplicationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StartCustomerApplicationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartCustomerApplicationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StartCustomerApplicationResponseMultiError, or nil if none found.
func (m *StartCustomerApplicationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StartCustomerApplicationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartCustomerApplicationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartCustomerApplicationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartCustomerApplicationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrentStage

	// no validation rules for StageStatus

	// no validation rules for NextAction

	if all {
		switch v := interface{}(m.GetActionMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartCustomerApplicationResponseValidationError{
					field:  "ActionMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartCustomerApplicationResponseValidationError{
					field:  "ActionMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartCustomerApplicationResponseValidationError{
				field:  "ActionMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicationId

	if len(errors) > 0 {
		return StartCustomerApplicationResponseMultiError(errors)
	}

	return nil
}

// StartCustomerApplicationResponseMultiError is an error wrapping multiple
// validation errors returned by
// StartCustomerApplicationResponse.ValidateAll() if the designated
// constraints aren't met.
type StartCustomerApplicationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartCustomerApplicationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartCustomerApplicationResponseMultiError) AllErrors() []error { return m }

// StartCustomerApplicationResponseValidationError is the validation error
// returned by StartCustomerApplicationResponse.Validate if the designated
// constraints aren't met.
type StartCustomerApplicationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartCustomerApplicationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartCustomerApplicationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartCustomerApplicationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartCustomerApplicationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartCustomerApplicationResponseValidationError) ErrorName() string {
	return "StartCustomerApplicationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StartCustomerApplicationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartCustomerApplicationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartCustomerApplicationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartCustomerApplicationResponseValidationError{}

// Validate checks the field values on GetCustomerApplicationStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCustomerApplicationStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomerApplicationStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCustomerApplicationStatusRequestMultiError, or nil if none found.
func (m *GetCustomerApplicationStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerApplicationStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.ApplicationIdentifier.(type) {
	case *GetCustomerApplicationStatusRequest_ApplicationId:
		if v == nil {
			err := GetCustomerApplicationStatusRequestValidationError{
				field:  "ApplicationIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ApplicationId
	case *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId_:
		if v == nil {
			err := GetCustomerApplicationStatusRequestValidationError{
				field:  "ApplicationIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetApplicantIdAndClientReqId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCustomerApplicationStatusRequestValidationError{
						field:  "ApplicantIdAndClientReqId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCustomerApplicationStatusRequestValidationError{
						field:  "ApplicantIdAndClientReqId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetApplicantIdAndClientReqId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCustomerApplicationStatusRequestValidationError{
					field:  "ApplicantIdAndClientReqId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetCustomerApplicationStatusRequestMultiError(errors)
	}

	return nil
}

// GetCustomerApplicationStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetCustomerApplicationStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCustomerApplicationStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerApplicationStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerApplicationStatusRequestMultiError) AllErrors() []error { return m }

// GetCustomerApplicationStatusRequestValidationError is the validation error
// returned by GetCustomerApplicationStatusRequest.Validate if the designated
// constraints aren't met.
type GetCustomerApplicationStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerApplicationStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomerApplicationStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomerApplicationStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomerApplicationStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomerApplicationStatusRequestValidationError) ErrorName() string {
	return "GetCustomerApplicationStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerApplicationStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerApplicationStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerApplicationStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerApplicationStatusRequestValidationError{}

// Validate checks the field values on GetCustomerApplicationStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCustomerApplicationStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomerApplicationStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCustomerApplicationStatusResponseMultiError, or nil if none found.
func (m *GetCustomerApplicationStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerApplicationStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerApplicationStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerApplicationStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerApplicationStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicationStatus

	// no validation rules for CurrentStage

	// no validation rules for StageStatus

	// no validation rules for NextAction

	if all {
		switch v := interface{}(m.GetActionMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerApplicationStatusResponseValidationError{
					field:  "ActionMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerApplicationStatusResponseValidationError{
					field:  "ActionMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerApplicationStatusResponseValidationError{
				field:  "ActionMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicationId

	if len(errors) > 0 {
		return GetCustomerApplicationStatusResponseMultiError(errors)
	}

	return nil
}

// GetCustomerApplicationStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetCustomerApplicationStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCustomerApplicationStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerApplicationStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerApplicationStatusResponseMultiError) AllErrors() []error { return m }

// GetCustomerApplicationStatusResponseValidationError is the validation error
// returned by GetCustomerApplicationStatusResponse.Validate if the designated
// constraints aren't met.
type GetCustomerApplicationStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerApplicationStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomerApplicationStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomerApplicationStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomerApplicationStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomerApplicationStatusResponseValidationError) ErrorName() string {
	return "GetCustomerApplicationStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerApplicationStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerApplicationStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerApplicationStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerApplicationStatusResponseValidationError{}

// Validate checks the field values on ActionMetadata with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ActionMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActionMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ActionMetadataMultiError,
// or nil if none found.
func (m *ActionMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *ActionMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Metadata.(type) {
	case *ActionMetadata_VkycMetadata:
		if v == nil {
			err := ActionMetadataValidationError{
				field:  "Metadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetVkycMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionMetadataValidationError{
						field:  "VkycMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionMetadataValidationError{
						field:  "VkycMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVkycMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionMetadataValidationError{
					field:  "VkycMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionMetadata_DigilockerKycMetadata:
		if v == nil {
			err := ActionMetadataValidationError{
				field:  "Metadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDigilockerKycMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionMetadataValidationError{
						field:  "DigilockerKycMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionMetadataValidationError{
						field:  "DigilockerKycMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDigilockerKycMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionMetadataValidationError{
					field:  "DigilockerKycMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ActionMetadataMultiError(errors)
	}

	return nil
}

// ActionMetadataMultiError is an error wrapping multiple validation errors
// returned by ActionMetadata.ValidateAll() if the designated constraints
// aren't met.
type ActionMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionMetadataMultiError) AllErrors() []error { return m }

// ActionMetadataValidationError is the validation error returned by
// ActionMetadata.Validate if the designated constraints aren't met.
type ActionMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionMetadataValidationError) ErrorName() string { return "ActionMetadataValidationError" }

// Error satisfies the builtin error interface
func (e ActionMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActionMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionMetadataValidationError{}

// Validate checks the field values on DigilockerKycMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DigilockerKycMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DigilockerKycMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DigilockerKycMetadataMultiError, or nil if none found.
func (m *DigilockerKycMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *DigilockerKycMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OauthUrl

	if len(errors) > 0 {
		return DigilockerKycMetadataMultiError(errors)
	}

	return nil
}

// DigilockerKycMetadataMultiError is an error wrapping multiple validation
// errors returned by DigilockerKycMetadata.ValidateAll() if the designated
// constraints aren't met.
type DigilockerKycMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DigilockerKycMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DigilockerKycMetadataMultiError) AllErrors() []error { return m }

// DigilockerKycMetadataValidationError is the validation error returned by
// DigilockerKycMetadata.Validate if the designated constraints aren't met.
type DigilockerKycMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DigilockerKycMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DigilockerKycMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DigilockerKycMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DigilockerKycMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DigilockerKycMetadataValidationError) ErrorName() string {
	return "DigilockerKycMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e DigilockerKycMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDigilockerKycMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DigilockerKycMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DigilockerKycMetadataValidationError{}

// Validate checks the field values on VkycMetadata with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VkycMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VkycMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VkycMetadataMultiError, or
// nil if none found.
func (m *VkycMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *VkycMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for CallId

	// no validation rules for CallStatus

	if len(errors) > 0 {
		return VkycMetadataMultiError(errors)
	}

	return nil
}

// VkycMetadataMultiError is an error wrapping multiple validation errors
// returned by VkycMetadata.ValidateAll() if the designated constraints aren't met.
type VkycMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VkycMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VkycMetadataMultiError) AllErrors() []error { return m }

// VkycMetadataValidationError is the validation error returned by
// VkycMetadata.Validate if the designated constraints aren't met.
type VkycMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VkycMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VkycMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VkycMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VkycMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VkycMetadataValidationError) ErrorName() string { return "VkycMetadataValidationError" }

// Error satisfies the builtin error interface
func (e VkycMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVkycMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VkycMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VkycMetadataValidationError{}

// Validate checks the field values on GetCustomerApplicationDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCustomerApplicationDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomerApplicationDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCustomerApplicationDetailsRequestMultiError, or nil if none found.
func (m *GetCustomerApplicationDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerApplicationDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for Stage

	if len(errors) > 0 {
		return GetCustomerApplicationDetailsRequestMultiError(errors)
	}

	return nil
}

// GetCustomerApplicationDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetCustomerApplicationDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCustomerApplicationDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerApplicationDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerApplicationDetailsRequestMultiError) AllErrors() []error { return m }

// GetCustomerApplicationDetailsRequestValidationError is the validation error
// returned by GetCustomerApplicationDetailsRequest.Validate if the designated
// constraints aren't met.
type GetCustomerApplicationDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerApplicationDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomerApplicationDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomerApplicationDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomerApplicationDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomerApplicationDetailsRequestValidationError) ErrorName() string {
	return "GetCustomerApplicationDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerApplicationDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerApplicationDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerApplicationDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerApplicationDetailsRequestValidationError{}

// Validate checks the field values on GetCustomerApplicationDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCustomerApplicationDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomerApplicationDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCustomerApplicationDetailsResponseMultiError, or nil if none found.
func (m *GetCustomerApplicationDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerApplicationDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerApplicationDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerApplicationDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerApplicationDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetApplicationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerApplicationDetailsResponseValidationError{
					field:  "ApplicationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerApplicationDetailsResponseValidationError{
					field:  "ApplicationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApplicationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerApplicationDetailsResponseValidationError{
				field:  "ApplicationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCustomerApplicationDetailsResponseMultiError(errors)
	}

	return nil
}

// GetCustomerApplicationDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetCustomerApplicationDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCustomerApplicationDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerApplicationDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerApplicationDetailsResponseMultiError) AllErrors() []error { return m }

// GetCustomerApplicationDetailsResponseValidationError is the validation error
// returned by GetCustomerApplicationDetailsResponse.Validate if the
// designated constraints aren't met.
type GetCustomerApplicationDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerApplicationDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomerApplicationDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomerApplicationDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomerApplicationDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomerApplicationDetailsResponseValidationError) ErrorName() string {
	return "GetCustomerApplicationDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerApplicationDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerApplicationDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerApplicationDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerApplicationDetailsResponseValidationError{}

// Validate checks the field values on CustomerApplicationDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomerApplicationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerApplicationDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerApplicationDetailsMultiError, or nil if none found.
func (m *CustomerApplicationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerApplicationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerApplicationId

	// no validation rules for ClientRequestId

	// no validation rules for Stage

	// no validation rules for Status

	// no validation rules for FailureReason

	if all {
		switch v := interface{}(m.GetCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerApplicationDetailsValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerApplicationDetailsValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerApplicationDetailsValidationError{
				field:  "CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeletedAtUnix

	if len(errors) > 0 {
		return CustomerApplicationDetailsMultiError(errors)
	}

	return nil
}

// CustomerApplicationDetailsMultiError is an error wrapping multiple
// validation errors returned by CustomerApplicationDetails.ValidateAll() if
// the designated constraints aren't met.
type CustomerApplicationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerApplicationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerApplicationDetailsMultiError) AllErrors() []error { return m }

// CustomerApplicationDetailsValidationError is the validation error returned
// by CustomerApplicationDetails.Validate if the designated constraints aren't met.
type CustomerApplicationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerApplicationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerApplicationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerApplicationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerApplicationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerApplicationDetailsValidationError) ErrorName() string {
	return "CustomerApplicationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CustomerApplicationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerApplicationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerApplicationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerApplicationDetailsValidationError{}

// Validate checks the field values on SkipCustomerApplicationStageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SkipCustomerApplicationStageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SkipCustomerApplicationStageRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SkipCustomerApplicationStageRequestMultiError, or nil if none found.
func (m *SkipCustomerApplicationStageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SkipCustomerApplicationStageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerApplicationId

	// no validation rules for ClientReqId

	// no validation rules for Stage

	if len(errors) > 0 {
		return SkipCustomerApplicationStageRequestMultiError(errors)
	}

	return nil
}

// SkipCustomerApplicationStageRequestMultiError is an error wrapping multiple
// validation errors returned by
// SkipCustomerApplicationStageRequest.ValidateAll() if the designated
// constraints aren't met.
type SkipCustomerApplicationStageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SkipCustomerApplicationStageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SkipCustomerApplicationStageRequestMultiError) AllErrors() []error { return m }

// SkipCustomerApplicationStageRequestValidationError is the validation error
// returned by SkipCustomerApplicationStageRequest.Validate if the designated
// constraints aren't met.
type SkipCustomerApplicationStageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SkipCustomerApplicationStageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SkipCustomerApplicationStageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SkipCustomerApplicationStageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SkipCustomerApplicationStageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SkipCustomerApplicationStageRequestValidationError) ErrorName() string {
	return "SkipCustomerApplicationStageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SkipCustomerApplicationStageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSkipCustomerApplicationStageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SkipCustomerApplicationStageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SkipCustomerApplicationStageRequestValidationError{}

// Validate checks the field values on SkipCustomerApplicationStageResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SkipCustomerApplicationStageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SkipCustomerApplicationStageResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SkipCustomerApplicationStageResponseMultiError, or nil if none found.
func (m *SkipCustomerApplicationStageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SkipCustomerApplicationStageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SkipCustomerApplicationStageResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SkipCustomerApplicationStageResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SkipCustomerApplicationStageResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SkipCustomerApplicationStageResponseMultiError(errors)
	}

	return nil
}

// SkipCustomerApplicationStageResponseMultiError is an error wrapping multiple
// validation errors returned by
// SkipCustomerApplicationStageResponse.ValidateAll() if the designated
// constraints aren't met.
type SkipCustomerApplicationStageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SkipCustomerApplicationStageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SkipCustomerApplicationStageResponseMultiError) AllErrors() []error { return m }

// SkipCustomerApplicationStageResponseValidationError is the validation error
// returned by SkipCustomerApplicationStageResponse.Validate if the designated
// constraints aren't met.
type SkipCustomerApplicationStageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SkipCustomerApplicationStageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SkipCustomerApplicationStageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SkipCustomerApplicationStageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SkipCustomerApplicationStageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SkipCustomerApplicationStageResponseValidationError) ErrorName() string {
	return "SkipCustomerApplicationStageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SkipCustomerApplicationStageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSkipCustomerApplicationStageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SkipCustomerApplicationStageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SkipCustomerApplicationStageResponseValidationError{}

// Validate checks the field values on ResetCustomerApplicationStageRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ResetCustomerApplicationStageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetCustomerApplicationStageRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ResetCustomerApplicationStageRequestMultiError, or nil if none found.
func (m *ResetCustomerApplicationStageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetCustomerApplicationStageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerApplicationId

	// no validation rules for ClientReqId

	// no validation rules for Stage

	if len(errors) > 0 {
		return ResetCustomerApplicationStageRequestMultiError(errors)
	}

	return nil
}

// ResetCustomerApplicationStageRequestMultiError is an error wrapping multiple
// validation errors returned by
// ResetCustomerApplicationStageRequest.ValidateAll() if the designated
// constraints aren't met.
type ResetCustomerApplicationStageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetCustomerApplicationStageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetCustomerApplicationStageRequestMultiError) AllErrors() []error { return m }

// ResetCustomerApplicationStageRequestValidationError is the validation error
// returned by ResetCustomerApplicationStageRequest.Validate if the designated
// constraints aren't met.
type ResetCustomerApplicationStageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetCustomerApplicationStageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetCustomerApplicationStageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetCustomerApplicationStageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetCustomerApplicationStageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetCustomerApplicationStageRequestValidationError) ErrorName() string {
	return "ResetCustomerApplicationStageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResetCustomerApplicationStageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetCustomerApplicationStageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetCustomerApplicationStageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetCustomerApplicationStageRequestValidationError{}

// Validate checks the field values on ResetCustomerApplicationStageResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ResetCustomerApplicationStageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetCustomerApplicationStageResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ResetCustomerApplicationStageResponseMultiError, or nil if none found.
func (m *ResetCustomerApplicationStageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetCustomerApplicationStageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResetCustomerApplicationStageResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResetCustomerApplicationStageResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResetCustomerApplicationStageResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResetCustomerApplicationStageResponseMultiError(errors)
	}

	return nil
}

// ResetCustomerApplicationStageResponseMultiError is an error wrapping
// multiple validation errors returned by
// ResetCustomerApplicationStageResponse.ValidateAll() if the designated
// constraints aren't met.
type ResetCustomerApplicationStageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetCustomerApplicationStageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetCustomerApplicationStageResponseMultiError) AllErrors() []error { return m }

// ResetCustomerApplicationStageResponseValidationError is the validation error
// returned by ResetCustomerApplicationStageResponse.Validate if the
// designated constraints aren't met.
type ResetCustomerApplicationStageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetCustomerApplicationStageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetCustomerApplicationStageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetCustomerApplicationStageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetCustomerApplicationStageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetCustomerApplicationStageResponseValidationError) ErrorName() string {
	return "ResetCustomerApplicationStageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResetCustomerApplicationStageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetCustomerApplicationStageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetCustomerApplicationStageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetCustomerApplicationStageResponseValidationError{}

// Validate checks the field values on
// GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdMultiError, or
// nil if none found.
func (m *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicantId

	// no validation rules for ClientReqId

	if len(errors) > 0 {
		return GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdMultiError(errors)
	}

	return nil
}

// GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdMultiError is
// an error wrapping multiple validation errors returned by
// GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId.ValidateAll()
// if the designated constraints aren't met.
type GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdMultiError) AllErrors() []error {
	return m
}

// GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdValidationError
// is the validation error returned by
// GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId.Validate if
// the designated constraints aren't met.
type GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdValidationError) ErrorName() string {
	return "GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqIdValidationError{}
