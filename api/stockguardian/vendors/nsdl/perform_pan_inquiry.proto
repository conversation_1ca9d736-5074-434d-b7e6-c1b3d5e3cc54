syntax = "proto3";

package stockguardian.vendors.nsdl;

import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/vendors/nsdl";

message PerformPanInquiryRequest {
  // pan inquiry data (supports min 1 and max 5 pan inputs)
  repeated PanInquiryData inquiries_data = 1 [json_name = 'inputData'];
  // A signature generated by signing the payload (json data) with Stockguardian's unique digital certificate
  string signature = 2 [json_name = 'signature'];
}

message PanInquiryData {
  string pan = 1 [json_name = 'pan'];
  string name = 2 [json_name = 'name'];
  // optional field
  // used google.protobuf.Value to allow null value in marshalled json which is require by vendor
  google.protobuf.Value father_name = 3 [json_name = "fathername"];
  string dob = 4 [json_name = 'dob'];
}

message PerformPanInquiryResponse {
  string response_code = 1 [json_name = 'response_Code'];
  repeated PanInquiryResult inquiry_results = 2 [json_name = 'outputData'];
}


message PanInquiryResult {
  string pan = 1 [json_name = 'pan'];
  string pan_status = 2 [json_name = 'pan_status'];
  string name = 3 [json_name = 'name'];
  string father_name = 4 [json_name = 'fathername'];
  string dob = 5 [json_name = 'dob'];
  string seeding_status = 6 [json_name = 'seeding_status'];
}
