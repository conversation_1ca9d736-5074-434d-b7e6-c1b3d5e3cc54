// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/preapprovedloan/enums/mandate.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Denotes the type of mandate to be used for setting up auto-deduction of loan EMI payments
type MandateType int32

const (
	MandateType_MANDATE_TYPE_UNSPECIFIED MandateType = 0
	// Note: E-NACH can have sub-variations like debit-card or net-banking based authorisation modes
	MandateType_MANDATE_TYPE_E_NACH MandateType = 1
	MandateType_MANDATE_TYPE_UPI    MandateType = 2
)

// Enum value maps for MandateType.
var (
	MandateType_name = map[int32]string{
		0: "MANDATE_TYPE_UNSPECIFIED",
		1: "MANDATE_TYPE_E_NACH",
		2: "MANDATE_TYPE_UPI",
	}
	MandateType_value = map[string]int32{
		"MANDATE_TYPE_UNSPECIFIED": 0,
		"MANDATE_TYPE_E_NACH":      1,
		"MANDATE_TYPE_UPI":         2,
	}
)

func (x MandateType) Enum() *MandateType {
	p := new(MandateType)
	*p = x
	return p
}

func (x MandateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MandateType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_mandate_proto_enumTypes[0].Descriptor()
}

func (MandateType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_mandate_proto_enumTypes[0]
}

func (x MandateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MandateType.Descriptor instead.
func (MandateType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_mandate_proto_rawDescGZIP(), []int{0}
}

var File_api_preapprovedloan_enums_mandate_proto protoreflect.FileDescriptor

var file_api_preapprovedloan_enums_mandate_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x6d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2a, 0x5a, 0x0a, 0x0b, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1c, 0x0a, 0x18, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a,
	0x13, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x5f,
	0x4e, 0x41, 0x43, 0x48, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x50, 0x49, 0x10, 0x02, 0x42, 0x64, 0x0a, 0x30,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x5a, 0x30, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_preapprovedloan_enums_mandate_proto_rawDescOnce sync.Once
	file_api_preapprovedloan_enums_mandate_proto_rawDescData = file_api_preapprovedloan_enums_mandate_proto_rawDesc
)

func file_api_preapprovedloan_enums_mandate_proto_rawDescGZIP() []byte {
	file_api_preapprovedloan_enums_mandate_proto_rawDescOnce.Do(func() {
		file_api_preapprovedloan_enums_mandate_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_preapprovedloan_enums_mandate_proto_rawDescData)
	})
	return file_api_preapprovedloan_enums_mandate_proto_rawDescData
}

var file_api_preapprovedloan_enums_mandate_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_preapprovedloan_enums_mandate_proto_goTypes = []interface{}{
	(MandateType)(0), // 0: preapprovedloan.enums.MandateType
}
var file_api_preapprovedloan_enums_mandate_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_preapprovedloan_enums_mandate_proto_init() }
func file_api_preapprovedloan_enums_mandate_proto_init() {
	if File_api_preapprovedloan_enums_mandate_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_preapprovedloan_enums_mandate_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_preapprovedloan_enums_mandate_proto_goTypes,
		DependencyIndexes: file_api_preapprovedloan_enums_mandate_proto_depIdxs,
		EnumInfos:         file_api_preapprovedloan_enums_mandate_proto_enumTypes,
	}.Build()
	File_api_preapprovedloan_enums_mandate_proto = out.File
	file_api_preapprovedloan_enums_mandate_proto_rawDesc = nil
	file_api_preapprovedloan_enums_mandate_proto_goTypes = nil
	file_api_preapprovedloan_enums_mandate_proto_depIdxs = nil
}
