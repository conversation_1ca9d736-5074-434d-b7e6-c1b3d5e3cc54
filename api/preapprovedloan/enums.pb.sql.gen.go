// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/preapprovedloan/enums.pb.go

package preapprovedloan

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the Vendor in string format in DB
func (p Vendor) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Vendor while reading from DB
func (p *Vendor) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Vendor_value[val]
	if !ok {
		return fmt.Errorf("unexpected Vendor value: %s", val)
	}
	*p = Vendor(valInt)
	return nil
}

// Marshaler interface implementation for Vendor
func (x Vendor) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Vendor
func (x *Vendor) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Vendor(Vendor_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanRequestType in string format in DB
func (p LoanRequestType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanRequestType while reading from DB
func (p *LoanRequestType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanRequestType_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanRequestType value: %s", val)
	}
	*p = LoanRequestType(valInt)
	return nil
}

// Marshaler interface implementation for LoanRequestType
func (x LoanRequestType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanRequestType
func (x *LoanRequestType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanRequestType(LoanRequestType_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanRequestStatus in string format in DB
func (p LoanRequestStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanRequestStatus while reading from DB
func (p *LoanRequestStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanRequestStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanRequestStatus value: %s", val)
	}
	*p = LoanRequestStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanRequestStatus
func (x LoanRequestStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanRequestStatus
func (x *LoanRequestStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanRequestStatus(LoanRequestStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanRequestSubStatus in string format in DB
func (p LoanRequestSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanRequestSubStatus while reading from DB
func (p *LoanRequestSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanRequestSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanRequestSubStatus value: %s", val)
	}
	*p = LoanRequestSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanRequestSubStatus
func (x LoanRequestSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanRequestSubStatus
func (x *LoanRequestSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanRequestSubStatus(LoanRequestSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanAccountStatus in string format in DB
func (p LoanAccountStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanAccountStatus while reading from DB
func (p *LoanAccountStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanAccountStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanAccountStatus value: %s", val)
	}
	*p = LoanAccountStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanAccountStatus
func (x LoanAccountStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanAccountStatus
func (x *LoanAccountStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanAccountStatus(LoanAccountStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanType in string format in DB
func (p LoanType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanType while reading from DB
func (p *LoanType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanType_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanType value: %s", val)
	}
	*p = LoanType(valInt)
	return nil
}

// Marshaler interface implementation for LoanType
func (x LoanType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanType
func (x *LoanType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanType(LoanType_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanRequestFieldMask in string format in DB
func (p LoanRequestFieldMask) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanRequestFieldMask while reading from DB
func (p *LoanRequestFieldMask) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanRequestFieldMask_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanRequestFieldMask value: %s", val)
	}
	*p = LoanRequestFieldMask(valInt)
	return nil
}

// Marshaler interface implementation for LoanRequestFieldMask
func (x LoanRequestFieldMask) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanRequestFieldMask
func (x *LoanRequestFieldMask) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanRequestFieldMask(LoanRequestFieldMask_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanAccountFieldMask in string format in DB
func (p LoanAccountFieldMask) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanAccountFieldMask while reading from DB
func (p *LoanAccountFieldMask) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanAccountFieldMask_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanAccountFieldMask value: %s", val)
	}
	*p = LoanAccountFieldMask(valInt)
	return nil
}

// Marshaler interface implementation for LoanAccountFieldMask
func (x LoanAccountFieldMask) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanAccountFieldMask
func (x *LoanAccountFieldMask) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanAccountFieldMask(LoanAccountFieldMask_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanOfferFieldMask in string format in DB
func (p LoanOfferFieldMask) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanOfferFieldMask while reading from DB
func (p *LoanOfferFieldMask) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanOfferFieldMask_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanOfferFieldMask value: %s", val)
	}
	*p = LoanOfferFieldMask(valInt)
	return nil
}

// Marshaler interface implementation for LoanOfferFieldMask
func (x LoanOfferFieldMask) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanOfferFieldMask
func (x *LoanOfferFieldMask) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanOfferFieldMask(LoanOfferFieldMask_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanApplicantFieldMask in string format in DB
func (p LoanApplicantFieldMask) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanApplicantFieldMask while reading from DB
func (p *LoanApplicantFieldMask) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanApplicantFieldMask_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanApplicantFieldMask value: %s", val)
	}
	*p = LoanApplicantFieldMask(valInt)
	return nil
}

// Marshaler interface implementation for LoanApplicantFieldMask
func (x LoanApplicantFieldMask) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanApplicantFieldMask
func (x *LoanApplicantFieldMask) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanApplicantFieldMask(LoanApplicantFieldMask_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanStepExecutionStatus in string format in DB
func (p LoanStepExecutionStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanStepExecutionStatus while reading from DB
func (p *LoanStepExecutionStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanStepExecutionStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanStepExecutionStatus value: %s", val)
	}
	*p = LoanStepExecutionStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanStepExecutionStatus
func (x LoanStepExecutionStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanStepExecutionStatus
func (x *LoanStepExecutionStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanStepExecutionStatus(LoanStepExecutionStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanStepExecutionSubStatus in string format in DB
func (p LoanStepExecutionSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanStepExecutionSubStatus while reading from DB
func (p *LoanStepExecutionSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanStepExecutionSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanStepExecutionSubStatus value: %s", val)
	}
	*p = LoanStepExecutionSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanStepExecutionSubStatus
func (x LoanStepExecutionSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanStepExecutionSubStatus
func (x *LoanStepExecutionSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanStepExecutionSubStatus(LoanStepExecutionSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanStepExecutionFlow in string format in DB
func (p LoanStepExecutionFlow) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanStepExecutionFlow while reading from DB
func (p *LoanStepExecutionFlow) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanStepExecutionFlow_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanStepExecutionFlow value: %s", val)
	}
	*p = LoanStepExecutionFlow(valInt)
	return nil
}

// Marshaler interface implementation for LoanStepExecutionFlow
func (x LoanStepExecutionFlow) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanStepExecutionFlow
func (x *LoanStepExecutionFlow) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanStepExecutionFlow(LoanStepExecutionFlow_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanStepExecutionStepName in string format in DB
func (p LoanStepExecutionStepName) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanStepExecutionStepName while reading from DB
func (p *LoanStepExecutionStepName) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanStepExecutionStepName_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanStepExecutionStepName value: %s", val)
	}
	*p = LoanStepExecutionStepName(valInt)
	return nil
}

// Marshaler interface implementation for LoanStepExecutionStepName
func (x LoanStepExecutionStepName) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanStepExecutionStepName
func (x *LoanStepExecutionStepName) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanStepExecutionStepName(LoanStepExecutionStepName_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanStepExecutionFieldMask in string format in DB
func (p LoanStepExecutionFieldMask) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanStepExecutionFieldMask while reading from DB
func (p *LoanStepExecutionFieldMask) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanStepExecutionFieldMask_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanStepExecutionFieldMask value: %s", val)
	}
	*p = LoanStepExecutionFieldMask(valInt)
	return nil
}

// Marshaler interface implementation for LoanStepExecutionFieldMask
func (x LoanStepExecutionFieldMask) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanStepExecutionFieldMask
func (x *LoanStepExecutionFieldMask) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanStepExecutionFieldMask(LoanStepExecutionFieldMask_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanOfferEligibilityCriteriaFieldMask in string format in DB
func (p LoanOfferEligibilityCriteriaFieldMask) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanOfferEligibilityCriteriaFieldMask while reading from DB
func (p *LoanOfferEligibilityCriteriaFieldMask) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanOfferEligibilityCriteriaFieldMask_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanOfferEligibilityCriteriaFieldMask value: %s", val)
	}
	*p = LoanOfferEligibilityCriteriaFieldMask(valInt)
	return nil
}

// Marshaler interface implementation for LoanOfferEligibilityCriteriaFieldMask
func (x LoanOfferEligibilityCriteriaFieldMask) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanOfferEligibilityCriteriaFieldMask
func (x *LoanOfferEligibilityCriteriaFieldMask) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanOfferEligibilityCriteriaFieldMask(LoanOfferEligibilityCriteriaFieldMask_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanOfferEligibilityCriteriaStatus in string format in DB
func (p LoanOfferEligibilityCriteriaStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanOfferEligibilityCriteriaStatus while reading from DB
func (p *LoanOfferEligibilityCriteriaStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanOfferEligibilityCriteriaStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanOfferEligibilityCriteriaStatus value: %s", val)
	}
	*p = LoanOfferEligibilityCriteriaStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanOfferEligibilityCriteriaStatus
func (x LoanOfferEligibilityCriteriaStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanOfferEligibilityCriteriaStatus
func (x *LoanOfferEligibilityCriteriaStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanOfferEligibilityCriteriaStatus(LoanOfferEligibilityCriteriaStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanOfferEligibilityCriteriaSubStatus in string format in DB
func (p LoanOfferEligibilityCriteriaSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanOfferEligibilityCriteriaSubStatus while reading from DB
func (p *LoanOfferEligibilityCriteriaSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanOfferEligibilityCriteriaSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanOfferEligibilityCriteriaSubStatus value: %s", val)
	}
	*p = LoanOfferEligibilityCriteriaSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanOfferEligibilityCriteriaSubStatus
func (x LoanOfferEligibilityCriteriaSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanOfferEligibilityCriteriaSubStatus
func (x *LoanOfferEligibilityCriteriaSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanOfferEligibilityCriteriaSubStatus(LoanOfferEligibilityCriteriaSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanInstallmentInfoStatus in string format in DB
func (p LoanInstallmentInfoStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanInstallmentInfoStatus while reading from DB
func (p *LoanInstallmentInfoStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanInstallmentInfoStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanInstallmentInfoStatus value: %s", val)
	}
	*p = LoanInstallmentInfoStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanInstallmentInfoStatus
func (x LoanInstallmentInfoStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanInstallmentInfoStatus
func (x *LoanInstallmentInfoStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanInstallmentInfoStatus(LoanInstallmentInfoStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanInstallmentInfoFieldMask in string format in DB
func (p LoanInstallmentInfoFieldMask) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanInstallmentInfoFieldMask while reading from DB
func (p *LoanInstallmentInfoFieldMask) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanInstallmentInfoFieldMask_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanInstallmentInfoFieldMask value: %s", val)
	}
	*p = LoanInstallmentInfoFieldMask(valInt)
	return nil
}

// Marshaler interface implementation for LoanInstallmentInfoFieldMask
func (x LoanInstallmentInfoFieldMask) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanInstallmentInfoFieldMask
func (x *LoanInstallmentInfoFieldMask) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanInstallmentInfoFieldMask(LoanInstallmentInfoFieldMask_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanActivityType in string format in DB
func (p LoanActivityType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanActivityType while reading from DB
func (p *LoanActivityType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanActivityType_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanActivityType value: %s", val)
	}
	*p = LoanActivityType(valInt)
	return nil
}

// Marshaler interface implementation for LoanActivityType
func (x LoanActivityType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanActivityType
func (x *LoanActivityType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanActivityType(LoanActivityType_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanActivityFieldMask in string format in DB
func (p LoanActivityFieldMask) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanActivityFieldMask while reading from DB
func (p *LoanActivityFieldMask) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanActivityFieldMask_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanActivityFieldMask value: %s", val)
	}
	*p = LoanActivityFieldMask(valInt)
	return nil
}

// Marshaler interface implementation for LoanActivityFieldMask
func (x LoanActivityFieldMask) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanActivityFieldMask
func (x *LoanActivityFieldMask) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanActivityFieldMask(LoanActivityFieldMask_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanPaymentRequestType in string format in DB
func (p LoanPaymentRequestType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanPaymentRequestType while reading from DB
func (p *LoanPaymentRequestType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanPaymentRequestType_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanPaymentRequestType value: %s", val)
	}
	*p = LoanPaymentRequestType(valInt)
	return nil
}

// Marshaler interface implementation for LoanPaymentRequestType
func (x LoanPaymentRequestType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanPaymentRequestType
func (x *LoanPaymentRequestType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanPaymentRequestType(LoanPaymentRequestType_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanPaymentRequestStatus in string format in DB
func (p LoanPaymentRequestStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanPaymentRequestStatus while reading from DB
func (p *LoanPaymentRequestStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanPaymentRequestStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanPaymentRequestStatus value: %s", val)
	}
	*p = LoanPaymentRequestStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanPaymentRequestStatus
func (x LoanPaymentRequestStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanPaymentRequestStatus
func (x *LoanPaymentRequestStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanPaymentRequestStatus(LoanPaymentRequestStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanPaymentRequestSubStatus in string format in DB
func (p LoanPaymentRequestSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanPaymentRequestSubStatus while reading from DB
func (p *LoanPaymentRequestSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanPaymentRequestSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanPaymentRequestSubStatus value: %s", val)
	}
	*p = LoanPaymentRequestSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanPaymentRequestSubStatus
func (x LoanPaymentRequestSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanPaymentRequestSubStatus
func (x *LoanPaymentRequestSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanPaymentRequestSubStatus(LoanPaymentRequestSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanInstallmentPayoutStatus in string format in DB
func (p LoanInstallmentPayoutStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanInstallmentPayoutStatus while reading from DB
func (p *LoanInstallmentPayoutStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanInstallmentPayoutStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanInstallmentPayoutStatus value: %s", val)
	}
	*p = LoanInstallmentPayoutStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanInstallmentPayoutStatus
func (x LoanInstallmentPayoutStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanInstallmentPayoutStatus
func (x *LoanInstallmentPayoutStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanInstallmentPayoutStatus(LoanInstallmentPayoutStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanInstallmentPayoutFieldMask in string format in DB
func (p LoanInstallmentPayoutFieldMask) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanInstallmentPayoutFieldMask while reading from DB
func (p *LoanInstallmentPayoutFieldMask) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanInstallmentPayoutFieldMask_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanInstallmentPayoutFieldMask value: %s", val)
	}
	*p = LoanInstallmentPayoutFieldMask(valInt)
	return nil
}

// Marshaler interface implementation for LoanInstallmentPayoutFieldMask
func (x LoanInstallmentPayoutFieldMask) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanInstallmentPayoutFieldMask
func (x *LoanInstallmentPayoutFieldMask) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanInstallmentPayoutFieldMask(LoanInstallmentPayoutFieldMask_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanApplicationStatus in string format in DB
func (p LoanApplicationStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanApplicationStatus while reading from DB
func (p *LoanApplicationStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanApplicationStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanApplicationStatus value: %s", val)
	}
	*p = LoanApplicationStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanApplicationStatus
func (x LoanApplicationStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanApplicationStatus
func (x *LoanApplicationStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanApplicationStatus(LoanApplicationStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanApplicationSubStatus in string format in DB
func (p LoanApplicationSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanApplicationSubStatus while reading from DB
func (p *LoanApplicationSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanApplicationSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanApplicationSubStatus value: %s", val)
	}
	*p = LoanApplicationSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanApplicationSubStatus
func (x LoanApplicationSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanApplicationSubStatus
func (x *LoanApplicationSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanApplicationSubStatus(LoanApplicationSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the GroupStage in string format in DB
func (p GroupStage) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing GroupStage while reading from DB
func (p *GroupStage) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := GroupStage_value[val]
	if !ok {
		return fmt.Errorf("unexpected GroupStage value: %s", val)
	}
	*p = GroupStage(valInt)
	return nil
}

// Marshaler interface implementation for GroupStage
func (x GroupStage) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for GroupStage
func (x *GroupStage) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = GroupStage(GroupStage_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanProgram in string format in DB
func (p LoanProgram) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanProgram while reading from DB
func (p *LoanProgram) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanProgram_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanProgram value: %s", val)
	}
	*p = LoanProgram(valInt)
	return nil
}

// Marshaler interface implementation for LoanProgram
func (x LoanProgram) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanProgram
func (x *LoanProgram) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanProgram(LoanProgram_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanApplicantStatus in string format in DB
func (p LoanApplicantStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanApplicantStatus while reading from DB
func (p *LoanApplicantStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanApplicantStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanApplicantStatus value: %s", val)
	}
	*p = LoanApplicantStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanApplicantStatus
func (x LoanApplicantStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanApplicantStatus
func (x *LoanApplicantStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanApplicantStatus(LoanApplicantStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanApplicantSubStatus in string format in DB
func (p LoanApplicantSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanApplicantSubStatus while reading from DB
func (p *LoanApplicantSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanApplicantSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanApplicantSubStatus value: %s", val)
	}
	*p = LoanApplicantSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanApplicantSubStatus
func (x LoanApplicantSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanApplicantSubStatus
func (x *LoanApplicantSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanApplicantSubStatus(LoanApplicantSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the AssetType in string format in DB
func (p AssetType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing AssetType while reading from DB
func (p *AssetType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := AssetType_value[val]
	if !ok {
		return fmt.Errorf("unexpected AssetType value: %s", val)
	}
	*p = AssetType(valInt)
	return nil
}

// Marshaler interface implementation for AssetType
func (x AssetType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for AssetType
func (x *AssetType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = AssetType(AssetType_value[val])
	return nil
}

// Valuer interface implementation for storing the FetchedAssetFieldMask in string format in DB
func (p FetchedAssetFieldMask) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing FetchedAssetFieldMask while reading from DB
func (p *FetchedAssetFieldMask) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := FetchedAssetFieldMask_value[val]
	if !ok {
		return fmt.Errorf("unexpected FetchedAssetFieldMask value: %s", val)
	}
	*p = FetchedAssetFieldMask(valInt)
	return nil
}

// Marshaler interface implementation for FetchedAssetFieldMask
func (x FetchedAssetFieldMask) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for FetchedAssetFieldMask
func (x *FetchedAssetFieldMask) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = FetchedAssetFieldMask(FetchedAssetFieldMask_value[val])
	return nil
}

// Valuer interface implementation for storing the IdfcCkycAddressPinCodeType in string format in DB
func (p IdfcCkycAddressPinCodeType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing IdfcCkycAddressPinCodeType while reading from DB
func (p *IdfcCkycAddressPinCodeType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := IdfcCkycAddressPinCodeType_value[val]
	if !ok {
		return fmt.Errorf("unexpected IdfcCkycAddressPinCodeType value: %s", val)
	}
	*p = IdfcCkycAddressPinCodeType(valInt)
	return nil
}

// Marshaler interface implementation for IdfcCkycAddressPinCodeType
func (x IdfcCkycAddressPinCodeType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for IdfcCkycAddressPinCodeType
func (x *IdfcCkycAddressPinCodeType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = IdfcCkycAddressPinCodeType(IdfcCkycAddressPinCodeType_value[val])
	return nil
}

// Valuer interface implementation for storing the OtpType in string format in DB
func (p OtpType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing OtpType while reading from DB
func (p *OtpType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := OtpType_value[val]
	if !ok {
		return fmt.Errorf("unexpected OtpType value: %s", val)
	}
	*p = OtpType(valInt)
	return nil
}

// Marshaler interface implementation for OtpType
func (x OtpType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for OtpType
func (x *OtpType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = OtpType(OtpType_value[val])
	return nil
}

// Valuer interface implementation for storing the OtpStatus in string format in DB
func (p OtpStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing OtpStatus while reading from DB
func (p *OtpStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := OtpStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected OtpStatus value: %s", val)
	}
	*p = OtpStatus(valInt)
	return nil
}

// Marshaler interface implementation for OtpStatus
func (x OtpStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for OtpStatus
func (x *OtpStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = OtpStatus(OtpStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the MandateRequestFieldMask in string format in DB
func (p MandateRequestFieldMask) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing MandateRequestFieldMask while reading from DB
func (p *MandateRequestFieldMask) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := MandateRequestFieldMask_value[val]
	if !ok {
		return fmt.Errorf("unexpected MandateRequestFieldMask value: %s", val)
	}
	*p = MandateRequestFieldMask(valInt)
	return nil
}

// Marshaler interface implementation for MandateRequestFieldMask
func (x MandateRequestFieldMask) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for MandateRequestFieldMask
func (x *MandateRequestFieldMask) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = MandateRequestFieldMask(MandateRequestFieldMask_value[val])
	return nil
}

// Valuer interface implementation for storing the MandateRequestStatus in string format in DB
func (p MandateRequestStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing MandateRequestStatus while reading from DB
func (p *MandateRequestStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := MandateRequestStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected MandateRequestStatus value: %s", val)
	}
	*p = MandateRequestStatus(valInt)
	return nil
}

// Marshaler interface implementation for MandateRequestStatus
func (x MandateRequestStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for MandateRequestStatus
func (x *MandateRequestStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = MandateRequestStatus(MandateRequestStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the DataRequirementType in string format in DB
func (p DataRequirementType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing DataRequirementType while reading from DB
func (p *DataRequirementType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := DataRequirementType_value[val]
	if !ok {
		return fmt.Errorf("unexpected DataRequirementType value: %s", val)
	}
	*p = DataRequirementType(valInt)
	return nil
}

// Marshaler interface implementation for DataRequirementType
func (x DataRequirementType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for DataRequirementType
func (x *DataRequirementType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = DataRequirementType(DataRequirementType_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanPaymentAccountType in string format in DB
func (p LoanPaymentAccountType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanPaymentAccountType while reading from DB
func (p *LoanPaymentAccountType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanPaymentAccountType_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanPaymentAccountType value: %s", val)
	}
	*p = LoanPaymentAccountType(valInt)
	return nil
}

// Marshaler interface implementation for LoanPaymentAccountType
func (x LoanPaymentAccountType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanPaymentAccountType
func (x *LoanPaymentAccountType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanPaymentAccountType(LoanPaymentAccountType_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanOfferType in string format in DB
func (p LoanOfferType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanOfferType while reading from DB
func (p *LoanOfferType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanOfferType_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanOfferType value: %s", val)
	}
	*p = LoanOfferType(valInt)
	return nil
}

// Marshaler interface implementation for LoanOfferType
func (x LoanOfferType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanOfferType
func (x *LoanOfferType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanOfferType(LoanOfferType_value[val])
	return nil
}

// Valuer interface implementation for storing the PreEligibilityOfferFieldMask in string format in DB
func (p PreEligibilityOfferFieldMask) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing PreEligibilityOfferFieldMask while reading from DB
func (p *PreEligibilityOfferFieldMask) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := PreEligibilityOfferFieldMask_value[val]
	if !ok {
		return fmt.Errorf("unexpected PreEligibilityOfferFieldMask value: %s", val)
	}
	*p = PreEligibilityOfferFieldMask(valInt)
	return nil
}

// Marshaler interface implementation for PreEligibilityOfferFieldMask
func (x PreEligibilityOfferFieldMask) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for PreEligibilityOfferFieldMask
func (x *PreEligibilityOfferFieldMask) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = PreEligibilityOfferFieldMask(PreEligibilityOfferFieldMask_value[val])
	return nil
}

// Valuer interface implementation for storing the Provenance in string format in DB
func (p Provenance) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Provenance while reading from DB
func (p *Provenance) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Provenance_value[val]
	if !ok {
		return fmt.Errorf("unexpected Provenance value: %s", val)
	}
	*p = Provenance(valInt)
	return nil
}

// Marshaler interface implementation for Provenance
func (x Provenance) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Provenance
func (x *Provenance) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Provenance(Provenance_value[val])
	return nil
}
