// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/preapprovedloan/loan_step_execution.proto

package preapprovedloan

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	enums "github.com/epifi/gamma/api/preapprovedloan/enums"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IncomeDataSource int32

const (
	IncomeDataSource_INCOME_DATA_SOURCE_UNSPECIFIED    IncomeDataSource = 0
	IncomeDataSource_INCOME_DATA_SOURCE_AA             IncomeDataSource = 1
	IncomeDataSource_INCOME_DATA_SOURCE_ITR_INTIMATION IncomeDataSource = 2
)

// Enum value maps for IncomeDataSource.
var (
	IncomeDataSource_name = map[int32]string{
		0: "INCOME_DATA_SOURCE_UNSPECIFIED",
		1: "INCOME_DATA_SOURCE_AA",
		2: "INCOME_DATA_SOURCE_ITR_INTIMATION",
	}
	IncomeDataSource_value = map[string]int32{
		"INCOME_DATA_SOURCE_UNSPECIFIED":    0,
		"INCOME_DATA_SOURCE_AA":             1,
		"INCOME_DATA_SOURCE_ITR_INTIMATION": 2,
	}
)

func (x IncomeDataSource) Enum() *IncomeDataSource {
	p := new(IncomeDataSource)
	*p = x
	return p
}

func (x IncomeDataSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IncomeDataSource) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_loan_step_execution_proto_enumTypes[0].Descriptor()
}

func (IncomeDataSource) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_loan_step_execution_proto_enumTypes[0]
}

func (x IncomeDataSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IncomeDataSource.Descriptor instead.
func (IncomeDataSource) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{0}
}

// FiAccountIneligibleForMandateReason denotes the list of reasons why user is not allowed to setup the mandate on
// fi-federal savings account. This info can be used to decide the appropriate flow/screens for the user
type MandateData_FiAccountIneligibleForMandateReason int32

const (
	MandateData_FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_UNSPECIFIED                        MandateData_FiAccountIneligibleForMandateReason = 0
	MandateData_FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_MIN_KYC_EXPIRY                     MandateData_FiAccountIneligibleForMandateReason = 1
	MandateData_FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_RE_KYC_DUE                         MandateData_FiAccountIneligibleForMandateReason = 2
	MandateData_FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_ACCOUNT_NOT_OPERATIONAL            MandateData_FiAccountIneligibleForMandateReason = 3
	MandateData_FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_DEFAULT_ACCOUNT_SETTING_NOT_NEEDED MandateData_FiAccountIneligibleForMandateReason = 4
)

// Enum value maps for MandateData_FiAccountIneligibleForMandateReason.
var (
	MandateData_FiAccountIneligibleForMandateReason_name = map[int32]string{
		0: "FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_UNSPECIFIED",
		1: "FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_MIN_KYC_EXPIRY",
		2: "FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_RE_KYC_DUE",
		3: "FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_ACCOUNT_NOT_OPERATIONAL",
		4: "FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_DEFAULT_ACCOUNT_SETTING_NOT_NEEDED",
	}
	MandateData_FiAccountIneligibleForMandateReason_value = map[string]int32{
		"FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_UNSPECIFIED":                        0,
		"FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_MIN_KYC_EXPIRY":                     1,
		"FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_RE_KYC_DUE":                         2,
		"FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_ACCOUNT_NOT_OPERATIONAL":            3,
		"FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_DEFAULT_ACCOUNT_SETTING_NOT_NEEDED": 4,
	}
)

func (x MandateData_FiAccountIneligibleForMandateReason) Enum() *MandateData_FiAccountIneligibleForMandateReason {
	p := new(MandateData_FiAccountIneligibleForMandateReason)
	*p = x
	return p
}

func (x MandateData_FiAccountIneligibleForMandateReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MandateData_FiAccountIneligibleForMandateReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_loan_step_execution_proto_enumTypes[1].Descriptor()
}

func (MandateData_FiAccountIneligibleForMandateReason) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_loan_step_execution_proto_enumTypes[1]
}

func (x MandateData_FiAccountIneligibleForMandateReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MandateData_FiAccountIneligibleForMandateReason.Descriptor instead.
func (MandateData_FiAccountIneligibleForMandateReason) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{18, 0}
}

type ItrIncomeInfo_ItrStatus int32

const (
	ItrIncomeInfo_ITR_STATUS_UNSPECIFIED  ItrIncomeInfo_ItrStatus = 0
	ItrIncomeInfo_ITR_STATUS_PENDING      ItrIncomeInfo_ItrStatus = 1
	ItrIncomeInfo_ITR_STATUS_FAILED       ItrIncomeInfo_ItrStatus = 2
	ItrIncomeInfo_ITR_STATUS_SUCCESS      ItrIncomeInfo_ItrStatus = 3
	ItrIncomeInfo_ITR_STATUS_NOT_FOUND    ItrIncomeInfo_ItrStatus = 4
	ItrIncomeInfo_ITR_STATUS_PAN_MISMATCH ItrIncomeInfo_ItrStatus = 5
	ItrIncomeInfo_ITR_STATUS_IN_PROGRESS  ItrIncomeInfo_ItrStatus = 6
)

// Enum value maps for ItrIncomeInfo_ItrStatus.
var (
	ItrIncomeInfo_ItrStatus_name = map[int32]string{
		0: "ITR_STATUS_UNSPECIFIED",
		1: "ITR_STATUS_PENDING",
		2: "ITR_STATUS_FAILED",
		3: "ITR_STATUS_SUCCESS",
		4: "ITR_STATUS_NOT_FOUND",
		5: "ITR_STATUS_PAN_MISMATCH",
		6: "ITR_STATUS_IN_PROGRESS",
	}
	ItrIncomeInfo_ItrStatus_value = map[string]int32{
		"ITR_STATUS_UNSPECIFIED":  0,
		"ITR_STATUS_PENDING":      1,
		"ITR_STATUS_FAILED":       2,
		"ITR_STATUS_SUCCESS":      3,
		"ITR_STATUS_NOT_FOUND":    4,
		"ITR_STATUS_PAN_MISMATCH": 5,
		"ITR_STATUS_IN_PROGRESS":  6,
	}
)

func (x ItrIncomeInfo_ItrStatus) Enum() *ItrIncomeInfo_ItrStatus {
	p := new(ItrIncomeInfo_ItrStatus)
	*p = x
	return p
}

func (x ItrIncomeInfo_ItrStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItrIncomeInfo_ItrStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_loan_step_execution_proto_enumTypes[2].Descriptor()
}

func (ItrIncomeInfo_ItrStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_loan_step_execution_proto_enumTypes[2]
}

func (x ItrIncomeInfo_ItrStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItrIncomeInfo_ItrStatus.Descriptor instead.
func (ItrIncomeInfo_ItrStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{24, 0}
}

type VendorPWAStagesStepData_PWAStage int32

const (
	VendorPWAStagesStepData_PWA_STAGE_UNSPECIFIED              VendorPWAStagesStepData_PWAStage = 0
	VendorPWAStagesStepData_PWA_STAGE_MANDATE                  VendorPWAStagesStepData_PWAStage = 1
	VendorPWAStagesStepData_PWA_STAGE_OFFER_SELECTION          VendorPWAStagesStepData_PWAStage = 2
	VendorPWAStagesStepData_PWA_STAGE_OFFER_REFRESH            VendorPWAStagesStepData_PWAStage = 3
	VendorPWAStagesStepData_PWA_STAGE_KYC                      VendorPWAStagesStepData_PWAStage = 4
	VendorPWAStagesStepData_PWA_STAGE_ADD_BANK_DETAILS         VendorPWAStagesStepData_PWAStage = 5
	VendorPWAStagesStepData_PWA_STAGE_COMPLIANCE_REVIEW        VendorPWAStagesStepData_PWAStage = 6
	VendorPWAStagesStepData_PWA_STAGE_INCOME_VERIFICATION      VendorPWAStagesStepData_PWAStage = 7
	VendorPWAStagesStepData_PWA_STAGE_AGREEMENT_SIGNING        VendorPWAStagesStepData_PWAStage = 8
	VendorPWAStagesStepData_PWA_STAGE_APPLICATION_SUBMISSION   VendorPWAStagesStepData_PWAStage = 9
	VendorPWAStagesStepData_PWA_STAGE_APPLICATION_RESUBMISSION VendorPWAStagesStepData_PWAStage = 10
	VendorPWAStagesStepData_PWA_STAGE_OFFER_RECEIVED           VendorPWAStagesStepData_PWAStage = 11
	VendorPWAStagesStepData_PWA_STAGE_OFFER_GENERATION         VendorPWAStagesStepData_PWAStage = 12
	VendorPWAStagesStepData_PWA_STAGE_USER_REGISTRATION        VendorPWAStagesStepData_PWAStage = 13
	VendorPWAStagesStepData_PWA_STAGE_DISBURSAL                VendorPWAStagesStepData_PWAStage = 14
)

// Enum value maps for VendorPWAStagesStepData_PWAStage.
var (
	VendorPWAStagesStepData_PWAStage_name = map[int32]string{
		0:  "PWA_STAGE_UNSPECIFIED",
		1:  "PWA_STAGE_MANDATE",
		2:  "PWA_STAGE_OFFER_SELECTION",
		3:  "PWA_STAGE_OFFER_REFRESH",
		4:  "PWA_STAGE_KYC",
		5:  "PWA_STAGE_ADD_BANK_DETAILS",
		6:  "PWA_STAGE_COMPLIANCE_REVIEW",
		7:  "PWA_STAGE_INCOME_VERIFICATION",
		8:  "PWA_STAGE_AGREEMENT_SIGNING",
		9:  "PWA_STAGE_APPLICATION_SUBMISSION",
		10: "PWA_STAGE_APPLICATION_RESUBMISSION",
		11: "PWA_STAGE_OFFER_RECEIVED",
		12: "PWA_STAGE_OFFER_GENERATION",
		13: "PWA_STAGE_USER_REGISTRATION",
		14: "PWA_STAGE_DISBURSAL",
	}
	VendorPWAStagesStepData_PWAStage_value = map[string]int32{
		"PWA_STAGE_UNSPECIFIED":              0,
		"PWA_STAGE_MANDATE":                  1,
		"PWA_STAGE_OFFER_SELECTION":          2,
		"PWA_STAGE_OFFER_REFRESH":            3,
		"PWA_STAGE_KYC":                      4,
		"PWA_STAGE_ADD_BANK_DETAILS":         5,
		"PWA_STAGE_COMPLIANCE_REVIEW":        6,
		"PWA_STAGE_INCOME_VERIFICATION":      7,
		"PWA_STAGE_AGREEMENT_SIGNING":        8,
		"PWA_STAGE_APPLICATION_SUBMISSION":   9,
		"PWA_STAGE_APPLICATION_RESUBMISSION": 10,
		"PWA_STAGE_OFFER_RECEIVED":           11,
		"PWA_STAGE_OFFER_GENERATION":         12,
		"PWA_STAGE_USER_REGISTRATION":        13,
		"PWA_STAGE_DISBURSAL":                14,
	}
)

func (x VendorPWAStagesStepData_PWAStage) Enum() *VendorPWAStagesStepData_PWAStage {
	p := new(VendorPWAStagesStepData_PWAStage)
	*p = x
	return p
}

func (x VendorPWAStagesStepData_PWAStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VendorPWAStagesStepData_PWAStage) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_loan_step_execution_proto_enumTypes[3].Descriptor()
}

func (VendorPWAStagesStepData_PWAStage) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_loan_step_execution_proto_enumTypes[3]
}

func (x VendorPWAStagesStepData_PWAStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VendorPWAStagesStepData_PWAStage.Descriptor instead.
func (VendorPWAStagesStepData_PWAStage) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{38, 0}
}

type VendorPWAStagesStepData_PWAStageStatus int32

const (
	VendorPWAStagesStepData_PWA_STAGE_STATUS_UNSPECIFIED VendorPWAStagesStepData_PWAStageStatus = 0
	VendorPWAStagesStepData_PWA_STAGE_STATUS_INITIATED   VendorPWAStagesStepData_PWAStageStatus = 1
	VendorPWAStagesStepData_PWA_STAGE_STATUS_COMPLETED   VendorPWAStagesStepData_PWAStageStatus = 2
	VendorPWAStagesStepData_PWA_STAGE_STATUS_FAILED      VendorPWAStagesStepData_PWAStageStatus = 3
)

// Enum value maps for VendorPWAStagesStepData_PWAStageStatus.
var (
	VendorPWAStagesStepData_PWAStageStatus_name = map[int32]string{
		0: "PWA_STAGE_STATUS_UNSPECIFIED",
		1: "PWA_STAGE_STATUS_INITIATED",
		2: "PWA_STAGE_STATUS_COMPLETED",
		3: "PWA_STAGE_STATUS_FAILED",
	}
	VendorPWAStagesStepData_PWAStageStatus_value = map[string]int32{
		"PWA_STAGE_STATUS_UNSPECIFIED": 0,
		"PWA_STAGE_STATUS_INITIATED":   1,
		"PWA_STAGE_STATUS_COMPLETED":   2,
		"PWA_STAGE_STATUS_FAILED":      3,
	}
)

func (x VendorPWAStagesStepData_PWAStageStatus) Enum() *VendorPWAStagesStepData_PWAStageStatus {
	p := new(VendorPWAStagesStepData_PWAStageStatus)
	*p = x
	return p
}

func (x VendorPWAStagesStepData_PWAStageStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VendorPWAStagesStepData_PWAStageStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_loan_step_execution_proto_enumTypes[4].Descriptor()
}

func (VendorPWAStagesStepData_PWAStageStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_loan_step_execution_proto_enumTypes[4]
}

func (x VendorPWAStagesStepData_PWAStageStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VendorPWAStagesStepData_PWAStageStatus.Descriptor instead.
func (VendorPWAStagesStepData_PWAStageStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{38, 1}
}

//go:generate gen_sql -types=LoanStepExecution,LoanStepExecutionDetails,ListOfString,ReviewerDetails
type LoanStepExecution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// references LoanRequest.Id
	RefId       string                     `protobuf:"bytes,3,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	Flow        LoanStepExecutionFlow      `protobuf:"varint,4,opt,name=flow,proto3,enum=preapprovedloan.LoanStepExecutionFlow" json:"flow,omitempty"`
	OrchId      string                     `protobuf:"bytes,5,opt,name=orch_id,json=orchId,proto3" json:"orch_id,omitempty"`
	StepName    LoanStepExecutionStepName  `protobuf:"varint,6,opt,name=step_name,json=stepName,proto3,enum=preapprovedloan.LoanStepExecutionStepName" json:"step_name,omitempty"`
	Details     *LoanStepExecutionDetails  `protobuf:"bytes,7,opt,name=details,proto3" json:"details,omitempty"`
	Status      LoanStepExecutionStatus    `protobuf:"varint,8,opt,name=status,proto3,enum=preapprovedloan.LoanStepExecutionStatus" json:"status,omitempty"`
	SubStatus   LoanStepExecutionSubStatus `protobuf:"varint,9,opt,name=sub_status,json=subStatus,proto3,enum=preapprovedloan.LoanStepExecutionSubStatus" json:"sub_status,omitempty"`
	StaledAt    *timestamppb.Timestamp     `protobuf:"bytes,10,opt,name=staled_at,json=staledAt,proto3" json:"staled_at,omitempty"`
	CompletedAt *timestamppb.Timestamp     `protobuf:"bytes,11,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	CreatedAt   *timestamppb.Timestamp     `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   *timestamppb.Timestamp     `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt   *timestamppb.Timestamp     `protobuf:"bytes,14,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	GroupStage  GroupStage                 `protobuf:"varint,15,opt,name=group_stage,json=groupStage,proto3,enum=preapprovedloan.GroupStage" json:"group_stage,omitempty"`
}

func (x *LoanStepExecution) Reset() {
	*x = LoanStepExecution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanStepExecution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanStepExecution) ProtoMessage() {}

func (x *LoanStepExecution) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanStepExecution.ProtoReflect.Descriptor instead.
func (*LoanStepExecution) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{0}
}

func (x *LoanStepExecution) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LoanStepExecution) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *LoanStepExecution) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

func (x *LoanStepExecution) GetFlow() LoanStepExecutionFlow {
	if x != nil {
		return x.Flow
	}
	return LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_UNSPECIFIED
}

func (x *LoanStepExecution) GetOrchId() string {
	if x != nil {
		return x.OrchId
	}
	return ""
}

func (x *LoanStepExecution) GetStepName() LoanStepExecutionStepName {
	if x != nil {
		return x.StepName
	}
	return LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_UNSPECIFIED
}

func (x *LoanStepExecution) GetDetails() *LoanStepExecutionDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *LoanStepExecution) GetStatus() LoanStepExecutionStatus {
	if x != nil {
		return x.Status
	}
	return LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED
}

func (x *LoanStepExecution) GetSubStatus() LoanStepExecutionSubStatus {
	if x != nil {
		return x.SubStatus
	}
	return LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED
}

func (x *LoanStepExecution) GetStaledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StaledAt
	}
	return nil
}

func (x *LoanStepExecution) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *LoanStepExecution) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LoanStepExecution) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *LoanStepExecution) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *LoanStepExecution) GetGroupStage() GroupStage {
	if x != nil {
		return x.GroupStage
	}
	return GroupStage_GROUP_STAGE_UNSPECIFIED
}

type LivenessStepData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttemptId             string                   `protobuf:"bytes,1,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	Otp                   string                   `protobuf:"bytes,2,opt,name=otp,proto3" json:"otp,omitempty"`
	NotificationTypeIdMap map[string]*ListOfString `protobuf:"bytes,3,rep,name=notification_type_id_map,json=notificationTypeIdMap,proto3" json:"notification_type_id_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *LivenessStepData) Reset() {
	*x = LivenessStepData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessStepData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessStepData) ProtoMessage() {}

func (x *LivenessStepData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessStepData.ProtoReflect.Descriptor instead.
func (*LivenessStepData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{1}
}

func (x *LivenessStepData) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *LivenessStepData) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *LivenessStepData) GetNotificationTypeIdMap() map[string]*ListOfString {
	if x != nil {
		return x.NotificationTypeIdMap
	}
	return nil
}

type FaceMatchStepData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttemptId string `protobuf:"bytes,1,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
}

func (x *FaceMatchStepData) Reset() {
	*x = FaceMatchStepData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceMatchStepData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceMatchStepData) ProtoMessage() {}

func (x *FaceMatchStepData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceMatchStepData.ProtoReflect.Descriptor instead.
func (*FaceMatchStepData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{2}
}

func (x *FaceMatchStepData) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

type ESignStepData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pre-sign-url for storing the e-sign agreement if there is only one document to be signed
	SignUrl               string                   `protobuf:"bytes,1,opt,name=sign_url,json=signUrl,proto3" json:"sign_url,omitempty"`
	ExpiryAt              *timestamppb.Timestamp   `protobuf:"bytes,2,opt,name=expiry_at,json=expiryAt,proto3" json:"expiry_at,omitempty"`
	NotificationTypeIdMap map[string]*ListOfString `protobuf:"bytes,3,rep,name=notification_type_id_map,json=notificationTypeIdMap,proto3" json:"notification_type_id_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	OtpInfo               *OtpInfo                 `protobuf:"bytes,4,opt,name=otp_info,json=otpInfo,proto3" json:"otp_info,omitempty"`
	DocumentId            string                   `protobuf:"bytes,5,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`
	// path of e-sign agreement pdf stored in s3
	AwsDestinationPath string `protobuf:"bytes,6,opt,name=aws_destination_path,json=awsDestinationPath,proto3" json:"aws_destination_path,omitempty"`
	// Used for storing kfs document details
	KfsDocument *LoanDocument `protobuf:"bytes,7,opt,name=kfsDocument,proto3" json:"kfsDocument,omitempty"`
	// Used for storing loan agreement document details
	LoanAgreementDocument *LoanDocument `protobuf:"bytes,8,opt,name=loanAgreementDocument,proto3" json:"loanAgreementDocument,omitempty"`
	// The time till which ROI can be modified
	RoiModificationDeadline *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=roi_modification_deadline,json=roiModificationDeadline,proto3" json:"roi_modification_deadline,omitempty"`
}

func (x *ESignStepData) Reset() {
	*x = ESignStepData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ESignStepData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ESignStepData) ProtoMessage() {}

func (x *ESignStepData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ESignStepData.ProtoReflect.Descriptor instead.
func (*ESignStepData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{3}
}

func (x *ESignStepData) GetSignUrl() string {
	if x != nil {
		return x.SignUrl
	}
	return ""
}

func (x *ESignStepData) GetExpiryAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryAt
	}
	return nil
}

func (x *ESignStepData) GetNotificationTypeIdMap() map[string]*ListOfString {
	if x != nil {
		return x.NotificationTypeIdMap
	}
	return nil
}

func (x *ESignStepData) GetOtpInfo() *OtpInfo {
	if x != nil {
		return x.OtpInfo
	}
	return nil
}

func (x *ESignStepData) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *ESignStepData) GetAwsDestinationPath() string {
	if x != nil {
		return x.AwsDestinationPath
	}
	return ""
}

func (x *ESignStepData) GetKfsDocument() *LoanDocument {
	if x != nil {
		return x.KfsDocument
	}
	return nil
}

func (x *ESignStepData) GetLoanAgreementDocument() *LoanDocument {
	if x != nil {
		return x.LoanAgreementDocument
	}
	return nil
}

func (x *ESignStepData) GetRoiModificationDeadline() *timestamppb.Timestamp {
	if x != nil {
		return x.RoiModificationDeadline
	}
	return nil
}

type LoanDocument struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// store pre-sign url in s3
	SignUrl string `protobuf:"bytes,1,opt,name=sign_url,json=signUrl,proto3" json:"sign_url,omitempty"`
	// path of e-sign agreement pdf stored in s3
	AwsDestinationPath string `protobuf:"bytes,2,opt,name=aws_destination_path,json=awsDestinationPath,proto3" json:"aws_destination_path,omitempty"`
	// expiry fo pre-sign url
	ExpiryAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=expiry_at,json=expiryAt,proto3" json:"expiry_at,omitempty"`
	// Path inside a S3 bucket at which the lender-signed document is stored
	SignedDocS3Path string `protobuf:"bytes,4,opt,name=signed_doc_s3_path,json=signedDocS3Path,proto3" json:"signed_doc_s3_path,omitempty"`
}

func (x *LoanDocument) Reset() {
	*x = LoanDocument{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanDocument) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanDocument) ProtoMessage() {}

func (x *LoanDocument) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanDocument.ProtoReflect.Descriptor instead.
func (*LoanDocument) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{4}
}

func (x *LoanDocument) GetSignUrl() string {
	if x != nil {
		return x.SignUrl
	}
	return ""
}

func (x *LoanDocument) GetAwsDestinationPath() string {
	if x != nil {
		return x.AwsDestinationPath
	}
	return ""
}

func (x *LoanDocument) GetExpiryAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryAt
	}
	return nil
}

func (x *LoanDocument) GetSignedDocS3Path() string {
	if x != nil {
		return x.SignedDocS3Path
	}
	return ""
}

type OtpInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Otp           string `protobuf:"bytes,1,opt,name=otp,proto3" json:"otp,omitempty"`
	MaxAttempts   int32  `protobuf:"varint,2,opt,name=max_attempts,json=maxAttempts,proto3" json:"max_attempts,omitempty"`
	AttemptsCount int32  `protobuf:"varint,3,opt,name=attempts_count,json=attemptsCount,proto3" json:"attempts_count,omitempty"`
	// In case of failure, last entered otp becomes null
	// In case of success, update last entered otp
	LastEnteredOtp string `protobuf:"bytes,4,opt,name=last_entered_otp,json=lastEnteredOtp,proto3" json:"last_entered_otp,omitempty"`
	// For OTPs generated internally, this will store the latest auth token corresponding to which OTP needs to be verified.
	Token string `protobuf:"bytes,5,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *OtpInfo) Reset() {
	*x = OtpInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OtpInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OtpInfo) ProtoMessage() {}

func (x *OtpInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OtpInfo.ProtoReflect.Descriptor instead.
func (*OtpInfo) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{5}
}

func (x *OtpInfo) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *OtpInfo) GetMaxAttempts() int32 {
	if x != nil {
		return x.MaxAttempts
	}
	return 0
}

func (x *OtpInfo) GetAttemptsCount() int32 {
	if x != nil {
		return x.AttemptsCount
	}
	return 0
}

func (x *OtpInfo) GetLastEnteredOtp() string {
	if x != nil {
		return x.LastEnteredOtp
	}
	return ""
}

func (x *OtpInfo) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type VkycStepData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/preapprovedloan/loan_step_execution.proto.
	NotificationId        string                   `protobuf:"bytes,1,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	NotificationTypeIdMap map[string]*ListOfString `protobuf:"bytes,2,rep,name=notification_type_id_map,json=notificationTypeIdMap,proto3" json:"notification_type_id_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ApplicationId         string                   `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	CallId                string                   `protobuf:"bytes,4,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
}

func (x *VkycStepData) Reset() {
	*x = VkycStepData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VkycStepData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VkycStepData) ProtoMessage() {}

func (x *VkycStepData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VkycStepData.ProtoReflect.Descriptor instead.
func (*VkycStepData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{6}
}

// Deprecated: Marked as deprecated in api/preapprovedloan/loan_step_execution.proto.
func (x *VkycStepData) GetNotificationId() string {
	if x != nil {
		return x.NotificationId
	}
	return ""
}

func (x *VkycStepData) GetNotificationTypeIdMap() map[string]*ListOfString {
	if x != nil {
		return x.NotificationTypeIdMap
	}
	return nil
}

func (x *VkycStepData) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *VkycStepData) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

// used to store data when kyc is done by vendor
type KycStepData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vendor's kyc url
	KycUrl string `protobuf:"bytes,1,opt,name=kyc_url,json=kycUrl,proto3" json:"kyc_url,omitempty"`
	// unique id to track kyc on vendor's end
	KycTrackingId string `protobuf:"bytes,2,opt,name=kyc_tracking_id,json=kycTrackingId,proto3" json:"kyc_tracking_id,omitempty"`
	// denotes the approximate time at which we received the KYC URL from vendor
	UrlGeneratedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=url_generated_at,json=urlGeneratedAt,proto3" json:"url_generated_at,omitempty"`
}

func (x *KycStepData) Reset() {
	*x = KycStepData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KycStepData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KycStepData) ProtoMessage() {}

func (x *KycStepData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KycStepData.ProtoReflect.Descriptor instead.
func (*KycStepData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{7}
}

func (x *KycStepData) GetKycUrl() string {
	if x != nil {
		return x.KycUrl
	}
	return ""
}

func (x *KycStepData) GetKycTrackingId() string {
	if x != nil {
		return x.KycTrackingId
	}
	return ""
}

func (x *KycStepData) GetUrlGeneratedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UrlGeneratedAt
	}
	return nil
}

// used when kyc is done on LSP side
type CkycStepData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CkycImagePath         string                         `protobuf:"bytes,1,opt,name=ckyc_image_path,json=ckycImagePath,proto3" json:"ckyc_image_path,omitempty"`
	EmploymentType        typesv2.EmploymentType         `protobuf:"varint,2,opt,name=employment_type,json=employmentType,proto3,enum=api.typesv2.EmploymentType" json:"employment_type,omitempty"`
	CkycId                string                         `protobuf:"bytes,3,opt,name=ckyc_id,json=ckycId,proto3" json:"ckyc_id,omitempty"`
	PositiveConfirmation  CkycPositiveConfirmationStatus `protobuf:"varint,4,opt,name=positive_confirmation,json=positiveConfirmation,proto3,enum=preapprovedloan.CkycPositiveConfirmationStatus" json:"positive_confirmation,omitempty"`
	PAN                   string                         `protobuf:"bytes,5,opt,name=PAN,proto3" json:"PAN,omitempty"`
	Name                  *common.Name                   `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	Gender                typesv2.Gender                 `protobuf:"varint,7,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	Dob                   *date.Date                     `protobuf:"bytes,8,opt,name=dob,proto3" json:"dob,omitempty"`
	PermanentAddress      *typesv2.PostalAddress         `protobuf:"bytes,9,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	CorrespondenceAddress *typesv2.PostalAddress         `protobuf:"bytes,10,opt,name=correspondence_address,json=correspondenceAddress,proto3" json:"correspondence_address,omitempty"`
	FathersName           *common.Name                   `protobuf:"bytes,11,opt,name=fathers_name,json=fathersName,proto3" json:"fathers_name,omitempty"`
	MothersName           *common.Name                   `protobuf:"bytes,12,opt,name=mothers_name,json=mothersName,proto3" json:"mothers_name,omitempty"`
	VendorKycId           string                         `protobuf:"bytes,15,opt,name=vendor_kyc_id,json=vendorKycId,proto3" json:"vendor_kyc_id,omitempty"`
	// this address comes from CKYC from one of the ovd documents list like Aadhaar, Voter Id, DL, Passport etc.
	KycAddress *typesv2.PostalAddress `protobuf:"bytes,17,opt,name=kyc_address,json=kycAddress,proto3" json:"kyc_address,omitempty"`
	// properties applicable only for specific vendors
	//
	// Types that are assignable to VendorSpecificDetails:
	//
	//	*CkycStepData_Idfc
	//	*CkycStepData_Abfl
	//	*CkycStepData_SgData
	VendorSpecificDetails isCkycStepData_VendorSpecificDetails `protobuf_oneof:"vendor_specific_details"`
}

func (x *CkycStepData) Reset() {
	*x = CkycStepData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CkycStepData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CkycStepData) ProtoMessage() {}

func (x *CkycStepData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CkycStepData.ProtoReflect.Descriptor instead.
func (*CkycStepData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{8}
}

func (x *CkycStepData) GetCkycImagePath() string {
	if x != nil {
		return x.CkycImagePath
	}
	return ""
}

func (x *CkycStepData) GetEmploymentType() typesv2.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return typesv2.EmploymentType(0)
}

func (x *CkycStepData) GetCkycId() string {
	if x != nil {
		return x.CkycId
	}
	return ""
}

func (x *CkycStepData) GetPositiveConfirmation() CkycPositiveConfirmationStatus {
	if x != nil {
		return x.PositiveConfirmation
	}
	return CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_UNSPECIFIED
}

func (x *CkycStepData) GetPAN() string {
	if x != nil {
		return x.PAN
	}
	return ""
}

func (x *CkycStepData) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CkycStepData) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *CkycStepData) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *CkycStepData) GetPermanentAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.PermanentAddress
	}
	return nil
}

func (x *CkycStepData) GetCorrespondenceAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.CorrespondenceAddress
	}
	return nil
}

func (x *CkycStepData) GetFathersName() *common.Name {
	if x != nil {
		return x.FathersName
	}
	return nil
}

func (x *CkycStepData) GetMothersName() *common.Name {
	if x != nil {
		return x.MothersName
	}
	return nil
}

func (x *CkycStepData) GetVendorKycId() string {
	if x != nil {
		return x.VendorKycId
	}
	return ""
}

func (x *CkycStepData) GetKycAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.KycAddress
	}
	return nil
}

func (m *CkycStepData) GetVendorSpecificDetails() isCkycStepData_VendorSpecificDetails {
	if m != nil {
		return m.VendorSpecificDetails
	}
	return nil
}

func (x *CkycStepData) GetIdfc() *IdfcCkycStepData {
	if x, ok := x.GetVendorSpecificDetails().(*CkycStepData_Idfc); ok {
		return x.Idfc
	}
	return nil
}

func (x *CkycStepData) GetAbfl() *AbflCkycStepData {
	if x, ok := x.GetVendorSpecificDetails().(*CkycStepData_Abfl); ok {
		return x.Abfl
	}
	return nil
}

func (x *CkycStepData) GetSgData() *SgCkycStepData {
	if x, ok := x.GetVendorSpecificDetails().(*CkycStepData_SgData); ok {
		return x.SgData
	}
	return nil
}

type isCkycStepData_VendorSpecificDetails interface {
	isCkycStepData_VendorSpecificDetails()
}

type CkycStepData_Idfc struct {
	// applicable only for Vendor_IDFC
	Idfc *IdfcCkycStepData `protobuf:"bytes,13,opt,name=idfc,proto3,oneof"`
}

type CkycStepData_Abfl struct {
	// applicable only for Vendor_ABFL
	Abfl *AbflCkycStepData `protobuf:"bytes,14,opt,name=abfl,proto3,oneof"`
}

type CkycStepData_SgData struct {
	// applicable only for Vendor_STOCK_GUARDIAN_LSP
	SgData *SgCkycStepData `protobuf:"bytes,16,opt,name=sg_data,json=sgData,proto3,oneof"`
}

func (*CkycStepData_Idfc) isCkycStepData_VendorSpecificDetails() {}

func (*CkycStepData_Abfl) isCkycStepData_VendorSpecificDetails() {}

func (*CkycStepData_SgData) isCkycStepData_VendorSpecificDetails() {}

type AbflMandateData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RpCustId string `protobuf:"bytes,1,opt,name=rp_cust_id,json=rpCustId,proto3" json:"rp_cust_id,omitempty"`
	ShortUrl string `protobuf:"bytes,2,opt,name=short_url,json=shortUrl,proto3" json:"short_url,omitempty"`
}

func (x *AbflMandateData) Reset() {
	*x = AbflMandateData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AbflMandateData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AbflMandateData) ProtoMessage() {}

func (x *AbflMandateData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AbflMandateData.ProtoReflect.Descriptor instead.
func (*AbflMandateData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{9}
}

func (x *AbflMandateData) GetRpCustId() string {
	if x != nil {
		return x.RpCustId
	}
	return ""
}

func (x *AbflMandateData) GetShortUrl() string {
	if x != nil {
		return x.ShortUrl
	}
	return ""
}

type SgMandateData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrchId string `protobuf:"bytes,1,opt,name=orch_id,json=orchId,proto3" json:"orch_id,omitempty"`
}

func (x *SgMandateData) Reset() {
	*x = SgMandateData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SgMandateData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SgMandateData) ProtoMessage() {}

func (x *SgMandateData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SgMandateData.ProtoReflect.Descriptor instead.
func (*SgMandateData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{10}
}

func (x *SgMandateData) GetOrchId() string {
	if x != nil {
		return x.OrchId
	}
	return ""
}

type FedMandateData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MandateAttemptOrchIdList []string `protobuf:"bytes,1,rep,name=mandate_attempt_orch_id_list,json=mandateAttemptOrchIdList,proto3" json:"mandate_attempt_orch_id_list,omitempty"`
}

func (x *FedMandateData) Reset() {
	*x = FedMandateData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FedMandateData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FedMandateData) ProtoMessage() {}

func (x *FedMandateData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FedMandateData.ProtoReflect.Descriptor instead.
func (*FedMandateData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{11}
}

func (x *FedMandateData) GetMandateAttemptOrchIdList() []string {
	if x != nil {
		return x.MandateAttemptOrchIdList
	}
	return nil
}

type LLMandateData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MandateId      string `protobuf:"bytes,1,opt,name=mandate_id,json=mandateId,proto3" json:"mandate_id,omitempty"`
	MandateAttempt int32  `protobuf:"varint,2,opt,name=mandate_attempt,json=mandateAttempt,proto3" json:"mandate_attempt,omitempty"`
	// denotes the latest time at which mandate was attempted by the user
	LatestMandateAttemptTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=latest_mandate_attempt_timestamp,json=latestMandateAttemptTimestamp,proto3" json:"latest_mandate_attempt_timestamp,omitempty"`
}

func (x *LLMandateData) Reset() {
	*x = LLMandateData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LLMandateData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LLMandateData) ProtoMessage() {}

func (x *LLMandateData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LLMandateData.ProtoReflect.Descriptor instead.
func (*LLMandateData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{12}
}

func (x *LLMandateData) GetMandateId() string {
	if x != nil {
		return x.MandateId
	}
	return ""
}

func (x *LLMandateData) GetMandateAttempt() int32 {
	if x != nil {
		return x.MandateAttempt
	}
	return 0
}

func (x *LLMandateData) GetLatestMandateAttemptTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.LatestMandateAttemptTimestamp
	}
	return nil
}

type IdfcCkycStepData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AddressPinCodeType IdfcCkycAddressPinCodeType `protobuf:"varint,1,opt,name=address_pin_code_type,json=addressPinCodeType,proto3,enum=preapprovedloan.IdfcCkycAddressPinCodeType" json:"address_pin_code_type,omitempty"`
}

func (x *IdfcCkycStepData) Reset() {
	*x = IdfcCkycStepData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdfcCkycStepData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdfcCkycStepData) ProtoMessage() {}

func (x *IdfcCkycStepData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdfcCkycStepData.ProtoReflect.Descriptor instead.
func (*IdfcCkycStepData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{13}
}

func (x *IdfcCkycStepData) GetAddressPinCodeType() IdfcCkycAddressPinCodeType {
	if x != nil {
		return x.AddressPinCodeType
	}
	return IdfcCkycAddressPinCodeType_IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_UNSPECIFIED
}

type SgCkycStepData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsKycAlreadyDone bool `protobuf:"varint,1,opt,name=is_kyc_already_done,json=isKycAlreadyDone,proto3" json:"is_kyc_already_done,omitempty"`
	// kyc_type denotes the type of kyc done by the user
	KycType KycType `protobuf:"varint,2,opt,name=kyc_type,json=kycType,proto3,enum=preapprovedloan.KycType" json:"kyc_type,omitempty"`
}

func (x *SgCkycStepData) Reset() {
	*x = SgCkycStepData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SgCkycStepData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SgCkycStepData) ProtoMessage() {}

func (x *SgCkycStepData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SgCkycStepData.ProtoReflect.Descriptor instead.
func (*SgCkycStepData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{14}
}

func (x *SgCkycStepData) GetIsKycAlreadyDone() bool {
	if x != nil {
		return x.IsKycAlreadyDone
	}
	return false
}

func (x *SgCkycStepData) GetKycType() KycType {
	if x != nil {
		return x.KycType
	}
	return KycType_KYC_TYPE_UNSPECIFIED
}

type AbflCkycStepData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId                 string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	AadhaarBase64Image        string                 `protobuf:"bytes,2,opt,name=aadhaar_base64_image,json=aadhaarBase64Image,proto3" json:"aadhaar_base64_image,omitempty"`
	PhotographBase64Image     string                 `protobuf:"bytes,3,opt,name=photograph_base64_image,json=photographBase64Image,proto3" json:"photograph_base64_image,omitempty"`
	EkycAuthBase64Image       string                 `protobuf:"bytes,4,opt,name=ekyc_auth_base64_image,json=ekycAuthBase64Image,proto3" json:"ekyc_auth_base64_image,omitempty"`
	PanBase64Image            string                 `protobuf:"bytes,5,opt,name=pan_base64_image,json=panBase64Image,proto3" json:"pan_base64_image,omitempty"`
	DrivingLicenceBase64Image string                 `protobuf:"bytes,6,opt,name=driving_licence_base64_image,json=drivingLicenceBase64Image,proto3" json:"driving_licence_base64_image,omitempty"`
	SignatureBase64Image      string                 `protobuf:"bytes,7,opt,name=signature_base64_image,json=signatureBase64Image,proto3" json:"signature_base64_image,omitempty"`
	VoterIdBase64Image        string                 `protobuf:"bytes,8,opt,name=voter_id_base64_image,json=voterIdBase64Image,proto3" json:"voter_id_base64_image,omitempty"`
	PassportBase64Image       string                 `protobuf:"bytes,9,opt,name=passport_base64_image,json=passportBase64Image,proto3" json:"passport_base64_image,omitempty"`
	PermanentAddress          *typesv2.PostalAddress `protobuf:"bytes,10,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	CorrespondingAddress      *typesv2.PostalAddress `protobuf:"bytes,11,opt,name=corresponding_address,json=correspondingAddress,proto3" json:"corresponding_address,omitempty"`
	Pan                       string                 `protobuf:"bytes,12,opt,name=pan,proto3" json:"pan,omitempty"`
	FullName                  *common.Name           `protobuf:"bytes,13,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	Dob                       *date.Date             `protobuf:"bytes,14,opt,name=dob,proto3" json:"dob,omitempty"`
	CkycId                    string                 `protobuf:"bytes,15,opt,name=ckyc_id,json=ckycId,proto3" json:"ckyc_id,omitempty"`
}

func (x *AbflCkycStepData) Reset() {
	*x = AbflCkycStepData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AbflCkycStepData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AbflCkycStepData) ProtoMessage() {}

func (x *AbflCkycStepData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AbflCkycStepData.ProtoReflect.Descriptor instead.
func (*AbflCkycStepData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{15}
}

func (x *AbflCkycStepData) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AbflCkycStepData) GetAadhaarBase64Image() string {
	if x != nil {
		return x.AadhaarBase64Image
	}
	return ""
}

func (x *AbflCkycStepData) GetPhotographBase64Image() string {
	if x != nil {
		return x.PhotographBase64Image
	}
	return ""
}

func (x *AbflCkycStepData) GetEkycAuthBase64Image() string {
	if x != nil {
		return x.EkycAuthBase64Image
	}
	return ""
}

func (x *AbflCkycStepData) GetPanBase64Image() string {
	if x != nil {
		return x.PanBase64Image
	}
	return ""
}

func (x *AbflCkycStepData) GetDrivingLicenceBase64Image() string {
	if x != nil {
		return x.DrivingLicenceBase64Image
	}
	return ""
}

func (x *AbflCkycStepData) GetSignatureBase64Image() string {
	if x != nil {
		return x.SignatureBase64Image
	}
	return ""
}

func (x *AbflCkycStepData) GetVoterIdBase64Image() string {
	if x != nil {
		return x.VoterIdBase64Image
	}
	return ""
}

func (x *AbflCkycStepData) GetPassportBase64Image() string {
	if x != nil {
		return x.PassportBase64Image
	}
	return ""
}

func (x *AbflCkycStepData) GetPermanentAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.PermanentAddress
	}
	return nil
}

func (x *AbflCkycStepData) GetCorrespondingAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.CorrespondingAddress
	}
	return nil
}

func (x *AbflCkycStepData) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *AbflCkycStepData) GetFullName() *common.Name {
	if x != nil {
		return x.FullName
	}
	return nil
}

func (x *AbflCkycStepData) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *AbflCkycStepData) GetCkycId() string {
	if x != nil {
		return x.CkycId
	}
	return ""
}

type ManualReviewStepData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/preapprovedloan/loan_step_execution.proto.
	NotificationId        string                                `protobuf:"bytes,1,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	NotificationTypeIdMap map[string]*ListOfString              `protobuf:"bytes,2,rep,name=notification_type_id_map,json=notificationTypeIdMap,proto3" json:"notification_type_id_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Reason                string                                `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	ReviewerDetails       *ManualReviewStepData_ReviewerDetails `protobuf:"bytes,4,opt,name=reviewer_details,json=reviewerDetails,proto3" json:"reviewer_details,omitempty"`
}

func (x *ManualReviewStepData) Reset() {
	*x = ManualReviewStepData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualReviewStepData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualReviewStepData) ProtoMessage() {}

func (x *ManualReviewStepData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualReviewStepData.ProtoReflect.Descriptor instead.
func (*ManualReviewStepData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{16}
}

// Deprecated: Marked as deprecated in api/preapprovedloan/loan_step_execution.proto.
func (x *ManualReviewStepData) GetNotificationId() string {
	if x != nil {
		return x.NotificationId
	}
	return ""
}

func (x *ManualReviewStepData) GetNotificationTypeIdMap() map[string]*ListOfString {
	if x != nil {
		return x.NotificationTypeIdMap
	}
	return nil
}

func (x *ManualReviewStepData) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ManualReviewStepData) GetReviewerDetails() *ManualReviewStepData_ReviewerDetails {
	if x != nil {
		return x.ReviewerDetails
	}
	return nil
}

type OnboardingData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AddressDetails    *OnboardingData_AddressDetails    `protobuf:"bytes,1,opt,name=address_details,json=addressDetails,proto3" json:"address_details,omitempty"`
	EmploymentDetails *OnboardingData_EmploymentDetails `protobuf:"bytes,2,opt,name=employment_details,json=employmentDetails,proto3" json:"employment_details,omitempty"`
	BankingDetails    *OnboardingData_BankingDetails    `protobuf:"bytes,3,opt,name=banking_details,json=bankingDetails,proto3" json:"banking_details,omitempty"`
}

func (x *OnboardingData) Reset() {
	*x = OnboardingData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnboardingData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingData) ProtoMessage() {}

func (x *OnboardingData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingData.ProtoReflect.Descriptor instead.
func (*OnboardingData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{17}
}

func (x *OnboardingData) GetAddressDetails() *OnboardingData_AddressDetails {
	if x != nil {
		return x.AddressDetails
	}
	return nil
}

func (x *OnboardingData) GetEmploymentDetails() *OnboardingData_EmploymentDetails {
	if x != nil {
		return x.EmploymentDetails
	}
	return nil
}

func (x *OnboardingData) GetBankingDetails() *OnboardingData_BankingDetails {
	if x != nil {
		return x.BankingDetails
	}
	return nil
}

type MandateData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url                string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	RecurringPaymentId string `protobuf:"bytes,2,opt,name=recurring_payment_id,json=recurringPaymentId,proto3" json:"recurring_payment_id,omitempty"`
	MerchantTxnId      string `protobuf:"bytes,3,opt,name=merchant_txn_id,json=merchantTxnId,proto3" json:"merchant_txn_id,omitempty"`
	// maximum amount we can deduct over one single transaction of repayment
	MaxTxnAmount *money.Money `protobuf:"bytes,4,opt,name=max_txn_amount,json=maxTxnAmount,proto3" json:"max_txn_amount,omitempty"`
	// denotes the time before which user needs to complete mandate
	MandateLinkExpiry *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=mandate_link_expiry,json=mandateLinkExpiry,proto3" json:"mandate_link_expiry,omitempty"`
	// Types that are assignable to VendorSpecificDetails:
	//
	//	*MandateData_Abfl
	//	*MandateData_LlMandateData
	//	*MandateData_SgMandateData
	//	*MandateData_FedMandateData
	VendorSpecificDetails isMandateData_VendorSpecificDetails `protobuf_oneof:"vendor_specific_details"`
	BankingDetails        *MandateData_BankingDetails         `protobuf:"bytes,6,opt,name=banking_details,json=bankingDetails,proto3" json:"banking_details,omitempty"`
	// denotes whether a user is not eligible to set up mandate with fi-federal bank account
	MandateWithFiAccountNotAllowed      bool                                            `protobuf:"varint,7,opt,name=mandate_with_fi_account_not_allowed,json=mandateWithFiAccountNotAllowed,proto3" json:"mandate_with_fi_account_not_allowed,omitempty"`
	FiAccountIneligibleForMandateReason MandateData_FiAccountIneligibleForMandateReason `protobuf:"varint,8,opt,name=fi_account_ineligible_for_mandate_reason,json=fiAccountIneligibleForMandateReason,proto3,enum=preapprovedloan.MandateData_FiAccountIneligibleForMandateReason" json:"fi_account_ineligible_for_mandate_reason,omitempty"`
	// Denotes the approximate time at which we received the URL to setup auto-repayment mandate for a loan
	UrlGeneratedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=url_generated_at,json=urlGeneratedAt,proto3" json:"url_generated_at,omitempty"`
	MandateType    enums.MandateType      `protobuf:"varint,10,opt,name=mandate_type,json=mandateType,proto3,enum=preapprovedloan.enums.MandateType" json:"mandate_type,omitempty"`
}

func (x *MandateData) Reset() {
	*x = MandateData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MandateData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MandateData) ProtoMessage() {}

func (x *MandateData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MandateData.ProtoReflect.Descriptor instead.
func (*MandateData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{18}
}

func (x *MandateData) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *MandateData) GetRecurringPaymentId() string {
	if x != nil {
		return x.RecurringPaymentId
	}
	return ""
}

func (x *MandateData) GetMerchantTxnId() string {
	if x != nil {
		return x.MerchantTxnId
	}
	return ""
}

func (x *MandateData) GetMaxTxnAmount() *money.Money {
	if x != nil {
		return x.MaxTxnAmount
	}
	return nil
}

func (x *MandateData) GetMandateLinkExpiry() *timestamppb.Timestamp {
	if x != nil {
		return x.MandateLinkExpiry
	}
	return nil
}

func (m *MandateData) GetVendorSpecificDetails() isMandateData_VendorSpecificDetails {
	if m != nil {
		return m.VendorSpecificDetails
	}
	return nil
}

func (x *MandateData) GetAbfl() *AbflMandateData {
	if x, ok := x.GetVendorSpecificDetails().(*MandateData_Abfl); ok {
		return x.Abfl
	}
	return nil
}

func (x *MandateData) GetLlMandateData() *LLMandateData {
	if x, ok := x.GetVendorSpecificDetails().(*MandateData_LlMandateData); ok {
		return x.LlMandateData
	}
	return nil
}

func (x *MandateData) GetSgMandateData() *SgMandateData {
	if x, ok := x.GetVendorSpecificDetails().(*MandateData_SgMandateData); ok {
		return x.SgMandateData
	}
	return nil
}

func (x *MandateData) GetFedMandateData() *FedMandateData {
	if x, ok := x.GetVendorSpecificDetails().(*MandateData_FedMandateData); ok {
		return x.FedMandateData
	}
	return nil
}

func (x *MandateData) GetBankingDetails() *MandateData_BankingDetails {
	if x != nil {
		return x.BankingDetails
	}
	return nil
}

func (x *MandateData) GetMandateWithFiAccountNotAllowed() bool {
	if x != nil {
		return x.MandateWithFiAccountNotAllowed
	}
	return false
}

func (x *MandateData) GetFiAccountIneligibleForMandateReason() MandateData_FiAccountIneligibleForMandateReason {
	if x != nil {
		return x.FiAccountIneligibleForMandateReason
	}
	return MandateData_FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_UNSPECIFIED
}

func (x *MandateData) GetUrlGeneratedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UrlGeneratedAt
	}
	return nil
}

func (x *MandateData) GetMandateType() enums.MandateType {
	if x != nil {
		return x.MandateType
	}
	return enums.MandateType(0)
}

type isMandateData_VendorSpecificDetails interface {
	isMandateData_VendorSpecificDetails()
}

type MandateData_Abfl struct {
	Abfl *AbflMandateData `protobuf:"bytes,14,opt,name=abfl,proto3,oneof"`
}

type MandateData_LlMandateData struct {
	LlMandateData *LLMandateData `protobuf:"bytes,15,opt,name=ll_mandate_data,json=llMandateData,proto3,oneof"`
}

type MandateData_SgMandateData struct {
	SgMandateData *SgMandateData `protobuf:"bytes,16,opt,name=sg_mandate_data,json=sgMandateData,proto3,oneof"`
}

type MandateData_FedMandateData struct {
	FedMandateData *FedMandateData `protobuf:"bytes,17,opt,name=fed_mandate_data,json=fedMandateData,proto3,oneof"`
}

func (*MandateData_Abfl) isMandateData_VendorSpecificDetails() {}

func (*MandateData_LlMandateData) isMandateData_VendorSpecificDetails() {}

func (*MandateData_SgMandateData) isMandateData_VendorSpecificDetails() {}

func (*MandateData_FedMandateData) isMandateData_VendorSpecificDetails() {}

type LoanStepExecutionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Details:
	//
	//	*LoanStepExecutionDetails_LivenessStepData
	//	*LoanStepExecutionDetails_FaceMatchStepData
	//	*LoanStepExecutionDetails_ESignStepData
	//	*LoanStepExecutionDetails_VkycStepData
	//	*LoanStepExecutionDetails_ManualReviewStepData
	//	*LoanStepExecutionDetails_OnboardingData
	//	*LoanStepExecutionDetails_MandateData
	//	*LoanStepExecutionDetails_HunterData
	//	*LoanStepExecutionDetails_CkycStepData
	//	*LoanStepExecutionDetails_CollectionData
	//	*LoanStepExecutionDetails_ApplicantData
	//	*LoanStepExecutionDetails_OtpVerificationData
	//	*LoanStepExecutionDetails_BreData
	//	*LoanStepExecutionDetails_SelfieData
	//	*LoanStepExecutionDetails_CreateLeadStepData
	//	*LoanStepExecutionDetails_VendorPwaStagesStepData
	//	*LoanStepExecutionDetails_KycStepData
	//	*LoanStepExecutionDetails_IncomeEstimateData
	//	*LoanStepExecutionDetails_AadhaarData
	//	*LoanStepExecutionDetails_LoanDetailsVerificationData
	//	*LoanStepExecutionDetails_ResetVendorLoanApplicationData
	//	*LoanStepExecutionDetails_ContactabilityDetailsData
	//	*LoanStepExecutionDetails_VendorIdentifiersData
	//	*LoanStepExecutionDetails_RoiModificationData
	//	*LoanStepExecutionDetails_PreEligibilityData
	Details isLoanStepExecutionDetails_Details `protobuf_oneof:"Details"`
	// intentionally marked as 15 to accommodate the oneof fields in case they increase
	VendorData string `protobuf:"bytes,15,opt,name=vendor_data,json=vendorData,proto3" json:"vendor_data,omitempty"`
	// Types that are assignable to VendorSpecificDetails:
	//
	//	*LoanStepExecutionDetails_Abfl
	VendorSpecificDetails isLoanStepExecutionDetails_VendorSpecificDetails `protobuf_oneof:"vendor_specific_details"`
}

func (x *LoanStepExecutionDetails) Reset() {
	*x = LoanStepExecutionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanStepExecutionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanStepExecutionDetails) ProtoMessage() {}

func (x *LoanStepExecutionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanStepExecutionDetails.ProtoReflect.Descriptor instead.
func (*LoanStepExecutionDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{19}
}

func (m *LoanStepExecutionDetails) GetDetails() isLoanStepExecutionDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetLivenessStepData() *LivenessStepData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_LivenessStepData); ok {
		return x.LivenessStepData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetFaceMatchStepData() *FaceMatchStepData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_FaceMatchStepData); ok {
		return x.FaceMatchStepData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetESignStepData() *ESignStepData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_ESignStepData); ok {
		return x.ESignStepData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetVkycStepData() *VkycStepData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_VkycStepData); ok {
		return x.VkycStepData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetManualReviewStepData() *ManualReviewStepData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_ManualReviewStepData); ok {
		return x.ManualReviewStepData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetOnboardingData() *OnboardingData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_OnboardingData); ok {
		return x.OnboardingData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetMandateData() *MandateData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_MandateData); ok {
		return x.MandateData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetHunterData() *HunterData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_HunterData); ok {
		return x.HunterData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetCkycStepData() *CkycStepData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_CkycStepData); ok {
		return x.CkycStepData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetCollectionData() *CollectionData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_CollectionData); ok {
		return x.CollectionData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetApplicantData() *ApplicantData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_ApplicantData); ok {
		return x.ApplicantData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetOtpVerificationData() *OtpVerificationData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_OtpVerificationData); ok {
		return x.OtpVerificationData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetBreData() *BreData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_BreData); ok {
		return x.BreData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetSelfieData() *SelfieData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_SelfieData); ok {
		return x.SelfieData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetCreateLeadStepData() *CreateLeadStepData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_CreateLeadStepData); ok {
		return x.CreateLeadStepData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetVendorPwaStagesStepData() *VendorPWAStagesStepData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_VendorPwaStagesStepData); ok {
		return x.VendorPwaStagesStepData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetKycStepData() *KycStepData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_KycStepData); ok {
		return x.KycStepData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetIncomeEstimateData() *IncomeEstimateData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_IncomeEstimateData); ok {
		return x.IncomeEstimateData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetAadhaarData() *AadhaarData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_AadhaarData); ok {
		return x.AadhaarData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetLoanDetailsVerificationData() *LoanDetailsVerificationData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_LoanDetailsVerificationData); ok {
		return x.LoanDetailsVerificationData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetResetVendorLoanApplicationData() *ResetVendorLoanApplicationData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_ResetVendorLoanApplicationData); ok {
		return x.ResetVendorLoanApplicationData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetContactabilityDetailsData() *ContactabilityDetailsData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_ContactabilityDetailsData); ok {
		return x.ContactabilityDetailsData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetVendorIdentifiersData() *VendorIdentifiersData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_VendorIdentifiersData); ok {
		return x.VendorIdentifiersData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetRoiModificationData() *ROIModificationData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_RoiModificationData); ok {
		return x.RoiModificationData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetPreEligibilityData() *PreEligibilityData {
	if x, ok := x.GetDetails().(*LoanStepExecutionDetails_PreEligibilityData); ok {
		return x.PreEligibilityData
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetVendorData() string {
	if x != nil {
		return x.VendorData
	}
	return ""
}

func (m *LoanStepExecutionDetails) GetVendorSpecificDetails() isLoanStepExecutionDetails_VendorSpecificDetails {
	if m != nil {
		return m.VendorSpecificDetails
	}
	return nil
}

func (x *LoanStepExecutionDetails) GetAbfl() *AbflLoanDisbursementData {
	if x, ok := x.GetVendorSpecificDetails().(*LoanStepExecutionDetails_Abfl); ok {
		return x.Abfl
	}
	return nil
}

type isLoanStepExecutionDetails_Details interface {
	isLoanStepExecutionDetails_Details()
}

type LoanStepExecutionDetails_LivenessStepData struct {
	LivenessStepData *LivenessStepData `protobuf:"bytes,1,opt,name=liveness_step_data,json=livenessStepData,proto3,oneof"`
}

type LoanStepExecutionDetails_FaceMatchStepData struct {
	FaceMatchStepData *FaceMatchStepData `protobuf:"bytes,2,opt,name=face_match_step_data,json=faceMatchStepData,proto3,oneof"`
}

type LoanStepExecutionDetails_ESignStepData struct {
	ESignStepData *ESignStepData `protobuf:"bytes,3,opt,name=e_sign_step_data,json=eSignStepData,proto3,oneof"`
}

type LoanStepExecutionDetails_VkycStepData struct {
	VkycStepData *VkycStepData `protobuf:"bytes,4,opt,name=vkyc_step_data,json=vkycStepData,proto3,oneof"`
}

type LoanStepExecutionDetails_ManualReviewStepData struct {
	ManualReviewStepData *ManualReviewStepData `protobuf:"bytes,5,opt,name=manual_review_step_data,json=manualReviewStepData,proto3,oneof"`
}

type LoanStepExecutionDetails_OnboardingData struct {
	OnboardingData *OnboardingData `protobuf:"bytes,6,opt,name=onboarding_data,json=onboardingData,proto3,oneof"`
}

type LoanStepExecutionDetails_MandateData struct {
	MandateData *MandateData `protobuf:"bytes,7,opt,name=mandate_data,json=mandateData,proto3,oneof"`
}

type LoanStepExecutionDetails_HunterData struct {
	HunterData *HunterData `protobuf:"bytes,8,opt,name=hunter_data,json=hunterData,proto3,oneof"`
}

type LoanStepExecutionDetails_CkycStepData struct {
	CkycStepData *CkycStepData `protobuf:"bytes,9,opt,name=ckyc_step_data,json=ckycStepData,proto3,oneof"`
}

type LoanStepExecutionDetails_CollectionData struct {
	CollectionData *CollectionData `protobuf:"bytes,10,opt,name=collection_data,json=collectionData,proto3,oneof"`
}

type LoanStepExecutionDetails_ApplicantData struct {
	ApplicantData *ApplicantData `protobuf:"bytes,11,opt,name=applicant_data,json=applicantData,proto3,oneof"`
}

type LoanStepExecutionDetails_OtpVerificationData struct {
	OtpVerificationData *OtpVerificationData `protobuf:"bytes,12,opt,name=otp_verification_data,json=otpVerificationData,proto3,oneof"`
}

type LoanStepExecutionDetails_BreData struct {
	BreData *BreData `protobuf:"bytes,13,opt,name=bre_data,json=breData,proto3,oneof"`
}

type LoanStepExecutionDetails_SelfieData struct {
	SelfieData *SelfieData `protobuf:"bytes,14,opt,name=selfie_data,json=selfieData,proto3,oneof"`
}

type LoanStepExecutionDetails_CreateLeadStepData struct {
	CreateLeadStepData *CreateLeadStepData `protobuf:"bytes,17,opt,name=create_lead_step_data,json=createLeadStepData,proto3,oneof"`
}

type LoanStepExecutionDetails_VendorPwaStagesStepData struct {
	VendorPwaStagesStepData *VendorPWAStagesStepData `protobuf:"bytes,18,opt,name=vendor_pwa_stages_step_data,json=vendorPwaStagesStepData,proto3,oneof"`
}

type LoanStepExecutionDetails_KycStepData struct {
	KycStepData *KycStepData `protobuf:"bytes,19,opt,name=kyc_step_data,json=kycStepData,proto3,oneof"`
}

type LoanStepExecutionDetails_IncomeEstimateData struct {
	// intentionally keeping it 23 to acccomodate for more vendor specific details
	IncomeEstimateData *IncomeEstimateData `protobuf:"bytes,23,opt,name=income_estimate_data,json=incomeEstimateData,proto3,oneof"`
}

type LoanStepExecutionDetails_AadhaarData struct {
	AadhaarData *AadhaarData `protobuf:"bytes,24,opt,name=aadhaar_data,json=aadhaarData,proto3,oneof"`
}

type LoanStepExecutionDetails_LoanDetailsVerificationData struct {
	LoanDetailsVerificationData *LoanDetailsVerificationData `protobuf:"bytes,25,opt,name=loan_details_verification_data,json=loanDetailsVerificationData,proto3,oneof"`
}

type LoanStepExecutionDetails_ResetVendorLoanApplicationData struct {
	ResetVendorLoanApplicationData *ResetVendorLoanApplicationData `protobuf:"bytes,26,opt,name=reset_vendor_loan_application_data,json=resetVendorLoanApplicationData,proto3,oneof"`
}

type LoanStepExecutionDetails_ContactabilityDetailsData struct {
	ContactabilityDetailsData *ContactabilityDetailsData `protobuf:"bytes,27,opt,name=contactability_details_data,json=contactabilityDetailsData,proto3,oneof"`
}

type LoanStepExecutionDetails_VendorIdentifiersData struct {
	VendorIdentifiersData *VendorIdentifiersData `protobuf:"bytes,28,opt,name=vendor_identifiers_data,json=vendorIdentifiersData,proto3,oneof"`
}

type LoanStepExecutionDetails_RoiModificationData struct {
	RoiModificationData *ROIModificationData `protobuf:"bytes,29,opt,name=roi_modification_data,json=roiModificationData,proto3,oneof"`
}

type LoanStepExecutionDetails_PreEligibilityData struct {
	PreEligibilityData *PreEligibilityData `protobuf:"bytes,30,opt,name=pre_eligibility_data,json=preEligibilityData,proto3,oneof"`
}

func (*LoanStepExecutionDetails_LivenessStepData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_FaceMatchStepData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_ESignStepData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_VkycStepData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_ManualReviewStepData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_OnboardingData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_MandateData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_HunterData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_CkycStepData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_CollectionData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_ApplicantData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_OtpVerificationData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_BreData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_SelfieData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_CreateLeadStepData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_VendorPwaStagesStepData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_KycStepData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_IncomeEstimateData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_AadhaarData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_LoanDetailsVerificationData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_ResetVendorLoanApplicationData) isLoanStepExecutionDetails_Details() {
}

func (*LoanStepExecutionDetails_ContactabilityDetailsData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_VendorIdentifiersData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_RoiModificationData) isLoanStepExecutionDetails_Details() {}

func (*LoanStepExecutionDetails_PreEligibilityData) isLoanStepExecutionDetails_Details() {}

type isLoanStepExecutionDetails_VendorSpecificDetails interface {
	isLoanStepExecutionDetails_VendorSpecificDetails()
}

type LoanStepExecutionDetails_Abfl struct {
	// applicable only for Vendor_ABFL
	Abfl *AbflLoanDisbursementData `protobuf:"bytes,16,opt,name=abfl,proto3,oneof"`
}

func (*LoanStepExecutionDetails_Abfl) isLoanStepExecutionDetails_VendorSpecificDetails() {}

// This data field is created to store the pre-eligibility flow data
type PreEligibilityData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditReportAttemptCount int64 `protobuf:"varint,1,opt,name=credit_report_attempt_count,json=creditReportAttemptCount,proto3" json:"credit_report_attempt_count,omitempty"`
}

func (x *PreEligibilityData) Reset() {
	*x = PreEligibilityData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreEligibilityData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreEligibilityData) ProtoMessage() {}

func (x *PreEligibilityData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreEligibilityData.ProtoReflect.Descriptor instead.
func (*PreEligibilityData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{20}
}

func (x *PreEligibilityData) GetCreditReportAttemptCount() int64 {
	if x != nil {
		return x.CreditReportAttemptCount
	}
	return 0
}

type AadhaarData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to VendorSpecificDetails:
	//
	//	*AadhaarData_Abfl
	VendorSpecificDetails isAadhaarData_VendorSpecificDetails `protobuf_oneof:"vendor_specific_details"`
	LastFourDigit         string                              `protobuf:"bytes,2,opt,name=last_four_digit,json=lastFourDigit,proto3" json:"last_four_digit,omitempty"`
}

func (x *AadhaarData) Reset() {
	*x = AadhaarData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AadhaarData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AadhaarData) ProtoMessage() {}

func (x *AadhaarData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AadhaarData.ProtoReflect.Descriptor instead.
func (*AadhaarData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{21}
}

func (m *AadhaarData) GetVendorSpecificDetails() isAadhaarData_VendorSpecificDetails {
	if m != nil {
		return m.VendorSpecificDetails
	}
	return nil
}

func (x *AadhaarData) GetAbfl() *AbflAadhaarData {
	if x, ok := x.GetVendorSpecificDetails().(*AadhaarData_Abfl); ok {
		return x.Abfl
	}
	return nil
}

func (x *AadhaarData) GetLastFourDigit() string {
	if x != nil {
		return x.LastFourDigit
	}
	return ""
}

type isAadhaarData_VendorSpecificDetails interface {
	isAadhaarData_VendorSpecificDetails()
}

type AadhaarData_Abfl struct {
	Abfl *AbflAadhaarData `protobuf:"bytes,1,opt,name=abfl,proto3,oneof"`
}

func (*AadhaarData_Abfl) isAadhaarData_VendorSpecificDetails() {}

type AbflAadhaarData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProfileId     string                 `protobuf:"bytes,1,opt,name=profile_id,json=profileId,proto3" json:"profile_id,omitempty"`
	PartnerReqId  string                 `protobuf:"bytes,2,opt,name=partner_req_id,json=partnerReqId,proto3" json:"partner_req_id,omitempty"`
	CaptureLink   string                 `protobuf:"bytes,3,opt,name=capture_link,json=captureLink,proto3" json:"capture_link,omitempty"`
	Address       *typesv2.PostalAddress `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	Age           int32                  `protobuf:"varint,5,opt,name=age,proto3" json:"age,omitempty"`
	Gender        typesv2.Gender         `protobuf:"varint,6,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	Name          *common.Name           `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	StreetAddress string                 `protobuf:"bytes,8,opt,name=street_address,json=streetAddress,proto3" json:"street_address,omitempty"`
	Dob           *date.Date             `protobuf:"bytes,9,opt,name=dob,proto3" json:"dob,omitempty"`
}

func (x *AbflAadhaarData) Reset() {
	*x = AbflAadhaarData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AbflAadhaarData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AbflAadhaarData) ProtoMessage() {}

func (x *AbflAadhaarData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AbflAadhaarData.ProtoReflect.Descriptor instead.
func (*AbflAadhaarData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{22}
}

func (x *AbflAadhaarData) GetProfileId() string {
	if x != nil {
		return x.ProfileId
	}
	return ""
}

func (x *AbflAadhaarData) GetPartnerReqId() string {
	if x != nil {
		return x.PartnerReqId
	}
	return ""
}

func (x *AbflAadhaarData) GetCaptureLink() string {
	if x != nil {
		return x.CaptureLink
	}
	return ""
}

func (x *AbflAadhaarData) GetAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *AbflAadhaarData) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *AbflAadhaarData) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *AbflAadhaarData) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *AbflAadhaarData) GetStreetAddress() string {
	if x != nil {
		return x.StreetAddress
	}
	return ""
}

func (x *AbflAadhaarData) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

type IncomeEstimateData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PredictedIncome           *typesv2.Money   `protobuf:"bytes,1,opt,name=predicted_income,json=predictedIncome,proto3" json:"predicted_income,omitempty"`
	Confidence                float32          `protobuf:"fixed32,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	RawResponse               string           `protobuf:"bytes,3,opt,name=raw_response,json=rawResponse,proto3" json:"raw_response,omitempty"`
	IncomeDataSource          IncomeDataSource `protobuf:"varint,4,opt,name=income_data_source,json=incomeDataSource,proto3,enum=preapprovedloan.IncomeDataSource" json:"income_data_source,omitempty"`
	IncomeEstimatorRespStatus *rpc.Status      `protobuf:"bytes,5,opt,name=income_estimator_resp_status,json=incomeEstimatorRespStatus,proto3" json:"income_estimator_resp_status,omitempty"`
	ItrIncomeInfo             *ItrIncomeInfo   `protobuf:"bytes,6,opt,name=itr_income_info,json=itrIncomeInfo,proto3" json:"itr_income_info,omitempty"`
	AttemptOrchIdList         []string         `protobuf:"bytes,7,rep,name=attempt_orch_id_list,json=attemptOrchIdList,proto3" json:"attempt_orch_id_list,omitempty"`
}

func (x *IncomeEstimateData) Reset() {
	*x = IncomeEstimateData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncomeEstimateData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncomeEstimateData) ProtoMessage() {}

func (x *IncomeEstimateData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncomeEstimateData.ProtoReflect.Descriptor instead.
func (*IncomeEstimateData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{23}
}

func (x *IncomeEstimateData) GetPredictedIncome() *typesv2.Money {
	if x != nil {
		return x.PredictedIncome
	}
	return nil
}

func (x *IncomeEstimateData) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *IncomeEstimateData) GetRawResponse() string {
	if x != nil {
		return x.RawResponse
	}
	return ""
}

func (x *IncomeEstimateData) GetIncomeDataSource() IncomeDataSource {
	if x != nil {
		return x.IncomeDataSource
	}
	return IncomeDataSource_INCOME_DATA_SOURCE_UNSPECIFIED
}

func (x *IncomeEstimateData) GetIncomeEstimatorRespStatus() *rpc.Status {
	if x != nil {
		return x.IncomeEstimatorRespStatus
	}
	return nil
}

func (x *IncomeEstimateData) GetItrIncomeInfo() *ItrIncomeInfo {
	if x != nil {
		return x.ItrIncomeInfo
	}
	return nil
}

func (x *IncomeEstimateData) GetAttemptOrchIdList() []string {
	if x != nil {
		return x.AttemptOrchIdList
	}
	return nil
}

type ItrIncomeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItrAttemptData []*ItrIncomeInfo_ItrAttemptData  `protobuf:"bytes,1,rep,name=itr_attempt_data,json=itrAttemptData,proto3" json:"itr_attempt_data,omitempty"`
	ItrIntimation  *ItrIncomeInfo_ItrIntimationData `protobuf:"bytes,2,opt,name=itr_intimation,json=itrIntimation,proto3" json:"itr_intimation,omitempty"`
}

func (x *ItrIncomeInfo) Reset() {
	*x = ItrIncomeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItrIncomeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItrIncomeInfo) ProtoMessage() {}

func (x *ItrIncomeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItrIncomeInfo.ProtoReflect.Descriptor instead.
func (*ItrIncomeInfo) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{24}
}

func (x *ItrIncomeInfo) GetItrAttemptData() []*ItrIncomeInfo_ItrAttemptData {
	if x != nil {
		return x.ItrAttemptData
	}
	return nil
}

func (x *ItrIncomeInfo) GetItrIntimation() *ItrIncomeInfo_ItrIntimationData {
	if x != nil {
		return x.ItrIntimation
	}
	return nil
}

type AbflLoanDisbursementData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanUniqueId string `protobuf:"bytes,1,opt,name=loan_unique_id,json=loanUniqueId,proto3" json:"loan_unique_id,omitempty"`
	LoanNumber   string `protobuf:"bytes,2,opt,name=loan_number,json=loanNumber,proto3" json:"loan_number,omitempty"`
	DealNumber   string `protobuf:"bytes,3,opt,name=deal_number,json=dealNumber,proto3" json:"deal_number,omitempty"`
}

func (x *AbflLoanDisbursementData) Reset() {
	*x = AbflLoanDisbursementData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AbflLoanDisbursementData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AbflLoanDisbursementData) ProtoMessage() {}

func (x *AbflLoanDisbursementData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AbflLoanDisbursementData.ProtoReflect.Descriptor instead.
func (*AbflLoanDisbursementData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{25}
}

func (x *AbflLoanDisbursementData) GetLoanUniqueId() string {
	if x != nil {
		return x.LoanUniqueId
	}
	return ""
}

func (x *AbflLoanDisbursementData) GetLoanNumber() string {
	if x != nil {
		return x.LoanNumber
	}
	return ""
}

func (x *AbflLoanDisbursementData) GetDealNumber() string {
	if x != nil {
		return x.DealNumber
	}
	return ""
}

type ListOfString struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Values []string `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *ListOfString) Reset() {
	*x = ListOfString{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOfString) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOfString) ProtoMessage() {}

func (x *ListOfString) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOfString.ProtoReflect.Descriptor instead.
func (*ListOfString) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{26}
}

func (x *ListOfString) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

type HunterData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rules []string `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
	Score int64    `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	Rule  string   `protobuf:"bytes,3,opt,name=rule,proto3" json:"rule,omitempty"`
}

func (x *HunterData) Reset() {
	*x = HunterData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HunterData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HunterData) ProtoMessage() {}

func (x *HunterData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HunterData.ProtoReflect.Descriptor instead.
func (*HunterData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{27}
}

func (x *HunterData) GetRules() []string {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *HunterData) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *HunterData) GetRule() string {
	if x != nil {
		return x.Rule
	}
	return ""
}

type CollectionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RepaymentBreakup []*RepaymentBreakupData `protobuf:"bytes,1,rep,name=repayment_breakup,json=repaymentBreakup,proto3" json:"repayment_breakup,omitempty"`
}

func (x *CollectionData) Reset() {
	*x = CollectionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionData) ProtoMessage() {}

func (x *CollectionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionData.ProtoReflect.Descriptor instead.
func (*CollectionData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{28}
}

func (x *CollectionData) GetRepaymentBreakup() []*RepaymentBreakupData {
	if x != nil {
		return x.RepaymentBreakup
	}
	return nil
}

type ApplicantData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pan               string              `protobuf:"bytes,1,opt,name=pan,proto3" json:"pan,omitempty"`
	Dob               *date.Date          `protobuf:"bytes,2,opt,name=dob,proto3" json:"dob,omitempty"`
	PhoneNumber       *common.PhoneNumber `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Email             string              `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	EmploymentType    string              `protobuf:"bytes,5,opt,name=employment_type,json=employmentType,proto3" json:"employment_type,omitempty"`
	MaritalStatus     string              `protobuf:"bytes,6,opt,name=marital_status,json=maritalStatus,proto3" json:"marital_status,omitempty"`
	ResidenceType     string              `protobuf:"bytes,7,opt,name=residence_type,json=residenceType,proto3" json:"residence_type,omitempty"`
	FatherName        string              `protobuf:"bytes,8,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	MotherName        string              `protobuf:"bytes,9,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	References        []*Reference        `protobuf:"bytes,10,rep,name=references,proto3" json:"references,omitempty"`
	PurposeOfLoan     string              `protobuf:"bytes,11,opt,name=purpose_of_loan,json=purposeOfLoan,proto3" json:"purpose_of_loan,omitempty"`
	DesiredLoanAmount *money.Money        `protobuf:"bytes,12,opt,name=desired_loan_amount,json=desiredLoanAmount,proto3" json:"desired_loan_amount,omitempty"`
}

func (x *ApplicantData) Reset() {
	*x = ApplicantData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicantData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicantData) ProtoMessage() {}

func (x *ApplicantData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicantData.ProtoReflect.Descriptor instead.
func (*ApplicantData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{29}
}

func (x *ApplicantData) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *ApplicantData) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *ApplicantData) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *ApplicantData) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ApplicantData) GetEmploymentType() string {
	if x != nil {
		return x.EmploymentType
	}
	return ""
}

func (x *ApplicantData) GetMaritalStatus() string {
	if x != nil {
		return x.MaritalStatus
	}
	return ""
}

func (x *ApplicantData) GetResidenceType() string {
	if x != nil {
		return x.ResidenceType
	}
	return ""
}

func (x *ApplicantData) GetFatherName() string {
	if x != nil {
		return x.FatherName
	}
	return ""
}

func (x *ApplicantData) GetMotherName() string {
	if x != nil {
		return x.MotherName
	}
	return ""
}

func (x *ApplicantData) GetReferences() []*Reference {
	if x != nil {
		return x.References
	}
	return nil
}

func (x *ApplicantData) GetPurposeOfLoan() string {
	if x != nil {
		return x.PurposeOfLoan
	}
	return ""
}

func (x *ApplicantData) GetDesiredLoanAmount() *money.Money {
	if x != nil {
		return x.DesiredLoanAmount
	}
	return nil
}

type OtpVerificationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OtpData []*OtpVerificationData_OtpData `protobuf:"bytes,1,rep,name=otp_data,json=otpData,proto3" json:"otp_data,omitempty"`
	// flag to instruct otp verification processes to skip
	// verifying remaining otps
	SkipRemainingOtps bool `protobuf:"varint,2,opt,name=skip_remaining_otps,json=skipRemainingOtps,proto3" json:"skip_remaining_otps,omitempty"`
	// flag to signal worker that otp verification is in progress.
	OtpVerificationInProgress bool `protobuf:"varint,3,opt,name=otp_verification_in_progress,json=otpVerificationInProgress,proto3" json:"otp_verification_in_progress,omitempty"`
	// timestamp when otp verification was last started. will be used by worker
	// to determine if the verification process has expired.
	OtpVerificationStartedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=otp_verification_started_at,json=otpVerificationStartedAt,proto3" json:"otp_verification_started_at,omitempty"`
}

func (x *OtpVerificationData) Reset() {
	*x = OtpVerificationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OtpVerificationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OtpVerificationData) ProtoMessage() {}

func (x *OtpVerificationData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OtpVerificationData.ProtoReflect.Descriptor instead.
func (*OtpVerificationData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{30}
}

func (x *OtpVerificationData) GetOtpData() []*OtpVerificationData_OtpData {
	if x != nil {
		return x.OtpData
	}
	return nil
}

func (x *OtpVerificationData) GetSkipRemainingOtps() bool {
	if x != nil {
		return x.SkipRemainingOtps
	}
	return false
}

func (x *OtpVerificationData) GetOtpVerificationInProgress() bool {
	if x != nil {
		return x.OtpVerificationInProgress
	}
	return false
}

func (x *OtpVerificationData) GetOtpVerificationStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.OtpVerificationStartedAt
	}
	return nil
}

// Condition represents expression that evaluate to a boolean value
type Condition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Condition:
	//
	//	*Condition_OtpStatusCondition
	//	*Condition_NotCondition
	Condition isCondition_Condition `protobuf_oneof:"condition"`
}

func (x *Condition) Reset() {
	*x = Condition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Condition) ProtoMessage() {}

func (x *Condition) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Condition.ProtoReflect.Descriptor instead.
func (*Condition) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{31}
}

func (m *Condition) GetCondition() isCondition_Condition {
	if m != nil {
		return m.Condition
	}
	return nil
}

func (x *Condition) GetOtpStatusCondition() *OtpStatusCondition {
	if x, ok := x.GetCondition().(*Condition_OtpStatusCondition); ok {
		return x.OtpStatusCondition
	}
	return nil
}

func (x *Condition) GetNotCondition() *NotCondition {
	if x, ok := x.GetCondition().(*Condition_NotCondition); ok {
		return x.NotCondition
	}
	return nil
}

type isCondition_Condition interface {
	isCondition_Condition()
}

type Condition_OtpStatusCondition struct {
	OtpStatusCondition *OtpStatusCondition `protobuf:"bytes,1,opt,name=otp_status_condition,json=otpStatusCondition,proto3,oneof"`
}

type Condition_NotCondition struct {
	NotCondition *NotCondition `protobuf:"bytes,2,opt,name=not_condition,json=notCondition,proto3,oneof"`
}

func (*Condition_OtpStatusCondition) isCondition_Condition() {}

func (*Condition_NotCondition) isCondition_Condition() {}

// OtpStatusCondition checks if otp status is present in `statuses` array.
type OtpStatusCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OtpId    string      `protobuf:"bytes,1,opt,name=otp_id,json=otpId,proto3" json:"otp_id,omitempty"`
	Statuses []OtpStatus `protobuf:"varint,2,rep,packed,name=statuses,proto3,enum=preapprovedloan.OtpStatus" json:"statuses,omitempty"`
}

func (x *OtpStatusCondition) Reset() {
	*x = OtpStatusCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OtpStatusCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OtpStatusCondition) ProtoMessage() {}

func (x *OtpStatusCondition) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OtpStatusCondition.ProtoReflect.Descriptor instead.
func (*OtpStatusCondition) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{32}
}

func (x *OtpStatusCondition) GetOtpId() string {
	if x != nil {
		return x.OtpId
	}
	return ""
}

func (x *OtpStatusCondition) GetStatuses() []OtpStatus {
	if x != nil {
		return x.Statuses
	}
	return nil
}

// returns NOT of base_condition outcome
type NotCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseCondition *Condition `protobuf:"bytes,1,opt,name=base_condition,json=baseCondition,proto3" json:"base_condition,omitempty"`
}

func (x *NotCondition) Reset() {
	*x = NotCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotCondition) ProtoMessage() {}

func (x *NotCondition) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotCondition.ProtoReflect.Descriptor instead.
func (*NotCondition) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{33}
}

func (x *NotCondition) GetBaseCondition() *Condition {
	if x != nil {
		return x.BaseCondition
	}
	return nil
}

type BreData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsEpfoDataNeeded bool `protobuf:"varint,1,opt,name=is_epfo_data_needed,json=isEpfoDataNeeded,proto3" json:"is_epfo_data_needed,omitempty"`
	// Generic identifier field. Can be used differently for all the vendors
	// This id could be used to fetch bre data against. Or can be used as an identifier from CKYC to be stored and used in further flow
	Identifier string `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`
}

func (x *BreData) Reset() {
	*x = BreData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BreData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BreData) ProtoMessage() {}

func (x *BreData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BreData.ProtoReflect.Descriptor instead.
func (*BreData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{34}
}

func (x *BreData) GetIsEpfoDataNeeded() bool {
	if x != nil {
		return x.IsEpfoDataNeeded
	}
	return false
}

func (x *BreData) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

type SelfieData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SelfieImage *common.Image `protobuf:"bytes,1,opt,name=selfie_image,json=selfieImage,proto3" json:"selfie_image,omitempty"`
}

func (x *SelfieData) Reset() {
	*x = SelfieData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelfieData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelfieData) ProtoMessage() {}

func (x *SelfieData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelfieData.ProtoReflect.Descriptor instead.
func (*SelfieData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{35}
}

func (x *SelfieData) GetSelfieImage() *common.Image {
	if x != nil {
		return x.SelfieImage
	}
	return nil
}

type Reference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        *common.Name        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *Reference) Reset() {
	*x = Reference{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reference) ProtoMessage() {}

func (x *Reference) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reference.ProtoReflect.Descriptor instead.
func (*Reference) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{36}
}

func (x *Reference) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *Reference) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

type CreateLeadStepData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the id of lead which got created at create_lead step.
	LeadId string `protobuf:"bytes,1,opt,name=lead_id,json=leadId,proto3" json:"lead_id,omitempty"`
	// denotes the user's pan number shared with the vendor.
	Pan string `protobuf:"bytes,2,opt,name=pan,proto3" json:"pan,omitempty"`
	// denotes the user's name shared with the vendor.
	Name *common.Name `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// denotes the user's phone-number shared with the vendor.
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Gender      typesv2.Gender      `protobuf:"varint,5,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	// denotes the user's date of birth shared with the vendor.
	Dob *date.Date `protobuf:"bytes,6,opt,name=dob,proto3" json:"dob,omitempty"`
	// denotes the user's employment type shared with the vendor.
	EmploymentType string `protobuf:"bytes,7,opt,name=employment_type,json=employmentType,proto3" json:"employment_type,omitempty"`
	// denotes the user's declared income shared with the vendor.
	DeclaredIncome *money.Money `protobuf:"bytes,8,opt,name=declared_income,json=declaredIncome,proto3" json:"declared_income,omitempty"`
	// denotes the user's current address shared with the vendor.
	CurrentAddress *typesv2.PostalAddress `protobuf:"bytes,9,opt,name=current_address,json=currentAddress,proto3" json:"current_address,omitempty"`
	// denotes the user's email sent shared with the vendor.
	Email string `protobuf:"bytes,10,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *CreateLeadStepData) Reset() {
	*x = CreateLeadStepData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLeadStepData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLeadStepData) ProtoMessage() {}

func (x *CreateLeadStepData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLeadStepData.ProtoReflect.Descriptor instead.
func (*CreateLeadStepData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{37}
}

func (x *CreateLeadStepData) GetLeadId() string {
	if x != nil {
		return x.LeadId
	}
	return ""
}

func (x *CreateLeadStepData) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *CreateLeadStepData) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CreateLeadStepData) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *CreateLeadStepData) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *CreateLeadStepData) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *CreateLeadStepData) GetEmploymentType() string {
	if x != nil {
		return x.EmploymentType
	}
	return ""
}

func (x *CreateLeadStepData) GetDeclaredIncome() *money.Money {
	if x != nil {
		return x.DeclaredIncome
	}
	return nil
}

func (x *CreateLeadStepData) GetCurrentAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.CurrentAddress
	}
	return nil
}

func (x *CreateLeadStepData) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type VendorPWAStagesStepData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes info of stages which were initiated, completed or failed in vendor pwa flow.
	StageInfos []*VendorPWAStagesStepData_StageInfo `protobuf:"bytes,1,rep,name=stage_infos,json=stageInfos,proto3" json:"stage_infos,omitempty"`
	// string pwa url here to redirect user to vendor pwa flow.
	// with the expiry of the url.
	PwaUrl           string                 `protobuf:"bytes,2,opt,name=pwa_url,json=pwaUrl,proto3" json:"pwa_url,omitempty"`
	PwaUrlExpiryTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=pwa_url_expiry_time,json=pwaUrlExpiryTime,proto3" json:"pwa_url_expiry_time,omitempty"`
}

func (x *VendorPWAStagesStepData) Reset() {
	*x = VendorPWAStagesStepData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VendorPWAStagesStepData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VendorPWAStagesStepData) ProtoMessage() {}

func (x *VendorPWAStagesStepData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VendorPWAStagesStepData.ProtoReflect.Descriptor instead.
func (*VendorPWAStagesStepData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{38}
}

func (x *VendorPWAStagesStepData) GetStageInfos() []*VendorPWAStagesStepData_StageInfo {
	if x != nil {
		return x.StageInfos
	}
	return nil
}

func (x *VendorPWAStagesStepData) GetPwaUrl() string {
	if x != nil {
		return x.PwaUrl
	}
	return ""
}

func (x *VendorPWAStagesStepData) GetPwaUrlExpiryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.PwaUrlExpiryTime
	}
	return nil
}

type LoanDetailsVerificationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Data:
	//
	//	*LoanDetailsVerificationData_Lamf
	Data isLoanDetailsVerificationData_Data `protobuf_oneof:"data"`
}

func (x *LoanDetailsVerificationData) Reset() {
	*x = LoanDetailsVerificationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanDetailsVerificationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanDetailsVerificationData) ProtoMessage() {}

func (x *LoanDetailsVerificationData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanDetailsVerificationData.ProtoReflect.Descriptor instead.
func (*LoanDetailsVerificationData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{39}
}

func (m *LoanDetailsVerificationData) GetData() isLoanDetailsVerificationData_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *LoanDetailsVerificationData) GetLamf() *LamfLoanDetailsVerificationData {
	if x, ok := x.GetData().(*LoanDetailsVerificationData_Lamf); ok {
		return x.Lamf
	}
	return nil
}

type isLoanDetailsVerificationData_Data interface {
	isLoanDetailsVerificationData_Data()
}

type LoanDetailsVerificationData_Lamf struct {
	Lamf *LamfLoanDetailsVerificationData `protobuf:"bytes,1,opt,name=lamf,proto3,oneof"`
}

func (*LoanDetailsVerificationData_Lamf) isLoanDetailsVerificationData_Data() {}

type LamfLoanDetailsVerificationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserActionTaken bool                                           `protobuf:"varint,1,opt,name=user_action_taken,json=userActionTaken,proto3" json:"user_action_taken,omitempty"`
	PfFetch_Data    []*LamfLoanDetailsVerificationData_PfFetchData `protobuf:"bytes,2,rep,name=pf_fetch_Data,json=pfFetchData,proto3" json:"pf_fetch_Data,omitempty"`
	// Mobile link details contains list of folios and the mobile number to which the folios need to update
	MobileLinkDetails *LamfLoanDetailsVerificationData_MfMobileLinkDetails `protobuf:"bytes,3,opt,name=mobileLinkDetails,proto3" json:"mobileLinkDetails,omitempty"`
	// Email link details contains list of folios and the email to which the folios need to update
	EmailLinkDetails *LamfLoanDetailsVerificationData_MfEmailLinkDetails `protobuf:"bytes,4,opt,name=emailLinkDetails,proto3" json:"emailLinkDetails,omitempty"`
	UserAction       *LamfLoanDetailsVerificationData_UserAction         `protobuf:"bytes,5,opt,name=user_action,json=userAction,proto3" json:"user_action,omitempty"`
}

func (x *LamfLoanDetailsVerificationData) Reset() {
	*x = LamfLoanDetailsVerificationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LamfLoanDetailsVerificationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LamfLoanDetailsVerificationData) ProtoMessage() {}

func (x *LamfLoanDetailsVerificationData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LamfLoanDetailsVerificationData.ProtoReflect.Descriptor instead.
func (*LamfLoanDetailsVerificationData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{40}
}

func (x *LamfLoanDetailsVerificationData) GetUserActionTaken() bool {
	if x != nil {
		return x.UserActionTaken
	}
	return false
}

func (x *LamfLoanDetailsVerificationData) GetPfFetch_Data() []*LamfLoanDetailsVerificationData_PfFetchData {
	if x != nil {
		return x.PfFetch_Data
	}
	return nil
}

func (x *LamfLoanDetailsVerificationData) GetMobileLinkDetails() *LamfLoanDetailsVerificationData_MfMobileLinkDetails {
	if x != nil {
		return x.MobileLinkDetails
	}
	return nil
}

func (x *LamfLoanDetailsVerificationData) GetEmailLinkDetails() *LamfLoanDetailsVerificationData_MfEmailLinkDetails {
	if x != nil {
		return x.EmailLinkDetails
	}
	return nil
}

func (x *LamfLoanDetailsVerificationData) GetUserAction() *LamfLoanDetailsVerificationData_UserAction {
	if x != nil {
		return x.UserAction
	}
	return nil
}

type ResetVendorLoanApplicationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Data:
	//
	//	*ResetVendorLoanApplicationData_Lamf
	Data isResetVendorLoanApplicationData_Data `protobuf_oneof:"data"`
}

func (x *ResetVendorLoanApplicationData) Reset() {
	*x = ResetVendorLoanApplicationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetVendorLoanApplicationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetVendorLoanApplicationData) ProtoMessage() {}

func (x *ResetVendorLoanApplicationData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetVendorLoanApplicationData.ProtoReflect.Descriptor instead.
func (*ResetVendorLoanApplicationData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{41}
}

func (m *ResetVendorLoanApplicationData) GetData() isResetVendorLoanApplicationData_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *ResetVendorLoanApplicationData) GetLamf() *LamfResetVendorLoanApplicationData {
	if x, ok := x.GetData().(*ResetVendorLoanApplicationData_Lamf); ok {
		return x.Lamf
	}
	return nil
}

type isResetVendorLoanApplicationData_Data interface {
	isResetVendorLoanApplicationData_Data()
}

type ResetVendorLoanApplicationData_Lamf struct {
	Lamf *LamfResetVendorLoanApplicationData `protobuf:"bytes,1,opt,name=lamf,proto3,oneof"`
}

func (*ResetVendorLoanApplicationData_Lamf) isResetVendorLoanApplicationData_Data() {}

type LamfResetVendorLoanApplicationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserActionTaken bool `protobuf:"varint,1,opt,name=user_action_taken,json=userActionTaken,proto3" json:"user_action_taken,omitempty"`
}

func (x *LamfResetVendorLoanApplicationData) Reset() {
	*x = LamfResetVendorLoanApplicationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LamfResetVendorLoanApplicationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LamfResetVendorLoanApplicationData) ProtoMessage() {}

func (x *LamfResetVendorLoanApplicationData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LamfResetVendorLoanApplicationData.ProtoReflect.Descriptor instead.
func (*LamfResetVendorLoanApplicationData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{42}
}

func (x *LamfResetVendorLoanApplicationData) GetUserActionTaken() bool {
	if x != nil {
		return x.UserActionTaken
	}
	return false
}

type ContactabilityDetailsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PhoneNumber *common.PhoneNumber `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Token       string              `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// max number of otp allowed before blocking user and putting in cool down
	MaxOtpAttempts int32 `protobuf:"varint,3,opt,name=max_otp_attempts,json=maxOtpAttempts,proto3" json:"max_otp_attempts,omitempty"`
	// current otp attempt count
	OtpAttemptCount int32 `protobuf:"varint,4,opt,name=otp_attempt_count,json=otpAttemptCount,proto3" json:"otp_attempt_count,omitempty"`
	// current phone number attempt count
	PhoneNumberAttemptCount int32 `protobuf:"varint,5,opt,name=phone_number_attempt_count,json=phoneNumberAttemptCount,proto3" json:"phone_number_attempt_count,omitempty"`
	// max number of times the user can change the phone numbers for requesting otp
	MaxPhoneAttemptAllowed int32 `protobuf:"varint,6,opt,name=max_phone_attempt_allowed,json=maxPhoneAttemptAllowed,proto3" json:"max_phone_attempt_allowed,omitempty"`
	// last time wrong otp was entered
	LastAttemptTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_attempt_time,json=lastAttemptTime,proto3" json:"last_attempt_time,omitempty"`
}

func (x *ContactabilityDetailsData) Reset() {
	*x = ContactabilityDetailsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContactabilityDetailsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactabilityDetailsData) ProtoMessage() {}

func (x *ContactabilityDetailsData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactabilityDetailsData.ProtoReflect.Descriptor instead.
func (*ContactabilityDetailsData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{43}
}

func (x *ContactabilityDetailsData) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *ContactabilityDetailsData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *ContactabilityDetailsData) GetMaxOtpAttempts() int32 {
	if x != nil {
		return x.MaxOtpAttempts
	}
	return 0
}

func (x *ContactabilityDetailsData) GetOtpAttemptCount() int32 {
	if x != nil {
		return x.OtpAttemptCount
	}
	return 0
}

func (x *ContactabilityDetailsData) GetPhoneNumberAttemptCount() int32 {
	if x != nil {
		return x.PhoneNumberAttemptCount
	}
	return 0
}

func (x *ContactabilityDetailsData) GetMaxPhoneAttemptAllowed() int32 {
	if x != nil {
		return x.MaxPhoneAttemptAllowed
	}
	return 0
}

func (x *ContactabilityDetailsData) GetLastAttemptTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastAttemptTime
	}
	return nil
}

// VendorIdentifiersData can be used to persist identifiers returned from vendor for a step, example: vendorRequestID for a loan step, vendorReferenceID.
// These identifiers can later be used to interact with vendor APIs.
// NOTE : This field is not expected to hold any loan related ids like loan account id etc...
type VendorIdentifiersData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *VendorIdentifiersData) Reset() {
	*x = VendorIdentifiersData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VendorIdentifiersData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VendorIdentifiersData) ProtoMessage() {}

func (x *VendorIdentifiersData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VendorIdentifiersData.ProtoReflect.Descriptor instead.
func (*VendorIdentifiersData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{44}
}

func (x *VendorIdentifiersData) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type ROIModificationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The modified interest rate chosen by user from the list of allowed interest rates
	ChosenRoi float64 `protobuf:"fixed64,1,opt,name=chosen_roi,json=chosenRoi,proto3" json:"chosen_roi,omitempty"`
	// For storing key fact statement document after a user has modified the ROI
	KfsDoc *LoanDocument `protobuf:"bytes,2,opt,name=kfs_doc,json=kfsDoc,proto3" json:"kfs_doc,omitempty"`
	// For storing loan agreement document after a user has modified the ROI
	LoanAgreementDoc *LoanDocument `protobuf:"bytes,3,opt,name=loan_agreement_doc,json=loanAgreementDoc,proto3" json:"loan_agreement_doc,omitempty"`
	// New EMI amount based on chosen ROI
	InstallmentAmount         *money.Money `protobuf:"bytes,4,opt,name=installment_amount,json=installmentAmount,proto3" json:"installment_amount,omitempty"`
	AllowedRoiForModification []float64    `protobuf:"fixed64,5,rep,packed,name=allowed_roi_for_modification,json=allowedRoiForModification,proto3" json:"allowed_roi_for_modification,omitempty"`
}

func (x *ROIModificationData) Reset() {
	*x = ROIModificationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ROIModificationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ROIModificationData) ProtoMessage() {}

func (x *ROIModificationData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ROIModificationData.ProtoReflect.Descriptor instead.
func (*ROIModificationData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{45}
}

func (x *ROIModificationData) GetChosenRoi() float64 {
	if x != nil {
		return x.ChosenRoi
	}
	return 0
}

func (x *ROIModificationData) GetKfsDoc() *LoanDocument {
	if x != nil {
		return x.KfsDoc
	}
	return nil
}

func (x *ROIModificationData) GetLoanAgreementDoc() *LoanDocument {
	if x != nil {
		return x.LoanAgreementDoc
	}
	return nil
}

func (x *ROIModificationData) GetInstallmentAmount() *money.Money {
	if x != nil {
		return x.InstallmentAmount
	}
	return nil
}

func (x *ROIModificationData) GetAllowedRoiForModification() []float64 {
	if x != nil {
		return x.AllowedRoiForModification
	}
	return nil
}

type ManualReviewStepData_ReviewerDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email      string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	ReviewedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=reviewed_at,json=reviewedAt,proto3" json:"reviewed_at,omitempty"`
}

func (x *ManualReviewStepData_ReviewerDetails) Reset() {
	*x = ManualReviewStepData_ReviewerDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualReviewStepData_ReviewerDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualReviewStepData_ReviewerDetails) ProtoMessage() {}

func (x *ManualReviewStepData_ReviewerDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualReviewStepData_ReviewerDetails.ProtoReflect.Descriptor instead.
func (*ManualReviewStepData_ReviewerDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{16, 1}
}

func (x *ManualReviewStepData_ReviewerDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ManualReviewStepData_ReviewerDetails) GetReviewedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ReviewedAt
	}
	return nil
}

type OnboardingData_AddressDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AddressDetails *typesv2.PostalAddress `protobuf:"bytes,1,opt,name=address_details,json=addressDetails,proto3" json:"address_details,omitempty"`
	AddressType    typesv2.AddressType    `protobuf:"varint,2,opt,name=address_type,json=addressType,proto3,enum=api.typesv2.AddressType" json:"address_type,omitempty"`
	// location token we get from client side when adding address details.
	LocationToken string                `protobuf:"bytes,3,opt,name=location_token,json=locationToken,proto3" json:"location_token,omitempty"`
	ResidenceType typesv2.ResidenceType `protobuf:"varint,4,opt,name=residence_type,json=residenceType,proto3,enum=api.typesv2.ResidenceType" json:"residence_type,omitempty"`
	// applicable only for rental and PG residence type
	MonthlyRent *money.Money `protobuf:"bytes,5,opt,name=monthly_rent,json=monthlyRent,proto3" json:"monthly_rent,omitempty"`
}

func (x *OnboardingData_AddressDetails) Reset() {
	*x = OnboardingData_AddressDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnboardingData_AddressDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingData_AddressDetails) ProtoMessage() {}

func (x *OnboardingData_AddressDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingData_AddressDetails.ProtoReflect.Descriptor instead.
func (*OnboardingData_AddressDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{17, 0}
}

func (x *OnboardingData_AddressDetails) GetAddressDetails() *typesv2.PostalAddress {
	if x != nil {
		return x.AddressDetails
	}
	return nil
}

func (x *OnboardingData_AddressDetails) GetAddressType() typesv2.AddressType {
	if x != nil {
		return x.AddressType
	}
	return typesv2.AddressType(0)
}

func (x *OnboardingData_AddressDetails) GetLocationToken() string {
	if x != nil {
		return x.LocationToken
	}
	return ""
}

func (x *OnboardingData_AddressDetails) GetResidenceType() typesv2.ResidenceType {
	if x != nil {
		return x.ResidenceType
	}
	return typesv2.ResidenceType(0)
}

func (x *OnboardingData_AddressDetails) GetMonthlyRent() *money.Money {
	if x != nil {
		return x.MonthlyRent
	}
	return nil
}

type OnboardingData_EmploymentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Occupation       typesv2.EmploymentType `protobuf:"varint,1,opt,name=occupation,proto3,enum=api.typesv2.EmploymentType" json:"occupation,omitempty"`
	OrganizationName string                 `protobuf:"bytes,2,opt,name=organization_name,json=organizationName,proto3" json:"organization_name,omitempty"`
	MonthlyIncome    *money.Money           `protobuf:"bytes,3,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
	WorkEmail        string                 `protobuf:"bytes,4,opt,name=work_email,json=workEmail,proto3" json:"work_email,omitempty"`
	OfficeAddress    *typesv2.PostalAddress `protobuf:"bytes,5,opt,name=office_address,json=officeAddress,proto3" json:"office_address,omitempty"`
	AnnualRevenue    *money.Money           `protobuf:"bytes,6,opt,name=annual_revenue,json=annualRevenue,proto3" json:"annual_revenue,omitempty"`
	GSTIN            string                 `protobuf:"bytes,7,opt,name=GSTIN,proto3" json:"GSTIN,omitempty"`
}

func (x *OnboardingData_EmploymentDetails) Reset() {
	*x = OnboardingData_EmploymentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnboardingData_EmploymentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingData_EmploymentDetails) ProtoMessage() {}

func (x *OnboardingData_EmploymentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingData_EmploymentDetails.ProtoReflect.Descriptor instead.
func (*OnboardingData_EmploymentDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{17, 1}
}

func (x *OnboardingData_EmploymentDetails) GetOccupation() typesv2.EmploymentType {
	if x != nil {
		return x.Occupation
	}
	return typesv2.EmploymentType(0)
}

func (x *OnboardingData_EmploymentDetails) GetOrganizationName() string {
	if x != nil {
		return x.OrganizationName
	}
	return ""
}

func (x *OnboardingData_EmploymentDetails) GetMonthlyIncome() *money.Money {
	if x != nil {
		return x.MonthlyIncome
	}
	return nil
}

func (x *OnboardingData_EmploymentDetails) GetWorkEmail() string {
	if x != nil {
		return x.WorkEmail
	}
	return ""
}

func (x *OnboardingData_EmploymentDetails) GetOfficeAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.OfficeAddress
	}
	return nil
}

func (x *OnboardingData_EmploymentDetails) GetAnnualRevenue() *money.Money {
	if x != nil {
		return x.AnnualRevenue
	}
	return nil
}

func (x *OnboardingData_EmploymentDetails) GetGSTIN() string {
	if x != nil {
		return x.GSTIN
	}
	return ""
}

type OnboardingData_BankingDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountNumber     string `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	AccountHolderName string `protobuf:"bytes,2,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	IfscCode          string `protobuf:"bytes,3,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
	BankName          string `protobuf:"bytes,4,opt,name=bank_name,json=bankName,proto3" json:"bank_name,omitempty"`
}

func (x *OnboardingData_BankingDetails) Reset() {
	*x = OnboardingData_BankingDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnboardingData_BankingDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingData_BankingDetails) ProtoMessage() {}

func (x *OnboardingData_BankingDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingData_BankingDetails.ProtoReflect.Descriptor instead.
func (*OnboardingData_BankingDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{17, 2}
}

func (x *OnboardingData_BankingDetails) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *OnboardingData_BankingDetails) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

func (x *OnboardingData_BankingDetails) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

func (x *OnboardingData_BankingDetails) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

type MandateData_BankingDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of accounts used by the user during the mandate setup flow
	// by default federal account will be populated
	// user can choose to add a new account, details should be reflected in this map
	AlternateAccDetails map[string]*MandateData_BankingDetails_AccountDetails `protobuf:"bytes,1,rep,name=alternate_acc_details,json=alternateAccDetails,proto3" json:"alternate_acc_details,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// acc details which was finally used/selected by the user
	// and was also sent to vendor
	// this acc can be Fi-Federal account or non Fi-Federal account
	FinalAccDetailsUsed *MandateData_BankingDetails_AccountDetails `protobuf:"bytes,2,opt,name=final_acc_details_used,json=finalAccDetailsUsed,proto3" json:"final_acc_details_used,omitempty"`
}

func (x *MandateData_BankingDetails) Reset() {
	*x = MandateData_BankingDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MandateData_BankingDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MandateData_BankingDetails) ProtoMessage() {}

func (x *MandateData_BankingDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MandateData_BankingDetails.ProtoReflect.Descriptor instead.
func (*MandateData_BankingDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{18, 0}
}

func (x *MandateData_BankingDetails) GetAlternateAccDetails() map[string]*MandateData_BankingDetails_AccountDetails {
	if x != nil {
		return x.AlternateAccDetails
	}
	return nil
}

func (x *MandateData_BankingDetails) GetFinalAccDetailsUsed() *MandateData_BankingDetails_AccountDetails {
	if x != nil {
		return x.FinalAccDetailsUsed
	}
	return nil
}

type MandateData_BankingDetails_AccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountNumber     string `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	AccountHolderName string `protobuf:"bytes,2,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	IfscCode          string `protobuf:"bytes,3,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
	BankName          string `protobuf:"bytes,4,opt,name=bank_name,json=bankName,proto3" json:"bank_name,omitempty"`
}

func (x *MandateData_BankingDetails_AccountDetails) Reset() {
	*x = MandateData_BankingDetails_AccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MandateData_BankingDetails_AccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MandateData_BankingDetails_AccountDetails) ProtoMessage() {}

func (x *MandateData_BankingDetails_AccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MandateData_BankingDetails_AccountDetails.ProtoReflect.Descriptor instead.
func (*MandateData_BankingDetails_AccountDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{18, 0, 0}
}

func (x *MandateData_BankingDetails_AccountDetails) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *MandateData_BankingDetails_AccountDetails) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

func (x *MandateData_BankingDetails_AccountDetails) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

func (x *MandateData_BankingDetails_AccountDetails) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

type ItrIncomeInfo_ItrAttemptData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttemptId string                  `protobuf:"bytes,1,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	ItrStatus ItrIncomeInfo_ItrStatus `protobuf:"varint,2,opt,name=itr_status,json=itrStatus,proto3,enum=preapprovedloan.ItrIncomeInfo_ItrStatus" json:"itr_status,omitempty"`
}

func (x *ItrIncomeInfo_ItrAttemptData) Reset() {
	*x = ItrIncomeInfo_ItrAttemptData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItrIncomeInfo_ItrAttemptData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItrIncomeInfo_ItrAttemptData) ProtoMessage() {}

func (x *ItrIncomeInfo_ItrAttemptData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItrIncomeInfo_ItrAttemptData.ProtoReflect.Descriptor instead.
func (*ItrIncomeInfo_ItrAttemptData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{24, 0}
}

func (x *ItrIncomeInfo_ItrAttemptData) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *ItrIncomeInfo_ItrAttemptData) GetItrStatus() ItrIncomeInfo_ItrStatus {
	if x != nil {
		return x.ItrStatus
	}
	return ItrIncomeInfo_ITR_STATUS_UNSPECIFIED
}

type ItrIncomeInfo_ItrIntimationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the taxpayer
	Name *common.Name `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// phone number of the taxpayer
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// pan number of the taxpayer
	Pan string `protobuf:"bytes,3,opt,name=pan,proto3" json:"pan,omitempty"`
	// gross total income provided by the taxpayer
	GrossTotalIncomeProvided *typesv2.Money `protobuf:"bytes,4,opt,name=gross_total_income_provided,json=grossTotalIncomeProvided,proto3" json:"gross_total_income_provided,omitempty"`
	// gross total income computed under section 143(1)
	GrossTotalIncomeComputed *typesv2.Money `protobuf:"bytes,5,opt,name=gross_total_income_computed,json=grossTotalIncomeComputed,proto3" json:"gross_total_income_computed,omitempty"`
	// total income provided by the taxpayer
	TotalIncomeProvided *typesv2.Money `protobuf:"bytes,6,opt,name=total_income_provided,json=totalIncomeProvided,proto3" json:"total_income_provided,omitempty"`
	// total income computed under section 143(1)
	TotalIncomeComputed *typesv2.Money         `protobuf:"bytes,7,opt,name=total_income_computed,json=totalIncomeComputed,proto3" json:"total_income_computed,omitempty"`
	DateOfFiling        *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=date_of_filing,json=dateOfFiling,proto3" json:"date_of_filing,omitempty"`
	AssessmentYear      string                 `protobuf:"bytes,9,opt,name=assessment_year,json=assessmentYear,proto3" json:"assessment_year,omitempty"`
	ItrFormType         ItrFormType            `protobuf:"varint,10,opt,name=itr_form_type,json=itrFormType,proto3,enum=preapprovedloan.ItrFormType" json:"itr_form_type,omitempty"`
	// status of the taxpayer - "Individual"
	TaxpayerStatus ItrTaxpayerStatus `protobuf:"varint,11,opt,name=taxpayer_status,json=taxpayerStatus,proto3,enum=preapprovedloan.ItrTaxpayerStatus" json:"taxpayer_status,omitempty"`
	// residential status of the taxpayer - "Resident"
	ResidentialStatus ItrTaxpayerResidentialStatus `protobuf:"varint,12,opt,name=residential_status,json=residentialStatus,proto3,enum=preapprovedloan.ItrTaxpayerResidentialStatus" json:"residential_status,omitempty"`
}

func (x *ItrIncomeInfo_ItrIntimationData) Reset() {
	*x = ItrIncomeInfo_ItrIntimationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItrIncomeInfo_ItrIntimationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItrIncomeInfo_ItrIntimationData) ProtoMessage() {}

func (x *ItrIncomeInfo_ItrIntimationData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItrIncomeInfo_ItrIntimationData.ProtoReflect.Descriptor instead.
func (*ItrIncomeInfo_ItrIntimationData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{24, 1}
}

func (x *ItrIncomeInfo_ItrIntimationData) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *ItrIncomeInfo_ItrIntimationData) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *ItrIncomeInfo_ItrIntimationData) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *ItrIncomeInfo_ItrIntimationData) GetGrossTotalIncomeProvided() *typesv2.Money {
	if x != nil {
		return x.GrossTotalIncomeProvided
	}
	return nil
}

func (x *ItrIncomeInfo_ItrIntimationData) GetGrossTotalIncomeComputed() *typesv2.Money {
	if x != nil {
		return x.GrossTotalIncomeComputed
	}
	return nil
}

func (x *ItrIncomeInfo_ItrIntimationData) GetTotalIncomeProvided() *typesv2.Money {
	if x != nil {
		return x.TotalIncomeProvided
	}
	return nil
}

func (x *ItrIncomeInfo_ItrIntimationData) GetTotalIncomeComputed() *typesv2.Money {
	if x != nil {
		return x.TotalIncomeComputed
	}
	return nil
}

func (x *ItrIncomeInfo_ItrIntimationData) GetDateOfFiling() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfFiling
	}
	return nil
}

func (x *ItrIncomeInfo_ItrIntimationData) GetAssessmentYear() string {
	if x != nil {
		return x.AssessmentYear
	}
	return ""
}

func (x *ItrIncomeInfo_ItrIntimationData) GetItrFormType() ItrFormType {
	if x != nil {
		return x.ItrFormType
	}
	return ItrFormType_ITR_FORM_TYPE_UNSPECIFIED
}

func (x *ItrIncomeInfo_ItrIntimationData) GetTaxpayerStatus() ItrTaxpayerStatus {
	if x != nil {
		return x.TaxpayerStatus
	}
	return ItrTaxpayerStatus_ITR_TAXPAYER_STATUS_UNSPECIFIED
}

func (x *ItrIncomeInfo_ItrIntimationData) GetResidentialStatus() ItrTaxpayerResidentialStatus {
	if x != nil {
		return x.ResidentialStatus
	}
	return ItrTaxpayerResidentialStatus_ITR_TAXPAYER_RESIDENTIAL_STATUS_UNSPECIFIED
}

type OtpVerificationData_OtpData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token           string              `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	OtpType         OtpType             `protobuf:"varint,2,opt,name=otp_type,json=otpType,proto3,enum=preapprovedloan.OtpType" json:"otp_type,omitempty"`
	OtpStatus       OtpStatus           `protobuf:"varint,3,opt,name=otp_status,json=otpStatus,proto3,enum=preapprovedloan.OtpStatus" json:"otp_status,omitempty"`
	PhoneNumber     *common.PhoneNumber `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Email           string              `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	VendorSessionId string              `protobuf:"bytes,6,opt,name=vendor_session_id,json=vendorSessionId,proto3" json:"vendor_session_id,omitempty"`
	// used to store the asset details that are to be pledged after otp verification
	AssetDetails map[string]float64 `protobuf:"bytes,7,rep,name=assetDetails,proto3" json:"assetDetails,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	// can be used to specify conditions that are to be run before starting otp generation.
	// if field is present and evaluates to false then otp is skipped.
	OtpValidationCondition *Condition `protobuf:"bytes,8,opt,name=otp_validation_condition,json=otpValidationCondition,proto3" json:"otp_validation_condition,omitempty"`
	// serial number for the otp
	OtpSerialNo int32 `protobuf:"varint,9,opt,name=otp_serial_no,json=otpSerialNo,proto3" json:"otp_serial_no,omitempty"`
}

func (x *OtpVerificationData_OtpData) Reset() {
	*x = OtpVerificationData_OtpData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OtpVerificationData_OtpData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OtpVerificationData_OtpData) ProtoMessage() {}

func (x *OtpVerificationData_OtpData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OtpVerificationData_OtpData.ProtoReflect.Descriptor instead.
func (*OtpVerificationData_OtpData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{30, 0}
}

func (x *OtpVerificationData_OtpData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *OtpVerificationData_OtpData) GetOtpType() OtpType {
	if x != nil {
		return x.OtpType
	}
	return OtpType_OTP_TYPE_UNSPECIFIED
}

func (x *OtpVerificationData_OtpData) GetOtpStatus() OtpStatus {
	if x != nil {
		return x.OtpStatus
	}
	return OtpStatus_OTP_STATUS_UNSPECIFIED
}

func (x *OtpVerificationData_OtpData) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *OtpVerificationData_OtpData) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *OtpVerificationData_OtpData) GetVendorSessionId() string {
	if x != nil {
		return x.VendorSessionId
	}
	return ""
}

func (x *OtpVerificationData_OtpData) GetAssetDetails() map[string]float64 {
	if x != nil {
		return x.AssetDetails
	}
	return nil
}

func (x *OtpVerificationData_OtpData) GetOtpValidationCondition() *Condition {
	if x != nil {
		return x.OtpValidationCondition
	}
	return nil
}

func (x *OtpVerificationData_OtpData) GetOtpSerialNo() int32 {
	if x != nil {
		return x.OtpSerialNo
	}
	return 0
}

type VendorPWAStagesStepData_StageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the pwa stage name
	StageName VendorPWAStagesStepData_PWAStage `protobuf:"varint,1,opt,name=stage_name,json=stageName,proto3,enum=preapprovedloan.VendorPWAStagesStepData_PWAStage" json:"stage_name,omitempty"`
	// denotes the current status of pwa stage.
	StageStatus VendorPWAStagesStepData_PWAStageStatus `protobuf:"varint,2,opt,name=stage_status,json=stageStatus,proto3,enum=preapprovedloan.VendorPWAStagesStepData_PWAStageStatus" json:"stage_status,omitempty"`
	// denotes when the stage status was updated in our system.
	// **Note** : this does not denotes stage update time at vendor's end.
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// denoted the exact stage name which is being piped from Vendor itself
	VendorStageName string `protobuf:"bytes,4,opt,name=vendor_stage_name,json=vendorStageName,proto3" json:"vendor_stage_name,omitempty"`
	// denotes the vendor updated_at timestamp for the event received
	VendorUpdatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=vendor_updated_at,json=vendorUpdatedAt,proto3" json:"vendor_updated_at,omitempty"`
}

func (x *VendorPWAStagesStepData_StageInfo) Reset() {
	*x = VendorPWAStagesStepData_StageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VendorPWAStagesStepData_StageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VendorPWAStagesStepData_StageInfo) ProtoMessage() {}

func (x *VendorPWAStagesStepData_StageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VendorPWAStagesStepData_StageInfo.ProtoReflect.Descriptor instead.
func (*VendorPWAStagesStepData_StageInfo) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{38, 0}
}

func (x *VendorPWAStagesStepData_StageInfo) GetStageName() VendorPWAStagesStepData_PWAStage {
	if x != nil {
		return x.StageName
	}
	return VendorPWAStagesStepData_PWA_STAGE_UNSPECIFIED
}

func (x *VendorPWAStagesStepData_StageInfo) GetStageStatus() VendorPWAStagesStepData_PWAStageStatus {
	if x != nil {
		return x.StageStatus
	}
	return VendorPWAStagesStepData_PWA_STAGE_STATUS_UNSPECIFIED
}

func (x *VendorPWAStagesStepData_StageInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *VendorPWAStagesStepData_StageInfo) GetVendorStageName() string {
	if x != nil {
		return x.VendorStageName
	}
	return ""
}

func (x *VendorPWAStagesStepData_StageInfo) GetVendorUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.VendorUpdatedAt
	}
	return nil
}

type LamfLoanDetailsVerificationData_PfFetchData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqId          string                 `protobuf:"bytes,1,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	FetchCompleted bool                   `protobuf:"varint,2,opt,name=fetch_completed,json=fetchCompleted,proto3" json:"fetch_completed,omitempty"`
	IsFetchSuccess bool                   `protobuf:"varint,3,opt,name=is_fetch_success,json=isFetchSuccess,proto3" json:"is_fetch_success,omitempty"`
	CompletionTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=completion_time,json=completionTime,proto3" json:"completion_time,omitempty"`
}

func (x *LamfLoanDetailsVerificationData_PfFetchData) Reset() {
	*x = LamfLoanDetailsVerificationData_PfFetchData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LamfLoanDetailsVerificationData_PfFetchData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LamfLoanDetailsVerificationData_PfFetchData) ProtoMessage() {}

func (x *LamfLoanDetailsVerificationData_PfFetchData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LamfLoanDetailsVerificationData_PfFetchData.ProtoReflect.Descriptor instead.
func (*LamfLoanDetailsVerificationData_PfFetchData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{40, 0}
}

func (x *LamfLoanDetailsVerificationData_PfFetchData) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *LamfLoanDetailsVerificationData_PfFetchData) GetFetchCompleted() bool {
	if x != nil {
		return x.FetchCompleted
	}
	return false
}

func (x *LamfLoanDetailsVerificationData_PfFetchData) GetIsFetchSuccess() bool {
	if x != nil {
		return x.IsFetchSuccess
	}
	return false
}

func (x *LamfLoanDetailsVerificationData_PfFetchData) GetCompletionTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletionTime
	}
	return nil
}

type LamfLoanDetailsVerificationData_MfMobileLinkDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The phone number of all the folios will be updated to this number (given that they are linked to the below email)
	NewMobile *common.PhoneNumber `protobuf:"bytes,1,opt,name=new_mobile,json=newMobile,proto3" json:"new_mobile,omitempty"`
	// all the folios should be linked to this email. This will be used for user authentication.
	LinkedEmail string                                       `protobuf:"bytes,2,opt,name=linked_email,json=linkedEmail,proto3" json:"linked_email,omitempty"`
	Folios      []*LamfLoanDetailsVerificationData_FolioData `protobuf:"bytes,3,rep,name=folios,proto3" json:"folios,omitempty"`
	// list of nft req ids for tracking the nft loan request and its history
	NftReqDetails []*LamfLoanDetailsVerificationData_NftRequestDetails `protobuf:"bytes,4,rep,name=nft_req_details,json=nftReqDetails,proto3" json:"nft_req_details,omitempty"`
}

func (x *LamfLoanDetailsVerificationData_MfMobileLinkDetails) Reset() {
	*x = LamfLoanDetailsVerificationData_MfMobileLinkDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LamfLoanDetailsVerificationData_MfMobileLinkDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LamfLoanDetailsVerificationData_MfMobileLinkDetails) ProtoMessage() {}

func (x *LamfLoanDetailsVerificationData_MfMobileLinkDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LamfLoanDetailsVerificationData_MfMobileLinkDetails.ProtoReflect.Descriptor instead.
func (*LamfLoanDetailsVerificationData_MfMobileLinkDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{40, 1}
}

func (x *LamfLoanDetailsVerificationData_MfMobileLinkDetails) GetNewMobile() *common.PhoneNumber {
	if x != nil {
		return x.NewMobile
	}
	return nil
}

func (x *LamfLoanDetailsVerificationData_MfMobileLinkDetails) GetLinkedEmail() string {
	if x != nil {
		return x.LinkedEmail
	}
	return ""
}

func (x *LamfLoanDetailsVerificationData_MfMobileLinkDetails) GetFolios() []*LamfLoanDetailsVerificationData_FolioData {
	if x != nil {
		return x.Folios
	}
	return nil
}

func (x *LamfLoanDetailsVerificationData_MfMobileLinkDetails) GetNftReqDetails() []*LamfLoanDetailsVerificationData_NftRequestDetails {
	if x != nil {
		return x.NftReqDetails
	}
	return nil
}

type LamfLoanDetailsVerificationData_MfEmailLinkDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The email of all the folios will be updated to this new email (given that they are linked to the below mobile)
	NewEmail string `protobuf:"bytes,1,opt,name=new_email,json=newEmail,proto3" json:"new_email,omitempty"`
	// all the folios should be linked to this phone number. This will be used for user authentication.
	LinkedMobile *common.PhoneNumber                          `protobuf:"bytes,2,opt,name=linked_mobile,json=linkedMobile,proto3" json:"linked_mobile,omitempty"`
	Folios       []*LamfLoanDetailsVerificationData_FolioData `protobuf:"bytes,3,rep,name=folios,proto3" json:"folios,omitempty"`
	// list of nft req ids for tracking the nft loan request and its history
	NftReqDetails []*LamfLoanDetailsVerificationData_NftRequestDetails `protobuf:"bytes,4,rep,name=nft_req_details,json=nftReqDetails,proto3" json:"nft_req_details,omitempty"`
}

func (x *LamfLoanDetailsVerificationData_MfEmailLinkDetails) Reset() {
	*x = LamfLoanDetailsVerificationData_MfEmailLinkDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LamfLoanDetailsVerificationData_MfEmailLinkDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LamfLoanDetailsVerificationData_MfEmailLinkDetails) ProtoMessage() {}

func (x *LamfLoanDetailsVerificationData_MfEmailLinkDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LamfLoanDetailsVerificationData_MfEmailLinkDetails.ProtoReflect.Descriptor instead.
func (*LamfLoanDetailsVerificationData_MfEmailLinkDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{40, 2}
}

func (x *LamfLoanDetailsVerificationData_MfEmailLinkDetails) GetNewEmail() string {
	if x != nil {
		return x.NewEmail
	}
	return ""
}

func (x *LamfLoanDetailsVerificationData_MfEmailLinkDetails) GetLinkedMobile() *common.PhoneNumber {
	if x != nil {
		return x.LinkedMobile
	}
	return nil
}

func (x *LamfLoanDetailsVerificationData_MfEmailLinkDetails) GetFolios() []*LamfLoanDetailsVerificationData_FolioData {
	if x != nil {
		return x.Folios
	}
	return nil
}

func (x *LamfLoanDetailsVerificationData_MfEmailLinkDetails) GetNftReqDetails() []*LamfLoanDetailsVerificationData_NftRequestDetails {
	if x != nil {
		return x.NftReqDetails
	}
	return nil
}

type LamfLoanDetailsVerificationData_FolioData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FolioNumber string `protobuf:"bytes,1,opt,name=folio_number,json=folioNumber,proto3" json:"folio_number,omitempty"`
	Isin        string `protobuf:"bytes,2,opt,name=isin,proto3" json:"isin,omitempty"`
	// units of mf that the user has
	Quantity float64 `protobuf:"fixed64,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Amc      string  `protobuf:"bytes,4,opt,name=amc,proto3" json:"amc,omitempty"`
}

func (x *LamfLoanDetailsVerificationData_FolioData) Reset() {
	*x = LamfLoanDetailsVerificationData_FolioData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LamfLoanDetailsVerificationData_FolioData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LamfLoanDetailsVerificationData_FolioData) ProtoMessage() {}

func (x *LamfLoanDetailsVerificationData_FolioData) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LamfLoanDetailsVerificationData_FolioData.ProtoReflect.Descriptor instead.
func (*LamfLoanDetailsVerificationData_FolioData) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{40, 3}
}

func (x *LamfLoanDetailsVerificationData_FolioData) GetFolioNumber() string {
	if x != nil {
		return x.FolioNumber
	}
	return ""
}

func (x *LamfLoanDetailsVerificationData_FolioData) GetIsin() string {
	if x != nil {
		return x.Isin
	}
	return ""
}

func (x *LamfLoanDetailsVerificationData_FolioData) GetQuantity() float64 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *LamfLoanDetailsVerificationData_FolioData) GetAmc() string {
	if x != nil {
		return x.Amc
	}
	return ""
}

type LamfLoanDetailsVerificationData_NftRequestDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientReqId string                 `protobuf:"bytes,1,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *LamfLoanDetailsVerificationData_NftRequestDetails) Reset() {
	*x = LamfLoanDetailsVerificationData_NftRequestDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LamfLoanDetailsVerificationData_NftRequestDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LamfLoanDetailsVerificationData_NftRequestDetails) ProtoMessage() {}

func (x *LamfLoanDetailsVerificationData_NftRequestDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LamfLoanDetailsVerificationData_NftRequestDetails.ProtoReflect.Descriptor instead.
func (*LamfLoanDetailsVerificationData_NftRequestDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{40, 4}
}

func (x *LamfLoanDetailsVerificationData_NftRequestDetails) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *LamfLoanDetailsVerificationData_NftRequestDetails) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type LamfLoanDetailsVerificationData_UserAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identifier RecordUserActionIdentifier `protobuf:"varint,1,opt,name=identifier,proto3,enum=preapprovedloan.RecordUserActionIdentifier" json:"identifier,omitempty"`
}

func (x *LamfLoanDetailsVerificationData_UserAction) Reset() {
	*x = LamfLoanDetailsVerificationData_UserAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LamfLoanDetailsVerificationData_UserAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LamfLoanDetailsVerificationData_UserAction) ProtoMessage() {}

func (x *LamfLoanDetailsVerificationData_UserAction) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_step_execution_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LamfLoanDetailsVerificationData_UserAction.ProtoReflect.Descriptor instead.
func (*LamfLoanDetailsVerificationData_UserAction) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP(), []int{40, 5}
}

func (x *LamfLoanDetailsVerificationData_UserAction) GetIdentifier() RecordUserActionIdentifier {
	if x != nil {
		return x.Identifier
	}
	return RecordUserActionIdentifier_RECORD_USER_ACTION_IDENTIFIER_UNSPECIFIED
}

var File_api_preapprovedloan_loan_step_execution_proto protoreflect.FileDescriptor

var file_api_preapprovedloan_loan_step_execution_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x5f,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xad, 0x06, 0x0a, 0x11, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x04, 0x66, 0x6c, 0x6f, 0x77,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x65,
	0x70, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x04,
	0x66, 0x6c, 0x6f, 0x77, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x63, 0x68, 0x49, 0x64, 0x12, 0x47, 0x0a,
	0x09, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2a, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x08, 0x73, 0x74,
	0x65, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74,
	0x65, 0x70, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x40, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f,
	0x61, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a,
	0x0a, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09,
	0x73, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x73, 0x74, 0x61,
	0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x3c, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x22, 0xa3, 0x02, 0x0a, 0x10, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x65,
	0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x75, 0x0a, 0x18, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x6d,
	0x61, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x15, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x1a, 0x67, 0x0a,
	0x1a, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4f, 0x66, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x32, 0x0a, 0x11, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x64, 0x22, 0xb6, 0x05, 0x0a, 0x0d, 0x45,
	0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08,
	0x73, 0x69, 0x67, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x69, 0x67, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x37, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x79, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x41, 0x74,
	0x12, 0x72, 0x0a, 0x18, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x15, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x64, 0x4d, 0x61, 0x70, 0x12, 0x33, 0x0a, 0x08, 0x6f, 0x74, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4f, 0x74, 0x70, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x07, 0x6f, 0x74, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x77,
	0x73, 0x5f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x77, 0x73, 0x44, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x12, 0x3f, 0x0a, 0x0b,
	0x6b, 0x66, 0x73, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x0b, 0x6b, 0x66, 0x73, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x53, 0x0a,
	0x15, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c,
	0x6f, 0x61, 0x6e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x15, 0x6c, 0x6f, 0x61,
	0x6e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x56, 0x0a, 0x19, 0x72, 0x6f, 0x69, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x17, 0x72, 0x6f, 0x69, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x67, 0x0a, 0x1a, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x66, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xc1, 0x01, 0x0a, 0x0c, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x69, 0x67, 0x6e, 0x55, 0x72, 0x6c, 0x12,
	0x30, 0x0a, 0x14, 0x61, 0x77, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61,
	0x77, 0x73, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x37, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x61, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x41, 0x74, 0x12, 0x2b, 0x0a, 0x12, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x73, 0x33, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x44, 0x6f,
	0x63, 0x53, 0x33, 0x50, 0x61, 0x74, 0x68, 0x22, 0xa5, 0x01, 0x0a, 0x07, 0x4f, 0x74, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78,
	0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0d, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x28, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x64, 0x5f,
	0x6f, 0x74, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x45,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x64, 0x4f, 0x74, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22,
	0xd7, 0x02, 0x0a, 0x0c, 0x56, 0x6b, 0x79, 0x63, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x2b, 0x0a, 0x0f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x71, 0x0a,
	0x18, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x38, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x56, 0x6b, 0x79, 0x63, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x15, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x4d, 0x61, 0x70,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64,
	0x1a, 0x67, 0x0a, 0x1a, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x33, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x66, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x94, 0x01, 0x0a, 0x0b, 0x4b, 0x79,
	0x63, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x6b, 0x79, 0x63,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6b, 0x79, 0x63, 0x55,
	0x72, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x6b, 0x79, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6b, 0x79, 0x63,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x10, 0x75, 0x72,
	0x6c, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0e, 0x75, 0x72, 0x6c, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x22, 0xcd, 0x07, 0x0a, 0x0c, 0x43, 0x6b, 0x79, 0x63, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6b, 0x79, 0x63,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x44, 0x0a, 0x0f, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x6b, 0x79, 0x63, 0x49, 0x64, 0x12, 0x64, 0x0a, 0x15, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x43, 0x6b, 0x79, 0x63, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x14, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x76, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x50, 0x41, 0x4e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x50, 0x41, 0x4e,
	0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2b,
	0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x03, 0x64,
	0x6f, 0x62, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x03, 0x64, 0x6f, 0x62,
	0x12, 0x47, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65,
	0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x51, 0x0a, 0x16, 0x63, 0x6f, 0x72,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x15, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3b, 0x0a, 0x0c,
	0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0b, 0x66, 0x61,
	0x74, 0x68, 0x65, 0x72, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x6d, 0x6f, 0x74,
	0x68, 0x65, 0x72, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x5f, 0x6b, 0x79, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4b, 0x79, 0x63, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x6b, 0x79,
	0x63, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f,
	0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0a, 0x6b, 0x79, 0x63,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x37, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x63, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x49, 0x64, 0x66, 0x63, 0x43, 0x6b, 0x79, 0x63,
	0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x04, 0x69, 0x64, 0x66, 0x63,
	0x12, 0x37, 0x0a, 0x04, 0x61, 0x62, 0x66, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x41, 0x62, 0x66, 0x6c, 0x43, 0x6b, 0x79, 0x63, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x04, 0x61, 0x62, 0x66, 0x6c, 0x12, 0x3a, 0x0a, 0x07, 0x73, 0x67, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x53, 0x67, 0x43,
	0x6b, 0x79, 0x63, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x06, 0x73,
	0x67, 0x44, 0x61, 0x74, 0x61, 0x42, 0x19, 0x0a, 0x17, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x22, 0x4c, 0x0a, 0x0f, 0x41, 0x62, 0x66, 0x6c, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x0a, 0x72, 0x70, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x70, 0x43, 0x75, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x22, 0x28,
	0x0a, 0x0d, 0x53, 0x67, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x17, 0x0a, 0x07, 0x6f, 0x72, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x72, 0x63, 0x68, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x0e, 0x46, 0x65, 0x64, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3e, 0x0a, 0x1c, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x6f, 0x72,
	0x63, 0x68, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x18, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x4f, 0x72, 0x63, 0x68, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xbc, 0x01, 0x0a, 0x0d, 0x4c,
	0x4c, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a,
	0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x12, 0x63, 0x0a, 0x20, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x1d, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x72, 0x0a, 0x10, 0x49, 0x64, 0x66,
	0x63, 0x43, 0x6b, 0x79, 0x63, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5e, 0x0a,
	0x15, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x49,
	0x64, 0x66, 0x63, 0x43, 0x6b, 0x79, 0x63, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x50, 0x69,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x50, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x74, 0x0a,
	0x0e, 0x53, 0x67, 0x43, 0x6b, 0x79, 0x63, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x2d, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x6b, 0x79, 0x63, 0x5f, 0x61, 0x6c, 0x72, 0x65, 0x61, 0x64,
	0x79, 0x5f, 0x64, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73,
	0x4b, 0x79, 0x63, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x44, 0x6f, 0x6e, 0x65, 0x12, 0x33,
	0x0a, 0x08, 0x6b, 0x79, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4b, 0x79, 0x63, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x6b, 0x79, 0x63, 0x54,
	0x79, 0x70, 0x65, 0x22, 0xf9, 0x05, 0x0a, 0x10, 0x41, 0x62, 0x66, 0x6c, 0x43, 0x6b, 0x79, 0x63,
	0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x61, 0x64, 0x68, 0x61,
	0x61, 0x72, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x42, 0x61,
	0x73, 0x65, 0x36, 0x34, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x68, 0x6f,
	0x74, 0x6f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x70, 0x68, 0x6f, 0x74,
	0x6f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x42, 0x61, 0x73, 0x65, 0x36, 0x34, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x33, 0x0a, 0x16, 0x65, 0x6b, 0x79, 0x63, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x62,
	0x61, 0x73, 0x65, 0x36, 0x34, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x13, 0x65, 0x6b, 0x79, 0x63, 0x41, 0x75, 0x74, 0x68, 0x42, 0x61, 0x73, 0x65, 0x36,
	0x34, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x61, 0x6e, 0x5f, 0x62, 0x61,
	0x73, 0x65, 0x36, 0x34, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x70, 0x61, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x36, 0x34, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x3f, 0x0a, 0x1c, 0x64, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x63, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x64, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x4c,
	0x69, 0x63, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x36, 0x34, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x34, 0x0a, 0x16, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x62,
	0x61, 0x73, 0x65, 0x36, 0x34, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x14, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x36, 0x34, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x76, 0x6f, 0x74, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x76, 0x6f, 0x74, 0x65, 0x72, 0x49, 0x64, 0x42,
	0x61, 0x73, 0x65, 0x36, 0x34, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x61,
	0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x5f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x70, 0x61, 0x73, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x42, 0x61, 0x73, 0x65, 0x36, 0x34, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x47,
	0x0a, 0x11, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x4f, 0x0a, 0x15, 0x63, 0x6f, 0x72, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x14, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x35, 0x0a, 0x09, 0x66, 0x75,
	0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x69,
	0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6b, 0x79, 0x63, 0x49, 0x64, 0x22,
	0x87, 0x04, 0x0a, 0x14, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x0a, 0x0f, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x79, 0x0a, 0x18, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x15, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x4d, 0x61, 0x70,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x60, 0x0a, 0x10, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x67, 0x0a, 0x1a, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x66, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x64, 0x0a, 0x0f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x3b, 0x0a, 0x0b,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x41, 0x74, 0x22, 0xec, 0x08, 0x0a, 0x0e, 0x4f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x57, 0x0a, 0x0f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x60, 0x0a, 0x12, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x61,
	0x74, 0x61, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x57, 0x0a, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x0e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x1a, 0xb3, 0x02, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x43, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61,
	0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x41, 0x0a, 0x0e,
	0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x52, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0d, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x35, 0x0a, 0x0c, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x6c, 0x79, 0x52, 0x65, 0x6e, 0x74, 0x1a, 0xeb, 0x02, 0x0a, 0x11, 0x45, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3b, 0x0a, 0x0a,
	0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x45,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6f,
	0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c,
	0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0d, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x49, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x41, 0x0a, 0x0e, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x0d, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x39, 0x0a, 0x0e, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x72, 0x65,
	0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0d, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x47, 0x53, 0x54, 0x49, 0x4e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x47,
	0x53, 0x54, 0x49, 0x4e, 0x1a, 0xa1, 0x01, 0x0a, 0x0e, 0x42, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e,
	0x0a, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x66, 0x73, 0x63, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x69, 0x66, 0x73, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x93, 0x0f, 0x0a, 0x0b, 0x4d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x54,
	0x78, 0x6e, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0e, 0x6d, 0x61, 0x78, 0x5f, 0x74, 0x78, 0x6e, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0c, 0x6d, 0x61, 0x78, 0x54, 0x78, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4a,
	0x0a, 0x13, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x11, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6e, 0x6b, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x12, 0x36, 0x0a, 0x04, 0x61, 0x62,
	0x66, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x41, 0x62, 0x66, 0x6c, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x04, 0x61, 0x62,
	0x66, 0x6c, 0x12, 0x48, 0x0a, 0x0f, 0x6c, 0x6c, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x4c,
	0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0d, 0x6c,
	0x6c, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x48, 0x0a, 0x0f,
	0x73, 0x67, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x53, 0x67, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0d, 0x73, 0x67, 0x4d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4b, 0x0a, 0x10, 0x66, 0x65, 0x64, 0x5f, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x46, 0x65, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x0e, 0x66, 0x65, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x54, 0x0a, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4b, 0x0a, 0x23, 0x6d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x66, 0x69, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x57,
	0x69, 0x74, 0x68, 0x46, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x41,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x97, 0x01, 0x0a, 0x28, 0x66, 0x69, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65,
	0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x46, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x4d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x23, 0x66, 0x69, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65,
	0x46, 0x6f, 0x72, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x44, 0x0a, 0x10, 0x75, 0x72, 0x6c, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x75, 0x72, 0x6c, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x45, 0x0a, 0x0c, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x1a, 0xa4, 0x04,
	0x0a, 0x0e, 0x42, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x78, 0x0a, 0x15, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x63,
	0x63, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x44, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x42, 0x61,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x41, 0x6c, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65,
	0x41, 0x63, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x6f, 0x0a, 0x16, 0x66, 0x69,
	0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f,
	0x75, 0x73, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x13, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x55, 0x73, 0x65, 0x64, 0x1a, 0xa1, 0x01, 0x0a, 0x0e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x66, 0x73, 0x63, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x66, 0x73, 0x63, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x1a,
	0x82, 0x01, 0x0a, 0x18, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x50,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x42, 0x61, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xec, 0x02, 0x0a, 0x23, 0x46, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x34,
	0x46, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49,
	0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x3b, 0x0a, 0x37, 0x46, 0x49, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x4d, 0x49, 0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52,
	0x59, 0x10, 0x01, 0x12, 0x37, 0x0a, 0x33, 0x46, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x44, 0x55, 0x45, 0x10, 0x02, 0x12, 0x44, 0x0a, 0x40,
	0x46, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49,
	0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c,
	0x10, 0x03, 0x12, 0x4f, 0x0a, 0x4b, 0x46, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f,
	0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x44,
	0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53,
	0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4e, 0x45, 0x45, 0x44, 0x45,
	0x44, 0x10, 0x04, 0x42, 0x19, 0x0a, 0x17, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xd9,
	0x11, 0x0a, 0x18, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x51, 0x0a, 0x12, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x10, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x55,
	0x0a, 0x14, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x65,
	0x70, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x46,
	0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x11, 0x66, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x65,
	0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x49, 0x0a, 0x10, 0x65, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x5f,
	0x73, 0x74, 0x65, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x0d, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x45, 0x0a, 0x0e, 0x76, 0x6b, 0x79, 0x63, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x56, 0x6b, 0x79, 0x63, 0x53,
	0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0c, 0x76, 0x6b, 0x79, 0x63, 0x53,
	0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5e, 0x0a, 0x17, 0x6d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x14, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53,
	0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4a, 0x0a, 0x0f, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x0e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x41, 0x0a, 0x0c, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b, 0x6d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3e, 0x0a, 0x0b, 0x68, 0x75, 0x6e, 0x74, 0x65, 0x72,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x48, 0x75,
	0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0a, 0x68, 0x75, 0x6e, 0x74,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x0e, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x73,
	0x74, 0x65, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x43, 0x6b, 0x79, 0x63, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x0c, 0x63, 0x6b, 0x79, 0x63, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4a, 0x0a,
	0x0f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0e, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x47, 0x0a, 0x0e, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x5a, 0x0a, 0x15, 0x6f, 0x74, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x4f, 0x74, 0x70, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x13, 0x6f, 0x74, 0x70, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x35,
	0x0a, 0x08, 0x62, 0x72, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x42, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x07, 0x62, 0x72,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3e, 0x0a, 0x0b, 0x73, 0x65, 0x6c, 0x66, 0x69, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x53, 0x65, 0x6c,
	0x66, 0x69, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x6c, 0x66, 0x69,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x58, 0x0a, 0x15, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x6c, 0x65, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61,
	0x64, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x12, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x68, 0x0a, 0x1b, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x70, 0x77, 0x61, 0x5f, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x73, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x50, 0x57, 0x41,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x73, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x17, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x50, 0x77, 0x61, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x73, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x42, 0x0a, 0x0d, 0x6b, 0x79, 0x63,
	0x5f, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4b, 0x79, 0x63, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x0b, 0x6b, 0x79, 0x63, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x57, 0x0a,
	0x14, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x49, 0x6e,
	0x63, 0x6f, 0x6d, 0x65, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x12, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x41, 0x0a, 0x0c, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61,
	0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x41,
	0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b, 0x61, 0x61,
	0x64, 0x68, 0x61, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x73, 0x0a, 0x1e, 0x6c, 0x6f, 0x61,
	0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x1b, 0x6c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x7d,
	0x0a, 0x22, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6c,
	0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1e, 0x72,
	0x65, 0x73, 0x65, 0x74, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x6c, 0x0a,
	0x1b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x19, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x60, 0x0a, 0x17, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x15, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5a, 0x0a,
	0x15, 0x72, 0x6f, 0x69, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x52,
	0x4f, 0x49, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x52, 0x13, 0x72, 0x6f, 0x69, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x57, 0x0a, 0x14, 0x70, 0x72, 0x65,
	0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x50, 0x72, 0x65, 0x45, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x12,
	0x70, 0x72, 0x65, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x04, 0x61, 0x62, 0x66, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x41, 0x62, 0x66, 0x6c, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x69, 0x73, 0x62,
	0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x48, 0x01, 0x52, 0x04,
	0x61, 0x62, 0x66, 0x6c, 0x42, 0x09, 0x0a, 0x07, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42,
	0x19, 0x0a, 0x17, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x53, 0x0a, 0x12, 0x50, 0x72,
	0x65, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x3d, 0x0a, 0x1b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x18, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x88, 0x01, 0x0a, 0x0b, 0x41, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x36, 0x0a, 0x04, 0x61, 0x62, 0x66, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x41, 0x62, 0x66, 0x6c, 0x41, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x04, 0x61, 0x62, 0x66, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x66, 0x6f, 0x75, 0x72, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x44, 0x69, 0x67, 0x69, 0x74, 0x42,
	0x19, 0x0a, 0x17, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xe8, 0x02, 0x0a, 0x0f, 0x41,
	0x62, 0x66, 0x6c, 0x41, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a,
	0x0e, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x70, 0x74, 0x75,
	0x72, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x34, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x10, 0x0a, 0x03,
	0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x2b,
	0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74, 0x72,
	0x65, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x03, 0x64, 0x6f, 0x62, 0x22, 0xae, 0x03, 0x0a, 0x12, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65,
	0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x10,
	0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x70, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72,
	0x61, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f,
	0x0a, 0x12, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x49, 0x6e, 0x63,
	0x6f, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x10, 0x69,
	0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x4c, 0x0a, 0x1c, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x19, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x0a,
	0x0f, 0x69, 0x74, 0x72, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x49, 0x74, 0x72, 0x49, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x69, 0x74, 0x72, 0x49, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f, 0x0a, 0x14, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x5f, 0x6f, 0x72, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x11, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x4f, 0x72, 0x63, 0x68,
	0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xa7, 0x0a, 0x0a, 0x0d, 0x49, 0x74, 0x72, 0x49, 0x6e,
	0x63, 0x6f, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x57, 0x0a, 0x10, 0x69, 0x74, 0x72, 0x5f,
	0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x49, 0x74, 0x72, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x2e, 0x49, 0x74, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x0e, 0x69, 0x74, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x57, 0x0a, 0x0e, 0x69, 0x74, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x69, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x49, 0x74, 0x72, 0x49,
	0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x49, 0x74, 0x72, 0x49, 0x6e, 0x74,
	0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x69, 0x74, 0x72,
	0x49, 0x6e, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x78, 0x0a, 0x0e, 0x49, 0x74,
	0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0a, 0x69,
	0x74, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x28, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x49, 0x74, 0x72, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e,
	0x49, 0x74, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x69, 0x74, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x1a, 0xa5, 0x06, 0x0a, 0x11, 0x49, 0x74, 0x72, 0x49, 0x6e, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52,
	0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x70, 0x61, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x51,
	0x0a, 0x1b, 0x67, 0x72, 0x6f, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e,
	0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x18, 0x67, 0x72, 0x6f, 0x73, 0x73, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x64, 0x12, 0x51, 0x0a, 0x1b, 0x67, 0x72, 0x6f, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x18, 0x67, 0x72, 0x6f, 0x73,
	0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x43, 0x6f, 0x6d, 0x70,
	0x75, 0x74, 0x65, 0x64, 0x12, 0x46, 0x0a, 0x15, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e,
	0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e,
	0x63, 0x6f, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64, 0x12, 0x46, 0x0a, 0x15,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x75, 0x74, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x43, 0x6f, 0x6d, 0x70,
	0x75, 0x74, 0x65, 0x64, 0x12, 0x40, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f,
	0x66, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66,
	0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x73, 0x73, 0x65, 0x73, 0x73,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x61, 0x73, 0x73, 0x65, 0x73, 0x73, 0x6d, 0x65, 0x6e, 0x74, 0x59, 0x65, 0x61, 0x72, 0x12,
	0x40, 0x0a, 0x0d, 0x69, 0x74, 0x72, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x49, 0x74, 0x72, 0x46, 0x6f, 0x72, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x69, 0x74, 0x72, 0x46, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x74, 0x61, 0x78, 0x70, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x49, 0x74, 0x72,
	0x54, 0x61, 0x78, 0x70, 0x61, 0x79, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e,
	0x74, 0x61, 0x78, 0x70, 0x61, 0x79, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5c,
	0x0a, 0x12, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x49, 0x74, 0x72,
	0x54, 0x61, 0x78, 0x70, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x72, 0x65, 0x73, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xc1, 0x01, 0x0a,
	0x09, 0x49, 0x74, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x54,
	0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x54, 0x52, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x15,
	0x0a, 0x11, 0x49, 0x54, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x54, 0x52, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x18, 0x0a,
	0x14, 0x49, 0x54, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x54, 0x52, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x54, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x06,
	0x22, 0x82, 0x01, 0x0a, 0x18, 0x41, 0x62, 0x66, 0x6c, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x69, 0x73,
	0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x24, 0x0a,
	0x0e, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x55, 0x6e, 0x69, 0x71, 0x75,
	0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x61, 0x6c, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x26, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x66, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x4c, 0x0a,
	0x0a, 0x48, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x72,
	0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x22, 0x64, 0x0a, 0x0e, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x52, 0x0a,
	0x11, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b,
	0x75, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x75, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x10, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x75,
	0x70, 0x22, 0x81, 0x04, 0x0a, 0x0d, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65,
	0x73, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66,
	0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a,
	0x0a, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x75, 0x72,
	0x70, 0x6f, 0x73, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x4f, 0x66, 0x4c, 0x6f, 0x61,
	0x6e, 0x12, 0x42, 0x0a, 0x13, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x61,
	0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x11, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x4c, 0x6f, 0x61, 0x6e, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xe1, 0x06, 0x0a, 0x13, 0x4f, 0x74, 0x70, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x47, 0x0a,
	0x08, 0x6f, 0x74, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x4f, 0x74, 0x70, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4f, 0x74, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x6f,
	0x74, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x72,
	0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x74, 0x70, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x11, 0x73, 0x6b, 0x69, 0x70, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x4f, 0x74, 0x70, 0x73, 0x12, 0x3f, 0x0a, 0x1c, 0x6f, 0x74, 0x70, 0x5f, 0x76, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x5f, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x6f, 0x74,
	0x70, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x59, 0x0a, 0x1b, 0x6f, 0x74, 0x70, 0x5f, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x18, 0x6f, 0x74, 0x70, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x1a, 0xb4, 0x04, 0x0a, 0x07, 0x4f, 0x74, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x33, 0x0a, 0x08, 0x6f, 0x74, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4f, 0x74, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x07, 0x6f, 0x74, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x6f, 0x74, 0x70,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x4f, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x6f, 0x74, 0x70, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x2a,
	0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x62, 0x0a, 0x0c, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4f, 0x74, 0x70, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4f, 0x74, 0x70, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x54,
	0x0a, 0x18, 0x6f, 0x74, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x16, 0x6f, 0x74,
	0x70, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x74, 0x70, 0x5f, 0x73, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x74, 0x70,
	0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x1a, 0x3f, 0x0a, 0x11, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb7, 0x01, 0x0a, 0x09, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x57, 0x0a, 0x14, 0x6f, 0x74, 0x70, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4f, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x12, 0x6f, 0x74,
	0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x44, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4e, 0x6f, 0x74, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0c, 0x6e, 0x6f, 0x74, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x63, 0x0a, 0x12, 0x4f, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x74, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x74, 0x70, 0x49, 0x64,
	0x12, 0x36, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4f, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x08,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x22, 0x51, 0x0a, 0x0c, 0x4e, 0x6f, 0x74, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x0e, 0x62, 0x61, 0x73, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x62, 0x61,
	0x73, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x58, 0x0a, 0x07, 0x42,
	0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x65, 0x70, 0x66,
	0x6f, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x65, 0x65, 0x64, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x45, 0x70, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x4e,
	0x65, 0x65, 0x64, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x4a, 0x0a, 0x0a, 0x53, 0x65, 0x6c, 0x66, 0x69, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x3c, 0x0a, 0x0c, 0x73, 0x65, 0x6c, 0x66, 0x69, 0x65, 0x5f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x6c, 0x66, 0x69, 0x65, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x22, 0x7d, 0x0a, 0x09, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x2c,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x0c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x22, 0xc4, 0x03, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x53,
	0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x65, 0x61, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x49, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70,
	0x61, 0x6e, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3b, 0x0a, 0x0f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x64, 0x65,
	0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0f,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0xa8, 0x09, 0x0a, 0x17, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x50, 0x57, 0x41, 0x53, 0x74, 0x61, 0x67, 0x65, 0x73, 0x53, 0x74, 0x65, 0x70, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x53, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x50, 0x57, 0x41, 0x53, 0x74, 0x61, 0x67, 0x65, 0x73, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x77, 0x61, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x77, 0x61, 0x55, 0x72,
	0x6c, 0x12, 0x49, 0x0a, 0x13, 0x70, 0x77, 0x61, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x70, 0x77, 0x61, 0x55,
	0x72, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0xe8, 0x02, 0x0a,
	0x09, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x50, 0x57, 0x41, 0x53, 0x74, 0x61, 0x67, 0x65, 0x73,
	0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x50, 0x57, 0x41, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5a, 0x0a, 0x0c,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x37, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x50, 0x57, 0x41, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x73, 0x53, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x50, 0x57, 0x41,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x73, 0x74, 0x61,
	0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x46, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xd6, 0x03, 0x0a, 0x08, 0x50, 0x57, 0x41, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x15, 0x0a, 0x11, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4d, 0x41, 0x4e,
	0x44, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48,
	0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x4b, 0x59, 0x43, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x53, 0x10, 0x05, 0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x49, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x45,
	0x56, 0x49, 0x45, 0x57, 0x10, 0x06, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x57, 0x41,
	0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x41, 0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x53, 0x49, 0x47, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x08, 0x12, 0x24, 0x0a, 0x20, 0x50, 0x57,
	0x41, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x09,
	0x12, 0x26, 0x0a, 0x22, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x41, 0x50,
	0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x42, 0x4d,
	0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x0a, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x57, 0x41, 0x5f,
	0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x43, 0x45,
	0x49, 0x56, 0x45, 0x44, 0x10, 0x0b, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0c, 0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0d, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x57, 0x41, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x41, 0x4c, 0x10, 0x0e,
	0x22, 0x8f, 0x01, 0x0a, 0x0e, 0x50, 0x57, 0x41, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41,
	0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45,
	0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x57, 0x41, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x03, 0x22, 0x6d, 0x0a, 0x1b, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x46, 0x0a, 0x04, 0x6c, 0x61, 0x6d, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x4c, 0x61, 0x6d, 0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x04, 0x6c, 0x61, 0x6d, 0x66, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0xe7, 0x0c, 0x0a, 0x1f, 0x4c, 0x61, 0x6d, 0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x61, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x6b, 0x65,
	0x6e, 0x12, 0x60, 0x0a, 0x0d, 0x70, 0x66, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x61, 0x6d, 0x66, 0x4c,
	0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x50, 0x66, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x70, 0x66, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x72, 0x0a, 0x11, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x6e,
	0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x4c, 0x61, 0x6d, 0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x2e, 0x4d, 0x66, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x6b,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x6f, 0x0a, 0x10, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x4c, 0x69, 0x6e, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x43, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x61, 0x6d, 0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x4d, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x10, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x6e,
	0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5c, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x4c, 0x61, 0x6d, 0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xbc, 0x01, 0x0a, 0x0b, 0x50, 0x66, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x12, 0x27, 0x0a,
	0x0f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x66, 0x65, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0e, 0x69, 0x73, 0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x43, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0xb8, 0x02, 0x0a, 0x13, 0x4d, 0x66, 0x4d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x0a,
	0x0a, 0x6e, 0x65, 0x77, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x09, 0x6e, 0x65, 0x77, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x52, 0x0a, 0x06, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3a, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4c, 0x61, 0x6d, 0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x2e, 0x46, 0x6f, 0x6c, 0x69, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x73, 0x12, 0x6a, 0x0a, 0x0f, 0x6e, 0x66, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x4c, 0x61, 0x6d, 0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x2e,
	0x4e, 0x66, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x0d, 0x6e, 0x66, 0x74, 0x52, 0x65, 0x71, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x1a, 0xb7, 0x02, 0x0a, 0x12, 0x4d, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x6e, 0x6b,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x65, 0x77, 0x5f, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x44, 0x0a, 0x0d, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x5f, 0x6d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0c, 0x6c, 0x69,
	0x6e, 0x6b, 0x65, 0x64, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x52, 0x0a, 0x06, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x61, 0x6d,
	0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x46, 0x6f, 0x6c,
	0x69, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x73, 0x12, 0x6a,
	0x0a, 0x0f, 0x6e, 0x66, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x61, 0x6d, 0x66, 0x4c, 0x6f,
	0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4e, 0x66, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x6e, 0x66, 0x74,
	0x52, 0x65, 0x71, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x70, 0x0a, 0x09, 0x46, 0x6f,
	0x6c, 0x69, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f, 0x6c, 0x69, 0x6f,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x73,
	0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x73, 0x69, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x6d,
	0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x6d, 0x63, 0x1a, 0x72, 0x0a, 0x11,
	0x4e, 0x66, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x1a, 0x59, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4b,
	0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52,
	0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x73, 0x0a, 0x1e, 0x52,
	0x65, 0x73, 0x65, 0x74, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x49, 0x0a,
	0x04, 0x6c, 0x61, 0x6d, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x61,
	0x6d, 0x66, 0x52, 0x65, 0x73, 0x65, 0x74, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4c, 0x6f, 0x61,
	0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x04, 0x6c, 0x61, 0x6d, 0x66, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x50, 0x0a, 0x22, 0x4c, 0x61, 0x6d, 0x66, 0x52, 0x65, 0x73, 0x65, 0x74, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x61, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x6b,
	0x65, 0x6e, 0x22, 0x8b, 0x03, 0x0a, 0x19, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x61,
	0x78, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x4f, 0x74, 0x70, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6f, 0x74, 0x70, 0x5f, 0x61, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x6f, 0x74, 0x70, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x3b, 0x0a, 0x1a, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x17, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a,
	0x19, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x16, 0x6d, 0x61, 0x78, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x46, 0x0a, 0x11, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0f, 0x6c, 0x61, 0x73, 0x74, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0x36, 0x0a, 0x15, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xbd, 0x02, 0x0a, 0x13, 0x52, 0x4f, 0x49,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x6f, 0x73, 0x65, 0x6e, 0x5f, 0x72, 0x6f, 0x69, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x63, 0x68, 0x6f, 0x73, 0x65, 0x6e, 0x52, 0x6f, 0x69, 0x12,
	0x36, 0x0a, 0x07, 0x6b, 0x66, 0x73, 0x5f, 0x64, 0x6f, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x06, 0x6b, 0x66, 0x73, 0x44, 0x6f, 0x63, 0x12, 0x4b, 0x0a, 0x12, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x6f, 0x63, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x10, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x6f, 0x63, 0x12, 0x41, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x1c, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x5f, 0x72, 0x6f, 0x69, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x03, 0x28, 0x01, 0x52, 0x19, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x6f, 0x69, 0x46, 0x6f, 0x72, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2a, 0x78, 0x0a, 0x10, 0x49, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x1e,
	0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x41, 0x41, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x49,
	0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43,
	0x45, 0x5f, 0x49, 0x54, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x02, 0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_preapprovedloan_loan_step_execution_proto_rawDescOnce sync.Once
	file_api_preapprovedloan_loan_step_execution_proto_rawDescData = file_api_preapprovedloan_loan_step_execution_proto_rawDesc
)

func file_api_preapprovedloan_loan_step_execution_proto_rawDescGZIP() []byte {
	file_api_preapprovedloan_loan_step_execution_proto_rawDescOnce.Do(func() {
		file_api_preapprovedloan_loan_step_execution_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_preapprovedloan_loan_step_execution_proto_rawDescData)
	})
	return file_api_preapprovedloan_loan_step_execution_proto_rawDescData
}

var file_api_preapprovedloan_loan_step_execution_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_api_preapprovedloan_loan_step_execution_proto_msgTypes = make([]protoimpl.MessageInfo, 68)
var file_api_preapprovedloan_loan_step_execution_proto_goTypes = []interface{}{
	(IncomeDataSource)(0),                                // 0: preapprovedloan.IncomeDataSource
	(MandateData_FiAccountIneligibleForMandateReason)(0), // 1: preapprovedloan.MandateData.FiAccountIneligibleForMandateReason
	(ItrIncomeInfo_ItrStatus)(0),                         // 2: preapprovedloan.ItrIncomeInfo.ItrStatus
	(VendorPWAStagesStepData_PWAStage)(0),                // 3: preapprovedloan.VendorPWAStagesStepData.PWAStage
	(VendorPWAStagesStepData_PWAStageStatus)(0),          // 4: preapprovedloan.VendorPWAStagesStepData.PWAStageStatus
	(*LoanStepExecution)(nil),                            // 5: preapprovedloan.LoanStepExecution
	(*LivenessStepData)(nil),                             // 6: preapprovedloan.LivenessStepData
	(*FaceMatchStepData)(nil),                            // 7: preapprovedloan.FaceMatchStepData
	(*ESignStepData)(nil),                                // 8: preapprovedloan.ESignStepData
	(*LoanDocument)(nil),                                 // 9: preapprovedloan.LoanDocument
	(*OtpInfo)(nil),                                      // 10: preapprovedloan.OtpInfo
	(*VkycStepData)(nil),                                 // 11: preapprovedloan.VkycStepData
	(*KycStepData)(nil),                                  // 12: preapprovedloan.KycStepData
	(*CkycStepData)(nil),                                 // 13: preapprovedloan.CkycStepData
	(*AbflMandateData)(nil),                              // 14: preapprovedloan.AbflMandateData
	(*SgMandateData)(nil),                                // 15: preapprovedloan.SgMandateData
	(*FedMandateData)(nil),                               // 16: preapprovedloan.FedMandateData
	(*LLMandateData)(nil),                                // 17: preapprovedloan.LLMandateData
	(*IdfcCkycStepData)(nil),                             // 18: preapprovedloan.IdfcCkycStepData
	(*SgCkycStepData)(nil),                               // 19: preapprovedloan.SgCkycStepData
	(*AbflCkycStepData)(nil),                             // 20: preapprovedloan.AbflCkycStepData
	(*ManualReviewStepData)(nil),                         // 21: preapprovedloan.ManualReviewStepData
	(*OnboardingData)(nil),                               // 22: preapprovedloan.OnboardingData
	(*MandateData)(nil),                                  // 23: preapprovedloan.MandateData
	(*LoanStepExecutionDetails)(nil),                     // 24: preapprovedloan.LoanStepExecutionDetails
	(*PreEligibilityData)(nil),                           // 25: preapprovedloan.PreEligibilityData
	(*AadhaarData)(nil),                                  // 26: preapprovedloan.AadhaarData
	(*AbflAadhaarData)(nil),                              // 27: preapprovedloan.AbflAadhaarData
	(*IncomeEstimateData)(nil),                           // 28: preapprovedloan.IncomeEstimateData
	(*ItrIncomeInfo)(nil),                                // 29: preapprovedloan.ItrIncomeInfo
	(*AbflLoanDisbursementData)(nil),                     // 30: preapprovedloan.AbflLoanDisbursementData
	(*ListOfString)(nil),                                 // 31: preapprovedloan.ListOfString
	(*HunterData)(nil),                                   // 32: preapprovedloan.HunterData
	(*CollectionData)(nil),                               // 33: preapprovedloan.CollectionData
	(*ApplicantData)(nil),                                // 34: preapprovedloan.ApplicantData
	(*OtpVerificationData)(nil),                          // 35: preapprovedloan.OtpVerificationData
	(*Condition)(nil),                                    // 36: preapprovedloan.Condition
	(*OtpStatusCondition)(nil),                           // 37: preapprovedloan.OtpStatusCondition
	(*NotCondition)(nil),                                 // 38: preapprovedloan.NotCondition
	(*BreData)(nil),                                      // 39: preapprovedloan.BreData
	(*SelfieData)(nil),                                   // 40: preapprovedloan.SelfieData
	(*Reference)(nil),                                    // 41: preapprovedloan.Reference
	(*CreateLeadStepData)(nil),                           // 42: preapprovedloan.CreateLeadStepData
	(*VendorPWAStagesStepData)(nil),                      // 43: preapprovedloan.VendorPWAStagesStepData
	(*LoanDetailsVerificationData)(nil),                  // 44: preapprovedloan.LoanDetailsVerificationData
	(*LamfLoanDetailsVerificationData)(nil),              // 45: preapprovedloan.LamfLoanDetailsVerificationData
	(*ResetVendorLoanApplicationData)(nil),               // 46: preapprovedloan.ResetVendorLoanApplicationData
	(*LamfResetVendorLoanApplicationData)(nil),           // 47: preapprovedloan.LamfResetVendorLoanApplicationData
	(*ContactabilityDetailsData)(nil),                    // 48: preapprovedloan.ContactabilityDetailsData
	(*VendorIdentifiersData)(nil),                        // 49: preapprovedloan.VendorIdentifiersData
	(*ROIModificationData)(nil),                          // 50: preapprovedloan.ROIModificationData
	nil,                                                  // 51: preapprovedloan.LivenessStepData.NotificationTypeIdMapEntry
	nil,                                                  // 52: preapprovedloan.ESignStepData.NotificationTypeIdMapEntry
	nil,                                                  // 53: preapprovedloan.VkycStepData.NotificationTypeIdMapEntry
	nil,                                                  // 54: preapprovedloan.ManualReviewStepData.NotificationTypeIdMapEntry
	(*ManualReviewStepData_ReviewerDetails)(nil),         // 55: preapprovedloan.ManualReviewStepData.ReviewerDetails
	(*OnboardingData_AddressDetails)(nil),                // 56: preapprovedloan.OnboardingData.AddressDetails
	(*OnboardingData_EmploymentDetails)(nil),             // 57: preapprovedloan.OnboardingData.EmploymentDetails
	(*OnboardingData_BankingDetails)(nil),                // 58: preapprovedloan.OnboardingData.BankingDetails
	(*MandateData_BankingDetails)(nil),                   // 59: preapprovedloan.MandateData.BankingDetails
	(*MandateData_BankingDetails_AccountDetails)(nil),    // 60: preapprovedloan.MandateData.BankingDetails.AccountDetails
	nil,                                     // 61: preapprovedloan.MandateData.BankingDetails.AlternateAccDetailsEntry
	(*ItrIncomeInfo_ItrAttemptData)(nil),    // 62: preapprovedloan.ItrIncomeInfo.ItrAttemptData
	(*ItrIncomeInfo_ItrIntimationData)(nil), // 63: preapprovedloan.ItrIncomeInfo.ItrIntimationData
	(*OtpVerificationData_OtpData)(nil),     // 64: preapprovedloan.OtpVerificationData.OtpData
	nil,                                     // 65: preapprovedloan.OtpVerificationData.OtpData.AssetDetailsEntry
	(*VendorPWAStagesStepData_StageInfo)(nil),                   // 66: preapprovedloan.VendorPWAStagesStepData.StageInfo
	(*LamfLoanDetailsVerificationData_PfFetchData)(nil),         // 67: preapprovedloan.LamfLoanDetailsVerificationData.PfFetchData
	(*LamfLoanDetailsVerificationData_MfMobileLinkDetails)(nil), // 68: preapprovedloan.LamfLoanDetailsVerificationData.MfMobileLinkDetails
	(*LamfLoanDetailsVerificationData_MfEmailLinkDetails)(nil),  // 69: preapprovedloan.LamfLoanDetailsVerificationData.MfEmailLinkDetails
	(*LamfLoanDetailsVerificationData_FolioData)(nil),           // 70: preapprovedloan.LamfLoanDetailsVerificationData.FolioData
	(*LamfLoanDetailsVerificationData_NftRequestDetails)(nil),   // 71: preapprovedloan.LamfLoanDetailsVerificationData.NftRequestDetails
	(*LamfLoanDetailsVerificationData_UserAction)(nil),          // 72: preapprovedloan.LamfLoanDetailsVerificationData.UserAction
	(LoanStepExecutionFlow)(0),                                  // 73: preapprovedloan.LoanStepExecutionFlow
	(LoanStepExecutionStepName)(0),                              // 74: preapprovedloan.LoanStepExecutionStepName
	(LoanStepExecutionStatus)(0),                                // 75: preapprovedloan.LoanStepExecutionStatus
	(LoanStepExecutionSubStatus)(0),                             // 76: preapprovedloan.LoanStepExecutionSubStatus
	(*timestamppb.Timestamp)(nil),                               // 77: google.protobuf.Timestamp
	(GroupStage)(0),                                             // 78: preapprovedloan.GroupStage
	(typesv2.EmploymentType)(0),                                 // 79: api.typesv2.EmploymentType
	(CkycPositiveConfirmationStatus)(0),                         // 80: preapprovedloan.CkycPositiveConfirmationStatus
	(*common.Name)(nil),                                         // 81: api.typesv2.common.Name
	(typesv2.Gender)(0),                                         // 82: api.typesv2.Gender
	(*date.Date)(nil),                                           // 83: google.type.Date
	(*typesv2.PostalAddress)(nil),                               // 84: api.typesv2.PostalAddress
	(IdfcCkycAddressPinCodeType)(0),                             // 85: preapprovedloan.IdfcCkycAddressPinCodeType
	(KycType)(0),                                                // 86: preapprovedloan.KycType
	(*money.Money)(nil),                                         // 87: google.type.Money
	(enums.MandateType)(0),                                      // 88: preapprovedloan.enums.MandateType
	(*typesv2.Money)(nil),                                       // 89: api.typesv2.Money
	(*rpc.Status)(nil),                                          // 90: rpc.Status
	(*RepaymentBreakupData)(nil),                                // 91: preapprovedloan.RepaymentBreakupData
	(*common.PhoneNumber)(nil),                                  // 92: api.typesv2.common.PhoneNumber
	(OtpStatus)(0),                                              // 93: preapprovedloan.OtpStatus
	(*common.Image)(nil),                                        // 94: api.typesv2.common.Image
	(typesv2.AddressType)(0),                                    // 95: api.typesv2.AddressType
	(typesv2.ResidenceType)(0),                                  // 96: api.typesv2.ResidenceType
	(ItrFormType)(0),                                            // 97: preapprovedloan.ItrFormType
	(ItrTaxpayerStatus)(0),                                      // 98: preapprovedloan.ItrTaxpayerStatus
	(ItrTaxpayerResidentialStatus)(0),                           // 99: preapprovedloan.ItrTaxpayerResidentialStatus
	(OtpType)(0),                                                // 100: preapprovedloan.OtpType
	(RecordUserActionIdentifier)(0),                             // 101: preapprovedloan.RecordUserActionIdentifier
}
var file_api_preapprovedloan_loan_step_execution_proto_depIdxs = []int32{
	73,  // 0: preapprovedloan.LoanStepExecution.flow:type_name -> preapprovedloan.LoanStepExecutionFlow
	74,  // 1: preapprovedloan.LoanStepExecution.step_name:type_name -> preapprovedloan.LoanStepExecutionStepName
	24,  // 2: preapprovedloan.LoanStepExecution.details:type_name -> preapprovedloan.LoanStepExecutionDetails
	75,  // 3: preapprovedloan.LoanStepExecution.status:type_name -> preapprovedloan.LoanStepExecutionStatus
	76,  // 4: preapprovedloan.LoanStepExecution.sub_status:type_name -> preapprovedloan.LoanStepExecutionSubStatus
	77,  // 5: preapprovedloan.LoanStepExecution.staled_at:type_name -> google.protobuf.Timestamp
	77,  // 6: preapprovedloan.LoanStepExecution.completed_at:type_name -> google.protobuf.Timestamp
	77,  // 7: preapprovedloan.LoanStepExecution.created_at:type_name -> google.protobuf.Timestamp
	77,  // 8: preapprovedloan.LoanStepExecution.updated_at:type_name -> google.protobuf.Timestamp
	77,  // 9: preapprovedloan.LoanStepExecution.deleted_at:type_name -> google.protobuf.Timestamp
	78,  // 10: preapprovedloan.LoanStepExecution.group_stage:type_name -> preapprovedloan.GroupStage
	51,  // 11: preapprovedloan.LivenessStepData.notification_type_id_map:type_name -> preapprovedloan.LivenessStepData.NotificationTypeIdMapEntry
	77,  // 12: preapprovedloan.ESignStepData.expiry_at:type_name -> google.protobuf.Timestamp
	52,  // 13: preapprovedloan.ESignStepData.notification_type_id_map:type_name -> preapprovedloan.ESignStepData.NotificationTypeIdMapEntry
	10,  // 14: preapprovedloan.ESignStepData.otp_info:type_name -> preapprovedloan.OtpInfo
	9,   // 15: preapprovedloan.ESignStepData.kfsDocument:type_name -> preapprovedloan.LoanDocument
	9,   // 16: preapprovedloan.ESignStepData.loanAgreementDocument:type_name -> preapprovedloan.LoanDocument
	77,  // 17: preapprovedloan.ESignStepData.roi_modification_deadline:type_name -> google.protobuf.Timestamp
	77,  // 18: preapprovedloan.LoanDocument.expiry_at:type_name -> google.protobuf.Timestamp
	53,  // 19: preapprovedloan.VkycStepData.notification_type_id_map:type_name -> preapprovedloan.VkycStepData.NotificationTypeIdMapEntry
	77,  // 20: preapprovedloan.KycStepData.url_generated_at:type_name -> google.protobuf.Timestamp
	79,  // 21: preapprovedloan.CkycStepData.employment_type:type_name -> api.typesv2.EmploymentType
	80,  // 22: preapprovedloan.CkycStepData.positive_confirmation:type_name -> preapprovedloan.CkycPositiveConfirmationStatus
	81,  // 23: preapprovedloan.CkycStepData.name:type_name -> api.typesv2.common.Name
	82,  // 24: preapprovedloan.CkycStepData.gender:type_name -> api.typesv2.Gender
	83,  // 25: preapprovedloan.CkycStepData.dob:type_name -> google.type.Date
	84,  // 26: preapprovedloan.CkycStepData.permanent_address:type_name -> api.typesv2.PostalAddress
	84,  // 27: preapprovedloan.CkycStepData.correspondence_address:type_name -> api.typesv2.PostalAddress
	81,  // 28: preapprovedloan.CkycStepData.fathers_name:type_name -> api.typesv2.common.Name
	81,  // 29: preapprovedloan.CkycStepData.mothers_name:type_name -> api.typesv2.common.Name
	84,  // 30: preapprovedloan.CkycStepData.kyc_address:type_name -> api.typesv2.PostalAddress
	18,  // 31: preapprovedloan.CkycStepData.idfc:type_name -> preapprovedloan.IdfcCkycStepData
	20,  // 32: preapprovedloan.CkycStepData.abfl:type_name -> preapprovedloan.AbflCkycStepData
	19,  // 33: preapprovedloan.CkycStepData.sg_data:type_name -> preapprovedloan.SgCkycStepData
	77,  // 34: preapprovedloan.LLMandateData.latest_mandate_attempt_timestamp:type_name -> google.protobuf.Timestamp
	85,  // 35: preapprovedloan.IdfcCkycStepData.address_pin_code_type:type_name -> preapprovedloan.IdfcCkycAddressPinCodeType
	86,  // 36: preapprovedloan.SgCkycStepData.kyc_type:type_name -> preapprovedloan.KycType
	84,  // 37: preapprovedloan.AbflCkycStepData.permanent_address:type_name -> api.typesv2.PostalAddress
	84,  // 38: preapprovedloan.AbflCkycStepData.corresponding_address:type_name -> api.typesv2.PostalAddress
	81,  // 39: preapprovedloan.AbflCkycStepData.full_name:type_name -> api.typesv2.common.Name
	83,  // 40: preapprovedloan.AbflCkycStepData.dob:type_name -> google.type.Date
	54,  // 41: preapprovedloan.ManualReviewStepData.notification_type_id_map:type_name -> preapprovedloan.ManualReviewStepData.NotificationTypeIdMapEntry
	55,  // 42: preapprovedloan.ManualReviewStepData.reviewer_details:type_name -> preapprovedloan.ManualReviewStepData.ReviewerDetails
	56,  // 43: preapprovedloan.OnboardingData.address_details:type_name -> preapprovedloan.OnboardingData.AddressDetails
	57,  // 44: preapprovedloan.OnboardingData.employment_details:type_name -> preapprovedloan.OnboardingData.EmploymentDetails
	58,  // 45: preapprovedloan.OnboardingData.banking_details:type_name -> preapprovedloan.OnboardingData.BankingDetails
	87,  // 46: preapprovedloan.MandateData.max_txn_amount:type_name -> google.type.Money
	77,  // 47: preapprovedloan.MandateData.mandate_link_expiry:type_name -> google.protobuf.Timestamp
	14,  // 48: preapprovedloan.MandateData.abfl:type_name -> preapprovedloan.AbflMandateData
	17,  // 49: preapprovedloan.MandateData.ll_mandate_data:type_name -> preapprovedloan.LLMandateData
	15,  // 50: preapprovedloan.MandateData.sg_mandate_data:type_name -> preapprovedloan.SgMandateData
	16,  // 51: preapprovedloan.MandateData.fed_mandate_data:type_name -> preapprovedloan.FedMandateData
	59,  // 52: preapprovedloan.MandateData.banking_details:type_name -> preapprovedloan.MandateData.BankingDetails
	1,   // 53: preapprovedloan.MandateData.fi_account_ineligible_for_mandate_reason:type_name -> preapprovedloan.MandateData.FiAccountIneligibleForMandateReason
	77,  // 54: preapprovedloan.MandateData.url_generated_at:type_name -> google.protobuf.Timestamp
	88,  // 55: preapprovedloan.MandateData.mandate_type:type_name -> preapprovedloan.enums.MandateType
	6,   // 56: preapprovedloan.LoanStepExecutionDetails.liveness_step_data:type_name -> preapprovedloan.LivenessStepData
	7,   // 57: preapprovedloan.LoanStepExecutionDetails.face_match_step_data:type_name -> preapprovedloan.FaceMatchStepData
	8,   // 58: preapprovedloan.LoanStepExecutionDetails.e_sign_step_data:type_name -> preapprovedloan.ESignStepData
	11,  // 59: preapprovedloan.LoanStepExecutionDetails.vkyc_step_data:type_name -> preapprovedloan.VkycStepData
	21,  // 60: preapprovedloan.LoanStepExecutionDetails.manual_review_step_data:type_name -> preapprovedloan.ManualReviewStepData
	22,  // 61: preapprovedloan.LoanStepExecutionDetails.onboarding_data:type_name -> preapprovedloan.OnboardingData
	23,  // 62: preapprovedloan.LoanStepExecutionDetails.mandate_data:type_name -> preapprovedloan.MandateData
	32,  // 63: preapprovedloan.LoanStepExecutionDetails.hunter_data:type_name -> preapprovedloan.HunterData
	13,  // 64: preapprovedloan.LoanStepExecutionDetails.ckyc_step_data:type_name -> preapprovedloan.CkycStepData
	33,  // 65: preapprovedloan.LoanStepExecutionDetails.collection_data:type_name -> preapprovedloan.CollectionData
	34,  // 66: preapprovedloan.LoanStepExecutionDetails.applicant_data:type_name -> preapprovedloan.ApplicantData
	35,  // 67: preapprovedloan.LoanStepExecutionDetails.otp_verification_data:type_name -> preapprovedloan.OtpVerificationData
	39,  // 68: preapprovedloan.LoanStepExecutionDetails.bre_data:type_name -> preapprovedloan.BreData
	40,  // 69: preapprovedloan.LoanStepExecutionDetails.selfie_data:type_name -> preapprovedloan.SelfieData
	42,  // 70: preapprovedloan.LoanStepExecutionDetails.create_lead_step_data:type_name -> preapprovedloan.CreateLeadStepData
	43,  // 71: preapprovedloan.LoanStepExecutionDetails.vendor_pwa_stages_step_data:type_name -> preapprovedloan.VendorPWAStagesStepData
	12,  // 72: preapprovedloan.LoanStepExecutionDetails.kyc_step_data:type_name -> preapprovedloan.KycStepData
	28,  // 73: preapprovedloan.LoanStepExecutionDetails.income_estimate_data:type_name -> preapprovedloan.IncomeEstimateData
	26,  // 74: preapprovedloan.LoanStepExecutionDetails.aadhaar_data:type_name -> preapprovedloan.AadhaarData
	44,  // 75: preapprovedloan.LoanStepExecutionDetails.loan_details_verification_data:type_name -> preapprovedloan.LoanDetailsVerificationData
	46,  // 76: preapprovedloan.LoanStepExecutionDetails.reset_vendor_loan_application_data:type_name -> preapprovedloan.ResetVendorLoanApplicationData
	48,  // 77: preapprovedloan.LoanStepExecutionDetails.contactability_details_data:type_name -> preapprovedloan.ContactabilityDetailsData
	49,  // 78: preapprovedloan.LoanStepExecutionDetails.vendor_identifiers_data:type_name -> preapprovedloan.VendorIdentifiersData
	50,  // 79: preapprovedloan.LoanStepExecutionDetails.roi_modification_data:type_name -> preapprovedloan.ROIModificationData
	25,  // 80: preapprovedloan.LoanStepExecutionDetails.pre_eligibility_data:type_name -> preapprovedloan.PreEligibilityData
	30,  // 81: preapprovedloan.LoanStepExecutionDetails.abfl:type_name -> preapprovedloan.AbflLoanDisbursementData
	27,  // 82: preapprovedloan.AadhaarData.abfl:type_name -> preapprovedloan.AbflAadhaarData
	84,  // 83: preapprovedloan.AbflAadhaarData.address:type_name -> api.typesv2.PostalAddress
	82,  // 84: preapprovedloan.AbflAadhaarData.gender:type_name -> api.typesv2.Gender
	81,  // 85: preapprovedloan.AbflAadhaarData.name:type_name -> api.typesv2.common.Name
	83,  // 86: preapprovedloan.AbflAadhaarData.dob:type_name -> google.type.Date
	89,  // 87: preapprovedloan.IncomeEstimateData.predicted_income:type_name -> api.typesv2.Money
	0,   // 88: preapprovedloan.IncomeEstimateData.income_data_source:type_name -> preapprovedloan.IncomeDataSource
	90,  // 89: preapprovedloan.IncomeEstimateData.income_estimator_resp_status:type_name -> rpc.Status
	29,  // 90: preapprovedloan.IncomeEstimateData.itr_income_info:type_name -> preapprovedloan.ItrIncomeInfo
	62,  // 91: preapprovedloan.ItrIncomeInfo.itr_attempt_data:type_name -> preapprovedloan.ItrIncomeInfo.ItrAttemptData
	63,  // 92: preapprovedloan.ItrIncomeInfo.itr_intimation:type_name -> preapprovedloan.ItrIncomeInfo.ItrIntimationData
	91,  // 93: preapprovedloan.CollectionData.repayment_breakup:type_name -> preapprovedloan.RepaymentBreakupData
	83,  // 94: preapprovedloan.ApplicantData.dob:type_name -> google.type.Date
	92,  // 95: preapprovedloan.ApplicantData.phone_number:type_name -> api.typesv2.common.PhoneNumber
	41,  // 96: preapprovedloan.ApplicantData.references:type_name -> preapprovedloan.Reference
	87,  // 97: preapprovedloan.ApplicantData.desired_loan_amount:type_name -> google.type.Money
	64,  // 98: preapprovedloan.OtpVerificationData.otp_data:type_name -> preapprovedloan.OtpVerificationData.OtpData
	77,  // 99: preapprovedloan.OtpVerificationData.otp_verification_started_at:type_name -> google.protobuf.Timestamp
	37,  // 100: preapprovedloan.Condition.otp_status_condition:type_name -> preapprovedloan.OtpStatusCondition
	38,  // 101: preapprovedloan.Condition.not_condition:type_name -> preapprovedloan.NotCondition
	93,  // 102: preapprovedloan.OtpStatusCondition.statuses:type_name -> preapprovedloan.OtpStatus
	36,  // 103: preapprovedloan.NotCondition.base_condition:type_name -> preapprovedloan.Condition
	94,  // 104: preapprovedloan.SelfieData.selfie_image:type_name -> api.typesv2.common.Image
	81,  // 105: preapprovedloan.Reference.name:type_name -> api.typesv2.common.Name
	92,  // 106: preapprovedloan.Reference.phone_number:type_name -> api.typesv2.common.PhoneNumber
	81,  // 107: preapprovedloan.CreateLeadStepData.name:type_name -> api.typesv2.common.Name
	92,  // 108: preapprovedloan.CreateLeadStepData.phone_number:type_name -> api.typesv2.common.PhoneNumber
	82,  // 109: preapprovedloan.CreateLeadStepData.gender:type_name -> api.typesv2.Gender
	83,  // 110: preapprovedloan.CreateLeadStepData.dob:type_name -> google.type.Date
	87,  // 111: preapprovedloan.CreateLeadStepData.declared_income:type_name -> google.type.Money
	84,  // 112: preapprovedloan.CreateLeadStepData.current_address:type_name -> api.typesv2.PostalAddress
	66,  // 113: preapprovedloan.VendorPWAStagesStepData.stage_infos:type_name -> preapprovedloan.VendorPWAStagesStepData.StageInfo
	77,  // 114: preapprovedloan.VendorPWAStagesStepData.pwa_url_expiry_time:type_name -> google.protobuf.Timestamp
	45,  // 115: preapprovedloan.LoanDetailsVerificationData.lamf:type_name -> preapprovedloan.LamfLoanDetailsVerificationData
	67,  // 116: preapprovedloan.LamfLoanDetailsVerificationData.pf_fetch_Data:type_name -> preapprovedloan.LamfLoanDetailsVerificationData.PfFetchData
	68,  // 117: preapprovedloan.LamfLoanDetailsVerificationData.mobileLinkDetails:type_name -> preapprovedloan.LamfLoanDetailsVerificationData.MfMobileLinkDetails
	69,  // 118: preapprovedloan.LamfLoanDetailsVerificationData.emailLinkDetails:type_name -> preapprovedloan.LamfLoanDetailsVerificationData.MfEmailLinkDetails
	72,  // 119: preapprovedloan.LamfLoanDetailsVerificationData.user_action:type_name -> preapprovedloan.LamfLoanDetailsVerificationData.UserAction
	47,  // 120: preapprovedloan.ResetVendorLoanApplicationData.lamf:type_name -> preapprovedloan.LamfResetVendorLoanApplicationData
	92,  // 121: preapprovedloan.ContactabilityDetailsData.phone_number:type_name -> api.typesv2.common.PhoneNumber
	77,  // 122: preapprovedloan.ContactabilityDetailsData.last_attempt_time:type_name -> google.protobuf.Timestamp
	9,   // 123: preapprovedloan.ROIModificationData.kfs_doc:type_name -> preapprovedloan.LoanDocument
	9,   // 124: preapprovedloan.ROIModificationData.loan_agreement_doc:type_name -> preapprovedloan.LoanDocument
	87,  // 125: preapprovedloan.ROIModificationData.installment_amount:type_name -> google.type.Money
	31,  // 126: preapprovedloan.LivenessStepData.NotificationTypeIdMapEntry.value:type_name -> preapprovedloan.ListOfString
	31,  // 127: preapprovedloan.ESignStepData.NotificationTypeIdMapEntry.value:type_name -> preapprovedloan.ListOfString
	31,  // 128: preapprovedloan.VkycStepData.NotificationTypeIdMapEntry.value:type_name -> preapprovedloan.ListOfString
	31,  // 129: preapprovedloan.ManualReviewStepData.NotificationTypeIdMapEntry.value:type_name -> preapprovedloan.ListOfString
	77,  // 130: preapprovedloan.ManualReviewStepData.ReviewerDetails.reviewed_at:type_name -> google.protobuf.Timestamp
	84,  // 131: preapprovedloan.OnboardingData.AddressDetails.address_details:type_name -> api.typesv2.PostalAddress
	95,  // 132: preapprovedloan.OnboardingData.AddressDetails.address_type:type_name -> api.typesv2.AddressType
	96,  // 133: preapprovedloan.OnboardingData.AddressDetails.residence_type:type_name -> api.typesv2.ResidenceType
	87,  // 134: preapprovedloan.OnboardingData.AddressDetails.monthly_rent:type_name -> google.type.Money
	79,  // 135: preapprovedloan.OnboardingData.EmploymentDetails.occupation:type_name -> api.typesv2.EmploymentType
	87,  // 136: preapprovedloan.OnboardingData.EmploymentDetails.monthly_income:type_name -> google.type.Money
	84,  // 137: preapprovedloan.OnboardingData.EmploymentDetails.office_address:type_name -> api.typesv2.PostalAddress
	87,  // 138: preapprovedloan.OnboardingData.EmploymentDetails.annual_revenue:type_name -> google.type.Money
	61,  // 139: preapprovedloan.MandateData.BankingDetails.alternate_acc_details:type_name -> preapprovedloan.MandateData.BankingDetails.AlternateAccDetailsEntry
	60,  // 140: preapprovedloan.MandateData.BankingDetails.final_acc_details_used:type_name -> preapprovedloan.MandateData.BankingDetails.AccountDetails
	60,  // 141: preapprovedloan.MandateData.BankingDetails.AlternateAccDetailsEntry.value:type_name -> preapprovedloan.MandateData.BankingDetails.AccountDetails
	2,   // 142: preapprovedloan.ItrIncomeInfo.ItrAttemptData.itr_status:type_name -> preapprovedloan.ItrIncomeInfo.ItrStatus
	81,  // 143: preapprovedloan.ItrIncomeInfo.ItrIntimationData.name:type_name -> api.typesv2.common.Name
	92,  // 144: preapprovedloan.ItrIncomeInfo.ItrIntimationData.phone_number:type_name -> api.typesv2.common.PhoneNumber
	89,  // 145: preapprovedloan.ItrIncomeInfo.ItrIntimationData.gross_total_income_provided:type_name -> api.typesv2.Money
	89,  // 146: preapprovedloan.ItrIncomeInfo.ItrIntimationData.gross_total_income_computed:type_name -> api.typesv2.Money
	89,  // 147: preapprovedloan.ItrIncomeInfo.ItrIntimationData.total_income_provided:type_name -> api.typesv2.Money
	89,  // 148: preapprovedloan.ItrIncomeInfo.ItrIntimationData.total_income_computed:type_name -> api.typesv2.Money
	77,  // 149: preapprovedloan.ItrIncomeInfo.ItrIntimationData.date_of_filing:type_name -> google.protobuf.Timestamp
	97,  // 150: preapprovedloan.ItrIncomeInfo.ItrIntimationData.itr_form_type:type_name -> preapprovedloan.ItrFormType
	98,  // 151: preapprovedloan.ItrIncomeInfo.ItrIntimationData.taxpayer_status:type_name -> preapprovedloan.ItrTaxpayerStatus
	99,  // 152: preapprovedloan.ItrIncomeInfo.ItrIntimationData.residential_status:type_name -> preapprovedloan.ItrTaxpayerResidentialStatus
	100, // 153: preapprovedloan.OtpVerificationData.OtpData.otp_type:type_name -> preapprovedloan.OtpType
	93,  // 154: preapprovedloan.OtpVerificationData.OtpData.otp_status:type_name -> preapprovedloan.OtpStatus
	92,  // 155: preapprovedloan.OtpVerificationData.OtpData.phone_number:type_name -> api.typesv2.common.PhoneNumber
	65,  // 156: preapprovedloan.OtpVerificationData.OtpData.assetDetails:type_name -> preapprovedloan.OtpVerificationData.OtpData.AssetDetailsEntry
	36,  // 157: preapprovedloan.OtpVerificationData.OtpData.otp_validation_condition:type_name -> preapprovedloan.Condition
	3,   // 158: preapprovedloan.VendorPWAStagesStepData.StageInfo.stage_name:type_name -> preapprovedloan.VendorPWAStagesStepData.PWAStage
	4,   // 159: preapprovedloan.VendorPWAStagesStepData.StageInfo.stage_status:type_name -> preapprovedloan.VendorPWAStagesStepData.PWAStageStatus
	77,  // 160: preapprovedloan.VendorPWAStagesStepData.StageInfo.updated_at:type_name -> google.protobuf.Timestamp
	77,  // 161: preapprovedloan.VendorPWAStagesStepData.StageInfo.vendor_updated_at:type_name -> google.protobuf.Timestamp
	77,  // 162: preapprovedloan.LamfLoanDetailsVerificationData.PfFetchData.completion_time:type_name -> google.protobuf.Timestamp
	92,  // 163: preapprovedloan.LamfLoanDetailsVerificationData.MfMobileLinkDetails.new_mobile:type_name -> api.typesv2.common.PhoneNumber
	70,  // 164: preapprovedloan.LamfLoanDetailsVerificationData.MfMobileLinkDetails.folios:type_name -> preapprovedloan.LamfLoanDetailsVerificationData.FolioData
	71,  // 165: preapprovedloan.LamfLoanDetailsVerificationData.MfMobileLinkDetails.nft_req_details:type_name -> preapprovedloan.LamfLoanDetailsVerificationData.NftRequestDetails
	92,  // 166: preapprovedloan.LamfLoanDetailsVerificationData.MfEmailLinkDetails.linked_mobile:type_name -> api.typesv2.common.PhoneNumber
	70,  // 167: preapprovedloan.LamfLoanDetailsVerificationData.MfEmailLinkDetails.folios:type_name -> preapprovedloan.LamfLoanDetailsVerificationData.FolioData
	71,  // 168: preapprovedloan.LamfLoanDetailsVerificationData.MfEmailLinkDetails.nft_req_details:type_name -> preapprovedloan.LamfLoanDetailsVerificationData.NftRequestDetails
	77,  // 169: preapprovedloan.LamfLoanDetailsVerificationData.NftRequestDetails.created_at:type_name -> google.protobuf.Timestamp
	101, // 170: preapprovedloan.LamfLoanDetailsVerificationData.UserAction.identifier:type_name -> preapprovedloan.RecordUserActionIdentifier
	171, // [171:171] is the sub-list for method output_type
	171, // [171:171] is the sub-list for method input_type
	171, // [171:171] is the sub-list for extension type_name
	171, // [171:171] is the sub-list for extension extendee
	0,   // [0:171] is the sub-list for field type_name
}

func init() { file_api_preapprovedloan_loan_step_execution_proto_init() }
func file_api_preapprovedloan_loan_step_execution_proto_init() {
	if File_api_preapprovedloan_loan_step_execution_proto != nil {
		return
	}
	file_api_preapprovedloan_collections_proto_init()
	file_api_preapprovedloan_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanStepExecution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessStepData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceMatchStepData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ESignStepData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanDocument); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OtpInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VkycStepData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KycStepData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CkycStepData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AbflMandateData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SgMandateData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FedMandateData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LLMandateData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdfcCkycStepData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SgCkycStepData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AbflCkycStepData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualReviewStepData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnboardingData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MandateData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanStepExecutionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreEligibilityData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AadhaarData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AbflAadhaarData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncomeEstimateData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItrIncomeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AbflLoanDisbursementData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOfString); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HunterData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicantData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OtpVerificationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Condition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OtpStatusCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BreData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelfieData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reference); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLeadStepData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VendorPWAStagesStepData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanDetailsVerificationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LamfLoanDetailsVerificationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetVendorLoanApplicationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LamfResetVendorLoanApplicationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContactabilityDetailsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VendorIdentifiersData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ROIModificationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualReviewStepData_ReviewerDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnboardingData_AddressDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnboardingData_EmploymentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnboardingData_BankingDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MandateData_BankingDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MandateData_BankingDetails_AccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItrIncomeInfo_ItrAttemptData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItrIncomeInfo_ItrIntimationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OtpVerificationData_OtpData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VendorPWAStagesStepData_StageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LamfLoanDetailsVerificationData_PfFetchData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LamfLoanDetailsVerificationData_MfMobileLinkDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LamfLoanDetailsVerificationData_MfEmailLinkDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LamfLoanDetailsVerificationData_FolioData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LamfLoanDetailsVerificationData_NftRequestDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_step_execution_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LamfLoanDetailsVerificationData_UserAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_preapprovedloan_loan_step_execution_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*CkycStepData_Idfc)(nil),
		(*CkycStepData_Abfl)(nil),
		(*CkycStepData_SgData)(nil),
	}
	file_api_preapprovedloan_loan_step_execution_proto_msgTypes[18].OneofWrappers = []interface{}{
		(*MandateData_Abfl)(nil),
		(*MandateData_LlMandateData)(nil),
		(*MandateData_SgMandateData)(nil),
		(*MandateData_FedMandateData)(nil),
	}
	file_api_preapprovedloan_loan_step_execution_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*LoanStepExecutionDetails_LivenessStepData)(nil),
		(*LoanStepExecutionDetails_FaceMatchStepData)(nil),
		(*LoanStepExecutionDetails_ESignStepData)(nil),
		(*LoanStepExecutionDetails_VkycStepData)(nil),
		(*LoanStepExecutionDetails_ManualReviewStepData)(nil),
		(*LoanStepExecutionDetails_OnboardingData)(nil),
		(*LoanStepExecutionDetails_MandateData)(nil),
		(*LoanStepExecutionDetails_HunterData)(nil),
		(*LoanStepExecutionDetails_CkycStepData)(nil),
		(*LoanStepExecutionDetails_CollectionData)(nil),
		(*LoanStepExecutionDetails_ApplicantData)(nil),
		(*LoanStepExecutionDetails_OtpVerificationData)(nil),
		(*LoanStepExecutionDetails_BreData)(nil),
		(*LoanStepExecutionDetails_SelfieData)(nil),
		(*LoanStepExecutionDetails_CreateLeadStepData)(nil),
		(*LoanStepExecutionDetails_VendorPwaStagesStepData)(nil),
		(*LoanStepExecutionDetails_KycStepData)(nil),
		(*LoanStepExecutionDetails_IncomeEstimateData)(nil),
		(*LoanStepExecutionDetails_AadhaarData)(nil),
		(*LoanStepExecutionDetails_LoanDetailsVerificationData)(nil),
		(*LoanStepExecutionDetails_ResetVendorLoanApplicationData)(nil),
		(*LoanStepExecutionDetails_ContactabilityDetailsData)(nil),
		(*LoanStepExecutionDetails_VendorIdentifiersData)(nil),
		(*LoanStepExecutionDetails_RoiModificationData)(nil),
		(*LoanStepExecutionDetails_PreEligibilityData)(nil),
		(*LoanStepExecutionDetails_Abfl)(nil),
	}
	file_api_preapprovedloan_loan_step_execution_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*AadhaarData_Abfl)(nil),
	}
	file_api_preapprovedloan_loan_step_execution_proto_msgTypes[31].OneofWrappers = []interface{}{
		(*Condition_OtpStatusCondition)(nil),
		(*Condition_NotCondition)(nil),
	}
	file_api_preapprovedloan_loan_step_execution_proto_msgTypes[39].OneofWrappers = []interface{}{
		(*LoanDetailsVerificationData_Lamf)(nil),
	}
	file_api_preapprovedloan_loan_step_execution_proto_msgTypes[41].OneofWrappers = []interface{}{
		(*ResetVendorLoanApplicationData_Lamf)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_preapprovedloan_loan_step_execution_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   68,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_preapprovedloan_loan_step_execution_proto_goTypes,
		DependencyIndexes: file_api_preapprovedloan_loan_step_execution_proto_depIdxs,
		EnumInfos:         file_api_preapprovedloan_loan_step_execution_proto_enumTypes,
		MessageInfos:      file_api_preapprovedloan_loan_step_execution_proto_msgTypes,
	}.Build()
	File_api_preapprovedloan_loan_step_execution_proto = out.File
	file_api_preapprovedloan_loan_step_execution_proto_rawDesc = nil
	file_api_preapprovedloan_loan_step_execution_proto_goTypes = nil
	file_api_preapprovedloan_loan_step_execution_proto_depIdxs = nil
}
