// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/preapprovedloan/service.proto

package preapprovedloan

import (
	context "context"
	dynamic_elements "github.com/epifi/gamma/api/dynamic_elements"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PreApprovedLoan_GetLandingInfo_FullMethodName                  = "/preapprovedloan.PreApprovedLoan/GetLandingInfo"
	PreApprovedLoan_GetLandingInfoV2_FullMethodName                = "/preapprovedloan.PreApprovedLoan/GetLandingInfoV2"
	PreApprovedLoan_GetLandingInfoV3_FullMethodName                = "/preapprovedloan.PreApprovedLoan/GetLandingInfoV3"
	PreApprovedLoan_GetOfferDetails_FullMethodName                 = "/preapprovedloan.PreApprovedLoan/GetOfferDetails"
	PreApprovedLoan_GenerateConfirmationCode_FullMethodName        = "/preapprovedloan.PreApprovedLoan/GenerateConfirmationCode"
	PreApprovedLoan_ApplyForLoan_FullMethodName                    = "/preapprovedloan.PreApprovedLoan/ApplyForLoan"
	PreApprovedLoan_ConfirmApplication_FullMethodName              = "/preapprovedloan.PreApprovedLoan/ConfirmApplication"
	PreApprovedLoan_GetApplicationStatus_FullMethodName            = "/preapprovedloan.PreApprovedLoan/GetApplicationStatus"
	PreApprovedLoan_GetApplicationStatusSync_FullMethodName        = "/preapprovedloan.PreApprovedLoan/GetApplicationStatusSync"
	PreApprovedLoan_GetLivenessStatus_FullMethodName               = "/preapprovedloan.PreApprovedLoan/GetLivenessStatus"
	PreApprovedLoan_GetDashboard_FullMethodName                    = "/preapprovedloan.PreApprovedLoan/GetDashboard"
	PreApprovedLoan_GetDashboardV2_FullMethodName                  = "/preapprovedloan.PreApprovedLoan/GetDashboardV2"
	PreApprovedLoan_CancelApplication_FullMethodName               = "/preapprovedloan.PreApprovedLoan/CancelApplication"
	PreApprovedLoan_InitiateESign_FullMethodName                   = "/preapprovedloan.PreApprovedLoan/InitiateESign"
	PreApprovedLoan_GetLoanDetails_FullMethodName                  = "/preapprovedloan.PreApprovedLoan/GetLoanDetails"
	PreApprovedLoan_PrePayLoan_FullMethodName                      = "/preapprovedloan.PreApprovedLoan/PrePayLoan"
	PreApprovedLoan_GetLoanActivityStatus_FullMethodName           = "/preapprovedloan.PreApprovedLoan/GetLoanActivityStatus"
	PreApprovedLoan_GetAllTransactions_FullMethodName              = "/preapprovedloan.PreApprovedLoan/GetAllTransactions"
	PreApprovedLoan_GetTransactionReceipt_FullMethodName           = "/preapprovedloan.PreApprovedLoan/GetTransactionReceipt"
	PreApprovedLoan_GetLoanAccountDetails_FullMethodName           = "/preapprovedloan.PreApprovedLoan/GetLoanAccountDetails"
	PreApprovedLoan_GetLoanOffers_FullMethodName                   = "/preapprovedloan.PreApprovedLoan/GetLoanOffers"
	PreApprovedLoan_GetRecentLoanApplicationDetails_FullMethodName = "/preapprovedloan.PreApprovedLoan/GetRecentLoanApplicationDetails"
	PreApprovedLoan_GetLoanTransactions_FullMethodName             = "/preapprovedloan.PreApprovedLoan/GetLoanTransactions"
	PreApprovedLoan_GetLoanSummaryForHome_FullMethodName           = "/preapprovedloan.PreApprovedLoan/GetLoanSummaryForHome"
	PreApprovedLoan_EstimateCreditUtilised_FullMethodName          = "/preapprovedloan.PreApprovedLoan/EstimateCreditUtilised"
	PreApprovedLoan_AddAddressDetails_FullMethodName               = "/preapprovedloan.PreApprovedLoan/AddAddressDetails"
	PreApprovedLoan_AddEmploymentDetails_FullMethodName            = "/preapprovedloan.PreApprovedLoan/AddEmploymentDetails"
	PreApprovedLoan_FetchDynamicElements_FullMethodName            = "/preapprovedloan.PreApprovedLoan/FetchDynamicElements"
	PreApprovedLoan_DynamicElementCallback_FullMethodName          = "/preapprovedloan.PreApprovedLoan/DynamicElementCallback"
	PreApprovedLoan_ConfirmRevisedLoanDetails_FullMethodName       = "/preapprovedloan.PreApprovedLoan/ConfirmRevisedLoanDetails"
	PreApprovedLoan_ClientCallback_FullMethodName                  = "/preapprovedloan.PreApprovedLoan/ClientCallback"
	PreApprovedLoan_VerifyDetails_FullMethodName                   = "/preapprovedloan.PreApprovedLoan/VerifyDetails"
	PreApprovedLoan_GetEarlySalaryDetails_FullMethodName           = "/preapprovedloan.PreApprovedLoan/GetEarlySalaryDetails"
	PreApprovedLoan_VerifyCkycDetails_FullMethodName               = "/preapprovedloan.PreApprovedLoan/VerifyCkycDetails"
	PreApprovedLoan_RefreshLMSSchedule_FullMethodName              = "/preapprovedloan.PreApprovedLoan/RefreshLMSSchedule"
	PreApprovedLoan_GetMandateViewData_FullMethodName              = "/preapprovedloan.PreApprovedLoan/GetMandateViewData"
	PreApprovedLoan_ExecuteCollection_FullMethodName               = "/preapprovedloan.PreApprovedLoan/ExecuteCollection"
	PreApprovedLoan_ReconLoanActivity_FullMethodName               = "/preapprovedloan.PreApprovedLoan/ReconLoanActivity"
	PreApprovedLoan_GetLoanSchedule_FullMethodName                 = "/preapprovedloan.PreApprovedLoan/GetLoanSchedule"
	PreApprovedLoan_AddPanAndDobData_FullMethodName                = "/preapprovedloan.PreApprovedLoan/AddPanAndDobData"
	PreApprovedLoan_GetLoanInstallmentPayoutDetails_FullMethodName = "/preapprovedloan.PreApprovedLoan/GetLoanInstallmentPayoutDetails"
	PreApprovedLoan_AddNameAndGender_FullMethodName                = "/preapprovedloan.PreApprovedLoan/AddNameAndGender"
	PreApprovedLoan_CheckLoanEligibility_FullMethodName            = "/preapprovedloan.PreApprovedLoan/CheckLoanEligibility"
	PreApprovedLoan_FetchCreditReport_FullMethodName               = "/preapprovedloan.PreApprovedLoan/FetchCreditReport"
	PreApprovedLoan_GetLoanDefaultDetails_FullMethodName           = "/preapprovedloan.PreApprovedLoan/GetLoanDefaultDetails"
	PreApprovedLoan_AddBankingDetails_FullMethodName               = "/preapprovedloan.PreApprovedLoan/AddBankingDetails"
	PreApprovedLoan_GetLoanReviewDetails_FullMethodName            = "/preapprovedloan.PreApprovedLoan/GetLoanReviewDetails"
	PreApprovedLoan_SubmitReviewLoanDetails_FullMethodName         = "/preapprovedloan.PreApprovedLoan/SubmitReviewLoanDetails"
	PreApprovedLoan_GetLoanUserDetails_FullMethodName              = "/preapprovedloan.PreApprovedLoan/GetLoanUserDetails"
	PreApprovedLoan_GetLoanIdByHeaderAndActorId_FullMethodName     = "/preapprovedloan.PreApprovedLoan/GetLoanIdByHeaderAndActorId"
	PreApprovedLoan_GetDowntimeStatus_FullMethodName               = "/preapprovedloan.PreApprovedLoan/GetDowntimeStatus"
	PreApprovedLoan_InitiateSiSetup_FullMethodName                 = "/preapprovedloan.PreApprovedLoan/InitiateSiSetup"
	PreApprovedLoan_GetCompletedLoanRequests_FullMethodName        = "/preapprovedloan.PreApprovedLoan/GetCompletedLoanRequests"
	PreApprovedLoan_GetActiveLoanRequests_FullMethodName           = "/preapprovedloan.PreApprovedLoan/GetActiveLoanRequests"
	PreApprovedLoan_GetVendorApplicantId_FullMethodName            = "/preapprovedloan.PreApprovedLoan/GetVendorApplicantId"
	PreApprovedLoan_SubmitIncomeVerificationOption_FullMethodName  = "/preapprovedloan.PreApprovedLoan/SubmitIncomeVerificationOption"
	PreApprovedLoan_GetMandateViewDataV2_FullMethodName            = "/preapprovedloan.PreApprovedLoan/GetMandateViewDataV2"
	PreApprovedLoan_GetPWARedirectionDetails_FullMethodName        = "/preapprovedloan.PreApprovedLoan/GetPWARedirectionDetails"
	PreApprovedLoan_RecordUserAction_FullMethodName                = "/preapprovedloan.PreApprovedLoan/RecordUserAction"
	PreApprovedLoan_ProcessOffAppLoanRepayment_FullMethodName      = "/preapprovedloan.PreApprovedLoan/ProcessOffAppLoanRepayment"
	PreApprovedLoan_GetLoansUserStatus_FullMethodName              = "/preapprovedloan.PreApprovedLoan/GetLoansUserStatus"
	PreApprovedLoan_GetIncomeVerificationInfo_FullMethodName       = "/preapprovedloan.PreApprovedLoan/GetIncomeVerificationInfo"
	PreApprovedLoan_GetVkycDeeplink_FullMethodName                 = "/preapprovedloan.PreApprovedLoan/GetVkycDeeplink"
	PreApprovedLoan_GetForeclosureDetails_FullMethodName           = "/preapprovedloan.PreApprovedLoan/GetForeclosureDetails"
	PreApprovedLoan_InitiateLoanClosure_FullMethodName             = "/preapprovedloan.PreApprovedLoan/InitiateLoanClosure"
	PreApprovedLoan_CheckLmsDataDifference_FullMethodName          = "/preapprovedloan.PreApprovedLoan/CheckLmsDataDifference"
	PreApprovedLoan_GenerateEsignAgreement_FullMethodName          = "/preapprovedloan.PreApprovedLoan/GenerateEsignAgreement"
	PreApprovedLoan_GetPrePayDetails_FullMethodName                = "/preapprovedloan.PreApprovedLoan/GetPrePayDetails"
	PreApprovedLoan_AddEmploymentDetailsSync_FullMethodName        = "/preapprovedloan.PreApprovedLoan/AddEmploymentDetailsSync"
	PreApprovedLoan_SaveContactDetails_FullMethodName              = "/preapprovedloan.PreApprovedLoan/SaveContactDetails"
	PreApprovedLoan_InitiateAutoPayExecution_FullMethodName        = "/preapprovedloan.PreApprovedLoan/InitiateAutoPayExecution"
	PreApprovedLoan_AddAddressDetailsSync_FullMethodName           = "/preapprovedloan.PreApprovedLoan/AddAddressDetailsSync"
	PreApprovedLoan_GetActiveLoanPaymentRequests_FullMethodName    = "/preapprovedloan.PreApprovedLoan/GetActiveLoanPaymentRequests"
	PreApprovedLoan_ModifyLoanTerms_FullMethodName                 = "/preapprovedloan.PreApprovedLoan/ModifyLoanTerms"
	PreApprovedLoan_GetAAConsentCollectionDetails_FullMethodName   = "/preapprovedloan.PreApprovedLoan/GetAAConsentCollectionDetails"
	PreApprovedLoan_GetApplicantByVendorApplicantId_FullMethodName = "/preapprovedloan.PreApprovedLoan/GetApplicantByVendorApplicantId"
	PreApprovedLoan_CollectData_FullMethodName                     = "/preapprovedloan.PreApprovedLoan/CollectData"
	PreApprovedLoan_GetFederalLoanCustomerDetails_FullMethodName   = "/preapprovedloan.PreApprovedLoan/GetFederalLoanCustomerDetails"
	PreApprovedLoan_GetRedirectDL_FullMethodName                   = "/preapprovedloan.PreApprovedLoan/GetRedirectDL"
	PreApprovedLoan_CollectFormData_FullMethodName                 = "/preapprovedloan.PreApprovedLoan/CollectFormData"
	PreApprovedLoan_GetLoanUserStatusV2_FullMethodName             = "/preapprovedloan.PreApprovedLoan/GetLoanUserStatusV2"
	PreApprovedLoan_IsPrepayAllowed_FullMethodName                 = "/preapprovedloan.PreApprovedLoan/IsPrepayAllowed"
)

// PreApprovedLoanClient is the client API for PreApprovedLoan service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PreApprovedLoanClient interface {
	// This rpc can be used to decide on which screen to land user in the pre-approved-loan flow.
	// If the user doesn't have any loan requests/accounts, but a loan offer,
	// they will be taken to the landing screen otherwise the management screen by rpc status
	// RPC status - Offer -> User doesn't have any loan requests, but has an active offer
	// RPC status - Applications -> User already has some loan requests
	// RPC status - StatusPermissionDenied -> User doesn't have any loan requests and is not eligible for any loans
	GetLandingInfo(ctx context.Context, in *GetLandingInfoRequest, opts ...grpc.CallOption) (*GetLandingInfoResponse, error)
	// This rpc is a successor of GetLandingInfo RPC, that decides on which screen to land user in the pre-approved loan flow.
	// If the user has a loan account/loan request then dashboard is displayed, else if user has loan offer, then loan
	// offer is displayed.
	// RPC status - Offer -> User doesn't have any loan requests, but has an active offer
	// RPC status - Applications -> User already has some loan requests
	// RPC status - StatusPermissionDenied -> User doesn't have any loan requests and is not eligible for any loans
	GetLandingInfoV2(ctx context.Context, in *GetLandingInfoV2Request, opts ...grpc.CallOption) (*GetLandingInfoV2Response, error)
	// This rpc is a successor of GetLandingInfoV2 RPC
	// This doesn't take the decision and instead passes on all the details required to create landing screen
	GetLandingInfoV3(ctx context.Context, in *GetLandingInfoV3Request, opts ...grpc.CallOption) (*GetLandingInfoV3Response, error)
	// used to show the details of the loan offer
	GetOfferDetails(ctx context.Context, in *GetOfferDetailsRequest, opts ...grpc.CallOption) (*GetOfferDetailsResponse, error)
	// to generate confirmation code(for now OTP)
	GenerateConfirmationCode(ctx context.Context, in *GenerateConfirmationCodeRequest, opts ...grpc.CallOption) (*GenerateConfirmationCodeResponse, error)
	// starts workflow for loan application
	ApplyForLoan(ctx context.Context, in *ApplyForLoanRequest, opts ...grpc.CallOption) (*ApplyForLoanResponse, error)
	// submit application to vendor
	ConfirmApplication(ctx context.Context, in *ConfirmApplicationRequest, opts ...grpc.CallOption) (*ConfirmApplicationResponse, error)
	// fetch status of application
	GetApplicationStatus(ctx context.Context, in *GetApplicationStatusRequest, opts ...grpc.CallOption) (*GetApplicationStatusResponse, error)
	// fetch status of application using sync proxy workflow
	GetApplicationStatusSync(ctx context.Context, in *GetApplicationStatusSyncRequest, opts ...grpc.CallOption) (*GetApplicationStatusSyncResponse, error)
	// to enable status poll for pre approved loan initiated liveness
	GetLivenessStatus(ctx context.Context, in *GetLivenessStatusRequest, opts ...grpc.CallOption) (*GetLivenessStatusResponse, error)
	// to power loan management screen
	GetDashboard(ctx context.Context, in *GetDashboardRequest, opts ...grpc.CallOption) (*GetDashboardResponse, error)
	// to power loan management screen
	GetDashboardV2(ctx context.Context, in *GetDashboardRequest, opts ...grpc.CallOption) (*GetDashboardResponse, error)
	// to cancel an ongoing loan application
	CancelApplication(ctx context.Context, in *CancelApplicationRequest, opts ...grpc.CallOption) (*CancelApplicationResponse, error)
	// to initiate E-Sign (KFS) process
	InitiateESign(ctx context.Context, in *InitiateESignRequest, opts ...grpc.CallOption) (*InitiateESignResponse, error)
	// to get loan details for a user for a loan
	GetLoanDetails(ctx context.Context, in *GetLoanDetailsRequest, opts ...grpc.CallOption) (*GetLoanDetailsResponse, error)
	// to make loan pre payment
	PrePayLoan(ctx context.Context, in *PrePayLoanRequest, opts ...grpc.CallOption) (*PrePayLoanResponse, error)
	// to check the make payment order status for pre-pay/close
	// RPC to check the status related to any pre-approved loan activity.
	// The RPC can be used to fetch he status of pre-payment of a loan account of pre-closure of a loan account.
	GetLoanActivityStatus(ctx context.Context, in *GetLoanActivityStatusRequest, opts ...grpc.CallOption) (*GetLoanActivityStatusResponse, error)
	// rpc to fetch all transactions/activities for a loan account
	// loads the data from db (loan_activities, loan_payment_requests) and populate the fields accordingly date wise sorted in desc order (latest first)
	GetAllTransactions(ctx context.Context, in *GetAllTransactionsRequest, opts ...grpc.CallOption) (*GetAllTransactionsResponse, error)
	// rpc to fetch the transaction receipt/details according to a loan activity
	GetTransactionReceipt(ctx context.Context, in *GetTransactionReceiptRequest, opts ...grpc.CallOption) (*GetTransactionReceiptResponse, error)
	// External rpc to fetch loan details for any user (used by ask fi)
	// The default behavior of the RPC is to send details for all loan accounts belonging to the actor irrespective of the loan status (active/closed).
	// To fetch loan details for specific loan accounts, the loanAccountId list field can be used.
	// If loanAccountIds is nil, fetch details of all loan accounts by actorId.
	GetLoanAccountDetails(ctx context.Context, in *GetLoanAccountDetailsRequest, opts ...grpc.CallOption) (*GetLoanAccountDetailsResponse, error)
	// External rpc to fetch latest loan offer for the user, if any, for each vendor.
	// If no loan offer exists, rpc returns loan_offers nil
	// Else, responds with a list of latest loan offers from each vendor which are eligible.
	// Each loan offer is active and user can take loan against that, if is_active field is true.
	// If is_active is false, the offer might be expired, or user would have taken loan against that offer
	GetLoanOffers(ctx context.Context, in *GetLoanOffersRequest, opts ...grpc.CallOption) (*GetLoanOffersResponse, error)
	// External rpc to fetch recent loan application started by the user, if any.
	// Fetches recent loan applications by actor id for all the vendors, if any.
	GetRecentLoanApplicationDetails(ctx context.Context, in *GetRecentLoanApplicationDetailsRequest, opts ...grpc.CallOption) (*GetRecentLoanApplicationDetailsResponse, error)
	// External paginated rpc to fetch transactions for a user
	// If the LoanAccountId list is populated, send the transaction details of only those loans present in the same.
	// If not then send the transaction details of all loan accounts (active / closed) associated with the actor.
	// Sorted in decreasing order of txn time.
	GetLoanTransactions(ctx context.Context, in *GetLoanTransactionsRequest, opts ...grpc.CallOption) (*GetLoanTransactionsResponse, error)
	// for populating different fields in pal home card, it will return response data based on type of card to be shown
	GetLoanSummaryForHome(ctx context.Context, in *GetLoanSummaryForHomeRequest, opts ...grpc.CallOption) (*GetLoanSummaryForHomeResponse, error)
	// External rpc to estimate credit limit utilised through loan, including applications completed by user
	EstimateCreditUtilised(ctx context.Context, in *EstimateCreditUtilisedRequest, opts ...grpc.CallOption) (*EstimateCreditUtilisedResponse, error)
	// rpc to persist the user's address in db
	AddAddressDetails(ctx context.Context, in *AddAddressDetailsRequest, opts ...grpc.CallOption) (*AddAddressDetailsResponse, error)
	// rpc to persist the user's employment in db
	AddEmploymentDetails(ctx context.Context, in *AddEmploymentDetailsRequest, opts ...grpc.CallOption) (*AddEmploymentDetailsResponse, error)
	// RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
	// ActorId is a mandatory parameter in the Request
	// Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
	// INVALID ARGUMENT if any mandatory param is missing
	// RECORD NOT FOUND if no elements found for the given user on this screen
	// INTERNAL SERVER ERROR if any error in processing
	// OK if the list of targeted comms elements is fetched successfully
	FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error)
	// DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
	DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error)
	// rpc to let customer confirm the revised loan details based on their occupation. This is called from the hustle screen and takes user to poll screen
	ConfirmRevisedLoanDetails(ctx context.Context, in *ConfirmRevisedLoanDetailsRequest, opts ...grpc.CallOption) (*ConfirmRevisedLoanDetailsResponse, error)
	// this rpc is to acknowledge client callbacks
	// whenever clients have to send any callback to BE, they can use this RPC. Not necessarily only for success cases,
	// callbacks could just be that the SDK flow has started.
	ClientCallback(ctx context.Context, in *ClientCallbackRequest, opts ...grpc.CallOption) (*ClientCallbackResponse, error)
	// rpc to make a vg call in idfc flow to verify the PAN details entered by the user
	VerifyDetails(ctx context.Context, in *VerifyDetailsRequest, opts ...grpc.CallOption) (*VerifyDetailsResponse, error)
	// external rpc to get early salary details for salary client
	// returns multiple stats which could be possible and data needed to be shown for them
	GetEarlySalaryDetails(ctx context.Context, in *GetEarlySalaryDetailsRequest, opts ...grpc.CallOption) (*GetEarlySalaryDetailsResponse, error)
	// rpc to record consent for ckyc verification from the user
	VerifyCkycDetails(ctx context.Context, in *VerifyCkycDetailsRequest, opts ...grpc.CallOption) (*VerifyCkycDetailsResponse, error)
	// rpc to update loan schedule of a loan account
	RefreshLMSSchedule(ctx context.Context, in *RefreshLmsScheduleRequest, opts ...grpc.CallOption) (*RefreshLmsScheduleResponse, error)
	// rpc to fetch web view from vendor
	GetMandateViewData(ctx context.Context, in *GetMandateViewDataRequest, opts ...grpc.CallOption) (*GetMandateViewDataResponse, error)
	// rpc to execute collections for any flow
	ExecuteCollection(ctx context.Context, in *ExecuteCollectionRequest, opts ...grpc.CallOption) (*ExecuteCollectionResponse, error)
	// rpc to reconcile loan txns from vendor
	ReconLoanActivity(ctx context.Context, in *ReconLoanActivityRequest, opts ...grpc.CallOption) (*ReconLoanActivityResponse, error)
	// Returns loan schedule if present in our DB
	GetLoanSchedule(ctx context.Context, in *GetLoanScheduleRequest, opts ...grpc.CallOption) (*GetLoanScheduleResponse, error)
	// rpc to persist the user entered PAN and DOB in Db
	AddPanAndDobData(ctx context.Context, in *AddPanAndDobDataRequest, opts ...grpc.CallOption) (*AddPanAndDobDataResponse, error)
	// GetLoanInstallmentPayoutDetails RPC to fetch the loan installment payout details, installment info and
	// loan account details for a given installment id
	GetLoanInstallmentPayoutDetails(ctx context.Context, in *GetLoanInstallmentPayoutDetailsRequest, opts ...grpc.CallOption) (*GetLoanInstallmentPayoutDetailsResponse, error)
	// rpc to persist the user entered Name and Gender
	AddNameAndGender(ctx context.Context, in *AddNameAndGenderRequest, opts ...grpc.CallOption) (*AddNameAndGenderResponse, error)
	// rpc to onboard a user for loan journey and starts workflow for loan eligibility
	// creates loan applicant and loan request if not present already
	CheckLoanEligibility(ctx context.Context, in *CheckLoanEligibilityRequest, opts ...grpc.CallOption) (*CheckLoanEligibilityResponse, error)
	// rpc to initiate the credit report fetch process for the user
	FetchCreditReport(ctx context.Context, in *FetchCreditReportRequest, opts ...grpc.CallOption) (*FetchCreditReportResponse, error)
	// GetLoanDefaultDetails RPC provides an aggregated view of all the defaults associated with a loan account across payouts
	GetLoanDefaultDetails(ctx context.Context, in *GetLoanDefaultDetailsRequest, opts ...grpc.CallOption) (*GetLoanDefaultDetailsResponse, error)
	// rpc to persist the user's banking details in db
	AddBankingDetails(ctx context.Context, in *AddBankingDetailsRequest, opts ...grpc.CallOption) (*AddBankingDetailsResponse, error)
	// rpc to get review details for loan application/eligibility summary details screen
	GetLoanReviewDetails(ctx context.Context, in *GetLoanReviewDetailsRequest, opts ...grpc.CallOption) (*GetLoanReviewDetailsResponse, error)
	// rpc to submit post user reviews the loan application/eligibility summary details
	SubmitReviewLoanDetails(ctx context.Context, in *SubmitReviewLoanDetailsRequest, opts ...grpc.CallOption) (*SubmitReviewLoanDetailsResponse, error)
	// external rpc to fetch all data stored for an applicant during a loan journey. Journey could be loan eligibility or loan application.
	GetLoanUserDetails(ctx context.Context, in *GetLoanUserDetailsRequest, opts ...grpc.CallOption) (*GetLoanUserDetailsResponse, error)
	// rpc to acquire LoanId using LoanHeader And ActorId can return errors if no loanId can be found for given LoanHeader and ActorId
	// or if there are multiple loanId for given LoanHeader And ActorId then log error and proceed with the latest loanId
	GetLoanIdByHeaderAndActorId(ctx context.Context, in *GetLoanIdByHeaderAndActorIdRequest, opts ...grpc.CallOption) (*GetLoanIdByHeaderAndActorIdResponse, error)
	// GetDowntimeStatus RPC to check if the vendor is down for maintenance
	GetDowntimeStatus(ctx context.Context, in *GetDowntimeStatusRequest, opts ...grpc.CallOption) (*GetDowntimeStatusResponse, error)
	// InitiateSiSetup RPC creates loan request and triggers SI workflow
	InitiateSiSetup(ctx context.Context, in *InitiateSiSetupRequest, opts ...grpc.CallOption) (*InitiateSiSetupResponse, error)
	// RPC to get completed loan requests for an actor by check all the existing loan requests
	GetCompletedLoanRequests(ctx context.Context, in *GetCompletedLoanRequestsRequest, opts ...grpc.CallOption) (*GetCompletedLoanRequestsResponse, error)
	// RPC to get active loan requests for an actor by check all the existing loan requests
	GetActiveLoanRequests(ctx context.Context, in *GetActiveLoanRequestsRequest, opts ...grpc.CallOption) (*GetActiveLoanRequestsResponse, error)
	// GetVendorApplicantId fetches the applicant id at vendor's end for the given user based on the loan header
	GetVendorApplicantId(ctx context.Context, in *GetVendorApplicantIdRequest, opts ...grpc.CallOption) (*GetVendorApplicantIdResponse, error)
	// rpc to let the backend know which income verification option is selected by the user
	SubmitIncomeVerificationOption(ctx context.Context, in *SubmitIncomeVerificationOptionRequest, opts ...grpc.CallOption) (*SubmitIncomeVerificationOptionResponse, error)
	// rpc to fetch mandate view details like webview/sdk and necessary params for the same from backend
	GetMandateViewDataV2(ctx context.Context, in *GetMandateViewDataV2Request, opts ...grpc.CallOption) (*GetMandateViewDataV2Response, error)
	// rpc to fetch pwa redirection details from vendor for redirecting the user to the pwa flow,
	// some flows are driven through a pwa journey on vendor side so this rpc is useful to fetch the pwa redirection details from the vendor to redirect the user to the pwa flow
	// e.g moneyview lender loan application and loan servicing flows are mostly driven on their pwa.
	GetPWARedirectionDetails(ctx context.Context, in *GetPWARedirectionDetailsRequest, opts ...grpc.CallOption) (*GetPWARedirectionDetailsResponse, error)
	// RecordUserAction rpc can be used to record some client action in a loan flow and return some action based on the response.
	RecordUserAction(ctx context.Context, in *RecordUserActionRequest, opts ...grpc.CallOption) (*RecordUserActionResponse, error)
	// ProcessOffAppLoanRepayment processes loan repayment that was done off app
	ProcessOffAppLoanRepayment(ctx context.Context, in *ProcessOffAppLoanRepaymentRequest, opts ...grpc.CallOption) (*ProcessOffAppLoanRepaymentResponse, error)
	// GetLoansUserStatus gives the loans status of an actor, if any loan account or loan request is active for the user.
	// This is an external api that can be used by other services to get the status of an user at loans end
	GetLoansUserStatus(ctx context.Context, in *GetLoansUserStatusRequest, opts ...grpc.CallOption) (*GetLoansUserStatusResponse, error)
	// GetIncomeVerificationInfo gives income verification info related to income verifications methods in pl flows,
	// for Income Verification through ITR, it will give max attempts reached or not
	GetIncomeVerificationInfo(ctx context.Context, in *GetIncomeVerificationInfoRequest, opts ...grpc.CallOption) (*GetIncomeVerificationInfoResponse, error)
	// GetVkycDeeplink can be used to do the vkyc related executions in the flow.
	GetVkycDeeplink(ctx context.Context, in *GetVkycDeeplinkRequest, opts ...grpc.CallOption) (*GetVkycDeeplinkResponse, error)
	// GetForeclosureDetails RPC simplifies fetching loan foreclosure details from backend
	// Currently we are only fetching foreclosure details from vendor
	// This RPC aims to eliminate complex wire dependencies currently present in the foreclosure details interface and
	// provide one common API which can be used across all the places to fetch the details
	GetForeclosureDetails(ctx context.Context, in *GetForeclosureDetailsRequest, opts ...grpc.CallOption) (*GetForeclosureDetailsResponse, error)
	// InitiateLoanClosure RPC initiates loan closure workflow in the backend for given loan account id
	// RPC returns OK and loan request id to identify workflow
	// RPC returns ALREADY EXISTS status code if some closure workflow already exists in successful state
	InitiateLoanClosure(ctx context.Context, in *InitiateLoanClosureRequest, opts ...grpc.CallOption) (*InitiateLoanClosureResponse, error)
	// CheckLmsDataDifference RPC is used to find differences in lms details for a given loan account id
	// between two lms data sources.
	CheckLmsDataDifference(ctx context.Context, in *CheckLmsDataDifferenceRequest, opts ...grpc.CallOption) (*CheckLmsDataDifferenceResponse, error)
	// This rpc generates esign agreement url stored in aws  if previous url has expired or does not exist.
	GenerateEsignAgreement(ctx context.Context, in *GenerateEsignAgreementRequest, opts ...grpc.CallOption) (*GenerateEsignAgreementResponse, error)
	// rpc to fetch pre-pay details like emi, charges, overdue, foreclosue amount etc
	GetPrePayDetails(ctx context.Context, in *GetPrePayDetailsRequest, opts ...grpc.CallOption) (*GetPrePayDetailsResponse, error)
	AddEmploymentDetailsSync(ctx context.Context, in *AddEmploymentDetailsRequest, opts ...grpc.CallOption) (*AddEmploymentDetailsResponse, error)
	// rpc to save details for alternate contact information
	SaveContactDetails(ctx context.Context, in *SaveContactDetailsRequest, opts ...grpc.CallOption) (*SaveContactDetailsResponse, error)
	// rpc to initiate auto pay execution for a given loan account id, it is responsible for deciding the auto-pay execution amount as well
	// returns FailedPrecondition if the loan account does not have any due/overdue amount to be collected
	InitiateAutoPayExecution(ctx context.Context, in *InitiateAutoPayExecutionRequest, opts ...grpc.CallOption) (*InitiateAutoPayExecutionResponse, error)
	// rpc to add address details in sync fashion
	AddAddressDetailsSync(ctx context.Context, in *AddAddressDetailsRequest, opts ...grpc.CallOption) (*AddAddressDetailsResponse, error)
	// RPC to get active loan requests for an actor by check all the existing loan requests
	GetActiveLoanPaymentRequests(ctx context.Context, in *GetActiveLoanPaymentRequestsRequest, opts ...grpc.CallOption) (*GetActiveLoanPaymentRequestsResponse, error)
	// ModifyLoanTerms will modify the loan terms (loan amount and tenure) for an active loan application
	// this is required when the offer is changed during the loan application and user needs to select the amount and tenure again after checking the revised offer
	ModifyLoanTerms(ctx context.Context, in *ModifyLoanTermsRequest, opts ...grpc.CallOption) (*ModifyLoanTermsResponse, error)
	GetAAConsentCollectionDetails(ctx context.Context, in *GetAAConsentCollectionDetailsRequest, opts ...grpc.CallOption) (*GetAAConsentCollectionDetailsResponse, error)
	// TODO: Temporary rpc to unblock nbfc testing
	GetApplicantByVendorApplicantId(ctx context.Context, in *GetApplicantByVendorApplicantIdRequest, opts ...grpc.CallOption) (*GetApplicantByVendorApplicantIdResponse, error)
	// To collect and save data like user selected loan amount, consents etc
	CollectData(ctx context.Context, in *CollectDataRequest, opts ...grpc.CallOption) (*CollectDataResponse, error)
	// To get Customer details like BRE Reference Number for Federal Loan
	GetFederalLoanCustomerDetails(ctx context.Context, in *GetFederalLoanCustomerDetailsRequest, opts ...grpc.CallOption) (*GetFederalLoanCustomerDetailsResponse, error)
	// GetRedirectDL: RPC that determines the next screen to show based on loan header, actor ID and client request ID.
	// Used by service providers to get appropriate deeplink for redirecting users to the next step in loan flow.
	// Returns a deeplink response that contains the next screen to display.
	GetRedirectDL(ctx context.Context, in *GetRedirectDLRequest, opts ...grpc.CallOption) (*GetRedirectDLResponse, error)
	// To collect various types of form data from the user
	CollectFormData(ctx context.Context, in *CollectFormDataRequest, opts ...grpc.CallOption) (*CollectFormDataResponse, error)
	// To get the current status of the loan user and what user can do next
	GetLoanUserStatusV2(ctx context.Context, in *GetLoanUserStatusV2Request, opts ...grpc.CallOption) (*GetLoanUserStatusV2Response, error)
	// To check if prepay is allowed for a given loan account id
	IsPrepayAllowed(ctx context.Context, in *IsPrepayAllowedRequest, opts ...grpc.CallOption) (*IsPrepayAllowedResponse, error)
}

type preApprovedLoanClient struct {
	cc grpc.ClientConnInterface
}

func NewPreApprovedLoanClient(cc grpc.ClientConnInterface) PreApprovedLoanClient {
	return &preApprovedLoanClient{cc}
}

func (c *preApprovedLoanClient) GetLandingInfo(ctx context.Context, in *GetLandingInfoRequest, opts ...grpc.CallOption) (*GetLandingInfoResponse, error) {
	out := new(GetLandingInfoResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLandingInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLandingInfoV2(ctx context.Context, in *GetLandingInfoV2Request, opts ...grpc.CallOption) (*GetLandingInfoV2Response, error) {
	out := new(GetLandingInfoV2Response)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLandingInfoV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLandingInfoV3(ctx context.Context, in *GetLandingInfoV3Request, opts ...grpc.CallOption) (*GetLandingInfoV3Response, error) {
	out := new(GetLandingInfoV3Response)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLandingInfoV3_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetOfferDetails(ctx context.Context, in *GetOfferDetailsRequest, opts ...grpc.CallOption) (*GetOfferDetailsResponse, error) {
	out := new(GetOfferDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetOfferDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GenerateConfirmationCode(ctx context.Context, in *GenerateConfirmationCodeRequest, opts ...grpc.CallOption) (*GenerateConfirmationCodeResponse, error) {
	out := new(GenerateConfirmationCodeResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GenerateConfirmationCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) ApplyForLoan(ctx context.Context, in *ApplyForLoanRequest, opts ...grpc.CallOption) (*ApplyForLoanResponse, error) {
	out := new(ApplyForLoanResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_ApplyForLoan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) ConfirmApplication(ctx context.Context, in *ConfirmApplicationRequest, opts ...grpc.CallOption) (*ConfirmApplicationResponse, error) {
	out := new(ConfirmApplicationResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_ConfirmApplication_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetApplicationStatus(ctx context.Context, in *GetApplicationStatusRequest, opts ...grpc.CallOption) (*GetApplicationStatusResponse, error) {
	out := new(GetApplicationStatusResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetApplicationStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetApplicationStatusSync(ctx context.Context, in *GetApplicationStatusSyncRequest, opts ...grpc.CallOption) (*GetApplicationStatusSyncResponse, error) {
	out := new(GetApplicationStatusSyncResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetApplicationStatusSync_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLivenessStatus(ctx context.Context, in *GetLivenessStatusRequest, opts ...grpc.CallOption) (*GetLivenessStatusResponse, error) {
	out := new(GetLivenessStatusResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLivenessStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetDashboard(ctx context.Context, in *GetDashboardRequest, opts ...grpc.CallOption) (*GetDashboardResponse, error) {
	out := new(GetDashboardResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetDashboard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetDashboardV2(ctx context.Context, in *GetDashboardRequest, opts ...grpc.CallOption) (*GetDashboardResponse, error) {
	out := new(GetDashboardResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetDashboardV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) CancelApplication(ctx context.Context, in *CancelApplicationRequest, opts ...grpc.CallOption) (*CancelApplicationResponse, error) {
	out := new(CancelApplicationResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_CancelApplication_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) InitiateESign(ctx context.Context, in *InitiateESignRequest, opts ...grpc.CallOption) (*InitiateESignResponse, error) {
	out := new(InitiateESignResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_InitiateESign_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoanDetails(ctx context.Context, in *GetLoanDetailsRequest, opts ...grpc.CallOption) (*GetLoanDetailsResponse, error) {
	out := new(GetLoanDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoanDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) PrePayLoan(ctx context.Context, in *PrePayLoanRequest, opts ...grpc.CallOption) (*PrePayLoanResponse, error) {
	out := new(PrePayLoanResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_PrePayLoan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoanActivityStatus(ctx context.Context, in *GetLoanActivityStatusRequest, opts ...grpc.CallOption) (*GetLoanActivityStatusResponse, error) {
	out := new(GetLoanActivityStatusResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoanActivityStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetAllTransactions(ctx context.Context, in *GetAllTransactionsRequest, opts ...grpc.CallOption) (*GetAllTransactionsResponse, error) {
	out := new(GetAllTransactionsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetAllTransactions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetTransactionReceipt(ctx context.Context, in *GetTransactionReceiptRequest, opts ...grpc.CallOption) (*GetTransactionReceiptResponse, error) {
	out := new(GetTransactionReceiptResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetTransactionReceipt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoanAccountDetails(ctx context.Context, in *GetLoanAccountDetailsRequest, opts ...grpc.CallOption) (*GetLoanAccountDetailsResponse, error) {
	out := new(GetLoanAccountDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoanAccountDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoanOffers(ctx context.Context, in *GetLoanOffersRequest, opts ...grpc.CallOption) (*GetLoanOffersResponse, error) {
	out := new(GetLoanOffersResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoanOffers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetRecentLoanApplicationDetails(ctx context.Context, in *GetRecentLoanApplicationDetailsRequest, opts ...grpc.CallOption) (*GetRecentLoanApplicationDetailsResponse, error) {
	out := new(GetRecentLoanApplicationDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetRecentLoanApplicationDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoanTransactions(ctx context.Context, in *GetLoanTransactionsRequest, opts ...grpc.CallOption) (*GetLoanTransactionsResponse, error) {
	out := new(GetLoanTransactionsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoanTransactions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoanSummaryForHome(ctx context.Context, in *GetLoanSummaryForHomeRequest, opts ...grpc.CallOption) (*GetLoanSummaryForHomeResponse, error) {
	out := new(GetLoanSummaryForHomeResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoanSummaryForHome_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) EstimateCreditUtilised(ctx context.Context, in *EstimateCreditUtilisedRequest, opts ...grpc.CallOption) (*EstimateCreditUtilisedResponse, error) {
	out := new(EstimateCreditUtilisedResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_EstimateCreditUtilised_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) AddAddressDetails(ctx context.Context, in *AddAddressDetailsRequest, opts ...grpc.CallOption) (*AddAddressDetailsResponse, error) {
	out := new(AddAddressDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_AddAddressDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) AddEmploymentDetails(ctx context.Context, in *AddEmploymentDetailsRequest, opts ...grpc.CallOption) (*AddEmploymentDetailsResponse, error) {
	out := new(AddEmploymentDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_AddEmploymentDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	out := new(dynamic_elements.FetchDynamicElementsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_FetchDynamicElements_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	out := new(dynamic_elements.DynamicElementCallbackResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_DynamicElementCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) ConfirmRevisedLoanDetails(ctx context.Context, in *ConfirmRevisedLoanDetailsRequest, opts ...grpc.CallOption) (*ConfirmRevisedLoanDetailsResponse, error) {
	out := new(ConfirmRevisedLoanDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_ConfirmRevisedLoanDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) ClientCallback(ctx context.Context, in *ClientCallbackRequest, opts ...grpc.CallOption) (*ClientCallbackResponse, error) {
	out := new(ClientCallbackResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_ClientCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) VerifyDetails(ctx context.Context, in *VerifyDetailsRequest, opts ...grpc.CallOption) (*VerifyDetailsResponse, error) {
	out := new(VerifyDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_VerifyDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetEarlySalaryDetails(ctx context.Context, in *GetEarlySalaryDetailsRequest, opts ...grpc.CallOption) (*GetEarlySalaryDetailsResponse, error) {
	out := new(GetEarlySalaryDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetEarlySalaryDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) VerifyCkycDetails(ctx context.Context, in *VerifyCkycDetailsRequest, opts ...grpc.CallOption) (*VerifyCkycDetailsResponse, error) {
	out := new(VerifyCkycDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_VerifyCkycDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) RefreshLMSSchedule(ctx context.Context, in *RefreshLmsScheduleRequest, opts ...grpc.CallOption) (*RefreshLmsScheduleResponse, error) {
	out := new(RefreshLmsScheduleResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_RefreshLMSSchedule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetMandateViewData(ctx context.Context, in *GetMandateViewDataRequest, opts ...grpc.CallOption) (*GetMandateViewDataResponse, error) {
	out := new(GetMandateViewDataResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetMandateViewData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) ExecuteCollection(ctx context.Context, in *ExecuteCollectionRequest, opts ...grpc.CallOption) (*ExecuteCollectionResponse, error) {
	out := new(ExecuteCollectionResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_ExecuteCollection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) ReconLoanActivity(ctx context.Context, in *ReconLoanActivityRequest, opts ...grpc.CallOption) (*ReconLoanActivityResponse, error) {
	out := new(ReconLoanActivityResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_ReconLoanActivity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoanSchedule(ctx context.Context, in *GetLoanScheduleRequest, opts ...grpc.CallOption) (*GetLoanScheduleResponse, error) {
	out := new(GetLoanScheduleResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoanSchedule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) AddPanAndDobData(ctx context.Context, in *AddPanAndDobDataRequest, opts ...grpc.CallOption) (*AddPanAndDobDataResponse, error) {
	out := new(AddPanAndDobDataResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_AddPanAndDobData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoanInstallmentPayoutDetails(ctx context.Context, in *GetLoanInstallmentPayoutDetailsRequest, opts ...grpc.CallOption) (*GetLoanInstallmentPayoutDetailsResponse, error) {
	out := new(GetLoanInstallmentPayoutDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoanInstallmentPayoutDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) AddNameAndGender(ctx context.Context, in *AddNameAndGenderRequest, opts ...grpc.CallOption) (*AddNameAndGenderResponse, error) {
	out := new(AddNameAndGenderResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_AddNameAndGender_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) CheckLoanEligibility(ctx context.Context, in *CheckLoanEligibilityRequest, opts ...grpc.CallOption) (*CheckLoanEligibilityResponse, error) {
	out := new(CheckLoanEligibilityResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_CheckLoanEligibility_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) FetchCreditReport(ctx context.Context, in *FetchCreditReportRequest, opts ...grpc.CallOption) (*FetchCreditReportResponse, error) {
	out := new(FetchCreditReportResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_FetchCreditReport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoanDefaultDetails(ctx context.Context, in *GetLoanDefaultDetailsRequest, opts ...grpc.CallOption) (*GetLoanDefaultDetailsResponse, error) {
	out := new(GetLoanDefaultDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoanDefaultDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) AddBankingDetails(ctx context.Context, in *AddBankingDetailsRequest, opts ...grpc.CallOption) (*AddBankingDetailsResponse, error) {
	out := new(AddBankingDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_AddBankingDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoanReviewDetails(ctx context.Context, in *GetLoanReviewDetailsRequest, opts ...grpc.CallOption) (*GetLoanReviewDetailsResponse, error) {
	out := new(GetLoanReviewDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoanReviewDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) SubmitReviewLoanDetails(ctx context.Context, in *SubmitReviewLoanDetailsRequest, opts ...grpc.CallOption) (*SubmitReviewLoanDetailsResponse, error) {
	out := new(SubmitReviewLoanDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_SubmitReviewLoanDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoanUserDetails(ctx context.Context, in *GetLoanUserDetailsRequest, opts ...grpc.CallOption) (*GetLoanUserDetailsResponse, error) {
	out := new(GetLoanUserDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoanUserDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoanIdByHeaderAndActorId(ctx context.Context, in *GetLoanIdByHeaderAndActorIdRequest, opts ...grpc.CallOption) (*GetLoanIdByHeaderAndActorIdResponse, error) {
	out := new(GetLoanIdByHeaderAndActorIdResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoanIdByHeaderAndActorId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetDowntimeStatus(ctx context.Context, in *GetDowntimeStatusRequest, opts ...grpc.CallOption) (*GetDowntimeStatusResponse, error) {
	out := new(GetDowntimeStatusResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetDowntimeStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) InitiateSiSetup(ctx context.Context, in *InitiateSiSetupRequest, opts ...grpc.CallOption) (*InitiateSiSetupResponse, error) {
	out := new(InitiateSiSetupResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_InitiateSiSetup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetCompletedLoanRequests(ctx context.Context, in *GetCompletedLoanRequestsRequest, opts ...grpc.CallOption) (*GetCompletedLoanRequestsResponse, error) {
	out := new(GetCompletedLoanRequestsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetCompletedLoanRequests_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetActiveLoanRequests(ctx context.Context, in *GetActiveLoanRequestsRequest, opts ...grpc.CallOption) (*GetActiveLoanRequestsResponse, error) {
	out := new(GetActiveLoanRequestsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetActiveLoanRequests_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetVendorApplicantId(ctx context.Context, in *GetVendorApplicantIdRequest, opts ...grpc.CallOption) (*GetVendorApplicantIdResponse, error) {
	out := new(GetVendorApplicantIdResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetVendorApplicantId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) SubmitIncomeVerificationOption(ctx context.Context, in *SubmitIncomeVerificationOptionRequest, opts ...grpc.CallOption) (*SubmitIncomeVerificationOptionResponse, error) {
	out := new(SubmitIncomeVerificationOptionResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_SubmitIncomeVerificationOption_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetMandateViewDataV2(ctx context.Context, in *GetMandateViewDataV2Request, opts ...grpc.CallOption) (*GetMandateViewDataV2Response, error) {
	out := new(GetMandateViewDataV2Response)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetMandateViewDataV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetPWARedirectionDetails(ctx context.Context, in *GetPWARedirectionDetailsRequest, opts ...grpc.CallOption) (*GetPWARedirectionDetailsResponse, error) {
	out := new(GetPWARedirectionDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetPWARedirectionDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) RecordUserAction(ctx context.Context, in *RecordUserActionRequest, opts ...grpc.CallOption) (*RecordUserActionResponse, error) {
	out := new(RecordUserActionResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_RecordUserAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) ProcessOffAppLoanRepayment(ctx context.Context, in *ProcessOffAppLoanRepaymentRequest, opts ...grpc.CallOption) (*ProcessOffAppLoanRepaymentResponse, error) {
	out := new(ProcessOffAppLoanRepaymentResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_ProcessOffAppLoanRepayment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoansUserStatus(ctx context.Context, in *GetLoansUserStatusRequest, opts ...grpc.CallOption) (*GetLoansUserStatusResponse, error) {
	out := new(GetLoansUserStatusResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoansUserStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetIncomeVerificationInfo(ctx context.Context, in *GetIncomeVerificationInfoRequest, opts ...grpc.CallOption) (*GetIncomeVerificationInfoResponse, error) {
	out := new(GetIncomeVerificationInfoResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetIncomeVerificationInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetVkycDeeplink(ctx context.Context, in *GetVkycDeeplinkRequest, opts ...grpc.CallOption) (*GetVkycDeeplinkResponse, error) {
	out := new(GetVkycDeeplinkResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetVkycDeeplink_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetForeclosureDetails(ctx context.Context, in *GetForeclosureDetailsRequest, opts ...grpc.CallOption) (*GetForeclosureDetailsResponse, error) {
	out := new(GetForeclosureDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetForeclosureDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) InitiateLoanClosure(ctx context.Context, in *InitiateLoanClosureRequest, opts ...grpc.CallOption) (*InitiateLoanClosureResponse, error) {
	out := new(InitiateLoanClosureResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_InitiateLoanClosure_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) CheckLmsDataDifference(ctx context.Context, in *CheckLmsDataDifferenceRequest, opts ...grpc.CallOption) (*CheckLmsDataDifferenceResponse, error) {
	out := new(CheckLmsDataDifferenceResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_CheckLmsDataDifference_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GenerateEsignAgreement(ctx context.Context, in *GenerateEsignAgreementRequest, opts ...grpc.CallOption) (*GenerateEsignAgreementResponse, error) {
	out := new(GenerateEsignAgreementResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GenerateEsignAgreement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetPrePayDetails(ctx context.Context, in *GetPrePayDetailsRequest, opts ...grpc.CallOption) (*GetPrePayDetailsResponse, error) {
	out := new(GetPrePayDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetPrePayDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) AddEmploymentDetailsSync(ctx context.Context, in *AddEmploymentDetailsRequest, opts ...grpc.CallOption) (*AddEmploymentDetailsResponse, error) {
	out := new(AddEmploymentDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_AddEmploymentDetailsSync_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) SaveContactDetails(ctx context.Context, in *SaveContactDetailsRequest, opts ...grpc.CallOption) (*SaveContactDetailsResponse, error) {
	out := new(SaveContactDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_SaveContactDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) InitiateAutoPayExecution(ctx context.Context, in *InitiateAutoPayExecutionRequest, opts ...grpc.CallOption) (*InitiateAutoPayExecutionResponse, error) {
	out := new(InitiateAutoPayExecutionResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_InitiateAutoPayExecution_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) AddAddressDetailsSync(ctx context.Context, in *AddAddressDetailsRequest, opts ...grpc.CallOption) (*AddAddressDetailsResponse, error) {
	out := new(AddAddressDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_AddAddressDetailsSync_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetActiveLoanPaymentRequests(ctx context.Context, in *GetActiveLoanPaymentRequestsRequest, opts ...grpc.CallOption) (*GetActiveLoanPaymentRequestsResponse, error) {
	out := new(GetActiveLoanPaymentRequestsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetActiveLoanPaymentRequests_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) ModifyLoanTerms(ctx context.Context, in *ModifyLoanTermsRequest, opts ...grpc.CallOption) (*ModifyLoanTermsResponse, error) {
	out := new(ModifyLoanTermsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_ModifyLoanTerms_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetAAConsentCollectionDetails(ctx context.Context, in *GetAAConsentCollectionDetailsRequest, opts ...grpc.CallOption) (*GetAAConsentCollectionDetailsResponse, error) {
	out := new(GetAAConsentCollectionDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetAAConsentCollectionDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetApplicantByVendorApplicantId(ctx context.Context, in *GetApplicantByVendorApplicantIdRequest, opts ...grpc.CallOption) (*GetApplicantByVendorApplicantIdResponse, error) {
	out := new(GetApplicantByVendorApplicantIdResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetApplicantByVendorApplicantId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) CollectData(ctx context.Context, in *CollectDataRequest, opts ...grpc.CallOption) (*CollectDataResponse, error) {
	out := new(CollectDataResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_CollectData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetFederalLoanCustomerDetails(ctx context.Context, in *GetFederalLoanCustomerDetailsRequest, opts ...grpc.CallOption) (*GetFederalLoanCustomerDetailsResponse, error) {
	out := new(GetFederalLoanCustomerDetailsResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetFederalLoanCustomerDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetRedirectDL(ctx context.Context, in *GetRedirectDLRequest, opts ...grpc.CallOption) (*GetRedirectDLResponse, error) {
	out := new(GetRedirectDLResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetRedirectDL_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) CollectFormData(ctx context.Context, in *CollectFormDataRequest, opts ...grpc.CallOption) (*CollectFormDataResponse, error) {
	out := new(CollectFormDataResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_CollectFormData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) GetLoanUserStatusV2(ctx context.Context, in *GetLoanUserStatusV2Request, opts ...grpc.CallOption) (*GetLoanUserStatusV2Response, error) {
	out := new(GetLoanUserStatusV2Response)
	err := c.cc.Invoke(ctx, PreApprovedLoan_GetLoanUserStatusV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preApprovedLoanClient) IsPrepayAllowed(ctx context.Context, in *IsPrepayAllowedRequest, opts ...grpc.CallOption) (*IsPrepayAllowedResponse, error) {
	out := new(IsPrepayAllowedResponse)
	err := c.cc.Invoke(ctx, PreApprovedLoan_IsPrepayAllowed_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PreApprovedLoanServer is the server API for PreApprovedLoan service.
// All implementations should embed UnimplementedPreApprovedLoanServer
// for forward compatibility
type PreApprovedLoanServer interface {
	// This rpc can be used to decide on which screen to land user in the pre-approved-loan flow.
	// If the user doesn't have any loan requests/accounts, but a loan offer,
	// they will be taken to the landing screen otherwise the management screen by rpc status
	// RPC status - Offer -> User doesn't have any loan requests, but has an active offer
	// RPC status - Applications -> User already has some loan requests
	// RPC status - StatusPermissionDenied -> User doesn't have any loan requests and is not eligible for any loans
	GetLandingInfo(context.Context, *GetLandingInfoRequest) (*GetLandingInfoResponse, error)
	// This rpc is a successor of GetLandingInfo RPC, that decides on which screen to land user in the pre-approved loan flow.
	// If the user has a loan account/loan request then dashboard is displayed, else if user has loan offer, then loan
	// offer is displayed.
	// RPC status - Offer -> User doesn't have any loan requests, but has an active offer
	// RPC status - Applications -> User already has some loan requests
	// RPC status - StatusPermissionDenied -> User doesn't have any loan requests and is not eligible for any loans
	GetLandingInfoV2(context.Context, *GetLandingInfoV2Request) (*GetLandingInfoV2Response, error)
	// This rpc is a successor of GetLandingInfoV2 RPC
	// This doesn't take the decision and instead passes on all the details required to create landing screen
	GetLandingInfoV3(context.Context, *GetLandingInfoV3Request) (*GetLandingInfoV3Response, error)
	// used to show the details of the loan offer
	GetOfferDetails(context.Context, *GetOfferDetailsRequest) (*GetOfferDetailsResponse, error)
	// to generate confirmation code(for now OTP)
	GenerateConfirmationCode(context.Context, *GenerateConfirmationCodeRequest) (*GenerateConfirmationCodeResponse, error)
	// starts workflow for loan application
	ApplyForLoan(context.Context, *ApplyForLoanRequest) (*ApplyForLoanResponse, error)
	// submit application to vendor
	ConfirmApplication(context.Context, *ConfirmApplicationRequest) (*ConfirmApplicationResponse, error)
	// fetch status of application
	GetApplicationStatus(context.Context, *GetApplicationStatusRequest) (*GetApplicationStatusResponse, error)
	// fetch status of application using sync proxy workflow
	GetApplicationStatusSync(context.Context, *GetApplicationStatusSyncRequest) (*GetApplicationStatusSyncResponse, error)
	// to enable status poll for pre approved loan initiated liveness
	GetLivenessStatus(context.Context, *GetLivenessStatusRequest) (*GetLivenessStatusResponse, error)
	// to power loan management screen
	GetDashboard(context.Context, *GetDashboardRequest) (*GetDashboardResponse, error)
	// to power loan management screen
	GetDashboardV2(context.Context, *GetDashboardRequest) (*GetDashboardResponse, error)
	// to cancel an ongoing loan application
	CancelApplication(context.Context, *CancelApplicationRequest) (*CancelApplicationResponse, error)
	// to initiate E-Sign (KFS) process
	InitiateESign(context.Context, *InitiateESignRequest) (*InitiateESignResponse, error)
	// to get loan details for a user for a loan
	GetLoanDetails(context.Context, *GetLoanDetailsRequest) (*GetLoanDetailsResponse, error)
	// to make loan pre payment
	PrePayLoan(context.Context, *PrePayLoanRequest) (*PrePayLoanResponse, error)
	// to check the make payment order status for pre-pay/close
	// RPC to check the status related to any pre-approved loan activity.
	// The RPC can be used to fetch he status of pre-payment of a loan account of pre-closure of a loan account.
	GetLoanActivityStatus(context.Context, *GetLoanActivityStatusRequest) (*GetLoanActivityStatusResponse, error)
	// rpc to fetch all transactions/activities for a loan account
	// loads the data from db (loan_activities, loan_payment_requests) and populate the fields accordingly date wise sorted in desc order (latest first)
	GetAllTransactions(context.Context, *GetAllTransactionsRequest) (*GetAllTransactionsResponse, error)
	// rpc to fetch the transaction receipt/details according to a loan activity
	GetTransactionReceipt(context.Context, *GetTransactionReceiptRequest) (*GetTransactionReceiptResponse, error)
	// External rpc to fetch loan details for any user (used by ask fi)
	// The default behavior of the RPC is to send details for all loan accounts belonging to the actor irrespective of the loan status (active/closed).
	// To fetch loan details for specific loan accounts, the loanAccountId list field can be used.
	// If loanAccountIds is nil, fetch details of all loan accounts by actorId.
	GetLoanAccountDetails(context.Context, *GetLoanAccountDetailsRequest) (*GetLoanAccountDetailsResponse, error)
	// External rpc to fetch latest loan offer for the user, if any, for each vendor.
	// If no loan offer exists, rpc returns loan_offers nil
	// Else, responds with a list of latest loan offers from each vendor which are eligible.
	// Each loan offer is active and user can take loan against that, if is_active field is true.
	// If is_active is false, the offer might be expired, or user would have taken loan against that offer
	GetLoanOffers(context.Context, *GetLoanOffersRequest) (*GetLoanOffersResponse, error)
	// External rpc to fetch recent loan application started by the user, if any.
	// Fetches recent loan applications by actor id for all the vendors, if any.
	GetRecentLoanApplicationDetails(context.Context, *GetRecentLoanApplicationDetailsRequest) (*GetRecentLoanApplicationDetailsResponse, error)
	// External paginated rpc to fetch transactions for a user
	// If the LoanAccountId list is populated, send the transaction details of only those loans present in the same.
	// If not then send the transaction details of all loan accounts (active / closed) associated with the actor.
	// Sorted in decreasing order of txn time.
	GetLoanTransactions(context.Context, *GetLoanTransactionsRequest) (*GetLoanTransactionsResponse, error)
	// for populating different fields in pal home card, it will return response data based on type of card to be shown
	GetLoanSummaryForHome(context.Context, *GetLoanSummaryForHomeRequest) (*GetLoanSummaryForHomeResponse, error)
	// External rpc to estimate credit limit utilised through loan, including applications completed by user
	EstimateCreditUtilised(context.Context, *EstimateCreditUtilisedRequest) (*EstimateCreditUtilisedResponse, error)
	// rpc to persist the user's address in db
	AddAddressDetails(context.Context, *AddAddressDetailsRequest) (*AddAddressDetailsResponse, error)
	// rpc to persist the user's employment in db
	AddEmploymentDetails(context.Context, *AddEmploymentDetailsRequest) (*AddEmploymentDetailsResponse, error)
	// RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
	// ActorId is a mandatory parameter in the Request
	// Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
	// INVALID ARGUMENT if any mandatory param is missing
	// RECORD NOT FOUND if no elements found for the given user on this screen
	// INTERNAL SERVER ERROR if any error in processing
	// OK if the list of targeted comms elements is fetched successfully
	FetchDynamicElements(context.Context, *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error)
	// DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
	DynamicElementCallback(context.Context, *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error)
	// rpc to let customer confirm the revised loan details based on their occupation. This is called from the hustle screen and takes user to poll screen
	ConfirmRevisedLoanDetails(context.Context, *ConfirmRevisedLoanDetailsRequest) (*ConfirmRevisedLoanDetailsResponse, error)
	// this rpc is to acknowledge client callbacks
	// whenever clients have to send any callback to BE, they can use this RPC. Not necessarily only for success cases,
	// callbacks could just be that the SDK flow has started.
	ClientCallback(context.Context, *ClientCallbackRequest) (*ClientCallbackResponse, error)
	// rpc to make a vg call in idfc flow to verify the PAN details entered by the user
	VerifyDetails(context.Context, *VerifyDetailsRequest) (*VerifyDetailsResponse, error)
	// external rpc to get early salary details for salary client
	// returns multiple stats which could be possible and data needed to be shown for them
	GetEarlySalaryDetails(context.Context, *GetEarlySalaryDetailsRequest) (*GetEarlySalaryDetailsResponse, error)
	// rpc to record consent for ckyc verification from the user
	VerifyCkycDetails(context.Context, *VerifyCkycDetailsRequest) (*VerifyCkycDetailsResponse, error)
	// rpc to update loan schedule of a loan account
	RefreshLMSSchedule(context.Context, *RefreshLmsScheduleRequest) (*RefreshLmsScheduleResponse, error)
	// rpc to fetch web view from vendor
	GetMandateViewData(context.Context, *GetMandateViewDataRequest) (*GetMandateViewDataResponse, error)
	// rpc to execute collections for any flow
	ExecuteCollection(context.Context, *ExecuteCollectionRequest) (*ExecuteCollectionResponse, error)
	// rpc to reconcile loan txns from vendor
	ReconLoanActivity(context.Context, *ReconLoanActivityRequest) (*ReconLoanActivityResponse, error)
	// Returns loan schedule if present in our DB
	GetLoanSchedule(context.Context, *GetLoanScheduleRequest) (*GetLoanScheduleResponse, error)
	// rpc to persist the user entered PAN and DOB in Db
	AddPanAndDobData(context.Context, *AddPanAndDobDataRequest) (*AddPanAndDobDataResponse, error)
	// GetLoanInstallmentPayoutDetails RPC to fetch the loan installment payout details, installment info and
	// loan account details for a given installment id
	GetLoanInstallmentPayoutDetails(context.Context, *GetLoanInstallmentPayoutDetailsRequest) (*GetLoanInstallmentPayoutDetailsResponse, error)
	// rpc to persist the user entered Name and Gender
	AddNameAndGender(context.Context, *AddNameAndGenderRequest) (*AddNameAndGenderResponse, error)
	// rpc to onboard a user for loan journey and starts workflow for loan eligibility
	// creates loan applicant and loan request if not present already
	CheckLoanEligibility(context.Context, *CheckLoanEligibilityRequest) (*CheckLoanEligibilityResponse, error)
	// rpc to initiate the credit report fetch process for the user
	FetchCreditReport(context.Context, *FetchCreditReportRequest) (*FetchCreditReportResponse, error)
	// GetLoanDefaultDetails RPC provides an aggregated view of all the defaults associated with a loan account across payouts
	GetLoanDefaultDetails(context.Context, *GetLoanDefaultDetailsRequest) (*GetLoanDefaultDetailsResponse, error)
	// rpc to persist the user's banking details in db
	AddBankingDetails(context.Context, *AddBankingDetailsRequest) (*AddBankingDetailsResponse, error)
	// rpc to get review details for loan application/eligibility summary details screen
	GetLoanReviewDetails(context.Context, *GetLoanReviewDetailsRequest) (*GetLoanReviewDetailsResponse, error)
	// rpc to submit post user reviews the loan application/eligibility summary details
	SubmitReviewLoanDetails(context.Context, *SubmitReviewLoanDetailsRequest) (*SubmitReviewLoanDetailsResponse, error)
	// external rpc to fetch all data stored for an applicant during a loan journey. Journey could be loan eligibility or loan application.
	GetLoanUserDetails(context.Context, *GetLoanUserDetailsRequest) (*GetLoanUserDetailsResponse, error)
	// rpc to acquire LoanId using LoanHeader And ActorId can return errors if no loanId can be found for given LoanHeader and ActorId
	// or if there are multiple loanId for given LoanHeader And ActorId then log error and proceed with the latest loanId
	GetLoanIdByHeaderAndActorId(context.Context, *GetLoanIdByHeaderAndActorIdRequest) (*GetLoanIdByHeaderAndActorIdResponse, error)
	// GetDowntimeStatus RPC to check if the vendor is down for maintenance
	GetDowntimeStatus(context.Context, *GetDowntimeStatusRequest) (*GetDowntimeStatusResponse, error)
	// InitiateSiSetup RPC creates loan request and triggers SI workflow
	InitiateSiSetup(context.Context, *InitiateSiSetupRequest) (*InitiateSiSetupResponse, error)
	// RPC to get completed loan requests for an actor by check all the existing loan requests
	GetCompletedLoanRequests(context.Context, *GetCompletedLoanRequestsRequest) (*GetCompletedLoanRequestsResponse, error)
	// RPC to get active loan requests for an actor by check all the existing loan requests
	GetActiveLoanRequests(context.Context, *GetActiveLoanRequestsRequest) (*GetActiveLoanRequestsResponse, error)
	// GetVendorApplicantId fetches the applicant id at vendor's end for the given user based on the loan header
	GetVendorApplicantId(context.Context, *GetVendorApplicantIdRequest) (*GetVendorApplicantIdResponse, error)
	// rpc to let the backend know which income verification option is selected by the user
	SubmitIncomeVerificationOption(context.Context, *SubmitIncomeVerificationOptionRequest) (*SubmitIncomeVerificationOptionResponse, error)
	// rpc to fetch mandate view details like webview/sdk and necessary params for the same from backend
	GetMandateViewDataV2(context.Context, *GetMandateViewDataV2Request) (*GetMandateViewDataV2Response, error)
	// rpc to fetch pwa redirection details from vendor for redirecting the user to the pwa flow,
	// some flows are driven through a pwa journey on vendor side so this rpc is useful to fetch the pwa redirection details from the vendor to redirect the user to the pwa flow
	// e.g moneyview lender loan application and loan servicing flows are mostly driven on their pwa.
	GetPWARedirectionDetails(context.Context, *GetPWARedirectionDetailsRequest) (*GetPWARedirectionDetailsResponse, error)
	// RecordUserAction rpc can be used to record some client action in a loan flow and return some action based on the response.
	RecordUserAction(context.Context, *RecordUserActionRequest) (*RecordUserActionResponse, error)
	// ProcessOffAppLoanRepayment processes loan repayment that was done off app
	ProcessOffAppLoanRepayment(context.Context, *ProcessOffAppLoanRepaymentRequest) (*ProcessOffAppLoanRepaymentResponse, error)
	// GetLoansUserStatus gives the loans status of an actor, if any loan account or loan request is active for the user.
	// This is an external api that can be used by other services to get the status of an user at loans end
	GetLoansUserStatus(context.Context, *GetLoansUserStatusRequest) (*GetLoansUserStatusResponse, error)
	// GetIncomeVerificationInfo gives income verification info related to income verifications methods in pl flows,
	// for Income Verification through ITR, it will give max attempts reached or not
	GetIncomeVerificationInfo(context.Context, *GetIncomeVerificationInfoRequest) (*GetIncomeVerificationInfoResponse, error)
	// GetVkycDeeplink can be used to do the vkyc related executions in the flow.
	GetVkycDeeplink(context.Context, *GetVkycDeeplinkRequest) (*GetVkycDeeplinkResponse, error)
	// GetForeclosureDetails RPC simplifies fetching loan foreclosure details from backend
	// Currently we are only fetching foreclosure details from vendor
	// This RPC aims to eliminate complex wire dependencies currently present in the foreclosure details interface and
	// provide one common API which can be used across all the places to fetch the details
	GetForeclosureDetails(context.Context, *GetForeclosureDetailsRequest) (*GetForeclosureDetailsResponse, error)
	// InitiateLoanClosure RPC initiates loan closure workflow in the backend for given loan account id
	// RPC returns OK and loan request id to identify workflow
	// RPC returns ALREADY EXISTS status code if some closure workflow already exists in successful state
	InitiateLoanClosure(context.Context, *InitiateLoanClosureRequest) (*InitiateLoanClosureResponse, error)
	// CheckLmsDataDifference RPC is used to find differences in lms details for a given loan account id
	// between two lms data sources.
	CheckLmsDataDifference(context.Context, *CheckLmsDataDifferenceRequest) (*CheckLmsDataDifferenceResponse, error)
	// This rpc generates esign agreement url stored in aws  if previous url has expired or does not exist.
	GenerateEsignAgreement(context.Context, *GenerateEsignAgreementRequest) (*GenerateEsignAgreementResponse, error)
	// rpc to fetch pre-pay details like emi, charges, overdue, foreclosue amount etc
	GetPrePayDetails(context.Context, *GetPrePayDetailsRequest) (*GetPrePayDetailsResponse, error)
	AddEmploymentDetailsSync(context.Context, *AddEmploymentDetailsRequest) (*AddEmploymentDetailsResponse, error)
	// rpc to save details for alternate contact information
	SaveContactDetails(context.Context, *SaveContactDetailsRequest) (*SaveContactDetailsResponse, error)
	// rpc to initiate auto pay execution for a given loan account id, it is responsible for deciding the auto-pay execution amount as well
	// returns FailedPrecondition if the loan account does not have any due/overdue amount to be collected
	InitiateAutoPayExecution(context.Context, *InitiateAutoPayExecutionRequest) (*InitiateAutoPayExecutionResponse, error)
	// rpc to add address details in sync fashion
	AddAddressDetailsSync(context.Context, *AddAddressDetailsRequest) (*AddAddressDetailsResponse, error)
	// RPC to get active loan requests for an actor by check all the existing loan requests
	GetActiveLoanPaymentRequests(context.Context, *GetActiveLoanPaymentRequestsRequest) (*GetActiveLoanPaymentRequestsResponse, error)
	// ModifyLoanTerms will modify the loan terms (loan amount and tenure) for an active loan application
	// this is required when the offer is changed during the loan application and user needs to select the amount and tenure again after checking the revised offer
	ModifyLoanTerms(context.Context, *ModifyLoanTermsRequest) (*ModifyLoanTermsResponse, error)
	GetAAConsentCollectionDetails(context.Context, *GetAAConsentCollectionDetailsRequest) (*GetAAConsentCollectionDetailsResponse, error)
	// TODO: Temporary rpc to unblock nbfc testing
	GetApplicantByVendorApplicantId(context.Context, *GetApplicantByVendorApplicantIdRequest) (*GetApplicantByVendorApplicantIdResponse, error)
	// To collect and save data like user selected loan amount, consents etc
	CollectData(context.Context, *CollectDataRequest) (*CollectDataResponse, error)
	// To get Customer details like BRE Reference Number for Federal Loan
	GetFederalLoanCustomerDetails(context.Context, *GetFederalLoanCustomerDetailsRequest) (*GetFederalLoanCustomerDetailsResponse, error)
	// GetRedirectDL: RPC that determines the next screen to show based on loan header, actor ID and client request ID.
	// Used by service providers to get appropriate deeplink for redirecting users to the next step in loan flow.
	// Returns a deeplink response that contains the next screen to display.
	GetRedirectDL(context.Context, *GetRedirectDLRequest) (*GetRedirectDLResponse, error)
	// To collect various types of form data from the user
	CollectFormData(context.Context, *CollectFormDataRequest) (*CollectFormDataResponse, error)
	// To get the current status of the loan user and what user can do next
	GetLoanUserStatusV2(context.Context, *GetLoanUserStatusV2Request) (*GetLoanUserStatusV2Response, error)
	// To check if prepay is allowed for a given loan account id
	IsPrepayAllowed(context.Context, *IsPrepayAllowedRequest) (*IsPrepayAllowedResponse, error)
}

// UnimplementedPreApprovedLoanServer should be embedded to have forward compatible implementations.
type UnimplementedPreApprovedLoanServer struct {
}

func (UnimplementedPreApprovedLoanServer) GetLandingInfo(context.Context, *GetLandingInfoRequest) (*GetLandingInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLandingInfo not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLandingInfoV2(context.Context, *GetLandingInfoV2Request) (*GetLandingInfoV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLandingInfoV2 not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLandingInfoV3(context.Context, *GetLandingInfoV3Request) (*GetLandingInfoV3Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLandingInfoV3 not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetOfferDetails(context.Context, *GetOfferDetailsRequest) (*GetOfferDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOfferDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) GenerateConfirmationCode(context.Context, *GenerateConfirmationCodeRequest) (*GenerateConfirmationCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateConfirmationCode not implemented")
}
func (UnimplementedPreApprovedLoanServer) ApplyForLoan(context.Context, *ApplyForLoanRequest) (*ApplyForLoanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyForLoan not implemented")
}
func (UnimplementedPreApprovedLoanServer) ConfirmApplication(context.Context, *ConfirmApplicationRequest) (*ConfirmApplicationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmApplication not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetApplicationStatus(context.Context, *GetApplicationStatusRequest) (*GetApplicationStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApplicationStatus not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetApplicationStatusSync(context.Context, *GetApplicationStatusSyncRequest) (*GetApplicationStatusSyncResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApplicationStatusSync not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLivenessStatus(context.Context, *GetLivenessStatusRequest) (*GetLivenessStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLivenessStatus not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetDashboard(context.Context, *GetDashboardRequest) (*GetDashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDashboard not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetDashboardV2(context.Context, *GetDashboardRequest) (*GetDashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDashboardV2 not implemented")
}
func (UnimplementedPreApprovedLoanServer) CancelApplication(context.Context, *CancelApplicationRequest) (*CancelApplicationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelApplication not implemented")
}
func (UnimplementedPreApprovedLoanServer) InitiateESign(context.Context, *InitiateESignRequest) (*InitiateESignResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateESign not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoanDetails(context.Context, *GetLoanDetailsRequest) (*GetLoanDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) PrePayLoan(context.Context, *PrePayLoanRequest) (*PrePayLoanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PrePayLoan not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoanActivityStatus(context.Context, *GetLoanActivityStatusRequest) (*GetLoanActivityStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanActivityStatus not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetAllTransactions(context.Context, *GetAllTransactionsRequest) (*GetAllTransactionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllTransactions not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetTransactionReceipt(context.Context, *GetTransactionReceiptRequest) (*GetTransactionReceiptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionReceipt not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoanAccountDetails(context.Context, *GetLoanAccountDetailsRequest) (*GetLoanAccountDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanAccountDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoanOffers(context.Context, *GetLoanOffersRequest) (*GetLoanOffersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanOffers not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetRecentLoanApplicationDetails(context.Context, *GetRecentLoanApplicationDetailsRequest) (*GetRecentLoanApplicationDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecentLoanApplicationDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoanTransactions(context.Context, *GetLoanTransactionsRequest) (*GetLoanTransactionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanTransactions not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoanSummaryForHome(context.Context, *GetLoanSummaryForHomeRequest) (*GetLoanSummaryForHomeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanSummaryForHome not implemented")
}
func (UnimplementedPreApprovedLoanServer) EstimateCreditUtilised(context.Context, *EstimateCreditUtilisedRequest) (*EstimateCreditUtilisedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EstimateCreditUtilised not implemented")
}
func (UnimplementedPreApprovedLoanServer) AddAddressDetails(context.Context, *AddAddressDetailsRequest) (*AddAddressDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAddressDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) AddEmploymentDetails(context.Context, *AddEmploymentDetailsRequest) (*AddEmploymentDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddEmploymentDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) FetchDynamicElements(context.Context, *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchDynamicElements not implemented")
}
func (UnimplementedPreApprovedLoanServer) DynamicElementCallback(context.Context, *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DynamicElementCallback not implemented")
}
func (UnimplementedPreApprovedLoanServer) ConfirmRevisedLoanDetails(context.Context, *ConfirmRevisedLoanDetailsRequest) (*ConfirmRevisedLoanDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmRevisedLoanDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) ClientCallback(context.Context, *ClientCallbackRequest) (*ClientCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClientCallback not implemented")
}
func (UnimplementedPreApprovedLoanServer) VerifyDetails(context.Context, *VerifyDetailsRequest) (*VerifyDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetEarlySalaryDetails(context.Context, *GetEarlySalaryDetailsRequest) (*GetEarlySalaryDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEarlySalaryDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) VerifyCkycDetails(context.Context, *VerifyCkycDetailsRequest) (*VerifyCkycDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyCkycDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) RefreshLMSSchedule(context.Context, *RefreshLmsScheduleRequest) (*RefreshLmsScheduleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshLMSSchedule not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetMandateViewData(context.Context, *GetMandateViewDataRequest) (*GetMandateViewDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMandateViewData not implemented")
}
func (UnimplementedPreApprovedLoanServer) ExecuteCollection(context.Context, *ExecuteCollectionRequest) (*ExecuteCollectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteCollection not implemented")
}
func (UnimplementedPreApprovedLoanServer) ReconLoanActivity(context.Context, *ReconLoanActivityRequest) (*ReconLoanActivityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReconLoanActivity not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoanSchedule(context.Context, *GetLoanScheduleRequest) (*GetLoanScheduleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanSchedule not implemented")
}
func (UnimplementedPreApprovedLoanServer) AddPanAndDobData(context.Context, *AddPanAndDobDataRequest) (*AddPanAndDobDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPanAndDobData not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoanInstallmentPayoutDetails(context.Context, *GetLoanInstallmentPayoutDetailsRequest) (*GetLoanInstallmentPayoutDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanInstallmentPayoutDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) AddNameAndGender(context.Context, *AddNameAndGenderRequest) (*AddNameAndGenderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddNameAndGender not implemented")
}
func (UnimplementedPreApprovedLoanServer) CheckLoanEligibility(context.Context, *CheckLoanEligibilityRequest) (*CheckLoanEligibilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckLoanEligibility not implemented")
}
func (UnimplementedPreApprovedLoanServer) FetchCreditReport(context.Context, *FetchCreditReportRequest) (*FetchCreditReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchCreditReport not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoanDefaultDetails(context.Context, *GetLoanDefaultDetailsRequest) (*GetLoanDefaultDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanDefaultDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) AddBankingDetails(context.Context, *AddBankingDetailsRequest) (*AddBankingDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBankingDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoanReviewDetails(context.Context, *GetLoanReviewDetailsRequest) (*GetLoanReviewDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanReviewDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) SubmitReviewLoanDetails(context.Context, *SubmitReviewLoanDetailsRequest) (*SubmitReviewLoanDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitReviewLoanDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoanUserDetails(context.Context, *GetLoanUserDetailsRequest) (*GetLoanUserDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanUserDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoanIdByHeaderAndActorId(context.Context, *GetLoanIdByHeaderAndActorIdRequest) (*GetLoanIdByHeaderAndActorIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanIdByHeaderAndActorId not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetDowntimeStatus(context.Context, *GetDowntimeStatusRequest) (*GetDowntimeStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDowntimeStatus not implemented")
}
func (UnimplementedPreApprovedLoanServer) InitiateSiSetup(context.Context, *InitiateSiSetupRequest) (*InitiateSiSetupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateSiSetup not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetCompletedLoanRequests(context.Context, *GetCompletedLoanRequestsRequest) (*GetCompletedLoanRequestsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompletedLoanRequests not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetActiveLoanRequests(context.Context, *GetActiveLoanRequestsRequest) (*GetActiveLoanRequestsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveLoanRequests not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetVendorApplicantId(context.Context, *GetVendorApplicantIdRequest) (*GetVendorApplicantIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVendorApplicantId not implemented")
}
func (UnimplementedPreApprovedLoanServer) SubmitIncomeVerificationOption(context.Context, *SubmitIncomeVerificationOptionRequest) (*SubmitIncomeVerificationOptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitIncomeVerificationOption not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetMandateViewDataV2(context.Context, *GetMandateViewDataV2Request) (*GetMandateViewDataV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMandateViewDataV2 not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetPWARedirectionDetails(context.Context, *GetPWARedirectionDetailsRequest) (*GetPWARedirectionDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPWARedirectionDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) RecordUserAction(context.Context, *RecordUserActionRequest) (*RecordUserActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordUserAction not implemented")
}
func (UnimplementedPreApprovedLoanServer) ProcessOffAppLoanRepayment(context.Context, *ProcessOffAppLoanRepaymentRequest) (*ProcessOffAppLoanRepaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessOffAppLoanRepayment not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoansUserStatus(context.Context, *GetLoansUserStatusRequest) (*GetLoansUserStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoansUserStatus not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetIncomeVerificationInfo(context.Context, *GetIncomeVerificationInfoRequest) (*GetIncomeVerificationInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIncomeVerificationInfo not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetVkycDeeplink(context.Context, *GetVkycDeeplinkRequest) (*GetVkycDeeplinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVkycDeeplink not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetForeclosureDetails(context.Context, *GetForeclosureDetailsRequest) (*GetForeclosureDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetForeclosureDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) InitiateLoanClosure(context.Context, *InitiateLoanClosureRequest) (*InitiateLoanClosureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateLoanClosure not implemented")
}
func (UnimplementedPreApprovedLoanServer) CheckLmsDataDifference(context.Context, *CheckLmsDataDifferenceRequest) (*CheckLmsDataDifferenceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckLmsDataDifference not implemented")
}
func (UnimplementedPreApprovedLoanServer) GenerateEsignAgreement(context.Context, *GenerateEsignAgreementRequest) (*GenerateEsignAgreementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateEsignAgreement not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetPrePayDetails(context.Context, *GetPrePayDetailsRequest) (*GetPrePayDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPrePayDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) AddEmploymentDetailsSync(context.Context, *AddEmploymentDetailsRequest) (*AddEmploymentDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddEmploymentDetailsSync not implemented")
}
func (UnimplementedPreApprovedLoanServer) SaveContactDetails(context.Context, *SaveContactDetailsRequest) (*SaveContactDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveContactDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) InitiateAutoPayExecution(context.Context, *InitiateAutoPayExecutionRequest) (*InitiateAutoPayExecutionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateAutoPayExecution not implemented")
}
func (UnimplementedPreApprovedLoanServer) AddAddressDetailsSync(context.Context, *AddAddressDetailsRequest) (*AddAddressDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAddressDetailsSync not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetActiveLoanPaymentRequests(context.Context, *GetActiveLoanPaymentRequestsRequest) (*GetActiveLoanPaymentRequestsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveLoanPaymentRequests not implemented")
}
func (UnimplementedPreApprovedLoanServer) ModifyLoanTerms(context.Context, *ModifyLoanTermsRequest) (*ModifyLoanTermsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyLoanTerms not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetAAConsentCollectionDetails(context.Context, *GetAAConsentCollectionDetailsRequest) (*GetAAConsentCollectionDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAAConsentCollectionDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetApplicantByVendorApplicantId(context.Context, *GetApplicantByVendorApplicantIdRequest) (*GetApplicantByVendorApplicantIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApplicantByVendorApplicantId not implemented")
}
func (UnimplementedPreApprovedLoanServer) CollectData(context.Context, *CollectDataRequest) (*CollectDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CollectData not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetFederalLoanCustomerDetails(context.Context, *GetFederalLoanCustomerDetailsRequest) (*GetFederalLoanCustomerDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFederalLoanCustomerDetails not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetRedirectDL(context.Context, *GetRedirectDLRequest) (*GetRedirectDLResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRedirectDL not implemented")
}
func (UnimplementedPreApprovedLoanServer) CollectFormData(context.Context, *CollectFormDataRequest) (*CollectFormDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CollectFormData not implemented")
}
func (UnimplementedPreApprovedLoanServer) GetLoanUserStatusV2(context.Context, *GetLoanUserStatusV2Request) (*GetLoanUserStatusV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanUserStatusV2 not implemented")
}
func (UnimplementedPreApprovedLoanServer) IsPrepayAllowed(context.Context, *IsPrepayAllowedRequest) (*IsPrepayAllowedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsPrepayAllowed not implemented")
}

// UnsafePreApprovedLoanServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PreApprovedLoanServer will
// result in compilation errors.
type UnsafePreApprovedLoanServer interface {
	mustEmbedUnimplementedPreApprovedLoanServer()
}

func RegisterPreApprovedLoanServer(s grpc.ServiceRegistrar, srv PreApprovedLoanServer) {
	s.RegisterService(&PreApprovedLoan_ServiceDesc, srv)
}

func _PreApprovedLoan_GetLandingInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLandingInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLandingInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLandingInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLandingInfo(ctx, req.(*GetLandingInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLandingInfoV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLandingInfoV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLandingInfoV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLandingInfoV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLandingInfoV2(ctx, req.(*GetLandingInfoV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLandingInfoV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLandingInfoV3Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLandingInfoV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLandingInfoV3_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLandingInfoV3(ctx, req.(*GetLandingInfoV3Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetOfferDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOfferDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetOfferDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetOfferDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetOfferDetails(ctx, req.(*GetOfferDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GenerateConfirmationCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateConfirmationCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GenerateConfirmationCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GenerateConfirmationCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GenerateConfirmationCode(ctx, req.(*GenerateConfirmationCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_ApplyForLoan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyForLoanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).ApplyForLoan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_ApplyForLoan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).ApplyForLoan(ctx, req.(*ApplyForLoanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_ConfirmApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmApplicationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).ConfirmApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_ConfirmApplication_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).ConfirmApplication(ctx, req.(*ConfirmApplicationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetApplicationStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicationStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetApplicationStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetApplicationStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetApplicationStatus(ctx, req.(*GetApplicationStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetApplicationStatusSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicationStatusSyncRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetApplicationStatusSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetApplicationStatusSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetApplicationStatusSync(ctx, req.(*GetApplicationStatusSyncRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLivenessStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLivenessStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLivenessStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLivenessStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLivenessStatus(ctx, req.(*GetLivenessStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetDashboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetDashboard(ctx, req.(*GetDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetDashboardV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetDashboardV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetDashboardV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetDashboardV2(ctx, req.(*GetDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_CancelApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelApplicationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).CancelApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_CancelApplication_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).CancelApplication(ctx, req.(*CancelApplicationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_InitiateESign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateESignRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).InitiateESign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_InitiateESign_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).InitiateESign(ctx, req.(*InitiateESignRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoanDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoanDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoanDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoanDetails(ctx, req.(*GetLoanDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_PrePayLoan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PrePayLoanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).PrePayLoan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_PrePayLoan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).PrePayLoan(ctx, req.(*PrePayLoanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoanActivityStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanActivityStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoanActivityStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoanActivityStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoanActivityStatus(ctx, req.(*GetLoanActivityStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetAllTransactions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllTransactionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetAllTransactions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetAllTransactions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetAllTransactions(ctx, req.(*GetAllTransactionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetTransactionReceipt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransactionReceiptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetTransactionReceipt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetTransactionReceipt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetTransactionReceipt(ctx, req.(*GetTransactionReceiptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoanAccountDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanAccountDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoanAccountDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoanAccountDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoanAccountDetails(ctx, req.(*GetLoanAccountDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoanOffers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanOffersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoanOffers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoanOffers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoanOffers(ctx, req.(*GetLoanOffersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetRecentLoanApplicationDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecentLoanApplicationDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetRecentLoanApplicationDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetRecentLoanApplicationDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetRecentLoanApplicationDetails(ctx, req.(*GetRecentLoanApplicationDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoanTransactions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanTransactionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoanTransactions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoanTransactions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoanTransactions(ctx, req.(*GetLoanTransactionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoanSummaryForHome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanSummaryForHomeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoanSummaryForHome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoanSummaryForHome_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoanSummaryForHome(ctx, req.(*GetLoanSummaryForHomeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_EstimateCreditUtilised_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EstimateCreditUtilisedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).EstimateCreditUtilised(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_EstimateCreditUtilised_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).EstimateCreditUtilised(ctx, req.(*EstimateCreditUtilisedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_AddAddressDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAddressDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).AddAddressDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_AddAddressDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).AddAddressDetails(ctx, req.(*AddAddressDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_AddEmploymentDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddEmploymentDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).AddEmploymentDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_AddEmploymentDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).AddEmploymentDetails(ctx, req.(*AddEmploymentDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_FetchDynamicElements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dynamic_elements.FetchDynamicElementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).FetchDynamicElements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_FetchDynamicElements_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).FetchDynamicElements(ctx, req.(*dynamic_elements.FetchDynamicElementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_DynamicElementCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dynamic_elements.DynamicElementCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).DynamicElementCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_DynamicElementCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).DynamicElementCallback(ctx, req.(*dynamic_elements.DynamicElementCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_ConfirmRevisedLoanDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmRevisedLoanDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).ConfirmRevisedLoanDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_ConfirmRevisedLoanDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).ConfirmRevisedLoanDetails(ctx, req.(*ConfirmRevisedLoanDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_ClientCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).ClientCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_ClientCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).ClientCallback(ctx, req.(*ClientCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_VerifyDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).VerifyDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_VerifyDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).VerifyDetails(ctx, req.(*VerifyDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetEarlySalaryDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEarlySalaryDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetEarlySalaryDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetEarlySalaryDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetEarlySalaryDetails(ctx, req.(*GetEarlySalaryDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_VerifyCkycDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyCkycDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).VerifyCkycDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_VerifyCkycDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).VerifyCkycDetails(ctx, req.(*VerifyCkycDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_RefreshLMSSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshLmsScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).RefreshLMSSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_RefreshLMSSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).RefreshLMSSchedule(ctx, req.(*RefreshLmsScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetMandateViewData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMandateViewDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetMandateViewData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetMandateViewData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetMandateViewData(ctx, req.(*GetMandateViewDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_ExecuteCollection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExecuteCollectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).ExecuteCollection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_ExecuteCollection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).ExecuteCollection(ctx, req.(*ExecuteCollectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_ReconLoanActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReconLoanActivityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).ReconLoanActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_ReconLoanActivity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).ReconLoanActivity(ctx, req.(*ReconLoanActivityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoanSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoanSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoanSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoanSchedule(ctx, req.(*GetLoanScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_AddPanAndDobData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPanAndDobDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).AddPanAndDobData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_AddPanAndDobData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).AddPanAndDobData(ctx, req.(*AddPanAndDobDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoanInstallmentPayoutDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanInstallmentPayoutDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoanInstallmentPayoutDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoanInstallmentPayoutDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoanInstallmentPayoutDetails(ctx, req.(*GetLoanInstallmentPayoutDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_AddNameAndGender_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddNameAndGenderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).AddNameAndGender(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_AddNameAndGender_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).AddNameAndGender(ctx, req.(*AddNameAndGenderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_CheckLoanEligibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckLoanEligibilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).CheckLoanEligibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_CheckLoanEligibility_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).CheckLoanEligibility(ctx, req.(*CheckLoanEligibilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_FetchCreditReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchCreditReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).FetchCreditReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_FetchCreditReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).FetchCreditReport(ctx, req.(*FetchCreditReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoanDefaultDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanDefaultDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoanDefaultDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoanDefaultDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoanDefaultDetails(ctx, req.(*GetLoanDefaultDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_AddBankingDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBankingDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).AddBankingDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_AddBankingDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).AddBankingDetails(ctx, req.(*AddBankingDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoanReviewDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanReviewDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoanReviewDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoanReviewDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoanReviewDetails(ctx, req.(*GetLoanReviewDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_SubmitReviewLoanDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitReviewLoanDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).SubmitReviewLoanDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_SubmitReviewLoanDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).SubmitReviewLoanDetails(ctx, req.(*SubmitReviewLoanDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoanUserDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanUserDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoanUserDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoanUserDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoanUserDetails(ctx, req.(*GetLoanUserDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoanIdByHeaderAndActorId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanIdByHeaderAndActorIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoanIdByHeaderAndActorId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoanIdByHeaderAndActorId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoanIdByHeaderAndActorId(ctx, req.(*GetLoanIdByHeaderAndActorIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetDowntimeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDowntimeStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetDowntimeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetDowntimeStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetDowntimeStatus(ctx, req.(*GetDowntimeStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_InitiateSiSetup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateSiSetupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).InitiateSiSetup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_InitiateSiSetup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).InitiateSiSetup(ctx, req.(*InitiateSiSetupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetCompletedLoanRequests_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompletedLoanRequestsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetCompletedLoanRequests(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetCompletedLoanRequests_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetCompletedLoanRequests(ctx, req.(*GetCompletedLoanRequestsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetActiveLoanRequests_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveLoanRequestsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetActiveLoanRequests(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetActiveLoanRequests_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetActiveLoanRequests(ctx, req.(*GetActiveLoanRequestsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetVendorApplicantId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVendorApplicantIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetVendorApplicantId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetVendorApplicantId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetVendorApplicantId(ctx, req.(*GetVendorApplicantIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_SubmitIncomeVerificationOption_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitIncomeVerificationOptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).SubmitIncomeVerificationOption(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_SubmitIncomeVerificationOption_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).SubmitIncomeVerificationOption(ctx, req.(*SubmitIncomeVerificationOptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetMandateViewDataV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMandateViewDataV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetMandateViewDataV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetMandateViewDataV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetMandateViewDataV2(ctx, req.(*GetMandateViewDataV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetPWARedirectionDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPWARedirectionDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetPWARedirectionDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetPWARedirectionDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetPWARedirectionDetails(ctx, req.(*GetPWARedirectionDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_RecordUserAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordUserActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).RecordUserAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_RecordUserAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).RecordUserAction(ctx, req.(*RecordUserActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_ProcessOffAppLoanRepayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessOffAppLoanRepaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).ProcessOffAppLoanRepayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_ProcessOffAppLoanRepayment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).ProcessOffAppLoanRepayment(ctx, req.(*ProcessOffAppLoanRepaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoansUserStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoansUserStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoansUserStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoansUserStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoansUserStatus(ctx, req.(*GetLoansUserStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetIncomeVerificationInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIncomeVerificationInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetIncomeVerificationInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetIncomeVerificationInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetIncomeVerificationInfo(ctx, req.(*GetIncomeVerificationInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetVkycDeeplink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVkycDeeplinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetVkycDeeplink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetVkycDeeplink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetVkycDeeplink(ctx, req.(*GetVkycDeeplinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetForeclosureDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetForeclosureDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetForeclosureDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetForeclosureDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetForeclosureDetails(ctx, req.(*GetForeclosureDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_InitiateLoanClosure_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateLoanClosureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).InitiateLoanClosure(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_InitiateLoanClosure_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).InitiateLoanClosure(ctx, req.(*InitiateLoanClosureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_CheckLmsDataDifference_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckLmsDataDifferenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).CheckLmsDataDifference(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_CheckLmsDataDifference_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).CheckLmsDataDifference(ctx, req.(*CheckLmsDataDifferenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GenerateEsignAgreement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateEsignAgreementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GenerateEsignAgreement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GenerateEsignAgreement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GenerateEsignAgreement(ctx, req.(*GenerateEsignAgreementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetPrePayDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrePayDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetPrePayDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetPrePayDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetPrePayDetails(ctx, req.(*GetPrePayDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_AddEmploymentDetailsSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddEmploymentDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).AddEmploymentDetailsSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_AddEmploymentDetailsSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).AddEmploymentDetailsSync(ctx, req.(*AddEmploymentDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_SaveContactDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveContactDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).SaveContactDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_SaveContactDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).SaveContactDetails(ctx, req.(*SaveContactDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_InitiateAutoPayExecution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateAutoPayExecutionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).InitiateAutoPayExecution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_InitiateAutoPayExecution_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).InitiateAutoPayExecution(ctx, req.(*InitiateAutoPayExecutionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_AddAddressDetailsSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAddressDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).AddAddressDetailsSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_AddAddressDetailsSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).AddAddressDetailsSync(ctx, req.(*AddAddressDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetActiveLoanPaymentRequests_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveLoanPaymentRequestsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetActiveLoanPaymentRequests(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetActiveLoanPaymentRequests_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetActiveLoanPaymentRequests(ctx, req.(*GetActiveLoanPaymentRequestsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_ModifyLoanTerms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyLoanTermsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).ModifyLoanTerms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_ModifyLoanTerms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).ModifyLoanTerms(ctx, req.(*ModifyLoanTermsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetAAConsentCollectionDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAAConsentCollectionDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetAAConsentCollectionDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetAAConsentCollectionDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetAAConsentCollectionDetails(ctx, req.(*GetAAConsentCollectionDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetApplicantByVendorApplicantId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicantByVendorApplicantIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetApplicantByVendorApplicantId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetApplicantByVendorApplicantId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetApplicantByVendorApplicantId(ctx, req.(*GetApplicantByVendorApplicantIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_CollectData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CollectDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).CollectData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_CollectData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).CollectData(ctx, req.(*CollectDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetFederalLoanCustomerDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFederalLoanCustomerDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetFederalLoanCustomerDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetFederalLoanCustomerDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetFederalLoanCustomerDetails(ctx, req.(*GetFederalLoanCustomerDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetRedirectDL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRedirectDLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetRedirectDL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetRedirectDL_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetRedirectDL(ctx, req.(*GetRedirectDLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_CollectFormData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CollectFormDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).CollectFormData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_CollectFormData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).CollectFormData(ctx, req.(*CollectFormDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_GetLoanUserStatusV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanUserStatusV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).GetLoanUserStatusV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_GetLoanUserStatusV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).GetLoanUserStatusV2(ctx, req.(*GetLoanUserStatusV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreApprovedLoan_IsPrepayAllowed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsPrepayAllowedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreApprovedLoanServer).IsPrepayAllowed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreApprovedLoan_IsPrepayAllowed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreApprovedLoanServer).IsPrepayAllowed(ctx, req.(*IsPrepayAllowedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PreApprovedLoan_ServiceDesc is the grpc.ServiceDesc for PreApprovedLoan service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PreApprovedLoan_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "preapprovedloan.PreApprovedLoan",
	HandlerType: (*PreApprovedLoanServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLandingInfo",
			Handler:    _PreApprovedLoan_GetLandingInfo_Handler,
		},
		{
			MethodName: "GetLandingInfoV2",
			Handler:    _PreApprovedLoan_GetLandingInfoV2_Handler,
		},
		{
			MethodName: "GetLandingInfoV3",
			Handler:    _PreApprovedLoan_GetLandingInfoV3_Handler,
		},
		{
			MethodName: "GetOfferDetails",
			Handler:    _PreApprovedLoan_GetOfferDetails_Handler,
		},
		{
			MethodName: "GenerateConfirmationCode",
			Handler:    _PreApprovedLoan_GenerateConfirmationCode_Handler,
		},
		{
			MethodName: "ApplyForLoan",
			Handler:    _PreApprovedLoan_ApplyForLoan_Handler,
		},
		{
			MethodName: "ConfirmApplication",
			Handler:    _PreApprovedLoan_ConfirmApplication_Handler,
		},
		{
			MethodName: "GetApplicationStatus",
			Handler:    _PreApprovedLoan_GetApplicationStatus_Handler,
		},
		{
			MethodName: "GetApplicationStatusSync",
			Handler:    _PreApprovedLoan_GetApplicationStatusSync_Handler,
		},
		{
			MethodName: "GetLivenessStatus",
			Handler:    _PreApprovedLoan_GetLivenessStatus_Handler,
		},
		{
			MethodName: "GetDashboard",
			Handler:    _PreApprovedLoan_GetDashboard_Handler,
		},
		{
			MethodName: "GetDashboardV2",
			Handler:    _PreApprovedLoan_GetDashboardV2_Handler,
		},
		{
			MethodName: "CancelApplication",
			Handler:    _PreApprovedLoan_CancelApplication_Handler,
		},
		{
			MethodName: "InitiateESign",
			Handler:    _PreApprovedLoan_InitiateESign_Handler,
		},
		{
			MethodName: "GetLoanDetails",
			Handler:    _PreApprovedLoan_GetLoanDetails_Handler,
		},
		{
			MethodName: "PrePayLoan",
			Handler:    _PreApprovedLoan_PrePayLoan_Handler,
		},
		{
			MethodName: "GetLoanActivityStatus",
			Handler:    _PreApprovedLoan_GetLoanActivityStatus_Handler,
		},
		{
			MethodName: "GetAllTransactions",
			Handler:    _PreApprovedLoan_GetAllTransactions_Handler,
		},
		{
			MethodName: "GetTransactionReceipt",
			Handler:    _PreApprovedLoan_GetTransactionReceipt_Handler,
		},
		{
			MethodName: "GetLoanAccountDetails",
			Handler:    _PreApprovedLoan_GetLoanAccountDetails_Handler,
		},
		{
			MethodName: "GetLoanOffers",
			Handler:    _PreApprovedLoan_GetLoanOffers_Handler,
		},
		{
			MethodName: "GetRecentLoanApplicationDetails",
			Handler:    _PreApprovedLoan_GetRecentLoanApplicationDetails_Handler,
		},
		{
			MethodName: "GetLoanTransactions",
			Handler:    _PreApprovedLoan_GetLoanTransactions_Handler,
		},
		{
			MethodName: "GetLoanSummaryForHome",
			Handler:    _PreApprovedLoan_GetLoanSummaryForHome_Handler,
		},
		{
			MethodName: "EstimateCreditUtilised",
			Handler:    _PreApprovedLoan_EstimateCreditUtilised_Handler,
		},
		{
			MethodName: "AddAddressDetails",
			Handler:    _PreApprovedLoan_AddAddressDetails_Handler,
		},
		{
			MethodName: "AddEmploymentDetails",
			Handler:    _PreApprovedLoan_AddEmploymentDetails_Handler,
		},
		{
			MethodName: "FetchDynamicElements",
			Handler:    _PreApprovedLoan_FetchDynamicElements_Handler,
		},
		{
			MethodName: "DynamicElementCallback",
			Handler:    _PreApprovedLoan_DynamicElementCallback_Handler,
		},
		{
			MethodName: "ConfirmRevisedLoanDetails",
			Handler:    _PreApprovedLoan_ConfirmRevisedLoanDetails_Handler,
		},
		{
			MethodName: "ClientCallback",
			Handler:    _PreApprovedLoan_ClientCallback_Handler,
		},
		{
			MethodName: "VerifyDetails",
			Handler:    _PreApprovedLoan_VerifyDetails_Handler,
		},
		{
			MethodName: "GetEarlySalaryDetails",
			Handler:    _PreApprovedLoan_GetEarlySalaryDetails_Handler,
		},
		{
			MethodName: "VerifyCkycDetails",
			Handler:    _PreApprovedLoan_VerifyCkycDetails_Handler,
		},
		{
			MethodName: "RefreshLMSSchedule",
			Handler:    _PreApprovedLoan_RefreshLMSSchedule_Handler,
		},
		{
			MethodName: "GetMandateViewData",
			Handler:    _PreApprovedLoan_GetMandateViewData_Handler,
		},
		{
			MethodName: "ExecuteCollection",
			Handler:    _PreApprovedLoan_ExecuteCollection_Handler,
		},
		{
			MethodName: "ReconLoanActivity",
			Handler:    _PreApprovedLoan_ReconLoanActivity_Handler,
		},
		{
			MethodName: "GetLoanSchedule",
			Handler:    _PreApprovedLoan_GetLoanSchedule_Handler,
		},
		{
			MethodName: "AddPanAndDobData",
			Handler:    _PreApprovedLoan_AddPanAndDobData_Handler,
		},
		{
			MethodName: "GetLoanInstallmentPayoutDetails",
			Handler:    _PreApprovedLoan_GetLoanInstallmentPayoutDetails_Handler,
		},
		{
			MethodName: "AddNameAndGender",
			Handler:    _PreApprovedLoan_AddNameAndGender_Handler,
		},
		{
			MethodName: "CheckLoanEligibility",
			Handler:    _PreApprovedLoan_CheckLoanEligibility_Handler,
		},
		{
			MethodName: "FetchCreditReport",
			Handler:    _PreApprovedLoan_FetchCreditReport_Handler,
		},
		{
			MethodName: "GetLoanDefaultDetails",
			Handler:    _PreApprovedLoan_GetLoanDefaultDetails_Handler,
		},
		{
			MethodName: "AddBankingDetails",
			Handler:    _PreApprovedLoan_AddBankingDetails_Handler,
		},
		{
			MethodName: "GetLoanReviewDetails",
			Handler:    _PreApprovedLoan_GetLoanReviewDetails_Handler,
		},
		{
			MethodName: "SubmitReviewLoanDetails",
			Handler:    _PreApprovedLoan_SubmitReviewLoanDetails_Handler,
		},
		{
			MethodName: "GetLoanUserDetails",
			Handler:    _PreApprovedLoan_GetLoanUserDetails_Handler,
		},
		{
			MethodName: "GetLoanIdByHeaderAndActorId",
			Handler:    _PreApprovedLoan_GetLoanIdByHeaderAndActorId_Handler,
		},
		{
			MethodName: "GetDowntimeStatus",
			Handler:    _PreApprovedLoan_GetDowntimeStatus_Handler,
		},
		{
			MethodName: "InitiateSiSetup",
			Handler:    _PreApprovedLoan_InitiateSiSetup_Handler,
		},
		{
			MethodName: "GetCompletedLoanRequests",
			Handler:    _PreApprovedLoan_GetCompletedLoanRequests_Handler,
		},
		{
			MethodName: "GetActiveLoanRequests",
			Handler:    _PreApprovedLoan_GetActiveLoanRequests_Handler,
		},
		{
			MethodName: "GetVendorApplicantId",
			Handler:    _PreApprovedLoan_GetVendorApplicantId_Handler,
		},
		{
			MethodName: "SubmitIncomeVerificationOption",
			Handler:    _PreApprovedLoan_SubmitIncomeVerificationOption_Handler,
		},
		{
			MethodName: "GetMandateViewDataV2",
			Handler:    _PreApprovedLoan_GetMandateViewDataV2_Handler,
		},
		{
			MethodName: "GetPWARedirectionDetails",
			Handler:    _PreApprovedLoan_GetPWARedirectionDetails_Handler,
		},
		{
			MethodName: "RecordUserAction",
			Handler:    _PreApprovedLoan_RecordUserAction_Handler,
		},
		{
			MethodName: "ProcessOffAppLoanRepayment",
			Handler:    _PreApprovedLoan_ProcessOffAppLoanRepayment_Handler,
		},
		{
			MethodName: "GetLoansUserStatus",
			Handler:    _PreApprovedLoan_GetLoansUserStatus_Handler,
		},
		{
			MethodName: "GetIncomeVerificationInfo",
			Handler:    _PreApprovedLoan_GetIncomeVerificationInfo_Handler,
		},
		{
			MethodName: "GetVkycDeeplink",
			Handler:    _PreApprovedLoan_GetVkycDeeplink_Handler,
		},
		{
			MethodName: "GetForeclosureDetails",
			Handler:    _PreApprovedLoan_GetForeclosureDetails_Handler,
		},
		{
			MethodName: "InitiateLoanClosure",
			Handler:    _PreApprovedLoan_InitiateLoanClosure_Handler,
		},
		{
			MethodName: "CheckLmsDataDifference",
			Handler:    _PreApprovedLoan_CheckLmsDataDifference_Handler,
		},
		{
			MethodName: "GenerateEsignAgreement",
			Handler:    _PreApprovedLoan_GenerateEsignAgreement_Handler,
		},
		{
			MethodName: "GetPrePayDetails",
			Handler:    _PreApprovedLoan_GetPrePayDetails_Handler,
		},
		{
			MethodName: "AddEmploymentDetailsSync",
			Handler:    _PreApprovedLoan_AddEmploymentDetailsSync_Handler,
		},
		{
			MethodName: "SaveContactDetails",
			Handler:    _PreApprovedLoan_SaveContactDetails_Handler,
		},
		{
			MethodName: "InitiateAutoPayExecution",
			Handler:    _PreApprovedLoan_InitiateAutoPayExecution_Handler,
		},
		{
			MethodName: "AddAddressDetailsSync",
			Handler:    _PreApprovedLoan_AddAddressDetailsSync_Handler,
		},
		{
			MethodName: "GetActiveLoanPaymentRequests",
			Handler:    _PreApprovedLoan_GetActiveLoanPaymentRequests_Handler,
		},
		{
			MethodName: "ModifyLoanTerms",
			Handler:    _PreApprovedLoan_ModifyLoanTerms_Handler,
		},
		{
			MethodName: "GetAAConsentCollectionDetails",
			Handler:    _PreApprovedLoan_GetAAConsentCollectionDetails_Handler,
		},
		{
			MethodName: "GetApplicantByVendorApplicantId",
			Handler:    _PreApprovedLoan_GetApplicantByVendorApplicantId_Handler,
		},
		{
			MethodName: "CollectData",
			Handler:    _PreApprovedLoan_CollectData_Handler,
		},
		{
			MethodName: "GetFederalLoanCustomerDetails",
			Handler:    _PreApprovedLoan_GetFederalLoanCustomerDetails_Handler,
		},
		{
			MethodName: "GetRedirectDL",
			Handler:    _PreApprovedLoan_GetRedirectDL_Handler,
		},
		{
			MethodName: "CollectFormData",
			Handler:    _PreApprovedLoan_CollectFormData_Handler,
		},
		{
			MethodName: "GetLoanUserStatusV2",
			Handler:    _PreApprovedLoan_GetLoanUserStatusV2_Handler,
		},
		{
			MethodName: "IsPrepayAllowed",
			Handler:    _PreApprovedLoan_IsPrepayAllowed_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/preapprovedloan/service.proto",
}
