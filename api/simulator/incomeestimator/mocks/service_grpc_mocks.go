// Code generated by MockGen. DO NOT EDIT.
// Source: api/./simulator/incomeestimator/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	inhouse "github.com/epifi/gamma/api/vendors/inhouse"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockIncomeEstimatorClient is a mock of IncomeEstimatorClient interface.
type MockIncomeEstimatorClient struct {
	ctrl     *gomock.Controller
	recorder *MockIncomeEstimatorClientMockRecorder
}

// MockIncomeEstimatorClientMockRecorder is the mock recorder for MockIncomeEstimatorClient.
type MockIncomeEstimatorClientMockRecorder struct {
	mock *MockIncomeEstimatorClient
}

// NewMockIncomeEstimatorClient creates a new mock instance.
func NewMockIncomeEstimatorClient(ctrl *gomock.Controller) *MockIncomeEstimatorClient {
	mock := &MockIncomeEstimatorClient{ctrl: ctrl}
	mock.recorder = &MockIncomeEstimatorClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIncomeEstimatorClient) EXPECT() *MockIncomeEstimatorClientMockRecorder {
	return m.recorder
}

// GetIncomeEstimate mocks base method.
func (m *MockIncomeEstimatorClient) GetIncomeEstimate(ctx context.Context, in *inhouse.GetIncomeEstimateRequest, opts ...grpc.CallOption) (*inhouse.GetIncomeEstimateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIncomeEstimate", varargs...)
	ret0, _ := ret[0].(*inhouse.GetIncomeEstimateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIncomeEstimate indicates an expected call of GetIncomeEstimate.
func (mr *MockIncomeEstimatorClientMockRecorder) GetIncomeEstimate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIncomeEstimate", reflect.TypeOf((*MockIncomeEstimatorClient)(nil).GetIncomeEstimate), varargs...)
}

// MockIncomeEstimatorServer is a mock of IncomeEstimatorServer interface.
type MockIncomeEstimatorServer struct {
	ctrl     *gomock.Controller
	recorder *MockIncomeEstimatorServerMockRecorder
}

// MockIncomeEstimatorServerMockRecorder is the mock recorder for MockIncomeEstimatorServer.
type MockIncomeEstimatorServerMockRecorder struct {
	mock *MockIncomeEstimatorServer
}

// NewMockIncomeEstimatorServer creates a new mock instance.
func NewMockIncomeEstimatorServer(ctrl *gomock.Controller) *MockIncomeEstimatorServer {
	mock := &MockIncomeEstimatorServer{ctrl: ctrl}
	mock.recorder = &MockIncomeEstimatorServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIncomeEstimatorServer) EXPECT() *MockIncomeEstimatorServerMockRecorder {
	return m.recorder
}

// GetIncomeEstimate mocks base method.
func (m *MockIncomeEstimatorServer) GetIncomeEstimate(arg0 context.Context, arg1 *inhouse.GetIncomeEstimateRequest) (*inhouse.GetIncomeEstimateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIncomeEstimate", arg0, arg1)
	ret0, _ := ret[0].(*inhouse.GetIncomeEstimateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIncomeEstimate indicates an expected call of GetIncomeEstimate.
func (mr *MockIncomeEstimatorServerMockRecorder) GetIncomeEstimate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIncomeEstimate", reflect.TypeOf((*MockIncomeEstimatorServer)(nil).GetIncomeEstimate), arg0, arg1)
}

// MockUnsafeIncomeEstimatorServer is a mock of UnsafeIncomeEstimatorServer interface.
type MockUnsafeIncomeEstimatorServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeIncomeEstimatorServerMockRecorder
}

// MockUnsafeIncomeEstimatorServerMockRecorder is the mock recorder for MockUnsafeIncomeEstimatorServer.
type MockUnsafeIncomeEstimatorServerMockRecorder struct {
	mock *MockUnsafeIncomeEstimatorServer
}

// NewMockUnsafeIncomeEstimatorServer creates a new mock instance.
func NewMockUnsafeIncomeEstimatorServer(ctrl *gomock.Controller) *MockUnsafeIncomeEstimatorServer {
	mock := &MockUnsafeIncomeEstimatorServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeIncomeEstimatorServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeIncomeEstimatorServer) EXPECT() *MockUnsafeIncomeEstimatorServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedIncomeEstimatorServer mocks base method.
func (m *MockUnsafeIncomeEstimatorServer) mustEmbedUnimplementedIncomeEstimatorServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedIncomeEstimatorServer")
}

// mustEmbedUnimplementedIncomeEstimatorServer indicates an expected call of mustEmbedUnimplementedIncomeEstimatorServer.
func (mr *MockUnsafeIncomeEstimatorServerMockRecorder) mustEmbedUnimplementedIncomeEstimatorServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedIncomeEstimatorServer", reflect.TypeOf((*MockUnsafeIncomeEstimatorServer)(nil).mustEmbedUnimplementedIncomeEstimatorServer))
}
