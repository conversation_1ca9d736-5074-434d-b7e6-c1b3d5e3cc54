// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/simulator/openbanking/accounts/tpap/upi_lite_account.proto

package tpap

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UpiLiteAccountStatus - represents the current status of upi lite account
type UpiLiteAccountStatus int32

const (
	UpiLiteAccountStatus_UPI_LITE_ACCOUNT_STATUS_UNSPECIFIED UpiLiteAccountStatus = 0
	// upi lite account is in active state
	UpiLiteAccountStatus_UPI_LITE_ACCOUNT_STATUS_ACTIVE UpiLiteAccountStatus = 1
	// upi lite account is in inactive state
	UpiLiteAccountStatus_UPI_LITE_ACCOUNT_STATUS_INACTIVE UpiLiteAccountStatus = 2
)

// Enum value maps for UpiLiteAccountStatus.
var (
	UpiLiteAccountStatus_name = map[int32]string{
		0: "UPI_LITE_ACCOUNT_STATUS_UNSPECIFIED",
		1: "UPI_LITE_ACCOUNT_STATUS_ACTIVE",
		2: "UPI_LITE_ACCOUNT_STATUS_INACTIVE",
	}
	UpiLiteAccountStatus_value = map[string]int32{
		"UPI_LITE_ACCOUNT_STATUS_UNSPECIFIED": 0,
		"UPI_LITE_ACCOUNT_STATUS_ACTIVE":      1,
		"UPI_LITE_ACCOUNT_STATUS_INACTIVE":    2,
	}
)

func (x UpiLiteAccountStatus) Enum() *UpiLiteAccountStatus {
	p := new(UpiLiteAccountStatus)
	*p = x
	return p
}

func (x UpiLiteAccountStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiLiteAccountStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_enumTypes[0].Descriptor()
}

func (UpiLiteAccountStatus) Type() protoreflect.EnumType {
	return &file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_enumTypes[0]
}

func (x UpiLiteAccountStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiLiteAccountStatus.Descriptor instead.
func (UpiLiteAccountStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_rawDescGZIP(), []int{0}
}

var File_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto protoreflect.FileDescriptor

var file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f,
	0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x2f, 0x74, 0x70, 0x61, 0x70, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6c, 0x69,
	0x74, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x23, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x2e, 0x74, 0x70, 0x61, 0x70, 0x2a, 0x89, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27,
	0x0a, 0x23, 0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x55, 0x50, 0x49, 0x5f, 0x4c,
	0x49, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x55,
	0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10,
	0x02, 0x42, 0x80, 0x01, 0x0a, 0x3e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e,
	0x74, 0x70, 0x61, 0x70, 0x5a, 0x3e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f,
	0x74, 0x70, 0x61, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_rawDescOnce sync.Once
	file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_rawDescData = file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_rawDesc
)

func file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_rawDescGZIP() []byte {
	file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_rawDescOnce.Do(func() {
		file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_rawDescData)
	})
	return file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_rawDescData
}

var file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_goTypes = []interface{}{
	(UpiLiteAccountStatus)(0), // 0: simulator.openbanking.accounts.tpap.UpiLiteAccountStatus
}
var file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_init() }
func file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_init() {
	if File_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_goTypes,
		DependencyIndexes: file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_depIdxs,
		EnumInfos:         file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_enumTypes,
	}.Build()
	File_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto = out.File
	file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_rawDesc = nil
	file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_goTypes = nil
	file_api_simulator_openbanking_accounts_tpap_upi_lite_account_proto_depIdxs = nil
}
