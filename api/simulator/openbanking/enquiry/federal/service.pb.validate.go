// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/simulator/openbanking/enquiry/federal/service.proto

package federal

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CustomerCreationEnquiryStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CustomerCreationEnquiryStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerCreationEnquiryStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CustomerCreationEnquiryStatusResponseMultiError, or nil if none found.
func (m *CustomerCreationEnquiryStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerCreationEnquiryStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for DeviceToken

	// no validation rules for RequestId

	// no validation rules for ResponseCode

	// no validation rules for ResponseReason

	// no validation rules for ResponseAction

	// no validation rules for OriginalRequestId

	if all {
		switch v := interface{}(m.GetCustomerCreationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerCreationEnquiryStatusResponseValidationError{
					field:  "CustomerCreationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerCreationEnquiryStatusResponseValidationError{
					field:  "CustomerCreationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomerCreationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerCreationEnquiryStatusResponseValidationError{
				field:  "CustomerCreationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestTimestamp

	if len(errors) > 0 {
		return CustomerCreationEnquiryStatusResponseMultiError(errors)
	}

	return nil
}

// CustomerCreationEnquiryStatusResponseMultiError is an error wrapping
// multiple validation errors returned by
// CustomerCreationEnquiryStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type CustomerCreationEnquiryStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerCreationEnquiryStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerCreationEnquiryStatusResponseMultiError) AllErrors() []error { return m }

// CustomerCreationEnquiryStatusResponseValidationError is the validation error
// returned by CustomerCreationEnquiryStatusResponse.Validate if the
// designated constraints aren't met.
type CustomerCreationEnquiryStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerCreationEnquiryStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerCreationEnquiryStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerCreationEnquiryStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerCreationEnquiryStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerCreationEnquiryStatusResponseValidationError) ErrorName() string {
	return "CustomerCreationEnquiryStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CustomerCreationEnquiryStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerCreationEnquiryStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerCreationEnquiryStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerCreationEnquiryStatusResponseValidationError{}

// Validate checks the field values on AccountCreationEnquiryStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AccountCreationEnquiryStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountCreationEnquiryStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AccountCreationEnquiryStatusResponseMultiError, or nil if none found.
func (m *AccountCreationEnquiryStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountCreationEnquiryStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for DeviceToken

	// no validation rules for RequestId

	// no validation rules for ResponseCode

	// no validation rules for ResponseReason

	// no validation rules for ResponseAction

	// no validation rules for OriginalRequestId

	if all {
		switch v := interface{}(m.GetAccountCreationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountCreationEnquiryStatusResponseValidationError{
					field:  "AccountCreationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountCreationEnquiryStatusResponseValidationError{
					field:  "AccountCreationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountCreationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountCreationEnquiryStatusResponseValidationError{
				field:  "AccountCreationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestTimestamp

	if len(errors) > 0 {
		return AccountCreationEnquiryStatusResponseMultiError(errors)
	}

	return nil
}

// AccountCreationEnquiryStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// AccountCreationEnquiryStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type AccountCreationEnquiryStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountCreationEnquiryStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountCreationEnquiryStatusResponseMultiError) AllErrors() []error { return m }

// AccountCreationEnquiryStatusResponseValidationError is the validation error
// returned by AccountCreationEnquiryStatusResponse.Validate if the designated
// constraints aren't met.
type AccountCreationEnquiryStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountCreationEnquiryStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountCreationEnquiryStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountCreationEnquiryStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountCreationEnquiryStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountCreationEnquiryStatusResponseValidationError) ErrorName() string {
	return "AccountCreationEnquiryStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AccountCreationEnquiryStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountCreationEnquiryStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountCreationEnquiryStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountCreationEnquiryStatusResponseValidationError{}

// Validate checks the field values on CardCreationEnquiryStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CardCreationEnquiryStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardCreationEnquiryStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CardCreationEnquiryStatusResponseMultiError, or nil if none found.
func (m *CardCreationEnquiryStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CardCreationEnquiryStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for DeviceToken

	// no validation rules for RequestId

	// no validation rules for ResponseCode

	// no validation rules for ResponseReason

	// no validation rules for ResponseAction

	// no validation rules for OriginalRequestId

	if all {
		switch v := interface{}(m.GetCardCreationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardCreationEnquiryStatusResponseValidationError{
					field:  "CardCreationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardCreationEnquiryStatusResponseValidationError{
					field:  "CardCreationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardCreationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardCreationEnquiryStatusResponseValidationError{
				field:  "CardCreationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestTimestamp

	if len(errors) > 0 {
		return CardCreationEnquiryStatusResponseMultiError(errors)
	}

	return nil
}

// CardCreationEnquiryStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// CardCreationEnquiryStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type CardCreationEnquiryStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardCreationEnquiryStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardCreationEnquiryStatusResponseMultiError) AllErrors() []error { return m }

// CardCreationEnquiryStatusResponseValidationError is the validation error
// returned by CardCreationEnquiryStatusResponse.Validate if the designated
// constraints aren't met.
type CardCreationEnquiryStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardCreationEnquiryStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardCreationEnquiryStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardCreationEnquiryStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardCreationEnquiryStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardCreationEnquiryStatusResponseValidationError) ErrorName() string {
	return "CardCreationEnquiryStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CardCreationEnquiryStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardCreationEnquiryStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardCreationEnquiryStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardCreationEnquiryStatusResponseValidationError{}

// Validate checks the field values on
// DeviceReRegistrationDetailsEnquiryStatusResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeviceReRegistrationDetailsEnquiryStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeviceReRegistrationDetailsEnquiryStatusResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// DeviceReRegistrationDetailsEnquiryStatusResponseMultiError, or nil if none found.
func (m *DeviceReRegistrationDetailsEnquiryStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceReRegistrationDetailsEnquiryStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for DeviceToken

	// no validation rules for RequestId

	// no validation rules for ResponseCode

	// no validation rules for ResponseReason

	// no validation rules for ResponseAction

	// no validation rules for OriginalRequestId

	if all {
		switch v := interface{}(m.GetDevReregistrationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceReRegistrationDetailsEnquiryStatusResponseValidationError{
					field:  "DevReregistrationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceReRegistrationDetailsEnquiryStatusResponseValidationError{
					field:  "DevReregistrationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevReregistrationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceReRegistrationDetailsEnquiryStatusResponseValidationError{
				field:  "DevReregistrationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestTimestamp

	if len(errors) > 0 {
		return DeviceReRegistrationDetailsEnquiryStatusResponseMultiError(errors)
	}

	return nil
}

// DeviceReRegistrationDetailsEnquiryStatusResponseMultiError is an error
// wrapping multiple validation errors returned by
// DeviceReRegistrationDetailsEnquiryStatusResponse.ValidateAll() if the
// designated constraints aren't met.
type DeviceReRegistrationDetailsEnquiryStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceReRegistrationDetailsEnquiryStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceReRegistrationDetailsEnquiryStatusResponseMultiError) AllErrors() []error { return m }

// DeviceReRegistrationDetailsEnquiryStatusResponseValidationError is the
// validation error returned by
// DeviceReRegistrationDetailsEnquiryStatusResponse.Validate if the designated
// constraints aren't met.
type DeviceReRegistrationDetailsEnquiryStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceReRegistrationDetailsEnquiryStatusResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e DeviceReRegistrationDetailsEnquiryStatusResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e DeviceReRegistrationDetailsEnquiryStatusResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e DeviceReRegistrationDetailsEnquiryStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceReRegistrationDetailsEnquiryStatusResponseValidationError) ErrorName() string {
	return "DeviceReRegistrationDetailsEnquiryStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeviceReRegistrationDetailsEnquiryStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceReRegistrationDetailsEnquiryStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceReRegistrationDetailsEnquiryStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceReRegistrationDetailsEnquiryStatusResponseValidationError{}

// Validate checks the field values on
// MailingAddressModifyDetailsEnquiryStatusResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MailingAddressModifyDetailsEnquiryStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// MailingAddressModifyDetailsEnquiryStatusResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// MailingAddressModifyDetailsEnquiryStatusResponseMultiError, or nil if none found.
func (m *MailingAddressModifyDetailsEnquiryStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MailingAddressModifyDetailsEnquiryStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for DeviceToken

	// no validation rules for RequestId

	// no validation rules for ResponseCode

	// no validation rules for ResponseReason

	// no validation rules for ResponseAction

	// no validation rules for OriginalRequestId

	if all {
		switch v := interface{}(m.GetMailingAddressModificationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MailingAddressModifyDetailsEnquiryStatusResponseValidationError{
					field:  "MailingAddressModificationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MailingAddressModifyDetailsEnquiryStatusResponseValidationError{
					field:  "MailingAddressModificationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMailingAddressModificationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MailingAddressModifyDetailsEnquiryStatusResponseValidationError{
				field:  "MailingAddressModificationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestTimestamp

	if len(errors) > 0 {
		return MailingAddressModifyDetailsEnquiryStatusResponseMultiError(errors)
	}

	return nil
}

// MailingAddressModifyDetailsEnquiryStatusResponseMultiError is an error
// wrapping multiple validation errors returned by
// MailingAddressModifyDetailsEnquiryStatusResponse.ValidateAll() if the
// designated constraints aren't met.
type MailingAddressModifyDetailsEnquiryStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MailingAddressModifyDetailsEnquiryStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MailingAddressModifyDetailsEnquiryStatusResponseMultiError) AllErrors() []error { return m }

// MailingAddressModifyDetailsEnquiryStatusResponseValidationError is the
// validation error returned by
// MailingAddressModifyDetailsEnquiryStatusResponse.Validate if the designated
// constraints aren't met.
type MailingAddressModifyDetailsEnquiryStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MailingAddressModifyDetailsEnquiryStatusResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e MailingAddressModifyDetailsEnquiryStatusResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e MailingAddressModifyDetailsEnquiryStatusResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e MailingAddressModifyDetailsEnquiryStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MailingAddressModifyDetailsEnquiryStatusResponseValidationError) ErrorName() string {
	return "MailingAddressModifyDetailsEnquiryStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MailingAddressModifyDetailsEnquiryStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMailingAddressModifyDetailsEnquiryStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MailingAddressModifyDetailsEnquiryStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MailingAddressModifyDetailsEnquiryStatusResponseValidationError{}

// Validate checks the field values on
// ShippingAddressUpdateEnquiryStatusResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ShippingAddressUpdateEnquiryStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ShippingAddressUpdateEnquiryStatusResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ShippingAddressUpdateEnquiryStatusResponseMultiError, or nil if none found.
func (m *ShippingAddressUpdateEnquiryStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ShippingAddressUpdateEnquiryStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for DeviceToken

	// no validation rules for RequestId

	// no validation rules for ResponseCode

	// no validation rules for ResponseReason

	// no validation rules for ResponseAction

	// no validation rules for OriginalRequestId

	if all {
		switch v := interface{}(m.GetShippingAddressUpdateDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ShippingAddressUpdateEnquiryStatusResponseValidationError{
					field:  "ShippingAddressUpdateDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ShippingAddressUpdateEnquiryStatusResponseValidationError{
					field:  "ShippingAddressUpdateDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShippingAddressUpdateDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShippingAddressUpdateEnquiryStatusResponseValidationError{
				field:  "ShippingAddressUpdateDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestTimestamp

	if len(errors) > 0 {
		return ShippingAddressUpdateEnquiryStatusResponseMultiError(errors)
	}

	return nil
}

// ShippingAddressUpdateEnquiryStatusResponseMultiError is an error wrapping
// multiple validation errors returned by
// ShippingAddressUpdateEnquiryStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type ShippingAddressUpdateEnquiryStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShippingAddressUpdateEnquiryStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShippingAddressUpdateEnquiryStatusResponseMultiError) AllErrors() []error { return m }

// ShippingAddressUpdateEnquiryStatusResponseValidationError is the validation
// error returned by ShippingAddressUpdateEnquiryStatusResponse.Validate if
// the designated constraints aren't met.
type ShippingAddressUpdateEnquiryStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShippingAddressUpdateEnquiryStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShippingAddressUpdateEnquiryStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShippingAddressUpdateEnquiryStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShippingAddressUpdateEnquiryStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShippingAddressUpdateEnquiryStatusResponseValidationError) ErrorName() string {
	return "ShippingAddressUpdateEnquiryStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ShippingAddressUpdateEnquiryStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShippingAddressUpdateEnquiryStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShippingAddressUpdateEnquiryStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShippingAddressUpdateEnquiryStatusResponseValidationError{}

// Validate checks the field values on
// DeviceRegistrationDetailsEnquiryStatusResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeviceRegistrationDetailsEnquiryStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeviceRegistrationDetailsEnquiryStatusResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// DeviceRegistrationDetailsEnquiryStatusResponseMultiError, or nil if none found.
func (m *DeviceRegistrationDetailsEnquiryStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceRegistrationDetailsEnquiryStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for DeviceToken

	// no validation rules for RequestId

	// no validation rules for ResponseCode

	// no validation rules for ResponseReason

	// no validation rules for ResponseAction

	// no validation rules for OriginalRequestId

	if all {
		switch v := interface{}(m.GetDeviceRegistrationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceRegistrationDetailsEnquiryStatusResponseValidationError{
					field:  "DeviceRegistrationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceRegistrationDetailsEnquiryStatusResponseValidationError{
					field:  "DeviceRegistrationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceRegistrationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceRegistrationDetailsEnquiryStatusResponseValidationError{
				field:  "DeviceRegistrationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestTimestamp

	if len(errors) > 0 {
		return DeviceRegistrationDetailsEnquiryStatusResponseMultiError(errors)
	}

	return nil
}

// DeviceRegistrationDetailsEnquiryStatusResponseMultiError is an error
// wrapping multiple validation errors returned by
// DeviceRegistrationDetailsEnquiryStatusResponse.ValidateAll() if the
// designated constraints aren't met.
type DeviceRegistrationDetailsEnquiryStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceRegistrationDetailsEnquiryStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceRegistrationDetailsEnquiryStatusResponseMultiError) AllErrors() []error { return m }

// DeviceRegistrationDetailsEnquiryStatusResponseValidationError is the
// validation error returned by
// DeviceRegistrationDetailsEnquiryStatusResponse.Validate if the designated
// constraints aren't met.
type DeviceRegistrationDetailsEnquiryStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceRegistrationDetailsEnquiryStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceRegistrationDetailsEnquiryStatusResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e DeviceRegistrationDetailsEnquiryStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceRegistrationDetailsEnquiryStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceRegistrationDetailsEnquiryStatusResponseValidationError) ErrorName() string {
	return "DeviceRegistrationDetailsEnquiryStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeviceRegistrationDetailsEnquiryStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceRegistrationDetailsEnquiryStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceRegistrationDetailsEnquiryStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceRegistrationDetailsEnquiryStatusResponseValidationError{}

// Validate checks the field values on
// PhysicalCardDispatchDetailsEnquiryStatusResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PhysicalCardDispatchDetailsEnquiryStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PhysicalCardDispatchDetailsEnquiryStatusResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// PhysicalCardDispatchDetailsEnquiryStatusResponseMultiError, or nil if none found.
func (m *PhysicalCardDispatchDetailsEnquiryStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PhysicalCardDispatchDetailsEnquiryStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for DeviceToken

	// no validation rules for RequestId

	// no validation rules for ResponseCode

	// no validation rules for ResponseReason

	// no validation rules for ResponseAction

	// no validation rules for OriginalRequestId

	if all {
		switch v := interface{}(m.GetPhysicalCardDispatchDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError{
					field:  "PhysicalCardDispatchDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError{
					field:  "PhysicalCardDispatchDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhysicalCardDispatchDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError{
				field:  "PhysicalCardDispatchDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestTimestamp

	if len(errors) > 0 {
		return PhysicalCardDispatchDetailsEnquiryStatusResponseMultiError(errors)
	}

	return nil
}

// PhysicalCardDispatchDetailsEnquiryStatusResponseMultiError is an error
// wrapping multiple validation errors returned by
// PhysicalCardDispatchDetailsEnquiryStatusResponse.ValidateAll() if the
// designated constraints aren't met.
type PhysicalCardDispatchDetailsEnquiryStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhysicalCardDispatchDetailsEnquiryStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhysicalCardDispatchDetailsEnquiryStatusResponseMultiError) AllErrors() []error { return m }

// PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError is the
// validation error returned by
// PhysicalCardDispatchDetailsEnquiryStatusResponse.Validate if the designated
// constraints aren't met.
type PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError) ErrorName() string {
	return "PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhysicalCardDispatchDetailsEnquiryStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhysicalCardDispatchDetailsEnquiryStatusResponseValidationError{}
