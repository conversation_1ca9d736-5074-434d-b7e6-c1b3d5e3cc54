// Code generated by MockGen. DO NOT EDIT.
// Source: api/simulator/openbanking/card/federal/callback_consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	federal "github.com/epifi/gamma/api/simulator/openbanking/card/federal"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCallbackConsumerClient is a mock of CallbackConsumerClient interface.
type MockCallbackConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockCallbackConsumerClientMockRecorder
}

// MockCallbackConsumerClientMockRecorder is the mock recorder for MockCallbackConsumerClient.
type MockCallbackConsumerClientMockRecorder struct {
	mock *MockCallbackConsumerClient
}

// NewMockCallbackConsumerClient creates a new mock instance.
func NewMockCallbackConsumerClient(ctrl *gomock.Controller) *MockCallbackConsumerClient {
	mock := &MockCallbackConsumerClient{ctrl: ctrl}
	mock.recorder = &MockCallbackConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCallbackConsumerClient) EXPECT() *MockCallbackConsumerClientMockRecorder {
	return m.recorder
}

// ProcessCreateCardCallBack mocks base method.
func (m *MockCallbackConsumerClient) ProcessCreateCardCallBack(ctx context.Context, in *federal.ProcessCreateCardCallBackRequest, opts ...grpc.CallOption) (*federal.ProcessCreateCardCallBackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCreateCardCallBack", varargs...)
	ret0, _ := ret[0].(*federal.ProcessCreateCardCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCreateCardCallBack indicates an expected call of ProcessCreateCardCallBack.
func (mr *MockCallbackConsumerClientMockRecorder) ProcessCreateCardCallBack(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCreateCardCallBack", reflect.TypeOf((*MockCallbackConsumerClient)(nil).ProcessCreateCardCallBack), varargs...)
}

// ProcessPhysicalCardDispatchCallBack mocks base method.
func (m *MockCallbackConsumerClient) ProcessPhysicalCardDispatchCallBack(ctx context.Context, in *federal.ProcessPhysicalCardDispatchCallBackRequest, opts ...grpc.CallOption) (*federal.ProcessPhysicalCardDispatchCallBackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessPhysicalCardDispatchCallBack", varargs...)
	ret0, _ := ret[0].(*federal.ProcessPhysicalCardDispatchCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessPhysicalCardDispatchCallBack indicates an expected call of ProcessPhysicalCardDispatchCallBack.
func (mr *MockCallbackConsumerClientMockRecorder) ProcessPhysicalCardDispatchCallBack(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessPhysicalCardDispatchCallBack", reflect.TypeOf((*MockCallbackConsumerClient)(nil).ProcessPhysicalCardDispatchCallBack), varargs...)
}

// MockCallbackConsumerServer is a mock of CallbackConsumerServer interface.
type MockCallbackConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockCallbackConsumerServerMockRecorder
}

// MockCallbackConsumerServerMockRecorder is the mock recorder for MockCallbackConsumerServer.
type MockCallbackConsumerServerMockRecorder struct {
	mock *MockCallbackConsumerServer
}

// NewMockCallbackConsumerServer creates a new mock instance.
func NewMockCallbackConsumerServer(ctrl *gomock.Controller) *MockCallbackConsumerServer {
	mock := &MockCallbackConsumerServer{ctrl: ctrl}
	mock.recorder = &MockCallbackConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCallbackConsumerServer) EXPECT() *MockCallbackConsumerServerMockRecorder {
	return m.recorder
}

// ProcessCreateCardCallBack mocks base method.
func (m *MockCallbackConsumerServer) ProcessCreateCardCallBack(arg0 context.Context, arg1 *federal.ProcessCreateCardCallBackRequest) (*federal.ProcessCreateCardCallBackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCreateCardCallBack", arg0, arg1)
	ret0, _ := ret[0].(*federal.ProcessCreateCardCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCreateCardCallBack indicates an expected call of ProcessCreateCardCallBack.
func (mr *MockCallbackConsumerServerMockRecorder) ProcessCreateCardCallBack(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCreateCardCallBack", reflect.TypeOf((*MockCallbackConsumerServer)(nil).ProcessCreateCardCallBack), arg0, arg1)
}

// ProcessPhysicalCardDispatchCallBack mocks base method.
func (m *MockCallbackConsumerServer) ProcessPhysicalCardDispatchCallBack(arg0 context.Context, arg1 *federal.ProcessPhysicalCardDispatchCallBackRequest) (*federal.ProcessPhysicalCardDispatchCallBackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessPhysicalCardDispatchCallBack", arg0, arg1)
	ret0, _ := ret[0].(*federal.ProcessPhysicalCardDispatchCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessPhysicalCardDispatchCallBack indicates an expected call of ProcessPhysicalCardDispatchCallBack.
func (mr *MockCallbackConsumerServerMockRecorder) ProcessPhysicalCardDispatchCallBack(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessPhysicalCardDispatchCallBack", reflect.TypeOf((*MockCallbackConsumerServer)(nil).ProcessPhysicalCardDispatchCallBack), arg0, arg1)
}

// MockUnsafeCallbackConsumerServer is a mock of UnsafeCallbackConsumerServer interface.
type MockUnsafeCallbackConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCallbackConsumerServerMockRecorder
}

// MockUnsafeCallbackConsumerServerMockRecorder is the mock recorder for MockUnsafeCallbackConsumerServer.
type MockUnsafeCallbackConsumerServerMockRecorder struct {
	mock *MockUnsafeCallbackConsumerServer
}

// NewMockUnsafeCallbackConsumerServer creates a new mock instance.
func NewMockUnsafeCallbackConsumerServer(ctrl *gomock.Controller) *MockUnsafeCallbackConsumerServer {
	mock := &MockUnsafeCallbackConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCallbackConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCallbackConsumerServer) EXPECT() *MockUnsafeCallbackConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCallbackConsumerServer mocks base method.
func (m *MockUnsafeCallbackConsumerServer) mustEmbedUnimplementedCallbackConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCallbackConsumerServer")
}

// mustEmbedUnimplementedCallbackConsumerServer indicates an expected call of mustEmbedUnimplementedCallbackConsumerServer.
func (mr *MockUnsafeCallbackConsumerServerMockRecorder) mustEmbedUnimplementedCallbackConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCallbackConsumerServer", reflect.TypeOf((*MockUnsafeCallbackConsumerServer)(nil).mustEmbedUnimplementedCallbackConsumerServer))
}
