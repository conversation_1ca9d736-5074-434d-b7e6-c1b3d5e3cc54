// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package simulator.cx.watson_client;

import "api/cx/watson/watson_client.proto";

option go_package = "github.com/epifi/gamma/api/simulator/cx/watson_client";
option java_package = "com.github.epifi.gamma.api.simulator.cx.watson_client";


service WatsonClient {
  rpc IsIncidentValid (.cx.watson.IsIncidentValidRequest) returns (.cx.watson.IsIncidentValidResponse) {}

  rpc GetTicketDetails (.cx.watson.GetTicketDetailsRequest) returns (.cx.watson.GetTicketDetailsResponse) {}

  rpc IsIncidentResolved (.cx.watson.IsIncidentResolvedRequest) returns (.cx.watson.IsIncidentResolvedResponse) {}
}
