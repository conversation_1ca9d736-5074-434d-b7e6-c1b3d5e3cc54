// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/simulator/standinginstruction/federal/service.proto

package federal

import (
	federal "github.com/epifi/gamma/api/vendors/federal"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_api_simulator_standinginstruction_federal_service_proto protoreflect.FileDescriptor

var file_api_simulator_standinginstruction_federal_service_proto_rawDesc = []byte{
	0x0a, 0x37, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f,
	0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x25, 0x73, 0x69, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x6f, 0x72, 0x2e, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x69, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c,
	0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65,
	0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x69, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xac, 0x04,
	0x0a, 0x13, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x81, 0x01, 0x0a, 0x08, 0x53, 0x49, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x12, 0x20, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64,
	0x65, 0x72, 0x61, 0x6c, 0x2e, 0x53, 0x49, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66,
	0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x53, 0x49, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x3a,
	0x01, 0x2a, 0x22, 0x25, 0x2f, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x69, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c,
	0x2f, 0x73, 0x69, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x09, 0x53, 0x49,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x12, 0x21, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x53, 0x49, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x53, 0x49, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x41, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x73, 0x69, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x08, 0x53, 0x49, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x12, 0x20, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2e, 0x53, 0x69, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65,
	0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x53, 0x49, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x3a, 0x01,
	0x2a, 0x22, 0x25, 0x2f, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x69, 0x6e, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f,
	0x73, 0x69, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x12, 0x81, 0x01, 0x0a, 0x08, 0x53, 0x49, 0x52,
	0x65, 0x76, 0x6f, 0x6b, 0x65, 0x12, 0x20, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x53, 0x69, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x53, 0x49, 0x52, 0x65, 0x76, 0x6f,
	0x6b, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2a, 0x3a, 0x01, 0x2a, 0x22, 0x25, 0x2f, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2f, 0x73, 0x69, 0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x42, 0x84, 0x01, 0x0a,
	0x40, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x69, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61,
	0x6c, 0x5a, 0x40, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_api_simulator_standinginstruction_federal_service_proto_goTypes = []interface{}{
	(*federal.SICreateRequest)(nil),      // 0: vendors.federal.SICreateRequest
	(*federal.SIExecuteRequest)(nil),     // 1: vendors.federal.SIExecuteRequest
	(*federal.SiModifyRequest)(nil),      // 2: vendors.federal.SiModifyRequest
	(*federal.SiRevokeRequest)(nil),      // 3: vendors.federal.SiRevokeRequest
	(*federal.SICreateResponse)(nil),     // 4: vendors.federal.SICreateResponse
	(*federal.SIExecuteAckResponse)(nil), // 5: vendors.federal.SIExecuteAckResponse
	(*federal.SIModifyResponse)(nil),     // 6: vendors.federal.SIModifyResponse
	(*federal.SIRevokeResponse)(nil),     // 7: vendors.federal.SIRevokeResponse
}
var file_api_simulator_standinginstruction_federal_service_proto_depIdxs = []int32{
	0, // 0: simulator.standinginstruction.federal.StandingInstruction.SICreate:input_type -> vendors.federal.SICreateRequest
	1, // 1: simulator.standinginstruction.federal.StandingInstruction.SIExecute:input_type -> vendors.federal.SIExecuteRequest
	2, // 2: simulator.standinginstruction.federal.StandingInstruction.SIModify:input_type -> vendors.federal.SiModifyRequest
	3, // 3: simulator.standinginstruction.federal.StandingInstruction.SIRevoke:input_type -> vendors.federal.SiRevokeRequest
	4, // 4: simulator.standinginstruction.federal.StandingInstruction.SICreate:output_type -> vendors.federal.SICreateResponse
	5, // 5: simulator.standinginstruction.federal.StandingInstruction.SIExecute:output_type -> vendors.federal.SIExecuteAckResponse
	6, // 6: simulator.standinginstruction.federal.StandingInstruction.SIModify:output_type -> vendors.federal.SIModifyResponse
	7, // 7: simulator.standinginstruction.federal.StandingInstruction.SIRevoke:output_type -> vendors.federal.SIRevokeResponse
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_simulator_standinginstruction_federal_service_proto_init() }
func file_api_simulator_standinginstruction_federal_service_proto_init() {
	if File_api_simulator_standinginstruction_federal_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_simulator_standinginstruction_federal_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_simulator_standinginstruction_federal_service_proto_goTypes,
		DependencyIndexes: file_api_simulator_standinginstruction_federal_service_proto_depIdxs,
	}.Build()
	File_api_simulator_standinginstruction_federal_service_proto = out.File
	file_api_simulator_standinginstruction_federal_service_proto_rawDesc = nil
	file_api_simulator_standinginstruction_federal_service_proto_goTypes = nil
	file_api_simulator_standinginstruction_federal_service_proto_depIdxs = nil
}
