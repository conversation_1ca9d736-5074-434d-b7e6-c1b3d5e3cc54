// Code generated by MockGen. DO NOT EDIT.
// Source: api/./simulator/idvalidate/karza/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	karza "github.com/epifi/gamma/api/vendors/karza"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockIdValidateClient is a mock of IdValidateClient interface.
type MockIdValidateClient struct {
	ctrl     *gomock.Controller
	recorder *MockIdValidateClientMockRecorder
}

// MockIdValidateClientMockRecorder is the mock recorder for MockIdValidateClient.
type MockIdValidateClientMockRecorder struct {
	mock *MockIdValidateClient
}

// NewMockIdValidateClient creates a new mock instance.
func NewMockIdValidateClient(ctrl *gomock.Controller) *MockIdValidateClient {
	mock := &MockIdValidateClient{ctrl: ctrl}
	mock.recorder = &MockIdValidateClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIdValidateClient) EXPECT() *MockIdValidateClientMockRecorder {
	return m.recorder
}

// ValidateVoterId mocks base method.
func (m *MockIdValidateClient) ValidateVoterId(ctx context.Context, in *karza.ValidateVoterIdRequest, opts ...grpc.CallOption) (*karza.ValidateVoterIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValidateVoterId", varargs...)
	ret0, _ := ret[0].(*karza.ValidateVoterIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateVoterId indicates an expected call of ValidateVoterId.
func (mr *MockIdValidateClientMockRecorder) ValidateVoterId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateVoterId", reflect.TypeOf((*MockIdValidateClient)(nil).ValidateVoterId), varargs...)
}

// MockIdValidateServer is a mock of IdValidateServer interface.
type MockIdValidateServer struct {
	ctrl     *gomock.Controller
	recorder *MockIdValidateServerMockRecorder
}

// MockIdValidateServerMockRecorder is the mock recorder for MockIdValidateServer.
type MockIdValidateServerMockRecorder struct {
	mock *MockIdValidateServer
}

// NewMockIdValidateServer creates a new mock instance.
func NewMockIdValidateServer(ctrl *gomock.Controller) *MockIdValidateServer {
	mock := &MockIdValidateServer{ctrl: ctrl}
	mock.recorder = &MockIdValidateServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIdValidateServer) EXPECT() *MockIdValidateServerMockRecorder {
	return m.recorder
}

// ValidateVoterId mocks base method.
func (m *MockIdValidateServer) ValidateVoterId(arg0 context.Context, arg1 *karza.ValidateVoterIdRequest) (*karza.ValidateVoterIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateVoterId", arg0, arg1)
	ret0, _ := ret[0].(*karza.ValidateVoterIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateVoterId indicates an expected call of ValidateVoterId.
func (mr *MockIdValidateServerMockRecorder) ValidateVoterId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateVoterId", reflect.TypeOf((*MockIdValidateServer)(nil).ValidateVoterId), arg0, arg1)
}

// MockUnsafeIdValidateServer is a mock of UnsafeIdValidateServer interface.
type MockUnsafeIdValidateServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeIdValidateServerMockRecorder
}

// MockUnsafeIdValidateServerMockRecorder is the mock recorder for MockUnsafeIdValidateServer.
type MockUnsafeIdValidateServerMockRecorder struct {
	mock *MockUnsafeIdValidateServer
}

// NewMockUnsafeIdValidateServer creates a new mock instance.
func NewMockUnsafeIdValidateServer(ctrl *gomock.Controller) *MockUnsafeIdValidateServer {
	mock := &MockUnsafeIdValidateServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeIdValidateServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeIdValidateServer) EXPECT() *MockUnsafeIdValidateServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedIdValidateServer mocks base method.
func (m *MockUnsafeIdValidateServer) mustEmbedUnimplementedIdValidateServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedIdValidateServer")
}

// mustEmbedUnimplementedIdValidateServer indicates an expected call of mustEmbedUnimplementedIdValidateServer.
func (mr *MockUnsafeIdValidateServerMockRecorder) mustEmbedUnimplementedIdValidateServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedIdValidateServer", reflect.TypeOf((*MockUnsafeIdValidateServer)(nil).mustEmbedUnimplementedIdValidateServer))
}
