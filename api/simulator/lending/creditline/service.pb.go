// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/simulator/lending/creditline/service.proto

package creditline

import (
	lending "github.com/epifi/gamma/api/vendors/federal/lending"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_api_simulator_lending_creditline_service_proto protoreflect.FileDescriptor

var file_api_simulator_lending_creditline_service_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x6c, 0x69,
	0x6e, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1c, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x2d,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x98, 0x01, 0x0a, 0x0a,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x0e, 0x47,
	0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x2e, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x16,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x3a, 0x01, 0x2a, 0x22, 0x0b, 0x2f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x46, 0x65, 0x74, 0x63, 0x68, 0x42, 0x72, 0x0a, 0x37, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x6c, 0x69, 0x6e,
	0x65, 0x5a, 0x37, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var file_api_simulator_lending_creditline_service_proto_goTypes = []interface{}{
	(*lending.GetCreditLimitRequest)(nil),  // 0: vendors.federal.lending.GetCreditLimitRequest
	(*lending.GetCreditLimitResponse)(nil), // 1: vendors.federal.lending.GetCreditLimitResponse
}
var file_api_simulator_lending_creditline_service_proto_depIdxs = []int32{
	0, // 0: simulator.lending.creditline.CreditLine.GetCreditLimit:input_type -> vendors.federal.lending.GetCreditLimitRequest
	1, // 1: simulator.lending.creditline.CreditLine.GetCreditLimit:output_type -> vendors.federal.lending.GetCreditLimitResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_simulator_lending_creditline_service_proto_init() }
func file_api_simulator_lending_creditline_service_proto_init() {
	if File_api_simulator_lending_creditline_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_simulator_lending_creditline_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_simulator_lending_creditline_service_proto_goTypes,
		DependencyIndexes: file_api_simulator_lending_creditline_service_proto_depIdxs,
	}.Build()
	File_api_simulator_lending_creditline_service_proto = out.File
	file_api_simulator_lending_creditline_service_proto_rawDesc = nil
	file_api_simulator_lending_creditline_service_proto_goTypes = nil
	file_api_simulator_lending_creditline_service_proto_depIdxs = nil
}
