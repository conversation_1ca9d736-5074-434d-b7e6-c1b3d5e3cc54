// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/lenden/init_mandate.proto

package lenden

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InitMandateRequestPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string              `protobuf:"bytes,1,opt,name=user_id,proto3" json:"user_id,omitempty"`
	LoanId      string              `protobuf:"bytes,2,opt,name=loan_id,proto3" json:"loan_id,omitempty"`
	ProductId   string              `protobuf:"bytes,3,opt,name=product_id,proto3" json:"product_id,omitempty"`
	Type        string              `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	ConsentData *ConsentDataRequest `protobuf:"bytes,5,opt,name=consent_data,proto3" json:"consent_data,omitempty"`
}

func (x *InitMandateRequestPayload) Reset() {
	*x = InitMandateRequestPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_init_mandate_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitMandateRequestPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitMandateRequestPayload) ProtoMessage() {}

func (x *InitMandateRequestPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_init_mandate_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitMandateRequestPayload.ProtoReflect.Descriptor instead.
func (*InitMandateRequestPayload) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_init_mandate_proto_rawDescGZIP(), []int{0}
}

func (x *InitMandateRequestPayload) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *InitMandateRequestPayload) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *InitMandateRequestPayload) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *InitMandateRequestPayload) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *InitMandateRequestPayload) GetConsentData() *ConsentDataRequest {
	if x != nil {
		return x.ConsentData
	}
	return nil
}

type InitMandateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Params     *Params                    `protobuf:"bytes,1,opt,name=params,proto3" json:"params,omitempty"`
	Fields     *Fields                    `protobuf:"bytes,2,opt,name=fields,proto3" json:"fields,omitempty"`
	Json       *InitMandateRequestPayload `protobuf:"bytes,3,opt,name=json,proto3" json:"json,omitempty"`
	Attributes *Attributes                `protobuf:"bytes,4,opt,name=attributes,proto3" json:"attributes,omitempty"`
	ApiCode    string                     `protobuf:"bytes,5,opt,name=api_code,proto3" json:"api_code,omitempty"`
}

func (x *InitMandateRequest) Reset() {
	*x = InitMandateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_init_mandate_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitMandateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitMandateRequest) ProtoMessage() {}

func (x *InitMandateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_init_mandate_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitMandateRequest.ProtoReflect.Descriptor instead.
func (*InitMandateRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_init_mandate_proto_rawDescGZIP(), []int{1}
}

func (x *InitMandateRequest) GetParams() *Params {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *InitMandateRequest) GetFields() *Fields {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *InitMandateRequest) GetJson() *InitMandateRequestPayload {
	if x != nil {
		return x.Json
	}
	return nil
}

func (x *InitMandateRequest) GetAttributes() *Attributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *InitMandateRequest) GetApiCode() string {
	if x != nil {
		return x.ApiCode
	}
	return ""
}

type InitMandateResponseWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message  string                                   `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Response *InitMandateResponseWrapper_ResponseData `protobuf:"bytes,2,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *InitMandateResponseWrapper) Reset() {
	*x = InitMandateResponseWrapper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_init_mandate_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitMandateResponseWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitMandateResponseWrapper) ProtoMessage() {}

func (x *InitMandateResponseWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_init_mandate_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitMandateResponseWrapper.ProtoReflect.Descriptor instead.
func (*InitMandateResponseWrapper) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_init_mandate_proto_rawDescGZIP(), []int{2}
}

func (x *InitMandateResponseWrapper) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *InitMandateResponseWrapper) GetResponse() *InitMandateResponseWrapper_ResponseData {
	if x != nil {
		return x.Response
	}
	return nil
}

type InitMandateResponseWrapper_ResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraceId      string                                          `protobuf:"bytes,1,opt,name=trace_id,proto3" json:"trace_id,omitempty"`
	MessageCode  string                                          `protobuf:"bytes,2,opt,name=message_code,proto3" json:"message_code,omitempty"`
	Message      string                                          `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	ResponseData *InitMandateResponseWrapper_InitMandateResponse `protobuf:"bytes,4,opt,name=response_data,proto3" json:"response_data,omitempty"`
}

func (x *InitMandateResponseWrapper_ResponseData) Reset() {
	*x = InitMandateResponseWrapper_ResponseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_init_mandate_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitMandateResponseWrapper_ResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitMandateResponseWrapper_ResponseData) ProtoMessage() {}

func (x *InitMandateResponseWrapper_ResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_init_mandate_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitMandateResponseWrapper_ResponseData.ProtoReflect.Descriptor instead.
func (*InitMandateResponseWrapper_ResponseData) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_init_mandate_proto_rawDescGZIP(), []int{2, 0}
}

func (x *InitMandateResponseWrapper_ResponseData) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *InitMandateResponseWrapper_ResponseData) GetMessageCode() string {
	if x != nil {
		return x.MessageCode
	}
	return ""
}

func (x *InitMandateResponseWrapper_ResponseData) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *InitMandateResponseWrapper_ResponseData) GetResponseData() *InitMandateResponseWrapper_InitMandateResponse {
	if x != nil {
		return x.ResponseData
	}
	return nil
}

type InitMandateResponseWrapper_InitMandateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Links         *InitMandateResponseWrapper_Links `protobuf:"bytes,1,opt,name=links,proto3" json:"links,omitempty"`
	TrackingId    string                            `protobuf:"bytes,2,opt,name=tracking_id,proto3" json:"tracking_id,omitempty"`
	MandateAmount int64                             `protobuf:"varint,3,opt,name=mandate_amount,proto3" json:"mandate_amount,omitempty"`
	// UMRN (Unique Mandate Reference Number) is a unique identifier generated by NPCI
	// when an e-NACH mandate is successfully registered. This field will be populated
	// when the mandate is already completed.
	Umrn   string `protobuf:"bytes,4,opt,name=umrn,proto3" json:"umrn,omitempty"`
	Amount int64  `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *InitMandateResponseWrapper_InitMandateResponse) Reset() {
	*x = InitMandateResponseWrapper_InitMandateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_init_mandate_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitMandateResponseWrapper_InitMandateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitMandateResponseWrapper_InitMandateResponse) ProtoMessage() {}

func (x *InitMandateResponseWrapper_InitMandateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_init_mandate_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitMandateResponseWrapper_InitMandateResponse.ProtoReflect.Descriptor instead.
func (*InitMandateResponseWrapper_InitMandateResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_init_mandate_proto_rawDescGZIP(), []int{2, 1}
}

func (x *InitMandateResponseWrapper_InitMandateResponse) GetLinks() *InitMandateResponseWrapper_Links {
	if x != nil {
		return x.Links
	}
	return nil
}

func (x *InitMandateResponseWrapper_InitMandateResponse) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

func (x *InitMandateResponseWrapper_InitMandateResponse) GetMandateAmount() int64 {
	if x != nil {
		return x.MandateAmount
	}
	return 0
}

func (x *InitMandateResponseWrapper_InitMandateResponse) GetUmrn() string {
	if x != nil {
		return x.Umrn
	}
	return ""
}

func (x *InitMandateResponseWrapper_InitMandateResponse) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

type InitMandateResponseWrapper_Links struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url             string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	MandateValidity string `protobuf:"bytes,2,opt,name=mandate_validity,json=validity,proto3" json:"mandate_validity,omitempty"`
	UrlValidity     string `protobuf:"bytes,3,opt,name=url_validity,json=valid_till,proto3" json:"url_validity,omitempty"`
	MandateId       string `protobuf:"bytes,4,opt,name=mandate_id,proto3" json:"mandate_id,omitempty"`
}

func (x *InitMandateResponseWrapper_Links) Reset() {
	*x = InitMandateResponseWrapper_Links{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_init_mandate_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitMandateResponseWrapper_Links) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitMandateResponseWrapper_Links) ProtoMessage() {}

func (x *InitMandateResponseWrapper_Links) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_init_mandate_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitMandateResponseWrapper_Links.ProtoReflect.Descriptor instead.
func (*InitMandateResponseWrapper_Links) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_init_mandate_proto_rawDescGZIP(), []int{2, 2}
}

func (x *InitMandateResponseWrapper_Links) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *InitMandateResponseWrapper_Links) GetMandateValidity() string {
	if x != nil {
		return x.MandateValidity
	}
	return ""
}

func (x *InitMandateResponseWrapper_Links) GetUrlValidity() string {
	if x != nil {
		return x.UrlValidity
	}
	return ""
}

func (x *InitMandateResponseWrapper_Links) GetMandateId() string {
	if x != nil {
		return x.MandateId
	}
	return ""
}

var File_api_vendors_lenden_init_mandate_proto protoreflect.FileDescriptor

var file_api_vendors_lenden_init_mandate_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x2f, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcb, 0x01, 0x0a, 0x19, 0x49, 0x6e, 0x69,
	0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x46,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x22, 0x8b, 0x02, 0x0a, 0x12, 0x49, 0x6e, 0x69, 0x74, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a,
	0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a,
	0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x3d, 0x0a,
	0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x49, 0x6e, 0x69,
	0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x0a,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65,
	0x6e, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x0a, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x70, 0x69, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x22, 0xb3, 0x05, 0x0a, 0x1a, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x53, 0x0a,
	0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e,
	0x2e, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x1a, 0xce, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x12,
	0x22, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x64, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x2e, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0xd3, 0x01, 0x0a, 0x13, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x05, 0x6c,
	0x69, 0x6e, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74,
	0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x73, 0x52, 0x05, 0x6c, 0x69,
	0x6e, 0x6b, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x6d, 0x72, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6d, 0x72,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x7f, 0x0a, 0x05, 0x4c, 0x69, 0x6e,
	0x6b, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x10, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x12, 0x20, 0x0a, 0x0c, 0x75, 0x72, 0x6c, 0x5f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_lenden_init_mandate_proto_rawDescOnce sync.Once
	file_api_vendors_lenden_init_mandate_proto_rawDescData = file_api_vendors_lenden_init_mandate_proto_rawDesc
)

func file_api_vendors_lenden_init_mandate_proto_rawDescGZIP() []byte {
	file_api_vendors_lenden_init_mandate_proto_rawDescOnce.Do(func() {
		file_api_vendors_lenden_init_mandate_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_lenden_init_mandate_proto_rawDescData)
	})
	return file_api_vendors_lenden_init_mandate_proto_rawDescData
}

var file_api_vendors_lenden_init_mandate_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_vendors_lenden_init_mandate_proto_goTypes = []interface{}{
	(*InitMandateRequestPayload)(nil),                      // 0: vendors.lenden.InitMandateRequestPayload
	(*InitMandateRequest)(nil),                             // 1: vendors.lenden.InitMandateRequest
	(*InitMandateResponseWrapper)(nil),                     // 2: vendors.lenden.InitMandateResponseWrapper
	(*InitMandateResponseWrapper_ResponseData)(nil),        // 3: vendors.lenden.InitMandateResponseWrapper.ResponseData
	(*InitMandateResponseWrapper_InitMandateResponse)(nil), // 4: vendors.lenden.InitMandateResponseWrapper.InitMandateResponse
	(*InitMandateResponseWrapper_Links)(nil),               // 5: vendors.lenden.InitMandateResponseWrapper.Links
	(*ConsentDataRequest)(nil),                             // 6: vendors.lenden.ConsentDataRequest
	(*Params)(nil),                                         // 7: vendors.lenden.Params
	(*Fields)(nil),                                         // 8: vendors.lenden.Fields
	(*Attributes)(nil),                                     // 9: vendors.lenden.Attributes
}
var file_api_vendors_lenden_init_mandate_proto_depIdxs = []int32{
	6, // 0: vendors.lenden.InitMandateRequestPayload.consent_data:type_name -> vendors.lenden.ConsentDataRequest
	7, // 1: vendors.lenden.InitMandateRequest.params:type_name -> vendors.lenden.Params
	8, // 2: vendors.lenden.InitMandateRequest.fields:type_name -> vendors.lenden.Fields
	0, // 3: vendors.lenden.InitMandateRequest.json:type_name -> vendors.lenden.InitMandateRequestPayload
	9, // 4: vendors.lenden.InitMandateRequest.attributes:type_name -> vendors.lenden.Attributes
	3, // 5: vendors.lenden.InitMandateResponseWrapper.response:type_name -> vendors.lenden.InitMandateResponseWrapper.ResponseData
	4, // 6: vendors.lenden.InitMandateResponseWrapper.ResponseData.response_data:type_name -> vendors.lenden.InitMandateResponseWrapper.InitMandateResponse
	5, // 7: vendors.lenden.InitMandateResponseWrapper.InitMandateResponse.links:type_name -> vendors.lenden.InitMandateResponseWrapper.Links
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_vendors_lenden_init_mandate_proto_init() }
func file_api_vendors_lenden_init_mandate_proto_init() {
	if File_api_vendors_lenden_init_mandate_proto != nil {
		return
	}
	file_api_vendors_lenden_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_lenden_init_mandate_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitMandateRequestPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_lenden_init_mandate_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitMandateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_lenden_init_mandate_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitMandateResponseWrapper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_lenden_init_mandate_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitMandateResponseWrapper_ResponseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_lenden_init_mandate_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitMandateResponseWrapper_InitMandateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_lenden_init_mandate_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitMandateResponseWrapper_Links); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_lenden_init_mandate_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_lenden_init_mandate_proto_goTypes,
		DependencyIndexes: file_api_vendors_lenden_init_mandate_proto_depIdxs,
		MessageInfos:      file_api_vendors_lenden_init_mandate_proto_msgTypes,
	}.Build()
	File_api_vendors_lenden_init_mandate_proto = out.File
	file_api_vendors_lenden_init_mandate_proto_rawDesc = nil
	file_api_vendors_lenden_init_mandate_proto_goTypes = nil
	file_api_vendors_lenden_init_mandate_proto_depIdxs = nil
}
