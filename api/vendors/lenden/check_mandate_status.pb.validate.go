// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/lenden/check_mandate_status.proto

package lenden

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CheckMandateStatusRequestPayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CheckMandateStatusRequestPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckMandateStatusRequestPayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckMandateStatusRequestPayloadMultiError, or nil if none found.
func (m *CheckMandateStatusRequestPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckMandateStatusRequestPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProductId

	// no validation rules for UserId

	// no validation rules for Type

	// no validation rules for TrackingId

	if len(errors) > 0 {
		return CheckMandateStatusRequestPayloadMultiError(errors)
	}

	return nil
}

// CheckMandateStatusRequestPayloadMultiError is an error wrapping multiple
// validation errors returned by
// CheckMandateStatusRequestPayload.ValidateAll() if the designated
// constraints aren't met.
type CheckMandateStatusRequestPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckMandateStatusRequestPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckMandateStatusRequestPayloadMultiError) AllErrors() []error { return m }

// CheckMandateStatusRequestPayloadValidationError is the validation error
// returned by CheckMandateStatusRequestPayload.Validate if the designated
// constraints aren't met.
type CheckMandateStatusRequestPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckMandateStatusRequestPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckMandateStatusRequestPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckMandateStatusRequestPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckMandateStatusRequestPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckMandateStatusRequestPayloadValidationError) ErrorName() string {
	return "CheckMandateStatusRequestPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e CheckMandateStatusRequestPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckMandateStatusRequestPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckMandateStatusRequestPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckMandateStatusRequestPayloadValidationError{}

// Validate checks the field values on CheckMandateStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckMandateStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckMandateStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckMandateStatusRequestMultiError, or nil if none found.
func (m *CheckMandateStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckMandateStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckMandateStatusRequestValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckMandateStatusRequestValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckMandateStatusRequestValidationError{
				field:  "Params",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckMandateStatusRequestValidationError{
					field:  "Fields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckMandateStatusRequestValidationError{
					field:  "Fields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckMandateStatusRequestValidationError{
				field:  "Fields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetJson()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckMandateStatusRequestValidationError{
					field:  "Json",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckMandateStatusRequestValidationError{
					field:  "Json",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJson()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckMandateStatusRequestValidationError{
				field:  "Json",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckMandateStatusRequestValidationError{
					field:  "Attributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckMandateStatusRequestValidationError{
					field:  "Attributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckMandateStatusRequestValidationError{
				field:  "Attributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApiCode

	if len(errors) > 0 {
		return CheckMandateStatusRequestMultiError(errors)
	}

	return nil
}

// CheckMandateStatusRequestMultiError is an error wrapping multiple validation
// errors returned by CheckMandateStatusRequest.ValidateAll() if the
// designated constraints aren't met.
type CheckMandateStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckMandateStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckMandateStatusRequestMultiError) AllErrors() []error { return m }

// CheckMandateStatusRequestValidationError is the validation error returned by
// CheckMandateStatusRequest.Validate if the designated constraints aren't met.
type CheckMandateStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckMandateStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckMandateStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckMandateStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckMandateStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckMandateStatusRequestValidationError) ErrorName() string {
	return "CheckMandateStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckMandateStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckMandateStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckMandateStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckMandateStatusRequestValidationError{}

// Validate checks the field values on CheckMandateStatusResponseWrapper with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CheckMandateStatusResponseWrapper) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckMandateStatusResponseWrapper
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CheckMandateStatusResponseWrapperMultiError, or nil if none found.
func (m *CheckMandateStatusResponseWrapper) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckMandateStatusResponseWrapper) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckMandateStatusResponseWrapperValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckMandateStatusResponseWrapperValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckMandateStatusResponseWrapperValidationError{
				field:  "Response",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckMandateStatusResponseWrapperMultiError(errors)
	}

	return nil
}

// CheckMandateStatusResponseWrapperMultiError is an error wrapping multiple
// validation errors returned by
// CheckMandateStatusResponseWrapper.ValidateAll() if the designated
// constraints aren't met.
type CheckMandateStatusResponseWrapperMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckMandateStatusResponseWrapperMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckMandateStatusResponseWrapperMultiError) AllErrors() []error { return m }

// CheckMandateStatusResponseWrapperValidationError is the validation error
// returned by CheckMandateStatusResponseWrapper.Validate if the designated
// constraints aren't met.
type CheckMandateStatusResponseWrapperValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckMandateStatusResponseWrapperValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckMandateStatusResponseWrapperValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckMandateStatusResponseWrapperValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckMandateStatusResponseWrapperValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckMandateStatusResponseWrapperValidationError) ErrorName() string {
	return "CheckMandateStatusResponseWrapperValidationError"
}

// Error satisfies the builtin error interface
func (e CheckMandateStatusResponseWrapperValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckMandateStatusResponseWrapper.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckMandateStatusResponseWrapperValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckMandateStatusResponseWrapperValidationError{}

// Validate checks the field values on
// CheckMandateStatusResponseWrapper_ResponseData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckMandateStatusResponseWrapper_ResponseData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckMandateStatusResponseWrapper_ResponseData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CheckMandateStatusResponseWrapper_ResponseDataMultiError, or nil if none found.
func (m *CheckMandateStatusResponseWrapper_ResponseData) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckMandateStatusResponseWrapper_ResponseData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TraceId

	// no validation rules for MessageCode

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetResponseData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckMandateStatusResponseWrapper_ResponseDataValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckMandateStatusResponseWrapper_ResponseDataValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckMandateStatusResponseWrapper_ResponseDataValidationError{
				field:  "ResponseData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckMandateStatusResponseWrapper_ResponseDataMultiError(errors)
	}

	return nil
}

// CheckMandateStatusResponseWrapper_ResponseDataMultiError is an error
// wrapping multiple validation errors returned by
// CheckMandateStatusResponseWrapper_ResponseData.ValidateAll() if the
// designated constraints aren't met.
type CheckMandateStatusResponseWrapper_ResponseDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckMandateStatusResponseWrapper_ResponseDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckMandateStatusResponseWrapper_ResponseDataMultiError) AllErrors() []error { return m }

// CheckMandateStatusResponseWrapper_ResponseDataValidationError is the
// validation error returned by
// CheckMandateStatusResponseWrapper_ResponseData.Validate if the designated
// constraints aren't met.
type CheckMandateStatusResponseWrapper_ResponseDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckMandateStatusResponseWrapper_ResponseDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckMandateStatusResponseWrapper_ResponseDataValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CheckMandateStatusResponseWrapper_ResponseDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckMandateStatusResponseWrapper_ResponseDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckMandateStatusResponseWrapper_ResponseDataValidationError) ErrorName() string {
	return "CheckMandateStatusResponseWrapper_ResponseDataValidationError"
}

// Error satisfies the builtin error interface
func (e CheckMandateStatusResponseWrapper_ResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckMandateStatusResponseWrapper_ResponseData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckMandateStatusResponseWrapper_ResponseDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckMandateStatusResponseWrapper_ResponseDataValidationError{}

// Validate checks the field values on
// CheckMandateStatusResponseWrapper_CheckMandateStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckMandateStatusResponseWrapper_CheckMandateStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckMandateStatusResponseWrapper_CheckMandateStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckMandateStatusResponseWrapper_CheckMandateStatusResponseMultiError, or
// nil if none found.
func (m *CheckMandateStatusResponseWrapper_CheckMandateStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckMandateStatusResponseWrapper_CheckMandateStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MandateStatus

	// no validation rules for TrackingId

	// no validation rules for CompletionDtm

	// no validation rules for Action

	// no validation rules for Reason

	if len(errors) > 0 {
		return CheckMandateStatusResponseWrapper_CheckMandateStatusResponseMultiError(errors)
	}

	return nil
}

// CheckMandateStatusResponseWrapper_CheckMandateStatusResponseMultiError is an
// error wrapping multiple validation errors returned by
// CheckMandateStatusResponseWrapper_CheckMandateStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type CheckMandateStatusResponseWrapper_CheckMandateStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckMandateStatusResponseWrapper_CheckMandateStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckMandateStatusResponseWrapper_CheckMandateStatusResponseMultiError) AllErrors() []error {
	return m
}

// CheckMandateStatusResponseWrapper_CheckMandateStatusResponseValidationError
// is the validation error returned by
// CheckMandateStatusResponseWrapper_CheckMandateStatusResponse.Validate if
// the designated constraints aren't met.
type CheckMandateStatusResponseWrapper_CheckMandateStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckMandateStatusResponseWrapper_CheckMandateStatusResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CheckMandateStatusResponseWrapper_CheckMandateStatusResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CheckMandateStatusResponseWrapper_CheckMandateStatusResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CheckMandateStatusResponseWrapper_CheckMandateStatusResponseValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e CheckMandateStatusResponseWrapper_CheckMandateStatusResponseValidationError) ErrorName() string {
	return "CheckMandateStatusResponseWrapper_CheckMandateStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckMandateStatusResponseWrapper_CheckMandateStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckMandateStatusResponseWrapper_CheckMandateStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckMandateStatusResponseWrapper_CheckMandateStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckMandateStatusResponseWrapper_CheckMandateStatusResponseValidationError{}
