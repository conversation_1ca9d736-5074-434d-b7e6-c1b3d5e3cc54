// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/lenden/check_mandate_status.proto

package lenden

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CheckMandateStatusRequestPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId  string `protobuf:"bytes,1,opt,name=product_id,proto3" json:"product_id,omitempty"`
	UserId     string `protobuf:"bytes,2,opt,name=user_id,proto3" json:"user_id,omitempty"`
	Type       string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	TrackingId string `protobuf:"bytes,4,opt,name=tracking_id,proto3" json:"tracking_id,omitempty"`
}

func (x *CheckMandateStatusRequestPayload) Reset() {
	*x = CheckMandateStatusRequestPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_check_mandate_status_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckMandateStatusRequestPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMandateStatusRequestPayload) ProtoMessage() {}

func (x *CheckMandateStatusRequestPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_check_mandate_status_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMandateStatusRequestPayload.ProtoReflect.Descriptor instead.
func (*CheckMandateStatusRequestPayload) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_check_mandate_status_proto_rawDescGZIP(), []int{0}
}

func (x *CheckMandateStatusRequestPayload) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *CheckMandateStatusRequestPayload) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CheckMandateStatusRequestPayload) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CheckMandateStatusRequestPayload) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

type CheckMandateStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Params     *Params                           `protobuf:"bytes,1,opt,name=params,proto3" json:"params,omitempty"`
	Fields     *Fields                           `protobuf:"bytes,2,opt,name=fields,proto3" json:"fields,omitempty"`
	Json       *CheckMandateStatusRequestPayload `protobuf:"bytes,3,opt,name=json,proto3" json:"json,omitempty"`
	Attributes *Attributes                       `protobuf:"bytes,4,opt,name=attributes,proto3" json:"attributes,omitempty"`
	ApiCode    string                            `protobuf:"bytes,5,opt,name=api_code,proto3" json:"api_code,omitempty"`
}

func (x *CheckMandateStatusRequest) Reset() {
	*x = CheckMandateStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_check_mandate_status_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckMandateStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMandateStatusRequest) ProtoMessage() {}

func (x *CheckMandateStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_check_mandate_status_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMandateStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckMandateStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_check_mandate_status_proto_rawDescGZIP(), []int{1}
}

func (x *CheckMandateStatusRequest) GetParams() *Params {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *CheckMandateStatusRequest) GetFields() *Fields {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *CheckMandateStatusRequest) GetJson() *CheckMandateStatusRequestPayload {
	if x != nil {
		return x.Json
	}
	return nil
}

func (x *CheckMandateStatusRequest) GetAttributes() *Attributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *CheckMandateStatusRequest) GetApiCode() string {
	if x != nil {
		return x.ApiCode
	}
	return ""
}

type CheckMandateStatusResponseWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message  string                                          `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Response *CheckMandateStatusResponseWrapper_ResponseData `protobuf:"bytes,2,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *CheckMandateStatusResponseWrapper) Reset() {
	*x = CheckMandateStatusResponseWrapper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_check_mandate_status_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckMandateStatusResponseWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMandateStatusResponseWrapper) ProtoMessage() {}

func (x *CheckMandateStatusResponseWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_check_mandate_status_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMandateStatusResponseWrapper.ProtoReflect.Descriptor instead.
func (*CheckMandateStatusResponseWrapper) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_check_mandate_status_proto_rawDescGZIP(), []int{2}
}

func (x *CheckMandateStatusResponseWrapper) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckMandateStatusResponseWrapper) GetResponse() *CheckMandateStatusResponseWrapper_ResponseData {
	if x != nil {
		return x.Response
	}
	return nil
}

type CheckMandateStatusResponseWrapper_ResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraceId      string                                                        `protobuf:"bytes,1,opt,name=trace_id,proto3" json:"trace_id,omitempty"`
	MessageCode  string                                                        `protobuf:"bytes,2,opt,name=message_code,proto3" json:"message_code,omitempty"`
	Message      string                                                        `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	ResponseData *CheckMandateStatusResponseWrapper_CheckMandateStatusResponse `protobuf:"bytes,4,opt,name=response_data,proto3" json:"response_data,omitempty"`
}

func (x *CheckMandateStatusResponseWrapper_ResponseData) Reset() {
	*x = CheckMandateStatusResponseWrapper_ResponseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_check_mandate_status_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckMandateStatusResponseWrapper_ResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMandateStatusResponseWrapper_ResponseData) ProtoMessage() {}

func (x *CheckMandateStatusResponseWrapper_ResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_check_mandate_status_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMandateStatusResponseWrapper_ResponseData.ProtoReflect.Descriptor instead.
func (*CheckMandateStatusResponseWrapper_ResponseData) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_check_mandate_status_proto_rawDescGZIP(), []int{2, 0}
}

func (x *CheckMandateStatusResponseWrapper_ResponseData) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *CheckMandateStatusResponseWrapper_ResponseData) GetMessageCode() string {
	if x != nil {
		return x.MessageCode
	}
	return ""
}

func (x *CheckMandateStatusResponseWrapper_ResponseData) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckMandateStatusResponseWrapper_ResponseData) GetResponseData() *CheckMandateStatusResponseWrapper_CheckMandateStatusResponse {
	if x != nil {
		return x.ResponseData
	}
	return nil
}

type CheckMandateStatusResponseWrapper_CheckMandateStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MandateStatus string `protobuf:"bytes,1,opt,name=mandate_status,json=status,proto3" json:"mandate_status,omitempty"`
	TrackingId    string `protobuf:"bytes,2,opt,name=tracking_id,proto3" json:"tracking_id,omitempty"`
	CompletionDtm string `protobuf:"bytes,3,opt,name=completion_dtm,proto3" json:"completion_dtm,omitempty"`
	Action        string `protobuf:"bytes,4,opt,name=action,proto3" json:"action,omitempty"`
	Reason        string `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *CheckMandateStatusResponseWrapper_CheckMandateStatusResponse) Reset() {
	*x = CheckMandateStatusResponseWrapper_CheckMandateStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_check_mandate_status_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckMandateStatusResponseWrapper_CheckMandateStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMandateStatusResponseWrapper_CheckMandateStatusResponse) ProtoMessage() {}

func (x *CheckMandateStatusResponseWrapper_CheckMandateStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_check_mandate_status_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMandateStatusResponseWrapper_CheckMandateStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckMandateStatusResponseWrapper_CheckMandateStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_check_mandate_status_proto_rawDescGZIP(), []int{2, 1}
}

func (x *CheckMandateStatusResponseWrapper_CheckMandateStatusResponse) GetMandateStatus() string {
	if x != nil {
		return x.MandateStatus
	}
	return ""
}

func (x *CheckMandateStatusResponseWrapper_CheckMandateStatusResponse) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

func (x *CheckMandateStatusResponseWrapper_CheckMandateStatusResponse) GetCompletionDtm() string {
	if x != nil {
		return x.CompletionDtm
	}
	return ""
}

func (x *CheckMandateStatusResponseWrapper_CheckMandateStatusResponse) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *CheckMandateStatusResponseWrapper_CheckMandateStatusResponse) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

var File_api_vendors_lenden_check_mandate_status_proto protoreflect.FileDescriptor

var file_api_vendors_lenden_check_mandate_status_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x1a,
	0x1f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x65, 0x6e,
	0x64, 0x65, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x92, 0x01, 0x0a, 0x20, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x69, 0x64, 0x22, 0x99, 0x02, 0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x06, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x52, 0x06, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x73, 0x12, 0x44, 0x0a, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x0a, 0x61, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x22, 0xb1, 0x04, 0x0a, 0x21, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x5a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a, 0xdc, 0x01,
	0x0a, 0x0c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1a,
	0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x72, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x4c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0d, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x1a, 0xb6, 0x01, 0x0a,
	0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0e, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x12, 0x26, 0x0a,
	0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x74, 0x6d, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x74, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_lenden_check_mandate_status_proto_rawDescOnce sync.Once
	file_api_vendors_lenden_check_mandate_status_proto_rawDescData = file_api_vendors_lenden_check_mandate_status_proto_rawDesc
)

func file_api_vendors_lenden_check_mandate_status_proto_rawDescGZIP() []byte {
	file_api_vendors_lenden_check_mandate_status_proto_rawDescOnce.Do(func() {
		file_api_vendors_lenden_check_mandate_status_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_lenden_check_mandate_status_proto_rawDescData)
	})
	return file_api_vendors_lenden_check_mandate_status_proto_rawDescData
}

var file_api_vendors_lenden_check_mandate_status_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_vendors_lenden_check_mandate_status_proto_goTypes = []interface{}{
	(*CheckMandateStatusRequestPayload)(nil),                             // 0: vendors.lenden.CheckMandateStatusRequestPayload
	(*CheckMandateStatusRequest)(nil),                                    // 1: vendors.lenden.CheckMandateStatusRequest
	(*CheckMandateStatusResponseWrapper)(nil),                            // 2: vendors.lenden.CheckMandateStatusResponseWrapper
	(*CheckMandateStatusResponseWrapper_ResponseData)(nil),               // 3: vendors.lenden.CheckMandateStatusResponseWrapper.ResponseData
	(*CheckMandateStatusResponseWrapper_CheckMandateStatusResponse)(nil), // 4: vendors.lenden.CheckMandateStatusResponseWrapper.CheckMandateStatusResponse
	(*Params)(nil),     // 5: vendors.lenden.Params
	(*Fields)(nil),     // 6: vendors.lenden.Fields
	(*Attributes)(nil), // 7: vendors.lenden.Attributes
}
var file_api_vendors_lenden_check_mandate_status_proto_depIdxs = []int32{
	5, // 0: vendors.lenden.CheckMandateStatusRequest.params:type_name -> vendors.lenden.Params
	6, // 1: vendors.lenden.CheckMandateStatusRequest.fields:type_name -> vendors.lenden.Fields
	0, // 2: vendors.lenden.CheckMandateStatusRequest.json:type_name -> vendors.lenden.CheckMandateStatusRequestPayload
	7, // 3: vendors.lenden.CheckMandateStatusRequest.attributes:type_name -> vendors.lenden.Attributes
	3, // 4: vendors.lenden.CheckMandateStatusResponseWrapper.response:type_name -> vendors.lenden.CheckMandateStatusResponseWrapper.ResponseData
	4, // 5: vendors.lenden.CheckMandateStatusResponseWrapper.ResponseData.response_data:type_name -> vendors.lenden.CheckMandateStatusResponseWrapper.CheckMandateStatusResponse
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_vendors_lenden_check_mandate_status_proto_init() }
func file_api_vendors_lenden_check_mandate_status_proto_init() {
	if File_api_vendors_lenden_check_mandate_status_proto != nil {
		return
	}
	file_api_vendors_lenden_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_lenden_check_mandate_status_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckMandateStatusRequestPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_lenden_check_mandate_status_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckMandateStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_lenden_check_mandate_status_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckMandateStatusResponseWrapper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_lenden_check_mandate_status_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckMandateStatusResponseWrapper_ResponseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_lenden_check_mandate_status_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckMandateStatusResponseWrapper_CheckMandateStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_lenden_check_mandate_status_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_lenden_check_mandate_status_proto_goTypes,
		DependencyIndexes: file_api_vendors_lenden_check_mandate_status_proto_depIdxs,
		MessageInfos:      file_api_vendors_lenden_check_mandate_status_proto_msgTypes,
	}.Build()
	File_api_vendors_lenden_check_mandate_status_proto = out.File
	file_api_vendors_lenden_check_mandate_status_proto_rawDesc = nil
	file_api_vendors_lenden_check_mandate_status_proto_goTypes = nil
	file_api_vendors_lenden_check_mandate_status_proto_depIdxs = nil
}
