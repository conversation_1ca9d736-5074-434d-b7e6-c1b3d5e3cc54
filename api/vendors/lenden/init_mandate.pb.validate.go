// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/lenden/init_mandate.proto

package lenden

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on InitMandateRequestPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitMandateRequestPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitMandateRequestPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitMandateRequestPayloadMultiError, or nil if none found.
func (m *InitMandateRequestPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *InitMandateRequestPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for LoanId

	// no validation rules for ProductId

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetConsentData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitMandateRequestPayloadValidationError{
					field:  "ConsentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitMandateRequestPayloadValidationError{
					field:  "ConsentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitMandateRequestPayloadValidationError{
				field:  "ConsentData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitMandateRequestPayloadMultiError(errors)
	}

	return nil
}

// InitMandateRequestPayloadMultiError is an error wrapping multiple validation
// errors returned by InitMandateRequestPayload.ValidateAll() if the
// designated constraints aren't met.
type InitMandateRequestPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitMandateRequestPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitMandateRequestPayloadMultiError) AllErrors() []error { return m }

// InitMandateRequestPayloadValidationError is the validation error returned by
// InitMandateRequestPayload.Validate if the designated constraints aren't met.
type InitMandateRequestPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitMandateRequestPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitMandateRequestPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitMandateRequestPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitMandateRequestPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitMandateRequestPayloadValidationError) ErrorName() string {
	return "InitMandateRequestPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e InitMandateRequestPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitMandateRequestPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitMandateRequestPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitMandateRequestPayloadValidationError{}

// Validate checks the field values on InitMandateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitMandateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitMandateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitMandateRequestMultiError, or nil if none found.
func (m *InitMandateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitMandateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitMandateRequestValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitMandateRequestValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitMandateRequestValidationError{
				field:  "Params",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitMandateRequestValidationError{
					field:  "Fields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitMandateRequestValidationError{
					field:  "Fields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitMandateRequestValidationError{
				field:  "Fields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetJson()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitMandateRequestValidationError{
					field:  "Json",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitMandateRequestValidationError{
					field:  "Json",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJson()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitMandateRequestValidationError{
				field:  "Json",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitMandateRequestValidationError{
					field:  "Attributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitMandateRequestValidationError{
					field:  "Attributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitMandateRequestValidationError{
				field:  "Attributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApiCode

	if len(errors) > 0 {
		return InitMandateRequestMultiError(errors)
	}

	return nil
}

// InitMandateRequestMultiError is an error wrapping multiple validation errors
// returned by InitMandateRequest.ValidateAll() if the designated constraints
// aren't met.
type InitMandateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitMandateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitMandateRequestMultiError) AllErrors() []error { return m }

// InitMandateRequestValidationError is the validation error returned by
// InitMandateRequest.Validate if the designated constraints aren't met.
type InitMandateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitMandateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitMandateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitMandateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitMandateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitMandateRequestValidationError) ErrorName() string {
	return "InitMandateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitMandateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitMandateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitMandateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitMandateRequestValidationError{}

// Validate checks the field values on InitMandateResponseWrapper with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitMandateResponseWrapper) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitMandateResponseWrapper with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitMandateResponseWrapperMultiError, or nil if none found.
func (m *InitMandateResponseWrapper) ValidateAll() error {
	return m.validate(true)
}

func (m *InitMandateResponseWrapper) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitMandateResponseWrapperValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitMandateResponseWrapperValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitMandateResponseWrapperValidationError{
				field:  "Response",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitMandateResponseWrapperMultiError(errors)
	}

	return nil
}

// InitMandateResponseWrapperMultiError is an error wrapping multiple
// validation errors returned by InitMandateResponseWrapper.ValidateAll() if
// the designated constraints aren't met.
type InitMandateResponseWrapperMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitMandateResponseWrapperMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitMandateResponseWrapperMultiError) AllErrors() []error { return m }

// InitMandateResponseWrapperValidationError is the validation error returned
// by InitMandateResponseWrapper.Validate if the designated constraints aren't met.
type InitMandateResponseWrapperValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitMandateResponseWrapperValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitMandateResponseWrapperValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitMandateResponseWrapperValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitMandateResponseWrapperValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitMandateResponseWrapperValidationError) ErrorName() string {
	return "InitMandateResponseWrapperValidationError"
}

// Error satisfies the builtin error interface
func (e InitMandateResponseWrapperValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitMandateResponseWrapper.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitMandateResponseWrapperValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitMandateResponseWrapperValidationError{}

// Validate checks the field values on InitMandateResponseWrapper_ResponseData
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InitMandateResponseWrapper_ResponseData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitMandateResponseWrapper_ResponseData with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// InitMandateResponseWrapper_ResponseDataMultiError, or nil if none found.
func (m *InitMandateResponseWrapper_ResponseData) ValidateAll() error {
	return m.validate(true)
}

func (m *InitMandateResponseWrapper_ResponseData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TraceId

	// no validation rules for MessageCode

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetResponseData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitMandateResponseWrapper_ResponseDataValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitMandateResponseWrapper_ResponseDataValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitMandateResponseWrapper_ResponseDataValidationError{
				field:  "ResponseData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitMandateResponseWrapper_ResponseDataMultiError(errors)
	}

	return nil
}

// InitMandateResponseWrapper_ResponseDataMultiError is an error wrapping
// multiple validation errors returned by
// InitMandateResponseWrapper_ResponseData.ValidateAll() if the designated
// constraints aren't met.
type InitMandateResponseWrapper_ResponseDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitMandateResponseWrapper_ResponseDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitMandateResponseWrapper_ResponseDataMultiError) AllErrors() []error { return m }

// InitMandateResponseWrapper_ResponseDataValidationError is the validation
// error returned by InitMandateResponseWrapper_ResponseData.Validate if the
// designated constraints aren't met.
type InitMandateResponseWrapper_ResponseDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitMandateResponseWrapper_ResponseDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitMandateResponseWrapper_ResponseDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitMandateResponseWrapper_ResponseDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitMandateResponseWrapper_ResponseDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitMandateResponseWrapper_ResponseDataValidationError) ErrorName() string {
	return "InitMandateResponseWrapper_ResponseDataValidationError"
}

// Error satisfies the builtin error interface
func (e InitMandateResponseWrapper_ResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitMandateResponseWrapper_ResponseData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitMandateResponseWrapper_ResponseDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitMandateResponseWrapper_ResponseDataValidationError{}

// Validate checks the field values on
// InitMandateResponseWrapper_InitMandateResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InitMandateResponseWrapper_InitMandateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitMandateResponseWrapper_InitMandateResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// InitMandateResponseWrapper_InitMandateResponseMultiError, or nil if none found.
func (m *InitMandateResponseWrapper_InitMandateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitMandateResponseWrapper_InitMandateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLinks()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitMandateResponseWrapper_InitMandateResponseValidationError{
					field:  "Links",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitMandateResponseWrapper_InitMandateResponseValidationError{
					field:  "Links",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLinks()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitMandateResponseWrapper_InitMandateResponseValidationError{
				field:  "Links",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TrackingId

	// no validation rules for MandateAmount

	// no validation rules for Umrn

	// no validation rules for Amount

	if len(errors) > 0 {
		return InitMandateResponseWrapper_InitMandateResponseMultiError(errors)
	}

	return nil
}

// InitMandateResponseWrapper_InitMandateResponseMultiError is an error
// wrapping multiple validation errors returned by
// InitMandateResponseWrapper_InitMandateResponse.ValidateAll() if the
// designated constraints aren't met.
type InitMandateResponseWrapper_InitMandateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitMandateResponseWrapper_InitMandateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitMandateResponseWrapper_InitMandateResponseMultiError) AllErrors() []error { return m }

// InitMandateResponseWrapper_InitMandateResponseValidationError is the
// validation error returned by
// InitMandateResponseWrapper_InitMandateResponse.Validate if the designated
// constraints aren't met.
type InitMandateResponseWrapper_InitMandateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitMandateResponseWrapper_InitMandateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitMandateResponseWrapper_InitMandateResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e InitMandateResponseWrapper_InitMandateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitMandateResponseWrapper_InitMandateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitMandateResponseWrapper_InitMandateResponseValidationError) ErrorName() string {
	return "InitMandateResponseWrapper_InitMandateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitMandateResponseWrapper_InitMandateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitMandateResponseWrapper_InitMandateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitMandateResponseWrapper_InitMandateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitMandateResponseWrapper_InitMandateResponseValidationError{}

// Validate checks the field values on InitMandateResponseWrapper_Links with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitMandateResponseWrapper_Links) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitMandateResponseWrapper_Links with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// InitMandateResponseWrapper_LinksMultiError, or nil if none found.
func (m *InitMandateResponseWrapper_Links) ValidateAll() error {
	return m.validate(true)
}

func (m *InitMandateResponseWrapper_Links) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	// no validation rules for MandateValidity

	// no validation rules for UrlValidity

	// no validation rules for MandateId

	if len(errors) > 0 {
		return InitMandateResponseWrapper_LinksMultiError(errors)
	}

	return nil
}

// InitMandateResponseWrapper_LinksMultiError is an error wrapping multiple
// validation errors returned by
// InitMandateResponseWrapper_Links.ValidateAll() if the designated
// constraints aren't met.
type InitMandateResponseWrapper_LinksMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitMandateResponseWrapper_LinksMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitMandateResponseWrapper_LinksMultiError) AllErrors() []error { return m }

// InitMandateResponseWrapper_LinksValidationError is the validation error
// returned by InitMandateResponseWrapper_Links.Validate if the designated
// constraints aren't met.
type InitMandateResponseWrapper_LinksValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitMandateResponseWrapper_LinksValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitMandateResponseWrapper_LinksValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitMandateResponseWrapper_LinksValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitMandateResponseWrapper_LinksValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitMandateResponseWrapper_LinksValidationError) ErrorName() string {
	return "InitMandateResponseWrapper_LinksValidationError"
}

// Error satisfies the builtin error interface
func (e InitMandateResponseWrapper_LinksValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitMandateResponseWrapper_Links.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitMandateResponseWrapper_LinksValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitMandateResponseWrapper_LinksValidationError{}
