// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/leegality/esign.proto

package leegality

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateESigningRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateESigningRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateESigningRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateESigningRequestMultiError, or nil if none found.
func (m *CreateESigningRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateESigningRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProfileId

	if all {
		switch v := interface{}(m.GetFile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateESigningRequestValidationError{
					field:  "File",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateESigningRequestValidationError{
					field:  "File",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateESigningRequestValidationError{
				field:  "File",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInvitees() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateESigningRequestValidationError{
						field:  fmt.Sprintf("Invitees[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateESigningRequestValidationError{
						field:  fmt.Sprintf("Invitees[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateESigningRequestValidationError{
					field:  fmt.Sprintf("Invitees[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Irn

	for idx, item := range m.GetCc() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateESigningRequestValidationError{
						field:  fmt.Sprintf("Cc[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateESigningRequestValidationError{
						field:  fmt.Sprintf("Cc[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateESigningRequestValidationError{
					field:  fmt.Sprintf("Cc[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateESigningRequestMultiError(errors)
	}

	return nil
}

// CreateESigningRequestMultiError is an error wrapping multiple validation
// errors returned by CreateESigningRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateESigningRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateESigningRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateESigningRequestMultiError) AllErrors() []error { return m }

// CreateESigningRequestValidationError is the validation error returned by
// CreateESigningRequest.Validate if the designated constraints aren't met.
type CreateESigningRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateESigningRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateESigningRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateESigningRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateESigningRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateESigningRequestValidationError) ErrorName() string {
	return "CreateESigningRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateESigningRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateESigningRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateESigningRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateESigningRequestValidationError{}

// Validate checks the field values on CreateESigningResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateESigningResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateESigningResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateESigningResponseMultiError, or nil if none found.
func (m *CreateESigningResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateESigningResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	for idx, item := range m.GetMessages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateESigningResponseValidationError{
						field:  fmt.Sprintf("Messages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateESigningResponseValidationError{
						field:  fmt.Sprintf("Messages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateESigningResponseValidationError{
					field:  fmt.Sprintf("Messages[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateESigningResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateESigningResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateESigningResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateESigningResponseMultiError(errors)
	}

	return nil
}

// CreateESigningResponseMultiError is an error wrapping multiple validation
// errors returned by CreateESigningResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateESigningResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateESigningResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateESigningResponseMultiError) AllErrors() []error { return m }

// CreateESigningResponseValidationError is the validation error returned by
// CreateESigningResponse.Validate if the designated constraints aren't met.
type CreateESigningResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateESigningResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateESigningResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateESigningResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateESigningResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateESigningResponseValidationError) ErrorName() string {
	return "CreateESigningResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateESigningResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateESigningResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateESigningResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateESigningResponseValidationError{}

// Validate checks the field values on CheckESigningStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckESigningStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckESigningStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckESigningStatusResponseMultiError, or nil if none found.
func (m *CheckESigningStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckESigningStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	for idx, item := range m.GetMessages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CheckESigningStatusResponseValidationError{
						field:  fmt.Sprintf("Messages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CheckESigningStatusResponseValidationError{
						field:  fmt.Sprintf("Messages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CheckESigningStatusResponseValidationError{
					field:  fmt.Sprintf("Messages[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckESigningStatusResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckESigningStatusResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckESigningStatusResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckESigningStatusResponseMultiError(errors)
	}

	return nil
}

// CheckESigningStatusResponseMultiError is an error wrapping multiple
// validation errors returned by CheckESigningStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type CheckESigningStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckESigningStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckESigningStatusResponseMultiError) AllErrors() []error { return m }

// CheckESigningStatusResponseValidationError is the validation error returned
// by CheckESigningStatusResponse.Validate if the designated constraints
// aren't met.
type CheckESigningStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckESigningStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckESigningStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckESigningStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckESigningStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckESigningStatusResponseValidationError) ErrorName() string {
	return "CheckESigningStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckESigningStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckESigningStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckESigningStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckESigningStatusResponseValidationError{}

// Validate checks the field values on Messages with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Messages) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Messages with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MessagesMultiError, or nil
// if none found.
func (m *Messages) ValidateAll() error {
	return m.validate(true)
}

func (m *Messages) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Message

	if len(errors) > 0 {
		return MessagesMultiError(errors)
	}

	return nil
}

// MessagesMultiError is an error wrapping multiple validation errors returned
// by Messages.ValidateAll() if the designated constraints aren't met.
type MessagesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MessagesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MessagesMultiError) AllErrors() []error { return m }

// MessagesValidationError is the validation error returned by
// Messages.Validate if the designated constraints aren't met.
type MessagesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MessagesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MessagesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MessagesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MessagesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MessagesValidationError) ErrorName() string { return "MessagesValidationError" }

// Error satisfies the builtin error interface
func (e MessagesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMessages.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MessagesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MessagesValidationError{}

// Validate checks the field values on CreateESigningRequest_File with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateESigningRequest_File) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateESigningRequest_File with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateESigningRequest_FileMultiError, or nil if none found.
func (m *CreateESigningRequest_File) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateESigningRequest_File) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for File

	for idx, item := range m.GetFields() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateESigningRequest_FileValidationError{
						field:  fmt.Sprintf("Fields[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateESigningRequest_FileValidationError{
						field:  fmt.Sprintf("Fields[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateESigningRequest_FileValidationError{
					field:  fmt.Sprintf("Fields[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateESigningRequest_FileMultiError(errors)
	}

	return nil
}

// CreateESigningRequest_FileMultiError is an error wrapping multiple
// validation errors returned by CreateESigningRequest_File.ValidateAll() if
// the designated constraints aren't met.
type CreateESigningRequest_FileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateESigningRequest_FileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateESigningRequest_FileMultiError) AllErrors() []error { return m }

// CreateESigningRequest_FileValidationError is the validation error returned
// by CreateESigningRequest_File.Validate if the designated constraints aren't met.
type CreateESigningRequest_FileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateESigningRequest_FileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateESigningRequest_FileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateESigningRequest_FileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateESigningRequest_FileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateESigningRequest_FileValidationError) ErrorName() string {
	return "CreateESigningRequest_FileValidationError"
}

// Error satisfies the builtin error interface
func (e CreateESigningRequest_FileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateESigningRequest_File.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateESigningRequest_FileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateESigningRequest_FileValidationError{}

// Validate checks the field values on CreateESigningRequest_Invitees with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateESigningRequest_Invitees) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateESigningRequest_Invitees with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateESigningRequest_InviteesMultiError, or nil if none found.
func (m *CreateESigningRequest_Invitees) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateESigningRequest_Invitees) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Email

	// no validation rules for Phone

	if all {
		switch v := interface{}(m.GetAadhaarConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateESigningRequest_InviteesValidationError{
					field:  "AadhaarConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateESigningRequest_InviteesValidationError{
					field:  "AadhaarConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAadhaarConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateESigningRequest_InviteesValidationError{
				field:  "AadhaarConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateESigningRequest_InviteesMultiError(errors)
	}

	return nil
}

// CreateESigningRequest_InviteesMultiError is an error wrapping multiple
// validation errors returned by CreateESigningRequest_Invitees.ValidateAll()
// if the designated constraints aren't met.
type CreateESigningRequest_InviteesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateESigningRequest_InviteesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateESigningRequest_InviteesMultiError) AllErrors() []error { return m }

// CreateESigningRequest_InviteesValidationError is the validation error
// returned by CreateESigningRequest_Invitees.Validate if the designated
// constraints aren't met.
type CreateESigningRequest_InviteesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateESigningRequest_InviteesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateESigningRequest_InviteesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateESigningRequest_InviteesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateESigningRequest_InviteesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateESigningRequest_InviteesValidationError) ErrorName() string {
	return "CreateESigningRequest_InviteesValidationError"
}

// Error satisfies the builtin error interface
func (e CreateESigningRequest_InviteesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateESigningRequest_Invitees.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateESigningRequest_InviteesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateESigningRequest_InviteesValidationError{}

// Validate checks the field values on CreateESigningRequest_File_Field with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateESigningRequest_File_Field) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateESigningRequest_File_Field with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateESigningRequest_File_FieldMultiError, or nil if none found.
func (m *CreateESigningRequest_File_Field) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateESigningRequest_File_Field) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Value

	// no validation rules for Required

	// no validation rules for Width

	// no validation rules for Height

	if len(errors) > 0 {
		return CreateESigningRequest_File_FieldMultiError(errors)
	}

	return nil
}

// CreateESigningRequest_File_FieldMultiError is an error wrapping multiple
// validation errors returned by
// CreateESigningRequest_File_Field.ValidateAll() if the designated
// constraints aren't met.
type CreateESigningRequest_File_FieldMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateESigningRequest_File_FieldMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateESigningRequest_File_FieldMultiError) AllErrors() []error { return m }

// CreateESigningRequest_File_FieldValidationError is the validation error
// returned by CreateESigningRequest_File_Field.Validate if the designated
// constraints aren't met.
type CreateESigningRequest_File_FieldValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateESigningRequest_File_FieldValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateESigningRequest_File_FieldValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateESigningRequest_File_FieldValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateESigningRequest_File_FieldValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateESigningRequest_File_FieldValidationError) ErrorName() string {
	return "CreateESigningRequest_File_FieldValidationError"
}

// Error satisfies the builtin error interface
func (e CreateESigningRequest_File_FieldValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateESigningRequest_File_Field.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateESigningRequest_File_FieldValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateESigningRequest_File_FieldValidationError{}

// Validate checks the field values on
// CreateESigningRequest_Invitees_AadhaarConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateESigningRequest_Invitees_AadhaarConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateESigningRequest_Invitees_AadhaarConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreateESigningRequest_Invitees_AadhaarConfigMultiError, or nil if none found.
func (m *CreateESigningRequest_Invitees_AadhaarConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateESigningRequest_Invitees_AadhaarConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Yob

	if len(errors) > 0 {
		return CreateESigningRequest_Invitees_AadhaarConfigMultiError(errors)
	}

	return nil
}

// CreateESigningRequest_Invitees_AadhaarConfigMultiError is an error wrapping
// multiple validation errors returned by
// CreateESigningRequest_Invitees_AadhaarConfig.ValidateAll() if the
// designated constraints aren't met.
type CreateESigningRequest_Invitees_AadhaarConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateESigningRequest_Invitees_AadhaarConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateESigningRequest_Invitees_AadhaarConfigMultiError) AllErrors() []error { return m }

// CreateESigningRequest_Invitees_AadhaarConfigValidationError is the
// validation error returned by
// CreateESigningRequest_Invitees_AadhaarConfig.Validate if the designated
// constraints aren't met.
type CreateESigningRequest_Invitees_AadhaarConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateESigningRequest_Invitees_AadhaarConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateESigningRequest_Invitees_AadhaarConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateESigningRequest_Invitees_AadhaarConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateESigningRequest_Invitees_AadhaarConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateESigningRequest_Invitees_AadhaarConfigValidationError) ErrorName() string {
	return "CreateESigningRequest_Invitees_AadhaarConfigValidationError"
}

// Error satisfies the builtin error interface
func (e CreateESigningRequest_Invitees_AadhaarConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateESigningRequest_Invitees_AadhaarConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateESigningRequest_Invitees_AadhaarConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateESigningRequest_Invitees_AadhaarConfigValidationError{}

// Validate checks the field values on CreateESigningResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateESigningResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateESigningResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateESigningResponse_DataMultiError, or nil if none found.
func (m *CreateESigningResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateESigningResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocumentId

	// no validation rules for Irn

	for idx, item := range m.GetInvitees() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateESigningResponse_DataValidationError{
						field:  fmt.Sprintf("Invitees[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateESigningResponse_DataValidationError{
						field:  fmt.Sprintf("Invitees[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateESigningResponse_DataValidationError{
					field:  fmt.Sprintf("Invitees[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCc() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateESigningResponse_DataValidationError{
						field:  fmt.Sprintf("Cc[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateESigningResponse_DataValidationError{
						field:  fmt.Sprintf("Cc[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateESigningResponse_DataValidationError{
					field:  fmt.Sprintf("Cc[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateESigningResponse_DataMultiError(errors)
	}

	return nil
}

// CreateESigningResponse_DataMultiError is an error wrapping multiple
// validation errors returned by CreateESigningResponse_Data.ValidateAll() if
// the designated constraints aren't met.
type CreateESigningResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateESigningResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateESigningResponse_DataMultiError) AllErrors() []error { return m }

// CreateESigningResponse_DataValidationError is the validation error returned
// by CreateESigningResponse_Data.Validate if the designated constraints
// aren't met.
type CreateESigningResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateESigningResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateESigningResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateESigningResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateESigningResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateESigningResponse_DataValidationError) ErrorName() string {
	return "CreateESigningResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e CreateESigningResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateESigningResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateESigningResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateESigningResponse_DataValidationError{}

// Validate checks the field values on CreateESigningResponse_Data_Invitees
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateESigningResponse_Data_Invitees) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateESigningResponse_Data_Invitees
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateESigningResponse_Data_InviteesMultiError, or nil if none found.
func (m *CreateESigningResponse_Data_Invitees) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateESigningResponse_Data_Invitees) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Email

	// no validation rules for Phone

	// no validation rules for SignUrl

	// no validation rules for Active

	// no validation rules for ExpiryDate

	if len(errors) > 0 {
		return CreateESigningResponse_Data_InviteesMultiError(errors)
	}

	return nil
}

// CreateESigningResponse_Data_InviteesMultiError is an error wrapping multiple
// validation errors returned by
// CreateESigningResponse_Data_Invitees.ValidateAll() if the designated
// constraints aren't met.
type CreateESigningResponse_Data_InviteesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateESigningResponse_Data_InviteesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateESigningResponse_Data_InviteesMultiError) AllErrors() []error { return m }

// CreateESigningResponse_Data_InviteesValidationError is the validation error
// returned by CreateESigningResponse_Data_Invitees.Validate if the designated
// constraints aren't met.
type CreateESigningResponse_Data_InviteesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateESigningResponse_Data_InviteesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateESigningResponse_Data_InviteesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateESigningResponse_Data_InviteesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateESigningResponse_Data_InviteesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateESigningResponse_Data_InviteesValidationError) ErrorName() string {
	return "CreateESigningResponse_Data_InviteesValidationError"
}

// Error satisfies the builtin error interface
func (e CreateESigningResponse_Data_InviteesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateESigningResponse_Data_Invitees.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateESigningResponse_Data_InviteesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateESigningResponse_Data_InviteesValidationError{}

// Validate checks the field values on CheckESigningStatusResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CheckESigningStatusResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckESigningStatusResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckESigningStatusResponse_DataMultiError, or nil if none found.
func (m *CheckESigningStatusResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckESigningStatusResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocumentId

	// no validation rules for Irn

	// no validation rules for FolderId

	// no validation rules for AuditTrail

	for idx, item := range m.GetRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CheckESigningStatusResponse_DataValidationError{
						field:  fmt.Sprintf("Requests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CheckESigningStatusResponse_DataValidationError{
						field:  fmt.Sprintf("Requests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CheckESigningStatusResponse_DataValidationError{
					field:  fmt.Sprintf("Requests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSigners() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CheckESigningStatusResponse_DataValidationError{
						field:  fmt.Sprintf("Signers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CheckESigningStatusResponse_DataValidationError{
						field:  fmt.Sprintf("Signers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CheckESigningStatusResponse_DataValidationError{
					field:  fmt.Sprintf("Signers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CheckESigningStatusResponse_DataMultiError(errors)
	}

	return nil
}

// CheckESigningStatusResponse_DataMultiError is an error wrapping multiple
// validation errors returned by
// CheckESigningStatusResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type CheckESigningStatusResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckESigningStatusResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckESigningStatusResponse_DataMultiError) AllErrors() []error { return m }

// CheckESigningStatusResponse_DataValidationError is the validation error
// returned by CheckESigningStatusResponse_Data.Validate if the designated
// constraints aren't met.
type CheckESigningStatusResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckESigningStatusResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckESigningStatusResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckESigningStatusResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckESigningStatusResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckESigningStatusResponse_DataValidationError) ErrorName() string {
	return "CheckESigningStatusResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e CheckESigningStatusResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckESigningStatusResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckESigningStatusResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckESigningStatusResponse_DataValidationError{}

// Validate checks the field values on CheckESigningStatusResponse_Data_Request
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CheckESigningStatusResponse_Data_Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckESigningStatusResponse_Data_Request with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CheckESigningStatusResponse_Data_RequestMultiError, or nil if none found.
func (m *CheckESigningStatusResponse_Data_Request) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckESigningStatusResponse_Data_Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Email

	// no validation rules for Phone

	// no validation rules for SignUrl

	// no validation rules for ExpiryDate

	// no validation rules for SignType

	// no validation rules for Active

	// no validation rules for Signed

	// no validation rules for Rejected

	// no validation rules for Expired

	if len(errors) > 0 {
		return CheckESigningStatusResponse_Data_RequestMultiError(errors)
	}

	return nil
}

// CheckESigningStatusResponse_Data_RequestMultiError is an error wrapping
// multiple validation errors returned by
// CheckESigningStatusResponse_Data_Request.ValidateAll() if the designated
// constraints aren't met.
type CheckESigningStatusResponse_Data_RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckESigningStatusResponse_Data_RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckESigningStatusResponse_Data_RequestMultiError) AllErrors() []error { return m }

// CheckESigningStatusResponse_Data_RequestValidationError is the validation
// error returned by CheckESigningStatusResponse_Data_Request.Validate if the
// designated constraints aren't met.
type CheckESigningStatusResponse_Data_RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckESigningStatusResponse_Data_RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckESigningStatusResponse_Data_RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckESigningStatusResponse_Data_RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckESigningStatusResponse_Data_RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckESigningStatusResponse_Data_RequestValidationError) ErrorName() string {
	return "CheckESigningStatusResponse_Data_RequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckESigningStatusResponse_Data_RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckESigningStatusResponse_Data_Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckESigningStatusResponse_Data_RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckESigningStatusResponse_Data_RequestValidationError{}

// Validate checks the field values on CheckESigningStatusResponse_Data_Signer
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CheckESigningStatusResponse_Data_Signer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckESigningStatusResponse_Data_Signer with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CheckESigningStatusResponse_Data_SignerMultiError, or nil if none found.
func (m *CheckESigningStatusResponse_Data_Signer) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckESigningStatusResponse_Data_Signer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Pincode

	// no validation rules for State

	// no validation rules for Title

	if len(errors) > 0 {
		return CheckESigningStatusResponse_Data_SignerMultiError(errors)
	}

	return nil
}

// CheckESigningStatusResponse_Data_SignerMultiError is an error wrapping
// multiple validation errors returned by
// CheckESigningStatusResponse_Data_Signer.ValidateAll() if the designated
// constraints aren't met.
type CheckESigningStatusResponse_Data_SignerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckESigningStatusResponse_Data_SignerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckESigningStatusResponse_Data_SignerMultiError) AllErrors() []error { return m }

// CheckESigningStatusResponse_Data_SignerValidationError is the validation
// error returned by CheckESigningStatusResponse_Data_Signer.Validate if the
// designated constraints aren't met.
type CheckESigningStatusResponse_Data_SignerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckESigningStatusResponse_Data_SignerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckESigningStatusResponse_Data_SignerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckESigningStatusResponse_Data_SignerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckESigningStatusResponse_Data_SignerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckESigningStatusResponse_Data_SignerValidationError) ErrorName() string {
	return "CheckESigningStatusResponse_Data_SignerValidationError"
}

// Error satisfies the builtin error interface
func (e CheckESigningStatusResponse_Data_SignerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckESigningStatusResponse_Data_Signer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckESigningStatusResponse_Data_SignerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckESigningStatusResponse_Data_SignerValidationError{}
