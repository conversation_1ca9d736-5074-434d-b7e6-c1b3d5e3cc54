// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/leegality/esign.proto

package leegality

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateESigningRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProfileId string                            `protobuf:"bytes,1,opt,name=profile_id,json=profileId,proto3" json:"profile_id,omitempty"`
	File      *CreateESigningRequest_File       `protobuf:"bytes,2,opt,name=file,proto3" json:"file,omitempty"`
	Invitees  []*CreateESigningRequest_Invitees `protobuf:"bytes,3,rep,name=invitees,proto3" json:"invitees,omitempty"`
	Irn       string                            `protobuf:"bytes,4,opt,name=irn,proto3" json:"irn,omitempty"`
	Cc        []*CreateESigningRequest_Invitees `protobuf:"bytes,5,rep,name=cc,proto3" json:"cc,omitempty"`
}

func (x *CreateESigningRequest) Reset() {
	*x = CreateESigningRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_leegality_esign_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateESigningRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateESigningRequest) ProtoMessage() {}

func (x *CreateESigningRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_leegality_esign_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateESigningRequest.ProtoReflect.Descriptor instead.
func (*CreateESigningRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_leegality_esign_proto_rawDescGZIP(), []int{0}
}

func (x *CreateESigningRequest) GetProfileId() string {
	if x != nil {
		return x.ProfileId
	}
	return ""
}

func (x *CreateESigningRequest) GetFile() *CreateESigningRequest_File {
	if x != nil {
		return x.File
	}
	return nil
}

func (x *CreateESigningRequest) GetInvitees() []*CreateESigningRequest_Invitees {
	if x != nil {
		return x.Invitees
	}
	return nil
}

func (x *CreateESigningRequest) GetIrn() string {
	if x != nil {
		return x.Irn
	}
	return ""
}

func (x *CreateESigningRequest) GetCc() []*CreateESigningRequest_Invitees {
	if x != nil {
		return x.Cc
	}
	return nil
}

type CreateESigningResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   int32                        `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Messages []*Messages                  `protobuf:"bytes,2,rep,name=messages,proto3" json:"messages,omitempty"`
	Data     *CreateESigningResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CreateESigningResponse) Reset() {
	*x = CreateESigningResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_leegality_esign_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateESigningResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateESigningResponse) ProtoMessage() {}

func (x *CreateESigningResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_leegality_esign_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateESigningResponse.ProtoReflect.Descriptor instead.
func (*CreateESigningResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_leegality_esign_proto_rawDescGZIP(), []int{1}
}

func (x *CreateESigningResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CreateESigningResponse) GetMessages() []*Messages {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *CreateESigningResponse) GetData() *CreateESigningResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type CheckESigningStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   int32                             `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Messages []*Messages                       `protobuf:"bytes,2,rep,name=messages,proto3" json:"messages,omitempty"`
	Data     *CheckESigningStatusResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CheckESigningStatusResponse) Reset() {
	*x = CheckESigningStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_leegality_esign_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckESigningStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckESigningStatusResponse) ProtoMessage() {}

func (x *CheckESigningStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_leegality_esign_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckESigningStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckESigningStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_leegality_esign_proto_rawDescGZIP(), []int{2}
}

func (x *CheckESigningStatusResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CheckESigningStatusResponse) GetMessages() []*Messages {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *CheckESigningStatusResponse) GetData() *CheckESigningStatusResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type Messages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *Messages) Reset() {
	*x = Messages{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_leegality_esign_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Messages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Messages) ProtoMessage() {}

func (x *Messages) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_leegality_esign_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Messages.ProtoReflect.Descriptor instead.
func (*Messages) Descriptor() ([]byte, []int) {
	return file_api_vendors_leegality_esign_proto_rawDescGZIP(), []int{3}
}

func (x *Messages) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Messages) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CreateESigningRequest_File struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string                              `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	File   string                              `protobuf:"bytes,2,opt,name=file,proto3" json:"file,omitempty"`
	Fields []*CreateESigningRequest_File_Field `protobuf:"bytes,3,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *CreateESigningRequest_File) Reset() {
	*x = CreateESigningRequest_File{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_leegality_esign_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateESigningRequest_File) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateESigningRequest_File) ProtoMessage() {}

func (x *CreateESigningRequest_File) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_leegality_esign_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateESigningRequest_File.ProtoReflect.Descriptor instead.
func (*CreateESigningRequest_File) Descriptor() ([]byte, []int) {
	return file_api_vendors_leegality_esign_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CreateESigningRequest_File) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateESigningRequest_File) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *CreateESigningRequest_File) GetFields() []*CreateESigningRequest_File_Field {
	if x != nil {
		return x.Fields
	}
	return nil
}

type CreateESigningRequest_Invitees struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string                                        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Email         string                                        `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Phone         string                                        `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty"`
	AadhaarConfig *CreateESigningRequest_Invitees_AadhaarConfig `protobuf:"bytes,4,opt,name=aadhaar_config,json=aadhaarConfig,proto3" json:"aadhaar_config,omitempty"`
}

func (x *CreateESigningRequest_Invitees) Reset() {
	*x = CreateESigningRequest_Invitees{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_leegality_esign_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateESigningRequest_Invitees) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateESigningRequest_Invitees) ProtoMessage() {}

func (x *CreateESigningRequest_Invitees) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_leegality_esign_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateESigningRequest_Invitees.ProtoReflect.Descriptor instead.
func (*CreateESigningRequest_Invitees) Descriptor() ([]byte, []int) {
	return file_api_vendors_leegality_esign_proto_rawDescGZIP(), []int{0, 1}
}

func (x *CreateESigningRequest_Invitees) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateESigningRequest_Invitees) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateESigningRequest_Invitees) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *CreateESigningRequest_Invitees) GetAadhaarConfig() *CreateESigningRequest_Invitees_AadhaarConfig {
	if x != nil {
		return x.AadhaarConfig
	}
	return nil
}

type CreateESigningRequest_File_Field struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type     string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Value    string `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
	Required bool   `protobuf:"varint,5,opt,name=required,proto3" json:"required,omitempty"`
	Width    string `protobuf:"bytes,6,opt,name=width,proto3" json:"width,omitempty"`
	Height   string `protobuf:"bytes,7,opt,name=height,proto3" json:"height,omitempty"`
}

func (x *CreateESigningRequest_File_Field) Reset() {
	*x = CreateESigningRequest_File_Field{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_leegality_esign_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateESigningRequest_File_Field) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateESigningRequest_File_Field) ProtoMessage() {}

func (x *CreateESigningRequest_File_Field) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_leegality_esign_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateESigningRequest_File_Field.ProtoReflect.Descriptor instead.
func (*CreateESigningRequest_File_Field) Descriptor() ([]byte, []int) {
	return file_api_vendors_leegality_esign_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *CreateESigningRequest_File_Field) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CreateESigningRequest_File_Field) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateESigningRequest_File_Field) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateESigningRequest_File_Field) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *CreateESigningRequest_File_Field) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *CreateESigningRequest_File_Field) GetWidth() string {
	if x != nil {
		return x.Width
	}
	return ""
}

func (x *CreateESigningRequest_File_Field) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

type CreateESigningRequest_Invitees_AadhaarConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// yob represent the year of birth, will be fetching yob from user's dob
	Yob string `protobuf:"bytes,1,opt,name=yob,json=verifyYob,proto3" json:"yob,omitempty"`
}

func (x *CreateESigningRequest_Invitees_AadhaarConfig) Reset() {
	*x = CreateESigningRequest_Invitees_AadhaarConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_leegality_esign_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateESigningRequest_Invitees_AadhaarConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateESigningRequest_Invitees_AadhaarConfig) ProtoMessage() {}

func (x *CreateESigningRequest_Invitees_AadhaarConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_leegality_esign_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateESigningRequest_Invitees_AadhaarConfig.ProtoReflect.Descriptor instead.
func (*CreateESigningRequest_Invitees_AadhaarConfig) Descriptor() ([]byte, []int) {
	return file_api_vendors_leegality_esign_proto_rawDescGZIP(), []int{0, 1, 0}
}

func (x *CreateESigningRequest_Invitees_AadhaarConfig) GetYob() string {
	if x != nil {
		return x.Yob
	}
	return ""
}

type CreateESigningResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DocumentId string                                  `protobuf:"bytes,1,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`
	Irn        string                                  `protobuf:"bytes,2,opt,name=irn,proto3" json:"irn,omitempty"`
	Invitees   []*CreateESigningResponse_Data_Invitees `protobuf:"bytes,3,rep,name=invitees,proto3" json:"invitees,omitempty"`
	Cc         []*CreateESigningResponse_Data_Invitees `protobuf:"bytes,4,rep,name=cc,proto3" json:"cc,omitempty"`
}

func (x *CreateESigningResponse_Data) Reset() {
	*x = CreateESigningResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_leegality_esign_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateESigningResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateESigningResponse_Data) ProtoMessage() {}

func (x *CreateESigningResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_leegality_esign_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateESigningResponse_Data.ProtoReflect.Descriptor instead.
func (*CreateESigningResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_leegality_esign_proto_rawDescGZIP(), []int{1, 0}
}

func (x *CreateESigningResponse_Data) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *CreateESigningResponse_Data) GetIrn() string {
	if x != nil {
		return x.Irn
	}
	return ""
}

func (x *CreateESigningResponse_Data) GetInvitees() []*CreateESigningResponse_Data_Invitees {
	if x != nil {
		return x.Invitees
	}
	return nil
}

func (x *CreateESigningResponse_Data) GetCc() []*CreateESigningResponse_Data_Invitees {
	if x != nil {
		return x.Cc
	}
	return nil
}

type CreateESigningResponse_Data_Invitees struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Email      string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Phone      string `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty"`
	SignUrl    string `protobuf:"bytes,4,opt,name=sign_url,json=signUrl,proto3" json:"sign_url,omitempty"`
	Active     bool   `protobuf:"varint,5,opt,name=active,proto3" json:"active,omitempty"`
	ExpiryDate string `protobuf:"bytes,6,opt,name=expiry_date,json=expiryDate,proto3" json:"expiry_date,omitempty"`
}

func (x *CreateESigningResponse_Data_Invitees) Reset() {
	*x = CreateESigningResponse_Data_Invitees{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_leegality_esign_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateESigningResponse_Data_Invitees) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateESigningResponse_Data_Invitees) ProtoMessage() {}

func (x *CreateESigningResponse_Data_Invitees) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_leegality_esign_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateESigningResponse_Data_Invitees.ProtoReflect.Descriptor instead.
func (*CreateESigningResponse_Data_Invitees) Descriptor() ([]byte, []int) {
	return file_api_vendors_leegality_esign_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *CreateESigningResponse_Data_Invitees) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateESigningResponse_Data_Invitees) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateESigningResponse_Data_Invitees) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *CreateESigningResponse_Data_Invitees) GetSignUrl() string {
	if x != nil {
		return x.SignUrl
	}
	return ""
}

func (x *CreateESigningResponse_Data_Invitees) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *CreateESigningResponse_Data_Invitees) GetExpiryDate() string {
	if x != nil {
		return x.ExpiryDate
	}
	return ""
}

type CheckESigningStatusResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DocumentId string                                      `protobuf:"bytes,1,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`
	Irn        string                                      `protobuf:"bytes,2,opt,name=irn,proto3" json:"irn,omitempty"`
	FolderId   string                                      `protobuf:"bytes,3,opt,name=folder_id,json=folderId,proto3" json:"folder_id,omitempty"`
	AuditTrail string                                      `protobuf:"bytes,4,opt,name=audit_trail,json=auditTrail,proto3" json:"audit_trail,omitempty"`
	Requests   []*CheckESigningStatusResponse_Data_Request `protobuf:"bytes,5,rep,name=requests,proto3" json:"requests,omitempty"`
	Files      []string                                    `protobuf:"bytes,6,rep,name=files,proto3" json:"files,omitempty"`
	Signers    []*CheckESigningStatusResponse_Data_Signer  `protobuf:"bytes,7,rep,name=signers,proto3" json:"signers,omitempty"`
}

func (x *CheckESigningStatusResponse_Data) Reset() {
	*x = CheckESigningStatusResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_leegality_esign_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckESigningStatusResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckESigningStatusResponse_Data) ProtoMessage() {}

func (x *CheckESigningStatusResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_leegality_esign_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckESigningStatusResponse_Data.ProtoReflect.Descriptor instead.
func (*CheckESigningStatusResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_leegality_esign_proto_rawDescGZIP(), []int{2, 0}
}

func (x *CheckESigningStatusResponse_Data) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *CheckESigningStatusResponse_Data) GetIrn() string {
	if x != nil {
		return x.Irn
	}
	return ""
}

func (x *CheckESigningStatusResponse_Data) GetFolderId() string {
	if x != nil {
		return x.FolderId
	}
	return ""
}

func (x *CheckESigningStatusResponse_Data) GetAuditTrail() string {
	if x != nil {
		return x.AuditTrail
	}
	return ""
}

func (x *CheckESigningStatusResponse_Data) GetRequests() []*CheckESigningStatusResponse_Data_Request {
	if x != nil {
		return x.Requests
	}
	return nil
}

func (x *CheckESigningStatusResponse_Data) GetFiles() []string {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *CheckESigningStatusResponse_Data) GetSigners() []*CheckESigningStatusResponse_Data_Signer {
	if x != nil {
		return x.Signers
	}
	return nil
}

type CheckESigningStatusResponse_Data_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Email      string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Phone      string `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty"`
	SignUrl    string `protobuf:"bytes,4,opt,name=sign_url,json=signUrl,proto3" json:"sign_url,omitempty"`
	ExpiryDate string `protobuf:"bytes,5,opt,name=expiry_date,json=expiryDate,proto3" json:"expiry_date,omitempty"`
	SignType   string `protobuf:"bytes,6,opt,name=sign_type,json=signType,proto3" json:"sign_type,omitempty"`
	Active     bool   `protobuf:"varint,7,opt,name=active,proto3" json:"active,omitempty"`
	Signed     bool   `protobuf:"varint,8,opt,name=signed,proto3" json:"signed,omitempty"`
	Rejected   bool   `protobuf:"varint,9,opt,name=rejected,proto3" json:"rejected,omitempty"`
	Expired    bool   `protobuf:"varint,10,opt,name=expired,proto3" json:"expired,omitempty"`
}

func (x *CheckESigningStatusResponse_Data_Request) Reset() {
	*x = CheckESigningStatusResponse_Data_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_leegality_esign_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckESigningStatusResponse_Data_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckESigningStatusResponse_Data_Request) ProtoMessage() {}

func (x *CheckESigningStatusResponse_Data_Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_leegality_esign_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckESigningStatusResponse_Data_Request.ProtoReflect.Descriptor instead.
func (*CheckESigningStatusResponse_Data_Request) Descriptor() ([]byte, []int) {
	return file_api_vendors_leegality_esign_proto_rawDescGZIP(), []int{2, 0, 0}
}

func (x *CheckESigningStatusResponse_Data_Request) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CheckESigningStatusResponse_Data_Request) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CheckESigningStatusResponse_Data_Request) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *CheckESigningStatusResponse_Data_Request) GetSignUrl() string {
	if x != nil {
		return x.SignUrl
	}
	return ""
}

func (x *CheckESigningStatusResponse_Data_Request) GetExpiryDate() string {
	if x != nil {
		return x.ExpiryDate
	}
	return ""
}

func (x *CheckESigningStatusResponse_Data_Request) GetSignType() string {
	if x != nil {
		return x.SignType
	}
	return ""
}

func (x *CheckESigningStatusResponse_Data_Request) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *CheckESigningStatusResponse_Data_Request) GetSigned() bool {
	if x != nil {
		return x.Signed
	}
	return false
}

func (x *CheckESigningStatusResponse_Data_Request) GetRejected() bool {
	if x != nil {
		return x.Rejected
	}
	return false
}

func (x *CheckESigningStatusResponse_Data_Request) GetExpired() bool {
	if x != nil {
		return x.Expired
	}
	return false
}

type CheckESigningStatusResponse_Data_Signer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Pincode string `protobuf:"bytes,2,opt,name=pincode,proto3" json:"pincode,omitempty"`
	State   string `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	Title   string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
}

func (x *CheckESigningStatusResponse_Data_Signer) Reset() {
	*x = CheckESigningStatusResponse_Data_Signer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_leegality_esign_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckESigningStatusResponse_Data_Signer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckESigningStatusResponse_Data_Signer) ProtoMessage() {}

func (x *CheckESigningStatusResponse_Data_Signer) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_leegality_esign_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckESigningStatusResponse_Data_Signer.ProtoReflect.Descriptor instead.
func (*CheckESigningStatusResponse_Data_Signer) Descriptor() ([]byte, []int) {
	return file_api_vendors_leegality_esign_proto_rawDescGZIP(), []int{2, 0, 1}
}

func (x *CheckESigningStatusResponse_Data_Signer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CheckESigningStatusResponse_Data_Signer) GetPincode() string {
	if x != nil {
		return x.Pincode
	}
	return ""
}

func (x *CheckESigningStatusResponse_Data_Signer) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *CheckESigningStatusResponse_Data_Signer) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

var File_api_vendors_leegality_esign_proto protoreflect.FileDescriptor

var file_api_vendors_leegality_esign_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x65,
	0x65, 0x67, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x65,
	0x67, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x22, 0x9b, 0x06, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12,
	0x41, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x65, 0x67, 0x61, 0x6c, 0x69, 0x74,
	0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x04, 0x66, 0x69,
	0x6c, 0x65, 0x12, 0x4d, 0x0a, 0x08, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c,
	0x65, 0x65, 0x67, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45,
	0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49,
	0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x73, 0x52, 0x08, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65,
	0x73, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x72, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x69, 0x72, 0x6e, 0x12, 0x41, 0x0a, 0x02, 0x63, 0x63, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x65, 0x67, 0x61, 0x6c,
	0x69, 0x74, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x65, 0x73, 0x52, 0x02, 0x63, 0x63, 0x1a, 0x9d, 0x02, 0x0a, 0x04, 0x46, 0x69, 0x6c, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x4b, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x6c, 0x65, 0x65, 0x67, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x06, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x1a, 0x9f, 0x01, 0x0a, 0x05, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16,
	0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0xdb, 0x01, 0x0a, 0x08, 0x49, 0x6e, 0x76, 0x69, 0x74,
	0x65, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x12, 0x66, 0x0a, 0x0e, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x65, 0x67, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x73, 0x2e, 0x41,
	0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x61, 0x61,
	0x64, 0x68, 0x61, 0x61, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x27, 0x0a, 0x0d, 0x41,
	0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x03,
	0x79, 0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x59, 0x6f, 0x62, 0x22, 0xa8, 0x04, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45,
	0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x65, 0x67, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x12, 0x42, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x65, 0x67, 0x61, 0x6c, 0x69,
	0x74, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x1a, 0xf8, 0x02, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a,
	0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x72, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x72, 0x6e,
	0x12, 0x53, 0x0a, 0x08, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x65,
	0x67, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x53, 0x69,
	0x67, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x73, 0x52, 0x08, 0x69, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x02, 0x63, 0x63, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x65, 0x67,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x53, 0x69, 0x67,
	0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x73, 0x52, 0x02, 0x63, 0x63, 0x1a, 0x9e,
	0x01, 0x0a, 0x08, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x69, 0x67, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x69, 0x67, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x22,
	0xe5, 0x06, 0x0a, 0x1b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x65, 0x67, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x12, 0x47, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x65, 0x67, 0x61, 0x6c, 0x69,
	0x74, 0x79, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0xab, 0x05, 0x0a, 0x04, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x72, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x69, 0x72, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x69,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x75, 0x64, 0x69, 0x74, 0x54, 0x72,
	0x61, 0x69, 0x6c, 0x12, 0x57, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x6c, 0x65, 0x65, 0x67, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45,
	0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x12, 0x54, 0x0a, 0x07, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65,
	0x65, 0x67, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x53, 0x69,
	0x67, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x72, 0x52,
	0x07, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x72, 0x73, 0x1a, 0x88, 0x02, 0x0a, 0x07, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x69, 0x67, 0x6e, 0x55, 0x72, 0x6c, 0x12,
	0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x64, 0x1a, 0x62, 0x0a, 0x06, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x22, 0x38, 0x0a, 0x08, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x65, 0x67, 0x61, 0x6c, 0x69, 0x74,
	0x79, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x65, 0x65, 0x67, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_leegality_esign_proto_rawDescOnce sync.Once
	file_api_vendors_leegality_esign_proto_rawDescData = file_api_vendors_leegality_esign_proto_rawDesc
)

func file_api_vendors_leegality_esign_proto_rawDescGZIP() []byte {
	file_api_vendors_leegality_esign_proto_rawDescOnce.Do(func() {
		file_api_vendors_leegality_esign_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_leegality_esign_proto_rawDescData)
	})
	return file_api_vendors_leegality_esign_proto_rawDescData
}

var file_api_vendors_leegality_esign_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_api_vendors_leegality_esign_proto_goTypes = []interface{}{
	(*CreateESigningRequest)(nil),                        // 0: vendors.leegality.CreateESigningRequest
	(*CreateESigningResponse)(nil),                       // 1: vendors.leegality.CreateESigningResponse
	(*CheckESigningStatusResponse)(nil),                  // 2: vendors.leegality.CheckESigningStatusResponse
	(*Messages)(nil),                                     // 3: vendors.leegality.Messages
	(*CreateESigningRequest_File)(nil),                   // 4: vendors.leegality.CreateESigningRequest.File
	(*CreateESigningRequest_Invitees)(nil),               // 5: vendors.leegality.CreateESigningRequest.Invitees
	(*CreateESigningRequest_File_Field)(nil),             // 6: vendors.leegality.CreateESigningRequest.File.Field
	(*CreateESigningRequest_Invitees_AadhaarConfig)(nil), // 7: vendors.leegality.CreateESigningRequest.Invitees.AadhaarConfig
	(*CreateESigningResponse_Data)(nil),                  // 8: vendors.leegality.CreateESigningResponse.Data
	(*CreateESigningResponse_Data_Invitees)(nil),         // 9: vendors.leegality.CreateESigningResponse.Data.Invitees
	(*CheckESigningStatusResponse_Data)(nil),             // 10: vendors.leegality.CheckESigningStatusResponse.Data
	(*CheckESigningStatusResponse_Data_Request)(nil),     // 11: vendors.leegality.CheckESigningStatusResponse.Data.Request
	(*CheckESigningStatusResponse_Data_Signer)(nil),      // 12: vendors.leegality.CheckESigningStatusResponse.Data.Signer
}
var file_api_vendors_leegality_esign_proto_depIdxs = []int32{
	4,  // 0: vendors.leegality.CreateESigningRequest.file:type_name -> vendors.leegality.CreateESigningRequest.File
	5,  // 1: vendors.leegality.CreateESigningRequest.invitees:type_name -> vendors.leegality.CreateESigningRequest.Invitees
	5,  // 2: vendors.leegality.CreateESigningRequest.cc:type_name -> vendors.leegality.CreateESigningRequest.Invitees
	3,  // 3: vendors.leegality.CreateESigningResponse.messages:type_name -> vendors.leegality.Messages
	8,  // 4: vendors.leegality.CreateESigningResponse.data:type_name -> vendors.leegality.CreateESigningResponse.Data
	3,  // 5: vendors.leegality.CheckESigningStatusResponse.messages:type_name -> vendors.leegality.Messages
	10, // 6: vendors.leegality.CheckESigningStatusResponse.data:type_name -> vendors.leegality.CheckESigningStatusResponse.Data
	6,  // 7: vendors.leegality.CreateESigningRequest.File.fields:type_name -> vendors.leegality.CreateESigningRequest.File.Field
	7,  // 8: vendors.leegality.CreateESigningRequest.Invitees.aadhaar_config:type_name -> vendors.leegality.CreateESigningRequest.Invitees.AadhaarConfig
	9,  // 9: vendors.leegality.CreateESigningResponse.Data.invitees:type_name -> vendors.leegality.CreateESigningResponse.Data.Invitees
	9,  // 10: vendors.leegality.CreateESigningResponse.Data.cc:type_name -> vendors.leegality.CreateESigningResponse.Data.Invitees
	11, // 11: vendors.leegality.CheckESigningStatusResponse.Data.requests:type_name -> vendors.leegality.CheckESigningStatusResponse.Data.Request
	12, // 12: vendors.leegality.CheckESigningStatusResponse.Data.signers:type_name -> vendors.leegality.CheckESigningStatusResponse.Data.Signer
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_api_vendors_leegality_esign_proto_init() }
func file_api_vendors_leegality_esign_proto_init() {
	if File_api_vendors_leegality_esign_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_leegality_esign_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateESigningRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_leegality_esign_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateESigningResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_leegality_esign_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckESigningStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_leegality_esign_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Messages); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_leegality_esign_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateESigningRequest_File); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_leegality_esign_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateESigningRequest_Invitees); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_leegality_esign_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateESigningRequest_File_Field); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_leegality_esign_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateESigningRequest_Invitees_AadhaarConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_leegality_esign_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateESigningResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_leegality_esign_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateESigningResponse_Data_Invitees); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_leegality_esign_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckESigningStatusResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_leegality_esign_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckESigningStatusResponse_Data_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_leegality_esign_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckESigningStatusResponse_Data_Signer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_leegality_esign_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_leegality_esign_proto_goTypes,
		DependencyIndexes: file_api_vendors_leegality_esign_proto_depIdxs,
		MessageInfos:      file_api_vendors_leegality_esign_proto_msgTypes,
	}.Build()
	File_api_vendors_leegality_esign_proto = out.File
	file_api_vendors_leegality_esign_proto_rawDesc = nil
	file_api_vendors_leegality_esign_proto_goTypes = nil
	file_api_vendors_leegality_esign_proto_depIdxs = nil
}
