// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/inhouse/risk.proto

package inhouse

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on DetectRiskRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DetectRiskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectRiskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetectRiskRequestMultiError, or nil if none found.
func (m *DetectRiskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectRiskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RequestId

	// no validation rules for ReferrerActorId

	// no validation rules for DeviceId

	// no validation rules for EmailId

	// no validation rules for CreditReportPresence

	// no validation rules for CreditReportDownloadConsent

	// no validation rules for DeviceManufacturer

	// no validation rules for IsDevicePremium

	// no validation rules for Age

	// no validation rules for GmailPanNameMatchScore

	// no validation rules for PhoneNumber

	// no validation rules for ScreenerMailCount

	// no validation rules for Latitude

	// no validation rules for Longitude

	// no validation rules for Threshold

	// no validation rules for HashedPhoneNumber

	if all {
		switch v := interface{}(m.GetFatherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFatherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "FatherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMotherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMotherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "MotherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OnboardingEkycNumberMismatch

	if all {
		switch v := interface{}(m.GetUserPanName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "UserPanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "UserPanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserPanName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "UserPanName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCbDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DetectRiskRequestValidationError{
						field:  fmt.Sprintf("CbDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DetectRiskRequestValidationError{
						field:  fmt.Sprintf("CbDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetectRiskRequestValidationError{
					field:  fmt.Sprintf("CbDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetEmploymentData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "EmploymentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "EmploymentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmploymentData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "EmploymentData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeviceDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "DeviceDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DetectRiskRequestMultiError(errors)
	}

	return nil
}

// DetectRiskRequestMultiError is an error wrapping multiple validation errors
// returned by DetectRiskRequest.ValidateAll() if the designated constraints
// aren't met.
type DetectRiskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectRiskRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectRiskRequestMultiError) AllErrors() []error { return m }

// DetectRiskRequestValidationError is the validation error returned by
// DetectRiskRequest.Validate if the designated constraints aren't met.
type DetectRiskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectRiskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectRiskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectRiskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectRiskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectRiskRequestValidationError) ErrorName() string {
	return "DetectRiskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DetectRiskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectRiskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectRiskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectRiskRequestValidationError{}

// Validate checks the field values on CreditReportAttributeInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreditReportAttributeInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditReportAttributeInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreditReportAttributeInfoMultiError, or nil if none found.
func (m *CreditReportAttributeInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditReportAttributeInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttributeKey

	// no validation rules for AttributeValue

	if len(errors) > 0 {
		return CreditReportAttributeInfoMultiError(errors)
	}

	return nil
}

// CreditReportAttributeInfoMultiError is an error wrapping multiple validation
// errors returned by CreditReportAttributeInfo.ValidateAll() if the
// designated constraints aren't met.
type CreditReportAttributeInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditReportAttributeInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditReportAttributeInfoMultiError) AllErrors() []error { return m }

// CreditReportAttributeInfoValidationError is the validation error returned by
// CreditReportAttributeInfo.Validate if the designated constraints aren't met.
type CreditReportAttributeInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditReportAttributeInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditReportAttributeInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditReportAttributeInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditReportAttributeInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditReportAttributeInfoValidationError) ErrorName() string {
	return "CreditReportAttributeInfoValidationError"
}

// Error satisfies the builtin error interface
func (e CreditReportAttributeInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditReportAttributeInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditReportAttributeInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditReportAttributeInfoValidationError{}

// Validate checks the field values on DetectRiskResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetectRiskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectRiskResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetectRiskResponseMultiError, or nil if none found.
func (m *DetectRiskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectRiskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Score

	// no validation rules for Threshold

	// no validation rules for RiskyUser

	// no validation rules for Time

	if len(errors) > 0 {
		return DetectRiskResponseMultiError(errors)
	}

	return nil
}

// DetectRiskResponseMultiError is an error wrapping multiple validation errors
// returned by DetectRiskResponse.ValidateAll() if the designated constraints
// aren't met.
type DetectRiskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectRiskResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectRiskResponseMultiError) AllErrors() []error { return m }

// DetectRiskResponseValidationError is the validation error returned by
// DetectRiskResponse.Validate if the designated constraints aren't met.
type DetectRiskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectRiskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectRiskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectRiskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectRiskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectRiskResponseValidationError) ErrorName() string {
	return "DetectRiskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DetectRiskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectRiskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectRiskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectRiskResponseValidationError{}

// Validate checks the field values on DetectReOnboardingRiskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetectReOnboardingRiskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectReOnboardingRiskRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DetectReOnboardingRiskRequestMultiError, or nil if none found.
func (m *DetectReOnboardingRiskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectReOnboardingRiskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RequestId

	// no validation rules for ReferrerActorId

	// no validation rules for DeviceId

	// no validation rules for EmailId

	// no validation rules for CreditReportPresence

	// no validation rules for CreditReportDownloadConsent

	// no validation rules for DeviceManufacturer

	// no validation rules for IsDevicePremium

	// no validation rules for Age

	// no validation rules for GmailPanNameMatchScore

	// no validation rules for PhoneNumber

	// no validation rules for ScreenerMailCount

	// no validation rules for Latitude

	// no validation rules for Longitude

	// no validation rules for Threshold

	// no validation rules for HashedPhoneNumber

	if all {
		switch v := interface{}(m.GetFatherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFatherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "FatherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMotherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMotherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "MotherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OnboardingEkycNumberMismatch

	if all {
		switch v := interface{}(m.GetUserPanName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "UserPanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "UserPanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserPanName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "UserPanName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserCity

	// no validation rules for UserPostalCode

	// no validation rules for KycLevel

	// no validation rules for OverallAfuStatus

	// no validation rules for FailureReason

	if all {
		switch v := interface{}(m.GetNewValues()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "NewValues",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "NewValues",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNewValues()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "NewValues",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AfuAttemptNum

	// no validation rules for EpifiEmailPhoneNumUpdate

	// no validation rules for EpifiDeviceUpdate

	if all {
		switch v := interface{}(m.GetCurrentValues()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "CurrentValues",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "CurrentValues",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValues()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "CurrentValues",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNewDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "NewDevice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "NewDevice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNewDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "NewDevice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActorAuthState()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "ActorAuthState",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "ActorAuthState",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActorAuthState()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "ActorAuthState",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserState

	// no validation rules for AfuLatitude

	// no validation rules for AfuLongitude

	// no validation rules for OnboardingCompletedAt

	for idx, item := range m.GetCbDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DetectReOnboardingRiskRequestValidationError{
						field:  fmt.Sprintf("CbDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DetectReOnboardingRiskRequestValidationError{
						field:  fmt.Sprintf("CbDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetectReOnboardingRiskRequestValidationError{
					field:  fmt.Sprintf("CbDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetEmploymentData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "EmploymentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "EmploymentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmploymentData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "EmploymentData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeviceDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "DeviceDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAfuAttempts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DetectReOnboardingRiskRequestValidationError{
						field:  fmt.Sprintf("AfuAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DetectReOnboardingRiskRequestValidationError{
						field:  fmt.Sprintf("AfuAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetectReOnboardingRiskRequestValidationError{
					field:  fmt.Sprintf("AfuAttempts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetScreenerChecksInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "ScreenerChecksInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "ScreenerChecksInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenerChecksInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "ScreenerChecksInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OnboardingModelRiskScore

	if all {
		switch v := interface{}(m.GetAccountInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "AccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "AccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "AccountInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOldDeviceDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "OldDeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "OldDeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOldDeviceDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "OldDeviceDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsCreditReportDownloadConsentGiven

	if all {
		switch v := interface{}(m.GetProfile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Profile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Profile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProfile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "Profile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DetectReOnboardingRiskRequestMultiError(errors)
	}

	return nil
}

// DetectReOnboardingRiskRequestMultiError is an error wrapping multiple
// validation errors returned by DetectReOnboardingRiskRequest.ValidateAll()
// if the designated constraints aren't met.
type DetectReOnboardingRiskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectReOnboardingRiskRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectReOnboardingRiskRequestMultiError) AllErrors() []error { return m }

// DetectReOnboardingRiskRequestValidationError is the validation error
// returned by DetectReOnboardingRiskRequest.Validate if the designated
// constraints aren't met.
type DetectReOnboardingRiskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectReOnboardingRiskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectReOnboardingRiskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectReOnboardingRiskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectReOnboardingRiskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectReOnboardingRiskRequestValidationError) ErrorName() string {
	return "DetectReOnboardingRiskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DetectReOnboardingRiskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectReOnboardingRiskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectReOnboardingRiskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectReOnboardingRiskRequestValidationError{}

// Validate checks the field values on DetectReOnboardingRiskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetectReOnboardingRiskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectReOnboardingRiskResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DetectReOnboardingRiskResponseMultiError, or nil if none found.
func (m *DetectReOnboardingRiskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectReOnboardingRiskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Score

	// no validation rules for Threshold

	// no validation rules for RiskyUser

	// no validation rules for Time

	if len(errors) > 0 {
		return DetectReOnboardingRiskResponseMultiError(errors)
	}

	return nil
}

// DetectReOnboardingRiskResponseMultiError is an error wrapping multiple
// validation errors returned by DetectReOnboardingRiskResponse.ValidateAll()
// if the designated constraints aren't met.
type DetectReOnboardingRiskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectReOnboardingRiskResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectReOnboardingRiskResponseMultiError) AllErrors() []error { return m }

// DetectReOnboardingRiskResponseValidationError is the validation error
// returned by DetectReOnboardingRiskResponse.Validate if the designated
// constraints aren't met.
type DetectReOnboardingRiskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectReOnboardingRiskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectReOnboardingRiskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectReOnboardingRiskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectReOnboardingRiskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectReOnboardingRiskResponseValidationError) ErrorName() string {
	return "DetectReOnboardingRiskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DetectReOnboardingRiskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectReOnboardingRiskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectReOnboardingRiskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectReOnboardingRiskResponseValidationError{}

// Validate checks the field values on DetectLocationRiskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetectLocationRiskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectLocationRiskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetectLocationRiskRequestMultiError, or nil if none found.
func (m *DetectLocationRiskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectLocationRiskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := DetectLocationRiskRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetRequestId()) < 1 {
		err := DetectLocationRiskRequestValidationError{
			field:  "RequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetLatitude()) < 1 {
		err := DetectLocationRiskRequestValidationError{
			field:  "Latitude",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetLongitude()) < 1 {
		err := DetectLocationRiskRequestValidationError{
			field:  "Longitude",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPincode()) < 1 {
		err := DetectLocationRiskRequestValidationError{
			field:  "Pincode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DetectLocationRiskRequestMultiError(errors)
	}

	return nil
}

// DetectLocationRiskRequestMultiError is an error wrapping multiple validation
// errors returned by DetectLocationRiskRequest.ValidateAll() if the
// designated constraints aren't met.
type DetectLocationRiskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectLocationRiskRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectLocationRiskRequestMultiError) AllErrors() []error { return m }

// DetectLocationRiskRequestValidationError is the validation error returned by
// DetectLocationRiskRequest.Validate if the designated constraints aren't met.
type DetectLocationRiskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectLocationRiskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectLocationRiskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectLocationRiskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectLocationRiskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectLocationRiskRequestValidationError) ErrorName() string {
	return "DetectLocationRiskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DetectLocationRiskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectLocationRiskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectLocationRiskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectLocationRiskRequestValidationError{}

// Validate checks the field values on DetectLocationRiskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetectLocationRiskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectLocationRiskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetectLocationRiskResponseMultiError, or nil if none found.
func (m *DetectLocationRiskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectLocationRiskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Score

	// no validation rules for Severity

	// no validation rules for Version

	if len(errors) > 0 {
		return DetectLocationRiskResponseMultiError(errors)
	}

	return nil
}

// DetectLocationRiskResponseMultiError is an error wrapping multiple
// validation errors returned by DetectLocationRiskResponse.ValidateAll() if
// the designated constraints aren't met.
type DetectLocationRiskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectLocationRiskResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectLocationRiskResponseMultiError) AllErrors() []error { return m }

// DetectLocationRiskResponseValidationError is the validation error returned
// by DetectLocationRiskResponse.Validate if the designated constraints aren't met.
type DetectLocationRiskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectLocationRiskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectLocationRiskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectLocationRiskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectLocationRiskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectLocationRiskResponseValidationError) ErrorName() string {
	return "DetectLocationRiskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DetectLocationRiskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectLocationRiskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectLocationRiskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectLocationRiskResponseValidationError{}

// Validate checks the field values on EmploymentData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EmploymentData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmploymentData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EmploymentDataMultiError,
// or nil if none found.
func (m *EmploymentData) ValidateAll() error {
	return m.validate(true)
}

func (m *EmploymentData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EmploymentType

	// no validation rules for CompanyName

	if all {
		switch v := interface{}(m.GetAnnualSalaryRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmploymentDataValidationError{
					field:  "AnnualSalaryRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmploymentDataValidationError{
					field:  "AnnualSalaryRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnualSalaryRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmploymentDataValidationError{
				field:  "AnnualSalaryRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsCompanyEpfRegistered

	if len(errors) > 0 {
		return EmploymentDataMultiError(errors)
	}

	return nil
}

// EmploymentDataMultiError is an error wrapping multiple validation errors
// returned by EmploymentData.ValidateAll() if the designated constraints
// aren't met.
type EmploymentDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmploymentDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmploymentDataMultiError) AllErrors() []error { return m }

// EmploymentDataValidationError is the validation error returned by
// EmploymentData.Validate if the designated constraints aren't met.
type EmploymentDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmploymentDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmploymentDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmploymentDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmploymentDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmploymentDataValidationError) ErrorName() string { return "EmploymentDataValidationError" }

// Error satisfies the builtin error interface
func (e EmploymentDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmploymentData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmploymentDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmploymentDataValidationError{}

// Validate checks the field values on DeviceDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeviceDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeviceDetailsMultiError, or
// nil if none found.
func (m *DeviceDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Manufacturer

	// no validation rules for IsPremium

	// no validation rules for AppVersion

	// no validation rules for Language

	// no validation rules for SwVersion

	for idx, item := range m.GetInstalledApps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeviceDetailsValidationError{
						field:  fmt.Sprintf("InstalledApps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeviceDetailsValidationError{
						field:  fmt.Sprintf("InstalledApps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeviceDetailsValidationError{
					field:  fmt.Sprintf("InstalledApps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Model

	if len(errors) > 0 {
		return DeviceDetailsMultiError(errors)
	}

	return nil
}

// DeviceDetailsMultiError is an error wrapping multiple validation errors
// returned by DeviceDetails.ValidateAll() if the designated constraints
// aren't met.
type DeviceDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceDetailsMultiError) AllErrors() []error { return m }

// DeviceDetailsValidationError is the validation error returned by
// DeviceDetails.Validate if the designated constraints aren't met.
type DeviceDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceDetailsValidationError) ErrorName() string { return "DeviceDetailsValidationError" }

// Error satisfies the builtin error interface
func (e DeviceDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceDetailsValidationError{}

// Validate checks the field values on AFUAttempt with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AFUAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AFUAttempt with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AFUAttemptMultiError, or
// nil if none found.
func (m *AFUAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *AFUAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OverallStatus

	// no validation rules for CreatedAt

	if len(errors) > 0 {
		return AFUAttemptMultiError(errors)
	}

	return nil
}

// AFUAttemptMultiError is an error wrapping multiple validation errors
// returned by AFUAttempt.ValidateAll() if the designated constraints aren't met.
type AFUAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AFUAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AFUAttemptMultiError) AllErrors() []error { return m }

// AFUAttemptValidationError is the validation error returned by
// AFUAttempt.Validate if the designated constraints aren't met.
type AFUAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AFUAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AFUAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AFUAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AFUAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AFUAttemptValidationError) ErrorName() string { return "AFUAttemptValidationError" }

// Error satisfies the builtin error interface
func (e AFUAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAFUAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AFUAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AFUAttemptValidationError{}

// Validate checks the field values on ScreenerChecksInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScreenerChecksInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenerChecksInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScreenerChecksInfoMultiError, or nil if none found.
func (m *ScreenerChecksInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenerChecksInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CreditReportFailCount

	if len(errors) > 0 {
		return ScreenerChecksInfoMultiError(errors)
	}

	return nil
}

// ScreenerChecksInfoMultiError is an error wrapping multiple validation errors
// returned by ScreenerChecksInfo.ValidateAll() if the designated constraints
// aren't met.
type ScreenerChecksInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenerChecksInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenerChecksInfoMultiError) AllErrors() []error { return m }

// ScreenerChecksInfoValidationError is the validation error returned by
// ScreenerChecksInfo.Validate if the designated constraints aren't met.
type ScreenerChecksInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenerChecksInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenerChecksInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenerChecksInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenerChecksInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenerChecksInfoValidationError) ErrorName() string {
	return "ScreenerChecksInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ScreenerChecksInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenerChecksInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenerChecksInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenerChecksInfoValidationError{}

// Validate checks the field values on AccountInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AccountInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AccountInfoMultiError, or
// nil if none found.
func (m *AccountInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tier

	// no validation rules for CreatedAt

	if len(errors) > 0 {
		return AccountInfoMultiError(errors)
	}

	return nil
}

// AccountInfoMultiError is an error wrapping multiple validation errors
// returned by AccountInfo.ValidateAll() if the designated constraints aren't met.
type AccountInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountInfoMultiError) AllErrors() []error { return m }

// AccountInfoValidationError is the validation error returned by
// AccountInfo.Validate if the designated constraints aren't met.
type AccountInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountInfoValidationError) ErrorName() string { return "AccountInfoValidationError" }

// Error satisfies the builtin error interface
func (e AccountInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountInfoValidationError{}

// Validate checks the field values on GetCasePrioritisationScoreRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCasePrioritisationScoreRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCasePrioritisationScoreRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCasePrioritisationScoreRequestMultiError, or nil if none found.
func (m *GetCasePrioritisationScoreRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCasePrioritisationScoreRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAlertWithRuleDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCasePrioritisationScoreRequestValidationError{
						field:  fmt.Sprintf("AlertWithRuleDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCasePrioritisationScoreRequestValidationError{
						field:  fmt.Sprintf("AlertWithRuleDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCasePrioritisationScoreRequestValidationError{
					field:  fmt.Sprintf("AlertWithRuleDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ActorId

	// no validation rules for RequestId

	// no validation rules for ModelVersion

	if all {
		switch v := interface{}(m.GetCaseDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequestValidationError{
					field:  "CaseDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequestValidationError{
					field:  "CaseDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCaseDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCasePrioritisationScoreRequestValidationError{
				field:  "CaseDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCasePrioritisationScoreRequestMultiError(errors)
	}

	return nil
}

// GetCasePrioritisationScoreRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetCasePrioritisationScoreRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCasePrioritisationScoreRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCasePrioritisationScoreRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCasePrioritisationScoreRequestMultiError) AllErrors() []error { return m }

// GetCasePrioritisationScoreRequestValidationError is the validation error
// returned by GetCasePrioritisationScoreRequest.Validate if the designated
// constraints aren't met.
type GetCasePrioritisationScoreRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCasePrioritisationScoreRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCasePrioritisationScoreRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCasePrioritisationScoreRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCasePrioritisationScoreRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCasePrioritisationScoreRequestValidationError) ErrorName() string {
	return "GetCasePrioritisationScoreRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCasePrioritisationScoreRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCasePrioritisationScoreRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCasePrioritisationScoreRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCasePrioritisationScoreRequestValidationError{}

// Validate checks the field values on ModelResponseInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ModelResponseInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModelResponseInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModelResponseInfoMultiError, or nil if none found.
func (m *ModelResponseInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ModelResponseInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Score

	if len(errors) > 0 {
		return ModelResponseInfoMultiError(errors)
	}

	return nil
}

// ModelResponseInfoMultiError is an error wrapping multiple validation errors
// returned by ModelResponseInfo.ValidateAll() if the designated constraints
// aren't met.
type ModelResponseInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModelResponseInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModelResponseInfoMultiError) AllErrors() []error { return m }

// ModelResponseInfoValidationError is the validation error returned by
// ModelResponseInfo.Validate if the designated constraints aren't met.
type ModelResponseInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModelResponseInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModelResponseInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModelResponseInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModelResponseInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModelResponseInfoValidationError) ErrorName() string {
	return "ModelResponseInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ModelResponseInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModelResponseInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModelResponseInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModelResponseInfoValidationError{}

// Validate checks the field values on GetCasePrioritisationScoreResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCasePrioritisationScoreResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCasePrioritisationScoreResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCasePrioritisationScoreResponseMultiError, or nil if none found.
func (m *GetCasePrioritisationScoreResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCasePrioritisationScoreResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for ModelVersion

	// no validation rules for Score

	for idx, item := range m.GetModelInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCasePrioritisationScoreResponseValidationError{
						field:  fmt.Sprintf("ModelInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCasePrioritisationScoreResponseValidationError{
						field:  fmt.Sprintf("ModelInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCasePrioritisationScoreResponseValidationError{
					field:  fmt.Sprintf("ModelInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCasePrioritisationScoreResponseMultiError(errors)
	}

	return nil
}

// GetCasePrioritisationScoreResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetCasePrioritisationScoreResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCasePrioritisationScoreResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCasePrioritisationScoreResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCasePrioritisationScoreResponseMultiError) AllErrors() []error { return m }

// GetCasePrioritisationScoreResponseValidationError is the validation error
// returned by GetCasePrioritisationScoreResponse.Validate if the designated
// constraints aren't met.
type GetCasePrioritisationScoreResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCasePrioritisationScoreResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCasePrioritisationScoreResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCasePrioritisationScoreResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCasePrioritisationScoreResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCasePrioritisationScoreResponseValidationError) ErrorName() string {
	return "GetCasePrioritisationScoreResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCasePrioritisationScoreResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCasePrioritisationScoreResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCasePrioritisationScoreResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCasePrioritisationScoreResponseValidationError{}

// Validate checks the field values on EmploymentData_Range with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EmploymentData_Range) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmploymentData_Range with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EmploymentData_RangeMultiError, or nil if none found.
func (m *EmploymentData_Range) ValidateAll() error {
	return m.validate(true)
}

func (m *EmploymentData_Range) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MinValue

	// no validation rules for MaxValue

	if len(errors) > 0 {
		return EmploymentData_RangeMultiError(errors)
	}

	return nil
}

// EmploymentData_RangeMultiError is an error wrapping multiple validation
// errors returned by EmploymentData_Range.ValidateAll() if the designated
// constraints aren't met.
type EmploymentData_RangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmploymentData_RangeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmploymentData_RangeMultiError) AllErrors() []error { return m }

// EmploymentData_RangeValidationError is the validation error returned by
// EmploymentData_Range.Validate if the designated constraints aren't met.
type EmploymentData_RangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmploymentData_RangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmploymentData_RangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmploymentData_RangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmploymentData_RangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmploymentData_RangeValidationError) ErrorName() string {
	return "EmploymentData_RangeValidationError"
}

// Error satisfies the builtin error interface
func (e EmploymentData_RangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmploymentData_Range.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmploymentData_RangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmploymentData_RangeValidationError{}

// Validate checks the field values on GetCasePrioritisationScoreRequest_Alert
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCasePrioritisationScoreRequest_Alert) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCasePrioritisationScoreRequest_Alert with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetCasePrioritisationScoreRequest_AlertMultiError, or nil if none found.
func (m *GetCasePrioritisationScoreRequest_Alert) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCasePrioritisationScoreRequest_Alert) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CaseId

	// no validation rules for RuleId

	// no validation rules for BatchName

	// no validation rules for ActorId

	// no validation rules for AccountId

	// no validation rules for EntityType

	// no validation rules for EntityId

	// no validation rules for Verdict

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	// no validation rules for AccountType

	// no validation rules for InitiatedAt

	// no validation rules for HandlingType

	// no validation rules for RulePrecision

	if all {
		switch v := interface{}(m.GetMetaDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequest_AlertValidationError{
					field:  "MetaDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequest_AlertValidationError{
					field:  "MetaDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCasePrioritisationScoreRequest_AlertValidationError{
				field:  "MetaDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCasePrioritisationScoreRequest_AlertMultiError(errors)
	}

	return nil
}

// GetCasePrioritisationScoreRequest_AlertMultiError is an error wrapping
// multiple validation errors returned by
// GetCasePrioritisationScoreRequest_Alert.ValidateAll() if the designated
// constraints aren't met.
type GetCasePrioritisationScoreRequest_AlertMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCasePrioritisationScoreRequest_AlertMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCasePrioritisationScoreRequest_AlertMultiError) AllErrors() []error { return m }

// GetCasePrioritisationScoreRequest_AlertValidationError is the validation
// error returned by GetCasePrioritisationScoreRequest_Alert.Validate if the
// designated constraints aren't met.
type GetCasePrioritisationScoreRequest_AlertValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCasePrioritisationScoreRequest_AlertValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCasePrioritisationScoreRequest_AlertValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCasePrioritisationScoreRequest_AlertValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCasePrioritisationScoreRequest_AlertValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCasePrioritisationScoreRequest_AlertValidationError) ErrorName() string {
	return "GetCasePrioritisationScoreRequest_AlertValidationError"
}

// Error satisfies the builtin error interface
func (e GetCasePrioritisationScoreRequest_AlertValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCasePrioritisationScoreRequest_Alert.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCasePrioritisationScoreRequest_AlertValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCasePrioritisationScoreRequest_AlertValidationError{}

// Validate checks the field values on
// GetCasePrioritisationScoreRequest_AlertMetaDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCasePrioritisationScoreRequest_AlertMetaDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCasePrioritisationScoreRequest_AlertMetaDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetCasePrioritisationScoreRequest_AlertMetaDetailsMultiError, or nil if
// none found.
func (m *GetCasePrioritisationScoreRequest_AlertMetaDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCasePrioritisationScoreRequest_AlertMetaDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTransactionBlocks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError{
						field:  fmt.Sprintf("TransactionBlocks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError{
						field:  fmt.Sprintf("TransactionBlocks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError{
					field:  fmt.Sprintf("TransactionBlocks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCasePrioritisationScoreRequest_AlertMetaDetailsMultiError(errors)
	}

	return nil
}

// GetCasePrioritisationScoreRequest_AlertMetaDetailsMultiError is an error
// wrapping multiple validation errors returned by
// GetCasePrioritisationScoreRequest_AlertMetaDetails.ValidateAll() if the
// designated constraints aren't met.
type GetCasePrioritisationScoreRequest_AlertMetaDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCasePrioritisationScoreRequest_AlertMetaDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCasePrioritisationScoreRequest_AlertMetaDetailsMultiError) AllErrors() []error { return m }

// GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError is the
// validation error returned by
// GetCasePrioritisationScoreRequest_AlertMetaDetails.Validate if the
// designated constraints aren't met.
type GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError) ErrorName() string {
	return "GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCasePrioritisationScoreRequest_AlertMetaDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCasePrioritisationScoreRequest_AlertMetaDetailsValidationError{}

// Validate checks the field values on
// GetCasePrioritisationScoreRequest_TransactionBlock with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCasePrioritisationScoreRequest_TransactionBlock) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCasePrioritisationScoreRequest_TransactionBlock with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetCasePrioritisationScoreRequest_TransactionBlockMultiError, or nil if
// none found.
func (m *GetCasePrioritisationScoreRequest_TransactionBlock) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCasePrioritisationScoreRequest_TransactionBlock) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for AlertId

	// no validation rules for AggregatedCredit

	// no validation rules for AggregatedDebit

	// no validation rules for DurationSeconds

	// no validation rules for BlockType

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	// no validation rules for DeletedAtUnix

	if len(errors) > 0 {
		return GetCasePrioritisationScoreRequest_TransactionBlockMultiError(errors)
	}

	return nil
}

// GetCasePrioritisationScoreRequest_TransactionBlockMultiError is an error
// wrapping multiple validation errors returned by
// GetCasePrioritisationScoreRequest_TransactionBlock.ValidateAll() if the
// designated constraints aren't met.
type GetCasePrioritisationScoreRequest_TransactionBlockMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCasePrioritisationScoreRequest_TransactionBlockMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCasePrioritisationScoreRequest_TransactionBlockMultiError) AllErrors() []error { return m }

// GetCasePrioritisationScoreRequest_TransactionBlockValidationError is the
// validation error returned by
// GetCasePrioritisationScoreRequest_TransactionBlock.Validate if the
// designated constraints aren't met.
type GetCasePrioritisationScoreRequest_TransactionBlockValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCasePrioritisationScoreRequest_TransactionBlockValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetCasePrioritisationScoreRequest_TransactionBlockValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCasePrioritisationScoreRequest_TransactionBlockValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetCasePrioritisationScoreRequest_TransactionBlockValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCasePrioritisationScoreRequest_TransactionBlockValidationError) ErrorName() string {
	return "GetCasePrioritisationScoreRequest_TransactionBlockValidationError"
}

// Error satisfies the builtin error interface
func (e GetCasePrioritisationScoreRequest_TransactionBlockValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCasePrioritisationScoreRequest_TransactionBlock.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCasePrioritisationScoreRequest_TransactionBlockValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCasePrioritisationScoreRequest_TransactionBlockValidationError{}

// Validate checks the field values on GetCasePrioritisationScoreRequest_Rule
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCasePrioritisationScoreRequest_Rule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCasePrioritisationScoreRequest_Rule with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetCasePrioritisationScoreRequest_RuleMultiError, or nil if none found.
func (m *GetCasePrioritisationScoreRequest_Rule) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCasePrioritisationScoreRequest_Rule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Version

	// no validation rules for Description

	// no validation rules for EvaluationMethod

	// no validation rules for Provenance

	// no validation rules for State

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	// no validation rules for DeletedAt

	// no validation rules for ExternalId

	// no validation rules for AssessedEntityType

	// no validation rules for TxnSuspectEntity

	// no validation rules for RuleGroup

	// no validation rules for AddedByEmail

	// no validation rules for SeedPrecision

	// no validation rules for ForceUseSeedPrecision

	if len(errors) > 0 {
		return GetCasePrioritisationScoreRequest_RuleMultiError(errors)
	}

	return nil
}

// GetCasePrioritisationScoreRequest_RuleMultiError is an error wrapping
// multiple validation errors returned by
// GetCasePrioritisationScoreRequest_Rule.ValidateAll() if the designated
// constraints aren't met.
type GetCasePrioritisationScoreRequest_RuleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCasePrioritisationScoreRequest_RuleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCasePrioritisationScoreRequest_RuleMultiError) AllErrors() []error { return m }

// GetCasePrioritisationScoreRequest_RuleValidationError is the validation
// error returned by GetCasePrioritisationScoreRequest_Rule.Validate if the
// designated constraints aren't met.
type GetCasePrioritisationScoreRequest_RuleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCasePrioritisationScoreRequest_RuleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCasePrioritisationScoreRequest_RuleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCasePrioritisationScoreRequest_RuleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCasePrioritisationScoreRequest_RuleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCasePrioritisationScoreRequest_RuleValidationError) ErrorName() string {
	return "GetCasePrioritisationScoreRequest_RuleValidationError"
}

// Error satisfies the builtin error interface
func (e GetCasePrioritisationScoreRequest_RuleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCasePrioritisationScoreRequest_Rule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCasePrioritisationScoreRequest_RuleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCasePrioritisationScoreRequest_RuleValidationError{}

// Validate checks the field values on
// GetCasePrioritisationScoreRequest_ExtendedRule with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetCasePrioritisationScoreRequest_ExtendedRule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCasePrioritisationScoreRequest_ExtendedRule with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetCasePrioritisationScoreRequest_ExtendedRuleMultiError, or nil if none found.
func (m *GetCasePrioritisationScoreRequest_ExtendedRule) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCasePrioritisationScoreRequest_ExtendedRule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequest_ExtendedRuleValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequest_ExtendedRuleValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCasePrioritisationScoreRequest_ExtendedRuleValidationError{
				field:  "Rule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetReviewTypeDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCasePrioritisationScoreRequest_ExtendedRuleValidationError{
						field:  fmt.Sprintf("ReviewTypeDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCasePrioritisationScoreRequest_ExtendedRuleValidationError{
						field:  fmt.Sprintf("ReviewTypeDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCasePrioritisationScoreRequest_ExtendedRuleValidationError{
					field:  fmt.Sprintf("ReviewTypeDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCasePrioritisationScoreRequest_ExtendedRuleMultiError(errors)
	}

	return nil
}

// GetCasePrioritisationScoreRequest_ExtendedRuleMultiError is an error
// wrapping multiple validation errors returned by
// GetCasePrioritisationScoreRequest_ExtendedRule.ValidateAll() if the
// designated constraints aren't met.
type GetCasePrioritisationScoreRequest_ExtendedRuleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCasePrioritisationScoreRequest_ExtendedRuleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCasePrioritisationScoreRequest_ExtendedRuleMultiError) AllErrors() []error { return m }

// GetCasePrioritisationScoreRequest_ExtendedRuleValidationError is the
// validation error returned by
// GetCasePrioritisationScoreRequest_ExtendedRule.Validate if the designated
// constraints aren't met.
type GetCasePrioritisationScoreRequest_ExtendedRuleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCasePrioritisationScoreRequest_ExtendedRuleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCasePrioritisationScoreRequest_ExtendedRuleValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCasePrioritisationScoreRequest_ExtendedRuleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCasePrioritisationScoreRequest_ExtendedRuleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCasePrioritisationScoreRequest_ExtendedRuleValidationError) ErrorName() string {
	return "GetCasePrioritisationScoreRequest_ExtendedRuleValidationError"
}

// Error satisfies the builtin error interface
func (e GetCasePrioritisationScoreRequest_ExtendedRuleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCasePrioritisationScoreRequest_ExtendedRule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCasePrioritisationScoreRequest_ExtendedRuleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCasePrioritisationScoreRequest_ExtendedRuleValidationError{}

// Validate checks the field values on
// GetCasePrioritisationScoreRequest_ReviewTypeDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCasePrioritisationScoreRequest_ReviewTypeDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCasePrioritisationScoreRequest_ReviewTypeDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetCasePrioritisationScoreRequest_ReviewTypeDetailsMultiError, or nil if
// none found.
func (m *GetCasePrioritisationScoreRequest_ReviewTypeDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCasePrioritisationScoreRequest_ReviewTypeDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReviewType

	if len(errors) > 0 {
		return GetCasePrioritisationScoreRequest_ReviewTypeDetailsMultiError(errors)
	}

	return nil
}

// GetCasePrioritisationScoreRequest_ReviewTypeDetailsMultiError is an error
// wrapping multiple validation errors returned by
// GetCasePrioritisationScoreRequest_ReviewTypeDetails.ValidateAll() if the
// designated constraints aren't met.
type GetCasePrioritisationScoreRequest_ReviewTypeDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCasePrioritisationScoreRequest_ReviewTypeDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCasePrioritisationScoreRequest_ReviewTypeDetailsMultiError) AllErrors() []error { return m }

// GetCasePrioritisationScoreRequest_ReviewTypeDetailsValidationError is the
// validation error returned by
// GetCasePrioritisationScoreRequest_ReviewTypeDetails.Validate if the
// designated constraints aren't met.
type GetCasePrioritisationScoreRequest_ReviewTypeDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCasePrioritisationScoreRequest_ReviewTypeDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetCasePrioritisationScoreRequest_ReviewTypeDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCasePrioritisationScoreRequest_ReviewTypeDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetCasePrioritisationScoreRequest_ReviewTypeDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCasePrioritisationScoreRequest_ReviewTypeDetailsValidationError) ErrorName() string {
	return "GetCasePrioritisationScoreRequest_ReviewTypeDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetCasePrioritisationScoreRequest_ReviewTypeDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCasePrioritisationScoreRequest_ReviewTypeDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCasePrioritisationScoreRequest_ReviewTypeDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCasePrioritisationScoreRequest_ReviewTypeDetailsValidationError{}

// Validate checks the field values on
// GetCasePrioritisationScoreRequest_AlertWithRuleDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCasePrioritisationScoreRequest_AlertWithRuleDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCasePrioritisationScoreRequest_AlertWithRuleDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCasePrioritisationScoreRequest_AlertWithRuleDetailsMultiError, or nil if
// none found.
func (m *GetCasePrioritisationScoreRequest_AlertWithRuleDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCasePrioritisationScoreRequest_AlertWithRuleDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAlert()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError{
					field:  "Alert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError{
					field:  "Alert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAlert()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError{
				field:  "Alert",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError{
				field:  "Rule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExtendedRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError{
					field:  "ExtendedRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError{
					field:  "ExtendedRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtendedRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError{
				field:  "ExtendedRule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCasePrioritisationScoreRequest_AlertWithRuleDetailsMultiError(errors)
	}

	return nil
}

// GetCasePrioritisationScoreRequest_AlertWithRuleDetailsMultiError is an error
// wrapping multiple validation errors returned by
// GetCasePrioritisationScoreRequest_AlertWithRuleDetails.ValidateAll() if the
// designated constraints aren't met.
type GetCasePrioritisationScoreRequest_AlertWithRuleDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCasePrioritisationScoreRequest_AlertWithRuleDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCasePrioritisationScoreRequest_AlertWithRuleDetailsMultiError) AllErrors() []error {
	return m
}

// GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError is the
// validation error returned by
// GetCasePrioritisationScoreRequest_AlertWithRuleDetails.Validate if the
// designated constraints aren't met.
type GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError) ErrorName() string {
	return "GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCasePrioritisationScoreRequest_AlertWithRuleDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCasePrioritisationScoreRequest_AlertWithRuleDetailsValidationError{}

// Validate checks the field values on
// GetCasePrioritisationScoreRequest_CaseDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetCasePrioritisationScoreRequest_CaseDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCasePrioritisationScoreRequest_CaseDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetCasePrioritisationScoreRequest_CaseDetailsMultiError, or nil if none found.
func (m *GetCasePrioritisationScoreRequest_CaseDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCasePrioritisationScoreRequest_CaseDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaseId

	for idx, item := range m.GetAlertWithRuleDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCasePrioritisationScoreRequest_CaseDetailsValidationError{
						field:  fmt.Sprintf("AlertWithRuleDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCasePrioritisationScoreRequest_CaseDetailsValidationError{
						field:  fmt.Sprintf("AlertWithRuleDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCasePrioritisationScoreRequest_CaseDetailsValidationError{
					field:  fmt.Sprintf("AlertWithRuleDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCasePrioritisationScoreRequest_CaseDetailsMultiError(errors)
	}

	return nil
}

// GetCasePrioritisationScoreRequest_CaseDetailsMultiError is an error wrapping
// multiple validation errors returned by
// GetCasePrioritisationScoreRequest_CaseDetails.ValidateAll() if the
// designated constraints aren't met.
type GetCasePrioritisationScoreRequest_CaseDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCasePrioritisationScoreRequest_CaseDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCasePrioritisationScoreRequest_CaseDetailsMultiError) AllErrors() []error { return m }

// GetCasePrioritisationScoreRequest_CaseDetailsValidationError is the
// validation error returned by
// GetCasePrioritisationScoreRequest_CaseDetails.Validate if the designated
// constraints aren't met.
type GetCasePrioritisationScoreRequest_CaseDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCasePrioritisationScoreRequest_CaseDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCasePrioritisationScoreRequest_CaseDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCasePrioritisationScoreRequest_CaseDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCasePrioritisationScoreRequest_CaseDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCasePrioritisationScoreRequest_CaseDetailsValidationError) ErrorName() string {
	return "GetCasePrioritisationScoreRequest_CaseDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetCasePrioritisationScoreRequest_CaseDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCasePrioritisationScoreRequest_CaseDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCasePrioritisationScoreRequest_CaseDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCasePrioritisationScoreRequest_CaseDetailsValidationError{}
