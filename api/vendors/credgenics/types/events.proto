/**
Vendor Ref: https://docs.credgenics.com/#webhook-events
 */

syntax = "proto3";

package vendors.credgenics.types;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendors/credgenics/types";
option java_package = "com.github.epifi.gamma.api.vendors.credgenics.types";

message CommsError {
  // Specific error if comms failed
  string error = 1 [json_name = "error"];
  string notice_link = 2 [json_name = "notice_link"];
  string notice_type = 3 [json_name = "notice_type"];
  google.protobuf.Struct notice_type_map = 4 [json_name = "notice_type_map"];
}

message EmailResponse {
  CommsError error = 1 [json_name = "error"];
  // e.g. "9018f9bcab894a7f8e36143eb90acc72"
  string shoot_id = 2 [json_name = "shoot_id"];
  // e.g. "<EMAIL>"
  string to = 3 [json_name = "to"];
  // e.g. "applicant"
  string applicant_type = 4 [json_name = "applicant_type"];
  // e.g. null
  string transaction_id = 5 [json_name = "transaction_id"];
  // e.g. true
  bool success = 6 [json_name = "success"];
  // e.g. 200
  int32 status_code = 7 [json_name = "status_code"];
  // e.g. "<EMAIL>"
  string cc = 8 [json_name = "cc"];
  // e.g. "Analog Fintech Credgenics Trademarks <<EMAIL>>"
  string from = 9 [json_name = "from"];
  // e.g. "<EMAIL>"
  string email_reply_to = 10 [json_name = "email_reply_to"];
  // e.g. "test email<br/>"
  string email_body = 11 [json_name = "email_body"];
  // e.g. "1137_test"
  string email_subject = 12 [json_name = "email_subject"];
  // e.g. "test"
  string template_name = 13 [json_name = "template_name"];
  // e.g. "2022-12-29 17:38:54.572208"
  string triggered_time = 14 [json_name = "triggered_time"];
}

message EmailEventPayload {
  string loan_id = 1 [json_name = "loan_id"];
  string event = 2 [json_name = "event"];

  message EmailEventData {
    repeated EmailResponse response = 1 [json_name = "response"];
    string allocation_month = 2 [json_name = "allocation_month"];
    string author = 3 [json_name = "author"];
  }

  EmailEventData data = 3 [json_name = "data"];
  string event_id = 4 [json_name = "event_id"];
  // timestamp field stores the time the event was recieved by the backend
  google.protobuf.Timestamp timestamp = 5 [json_name = "timestamp"];
}


message SmsResponse {
  // e.g. "applicant"
  string applicant_type = 1 [json_name = "applicant_type"];
  string transaction_id = 2 [json_name = "transaction_id"];
  // e.g. "9999999999"
  string to = 3 [json_name = "to"];
  CommsError error = 4 [json_name = "error"];
  // e.g. true
  bool success = 5 [json_name = "success"];
  // e.g. 200
  int32 status_code = 6 [json_name = "status_code"];
  // e.g. "fd80b0c1824b41808fe2e95b2a85d3d7"
  string shoot_id = 7 [json_name = "shoot_id"];
  // e.g. "Test sms"
  string sms_body = 8 [json_name = "sms_body"];
  // e.g. "en"
  string sms_language = 9 [json_name = "sms_language"];
  // e.g. "LRN-DLT"
  string template_name = 10 [json_name = "template_name"];
  // e.g. "1107161827754551896"
  string client_template_id = 11 [json_name = "client_template_id"];
  // e.g. "2022-12-29 17:36:14.374311"
  string triggered_time = 12 [json_name = "triggered_time"];
}

message SmsEventPayload {
  string loan_id = 1 [json_name = "loan_id"];
  string event = 2 [json_name = "event"];

  message SmsEventData {
    repeated SmsResponse response = 1 [json_name = "response"];
    string allocation_month = 2 [json_name = "allocation_month"];
    string author = 3 [json_name = "author"];
  }

  SmsEventData data = 3 [json_name = "data"];
  string event_id = 4 [json_name = "event_id"];
  // timestamp field stores the time the event was recieved by the backend
  google.protobuf.Timestamp timestamp = 5 [json_name = "timestamp"];
}

message VoiceResponse {
  // e.g. "applicant"
  string applicant_type = 1 [json_name = "applicant_type"];
  string transaction_id = 2 [json_name = "transaction_id"];
  // e.g. "9999999999"
  string to = 3 [json_name = "to"];
  CommsError error = 4 [json_name = "error"];
  // e.g. true
  bool success = 5 [json_name = "success"];
  // e.g. 200
  int32 status_code = 6 [json_name = "status_code"];
  // e.g. "e15ae043d1a242479d4c24d5e2df34e9"
  string shoot_id = 7 [json_name = "shoot_id"];
  // e.g. "test message"
  string voice_body = 8 [json_name = "voice_body"];
  // e.g. "en-in"
  string voice_language = 9 [json_name = "voice_language"];
  // e.g. "female"
  string voice_gender = 10 [json_name = "voice_gender"];
  // e.g. "English Language"
  string template_name = 11 [json_name = "template_name"];
  // e.g. "2022-12-29 17:40:35.081658"
  string triggered_time = 12 [json_name = "triggered_time"];
}

message VoiceMessageEventPayload {
  string loan_id = 1 [json_name = "loan_id"];
  string event = 2 [json_name = "event"];

  message VoiceEventData {
    repeated VoiceResponse response = 1 [json_name = "response"];
    string allocation_month = 2 [json_name = "allocation_month"];
    string author = 3 [json_name = "author"];
  }

  VoiceEventData data = 3 [json_name = "data"];
  string event_id = 4 [json_name = "event_id"];
  // timestamp field stores the time the event was recieved by the backend
  google.protobuf.Timestamp timestamp = 5 [json_name = "timestamp"];
}

message WhatsappResponse {
  // e.g. "applicant"
  string applicant_type = 1 [json_name = "applicant_type"];
  string transaction_id = 2 [json_name = "transaction_id"];
  // e.g. "9999999999"
  string to = 3 [json_name = "to"];
  CommsError error = 4 [json_name = "error"];
  // e.g. true
  bool success = 5 [json_name = "success"];
  // e.g. 200
  int32 status_code = 6 [json_name = "status_code"];
  // e.g. "db9a14653c6448b3b16e0ff71a9f8624"
  string shoot_id = 7 [json_name = "shoot_id"];
  // e.g. "Hi, this is test message"
  string whatsapp_body = 8 [json_name = "whatsapp_body"];
  // e.g. "6416443"
  string client_template_id = 9 [json_name = "client_template_id"];
  // e.g. "Test Template"
  string template_name = 10 [json_name = "template_name"];
  // e.g. "2022-12-29 18:04:19.772659"
  string triggered_time = 11 [json_name = "triggered_time"];
}

message WhatsappEventPayload {
  string loan_id = 1 [json_name = "loan_id"];
  string event = 2 [json_name = "event"];

  message WhatsappEventData {
    repeated WhatsappResponse response = 1 [json_name = "response"];
    string allocation_month = 2 [json_name = "allocation_month"];
    string author = 3 [json_name = "author"];
  }

  WhatsappEventData data = 3 [json_name = "data"];
  string event_id = 4 [json_name = "event_id"];
  // timestamp field stores the time the event was recieved by the backend
  google.protobuf.Timestamp timestamp = 5 [json_name = "timestamp"];
}


message CallingEventPayload {
  string loan_id = 1 [json_name = "loan_id"];
  string event = 2 [json_name = "event"];

  message CallingEventData {
    // e.g. "CN800"
    string campaign_name = 1 [json_name = "Campaign Name"];
    // e.g. "f545eaac-d296-4831-9002-055e90f2ded1"
    string campaign_id = 2 [json_name = "Campaign ID"];
    // e.g. "1070"
    string loan_id = 3 [json_name = "Loan ID"];
    string dpd_bucket = 4 [json_name = "DPD Bucket"];
    // e.g. "2023-5-01"
    string allocation_month = 5 [json_name = "Allocation Month"];
    // e.g. "manual"
    string call_type = 6 [json_name = "call type"];
    // e.g. "prk38"
    string agent_name = 7 [json_name = "Agent Name"];
    // e.g. "<EMAIL>"
    string agent_email_id = 8 [json_name = "Agent Email ID"];
    // e.g. "65752148"
    string did_number = 9 [json_name = "DID Number"];
    // e.g. "fe2cebdb-422b-4c25-aedd-62bd284e21fe"
    string shoot_id = 10 [json_name = "Shoot ID"];
    // e.g. "2023-05-15 17:43:08"
    string call_start_time = 11 [json_name = "Call Start Time"];
    // e.g. "2023-05-15 17:43:09"
    string customer_leg_ringing_start_time = 12 [json_name = "Customer Leg Ringing Start Time"];
    // e.g. "2023-05-15 17:43:17"
    string customer_leg_ringing_end_time = 13 [json_name = "Customer Leg Ringing End Time"];
    // e.g. "2023-05-15 17:43:09"
    string agent_leg_ringing_start_time = 14 [json_name = "Agent Leg Ringing Start Time"];
    // e.g. "2023-05-15 17:43:09"
    string agent_leg_ringing_end_time = 15 [json_name = "Agent Leg Ringing End Time"];
    // e.g. "00:00:08"
    string total_ringing_time_duration = 16 [json_name = "Total Ringing Time Duration"];
    // e.g. "2023-05-15 17:43:17"
    string customer_call_pickup_time = 17 [json_name = "Customer Call Pickup Time"];
    // e.g. "2023-05-15 17:43:09"
    string agent_call_pickup_time = 18 [json_name = "Agent Call Pickup Time"];
    // e.g. "2023-05-15 17:43:21"
    string call_end_time = 19 [json_name = "Call End Time"];
    // e.g. "00:00:03"
    string total_talk_time_duration = 20 [json_name = "Total Talk Time Duration"];
    // e.g. "00:00:13"
    string total_call_duration = 21 [json_name = "Total Call Duration"];
    // e.g. "00:00:03"
    string customer_wait_duration = 22 [json_name = "Customer Wait Duration"];
    // e.g. "2023-05-15 17:43:21"
    string wrapup_start_time = 23 [json_name = "Wrapup Start Time"];
    // e.g. "2023-05-15 17:43:25"
    string wrapup_end_time = 24 [json_name = "Wrapup End Time"];
    // e.g. "00:00:04"
    string wrapup_duration = 25 [json_name = "Wrapup Duration"];
    // e.g. "customer"
    string call_disconnected_by = 26 [json_name = "Call Disconnected By"];
    // e.g. "After Customer Picked"
    string call_disconnection_stage = 27 [json_name = "Call Disconnection Stage"];
    // e.g. "Answered"
    string call_status = 28 [json_name = "Call Status"];
    // e.g. "Confirmation Pending"
    string disposition = 29 [json_name = "Disposition"];
    string sub_disposition_1 = 30 [json_name = "Sub Disposition 1"];
    string sub_disposition_2 = 31 [json_name = "Sub Disposition 2"];
    // e.g. "Normal Clearing"
    string dialer_disposition = 32 [json_name = "Dialer Disposition"];
    string call_transfer_from = 33 [json_name = "Call Transfer From"];
    string call_transfer_to = 34 [json_name = "Call Transfer To"];
    string call_transfer_type = 35 [json_name = "Call Transfer Type"];
    string call_response = 36 [json_name = "Call Response"];
    string reminder_date = 37 [json_name = "Reminder Date"];
    float committed_amount = 38 [json_name = "Committed Amount"];
    // e.g. "calling"
    string role = 39 [json_name = "Role"];
    // e.g. "https://app.credgenics.com/dialer/recording?id=fe2cebdb-422b-4c25-aedd-62bd284e21fe"
    string recording_link = 40 [json_name = "recording_link"];
    string due_date = 41 [json_name = "Due Date"];
    string allocation_date = 42 [json_name = "Allocation Date"];
    // e.g. "Shiva_70"
    string account_id = 43 [json_name = "Account Id"];
    string product_type = 44 [json_name = "product_type"];
    AdditionalDetails additional_fields = 45 [json_name = "additional_fields"];
  }

  message AdditionalDetails {
    string educated = 1 [json_name = "Educated"];
    string customer_tone = 2 [json_name = "Customer Tone"];
    string intention_to_pay = 3 [json_name = "Intention to Pay "];
    string manager_to_review = 4 [json_name = "Manager to Review"];
    string low_risk_defaulter = 5 [json_name = "Low Risk Defaulter"];
    string reason_for_default = 6 [json_name = "Reason for Deafult"];
    string aware_of_default_consequences = 7 [json_name = "Aware of Default Consequences"];
  }


  CallingEventData data = 3 [json_name = "data"];

  string event_id = 4 [json_name = "event_id"];
  // timestamp field stores the time the event was recieved by the backend
  google.protobuf.Timestamp timestamp = 5 [json_name = "timestamp"];
}

message BillzyPaymentEventPayload {
       //loan id as provided by vendor
       string loan_id=1 [json_name = "loan_id"];
       //amount received
       float amount=2 [json_name = "amount"];
       //status of transaction
       string status=3 [json_name = "status"];
       //eg UPI
       string payment_mode=4 [json_name = "payment_mode"];
       //ref number as provided by credgenics
       string order_id=5 [json_name = "order_id"];
       //timestamp on which payment was made
       string pg_success_datetime=6 [json_name = "pg_success_datetime"];
       //date on which loan was uploaded
       string allocation_month=7 [json_name = "allocation_month"];
       //UUID format - unique id for company on CG platform
       string company_id=8 [json_name = "company_id"];
       //webhook ID
       string event_id=9 [json_name = "event_id"];
       //trust name present in loan data
       string trust_name=10 [json_name = "trust_name"];
       // eg payment
       string event=11 [json_name = "event"];
       string amount_type=12 [json_name = "amount_type"];
}