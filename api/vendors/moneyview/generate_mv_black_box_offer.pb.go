// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/moneyview/generate_mv_black_box_offer.proto

package moneyview

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GenerateMvBlackBoxOfferVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeadUserRef    string       `protobuf:"bytes,1,opt,name=leadUserRef,proto3" json:"leadUserRef,omitempty"`
	LeadSource     string       `protobuf:"bytes,2,opt,name=leadSource,proto3" json:"leadSource,omitempty"`
	DataFormat     string       `protobuf:"bytes,3,opt,name=dataFormat,proto3" json:"dataFormat,omitempty"`
	Pincode        string       `protobuf:"bytes,4,opt,name=pincode,proto3" json:"pincode,omitempty"`
	EmploymentType string       `protobuf:"bytes,5,opt,name=employmentType,proto3" json:"employmentType,omitempty"`
	DeclaredIncome float64      `protobuf:"fixed64,6,opt,name=declaredIncome,proto3" json:"declaredIncome,omitempty"`
	Dob            string       `protobuf:"bytes,7,opt,name=dob,proto3" json:"dob,omitempty"`
	BureauProvider string       `protobuf:"bytes,8,opt,name=bureauProvider,proto3" json:"bureauProvider,omitempty"`
	PkgInfoData    *PkgInfoData `protobuf:"bytes,9,opt,name=pkgInfoData,proto3" json:"pkgInfoData,omitempty"`
	SmsData        *SmsData     `protobuf:"bytes,10,opt,name=smsData,proto3" json:"smsData,omitempty"`
	BureauDataStr  string       `protobuf:"bytes,11,opt,name=bureauDataStr,proto3" json:"bureauDataStr,omitempty"`
}

func (x *GenerateMvBlackBoxOfferVendorRequest) Reset() {
	*x = GenerateMvBlackBoxOfferVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateMvBlackBoxOfferVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateMvBlackBoxOfferVendorRequest) ProtoMessage() {}

func (x *GenerateMvBlackBoxOfferVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateMvBlackBoxOfferVendorRequest.ProtoReflect.Descriptor instead.
func (*GenerateMvBlackBoxOfferVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescGZIP(), []int{0}
}

func (x *GenerateMvBlackBoxOfferVendorRequest) GetLeadUserRef() string {
	if x != nil {
		return x.LeadUserRef
	}
	return ""
}

func (x *GenerateMvBlackBoxOfferVendorRequest) GetLeadSource() string {
	if x != nil {
		return x.LeadSource
	}
	return ""
}

func (x *GenerateMvBlackBoxOfferVendorRequest) GetDataFormat() string {
	if x != nil {
		return x.DataFormat
	}
	return ""
}

func (x *GenerateMvBlackBoxOfferVendorRequest) GetPincode() string {
	if x != nil {
		return x.Pincode
	}
	return ""
}

func (x *GenerateMvBlackBoxOfferVendorRequest) GetEmploymentType() string {
	if x != nil {
		return x.EmploymentType
	}
	return ""
}

func (x *GenerateMvBlackBoxOfferVendorRequest) GetDeclaredIncome() float64 {
	if x != nil {
		return x.DeclaredIncome
	}
	return 0
}

func (x *GenerateMvBlackBoxOfferVendorRequest) GetDob() string {
	if x != nil {
		return x.Dob
	}
	return ""
}

func (x *GenerateMvBlackBoxOfferVendorRequest) GetBureauProvider() string {
	if x != nil {
		return x.BureauProvider
	}
	return ""
}

func (x *GenerateMvBlackBoxOfferVendorRequest) GetPkgInfoData() *PkgInfoData {
	if x != nil {
		return x.PkgInfoData
	}
	return nil
}

func (x *GenerateMvBlackBoxOfferVendorRequest) GetSmsData() *SmsData {
	if x != nil {
		return x.SmsData
	}
	return nil
}

func (x *GenerateMvBlackBoxOfferVendorRequest) GetBureauDataStr() string {
	if x != nil {
		return x.BureauDataStr
	}
	return ""
}

type PkgInfoData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PkgInfoDTOList []*PkgInfo `protobuf:"bytes,1,rep,name=pkgInfoDTOList,proto3" json:"pkgInfoDTOList,omitempty"`
}

func (x *PkgInfoData) Reset() {
	*x = PkgInfoData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgInfoData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgInfoData) ProtoMessage() {}

func (x *PkgInfoData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgInfoData.ProtoReflect.Descriptor instead.
func (*PkgInfoData) Descriptor() ([]byte, []int) {
	return file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescGZIP(), []int{1}
}

func (x *PkgInfoData) GetPkgInfoDTOList() []*PkgInfo {
	if x != nil {
		return x.PkgInfoDTOList
	}
	return nil
}

type PkgInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PkgName     string `protobuf:"bytes,1,opt,name=pkgName,proto3" json:"pkgName,omitempty"`
	UserId      string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	DateCreated int64  `protobuf:"varint,3,opt,name=dateCreated,proto3" json:"dateCreated,omitempty"`
}

func (x *PkgInfo) Reset() {
	*x = PkgInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgInfo) ProtoMessage() {}

func (x *PkgInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgInfo.ProtoReflect.Descriptor instead.
func (*PkgInfo) Descriptor() ([]byte, []int) {
	return file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescGZIP(), []int{2}
}

func (x *PkgInfo) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *PkgInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PkgInfo) GetDateCreated() int64 {
	if x != nil {
		return x.DateCreated
	}
	return 0
}

type SmsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SmsList []*Sms `protobuf:"bytes,1,rep,name=smsList,proto3" json:"smsList,omitempty"`
}

func (x *SmsData) Reset() {
	*x = SmsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmsData) ProtoMessage() {}

func (x *SmsData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmsData.ProtoReflect.Descriptor instead.
func (*SmsData) Descriptor() ([]byte, []int) {
	return file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescGZIP(), []int{3}
}

func (x *SmsData) GetSmsList() []*Sms {
	if x != nil {
		return x.SmsList
	}
	return nil
}

type Sms struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg     string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	Date    int64  `protobuf:"varint,3,opt,name=date,proto3" json:"date,omitempty"`
}

func (x *Sms) Reset() {
	*x = Sms{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Sms) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sms) ProtoMessage() {}

func (x *Sms) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sms.ProtoReflect.Descriptor instead.
func (*Sms) Descriptor() ([]byte, []int) {
	return file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescGZIP(), []int{4}
}

func (x *Sms) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *Sms) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Sms) GetDate() int64 {
	if x != nil {
		return x.Date
	}
	return 0
}

type GenerateMvBlackBoxOfferVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Body    *VendorResponseBody `protobuf:"bytes,1,opt,name=body,proto3" json:"body,omitempty"`
	Message string              `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Success bool                `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *GenerateMvBlackBoxOfferVendorResponse) Reset() {
	*x = GenerateMvBlackBoxOfferVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateMvBlackBoxOfferVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateMvBlackBoxOfferVendorResponse) ProtoMessage() {}

func (x *GenerateMvBlackBoxOfferVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateMvBlackBoxOfferVendorResponse.ProtoReflect.Descriptor instead.
func (*GenerateMvBlackBoxOfferVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescGZIP(), []int{5}
}

func (x *GenerateMvBlackBoxOfferVendorResponse) GetBody() *VendorResponseBody {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *GenerateMvBlackBoxOfferVendorResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GenerateMvBlackBoxOfferVendorResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type VendorResponseBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeadUserRef      string  `protobuf:"bytes,1,opt,name=leadUserRef,proto3" json:"leadUserRef,omitempty"`
	Status           string  `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	BestOfferAmount  float64 `protobuf:"fixed64,3,opt,name=bestOfferAmount,proto3" json:"bestOfferAmount,omitempty"`
	BestOfferTenure  int32   `protobuf:"varint,4,opt,name=bestOfferTenure,proto3" json:"bestOfferTenure,omitempty"`
	BestOfferRoi     float64 `protobuf:"fixed64,5,opt,name=bestOfferRoi,proto3" json:"bestOfferRoi,omitempty"`
	ExpiryDate       string  `protobuf:"bytes,6,opt,name=expiryDate,proto3" json:"expiryDate,omitempty"`
	Category         string  `protobuf:"bytes,7,opt,name=category,proto3" json:"category,omitempty"`
	LeadSource       string  `protobuf:"bytes,8,opt,name=leadSource,proto3" json:"leadSource,omitempty"`
	MetaIncome       string  `protobuf:"bytes,9,opt,name=metaIncome,proto3" json:"metaIncome,omitempty"`
	MetaTagName      string  `protobuf:"bytes,10,opt,name=metaTagName,proto3" json:"metaTagName,omitempty"`
	EmploymentType   string  `protobuf:"bytes,11,opt,name=employmentType,proto3" json:"employmentType,omitempty"`
	SummaryVariables string  `protobuf:"bytes,12,opt,name=summaryVariables,proto3" json:"summaryVariables,omitempty"`
}

func (x *VendorResponseBody) Reset() {
	*x = VendorResponseBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VendorResponseBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VendorResponseBody) ProtoMessage() {}

func (x *VendorResponseBody) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VendorResponseBody.ProtoReflect.Descriptor instead.
func (*VendorResponseBody) Descriptor() ([]byte, []int) {
	return file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescGZIP(), []int{6}
}

func (x *VendorResponseBody) GetLeadUserRef() string {
	if x != nil {
		return x.LeadUserRef
	}
	return ""
}

func (x *VendorResponseBody) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *VendorResponseBody) GetBestOfferAmount() float64 {
	if x != nil {
		return x.BestOfferAmount
	}
	return 0
}

func (x *VendorResponseBody) GetBestOfferTenure() int32 {
	if x != nil {
		return x.BestOfferTenure
	}
	return 0
}

func (x *VendorResponseBody) GetBestOfferRoi() float64 {
	if x != nil {
		return x.BestOfferRoi
	}
	return 0
}

func (x *VendorResponseBody) GetExpiryDate() string {
	if x != nil {
		return x.ExpiryDate
	}
	return ""
}

func (x *VendorResponseBody) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *VendorResponseBody) GetLeadSource() string {
	if x != nil {
		return x.LeadSource
	}
	return ""
}

func (x *VendorResponseBody) GetMetaIncome() string {
	if x != nil {
		return x.MetaIncome
	}
	return ""
}

func (x *VendorResponseBody) GetMetaTagName() string {
	if x != nil {
		return x.MetaTagName
	}
	return ""
}

func (x *VendorResponseBody) GetEmploymentType() string {
	if x != nil {
		return x.EmploymentType
	}
	return ""
}

func (x *VendorResponseBody) GetSummaryVariables() string {
	if x != nil {
		return x.SummaryVariables
	}
	return ""
}

var File_api_vendors_moneyview_generate_mv_black_box_offer_proto protoreflect.FileDescriptor

var file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDesc = []byte{
	0x0a, 0x37, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x6f,
	0x6e, 0x65, 0x79, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x5f, 0x6d, 0x76, 0x5f, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x78, 0x5f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x76, 0x69, 0x65, 0x77, 0x22, 0xca, 0x03, 0x0a,
	0x24, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4d, 0x76, 0x42, 0x6c, 0x61, 0x63, 0x6b,
	0x42, 0x6f, 0x78, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x65, 0x61, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x65, 0x61, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x65, 0x61, 0x64, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x61,
	0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x46,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74,
	0x61, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x69, 0x6e, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x63,
	0x6c, 0x61, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0e, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x64, 0x6f, 0x62, 0x12, 0x26, 0x0a, 0x0e, 0x62, 0x75, 0x72, 0x65, 0x61, 0x75, 0x50, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x75, 0x72,
	0x65, 0x61, 0x75, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x0b, 0x70,
	0x6b, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x6e, 0x65, 0x79,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x6b, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x0b, 0x70, 0x6b, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x12, 0x34, 0x0a,
	0x07, 0x73, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x76, 0x69,
	0x65, 0x77, 0x2e, 0x53, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x73, 0x6d, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x24, 0x0a, 0x0d, 0x62, 0x75, 0x72, 0x65, 0x61, 0x75, 0x44, 0x61, 0x74,
	0x61, 0x53, 0x74, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x75, 0x72, 0x65,
	0x61, 0x75, 0x44, 0x61, 0x74, 0x61, 0x53, 0x74, 0x72, 0x22, 0x51, 0x0a, 0x0b, 0x50, 0x6b, 0x67,
	0x49, 0x6e, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x12, 0x42, 0x0a, 0x0e, 0x70, 0x6b, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x44, 0x54, 0x4f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x6e, 0x65, 0x79,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x6b, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x70, 0x6b,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x54, 0x4f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x5d, 0x0a, 0x07,
	0x50, 0x6b, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x22, 0x3b, 0x0a, 0x07, 0x53,
	0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x30, 0x0a, 0x07, 0x73, 0x6d, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x53, 0x6d, 0x73, 0x52,
	0x07, 0x73, 0x6d, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x45, 0x0a, 0x03, 0x53, 0x6d, 0x73, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x22,
	0x96, 0x01, 0x0a, 0x25, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4d, 0x76, 0x42, 0x6c,
	0x61, 0x63, 0x6b, 0x42, 0x6f, 0x78, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x62, 0x6f, 0x64,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x52, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xb8, 0x03, 0x0a, 0x12, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x12,
	0x20, 0x0a, 0x0b, 0x6c, 0x65, 0x61, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x66, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x65, 0x61, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x66, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x62, 0x65, 0x73,
	0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0f, 0x62, 0x65, 0x73, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x62, 0x65, 0x73, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x62, 0x65,
	0x73, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x62, 0x65, 0x73, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x6f, 0x69, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0c, 0x62, 0x65, 0x73, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x6f,
	0x69, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1e, 0x0a,
	0x0a, 0x6c, 0x65, 0x61, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6c, 0x65, 0x61, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x6d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x6d, 0x65, 0x74, 0x61, 0x54, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x74, 0x61, 0x54, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x26, 0x0a, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x73, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x76,
	0x69, 0x65, 0x77, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x76, 0x69, 0x65,
	0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescOnce sync.Once
	file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescData = file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDesc
)

func file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescGZIP() []byte {
	file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescOnce.Do(func() {
		file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescData)
	})
	return file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDescData
}

var file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_vendors_moneyview_generate_mv_black_box_offer_proto_goTypes = []interface{}{
	(*GenerateMvBlackBoxOfferVendorRequest)(nil),  // 0: vendors.moneyview.GenerateMvBlackBoxOfferVendorRequest
	(*PkgInfoData)(nil),                           // 1: vendors.moneyview.PkgInfoData
	(*PkgInfo)(nil),                               // 2: vendors.moneyview.PkgInfo
	(*SmsData)(nil),                               // 3: vendors.moneyview.SmsData
	(*Sms)(nil),                                   // 4: vendors.moneyview.Sms
	(*GenerateMvBlackBoxOfferVendorResponse)(nil), // 5: vendors.moneyview.GenerateMvBlackBoxOfferVendorResponse
	(*VendorResponseBody)(nil),                    // 6: vendors.moneyview.VendorResponseBody
}
var file_api_vendors_moneyview_generate_mv_black_box_offer_proto_depIdxs = []int32{
	1, // 0: vendors.moneyview.GenerateMvBlackBoxOfferVendorRequest.pkgInfoData:type_name -> vendors.moneyview.PkgInfoData
	3, // 1: vendors.moneyview.GenerateMvBlackBoxOfferVendorRequest.smsData:type_name -> vendors.moneyview.SmsData
	2, // 2: vendors.moneyview.PkgInfoData.pkgInfoDTOList:type_name -> vendors.moneyview.PkgInfo
	4, // 3: vendors.moneyview.SmsData.smsList:type_name -> vendors.moneyview.Sms
	6, // 4: vendors.moneyview.GenerateMvBlackBoxOfferVendorResponse.body:type_name -> vendors.moneyview.VendorResponseBody
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_vendors_moneyview_generate_mv_black_box_offer_proto_init() }
func file_api_vendors_moneyview_generate_mv_black_box_offer_proto_init() {
	if File_api_vendors_moneyview_generate_mv_black_box_offer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateMvBlackBoxOfferVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgInfoData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Sms); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateMvBlackBoxOfferVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VendorResponseBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_moneyview_generate_mv_black_box_offer_proto_goTypes,
		DependencyIndexes: file_api_vendors_moneyview_generate_mv_black_box_offer_proto_depIdxs,
		MessageInfos:      file_api_vendors_moneyview_generate_mv_black_box_offer_proto_msgTypes,
	}.Build()
	File_api_vendors_moneyview_generate_mv_black_box_offer_proto = out.File
	file_api_vendors_moneyview_generate_mv_black_box_offer_proto_rawDesc = nil
	file_api_vendors_moneyview_generate_mv_black_box_offer_proto_goTypes = nil
	file_api_vendors_moneyview_generate_mv_black_box_offer_proto_depIdxs = nil
}
