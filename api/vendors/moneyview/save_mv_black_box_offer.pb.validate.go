// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/moneyview/save_mv_black_box_offer.proto

package moneyview

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SaveBlackBoxOfferVendorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveBlackBoxOfferVendorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveBlackBoxOfferVendorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SaveBlackBoxOfferVendorRequestMultiError, or nil if none found.
func (m *SaveBlackBoxOfferVendorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveBlackBoxOfferVendorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LeadUserRef

	// no validation rules for Status

	// no validation rules for BestOfferAmount

	// no validation rules for BestOfferTenure

	// no validation rules for BestOfferRoi

	// no validation rules for ExpiryDate

	// no validation rules for Category

	// no validation rules for LeadSource

	// no validation rules for MetaIncome

	// no validation rules for MetaTagName

	// no validation rules for EmploymentType

	if len(errors) > 0 {
		return SaveBlackBoxOfferVendorRequestMultiError(errors)
	}

	return nil
}

// SaveBlackBoxOfferVendorRequestMultiError is an error wrapping multiple
// validation errors returned by SaveBlackBoxOfferVendorRequest.ValidateAll()
// if the designated constraints aren't met.
type SaveBlackBoxOfferVendorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveBlackBoxOfferVendorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveBlackBoxOfferVendorRequestMultiError) AllErrors() []error { return m }

// SaveBlackBoxOfferVendorRequestValidationError is the validation error
// returned by SaveBlackBoxOfferVendorRequest.Validate if the designated
// constraints aren't met.
type SaveBlackBoxOfferVendorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveBlackBoxOfferVendorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveBlackBoxOfferVendorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveBlackBoxOfferVendorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveBlackBoxOfferVendorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveBlackBoxOfferVendorRequestValidationError) ErrorName() string {
	return "SaveBlackBoxOfferVendorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SaveBlackBoxOfferVendorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveBlackBoxOfferVendorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveBlackBoxOfferVendorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveBlackBoxOfferVendorRequestValidationError{}

// Validate checks the field values on SaveBlackBoxOfferVendorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveBlackBoxOfferVendorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveBlackBoxOfferVendorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SaveBlackBoxOfferVendorResponseMultiError, or nil if none found.
func (m *SaveBlackBoxOfferVendorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveBlackBoxOfferVendorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	// no validation rules for Code

	if len(errors) > 0 {
		return SaveBlackBoxOfferVendorResponseMultiError(errors)
	}

	return nil
}

// SaveBlackBoxOfferVendorResponseMultiError is an error wrapping multiple
// validation errors returned by SaveBlackBoxOfferVendorResponse.ValidateAll()
// if the designated constraints aren't met.
type SaveBlackBoxOfferVendorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveBlackBoxOfferVendorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveBlackBoxOfferVendorResponseMultiError) AllErrors() []error { return m }

// SaveBlackBoxOfferVendorResponseValidationError is the validation error
// returned by SaveBlackBoxOfferVendorResponse.Validate if the designated
// constraints aren't met.
type SaveBlackBoxOfferVendorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveBlackBoxOfferVendorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveBlackBoxOfferVendorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveBlackBoxOfferVendorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveBlackBoxOfferVendorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveBlackBoxOfferVendorResponseValidationError) ErrorName() string {
	return "SaveBlackBoxOfferVendorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SaveBlackBoxOfferVendorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveBlackBoxOfferVendorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveBlackBoxOfferVendorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveBlackBoxOfferVendorResponseValidationError{}
