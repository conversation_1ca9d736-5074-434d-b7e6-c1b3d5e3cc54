// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/moneyview/save_mv_black_box_offer.proto

package moneyview

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SaveBlackBoxOfferVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeadUserRef     string  `protobuf:"bytes,1,opt,name=lead_user_ref,json=leadUserRef,proto3" json:"lead_user_ref,omitempty"`
	Status          string  `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	BestOfferAmount float64 `protobuf:"fixed64,3,opt,name=best_offer_amount,json=bestOfferAmount,proto3" json:"best_offer_amount,omitempty"`
	BestOfferTenure int32   `protobuf:"varint,4,opt,name=best_offer_tenure,json=bestOfferTenure,proto3" json:"best_offer_tenure,omitempty"`
	BestOfferRoi    float64 `protobuf:"fixed64,5,opt,name=best_offer_roi,json=bestOfferRoi,proto3" json:"best_offer_roi,omitempty"`
	ExpiryDate      string  `protobuf:"bytes,6,opt,name=expiry_date,json=expiryDate,proto3" json:"expiry_date,omitempty"`
	Category        string  `protobuf:"bytes,7,opt,name=category,proto3" json:"category,omitempty"`
	LeadSource      string  `protobuf:"bytes,8,opt,name=lead_source,json=leadSource,proto3" json:"lead_source,omitempty"`
	MetaIncome      string  `protobuf:"bytes,9,opt,name=meta_income,json=metaIncome,proto3" json:"meta_income,omitempty"`
	MetaTagName     string  `protobuf:"bytes,10,opt,name=meta_tag_name,json=metaTagName,proto3" json:"meta_tag_name,omitempty"`
	EmploymentType  string  `protobuf:"bytes,11,opt,name=employment_type,json=employmentType,proto3" json:"employment_type,omitempty"`
}

func (x *SaveBlackBoxOfferVendorRequest) Reset() {
	*x = SaveBlackBoxOfferVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moneyview_save_mv_black_box_offer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveBlackBoxOfferVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveBlackBoxOfferVendorRequest) ProtoMessage() {}

func (x *SaveBlackBoxOfferVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moneyview_save_mv_black_box_offer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveBlackBoxOfferVendorRequest.ProtoReflect.Descriptor instead.
func (*SaveBlackBoxOfferVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_moneyview_save_mv_black_box_offer_proto_rawDescGZIP(), []int{0}
}

func (x *SaveBlackBoxOfferVendorRequest) GetLeadUserRef() string {
	if x != nil {
		return x.LeadUserRef
	}
	return ""
}

func (x *SaveBlackBoxOfferVendorRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SaveBlackBoxOfferVendorRequest) GetBestOfferAmount() float64 {
	if x != nil {
		return x.BestOfferAmount
	}
	return 0
}

func (x *SaveBlackBoxOfferVendorRequest) GetBestOfferTenure() int32 {
	if x != nil {
		return x.BestOfferTenure
	}
	return 0
}

func (x *SaveBlackBoxOfferVendorRequest) GetBestOfferRoi() float64 {
	if x != nil {
		return x.BestOfferRoi
	}
	return 0
}

func (x *SaveBlackBoxOfferVendorRequest) GetExpiryDate() string {
	if x != nil {
		return x.ExpiryDate
	}
	return ""
}

func (x *SaveBlackBoxOfferVendorRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *SaveBlackBoxOfferVendorRequest) GetLeadSource() string {
	if x != nil {
		return x.LeadSource
	}
	return ""
}

func (x *SaveBlackBoxOfferVendorRequest) GetMetaIncome() string {
	if x != nil {
		return x.MetaIncome
	}
	return ""
}

func (x *SaveBlackBoxOfferVendorRequest) GetMetaTagName() string {
	if x != nil {
		return x.MetaTagName
	}
	return ""
}

func (x *SaveBlackBoxOfferVendorRequest) GetEmploymentType() string {
	if x != nil {
		return x.EmploymentType
	}
	return ""
}

type SaveBlackBoxOfferVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Code    string `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *SaveBlackBoxOfferVendorResponse) Reset() {
	*x = SaveBlackBoxOfferVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moneyview_save_mv_black_box_offer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveBlackBoxOfferVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveBlackBoxOfferVendorResponse) ProtoMessage() {}

func (x *SaveBlackBoxOfferVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moneyview_save_mv_black_box_offer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveBlackBoxOfferVendorResponse.ProtoReflect.Descriptor instead.
func (*SaveBlackBoxOfferVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_moneyview_save_mv_black_box_offer_proto_rawDescGZIP(), []int{1}
}

func (x *SaveBlackBoxOfferVendorResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SaveBlackBoxOfferVendorResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SaveBlackBoxOfferVendorResponse) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

var File_api_vendors_moneyview_save_mv_black_box_offer_proto protoreflect.FileDescriptor

var file_api_vendors_moneyview_save_mv_black_box_offer_proto_rawDesc = []byte{
	0x0a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x6f,
	0x6e, 0x65, 0x79, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x73, 0x61, 0x76, 0x65, 0x5f, 0x6d, 0x76, 0x5f,
	0x62, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x78, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x76, 0x69, 0x65, 0x77, 0x22, 0xa6, 0x03, 0x0a, 0x1e, 0x53, 0x61, 0x76,
	0x65, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x42, 0x6f, 0x78, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x56, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x6c,
	0x65, 0x61, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x6c, 0x65, 0x61, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x66, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x65, 0x73, 0x74, 0x5f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0f, 0x62, 0x65, 0x73, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x65, 0x73, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x62, 0x65, 0x73, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12,
	0x24, 0x0a, 0x0e, 0x62, 0x65, 0x73, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x72, 0x6f,
	0x69, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x62, 0x65, 0x73, 0x74, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x52, 0x6f, 0x69, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x65, 0x61, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x61, 0x64, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x69, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x61, 0x49, 0x6e,
	0x63, 0x6f, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x74, 0x61, 0x67,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x74,
	0x61, 0x54, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x67, 0x0a, 0x1f, 0x53, 0x61, 0x76, 0x65, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x42, 0x6f,
	0x78, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x76, 0x69, 0x65, 0x77, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x76, 0x69, 0x65, 0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_moneyview_save_mv_black_box_offer_proto_rawDescOnce sync.Once
	file_api_vendors_moneyview_save_mv_black_box_offer_proto_rawDescData = file_api_vendors_moneyview_save_mv_black_box_offer_proto_rawDesc
)

func file_api_vendors_moneyview_save_mv_black_box_offer_proto_rawDescGZIP() []byte {
	file_api_vendors_moneyview_save_mv_black_box_offer_proto_rawDescOnce.Do(func() {
		file_api_vendors_moneyview_save_mv_black_box_offer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_moneyview_save_mv_black_box_offer_proto_rawDescData)
	})
	return file_api_vendors_moneyview_save_mv_black_box_offer_proto_rawDescData
}

var file_api_vendors_moneyview_save_mv_black_box_offer_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_vendors_moneyview_save_mv_black_box_offer_proto_goTypes = []interface{}{
	(*SaveBlackBoxOfferVendorRequest)(nil),  // 0: vendors.moneyview.SaveBlackBoxOfferVendorRequest
	(*SaveBlackBoxOfferVendorResponse)(nil), // 1: vendors.moneyview.SaveBlackBoxOfferVendorResponse
}
var file_api_vendors_moneyview_save_mv_black_box_offer_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendors_moneyview_save_mv_black_box_offer_proto_init() }
func file_api_vendors_moneyview_save_mv_black_box_offer_proto_init() {
	if File_api_vendors_moneyview_save_mv_black_box_offer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_moneyview_save_mv_black_box_offer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveBlackBoxOfferVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moneyview_save_mv_black_box_offer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveBlackBoxOfferVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_moneyview_save_mv_black_box_offer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_moneyview_save_mv_black_box_offer_proto_goTypes,
		DependencyIndexes: file_api_vendors_moneyview_save_mv_black_box_offer_proto_depIdxs,
		MessageInfos:      file_api_vendors_moneyview_save_mv_black_box_offer_proto_msgTypes,
	}.Build()
	File_api_vendors_moneyview_save_mv_black_box_offer_proto = out.File
	file_api_vendors_moneyview_save_mv_black_box_offer_proto_rawDesc = nil
	file_api_vendors_moneyview_save_mv_black_box_offer_proto_goTypes = nil
	file_api_vendors_moneyview_save_mv_black_box_offer_proto_depIdxs = nil
}
