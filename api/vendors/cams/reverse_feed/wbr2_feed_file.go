package reverse_feed

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/vendors/cams/scheme_code_converter"

	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/net/context"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/logger"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	pb "github.com/epifi/gamma/api/investment/mutualfund/order"
	invPkg "github.com/epifi/gamma/pkg/investment"
)

const (
	mmddyyyyhhmmssPMLayout = "1/2/2006 15:04:05 PM"
	ddMonyyyyLayout        = "02-Jan-2006"
	hhmmssLayout           = "15:04:05"
)

// parseDateTimeWithAMPM tries to parse a date string with multiple layouts as fallbacks
func parseDateTimeWithAMPM(dateStr string) (time.Time, error) {
	// List of layouts to try in order of preference
	layouts := []string{
		mmddyyyyhhmmssPMLayout,   // "1/2/2006 15:04:05 PM"
		"1/2/2006 15:04:05",      // "1/2/2006 15:04:05" (24-hour format)
		"1/2/2006 3:04 PM",       // 12-hour format without seconds
		"1/2/2006 3:04 AM",       // AM format without seconds
		"01/02/2006 3:04 PM",     // Zero Padded format without seconds
		"01/02/2006 3:04 AM",     // Zero Padded AM format without seconds
		"1/2/2006 3:04:05 PM",    // Single digit month/day with PM
		"1/2/2006 3:04:05 AM",    // Single digit month/day with AM
		"1/2/2006 09:04:05 AM",   // "1/2/2006 15:04:05 AM"
		"1/2/2006 3:04:05",       // Single digit month/day 24-hour
		"7/2/2006 12:00 AM",      // Specific format: "7/21/2025 12:00 AM"
		"1/21/2006 12:00 PM",     // Specific format: "7/21/2025 12:00 PM"
		"7/21/2006 15:04:05 PM",  // Zero-padded with PM
		"01/02/2006 09:04:05 AM", // Zero-padded with AM
		"01/02/2006 15:04:05",    // Zero-padded 24-hour
		"01/02/2006 3:04:05 PM",  // Zero-padded month/day with PM
		"01/02/2006 3:04:05 AM",  // Zero-padded month/day with AM
		"01/02/2006 3:04:05",     // Zero-padded month/day 24-hour
		"01/02/2006 12:00 AM",    // Zero-padded specific format
		"01/02/2006 12:00 PM",    // Zero-padded specific format
	}

	for _, layout := range layouts {
		if t, err := time.Parse(layout, dateStr); err == nil {
			return t, nil
		}
	}

	return time.Time{}, fmt.Errorf("cannot parse date with any supported format: %s", dateStr)
}

// WBR2File struct represents an individual row present in the reverse feed file of WBR2 format
type WBR2File struct {
	AMCCode                   string
	FolioNumber               string
	ProductCode               string
	ProductName               string
	InvestorName              string
	TransactionTypeCode       string
	TransactionNumber         string
	TransactionMode           string
	TransactionStatus         string
	SourceCode                string
	SourceSerialNumber        string
	TradeDate                 string
	PostedDate                string
	Price                     string
	Units                     string
	Amount                    string
	BrokerDealerCode          string
	SubbrokerDealerCode       string
	BrokeragePercentage       string
	BrokerageAmount           string
	InvestorIdentification    string
	DateOfThisDataFeed        string
	TimeOfThisDataFeedTIME1   string
	TransactionSubtype        string
	ApplicationNo             string
	TransactionNature         string
	BasicTDS                  string
	TotalTDS                  string
	Form15H                   string
	MICRNo                    string
	Remarks                   string
	Swflag                    string
	OldFolioNo                string
	SequenceNo                string
	ReinvestFlag              string
	MultBrok                  string
	Stt                       string
	Location                  string
	SchemeType                string
	TaxStatus                 string
	EntryLoad                 string
	ScanRefNo                 string
	Pan                       string
	Min                       string
	TargSrcScheme             string
	TrxnTypeFlag              string
	TICOBTrxnType             string
	TICOBTrxnNo               string
	TICOBPostedDate           string
	DPID                      string
	TrxnCharges               string
	EligibAmt                 string
	SrcOfTrxn                 string
	TrxnSuffix                string
	Siptrxnno                 string
	TerLocati                 string
	Euin                      string
	EuinValid                 string
	EuinOpted                 string
	SubBrkAr                  string
	ExchDcFlag                string
	SrcBrkCode                string
	SysRegnDate               string
	AcNo                      string
	BankName                  string
	ReversalCode              string
	ExchangeFlag              string
	CAInitiatedDate           string
	GSTStateCode              string
	IgstAmount                string
	CgstAmount                string
	SgstAmount                string
	EditTrxnCaID              string
	OriginalTransactionNumber string
	StampDuty                 string
}

var WBR2FieldMap = map[int]string{
	0:  "AMCCode",
	1:  "FolioNumber",
	2:  "ProductCode",
	3:  "ProductName",
	4:  "InvestorName",
	5:  "TransactionTypeCode",
	6:  "TransactionNumber",
	7:  "TransactionMode",
	8:  "TransactionStatus",
	9:  "SourceCode",
	10: "SourceSerialNumber",
	11: "TradeDate",
	12: "PostedDate",
	13: "Price",
	14: "Units",
	15: "Amount",
	16: "BrokerDealerCode",
	17: "SubbrokerDealerCode",
	18: "BrokeragePercentage",
	19: "BrokerageAmount",
	20: "InvestorIdentification",
	21: "DateOfThisDataFeed",
	22: "TimeOfThisDataFeedTIME1",
	23: "TransactionSubtype",
	24: "ApplicationNo",
	25: "TransactionNature",
	26: "BasicTDS",
	27: "TotalTDS",
	28: "Form15H",
	29: "MICRNo",
	30: "Remarks",
	31: "Swflag",
	32: "OldFolioNo",
	33: "SequenceNo",
	34: "ReinvestFlag",
	35: "MultBrok",
	36: "Stt",
	37: "Location",
	38: "SchemeType",
	39: "TaxStatus",
	40: "EntryLoad",
	41: "ScanRefNo",
	42: "Pan",
	43: "Min",
	44: "TargSrcScheme",
	45: "TrxnTypeFlag",
	46: "TICOBTrxnType",
	47: "TICOBTrxnNo",
	48: "TICOBPostedDate",
	49: "DPID",
	50: "TrxnCharges",
	51: "EligibAmt",
	52: "SrcOfTrxn",
	53: "TrxnSuffix",
	54: "Siptrxnno",
	55: "TerLocati",
	56: "Euin",
	57: "EuinValid",
	58: "EuinOpted",
	59: "SubBrkAr",
	60: "ExchDcFlag",
	61: "SrcBrkCode",
	62: "SysRegnDate",
	63: "AcNo",
	64: "BankName",
	65: "ReversalCode",
	66: "ExchangeFlag",
	67: "CAInitiatedDate",
	68: "GSTStateCode",
	69: "IgstAmount",
	70: "CgstAmount",
	71: "SgstAmount",
	72: "EditTrxnCaID",
	73: "OriginalTransactionNumber",
	74: "StampDuty",
}

func ParseWBR2File(ctx context.Context, fileContent []byte) ([]*WBR2File, error) {
	reader := csv.NewReader(bytes.NewReader(fileContent))
	reader.Comma = '|'
	feedRes, err := reader.ReadAll()
	if err != nil {
		return nil, errors.Wrap(err, "error while reading feed file")
	}
	// iterate over every line and parse it
	var wbR2FeedFiles []*WBR2File
	for _, feed := range feedRes {
		wbR2file := WBR2File{}
		for keyIdx, value := range feed {
			// The actual wbr33 files has some additional legacy columns which are present in the template. These columns can be ignored.
			if keyIdx >= len(WBR2FieldMap) {
				break
			}

			keyName, keyOk := WBR2FieldMap[keyIdx]
			if !keyOk {
				logger.ErrorNoCtx(fmt.Sprintf("error while fetching key name using index, keyIdx: %v", keyIdx))
				continue
			}
			// setting the value as string, parsing to apt data type will be done during sending out request to order manager for updates.
			// TODO(ismail): the following line panics if we try set a key which is not present in the struct, for now the FieldMap and Struct keys need to be in sync.
			reflect.ValueOf(&wbR2file).Elem().FieldByName(keyName).SetString(value)
		}
		logger.Info(ctx, "value of struct", zap.Any("struct", wbR2file), zap.String(logger.ORDER_ID, wbR2file.SourceSerialNumber))
		wbR2FeedFiles = append(wbR2FeedFiles, &wbR2file)
	}
	return wbR2FeedFiles, nil
}

// AggregateWBR2File takes in a list of wbr2Rows and aggregates required fields grouping them by the vendorOrderID.
// It returns a list of successfully parsed entries and failed entries(a map of vendorOrderID to failure reason) and error if any.
func AggregateWBR2File(ctx context.Context, wbr2Rows []*WBR2File) ([]*WBR2File, map[string]string, error) {
	aggregatedMap := make(map[string]*WBR2File)
	parsingFailedOrderIDs := make(map[string]string)
	var err error
	for _, row := range wbr2Rows {
		// SourceSerialNumber per SourceCode should be grouped as SourceSerialNumber(VendorOrderID) can get duplicated among different SourceCodes.
		vendorOrderIDSourceCode := row.SourceCode + row.SourceSerialNumber

		_, ok := parsingFailedOrderIDs[vendorOrderIDSourceCode]
		if ok {
			continue
		}

		_, ok = aggregatedMap[vendorOrderIDSourceCode]
		if ok {
			aggregatedMap[vendorOrderIDSourceCode], err = aggregateWBR2Entries(ctx, aggregatedMap[vendorOrderIDSourceCode], row)
			if err != nil {
				logger.Error(ctx, "error in aggregateWBR2Entries", zap.Error(err), zap.String(logger.VENDOR_ORDER_ID, row.SourceSerialNumber))
				parsingFailedOrderIDs[vendorOrderIDSourceCode] = err.Error()
			}
		} else {
			aggregatedMap[vendorOrderIDSourceCode] = row
		}
	}

	var aggregatedList []*WBR2File
	for key, value := range aggregatedMap {
		_, ok := parsingFailedOrderIDs[key]
		if ok {
			continue
		}

		aggregatedList = append(aggregatedList, value)
	}

	return aggregatedList, parsingFailedOrderIDs, nil
}

//nolint:funlen
func aggregateWBR2Entries(ctx context.Context, entry1 *WBR2File, entry2 *WBR2File) (*WBR2File, error) {
	units1, err := strconv.ParseFloat(entry1.Units, 64)
	if err != nil {
		logger.Error(ctx, "error while parsing feed units", zap.Error(err), zap.String("file_type", "wbr2"),
			zap.String(logger.VENDOR_ORDER_ID, entry1.SourceSerialNumber))
		return nil, err
	}
	units2, err := strconv.ParseFloat(entry2.Units, 64)
	if err != nil {
		logger.Error(ctx, "error while parsing feed units", zap.Error(err), zap.String("file_type", "wbr2"),
			zap.String(logger.VENDOR_ORDER_ID, entry2.SourceSerialNumber))
		return nil, err
	}

	amount1, err := strconv.ParseFloat(entry1.Amount, 64)
	if err != nil {
		logger.Error(ctx, "error while parsing amount", zap.Error(err), zap.String("file_type", "wbr2"),
			zap.String(logger.VENDOR_ORDER_ID, entry1.SourceSerialNumber))
		return nil, err
	}
	amount2, err := strconv.ParseFloat(entry2.Amount, 64)
	if err != nil {
		logger.Error(ctx, "error while parsing units", zap.Error(err), zap.String("file_type", "wbr2"),
			zap.String(logger.VENDOR_ORDER_ID, entry2.SourceSerialNumber))
		return nil, err
	}

	basicTDS1 := ParseFloat(ctx, entry1.BasicTDS, "basicTDS", entry1.SourceSerialNumber)
	basicTDS2 := ParseFloat(ctx, entry2.BasicTDS, "basicTDS", entry2.SourceSerialNumber)

	totalTDS1 := ParseFloat(ctx, entry1.TotalTDS, "totalTDS", entry1.SourceSerialNumber)
	totalTDS2 := ParseFloat(ctx, entry2.TotalTDS, "totalTDS", entry2.SourceSerialNumber)

	stt1 := ParseFloat(ctx, entry1.Stt, "STT", entry1.SourceSerialNumber)
	stt2 := ParseFloat(ctx, entry2.Stt, "STT", entry2.SourceSerialNumber)

	entryLoad1 := ParseFloat(ctx, entry1.EntryLoad, "EntryLoad", entry1.SourceSerialNumber)
	entryLoad2 := ParseFloat(ctx, entry2.EntryLoad, "EntryLoad", entry2.SourceSerialNumber)

	transactionCharges1 := ParseFloat(ctx, entry1.TrxnCharges, "TrxnCharges", entry1.SourceSerialNumber)
	transactionCharges2 := ParseFloat(ctx, entry2.TrxnCharges, "TrxnCharges", entry2.SourceSerialNumber)

	igstAmount1 := ParseFloat(ctx, entry1.IgstAmount, "IgstAmount", entry1.SourceSerialNumber)
	igstAmount2 := ParseFloat(ctx, entry2.IgstAmount, "IgstAmount", entry2.SourceSerialNumber)
	cgstAmount1 := ParseFloat(ctx, entry1.CgstAmount, "CgstAmount", entry1.SourceSerialNumber)
	cgstAmount2 := ParseFloat(ctx, entry2.CgstAmount, "CgstAmount", entry2.SourceSerialNumber)
	sgstAmount1 := ParseFloat(ctx, entry1.SgstAmount, "SgstAmount", entry1.SourceSerialNumber)
	sgstAmount2 := ParseFloat(ctx, entry2.SgstAmount, "SgstAmount", entry2.SourceSerialNumber)

	stampDuty1 := ParseFloat(ctx, entry1.StampDuty, "StampDuty", entry1.SourceSerialNumber)
	stampDuty2 := ParseFloat(ctx, entry2.StampDuty, "StampDuty", entry2.SourceSerialNumber)

	var aggregatedEntry WBR2File
	err = copier.Copy(&aggregatedEntry, entry1)
	if err != nil {
		return nil, err
	}

	aggregatedEntry.Units = fmt.Sprintf("%v", units1+units2)
	aggregatedEntry.Amount = fmt.Sprintf("%v", amount1+amount2)
	aggregatedEntry.BasicTDS = fmt.Sprintf("%v", basicTDS1+basicTDS2)
	aggregatedEntry.TotalTDS = fmt.Sprintf("%v", totalTDS1+totalTDS2)
	aggregatedEntry.Stt = fmt.Sprintf("%v", stt1+stt2)
	aggregatedEntry.EntryLoad = fmt.Sprintf("%v", entryLoad1+entryLoad2)
	aggregatedEntry.TrxnCharges = fmt.Sprintf("%v", transactionCharges1+transactionCharges2)
	aggregatedEntry.IgstAmount = fmt.Sprintf("%v", igstAmount1+igstAmount2)
	aggregatedEntry.CgstAmount = fmt.Sprintf("%v", cgstAmount1+cgstAmount2)
	aggregatedEntry.SgstAmount = fmt.Sprintf("%v", sgstAmount1+sgstAmount2)
	aggregatedEntry.StampDuty = fmt.Sprintf("%v", stampDuty1+stampDuty2)
	return &aggregatedEntry, nil
}

func ParseFloat(ctx context.Context, value string, param string, vendorOrderId string) float64 {
	if len(value) == 0 {
		return 0.0
	}

	parsedValue, err := strconv.ParseFloat(value, 64)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error while parsing %s", param), zap.Error(err), zap.String("file_type", "wbr2"),
			zap.String(logger.VENDOR_ORDER_ID, vendorOrderId))
		return 0.0
	}
	return parsedValue
}

func GetVendorOrderID(epifiUserCode string, reverseFeedUserCode, reverseFeedID string) string {
	if strings.Compare(epifiUserCode, reverseFeedUserCode) == 0 {
		return reverseFeedID
	} else {
		externalVendorOrderId := reverseFeedID + "_" + reverseFeedUserCode
		return externalVendorOrderId
	}
}

//nolint:funlen
func ConvertWBR2ToMetaData(_ context.Context, wbr2Feed *WBR2File) (*pb.OrderConfirmationMetadata, error) {

	var err error
	allotmentDate, err := parseDateTimeWithAMPM(wbr2Feed.TradeDate)
	if err != nil {
		return nil, errors.Wrap(err, "error while parsing trade date")
	}
	orderDate, err := parseDateTimeWithAMPM(wbr2Feed.PostedDate)
	if err != nil {
		return nil, errors.Wrap(err, "error while parsing post date")
	}

	dataFeedDate, err := parseDateTimeWithAMPM(wbr2Feed.DateOfThisDataFeed)
	if err != nil {
		return nil, errors.Wrap(err, "error while parsing post date")
	}

	var tax *moneyPb.Money = nil
	if len(wbr2Feed.BasicTDS) > 0 {
		tax, err = ParseToMoney(wbr2Feed.BasicTDS)
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing tax")
		}
	}

	var stt *moneyPb.Money = nil
	if len(wbr2Feed.Stt) > 0 {
		stt, err = ParseToMoney(wbr2Feed.Stt)
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing security transaction tax")
		}
	}

	var transactionCharges *moneyPb.Money = nil
	if len(wbr2Feed.TrxnCharges) > 0 {
		transactionCharges, err = ParseToMoney(wbr2Feed.TrxnCharges)
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing transaction charges")
		}
	}

	var entryLoad *moneyPb.Money = nil
	if len(wbr2Feed.EntryLoad) > 0 {
		entryLoad, err = ParseToMoney(wbr2Feed.EntryLoad)
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing entry load")
		}
	}

	var igstAmount *moneyPb.Money = nil
	if len(wbr2Feed.IgstAmount) > 0 {
		igstAmount, err = ParseToMoney(wbr2Feed.IgstAmount)
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing igst amount")
		}
	}

	var cgstAmount *moneyPb.Money = nil
	if len(wbr2Feed.CgstAmount) > 0 {
		cgstAmount, err = ParseToMoney(wbr2Feed.CgstAmount)
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing cgst amount")
		}
	}

	var sgstAmount *moneyPb.Money = nil
	if len(wbr2Feed.SgstAmount) > 0 {
		sgstAmount, err = ParseToMoney(wbr2Feed.SgstAmount)
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing sgst amount")
		}
	}

	var stampDuty *moneyPb.Money = nil
	if len(wbr2Feed.StampDuty) > 0 {
		stampDuty, err = ParseToMoney(wbr2Feed.StampDuty)
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing stamp duty")
		}
	}

	orderConfirmationMetaData := &pb.OrderConfirmationMetadata{
		TransactionNumber:      wbr2Feed.TransactionNumber,
		SourceCode:             wbr2Feed.SourceCode,
		AllotmentDate:          timestampPb.New(allotmentDate),
		OrderDate:              timestampPb.New(orderDate),
		DataFeedTime:           timestampPb.New(dataFeedDate),
		TransactionType:        wbr2Feed.TransactionNature,
		Tax:                    tax,
		SecurityTransactionTax: stt,
		SchemeType:             wbr2Feed.SchemeType,
		TaxStatus:              wbr2Feed.TaxStatus,
		EntryLoad:              entryLoad,
		ReversalReason:         wbr2Feed.ReversalCode,
		TransactionCharges:     transactionCharges,
		GstStateCode:           wbr2Feed.GSTStateCode,
		IgstAmount:             igstAmount,
		CgstAmount:             cgstAmount,
		SgstAmount:             sgstAmount,
		StampDuty:              stampDuty,
	}
	return orderConfirmationMetaData, nil
}

// FilterExternalAndInternalOrders :This function takes in a list of entries present in wbr2 and splits them into
//
//	internal and external orders. Internal orders are orders placed via fi app and
//	external orders are order placed via an external platform using a folioId that
//	was created by fi. If the sourceCode(same as UserCode) present fot the entry is
//	same as the one used by FI, then it means that, the order
//	was created by fi systems. But, if it is not the same, then it means that the
//	order was placed by a folio number created at fi in other external platforms/apps.
func FilterExternalAndInternalOrders(ctx context.Context, wbr2Entries []*WBR2File, fiUserCode string) (
	[]*WBR2File,
	map[mfPb.Amc]map[pb.OrderSubType][]*WBR2File,
	map[mfPb.Amc]map[pb.OrderSubType][]*WBR2File,
	map[string]string) {

	var internallyPlacedOrders []*WBR2File
	externallyPlacedSellOrders := make(map[mfPb.Amc]map[pb.OrderSubType][]*WBR2File)
	externallyPlacedBuyOrders := make(map[mfPb.Amc]map[pb.OrderSubType][]*WBR2File)

	failedOrderTypeDetection := make(map[string]string)
	for _, wbr2FeedEntry := range wbr2Entries {
		if strings.Compare(fiUserCode, wbr2FeedEntry.SourceCode) == 0 {
			internallyPlacedOrders = append(internallyPlacedOrders, wbr2FeedEntry)
		} else {
			amc, err := invPkg.GetAmcFromCode(wbr2FeedEntry.AMCCode)
			if err != nil {
				logger.Error(ctx, "AMC code not supported", zap.String(logger.MF_AMC, wbr2FeedEntry.AMCCode))
				continue
			}
			if len(wbr2FeedEntry.TransactionTypeCode) == 0 {
				logger.Error(ctx, "received empty transaction type code", zap.String(logger.VENDOR_ORDER_ID, wbr2FeedEntry.SourceSerialNumber), zap.String(logger.VENDOR, wbr2FeedEntry.SourceCode))
				continue
			}

			_, ok := externallyPlacedBuyOrders[amc]
			if !ok {
				externallyPlacedBuyOrders[amc] = make(map[pb.OrderSubType][]*WBR2File)
			}

			_, ok2 := externallyPlacedSellOrders[amc]
			if !ok2 {
				externallyPlacedSellOrders[amc] = make(map[pb.OrderSubType][]*WBR2File)
			}

			if IsBuyOrderSuccessful(wbr2FeedEntry.TransactionTypeCode) {
				externallyPlacedBuyOrders[amc][pb.OrderSubType_BUY_EXTERNAL] = append(externallyPlacedBuyOrders[amc][pb.OrderSubType_BUY_EXTERNAL], wbr2FeedEntry)
				continue
			}
			if IsSellOrderSuccessful(wbr2FeedEntry.TransactionTypeCode) {
				externallyPlacedSellOrders[amc][pb.OrderSubType_SELL_EXTERNAL] = append(externallyPlacedSellOrders[amc][pb.OrderSubType_SELL_EXTERNAL], wbr2FeedEntry)
				continue
			}
			failedOrderTypeDetection[wbr2FeedEntry.SourceSerialNumber] = fmt.Sprintf("transaction type: %s not recongnised for order, userCode: %s", wbr2FeedEntry.TransactionTypeCode, wbr2FeedEntry.TransactionTypeCode)
		}
	}
	return internallyPlacedOrders, externallyPlacedSellOrders, externallyPlacedBuyOrders, failedOrderTypeDetection
}

func FilterSwitchOrders(ctx context.Context, wbr2Entries []*WBR2File, fiUserCode string) ([]*WBR2File, map[mfPb.Amc]map[pb.OrderSubType][]*WBR2File, []*WBR2File) {
	var internalSwitchOrders []*WBR2File
	var filteredWbr2Entries []*WBR2File
	externalSwitchOrders := make(map[mfPb.Amc]map[pb.OrderSubType][]*WBR2File)

	for _, val := range wbr2Entries {
		if IsSwitchInOrderSuccessful(val.TransactionTypeCode) || IsSwitchOutOrderSuccessful(val.TransactionTypeCode) {
			if strings.Compare(fiUserCode, val.SourceCode) == 0 {
				internalSwitchOrders = append(internalSwitchOrders, val)
				continue
			}
			amc, err := invPkg.GetAmcFromCode(val.AMCCode)
			if err != nil {
				logger.Error(ctx, "AMC code not supported", zap.String(logger.MF_AMC, val.AMCCode))
				continue
			}
			if len(val.TransactionTypeCode) == 0 {
				logger.Error(ctx, "received empty transaction type code", zap.String(logger.VENDOR_ORDER_ID, val.SourceSerialNumber), zap.String(logger.VENDOR, val.SourceCode))
				continue
			}

			_, ok3 := externalSwitchOrders[amc]
			if !ok3 {
				externalSwitchOrders[amc] = make(map[pb.OrderSubType][]*WBR2File)
			}

			if IsSwitchInOrderSuccessful(val.TransactionTypeCode) {
				externalSwitchOrders[amc][pb.OrderSubType_SWITCH_IN_EXTERNAL] = append(externalSwitchOrders[amc][pb.OrderSubType_SWITCH_IN_EXTERNAL], val)
				continue
			}
			if IsSwitchOutOrderSuccessful(val.TransactionTypeCode) {
				externalSwitchOrders[amc][pb.OrderSubType_SWITCH_OUT_EXTERNAL] = append(externalSwitchOrders[amc][pb.OrderSubType_SWITCH_OUT_EXTERNAL], val)
				continue
			}
		} else {
			filteredWbr2Entries = append(filteredWbr2Entries, val)
		}
	}

	return internalSwitchOrders, externalSwitchOrders, filteredWbr2Entries
}

func IsBuyOrderSuccessful(code string) bool {
	return strings.Compare(strings.ToUpper(code[:1]), "P") == 0
}

func IsSellOrderSuccessful(code string) bool {
	return strings.Compare(strings.ToUpper(code[:1]), "R") == 0
}

func IsSwitchInOrderSuccessful(code string) bool {
	return strings.Compare(strings.ToUpper(code[:2]), "SI") == 0
}

func IsSwitchOutOrderSuccessful(code string) bool {
	return strings.Compare(strings.ToUpper(code[:2]), "SO") == 0
}

// SplitOrderByCategories takes in WBR201File and splits orders into the following categories.
// internallyPlacedOrders, externallyPlacedBuyOrders, externallyPlacedSellOrders, unspecifiedOrders, unSuccessfullyParsedOrders and internalSwitchOrders.
// Orders are split into these categories as they need to be processed separately with different logic.
func SplitOrderByCategories(ctx context.Context, fileContent []byte, userCode string, filePath string) (
	internallyPlacedOrders []*WBR2File,
	externallyPlacedSellOrders map[mfPb.Amc]map[pb.OrderSubType][]*WBR2File,
	externallyPlacedBuyOrders map[mfPb.Amc]map[pb.OrderSubType][]*WBR2File,
	unspecifiedOrders map[string]string,
	unSuccessfullyParsedOrders map[string]string,
	internalSwitchOrders []*WBR2File,
	externalSwitchOrders map[mfPb.Amc]map[pb.OrderSubType][]*WBR2File,
	err error,
) {

	// Parse reverse feed file into a list of WBR2 objects
	wbR2FeedFiles, err := ParseWBR2File(ctx, fileContent)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, err
	}

	logger.Info(ctx, fmt.Sprintf("number of entries in the wbr2 file: %d", len(wbR2FeedFiles)), zap.String(logger.AWS_FILE_PATH, filePath))

	// Switch orders have certain edge cases because of which they need to filtered out and handled separately.
	internalSwitchOrders, externalSwitchOrders, filteredWbr2FeedFiles := FilterSwitchOrders(ctx, wbR2FeedFiles, userCode)

	// For some orders, rta sometimes splits the order into multiple rows in the file.
	// For eg: 	If user places an order for ₹100 with vendor order id v1, then there could be 2 entries in the file
	// 			with ₹70 and ₹30 each. We don't know on what basis rta splits these order, but the sum of entries in
	//			this file for an order should be equivalent to order that user has placed for. So, we group the entries
	//			present in the file by (UserCode, VendorOrderID) and aggregate them so that these can be sent to as a
	//			single entry/order to orderManager service.
	wbR2FeedFiles, unSuccessfullyParsedOrders, err = AggregateWBR2File(ctx, filteredWbr2FeedFiles)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, err
	}

	internallyPlacedOrders, externallyPlacedSellOrders, externallyPlacedBuyOrders, unspecifiedOrders = FilterExternalAndInternalOrders(ctx, wbR2FeedFiles, userCode)

	return internallyPlacedOrders, externallyPlacedSellOrders, externallyPlacedBuyOrders, unspecifiedOrders, unSuccessfullyParsedOrders, internalSwitchOrders, externalSwitchOrders, nil
}

// GetBatchOrderRequest converts a feed chunk array to an OrderUpdate array
// It does it on a best effort basis while silently logging any errors occurred during the parsing of a feed file
//
//nolint:funlen
func GetBatchOrderRequest(ctx context.Context, feedFile []*WBR2File) ([]*pb.OrderConfirmationDetail, map[string]*pb.OrderRejectionMetaData, map[string]string, error) {
	var res []*pb.OrderConfirmationDetail
	orderRejectionInfoMap := make(map[string]*pb.OrderRejectionMetaData)
	failedVendorOrderIDToReasonMap := make(map[string]string)
	for _, feed := range feedFile {
		amt, err := ParseToMoney(feed.Amount)
		if err != nil {
			logger.Error(ctx, "error while parsing feed amount", zap.Error(err), zap.String("file_type", "wbr2"),
				zap.String(logger.VENDOR_ORDER_ID, feed.SourceSerialNumber))
			failedVendorOrderIDToReasonMap[feed.SourceSerialNumber] = "error while parsing feed amount: " + err.Error()
			continue
		}
		price, err := ParseToMoney(feed.Price)
		if err != nil {
			logger.Error(ctx, "error while parsing feed price", zap.Error(err), zap.String("file_type", "wbr2"),
				zap.String(logger.VENDOR_ORDER_ID, feed.SourceSerialNumber))
			failedVendorOrderIDToReasonMap[feed.SourceSerialNumber] = "error while parsing feed price: " + err.Error()
			continue
		}
		units, err := strconv.ParseFloat(feed.Units, 64)
		if err != nil {
			logger.Error(ctx, "error while parsing feed units", zap.Error(err), zap.String("file_type", "wbr2"),
				zap.String(logger.VENDOR_ORDER_ID, feed.SourceSerialNumber))
			failedVendorOrderIDToReasonMap[feed.SourceSerialNumber] = "error while parsing feed units: " + err.Error()
			continue
		}

		amc, err := invPkg.GetAmcFromCode(feed.AMCCode)
		if err != nil {
			logger.Error(ctx, "error in GetAmcFromCode", zap.Error(err), zap.String(logger.MF_AMC, feed.AMCCode), zap.String("file_type", "wbr2"), zap.String(logger.VENDOR_ORDER_ID, feed.SourceSerialNumber))
			failedVendorOrderIDToReasonMap[feed.SourceSerialNumber] = "error in  GetAmcFromCode" + err.Error()
			continue
		}

		if len(feed.TransactionTypeCode) > 0 && strings.Compare(fmt.Sprintf("%c", strings.ToLower(feed.TransactionTypeCode)[0]), "j") == 0 {
			orderRejectionInfoMap[feed.SourceSerialNumber] = &pb.OrderRejectionMetaData{
				RejectionReason: fmt.Sprintf("TransactionTypeCode: %s represents rejection", feed.TransactionTypeCode),
				Amc:             amc,
				FolioId:         feed.FolioNumber,
				SchemeCode:      scheme_code_converter.GetSchemeCode(amc, feed.ProductCode[len(feed.AMCCode):]),
			}
			continue
		}

		if !money.IsPositive(amt) || units <= 0 {
			orderRejectionInfoMap[feed.SourceSerialNumber] = &pb.OrderRejectionMetaData{
				RejectionReason: fmt.Sprintf("atleast one of aggregated units: %v, amount: %v, not positive", units, amt),
				Amc:             amc,
				FolioId:         feed.FolioNumber,
				SchemeCode:      scheme_code_converter.GetSchemeCode(amc, feed.ProductCode[len(feed.AMCCode):]),
			}
			continue
		}

		if !money.IsPositive(price) {
			rejectionReason := fmt.Sprintf("NAV present in feed is not positive. nav: %v ", price)
			logger.Error(ctx, rejectionReason, zap.String("remarks", feed.Remarks),
				zap.String(logger.VENDOR_ORDER_ID, feed.SourceSerialNumber))
			failedVendorOrderIDToReasonMap[feed.SourceSerialNumber] = rejectionReason
			continue
		}

		orderConfirmationMetaData, err := ConvertWBR2ToMetaData(ctx, feed)
		if err != nil {
			logger.Error(ctx, "error while parsing orderConfirmationMetaData", zap.Error(err), zap.String("file_type", "wbr2"),
				zap.String(logger.VENDOR_ORDER_ID, feed.SourceSerialNumber))
			failedVendorOrderIDToReasonMap[feed.SourceSerialNumber] = "error while parsing orderConfirmationMetaData " + err.Error()
			continue
		}

		orderType, err := getOrderType(feed)
		if err != nil {
			logger.Error(ctx, "error in getOrderType", zap.Error(err), zap.String(logger.MF_AMC, feed.AMCCode), zap.String("file_type", "wbr2"), zap.String(logger.VENDOR_ORDER_ID, feed.SourceSerialNumber))
			failedVendorOrderIDToReasonMap[feed.SourceSerialNumber] = "error in getOrderType" + err.Error()
			continue
		}

		res = append(res, &pb.OrderConfirmationDetail{
			OrderType:                 orderType,
			VendorOrderId:             feed.SourceSerialNumber,
			Amount:                    amt,
			Nav:                       price,
			UnitsAllocated:            units,
			FolioId:                   feed.FolioNumber,
			RtaTransactionNumber:      feed.TransactionNumber,
			OrderConfirmationMetaData: orderConfirmationMetaData,
			UserCode:                  feed.SourceCode,
			SchemeCode:                scheme_code_converter.GetSchemeCode(amc, feed.ProductCode[len(feed.AMCCode):]),
			OrderAllocatedDate:        datetime.TimestampToDateInLoc(orderConfirmationMetaData.GetAllotmentDate(), datetime.IST),
			Amc:                       amc,
			PostDate:                  datetime.TimestampToDateInLoc(orderConfirmationMetaData.GetOrderDate(), datetime.IST),
		})
	}
	return res, orderRejectionInfoMap, failedVendorOrderIDToReasonMap, nil
}

func getOrderType(wbr2Feed *WBR2File) (pb.OrderType, error) {
	if IsBuyOrderSuccessful(wbr2Feed.TransactionTypeCode) || IsSwitchInOrderSuccessful(wbr2Feed.TransactionTypeCode) {
		return pb.OrderType_BUY, nil
	}
	if IsSellOrderSuccessful(wbr2Feed.TransactionTypeCode) || IsSwitchOutOrderSuccessful(wbr2Feed.TransactionTypeCode) {
		return pb.OrderType_SELL, nil
	}
	return pb.OrderType_ORDER_TYPE_UNSPECIFIED, fmt.Errorf("unsupported transaction type code: %s", wbr2Feed.TransactionTypeCode)
}
