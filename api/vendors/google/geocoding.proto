syntax = "proto3";

package vendors.google;

option go_package = "github.com/epifi/gamma/api/vendors/google";
option java_package = "com.github.epifi.gamma.api.vendors.google";

message ReverseGeocodingRequest {
  string lat_lng = 1 [json_name= "latlng"];
}

message ReverseGeocodingResponse {
  repeated ReverseGeocodingResponseResult results = 1 [json_name = "results"];
  string status = 2 [json_name = "status"];
}

message ReverseGeocodingResponseResult {
  repeated ReverseGeocodingResponseAddressComponents address_components = 1 [json_name = "address_components"];
  string place_id = 2 [json_name = "place_id"];
}

message ReverseGeocodingResponseAddressComponents {
  string long_name = 1 [json_name = "long_name"];
  string short_name = 2 [json_name = "short_name"];
  repeated string types = 3 [json_name = "types"];
}

message GeocodingRequest {

}

message GeocodingResponse {
  repeated Result results = 1 [json_name = "results"];
  string status = 3 [json_name = "status"];
}

message Result {
  Geometry geometry = 1 [json_name = "geometry"];
  string formatted_address = 2 [json_name = "formatted_address"];
}

message Geometry {
  Location location = 1 [json_name = "location"];
}

message Location {
  float lat = 1 [json_name = "lat"];
  float lng = 2 [json_name = "lng"];;
}
