// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/securities/catalog/model_security_listing.proto

package catalog

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on SecurityListing with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SecurityListing) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecurityListing with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecurityListingMultiError, or nil if none found.
func (m *SecurityListing) ValidateAll() error {
	return m.validate(true)
}

func (m *SecurityListing) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InternalId

	// no validation rules for ExternalId

	// no validation rules for SecurityId

	// no validation rules for Exchange

	// no validation rules for Symbol

	// no validation rules for IsPrimaryListing

	// no validation rules for Status

	// no validation rules for Isin

	// no validation rules for Vendor

	// no validation rules for VendorListingId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityListingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityListingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityListingValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityListingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityListingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityListingValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityListingValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityListingValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityListingValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SecurityListingMultiError(errors)
	}

	return nil
}

// SecurityListingMultiError is an error wrapping multiple validation errors
// returned by SecurityListing.ValidateAll() if the designated constraints
// aren't met.
type SecurityListingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecurityListingMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecurityListingMultiError) AllErrors() []error { return m }

// SecurityListingValidationError is the validation error returned by
// SecurityListing.Validate if the designated constraints aren't met.
type SecurityListingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecurityListingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecurityListingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecurityListingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecurityListingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecurityListingValidationError) ErrorName() string { return "SecurityListingValidationError" }

// Error satisfies the builtin error interface
func (e SecurityListingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecurityListing.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecurityListingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecurityListingValidationError{}
