// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/rewards/reward.proto

package rewards

import (
	queue "github.com/epifi/be-common/api/queue"
	money "google.golang.org/genproto/googleapis/type/money"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Types of Rewards
// Todo : Update RewardOptionSchema in epiview/src/main/scala/com.epifi.dataplatform.epiview/commons/rewards/RewardsConstants.scala whenever ths is updated accordingly
type RewardType int32

const (
	RewardType_REWARD_TYPE_UNSPECIFIED RewardType = 0
	// NO_REWARD denotes no reward was given.
	// used for cases where no reward was given.
	// eg : in case of a lucky draw.
	RewardType_NO_REWARD               RewardType = 1
	RewardType_CASH                    RewardType = 2
	RewardType_FI_COINS                RewardType = 3
	RewardType_LUCKY_DRAW              RewardType = 4
	RewardType_SMART_DEPOSIT           RewardType = 5
	RewardType_GIFT_HAMPER             RewardType = 6
	RewardType_METAL_CREDIT_CARD       RewardType = 7
	RewardType_EGV_BASKET              RewardType = 8
	RewardType_THRIWE_BENEFITS_PACKAGE RewardType = 9
	RewardType_US_STOCK                RewardType = 10
	RewardType_CREDIT_CARD_BILL_ERASER RewardType = 11
)

// Enum value maps for RewardType.
var (
	RewardType_name = map[int32]string{
		0:  "REWARD_TYPE_UNSPECIFIED",
		1:  "NO_REWARD",
		2:  "CASH",
		3:  "FI_COINS",
		4:  "LUCKY_DRAW",
		5:  "SMART_DEPOSIT",
		6:  "GIFT_HAMPER",
		7:  "METAL_CREDIT_CARD",
		8:  "EGV_BASKET",
		9:  "THRIWE_BENEFITS_PACKAGE",
		10: "US_STOCK",
		11: "CREDIT_CARD_BILL_ERASER",
	}
	RewardType_value = map[string]int32{
		"REWARD_TYPE_UNSPECIFIED": 0,
		"NO_REWARD":               1,
		"CASH":                    2,
		"FI_COINS":                3,
		"LUCKY_DRAW":              4,
		"SMART_DEPOSIT":           5,
		"GIFT_HAMPER":             6,
		"METAL_CREDIT_CARD":       7,
		"EGV_BASKET":              8,
		"THRIWE_BENEFITS_PACKAGE": 9,
		"US_STOCK":                10,
		"CREDIT_CARD_BILL_ERASER": 11,
	}
)

func (x RewardType) Enum() *RewardType {
	p := new(RewardType)
	*p = x
	return p
}

func (x RewardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_reward_proto_enumTypes[0].Descriptor()
}

func (RewardType) Type() protoreflect.EnumType {
	return &file_api_rewards_reward_proto_enumTypes[0]
}

func (x RewardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardType.Descriptor instead.
func (RewardType) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{0}
}

// Status of rewards
type RewardStatus int32

const (
	// this status will be used to create reward but process it on-demand when user asks to redeem
	RewardStatus_CREATED RewardStatus = 0
	// this status will be used to create reward and auto process it without asking the user to redeem it
	RewardStatus_PROCESSING_PENDING RewardStatus = 1
	// this status will be used when reward redemption is pending at dependent system side
	// like in case of cash reward, if the payment is pending at bank side
	RewardStatus_PROCESSING_IN_PROGRESS RewardStatus = 2
	// for successfully processing the reward
	RewardStatus_PROCESSED RewardStatus = 3
	// for if processing is unsuccessful
	RewardStatus_PROCESSING_FAILED RewardStatus = 4
	// for reward failures like frozen account, min kyc account, etc which need manual intervention
	RewardStatus_PROCESSING_MANUAL_INTERVENTION RewardStatus = 5
	// for when a generated reward is taken back. it's a terminal state.
	RewardStatus_CLAWED_BACK RewardStatus = 6
	// for when the reward is generated but LOCKED, and can't be fulfilled
	RewardStatus_LOCKED RewardStatus = 7
	// for when the actor's account closed & reward didn't processed yet.
	RewardStatus_EXPIRED RewardStatus = 8
)

// Enum value maps for RewardStatus.
var (
	RewardStatus_name = map[int32]string{
		0: "CREATED",
		1: "PROCESSING_PENDING",
		2: "PROCESSING_IN_PROGRESS",
		3: "PROCESSED",
		4: "PROCESSING_FAILED",
		5: "PROCESSING_MANUAL_INTERVENTION",
		6: "CLAWED_BACK",
		7: "LOCKED",
		8: "EXPIRED",
	}
	RewardStatus_value = map[string]int32{
		"CREATED":                        0,
		"PROCESSING_PENDING":             1,
		"PROCESSING_IN_PROGRESS":         2,
		"PROCESSED":                      3,
		"PROCESSING_FAILED":              4,
		"PROCESSING_MANUAL_INTERVENTION": 5,
		"CLAWED_BACK":                    6,
		"LOCKED":                         7,
		"EXPIRED":                        8,
	}
)

func (x RewardStatus) Enum() *RewardStatus {
	p := new(RewardStatus)
	*p = x
	return p
}

func (x RewardStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_reward_proto_enumTypes[1].Descriptor()
}

func (RewardStatus) Type() protoreflect.EnumType {
	return &file_api_rewards_reward_proto_enumTypes[1]
}

func (x RewardStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardStatus.Descriptor instead.
func (RewardStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{1}
}

// TODO : Update ValidRewardSubStatus in data-platform whenever SubStatus is updated : src/main/scala/com/epifi/dataplatform/epiview/commons/rewards/RewardsConstants.scala
type SubStatus int32

const (
	SubStatus_SUB_STATUS_UNSPECIFIED SubStatus = 0
	// sub statuses for `LOCKED` status
	// `SUB_STATUS_IMPLICITLY_LOCKED` means that implicit (code level) locking is in effect.
	// eg. when a reward is locked as user is a Min KYC or Fi-Lite user
	SubStatus_SUB_STATUS_IMPLICITLY_LOCKED SubStatus = 1
	// `SUB_STATUS_EXPLICITLY_LOCKED` means that explicit locking (configured for reward offer) is in effect
	// eg. when a reward is locked on tier update event.
	SubStatus_SUB_STATUS_EXPLICITLY_LOCKED SubStatus = 2
	// sub status for 'EXPIRY' status
	// eg. when user's account has been closed completely then we update to expiry state.
	SubStatus_SUB_STATUS_ACCOUNT_CLOSURE_EXPIRY SubStatus = 3
	// sub status when the fi-coins have expired
	SubStatus_SUB_STATUS_FI_COINS_EXPIRED SubStatus = 4
	// sub statuses for `PROCESSED` status (intentional gap in field numbers as these statuses)
	SubStatus_SUB_STATUS_CREDITED_FI_COINS         SubStatus = 100
	SubStatus_SUB_STATUS_REGISTERED_FOR_LUCKY_DRAW SubStatus = 101
	// sub statuses for `MANUAL_INTERVENTION` status
	SubStatus_SUB_STATUS_TRANSACTION_NOT_ALLOWED SubStatus = 102
)

// Enum value maps for SubStatus.
var (
	SubStatus_name = map[int32]string{
		0:   "SUB_STATUS_UNSPECIFIED",
		1:   "SUB_STATUS_IMPLICITLY_LOCKED",
		2:   "SUB_STATUS_EXPLICITLY_LOCKED",
		3:   "SUB_STATUS_ACCOUNT_CLOSURE_EXPIRY",
		4:   "SUB_STATUS_FI_COINS_EXPIRED",
		100: "SUB_STATUS_CREDITED_FI_COINS",
		101: "SUB_STATUS_REGISTERED_FOR_LUCKY_DRAW",
		102: "SUB_STATUS_TRANSACTION_NOT_ALLOWED",
	}
	SubStatus_value = map[string]int32{
		"SUB_STATUS_UNSPECIFIED":               0,
		"SUB_STATUS_IMPLICITLY_LOCKED":         1,
		"SUB_STATUS_EXPLICITLY_LOCKED":         2,
		"SUB_STATUS_ACCOUNT_CLOSURE_EXPIRY":    3,
		"SUB_STATUS_FI_COINS_EXPIRED":          4,
		"SUB_STATUS_CREDITED_FI_COINS":         100,
		"SUB_STATUS_REGISTERED_FOR_LUCKY_DRAW": 101,
		"SUB_STATUS_TRANSACTION_NOT_ALLOWED":   102,
	}
)

func (x SubStatus) Enum() *SubStatus {
	p := new(SubStatus)
	*p = x
	return p
}

func (x SubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_reward_proto_enumTypes[2].Descriptor()
}

func (SubStatus) Type() protoreflect.EnumType {
	return &file_api_rewards_reward_proto_enumTypes[2]
}

func (x SubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubStatus.Descriptor instead.
func (SubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{2}
}

type ClawbackReason int32

const (
	ClawbackReason_CLAWBACK_REASON_UNSPECIFIED ClawbackReason = 0
	// for cases when a reward is clawed back if the transaction for which the reward was generated was reversed.
	// e.g. credit card refunds
	ClawbackReason_TXN_REVERSAL ClawbackReason = 1
)

// Enum value maps for ClawbackReason.
var (
	ClawbackReason_name = map[int32]string{
		0: "CLAWBACK_REASON_UNSPECIFIED",
		1: "TXN_REVERSAL",
	}
	ClawbackReason_value = map[string]int32{
		"CLAWBACK_REASON_UNSPECIFIED": 0,
		"TXN_REVERSAL":                1,
	}
)

func (x ClawbackReason) Enum() *ClawbackReason {
	p := new(ClawbackReason)
	*p = x
	return p
}

func (x ClawbackReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClawbackReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_reward_proto_enumTypes[3].Descriptor()
}

func (ClawbackReason) Type() protoreflect.EnumType {
	return &file_api_rewards_reward_proto_enumTypes[3]
}

func (x ClawbackReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClawbackReason.Descriptor instead.
func (ClawbackReason) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{3}
}

// defines the theme for displaying reward tile on APP.
// Based on theme, the APP decides the images to show on reward tile and corresponding animation to
// play while claiming the reward. If RewardTileThemeType is THEME_TYPE_UNSPECIFIED, the APP fallbacks
// to a default theme.
type RewardTileThemeType int32

const (
	RewardTileThemeType_REWARD_TILE_THEME_UNSPECIFIED RewardTileThemeType = 0
	RewardTileThemeType_PLANT_THEME_CHRYSANTHEMUM     RewardTileThemeType = 1
	RewardTileThemeType_PLANT_THEME_ORCHID            RewardTileThemeType = 2
)

// Enum value maps for RewardTileThemeType.
var (
	RewardTileThemeType_name = map[int32]string{
		0: "REWARD_TILE_THEME_UNSPECIFIED",
		1: "PLANT_THEME_CHRYSANTHEMUM",
		2: "PLANT_THEME_ORCHID",
	}
	RewardTileThemeType_value = map[string]int32{
		"REWARD_TILE_THEME_UNSPECIFIED": 0,
		"PLANT_THEME_CHRYSANTHEMUM":     1,
		"PLANT_THEME_ORCHID":            2,
	}
)

func (x RewardTileThemeType) Enum() *RewardTileThemeType {
	p := new(RewardTileThemeType)
	*p = x
	return p
}

func (x RewardTileThemeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardTileThemeType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_reward_proto_enumTypes[4].Descriptor()
}

func (RewardTileThemeType) Type() protoreflect.EnumType {
	return &file_api_rewards_reward_proto_enumTypes[4]
}

func (x RewardTileThemeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardTileThemeType.Descriptor instead.
func (RewardTileThemeType) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{4}
}

type ClaimFlowRewardValueRevealType int32

const (
	ClaimFlowRewardValueRevealType_CLAIM_FLOW_REVEAL_TYPE_UNSPECIFIED ClaimFlowRewardValueRevealType = 0
	// intermediate reward values (not the delta) ending with the final value
	ClaimFlowRewardValueRevealType_CLAIM_FLOW_REWARD_VALUE_REVEAL_TYPE_INTERMEDIATE_VALUES ClaimFlowRewardValueRevealType = 1
	// only the base reward value will be shown for each reveal. The final value will be shown in the end of claim flow
	ClaimFlowRewardValueRevealType_CLAIM_FLOW_REWARD_VALUE_REVEAL_TYPE_BASE_VALUE ClaimFlowRewardValueRevealType = 2
)

// Enum value maps for ClaimFlowRewardValueRevealType.
var (
	ClaimFlowRewardValueRevealType_name = map[int32]string{
		0: "CLAIM_FLOW_REVEAL_TYPE_UNSPECIFIED",
		1: "CLAIM_FLOW_REWARD_VALUE_REVEAL_TYPE_INTERMEDIATE_VALUES",
		2: "CLAIM_FLOW_REWARD_VALUE_REVEAL_TYPE_BASE_VALUE",
	}
	ClaimFlowRewardValueRevealType_value = map[string]int32{
		"CLAIM_FLOW_REVEAL_TYPE_UNSPECIFIED":                      0,
		"CLAIM_FLOW_REWARD_VALUE_REVEAL_TYPE_INTERMEDIATE_VALUES": 1,
		"CLAIM_FLOW_REWARD_VALUE_REVEAL_TYPE_BASE_VALUE":          2,
	}
)

func (x ClaimFlowRewardValueRevealType) Enum() *ClaimFlowRewardValueRevealType {
	p := new(ClaimFlowRewardValueRevealType)
	*p = x
	return p
}

func (x ClaimFlowRewardValueRevealType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClaimFlowRewardValueRevealType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_reward_proto_enumTypes[5].Descriptor()
}

func (ClaimFlowRewardValueRevealType) Type() protoreflect.EnumType {
	return &file_api_rewards_reward_proto_enumTypes[5]
}

func (x ClaimFlowRewardValueRevealType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClaimFlowRewardValueRevealType.Descriptor instead.
func (ClaimFlowRewardValueRevealType) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{5}
}

// RewardTag contains tags which can be used for purposes such as display, filtering etc. The scope can be extended/limited later.
// For e.g., (display purpose) if ACCOUNT_TIER_2 is present, then we will show the tag on the reward tile.
type RewardTag int32

const (
	RewardTag_REWARD_DISPLAY_TAG_UNSPECIFIED RewardTag = 0
	RewardTag_TIER_FI_BASIC                  RewardTag = 1
	RewardTag_TIER_FI_PLUS                   RewardTag = 2
	RewardTag_TIER_FI_INFINITE               RewardTag = 3
	RewardTag_SALARY_PROGRAM                 RewardTag = 4
	RewardTag_CREDIT_CARD                    RewardTag = 5
	// booster/multiplier specific
	RewardTag_BOOSTER_1X                    RewardTag = 6
	RewardTag_BOOSTER_2X                    RewardTag = 7
	RewardTag_BOOSTER_3X                    RewardTag = 8
	RewardTag_BOOSTER_10_PERCENT            RewardTag = 9
	RewardTag_BOOSTER_15_PERCENT            RewardTag = 10
	RewardTag_BOOSTER_20_PERCENT            RewardTag = 11
	RewardTag_BOOSTER_50_PERCENT            RewardTag = 12
	RewardTag_BOOSTER_100_PERCENT           RewardTag = 13
	RewardTag_CREDIT_CARD_SPENDS_2X_BOOSTER RewardTag = 14
	RewardTag_CREDIT_CARD_SPENDS_5X_BOOSTER RewardTag = 15
)

// Enum value maps for RewardTag.
var (
	RewardTag_name = map[int32]string{
		0:  "REWARD_DISPLAY_TAG_UNSPECIFIED",
		1:  "TIER_FI_BASIC",
		2:  "TIER_FI_PLUS",
		3:  "TIER_FI_INFINITE",
		4:  "SALARY_PROGRAM",
		5:  "CREDIT_CARD",
		6:  "BOOSTER_1X",
		7:  "BOOSTER_2X",
		8:  "BOOSTER_3X",
		9:  "BOOSTER_10_PERCENT",
		10: "BOOSTER_15_PERCENT",
		11: "BOOSTER_20_PERCENT",
		12: "BOOSTER_50_PERCENT",
		13: "BOOSTER_100_PERCENT",
		14: "CREDIT_CARD_SPENDS_2X_BOOSTER",
		15: "CREDIT_CARD_SPENDS_5X_BOOSTER",
	}
	RewardTag_value = map[string]int32{
		"REWARD_DISPLAY_TAG_UNSPECIFIED": 0,
		"TIER_FI_BASIC":                  1,
		"TIER_FI_PLUS":                   2,
		"TIER_FI_INFINITE":               3,
		"SALARY_PROGRAM":                 4,
		"CREDIT_CARD":                    5,
		"BOOSTER_1X":                     6,
		"BOOSTER_2X":                     7,
		"BOOSTER_3X":                     8,
		"BOOSTER_10_PERCENT":             9,
		"BOOSTER_15_PERCENT":             10,
		"BOOSTER_20_PERCENT":             11,
		"BOOSTER_50_PERCENT":             12,
		"BOOSTER_100_PERCENT":            13,
		"CREDIT_CARD_SPENDS_2X_BOOSTER":  14,
		"CREDIT_CARD_SPENDS_5X_BOOSTER":  15,
	}
)

func (x RewardTag) Enum() *RewardTag {
	p := new(RewardTag)
	*p = x
	return p
}

func (x RewardTag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardTag) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_reward_proto_enumTypes[6].Descriptor()
}

func (RewardTag) Type() protoreflect.EnumType {
	return &file_api_rewards_reward_proto_enumTypes[6]
}

func (x RewardTag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardTag.Descriptor instead.
func (RewardTag) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{6}
}

type ClaimType int32

const (
	ClaimType_CLAIM_TYPE_UNSPECIFIED ClaimType = 0
	// denotes that the reward will be required to be claimed by the user on the app
	ClaimType_CLAIM_TYPE_MANUAL ClaimType = 1
	// denotes that the reward will be auto-claimed without need of user to claim the reward
	ClaimType_CLAIM_TYPE_AUTOMATIC ClaimType = 2
)

// Enum value maps for ClaimType.
var (
	ClaimType_name = map[int32]string{
		0: "CLAIM_TYPE_UNSPECIFIED",
		1: "CLAIM_TYPE_MANUAL",
		2: "CLAIM_TYPE_AUTOMATIC",
	}
	ClaimType_value = map[string]int32{
		"CLAIM_TYPE_UNSPECIFIED": 0,
		"CLAIM_TYPE_MANUAL":      1,
		"CLAIM_TYPE_AUTOMATIC":   2,
	}
)

func (x ClaimType) Enum() *ClaimType {
	p := new(ClaimType)
	*p = x
	return p
}

func (x ClaimType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClaimType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_reward_proto_enumTypes[7].Descriptor()
}

func (ClaimType) Type() protoreflect.EnumType {
	return &file_api_rewards_reward_proto_enumTypes[7]
}

func (x ClaimType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClaimType.Descriptor instead.
func (ClaimType) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{7}
}

// proto for reward. Will be used to communicate between reward generator and processor and also in the APIs
type Reward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	RefId string `protobuf:"bytes,2,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	// the secondary identifier of the action that generated the reward.
	// This allows us to generate multiple rewards for the same (actor_id, offer_id, ref_id) combination
	SecondaryRefId string       `protobuf:"bytes,28,opt,name=secondary_ref_id,json=secondaryRefId,proto3" json:"secondary_ref_id,omitempty"`
	ActorId        string       `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Status         RewardStatus `protobuf:"varint,4,opt,name=status,proto3,enum=rewards.RewardStatus" json:"status,omitempty"`
	// deprecated in favour of sub_status_v2
	//
	// Deprecated: Marked as deprecated in api/rewards/reward.proto.
	SubStatus     string                 `protobuf:"bytes,5,opt,name=sub_status,json=subStatus,proto3" json:"sub_status,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	RewardOptions *RewardOptions         `protobuf:"bytes,7,opt,name=reward_options,json=rewardOptions,proto3" json:"reward_options,omitempty"`
	ChosenReward  *RewardOption          `protobuf:"bytes,8,opt,name=chosen_reward,json=chosenReward,proto3" json:"chosen_reward,omitempty"`
	// if it's visible on the UI or not
	IsVisible bool `protobuf:"varint,9,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	// payment reference like order id in case of cash reward
	ProcessingRef string `protobuf:"bytes,10,opt,name=processing_ref,json=processingRef,proto3" json:"processing_ref,omitempty"`
	// external_id is equivalent to reward id, but this external_id can be displayed
	// on the app while id shouldn't be due to security reasons.
	ExternalId string `protobuf:"bytes,11,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// display related properties for reward.
	RewardDisplay *RewardDisplay `protobuf:"bytes,12,opt,name=reward_display,json=rewardDisplay,proto3" json:"reward_display,omitempty"`
	// rewardOfferId which is responsible for this reward
	OfferId string `protobuf:"bytes,13,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	// type of reward offer which is responsible for this reward.
	OfferType RewardOfferType `protobuf:"varint,14,opt,name=offer_type,json=offerType,proto3,enum=rewards.RewardOfferType" json:"offer_type,omitempty"`
	// to link reward with an external ref/entity e.g. finiteCodeClaimId for referral rewards.
	ExternalRef string `protobuf:"bytes,15,opt,name=external_ref,json=externalRef,proto3" json:"external_ref,omitempty"`
	// reason because of which the reward was clawed back. it will be CLAW_BACK_REASON_UNSPECIFIED unless reward has been moved to CLAWED_BACK state
	ClawbackReason ClawbackReason `protobuf:"varint,16,opt,name=clawback_reason,json=clawbackReason,proto3,enum=rewards.ClawbackReason" json:"clawback_reason,omitempty"`
	// reference ID of event which lead to claw back of reward. it will be empty unless reward is clawed-back
	ClawbackRefId string `protobuf:"bytes,17,opt,name=clawback_ref_id,json=clawbackRefId,proto3" json:"clawback_ref_id,omitempty"`
	// tags for rewards.
	// current use case is limited to display. The scope can be extended later, for e.g. filtering
	Tags []RewardTag `protobuf:"varint,18,rep,packed,name=tags,proto3,enum=rewards.RewardTag" json:"tags,omitempty"`
	// type of event that was collected that lead to this reward being generated
	ActionType CollectedDataType `protobuf:"varint,20,opt,name=action_type,json=actionType,proto3,enum=rewards.CollectedDataType" json:"action_type,omitempty"`
	// reward specific metadata. can include any additional information related to the generated reward
	RewardMetadata *RewardMetadata `protobuf:"bytes,21,opt,name=reward_metadata,json=rewardMetadata,proto3" json:"reward_metadata,omitempty"`
	// denotes how the reward will be claimed post generation
	ClaimType ClaimType `protobuf:"varint,22,opt,name=claim_type,json=claimType,proto3,enum=rewards.ClaimType" json:"claim_type,omitempty"`
	// denotes additional information related to the current status of the reward
	SubStatusV2 SubStatus `protobuf:"varint,23,opt,name=sub_status_v2,json=subStatusV2,proto3,enum=rewards.SubStatus" json:"sub_status_v2,omitempty"`
	// time at which the reward generating action occurred
	ActionTime *timestamppb.Timestamp `protobuf:"bytes,24,opt,name=action_time,json=actionTime,proto3" json:"action_time,omitempty"`
	UpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,25,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt  *timestamppb.Timestamp `protobuf:"bytes,26,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// denotes the time at which the reward will expire, is null then the reward will not auto expire
	ExpiresAt *timestamppb.Timestamp `protobuf:"bytes,27,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
}

func (x *Reward) Reset() {
	*x = Reward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reward) ProtoMessage() {}

func (x *Reward) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reward.ProtoReflect.Descriptor instead.
func (*Reward) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{0}
}

func (x *Reward) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Reward) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

func (x *Reward) GetSecondaryRefId() string {
	if x != nil {
		return x.SecondaryRefId
	}
	return ""
}

func (x *Reward) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *Reward) GetStatus() RewardStatus {
	if x != nil {
		return x.Status
	}
	return RewardStatus_CREATED
}

// Deprecated: Marked as deprecated in api/rewards/reward.proto.
func (x *Reward) GetSubStatus() string {
	if x != nil {
		return x.SubStatus
	}
	return ""
}

func (x *Reward) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Reward) GetRewardOptions() *RewardOptions {
	if x != nil {
		return x.RewardOptions
	}
	return nil
}

func (x *Reward) GetChosenReward() *RewardOption {
	if x != nil {
		return x.ChosenReward
	}
	return nil
}

func (x *Reward) GetIsVisible() bool {
	if x != nil {
		return x.IsVisible
	}
	return false
}

func (x *Reward) GetProcessingRef() string {
	if x != nil {
		return x.ProcessingRef
	}
	return ""
}

func (x *Reward) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *Reward) GetRewardDisplay() *RewardDisplay {
	if x != nil {
		return x.RewardDisplay
	}
	return nil
}

func (x *Reward) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *Reward) GetOfferType() RewardOfferType {
	if x != nil {
		return x.OfferType
	}
	return RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE
}

func (x *Reward) GetExternalRef() string {
	if x != nil {
		return x.ExternalRef
	}
	return ""
}

func (x *Reward) GetClawbackReason() ClawbackReason {
	if x != nil {
		return x.ClawbackReason
	}
	return ClawbackReason_CLAWBACK_REASON_UNSPECIFIED
}

func (x *Reward) GetClawbackRefId() string {
	if x != nil {
		return x.ClawbackRefId
	}
	return ""
}

func (x *Reward) GetTags() []RewardTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Reward) GetActionType() CollectedDataType {
	if x != nil {
		return x.ActionType
	}
	return CollectedDataType_UNSPECIFIED_COLLECTED_DATA_TYPE
}

func (x *Reward) GetRewardMetadata() *RewardMetadata {
	if x != nil {
		return x.RewardMetadata
	}
	return nil
}

func (x *Reward) GetClaimType() ClaimType {
	if x != nil {
		return x.ClaimType
	}
	return ClaimType_CLAIM_TYPE_UNSPECIFIED
}

func (x *Reward) GetSubStatusV2() SubStatus {
	if x != nil {
		return x.SubStatusV2
	}
	return SubStatus_SUB_STATUS_UNSPECIFIED
}

func (x *Reward) GetActionTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ActionTime
	}
	return nil
}

func (x *Reward) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Reward) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *Reward) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

// RewardDisplay stores display related properties for reward.
type RewardDisplay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// background image to display on reward tile when reward is in unclaimed state.
	TileBgImageBeforeClaim string `protobuf:"bytes,1,opt,name=tile_bg_image_before_claim,json=tileBgImageBeforeClaim,proto3" json:"tile_bg_image_before_claim,omitempty"`
	// background image to display on reward tile after reward is claimed.
	TileBgImageAfterClaim string `protobuf:"bytes,2,opt,name=tile_bg_image_after_claim,json=tileBgImageAfterClaim,proto3" json:"tile_bg_image_after_claim,omitempty"`
	// theme for displaying reward tile on APP. Based on theme, the APP decides the images to show on reward tile
	// and corresponding animation to play while claiming the reward
	RewardTileThemeType RewardTileThemeType `protobuf:"varint,3,opt,name=reward_tile_theme_type,json=rewardTileThemeType,proto3,enum=rewards.RewardTileThemeType" json:"reward_tile_theme_type,omitempty"`
	// flag to decide whether we want the flower animation to be skippable or not. it's named this way to keep the default
	// value as 'false' and only make animation un-skippable if offer is configured that way
	IsAnimationUnskippable bool `protobuf:"varint,4,opt,name=is_animation_unskippable,json=isAnimationUnskippable,proto3" json:"is_animation_unskippable,omitempty"`
	// flag to decide whether we'd want to skip the whole animation altogether
	// use case: in case booster is applied, we'd want to skip animation to give time for further frames
	SkipAnimation bool `protobuf:"varint,5,opt,name=skip_animation,json=skipAnimation,proto3" json:"skip_animation,omitempty"`
	// Determines how fast the money plant animation should run
	// note: The expected default value is 1.5, 1.0 for normal speed, 1.5 for faster, 0.5 for slower and so on
	AnimationSpeed float32 `protobuf:"fixed32,6,opt,name=animation_speed,json=animationSpeed,proto3" json:"animation_speed,omitempty"`
}

func (x *RewardDisplay) Reset() {
	*x = RewardDisplay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardDisplay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardDisplay) ProtoMessage() {}

func (x *RewardDisplay) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardDisplay.ProtoReflect.Descriptor instead.
func (*RewardDisplay) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{1}
}

func (x *RewardDisplay) GetTileBgImageBeforeClaim() string {
	if x != nil {
		return x.TileBgImageBeforeClaim
	}
	return ""
}

func (x *RewardDisplay) GetTileBgImageAfterClaim() string {
	if x != nil {
		return x.TileBgImageAfterClaim
	}
	return ""
}

func (x *RewardDisplay) GetRewardTileThemeType() RewardTileThemeType {
	if x != nil {
		return x.RewardTileThemeType
	}
	return RewardTileThemeType_REWARD_TILE_THEME_UNSPECIFIED
}

func (x *RewardDisplay) GetIsAnimationUnskippable() bool {
	if x != nil {
		return x.IsAnimationUnskippable
	}
	return false
}

func (x *RewardDisplay) GetSkipAnimation() bool {
	if x != nil {
		return x.SkipAnimation
	}
	return false
}

func (x *RewardDisplay) GetAnimationSpeed() float32 {
	if x != nil {
		return x.AnimationSpeed
	}
	return 0
}

// for Cash reward
type Cash struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount *money.Money `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
	// remark to be used for displaying with cash txn.
	// if txn_remark is not specified a default remark is used.
	TxnRemark string `protobuf:"bytes,2,opt,name=txn_remark,json=txnRemark,proto3" json:"txn_remark,omitempty"`
}

func (x *Cash) Reset() {
	*x = Cash{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cash) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cash) ProtoMessage() {}

func (x *Cash) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cash.ProtoReflect.Descriptor instead.
func (*Cash) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{2}
}

func (x *Cash) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *Cash) GetTxnRemark() string {
	if x != nil {
		return x.TxnRemark
	}
	return ""
}

// for Fi Coins reward
type FiCoins struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Units     uint32                 `protobuf:"varint,1,opt,name=units,proto3" json:"units,omitempty"`
	ExpiresAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	// this flag will be true when the reward is given for reward type FI_COINS but given as FI_POINTS
	IsFiPoints bool `protobuf:"varint,3,opt,name=is_fi_points,json=isFiPoints,proto3" json:"is_fi_points,omitempty"`
}

func (x *FiCoins) Reset() {
	*x = FiCoins{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiCoins) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiCoins) ProtoMessage() {}

func (x *FiCoins) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiCoins.ProtoReflect.Descriptor instead.
func (*FiCoins) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{3}
}

func (x *FiCoins) GetUnits() uint32 {
	if x != nil {
		return x.Units
	}
	return 0
}

func (x *FiCoins) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *FiCoins) GetIsFiPoints() bool {
	if x != nil {
		return x.IsFiPoints
	}
	return false
}

// for Lucky Draw reward
type LuckyDraw struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LuckyDrawId string `protobuf:"bytes,1,opt,name=lucky_draw_id,json=luckyDrawId,proto3" json:"lucky_draw_id,omitempty"`
}

func (x *LuckyDraw) Reset() {
	*x = LuckyDraw{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LuckyDraw) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuckyDraw) ProtoMessage() {}

func (x *LuckyDraw) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuckyDraw.ProtoReflect.Descriptor instead.
func (*LuckyDraw) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{4}
}

func (x *LuckyDraw) GetLuckyDrawId() string {
	if x != nil {
		return x.LuckyDrawId
	}
	return ""
}

// for NO_Reward rewardType
type NoReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NoReward) Reset() {
	*x = NoReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoReward) ProtoMessage() {}

func (x *NoReward) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoReward.ProtoReflect.Descriptor instead.
func (*NoReward) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{5}
}

// for Metal credit card rewardType
type MetalCreditCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MetalCreditCard) Reset() {
	*x = MetalCreditCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetalCreditCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetalCreditCard) ProtoMessage() {}

func (x *MetalCreditCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetalCreditCard.ProtoReflect.Descriptor instead.
func (*MetalCreditCard) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{6}
}

// for EGV basket rewardType
type EgvBasket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ids of offers configured in offers service, which need to be given as reward
	EgvOfferIds []string `protobuf:"bytes,1,rep,name=egv_offer_ids,json=egvOfferIds,proto3" json:"egv_offer_ids,omitempty"`
}

func (x *EgvBasket) Reset() {
	*x = EgvBasket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EgvBasket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EgvBasket) ProtoMessage() {}

func (x *EgvBasket) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EgvBasket.ProtoReflect.Descriptor instead.
func (*EgvBasket) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{7}
}

func (x *EgvBasket) GetEgvOfferIds() []string {
	if x != nil {
		return x.EgvOfferIds
	}
	return nil
}

type ThriweBenefitsPackage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the id of offer configured in the offer service which would be used for fulfilling thriwe benefits package reward.
	OfferId string `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
}

func (x *ThriweBenefitsPackage) Reset() {
	*x = ThriweBenefitsPackage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThriweBenefitsPackage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThriweBenefitsPackage) ProtoMessage() {}

func (x *ThriweBenefitsPackage) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThriweBenefitsPackage.ProtoReflect.Descriptor instead.
func (*ThriweBenefitsPackage) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{8}
}

func (x *ThriweBenefitsPackage) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

// for CC bill eraser reward
type CreditCardBillEraser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// amount to be added to user's CC existing limit
	Amount *money.Money `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *CreditCardBillEraser) Reset() {
	*x = CreditCardBillEraser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditCardBillEraser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditCardBillEraser) ProtoMessage() {}

func (x *CreditCardBillEraser) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditCardBillEraser.ProtoReflect.Descriptor instead.
func (*CreditCardBillEraser) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{9}
}

func (x *CreditCardBillEraser) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

// for SmartDeposit Reward
type SmartDeposit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// amount to be deposited in SD
	Amount *money.Money `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
	// SD maturity date.
	// maturity date will be used only for new reward SD.
	MaturityDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=maturity_date,json=maturityDate,proto3" json:"maturity_date,omitempty"`
	// used to calculate maturity date, if
	// maturity date is already not calculated
	MaturityDateConfig *RewardTimeConfig `protobuf:"bytes,4,opt,name=maturity_date_config,json=maturityDateConfig,proto3" json:"maturity_date_config,omitempty"`
	// nominee details are required to create a new SD
	NomineeInfoList []*DepositNomineeInfo `protobuf:"bytes,5,rep,name=nominee_info_list,json=nomineeInfoList,proto3" json:"nominee_info_list,omitempty"`
	// deposit name
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SmartDeposit) Reset() {
	*x = SmartDeposit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmartDeposit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmartDeposit) ProtoMessage() {}

func (x *SmartDeposit) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmartDeposit.ProtoReflect.Descriptor instead.
func (*SmartDeposit) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{10}
}

func (x *SmartDeposit) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *SmartDeposit) GetMaturityDate() *timestamppb.Timestamp {
	if x != nil {
		return x.MaturityDate
	}
	return nil
}

func (x *SmartDeposit) GetMaturityDateConfig() *RewardTimeConfig {
	if x != nil {
		return x.MaturityDateConfig
	}
	return nil
}

func (x *SmartDeposit) GetNomineeInfoList() []*DepositNomineeInfo {
	if x != nil {
		return x.NomineeInfoList
	}
	return nil
}

func (x *SmartDeposit) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// nominee info for creating a new SD as part of reward
type DepositNomineeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NomineeId       string `protobuf:"bytes,1,opt,name=nominee_id,json=nomineeId,proto3" json:"nominee_id,omitempty"`
	PercentageShare string `protobuf:"bytes,2,opt,name=percentage_share,json=percentageShare,proto3" json:"percentage_share,omitempty"`
}

func (x *DepositNomineeInfo) Reset() {
	*x = DepositNomineeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositNomineeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositNomineeInfo) ProtoMessage() {}

func (x *DepositNomineeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositNomineeInfo.ProtoReflect.Descriptor instead.
func (*DepositNomineeInfo) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{11}
}

func (x *DepositNomineeInfo) GetNomineeId() string {
	if x != nil {
		return x.NomineeId
	}
	return ""
}

func (x *DepositNomineeInfo) GetPercentageShare() string {
	if x != nil {
		return x.PercentageShare
	}
	return ""
}

// for GiftHamper reward
type GiftHamper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of vendor who will provide the gift hamper
	VendorName string `protobuf:"bytes,1,opt,name=vendor_name,json=vendorName,proto3" json:"vendor_name,omitempty"`
	// vendor specific product identifier
	VendorProductId string `protobuf:"bytes,2,opt,name=vendor_product_id,json=vendorProductId,proto3" json:"vendor_product_id,omitempty"`
	// address where hamper is to be shipped
	ShippingAddress *postaladdress.PostalAddress `protobuf:"bytes,3,opt,name=shipping_address,json=shippingAddress,proto3" json:"shipping_address,omitempty"`
	// name of product like "Monsoon Harvest Hamper"
	ProductName string `protobuf:"bytes,4,opt,name=product_name,json=productName,proto3" json:"product_name,omitempty"`
}

func (x *GiftHamper) Reset() {
	*x = GiftHamper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftHamper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftHamper) ProtoMessage() {}

func (x *GiftHamper) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftHamper.ProtoReflect.Descriptor instead.
func (*GiftHamper) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{12}
}

func (x *GiftHamper) GetVendorName() string {
	if x != nil {
		return x.VendorName
	}
	return ""
}

func (x *GiftHamper) GetVendorProductId() string {
	if x != nil {
		return x.VendorProductId
	}
	return ""
}

func (x *GiftHamper) GetShippingAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.ShippingAddress
	}
	return nil
}

func (x *GiftHamper) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

type RewardEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	Reward        *Reward                      `protobuf:"bytes,2,opt,name=reward,proto3" json:"reward,omitempty"`
	// if true, suppresses all notifications related to reward processing
	ShouldSuppressNotifications bool `protobuf:"varint,3,opt,name=should_suppress_notifications,json=shouldSuppressNotifications,proto3" json:"should_suppress_notifications,omitempty"`
}

func (x *RewardEvent) Reset() {
	*x = RewardEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardEvent) ProtoMessage() {}

func (x *RewardEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardEvent.ProtoReflect.Descriptor instead.
func (*RewardEvent) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{13}
}

func (x *RewardEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *RewardEvent) GetReward() *Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

func (x *RewardEvent) GetShouldSuppressNotifications() bool {
	if x != nil {
		return x.ShouldSuppressNotifications
	}
	return false
}

type RewardTimeConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Config:
	//
	//	*RewardTimeConfig_AbsoluteTime
	//	*RewardTimeConfig_RelativeTimeInMinutes
	Config isRewardTimeConfig_Config `protobuf_oneof:"config"`
	// intentional gap in field numbers to accommodate new fields in one-of config
	// if this flag is set to true then the computed time using the above one-of config should be rounded up to the start of the next day in IST timezone.
	RoundUpToStartOfNextDay bool `protobuf:"varint,5,opt,name=round_up_to_start_of_next_day,json=roundUpToStartOfNextDay,proto3" json:"round_up_to_start_of_next_day,omitempty"`
}

func (x *RewardTimeConfig) Reset() {
	*x = RewardTimeConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardTimeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardTimeConfig) ProtoMessage() {}

func (x *RewardTimeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardTimeConfig.ProtoReflect.Descriptor instead.
func (*RewardTimeConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{14}
}

func (m *RewardTimeConfig) GetConfig() isRewardTimeConfig_Config {
	if m != nil {
		return m.Config
	}
	return nil
}

func (x *RewardTimeConfig) GetAbsoluteTime() *timestamppb.Timestamp {
	if x, ok := x.GetConfig().(*RewardTimeConfig_AbsoluteTime); ok {
		return x.AbsoluteTime
	}
	return nil
}

func (x *RewardTimeConfig) GetRelativeTimeInMinutes() uint32 {
	if x, ok := x.GetConfig().(*RewardTimeConfig_RelativeTimeInMinutes); ok {
		return x.RelativeTimeInMinutes
	}
	return 0
}

func (x *RewardTimeConfig) GetRoundUpToStartOfNextDay() bool {
	if x != nil {
		return x.RoundUpToStartOfNextDay
	}
	return false
}

type isRewardTimeConfig_Config interface {
	isRewardTimeConfig_Config()
}

type RewardTimeConfig_AbsoluteTime struct {
	AbsoluteTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=absolute_time,json=absoluteTime,proto3,oneof"`
}

type RewardTimeConfig_RelativeTimeInMinutes struct {
	RelativeTimeInMinutes uint32 `protobuf:"varint,2,opt,name=relative_time_in_minutes,json=relativeTimeInMinutes,proto3,oneof"`
}

func (*RewardTimeConfig_AbsoluteTime) isRewardTimeConfig_Config() {}

func (*RewardTimeConfig_RelativeTimeInMinutes) isRewardTimeConfig_Config() {}

type RewardOptionDisplay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated : use before_claim_title and after_claim_title
	Title                string                                     `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Icon                 string                                     `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	BgColor              string                                     `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	BeforeClaimTitle     string                                     `protobuf:"bytes,4,opt,name=before_claim_title,json=beforeClaimTitle,proto3" json:"before_claim_title,omitempty"`
	AfterClaimTitle      string                                     `protobuf:"bytes,5,opt,name=after_claim_title,json=afterClaimTitle,proto3" json:"after_claim_title,omitempty"`
	HtmlFormattedDetails []*RewardOptionDisplay_HtmlFormattedDetail `protobuf:"bytes,6,rep,name=html_formatted_details,json=htmlFormattedDetails,proto3" json:"html_formatted_details,omitempty"`
	// banner text displayed on top of the reward option card at the time of choosing the option e.g. "10% EXTRA REWARD"
	// Design : https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=5953%3A65408
	// can be empty if banner text does not needs to be displayed.
	BeforeClaimBannerText string `protobuf:"bytes,7,opt,name=before_claim_banner_text,json=beforeClaimBannerText,proto3" json:"before_claim_banner_text,omitempty"`
	// tags for reward to be propagated eventually.
	// current use case: for display purpose once the option is chosen.
	Tags []RewardTag `protobuf:"varint,8,rep,packed,name=tags,proto3,enum=rewards.RewardTag" json:"tags,omitempty"`
}

func (x *RewardOptionDisplay) Reset() {
	*x = RewardOptionDisplay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOptionDisplay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOptionDisplay) ProtoMessage() {}

func (x *RewardOptionDisplay) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOptionDisplay.ProtoReflect.Descriptor instead.
func (*RewardOptionDisplay) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{15}
}

func (x *RewardOptionDisplay) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *RewardOptionDisplay) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *RewardOptionDisplay) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *RewardOptionDisplay) GetBeforeClaimTitle() string {
	if x != nil {
		return x.BeforeClaimTitle
	}
	return ""
}

func (x *RewardOptionDisplay) GetAfterClaimTitle() string {
	if x != nil {
		return x.AfterClaimTitle
	}
	return ""
}

func (x *RewardOptionDisplay) GetHtmlFormattedDetails() []*RewardOptionDisplay_HtmlFormattedDetail {
	if x != nil {
		return x.HtmlFormattedDetails
	}
	return nil
}

func (x *RewardOptionDisplay) GetBeforeClaimBannerText() string {
	if x != nil {
		return x.BeforeClaimBannerText
	}
	return ""
}

func (x *RewardOptionDisplay) GetTags() []RewardTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

type RewardOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                         string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Display                    *RewardOptionDisplay   `protobuf:"bytes,2,opt,name=display,proto3" json:"display,omitempty"`
	ProcessingDate             *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=processing_date,json=processingDate,proto3" json:"processing_date,omitempty"`
	RewardProcessingTimeConfig *RewardTimeConfig      `protobuf:"bytes,4,opt,name=reward_processing_time_config,json=rewardProcessingTimeConfig,proto3" json:"reward_processing_time_config,omitempty"`
	// type of reward namely Cash, FiCoins etc.
	RewardType RewardType `protobuf:"varint,5,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	// to store the individual (ranging from base, intermediate, final) reward units calculation entries.
	// source of calculations currently include:
	// 1. default config, i.e. base config
	// 2. booster configs
	RewardUnitsCalculationInfo *RewardUnitsCalculationInfo `protobuf:"bytes,6,opt,name=reward_units_calculation_info,json=rewardUnitsCalculationInfo,proto3" json:"reward_units_calculation_info,omitempty"`
	// current usage includes specific handling of display details of reward option. it's an optional field and can be used for the following reward types if we want to distinguish between options:
	// 1. gift_hamper
	// 2. egv_basket
	ProductSku string `protobuf:"bytes,7,opt,name=product_sku,json=productSku,proto3" json:"product_sku,omitempty"`
	// intentional gap to accommodate new fields in future
	// TODO : Update UnitRewardTypes or RewardOptionSchema in data-platform whenever option is updated : src/main/scala/com/epifi/dataplatform/epiview/commons/rewards/RewardsConstants.scala
	//
	// Types that are assignable to Option:
	//
	//	*RewardOption_Cash
	//	*RewardOption_FiCoins
	//	*RewardOption_LuckyDraw
	//	*RewardOption_SmartDeposit
	//	*RewardOption_GiftHamper
	//	*RewardOption_MetalCreditCard
	//	*RewardOption_EgvBasket
	//	*RewardOption_NoReward
	//	*RewardOption_ThriweBenefitsPackage
	//	*RewardOption_UsstockReward
	//	*RewardOption_CreditCardBillEraser
	Option isRewardOption_Option `protobuf_oneof:"option"`
}

func (x *RewardOption) Reset() {
	*x = RewardOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOption) ProtoMessage() {}

func (x *RewardOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOption.ProtoReflect.Descriptor instead.
func (*RewardOption) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{16}
}

func (x *RewardOption) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RewardOption) GetDisplay() *RewardOptionDisplay {
	if x != nil {
		return x.Display
	}
	return nil
}

func (x *RewardOption) GetProcessingDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ProcessingDate
	}
	return nil
}

func (x *RewardOption) GetRewardProcessingTimeConfig() *RewardTimeConfig {
	if x != nil {
		return x.RewardProcessingTimeConfig
	}
	return nil
}

func (x *RewardOption) GetRewardType() RewardType {
	if x != nil {
		return x.RewardType
	}
	return RewardType_REWARD_TYPE_UNSPECIFIED
}

func (x *RewardOption) GetRewardUnitsCalculationInfo() *RewardUnitsCalculationInfo {
	if x != nil {
		return x.RewardUnitsCalculationInfo
	}
	return nil
}

func (x *RewardOption) GetProductSku() string {
	if x != nil {
		return x.ProductSku
	}
	return ""
}

func (m *RewardOption) GetOption() isRewardOption_Option {
	if m != nil {
		return m.Option
	}
	return nil
}

func (x *RewardOption) GetCash() *Cash {
	if x, ok := x.GetOption().(*RewardOption_Cash); ok {
		return x.Cash
	}
	return nil
}

func (x *RewardOption) GetFiCoins() *FiCoins {
	if x, ok := x.GetOption().(*RewardOption_FiCoins); ok {
		return x.FiCoins
	}
	return nil
}

func (x *RewardOption) GetLuckyDraw() *LuckyDraw {
	if x, ok := x.GetOption().(*RewardOption_LuckyDraw); ok {
		return x.LuckyDraw
	}
	return nil
}

func (x *RewardOption) GetSmartDeposit() *SmartDeposit {
	if x, ok := x.GetOption().(*RewardOption_SmartDeposit); ok {
		return x.SmartDeposit
	}
	return nil
}

func (x *RewardOption) GetGiftHamper() *GiftHamper {
	if x, ok := x.GetOption().(*RewardOption_GiftHamper); ok {
		return x.GiftHamper
	}
	return nil
}

func (x *RewardOption) GetMetalCreditCard() *MetalCreditCard {
	if x, ok := x.GetOption().(*RewardOption_MetalCreditCard); ok {
		return x.MetalCreditCard
	}
	return nil
}

func (x *RewardOption) GetEgvBasket() *EgvBasket {
	if x, ok := x.GetOption().(*RewardOption_EgvBasket); ok {
		return x.EgvBasket
	}
	return nil
}

func (x *RewardOption) GetNoReward() *NoReward {
	if x, ok := x.GetOption().(*RewardOption_NoReward); ok {
		return x.NoReward
	}
	return nil
}

func (x *RewardOption) GetThriweBenefitsPackage() *ThriweBenefitsPackage {
	if x, ok := x.GetOption().(*RewardOption_ThriweBenefitsPackage); ok {
		return x.ThriweBenefitsPackage
	}
	return nil
}

func (x *RewardOption) GetUsstockReward() *USStockReward {
	if x, ok := x.GetOption().(*RewardOption_UsstockReward); ok {
		return x.UsstockReward
	}
	return nil
}

func (x *RewardOption) GetCreditCardBillEraser() *CreditCardBillEraser {
	if x, ok := x.GetOption().(*RewardOption_CreditCardBillEraser); ok {
		return x.CreditCardBillEraser
	}
	return nil
}

type isRewardOption_Option interface {
	isRewardOption_Option()
}

type RewardOption_Cash struct {
	Cash *Cash `protobuf:"bytes,11,opt,name=cash,proto3,oneof"`
}

type RewardOption_FiCoins struct {
	FiCoins *FiCoins `protobuf:"bytes,12,opt,name=fi_coins,json=fiCoins,proto3,oneof"`
}

type RewardOption_LuckyDraw struct {
	LuckyDraw *LuckyDraw `protobuf:"bytes,13,opt,name=lucky_draw,json=luckyDraw,proto3,oneof"`
}

type RewardOption_SmartDeposit struct {
	SmartDeposit *SmartDeposit `protobuf:"bytes,14,opt,name=smart_deposit,json=smartDeposit,proto3,oneof"`
}

type RewardOption_GiftHamper struct {
	GiftHamper *GiftHamper `protobuf:"bytes,15,opt,name=gift_hamper,json=giftHamper,proto3,oneof"`
}

type RewardOption_MetalCreditCard struct {
	MetalCreditCard *MetalCreditCard `protobuf:"bytes,16,opt,name=metal_credit_card,json=metalCreditCard,proto3,oneof"`
}

type RewardOption_EgvBasket struct {
	EgvBasket *EgvBasket `protobuf:"bytes,17,opt,name=egv_basket,json=egvBasket,proto3,oneof"`
}

type RewardOption_NoReward struct {
	// intentional gap in field number to accomodate new reward types
	NoReward *NoReward `protobuf:"bytes,20,opt,name=no_reward,json=noReward,proto3,oneof"`
}

type RewardOption_ThriweBenefitsPackage struct {
	ThriweBenefitsPackage *ThriweBenefitsPackage `protobuf:"bytes,21,opt,name=thriwe_benefits_package,json=thriweBenefitsPackage,proto3,oneof"`
}

type RewardOption_UsstockReward struct {
	UsstockReward *USStockReward `protobuf:"bytes,22,opt,name=usstock_reward,json=usstockReward,proto3,oneof"`
}

type RewardOption_CreditCardBillEraser struct {
	CreditCardBillEraser *CreditCardBillEraser `protobuf:"bytes,23,opt,name=credit_card_bill_eraser,json=creditCardBillEraser,proto3,oneof"`
}

func (*RewardOption_Cash) isRewardOption_Option() {}

func (*RewardOption_FiCoins) isRewardOption_Option() {}

func (*RewardOption_LuckyDraw) isRewardOption_Option() {}

func (*RewardOption_SmartDeposit) isRewardOption_Option() {}

func (*RewardOption_GiftHamper) isRewardOption_Option() {}

func (*RewardOption_MetalCreditCard) isRewardOption_Option() {}

func (*RewardOption_EgvBasket) isRewardOption_Option() {}

func (*RewardOption_NoReward) isRewardOption_Option() {}

func (*RewardOption_ThriweBenefitsPackage) isRewardOption_Option() {}

func (*RewardOption_UsstockReward) isRewardOption_Option() {}

func (*RewardOption_CreditCardBillEraser) isRewardOption_Option() {}

type RewardUnitsCalculationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// storing the results of reward calculations
	// each entry stores the intermediate reward value (and not the delta)
	RewardUnitsCalculationEntries []*RewardUnitsCalculationInfo_RewardUnitsCalculationEntry `protobuf:"bytes,1,rep,name=reward_units_calculation_entries,json=rewardUnitsCalculationEntries,proto3" json:"reward_units_calculation_entries,omitempty"`
	// to decide how the claim flow would reveal the reward values for each calculation
	ClaimFlowRewardValueRevealType ClaimFlowRewardValueRevealType `protobuf:"varint,2,opt,name=claim_flow_reward_value_reveal_type,json=claimFlowRewardValueRevealType,proto3,enum=rewards.ClaimFlowRewardValueRevealType" json:"claim_flow_reward_value_reveal_type,omitempty"`
}

func (x *RewardUnitsCalculationInfo) Reset() {
	*x = RewardUnitsCalculationInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardUnitsCalculationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardUnitsCalculationInfo) ProtoMessage() {}

func (x *RewardUnitsCalculationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardUnitsCalculationInfo.ProtoReflect.Descriptor instead.
func (*RewardUnitsCalculationInfo) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{17}
}

func (x *RewardUnitsCalculationInfo) GetRewardUnitsCalculationEntries() []*RewardUnitsCalculationInfo_RewardUnitsCalculationEntry {
	if x != nil {
		return x.RewardUnitsCalculationEntries
	}
	return nil
}

func (x *RewardUnitsCalculationInfo) GetClaimFlowRewardValueRevealType() ClaimFlowRewardValueRevealType {
	if x != nil {
		return x.ClaimFlowRewardValueRevealType
	}
	return ClaimFlowRewardValueRevealType_CLAIM_FLOW_REVEAL_TYPE_UNSPECIFIED
}

type RewardOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DefaultDecideTimeInSecs uint32                 `protobuf:"varint,1,opt,name=default_decide_time_in_secs,json=defaultDecideTimeInSecs,proto3" json:"default_decide_time_in_secs,omitempty"`
	UnlockDate              *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=unlock_date,json=unlockDate,proto3" json:"unlock_date,omitempty"`
	Options                 []*RewardOption        `protobuf:"bytes,3,rep,name=options,proto3" json:"options,omitempty"`
	ActionDetails           string                 `protobuf:"bytes,4,opt,name=action_details,json=actionDetails,proto3" json:"action_details,omitempty"`
	// bool to decide whether reward should be unlocked for MinKYC users or not
	//
	// Deprecated: Marked as deprecated in api/rewards/reward.proto.
	IsUnlockedForMinKyc bool `protobuf:"varint,5,opt,name=is_unlocked_for_min_kyc,json=isUnlockedForMinKyc,proto3" json:"is_unlocked_for_min_kyc,omitempty"`
	// time after which the reward's default option should be auto-claimed,
	// will be null if reward shouldn't be auto-claimed.
	AutoClaimTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=auto_claim_time,json=autoClaimTime,proto3" json:"auto_claim_time,omitempty"`
	// bool to decide whether implicit locking is enabled for this reward or not
	IsImplicitLockingDisabled bool `protobuf:"varint,7,opt,name=is_implicit_locking_disabled,json=isImplicitLockingDisabled,proto3" json:"is_implicit_locking_disabled,omitempty"`
}

func (x *RewardOptions) Reset() {
	*x = RewardOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOptions) ProtoMessage() {}

func (x *RewardOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOptions.ProtoReflect.Descriptor instead.
func (*RewardOptions) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{18}
}

func (x *RewardOptions) GetDefaultDecideTimeInSecs() uint32 {
	if x != nil {
		return x.DefaultDecideTimeInSecs
	}
	return 0
}

func (x *RewardOptions) GetUnlockDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UnlockDate
	}
	return nil
}

func (x *RewardOptions) GetOptions() []*RewardOption {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *RewardOptions) GetActionDetails() string {
	if x != nil {
		return x.ActionDetails
	}
	return ""
}

// Deprecated: Marked as deprecated in api/rewards/reward.proto.
func (x *RewardOptions) GetIsUnlockedForMinKyc() bool {
	if x != nil {
		return x.IsUnlockedForMinKyc
	}
	return false
}

func (x *RewardOptions) GetAutoClaimTime() *timestamppb.Timestamp {
	if x != nil {
		return x.AutoClaimTime
	}
	return nil
}

func (x *RewardOptions) GetIsImplicitLockingDisabled() bool {
	if x != nil {
		return x.IsImplicitLockingDisabled
	}
	return false
}

// metadata required to claim reward eg : nominee details for SD.
type RewardClaimMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// useful for physical rewards like gift hampers.
	// not added to reward_type_specific_data as this field can be used for multiple reward api.typesv2.
	ShippingAddress *postaladdress.PostalAddress `protobuf:"bytes,1,opt,name=shipping_address,json=shippingAddress,proto3" json:"shipping_address,omitempty"`
	// Types that are assignable to RewardTypeSpecificData:
	//
	//	*RewardClaimMetadata_SdMetadata
	RewardTypeSpecificData isRewardClaimMetadata_RewardTypeSpecificData `protobuf_oneof:"reward_type_specific_data"`
}

func (x *RewardClaimMetadata) Reset() {
	*x = RewardClaimMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardClaimMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardClaimMetadata) ProtoMessage() {}

func (x *RewardClaimMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardClaimMetadata.ProtoReflect.Descriptor instead.
func (*RewardClaimMetadata) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{19}
}

func (x *RewardClaimMetadata) GetShippingAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.ShippingAddress
	}
	return nil
}

func (m *RewardClaimMetadata) GetRewardTypeSpecificData() isRewardClaimMetadata_RewardTypeSpecificData {
	if m != nil {
		return m.RewardTypeSpecificData
	}
	return nil
}

func (x *RewardClaimMetadata) GetSdMetadata() *RewardClaimMetadata_SDMetadata {
	if x, ok := x.GetRewardTypeSpecificData().(*RewardClaimMetadata_SdMetadata); ok {
		return x.SdMetadata
	}
	return nil
}

type isRewardClaimMetadata_RewardTypeSpecificData interface {
	isRewardClaimMetadata_RewardTypeSpecificData()
}

type RewardClaimMetadata_SdMetadata struct {
	SdMetadata *RewardClaimMetadata_SDMetadata `protobuf:"bytes,6,opt,name=sd_metadata,json=sdMetadata,proto3,oneof"`
}

func (*RewardClaimMetadata_SdMetadata) isRewardClaimMetadata_RewardTypeSpecificData() {}

// any metadata related to the generated reward
type RewardMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OfferTypeSpecificMetadata *RewardMetadata_RewardOfferTypeSpecificMetadata `protobuf:"bytes,1,opt,name=offer_type_specific_metadata,json=offerTypeSpecificMetadata,proto3" json:"offer_type_specific_metadata,omitempty"`
	ProjectionMetadata        *RewardMetadata_ProjectionMetadata              `protobuf:"bytes,2,opt,name=projection_metadata,json=projectionMetadata,proto3" json:"projection_metadata,omitempty"`
}

func (x *RewardMetadata) Reset() {
	*x = RewardMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardMetadata) ProtoMessage() {}

func (x *RewardMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardMetadata.ProtoReflect.Descriptor instead.
func (*RewardMetadata) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{20}
}

func (x *RewardMetadata) GetOfferTypeSpecificMetadata() *RewardMetadata_RewardOfferTypeSpecificMetadata {
	if x != nil {
		return x.OfferTypeSpecificMetadata
	}
	return nil
}

func (x *RewardMetadata) GetProjectionMetadata() *RewardMetadata_ProjectionMetadata {
	if x != nil {
		return x.ProjectionMetadata
	}
	return nil
}

// RewardOptionMinimal contains just the reward type and reward units associated with a reward option.
// it can be used in situations where we don't need all the details related to a reward option (like display details)
// and just the reward type and units matter.
type RewardOptionMinimal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type of reward for this option
	RewardType RewardType `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	// number of units of given type
	Units float32 `protobuf:"fixed32,2,opt,name=units,proto3" json:"units,omitempty"`
}

func (x *RewardOptionMinimal) Reset() {
	*x = RewardOptionMinimal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOptionMinimal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOptionMinimal) ProtoMessage() {}

func (x *RewardOptionMinimal) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOptionMinimal.ProtoReflect.Descriptor instead.
func (*RewardOptionMinimal) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{21}
}

func (x *RewardOptionMinimal) GetRewardType() RewardType {
	if x != nil {
		return x.RewardType
	}
	return RewardType_REWARD_TYPE_UNSPECIFIED
}

func (x *RewardOptionMinimal) GetUnits() float32 {
	if x != nil {
		return x.Units
	}
	return 0
}

type USStockReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// represent the stock id which is being given as reward
	StockId string `protobuf:"bytes,1,opt,name=stock_id,json=stockId,proto3" json:"stock_id,omitempty"`
	// represent the amount worth of stock given to user
	Amount *money.Money `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *USStockReward) Reset() {
	*x = USStockReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *USStockReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*USStockReward) ProtoMessage() {}

func (x *USStockReward) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use USStockReward.ProtoReflect.Descriptor instead.
func (*USStockReward) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{22}
}

func (x *USStockReward) GetStockId() string {
	if x != nil {
		return x.StockId
	}
	return ""
}

func (x *USStockReward) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

// html formatted details
// e.g for a Gift Hamper Reward, a detail entry can look like
//
//	{
//	 header : "Description"
//	 body :"<b>Gift Box Contains :</b> <br>
//	 1 Toasted Millet Muesli <br>
//	 1 Choco chip oat clusters and Ragi flakes with banana <br>
//	 1 Box of 12 Crunchy granola bars - dark chocolate & espresso."
//	}
type RewardOptionDisplay_HtmlFormattedDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header string `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Body   string `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *RewardOptionDisplay_HtmlFormattedDetail) Reset() {
	*x = RewardOptionDisplay_HtmlFormattedDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOptionDisplay_HtmlFormattedDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOptionDisplay_HtmlFormattedDetail) ProtoMessage() {}

func (x *RewardOptionDisplay_HtmlFormattedDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOptionDisplay_HtmlFormattedDetail.ProtoReflect.Descriptor instead.
func (*RewardOptionDisplay_HtmlFormattedDetail) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{15, 0}
}

func (x *RewardOptionDisplay_HtmlFormattedDetail) GetHeader() string {
	if x != nil {
		return x.Header
	}
	return ""
}

func (x *RewardOptionDisplay_HtmlFormattedDetail) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

type RewardUnitsCalculationInfo_RewardUnitsCalculationEntry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardValue    float32                                                                `protobuf:"fixed32,1,opt,name=reward_value,json=rewardValue,proto3" json:"reward_value,omitempty"`
	DisplayDetails *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails `protobuf:"bytes,2,opt,name=display_details,json=displayDetails,proto3" json:"display_details,omitempty"`
}

func (x *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry) Reset() {
	*x = RewardUnitsCalculationInfo_RewardUnitsCalculationEntry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardUnitsCalculationInfo_RewardUnitsCalculationEntry) ProtoMessage() {}

func (x *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardUnitsCalculationInfo_RewardUnitsCalculationEntry.ProtoReflect.Descriptor instead.
func (*RewardUnitsCalculationInfo_RewardUnitsCalculationEntry) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{17, 0}
}

func (x *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry) GetRewardValue() float32 {
	if x != nil {
		return x.RewardValue
	}
	return 0
}

func (x *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry) GetDisplayDetails() *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails {
	if x != nil {
		return x.DisplayDetails
	}
	return nil
}

type RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tags associated with the calculation entry
	Tags []RewardTag `protobuf:"varint,1,rep,packed,name=tags,proto3,enum=rewards.RewardTag" json:"tags,omitempty"`
	// title for the calculation entry, for e.g. "2x boost applied for Fi Plus accounts"
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// title color
	TitleColor string `protobuf:"bytes,3,opt,name=title_color,json=titleColor,proto3" json:"title_color,omitempty"`
	// bg color for displaying booster details
	BgColor string `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails) Reset() {
	*x = RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails) ProtoMessage() {}

func (x *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails.ProtoReflect.Descriptor instead.
func (*RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{17, 0, 0}
}

func (x *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails) GetTags() []RewardTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails) GetTitleColor() string {
	if x != nil {
		return x.TitleColor
	}
	return ""
}

func (x *RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

// SDMetadata contains metadata specific to SD Reward.
type RewardClaimMetadata_SDMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// todo (utkarsh) : remove it
	NomineeDetails string `protobuf:"bytes,1,opt,name=nominee_details,json=nomineeDetails,proto3" json:"nominee_details,omitempty"`
	// nominee info for creating a new SD as part of reward
	NomineeInfoList []*DepositNomineeInfo `protobuf:"bytes,2,rep,name=nominee_info_list,json=nomineeInfoList,proto3" json:"nominee_info_list,omitempty"`
	SdName          string                `protobuf:"bytes,3,opt,name=sd_name,json=sdName,proto3" json:"sd_name,omitempty"`
}

func (x *RewardClaimMetadata_SDMetadata) Reset() {
	*x = RewardClaimMetadata_SDMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardClaimMetadata_SDMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardClaimMetadata_SDMetadata) ProtoMessage() {}

func (x *RewardClaimMetadata_SDMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardClaimMetadata_SDMetadata.ProtoReflect.Descriptor instead.
func (*RewardClaimMetadata_SDMetadata) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{19, 0}
}

func (x *RewardClaimMetadata_SDMetadata) GetNomineeDetails() string {
	if x != nil {
		return x.NomineeDetails
	}
	return ""
}

func (x *RewardClaimMetadata_SDMetadata) GetNomineeInfoList() []*DepositNomineeInfo {
	if x != nil {
		return x.NomineeInfoList
	}
	return nil
}

func (x *RewardClaimMetadata_SDMetadata) GetSdName() string {
	if x != nil {
		return x.SdName
	}
	return ""
}

// metadata related to the reward offer. for ex. name of top merchants for which the 5x reward was generated
type RewardMetadata_RewardOfferTypeSpecificMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Metadata:
	//
	//	*RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata_
	//	*RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata_
	Metadata isRewardMetadata_RewardOfferTypeSpecificMetadata_Metadata `protobuf_oneof:"metadata"`
}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata) Reset() {
	*x = RewardMetadata_RewardOfferTypeSpecificMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardMetadata_RewardOfferTypeSpecificMetadata) ProtoMessage() {}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardMetadata_RewardOfferTypeSpecificMetadata.ProtoReflect.Descriptor instead.
func (*RewardMetadata_RewardOfferTypeSpecificMetadata) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{20, 0}
}

func (m *RewardMetadata_RewardOfferTypeSpecificMetadata) GetMetadata() isRewardMetadata_RewardOfferTypeSpecificMetadata_Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata) GetCcTopMerchantsSpendsRewardMetadata() *RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata {
	if x, ok := x.GetMetadata().(*RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata_); ok {
		return x.CcTopMerchantsSpendsRewardMetadata
	}
	return nil
}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata) GetCcCuratedMerchantsSpendsRewardMetadata() *RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata {
	if x, ok := x.GetMetadata().(*RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata_); ok {
		return x.CcCuratedMerchantsSpendsRewardMetadata
	}
	return nil
}

type isRewardMetadata_RewardOfferTypeSpecificMetadata_Metadata interface {
	isRewardMetadata_RewardOfferTypeSpecificMetadata_Metadata()
}

type RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata_ struct {
	CcTopMerchantsSpendsRewardMetadata *RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata `protobuf:"bytes,1,opt,name=cc_top_merchants_spends_reward_metadata,json=ccTopMerchantsSpendsRewardMetadata,proto3,oneof"`
}

type RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata_ struct {
	CcCuratedMerchantsSpendsRewardMetadata *RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata `protobuf:"bytes,2,opt,name=cc_curated_merchants_spends_reward_metadata,json=ccCuratedMerchantsSpendsRewardMetadata,proto3,oneof"`
}

func (*RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata_) isRewardMetadata_RewardOfferTypeSpecificMetadata_Metadata() {
}

func (*RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata_) isRewardMetadata_RewardOfferTypeSpecificMetadata_Metadata() {
}

// ProjectionMetadata contains metadata related to the reward projections if the reward was generated against projections
type RewardMetadata_ProjectionMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// flag to denote if all projections against which this reward was generated are updated with the reward specific details or not
	// this is updated for all the rewards which are generated against projections
	AreAllProjectionsUpdated bool `protobuf:"varint,1,opt,name=are_all_projections_updated,json=areAllProjectionsUpdated,proto3" json:"are_all_projections_updated,omitempty"`
}

func (x *RewardMetadata_ProjectionMetadata) Reset() {
	*x = RewardMetadata_ProjectionMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardMetadata_ProjectionMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardMetadata_ProjectionMetadata) ProtoMessage() {}

func (x *RewardMetadata_ProjectionMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardMetadata_ProjectionMetadata.ProtoReflect.Descriptor instead.
func (*RewardMetadata_ProjectionMetadata) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{20, 1}
}

func (x *RewardMetadata_ProjectionMetadata) GetAreAllProjectionsUpdated() bool {
	if x != nil {
		return x.AreAllProjectionsUpdated
	}
	return false
}

type RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// value of reward multiplier that was used to calculate top merchant spends reward from 1x reward
	AppliedRewardMultiplier float32 `protobuf:"fixed32,1,opt,name=applied_reward_multiplier,json=appliedRewardMultiplier,proto3" json:"applied_reward_multiplier,omitempty"`
	// map of top merchant name to aggregated (in a specified duration based on reward construct) 1x reward amount that was given on txns to that merchant.
	// This aggregated 1x reward amount on top merchants was used to calculate the reward amount for cc top merchant spends reward.
	TopMerchantNameTo_1XRewardAmount map[string]float32 `protobuf:"bytes,2,rep,name=top_merchant_name_to_1x_reward_amount,json=topMerchantNameTo1xRewardAmount,proto3" json:"top_merchant_name_to_1x_reward_amount,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata) Reset() {
	*x = RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata) ProtoMessage() {
}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata.ProtoReflect.Descriptor instead.
func (*RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{20, 0, 0}
}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata) GetAppliedRewardMultiplier() float32 {
	if x != nil {
		return x.AppliedRewardMultiplier
	}
	return 0
}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata) GetTopMerchantNameTo_1XRewardAmount() map[string]float32 {
	if x != nil {
		return x.TopMerchantNameTo_1XRewardAmount
	}
	return nil
}

type RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// map of curated merchant name to aggregated (in a specified duration based on reward construct) 1x reward amount that was given on txns to that merchant.
	// This aggregated 1x reward amount on merchants was used to calculate the reward amount for cc curated merchants spends reward.
	CuratedMerchantNameTo_1XRewardAmount map[string]float32 `protobuf:"bytes,2,rep,name=curated_merchant_name_to_1x_reward_amount,json=curatedMerchantNameTo1xRewardAmount,proto3" json:"curated_merchant_name_to_1x_reward_amount,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata) Reset() {
	*x = RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata) ProtoMessage() {
}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata.ProtoReflect.Descriptor instead.
func (*RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_proto_rawDescGZIP(), []int{20, 0, 1}
}

func (x *RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata) GetCuratedMerchantNameTo_1XRewardAmount() map[string]float32 {
	if x != nil {
		return x.CuratedMerchantNameTo_1XRewardAmount
	}
	return nil
}

var File_api_rewards_reward_proto protoreflect.FileDescriptor

var file_api_rewards_reward_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x83, 0x0a,
	0x0a, 0x06, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x10, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x61, 0x72, 0x79, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x73, 0x75, 0x62,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x3d, 0x0a, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x0d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x3a, 0x0a, 0x0d, 0x63, 0x68, 0x6f, 0x73, 0x65, 0x6e, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c,
	0x63, 0x68, 0x6f, 0x73, 0x65, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x69, 0x73, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x69, 0x73, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x66, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x52, 0x0d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a,
	0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x12, 0x40, 0x0a, 0x0f, 0x63, 0x6c, 0x61,
	0x77, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x17, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6c, 0x61,
	0x77, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0e, 0x63, 0x6c, 0x61,
	0x77, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x63,
	0x6c, 0x61, 0x77, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x66, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x12, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x31, 0x0a, 0x0a, 0x63, 0x6c,
	0x61, 0x69, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a,
	0x0d, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x32, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x53,
	0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x56, 0x32, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x73, 0x41, 0x74, 0x22, 0xe2, 0x02, 0x0a, 0x0d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x3a, 0x0a, 0x1a, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x62, 0x67,
	0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x6c,
	0x61, 0x69, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x74, 0x69, 0x6c, 0x65, 0x42,
	0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x61, 0x69,
	0x6d, 0x12, 0x38, 0x0a, 0x19, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x62, 0x67, 0x5f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x74, 0x69, 0x6c, 0x65, 0x42, 0x67, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x41, 0x66, 0x74, 0x65, 0x72, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x51, 0x0a, 0x16, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6c, 0x65,
	0x54, 0x68, 0x65, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x54, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38,
	0x0a, 0x18, 0x69, 0x73, 0x5f, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75,
	0x6e, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x16, 0x69, 0x73, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x73,
	0x6b, 0x69, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x6b, 0x69, 0x70,
	0x5f, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x27, 0x0a, 0x0f, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x70, 0x65,
	0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x70, 0x65, 0x65, 0x64, 0x22, 0x51, 0x0a, 0x04, 0x43, 0x61, 0x73, 0x68,
	0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x74, 0x78, 0x6e, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x74, 0x78, 0x6e, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x22, 0x7c, 0x0a, 0x07, 0x46,
	0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x39, 0x0a, 0x0a,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x73, 0x41, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x66, 0x69,
	0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69,
	0x73, 0x46, 0x69, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22, 0x2f, 0x0a, 0x09, 0x4c, 0x75, 0x63,
	0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x5f,
	0x64, 0x72, 0x61, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c,
	0x75, 0x63, 0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x49, 0x64, 0x22, 0x0a, 0x0a, 0x08, 0x4e, 0x6f,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x22, 0x11, 0x0a, 0x0f, 0x4d, 0x65, 0x74, 0x61, 0x6c, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x22, 0x2f, 0x0a, 0x09, 0x45, 0x67, 0x76,
	0x42, 0x61, 0x73, 0x6b, 0x65, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x65, 0x67, 0x76, 0x5f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x65,
	0x67, 0x76, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x32, 0x0a, 0x15, 0x54, 0x68,
	0x72, 0x69, 0x77, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x22, 0x42,
	0x0a, 0x14, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c,
	0x45, 0x72, 0x61, 0x73, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0xa5, 0x02, 0x0a, 0x0c, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x3f, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x4b, 0x0a, 0x14, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x12, 0x6d, 0x61, 0x74, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x44, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x47, 0x0a,
	0x11, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x5e, 0x0a, 0x12, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x49, 0x64, 0x12,
	0x29, 0x0a, 0x10, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x68,
	0x61, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x53, 0x68, 0x61, 0x72, 0x65, 0x22, 0xc3, 0x01, 0x0a, 0x0a, 0x47,
	0x69, 0x66, 0x74, 0x48, 0x61, 0x6d, 0x70, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x10, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50,
	0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0f, 0x73, 0x68,
	0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0xbf, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x42,
	0x0a, 0x1d, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x53, 0x75, 0x70,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x22, 0xda, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x41, 0x0a, 0x0d, 0x61, 0x62, 0x73, 0x6f, 0x6c,
	0x75, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x0c, 0x61, 0x62,
	0x73, 0x6f, 0x6c, 0x75, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x18, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6d,
	0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x15,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x4d, 0x69,
	0x6e, 0x75, 0x74, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x1d, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x75,
	0x70, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x55, 0x70, 0x54, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4f, 0x66, 0x4e, 0x65,
	0x78, 0x74, 0x44, 0x61, 0x79, 0x42, 0x08, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22,
	0xc0, 0x03, 0x0a, 0x13, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2c, 0x0a, 0x12,
	0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x66,
	0x74, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x43, 0x6c, 0x61, 0x69,
	0x6d, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x66, 0x0a, 0x16, 0x68, 0x74, 0x6d, 0x6c, 0x5f, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x2e, 0x48, 0x74, 0x6d, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74,
	0x65, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x14, 0x68, 0x74, 0x6d, 0x6c, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x37,
	0x0a, 0x18, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x62,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x15, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x42, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x54, 0x65, 0x78, 0x74, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x1a,
	0x41, 0x0a, 0x13, 0x48, 0x74, 0x6d, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f,
	0x64, 0x79, 0x22, 0xe3, 0x08, 0x0a, 0x0c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x52, 0x07, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x43, 0x0a, 0x0f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x5c, 0x0a, 0x1d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x1a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x34,
	0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x66, 0x0a, 0x1d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74,
	0x73, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x1a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x6c,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x73, 0x6b, 0x75, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x6b, 0x75, 0x12, 0x23, 0x0a,
	0x04, 0x63, 0x61, 0x73, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x61, 0x73, 0x68, 0x48, 0x00, 0x52, 0x04, 0x63, 0x61,
	0x73, 0x68, 0x12, 0x2d, 0x0a, 0x08, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x46,
	0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x48, 0x00, 0x52, 0x07, 0x66, 0x69, 0x43, 0x6f, 0x69, 0x6e,
	0x73, 0x12, 0x33, 0x0a, 0x0a, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x4c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x48, 0x00, 0x52, 0x09, 0x6c, 0x75, 0x63,
	0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x12, 0x3c, 0x0a, 0x0d, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x12, 0x36, 0x0a, 0x0b, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x68, 0x61, 0x6d,
	0x70, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x48, 0x61, 0x6d, 0x70, 0x65, 0x72, 0x48, 0x00,
	0x52, 0x0a, 0x67, 0x69, 0x66, 0x74, 0x48, 0x61, 0x6d, 0x70, 0x65, 0x72, 0x12, 0x46, 0x0a, 0x11,
	0x6d, 0x65, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72,
	0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x48, 0x00, 0x52, 0x0f, 0x6d, 0x65, 0x74, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x12, 0x33, 0x0a, 0x0a, 0x65, 0x67, 0x76, 0x5f, 0x62, 0x61, 0x73, 0x6b,
	0x65, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x45, 0x67, 0x76, 0x42, 0x61, 0x73, 0x6b, 0x65, 0x74, 0x48, 0x00, 0x52, 0x09,
	0x65, 0x67, 0x76, 0x42, 0x61, 0x73, 0x6b, 0x65, 0x74, 0x12, 0x30, 0x0a, 0x09, 0x6e, 0x6f, 0x5f,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x4e, 0x6f, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x48,
	0x00, 0x52, 0x08, 0x6e, 0x6f, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x58, 0x0a, 0x17, 0x74,
	0x68, 0x72, 0x69, 0x77, 0x65, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x54, 0x68, 0x72, 0x69, 0x77, 0x65, 0x42, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x15,
	0x74, 0x68, 0x72, 0x69, 0x77, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x3f, 0x0a, 0x0e, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x55, 0x53, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x48, 0x00, 0x52, 0x0d, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x56, 0x0a, 0x17, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x65, 0x72, 0x61, 0x73, 0x65,
	0x72, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c,
	0x45, 0x72, 0x61, 0x73, 0x65, 0x72, 0x48, 0x00, 0x52, 0x14, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x45, 0x72, 0x61, 0x73, 0x65, 0x72, 0x42, 0x08,
	0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe6, 0x04, 0x0a, 0x1a, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x88, 0x01, 0x0a, 0x20, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e,
	0x69, 0x74, 0x73, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x1d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73,
	0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x69,
	0x65, 0x73, 0x12, 0x74, 0x0a, 0x23, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x66, 0x6c, 0x6f, 0x77,
	0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x72, 0x65,
	0x76, 0x65, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x27, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x46,
	0x6c, 0x6f, 0x77, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65,
	0x76, 0x65, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x1e, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x46,
	0x6c, 0x6f, 0x77, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65,
	0x76, 0x65, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x1a, 0xc6, 0x02, 0x0a, 0x1b, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x77, 0x0a, 0x0f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x1a, 0x8a, 0x01, 0x0a, 0x0e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x22, 0xa1, 0x03, 0x0a, 0x0d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x3c, 0x0a, 0x1b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x64,
	0x65, 0x63, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x65,
	0x63, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x17, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x44, 0x65, 0x63, 0x69, 0x64, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x53, 0x65, 0x63,
	0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0a, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2f,
	0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x38, 0x0a, 0x17, 0x69, 0x73, 0x5f, 0x75, 0x6e, 0x6c,
	0x6f, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x6b, 0x79,
	0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x13, 0x69, 0x73, 0x55,
	0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x4d, 0x69, 0x6e, 0x4b, 0x79, 0x63,
	0x12, 0x42, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x1c, 0x69, 0x73, 0x5f, 0x69, 0x6d, 0x70, 0x6c, 0x69,
	0x63, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x69, 0x73, 0x49, 0x6d,
	0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x4c, 0x6f, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0xdf, 0x02, 0x0a, 0x13, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x45, 0x0a,
	0x10, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x0f, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x4a, 0x0a, 0x0b, 0x73, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x44, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x1a, 0x97, 0x01, 0x0a, 0x0a, 0x53, 0x44, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x27, 0x0a, 0x0f, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x47, 0x0a, 0x11, 0x6e, 0x6f, 0x6d, 0x69,
	0x6e, 0x65, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0f, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x1b, 0x0a, 0x19, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x22, 0xcf, 0x0b, 0x0a, 0x0e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x78, 0x0a, 0x1c, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x19, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x5b, 0x0a, 0x13, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x12, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x1a, 0x90, 0x09, 0x0a, 0x1f, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0xb1, 0x01, 0x0a, 0x27, 0x63, 0x63, 0x5f, 0x74, 0x6f, 0x70,
	0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x73, 0x5f, 0x73, 0x70, 0x65, 0x6e, 0x64,
	0x73, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x2e, 0x43, 0x63, 0x54, 0x6f, 0x70, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x73, 0x53,
	0x70, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x22, 0x63, 0x63, 0x54, 0x6f, 0x70, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x73, 0x53, 0x70, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0xbd, 0x01, 0x0a, 0x2b, 0x63, 0x63,
	0x5f, 0x63, 0x75, 0x72, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x73, 0x5f, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x73, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x5e, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x63, 0x43, 0x75, 0x72, 0x61, 0x74,
	0x65, 0x64, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x73, 0x53, 0x70, 0x65, 0x6e, 0x64,
	0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x26, 0x63, 0x63, 0x43, 0x75, 0x72, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x73, 0x53, 0x70, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x86, 0x03, 0x0a, 0x22, 0x43, 0x63,
	0x54, 0x6f, 0x70, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x73, 0x53, 0x70, 0x65, 0x6e,
	0x64, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x3a, 0x0a, 0x19, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x17, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x12, 0xcf, 0x01, 0x0a,
	0x25, 0x74, 0x6f, 0x70, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x31, 0x78, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x7f, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x63, 0x54, 0x6f, 0x70, 0x4d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x73, 0x53, 0x70, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x6f, 0x70, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x54, 0x6f, 0x31, 0x78, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x1f, 0x74,
	0x6f, 0x70, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x54, 0x6f,
	0x31, 0x78, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x52,
	0x0a, 0x24, 0x54, 0x6f, 0x70, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x54, 0x6f, 0x31, 0x78, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0xe3, 0x02, 0x0a, 0x26, 0x43, 0x63, 0x43, 0x75, 0x72, 0x61, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x73, 0x53, 0x70, 0x65, 0x6e, 0x64, 0x73, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0xe0, 0x01,
	0x0a, 0x29, 0x63, 0x75, 0x72, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x31, 0x78, 0x5f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x87, 0x01, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x63, 0x43, 0x75,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x73, 0x53, 0x70,
	0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x2e, 0x43, 0x75, 0x72, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x54, 0x6f, 0x31, 0x78, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x23, 0x63, 0x75, 0x72,
	0x61, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x54, 0x6f, 0x31, 0x78, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x1a, 0x56, 0x0a, 0x28, 0x43, 0x75, 0x72, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x54, 0x6f, 0x31, 0x78, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x1a, 0x53, 0x0a, 0x12, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x1b, 0x61, 0x72,
	0x65, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x18, 0x61, 0x72, 0x65, 0x41, 0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x22, 0x61, 0x0a, 0x13, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x69, 0x6e, 0x69, 0x6d, 0x61, 0x6c,
	0x12, 0x34, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x22, 0x56, 0x0a, 0x0d,
	0x55, 0x53, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x2a, 0xf9, 0x01, 0x0a, 0x0a, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12,
	0x08, 0x0a, 0x04, 0x43, 0x41, 0x53, 0x48, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x49, 0x5f,
	0x43, 0x4f, 0x49, 0x4e, 0x53, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x55, 0x43, 0x4b, 0x59,
	0x5f, 0x44, 0x52, 0x41, 0x57, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x4d, 0x41, 0x52, 0x54,
	0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x47, 0x49,
	0x46, 0x54, 0x5f, 0x48, 0x41, 0x4d, 0x50, 0x45, 0x52, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11, 0x4d,
	0x45, 0x54, 0x41, 0x4c, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x10, 0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x47, 0x56, 0x5f, 0x42, 0x41, 0x53, 0x4b, 0x45, 0x54,
	0x10, 0x08, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x48, 0x52, 0x49, 0x57, 0x45, 0x5f, 0x42, 0x45, 0x4e,
	0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x10, 0x09, 0x12,
	0x0c, 0x0a, 0x08, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x10, 0x0a, 0x12, 0x1b, 0x0a,
	0x17, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x42, 0x49, 0x4c,
	0x4c, 0x5f, 0x45, 0x52, 0x41, 0x53, 0x45, 0x52, 0x10, 0x0b, 0x22, 0x04, 0x08, 0x0c, 0x10, 0x0c,
	0x2a, 0xc3, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16,
	0x0a, 0x12, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53,
	0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x10,
	0x03, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x52, 0x4f, 0x43,
	0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b,
	0x43, 0x4c, 0x41, 0x57, 0x45, 0x44, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x06, 0x12, 0x0a, 0x0a,
	0x06, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50,
	0x49, 0x52, 0x45, 0x44, 0x10, 0x08, 0x2a, 0xa7, 0x02, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x20, 0x0a, 0x1c, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49,
	0x4d, 0x50, 0x4c, 0x49, 0x43, 0x49, 0x54, 0x4c, 0x59, 0x5f, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x45, 0x58, 0x50, 0x4c, 0x49, 0x43, 0x49, 0x54, 0x4c, 0x59, 0x5f, 0x4c, 0x4f, 0x43, 0x4b,
	0x45, 0x44, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55,
	0x52, 0x45, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x59, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x49, 0x5f, 0x43, 0x4f, 0x49,
	0x4e, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c,
	0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x45, 0x44, 0x5f, 0x46, 0x49, 0x5f, 0x43, 0x4f, 0x49, 0x4e, 0x53, 0x10, 0x64, 0x12, 0x28,
	0x0a, 0x24, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x47,
	0x49, 0x53, 0x54, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4c, 0x55, 0x43, 0x4b,
	0x59, 0x5f, 0x44, 0x52, 0x41, 0x57, 0x10, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x66,
	0x2a, 0x43, 0x0a, 0x0e, 0x43, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4c, 0x41, 0x57, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52,
	0x53, 0x41, 0x4c, 0x10, 0x01, 0x2a, 0x6f, 0x0a, 0x13, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54,
	0x69, 0x6c, 0x65, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d,
	0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x48, 0x45, 0x4d,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x1d, 0x0a, 0x19, 0x50, 0x4c, 0x41, 0x4e, 0x54, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x43,
	0x48, 0x52, 0x59, 0x53, 0x41, 0x4e, 0x54, 0x48, 0x45, 0x4d, 0x55, 0x4d, 0x10, 0x01, 0x12, 0x16,
	0x0a, 0x12, 0x50, 0x4c, 0x41, 0x4e, 0x54, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x4f, 0x52,
	0x43, 0x48, 0x49, 0x44, 0x10, 0x02, 0x2a, 0xb9, 0x01, 0x0a, 0x1e, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x65, 0x76, 0x65, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4c, 0x41,
	0x49, 0x4d, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x41, 0x4c, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x3b, 0x0a, 0x37, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x52, 0x45, 0x56,
	0x45, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4d, 0x45,
	0x44, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x53, 0x10, 0x01, 0x12, 0x32,
	0x0a, 0x2e, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x52, 0x45, 0x57,
	0x41, 0x52, 0x44, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x41, 0x4c,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45,
	0x10, 0x02, 0x2a, 0xfe, 0x02, 0x0a, 0x09, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61, 0x67,
	0x12, 0x22, 0x0a, 0x1e, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c,
	0x41, 0x59, 0x5f, 0x54, 0x41, 0x47, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f,
	0x42, 0x41, 0x53, 0x49, 0x43, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x49, 0x45, 0x52, 0x5f,
	0x46, 0x49, 0x5f, 0x50, 0x4c, 0x55, 0x53, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x49, 0x45,
	0x52, 0x5f, 0x46, 0x49, 0x5f, 0x49, 0x4e, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x10, 0x03, 0x12,
	0x12, 0x0a, 0x0e, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41,
	0x4d, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x4f, 0x4f, 0x53, 0x54, 0x45, 0x52, 0x5f,
	0x31, 0x58, 0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x4f, 0x4f, 0x53, 0x54, 0x45, 0x52, 0x5f,
	0x32, 0x58, 0x10, 0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x4f, 0x4f, 0x53, 0x54, 0x45, 0x52, 0x5f,
	0x33, 0x58, 0x10, 0x08, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x4f, 0x4f, 0x53, 0x54, 0x45, 0x52, 0x5f,
	0x31, 0x30, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x09, 0x12, 0x16, 0x0a, 0x12,
	0x42, 0x4f, 0x4f, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x31, 0x35, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45,
	0x4e, 0x54, 0x10, 0x0a, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x4f, 0x4f, 0x53, 0x54, 0x45, 0x52, 0x5f,
	0x32, 0x30, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x0b, 0x12, 0x16, 0x0a, 0x12,
	0x42, 0x4f, 0x4f, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x35, 0x30, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45,
	0x4e, 0x54, 0x10, 0x0c, 0x12, 0x17, 0x0a, 0x13, 0x42, 0x4f, 0x4f, 0x53, 0x54, 0x45, 0x52, 0x5f,
	0x31, 0x30, 0x30, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x0d, 0x12, 0x21, 0x0a,
	0x1d, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x50, 0x45,
	0x4e, 0x44, 0x53, 0x5f, 0x32, 0x58, 0x5f, 0x42, 0x4f, 0x4f, 0x53, 0x54, 0x45, 0x52, 0x10, 0x0e,
	0x12, 0x21, 0x0a, 0x1d, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x53, 0x50, 0x45, 0x4e, 0x44, 0x53, 0x5f, 0x35, 0x58, 0x5f, 0x42, 0x4f, 0x4f, 0x53, 0x54, 0x45,
	0x52, 0x10, 0x0f, 0x2a, 0x58, 0x0a, 0x09, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1a, 0x0a, 0x16, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11,
	0x43, 0x4c, 0x41, 0x49, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41,
	0x4c, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x4d, 0x41, 0x54, 0x49, 0x43, 0x10, 0x02, 0x42, 0x48, 0x0a,
	0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_rewards_reward_proto_rawDescOnce sync.Once
	file_api_rewards_reward_proto_rawDescData = file_api_rewards_reward_proto_rawDesc
)

func file_api_rewards_reward_proto_rawDescGZIP() []byte {
	file_api_rewards_reward_proto_rawDescOnce.Do(func() {
		file_api_rewards_reward_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_rewards_reward_proto_rawDescData)
	})
	return file_api_rewards_reward_proto_rawDescData
}

var file_api_rewards_reward_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_api_rewards_reward_proto_msgTypes = make([]protoimpl.MessageInfo, 33)
var file_api_rewards_reward_proto_goTypes = []interface{}{
	(RewardType)(0),                                 // 0: rewards.RewardType
	(RewardStatus)(0),                               // 1: rewards.RewardStatus
	(SubStatus)(0),                                  // 2: rewards.SubStatus
	(ClawbackReason)(0),                             // 3: rewards.ClawbackReason
	(RewardTileThemeType)(0),                        // 4: rewards.RewardTileThemeType
	(ClaimFlowRewardValueRevealType)(0),             // 5: rewards.ClaimFlowRewardValueRevealType
	(RewardTag)(0),                                  // 6: rewards.RewardTag
	(ClaimType)(0),                                  // 7: rewards.ClaimType
	(*Reward)(nil),                                  // 8: rewards.Reward
	(*RewardDisplay)(nil),                           // 9: rewards.RewardDisplay
	(*Cash)(nil),                                    // 10: rewards.Cash
	(*FiCoins)(nil),                                 // 11: rewards.FiCoins
	(*LuckyDraw)(nil),                               // 12: rewards.LuckyDraw
	(*NoReward)(nil),                                // 13: rewards.NoReward
	(*MetalCreditCard)(nil),                         // 14: rewards.MetalCreditCard
	(*EgvBasket)(nil),                               // 15: rewards.EgvBasket
	(*ThriweBenefitsPackage)(nil),                   // 16: rewards.ThriweBenefitsPackage
	(*CreditCardBillEraser)(nil),                    // 17: rewards.CreditCardBillEraser
	(*SmartDeposit)(nil),                            // 18: rewards.SmartDeposit
	(*DepositNomineeInfo)(nil),                      // 19: rewards.DepositNomineeInfo
	(*GiftHamper)(nil),                              // 20: rewards.GiftHamper
	(*RewardEvent)(nil),                             // 21: rewards.RewardEvent
	(*RewardTimeConfig)(nil),                        // 22: rewards.RewardTimeConfig
	(*RewardOptionDisplay)(nil),                     // 23: rewards.RewardOptionDisplay
	(*RewardOption)(nil),                            // 24: rewards.RewardOption
	(*RewardUnitsCalculationInfo)(nil),              // 25: rewards.RewardUnitsCalculationInfo
	(*RewardOptions)(nil),                           // 26: rewards.RewardOptions
	(*RewardClaimMetadata)(nil),                     // 27: rewards.RewardClaimMetadata
	(*RewardMetadata)(nil),                          // 28: rewards.RewardMetadata
	(*RewardOptionMinimal)(nil),                     // 29: rewards.RewardOptionMinimal
	(*USStockReward)(nil),                           // 30: rewards.USStockReward
	(*RewardOptionDisplay_HtmlFormattedDetail)(nil), // 31: rewards.RewardOptionDisplay.HtmlFormattedDetail
	(*RewardUnitsCalculationInfo_RewardUnitsCalculationEntry)(nil),                                // 32: rewards.RewardUnitsCalculationInfo.RewardUnitsCalculationEntry
	(*RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails)(nil),                 // 33: rewards.RewardUnitsCalculationInfo.RewardUnitsCalculationEntry.DisplayDetails
	(*RewardClaimMetadata_SDMetadata)(nil),                                                        // 34: rewards.RewardClaimMetadata.SDMetadata
	(*RewardMetadata_RewardOfferTypeSpecificMetadata)(nil),                                        // 35: rewards.RewardMetadata.RewardOfferTypeSpecificMetadata
	(*RewardMetadata_ProjectionMetadata)(nil),                                                     // 36: rewards.RewardMetadata.ProjectionMetadata
	(*RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata)(nil),     // 37: rewards.RewardMetadata.RewardOfferTypeSpecificMetadata.CcTopMerchantsSpendsRewardMetadata
	(*RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata)(nil), // 38: rewards.RewardMetadata.RewardOfferTypeSpecificMetadata.CcCuratedMerchantsSpendsRewardMetadata
	nil,                                 // 39: rewards.RewardMetadata.RewardOfferTypeSpecificMetadata.CcTopMerchantsSpendsRewardMetadata.TopMerchantNameTo1xRewardAmountEntry
	nil,                                 // 40: rewards.RewardMetadata.RewardOfferTypeSpecificMetadata.CcCuratedMerchantsSpendsRewardMetadata.CuratedMerchantNameTo1xRewardAmountEntry
	(*timestamppb.Timestamp)(nil),       // 41: google.protobuf.Timestamp
	(RewardOfferType)(0),                // 42: rewards.RewardOfferType
	(CollectedDataType)(0),              // 43: rewards.CollectedDataType
	(*money.Money)(nil),                 // 44: google.type.Money
	(*postaladdress.PostalAddress)(nil), // 45: google.type.PostalAddress
	(*queue.ConsumerRequestHeader)(nil), // 46: queue.ConsumerRequestHeader
}
var file_api_rewards_reward_proto_depIdxs = []int32{
	1,  // 0: rewards.Reward.status:type_name -> rewards.RewardStatus
	41, // 1: rewards.Reward.created_at:type_name -> google.protobuf.Timestamp
	26, // 2: rewards.Reward.reward_options:type_name -> rewards.RewardOptions
	24, // 3: rewards.Reward.chosen_reward:type_name -> rewards.RewardOption
	9,  // 4: rewards.Reward.reward_display:type_name -> rewards.RewardDisplay
	42, // 5: rewards.Reward.offer_type:type_name -> rewards.RewardOfferType
	3,  // 6: rewards.Reward.clawback_reason:type_name -> rewards.ClawbackReason
	6,  // 7: rewards.Reward.tags:type_name -> rewards.RewardTag
	43, // 8: rewards.Reward.action_type:type_name -> rewards.CollectedDataType
	28, // 9: rewards.Reward.reward_metadata:type_name -> rewards.RewardMetadata
	7,  // 10: rewards.Reward.claim_type:type_name -> rewards.ClaimType
	2,  // 11: rewards.Reward.sub_status_v2:type_name -> rewards.SubStatus
	41, // 12: rewards.Reward.action_time:type_name -> google.protobuf.Timestamp
	41, // 13: rewards.Reward.updated_at:type_name -> google.protobuf.Timestamp
	41, // 14: rewards.Reward.deleted_at:type_name -> google.protobuf.Timestamp
	41, // 15: rewards.Reward.expires_at:type_name -> google.protobuf.Timestamp
	4,  // 16: rewards.RewardDisplay.reward_tile_theme_type:type_name -> rewards.RewardTileThemeType
	44, // 17: rewards.Cash.amount:type_name -> google.type.Money
	41, // 18: rewards.FiCoins.expires_at:type_name -> google.protobuf.Timestamp
	44, // 19: rewards.CreditCardBillEraser.amount:type_name -> google.type.Money
	44, // 20: rewards.SmartDeposit.amount:type_name -> google.type.Money
	41, // 21: rewards.SmartDeposit.maturity_date:type_name -> google.protobuf.Timestamp
	22, // 22: rewards.SmartDeposit.maturity_date_config:type_name -> rewards.RewardTimeConfig
	19, // 23: rewards.SmartDeposit.nominee_info_list:type_name -> rewards.DepositNomineeInfo
	45, // 24: rewards.GiftHamper.shipping_address:type_name -> google.type.PostalAddress
	46, // 25: rewards.RewardEvent.request_header:type_name -> queue.ConsumerRequestHeader
	8,  // 26: rewards.RewardEvent.reward:type_name -> rewards.Reward
	41, // 27: rewards.RewardTimeConfig.absolute_time:type_name -> google.protobuf.Timestamp
	31, // 28: rewards.RewardOptionDisplay.html_formatted_details:type_name -> rewards.RewardOptionDisplay.HtmlFormattedDetail
	6,  // 29: rewards.RewardOptionDisplay.tags:type_name -> rewards.RewardTag
	23, // 30: rewards.RewardOption.display:type_name -> rewards.RewardOptionDisplay
	41, // 31: rewards.RewardOption.processing_date:type_name -> google.protobuf.Timestamp
	22, // 32: rewards.RewardOption.reward_processing_time_config:type_name -> rewards.RewardTimeConfig
	0,  // 33: rewards.RewardOption.reward_type:type_name -> rewards.RewardType
	25, // 34: rewards.RewardOption.reward_units_calculation_info:type_name -> rewards.RewardUnitsCalculationInfo
	10, // 35: rewards.RewardOption.cash:type_name -> rewards.Cash
	11, // 36: rewards.RewardOption.fi_coins:type_name -> rewards.FiCoins
	12, // 37: rewards.RewardOption.lucky_draw:type_name -> rewards.LuckyDraw
	18, // 38: rewards.RewardOption.smart_deposit:type_name -> rewards.SmartDeposit
	20, // 39: rewards.RewardOption.gift_hamper:type_name -> rewards.GiftHamper
	14, // 40: rewards.RewardOption.metal_credit_card:type_name -> rewards.MetalCreditCard
	15, // 41: rewards.RewardOption.egv_basket:type_name -> rewards.EgvBasket
	13, // 42: rewards.RewardOption.no_reward:type_name -> rewards.NoReward
	16, // 43: rewards.RewardOption.thriwe_benefits_package:type_name -> rewards.ThriweBenefitsPackage
	30, // 44: rewards.RewardOption.usstock_reward:type_name -> rewards.USStockReward
	17, // 45: rewards.RewardOption.credit_card_bill_eraser:type_name -> rewards.CreditCardBillEraser
	32, // 46: rewards.RewardUnitsCalculationInfo.reward_units_calculation_entries:type_name -> rewards.RewardUnitsCalculationInfo.RewardUnitsCalculationEntry
	5,  // 47: rewards.RewardUnitsCalculationInfo.claim_flow_reward_value_reveal_type:type_name -> rewards.ClaimFlowRewardValueRevealType
	41, // 48: rewards.RewardOptions.unlock_date:type_name -> google.protobuf.Timestamp
	24, // 49: rewards.RewardOptions.options:type_name -> rewards.RewardOption
	41, // 50: rewards.RewardOptions.auto_claim_time:type_name -> google.protobuf.Timestamp
	45, // 51: rewards.RewardClaimMetadata.shipping_address:type_name -> google.type.PostalAddress
	34, // 52: rewards.RewardClaimMetadata.sd_metadata:type_name -> rewards.RewardClaimMetadata.SDMetadata
	35, // 53: rewards.RewardMetadata.offer_type_specific_metadata:type_name -> rewards.RewardMetadata.RewardOfferTypeSpecificMetadata
	36, // 54: rewards.RewardMetadata.projection_metadata:type_name -> rewards.RewardMetadata.ProjectionMetadata
	0,  // 55: rewards.RewardOptionMinimal.reward_type:type_name -> rewards.RewardType
	44, // 56: rewards.USStockReward.amount:type_name -> google.type.Money
	33, // 57: rewards.RewardUnitsCalculationInfo.RewardUnitsCalculationEntry.display_details:type_name -> rewards.RewardUnitsCalculationInfo.RewardUnitsCalculationEntry.DisplayDetails
	6,  // 58: rewards.RewardUnitsCalculationInfo.RewardUnitsCalculationEntry.DisplayDetails.tags:type_name -> rewards.RewardTag
	19, // 59: rewards.RewardClaimMetadata.SDMetadata.nominee_info_list:type_name -> rewards.DepositNomineeInfo
	37, // 60: rewards.RewardMetadata.RewardOfferTypeSpecificMetadata.cc_top_merchants_spends_reward_metadata:type_name -> rewards.RewardMetadata.RewardOfferTypeSpecificMetadata.CcTopMerchantsSpendsRewardMetadata
	38, // 61: rewards.RewardMetadata.RewardOfferTypeSpecificMetadata.cc_curated_merchants_spends_reward_metadata:type_name -> rewards.RewardMetadata.RewardOfferTypeSpecificMetadata.CcCuratedMerchantsSpendsRewardMetadata
	39, // 62: rewards.RewardMetadata.RewardOfferTypeSpecificMetadata.CcTopMerchantsSpendsRewardMetadata.top_merchant_name_to_1x_reward_amount:type_name -> rewards.RewardMetadata.RewardOfferTypeSpecificMetadata.CcTopMerchantsSpendsRewardMetadata.TopMerchantNameTo1xRewardAmountEntry
	40, // 63: rewards.RewardMetadata.RewardOfferTypeSpecificMetadata.CcCuratedMerchantsSpendsRewardMetadata.curated_merchant_name_to_1x_reward_amount:type_name -> rewards.RewardMetadata.RewardOfferTypeSpecificMetadata.CcCuratedMerchantsSpendsRewardMetadata.CuratedMerchantNameTo1xRewardAmountEntry
	64, // [64:64] is the sub-list for method output_type
	64, // [64:64] is the sub-list for method input_type
	64, // [64:64] is the sub-list for extension type_name
	64, // [64:64] is the sub-list for extension extendee
	0,  // [0:64] is the sub-list for field type_name
}

func init() { file_api_rewards_reward_proto_init() }
func file_api_rewards_reward_proto_init() {
	if File_api_rewards_reward_proto != nil {
		return
	}
	file_api_rewards_collected_data_type_proto_init()
	file_api_rewards_reward_offer_type_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_rewards_reward_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardDisplay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Cash); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiCoins); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LuckyDraw); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MetalCreditCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EgvBasket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThriweBenefitsPackage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditCardBillEraser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmartDeposit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositNomineeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftHamper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardTimeConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOptionDisplay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardUnitsCalculationInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardClaimMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOptionMinimal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*USStockReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOptionDisplay_HtmlFormattedDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardUnitsCalculationInfo_RewardUnitsCalculationEntry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardClaimMetadata_SDMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardMetadata_RewardOfferTypeSpecificMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardMetadata_ProjectionMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_reward_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_rewards_reward_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*RewardTimeConfig_AbsoluteTime)(nil),
		(*RewardTimeConfig_RelativeTimeInMinutes)(nil),
	}
	file_api_rewards_reward_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*RewardOption_Cash)(nil),
		(*RewardOption_FiCoins)(nil),
		(*RewardOption_LuckyDraw)(nil),
		(*RewardOption_SmartDeposit)(nil),
		(*RewardOption_GiftHamper)(nil),
		(*RewardOption_MetalCreditCard)(nil),
		(*RewardOption_EgvBasket)(nil),
		(*RewardOption_NoReward)(nil),
		(*RewardOption_ThriweBenefitsPackage)(nil),
		(*RewardOption_UsstockReward)(nil),
		(*RewardOption_CreditCardBillEraser)(nil),
	}
	file_api_rewards_reward_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*RewardClaimMetadata_SdMetadata)(nil),
	}
	file_api_rewards_reward_proto_msgTypes[27].OneofWrappers = []interface{}{
		(*RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata_)(nil),
		(*RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_rewards_reward_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   33,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_rewards_reward_proto_goTypes,
		DependencyIndexes: file_api_rewards_reward_proto_depIdxs,
		EnumInfos:         file_api_rewards_reward_proto_enumTypes,
		MessageInfos:      file_api_rewards_reward_proto_msgTypes,
	}.Build()
	File_api_rewards_reward_proto = out.File
	file_api_rewards_reward_proto_rawDesc = nil
	file_api_rewards_reward_proto_goTypes = nil
	file_api_rewards_reward_proto_depIdxs = nil
}
