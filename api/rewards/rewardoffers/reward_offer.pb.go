//go:generate gen_sql -types=<PERSON>lyticsData,UnlockMeta,RewardOfferStatus,RewardOfferTag,GenerationType,AdditionalDetails,UserAggregateInTimePeriod

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/rewards/rewardoffers/reward_offer.proto

package rewardoffers

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	comms "github.com/epifi/gamma/api/comms"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	rewards "github.com/epifi/gamma/api/rewards"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MathematicalOperation int32

const (
	MathematicalOperation_MATHEMATICAL_OPERATION_UNSPECIFIED MathematicalOperation = 0
	MathematicalOperation_ADDITION                           MathematicalOperation = 1
	MathematicalOperation_MULTIPLICATION                     MathematicalOperation = 2
)

// Enum value maps for MathematicalOperation.
var (
	MathematicalOperation_name = map[int32]string{
		0: "MATHEMATICAL_OPERATION_UNSPECIFIED",
		1: "ADDITION",
		2: "MULTIPLICATION",
	}
	MathematicalOperation_value = map[string]int32{
		"MATHEMATICAL_OPERATION_UNSPECIFIED": 0,
		"ADDITION":                           1,
		"MULTIPLICATION":                     2,
	}
)

func (x MathematicalOperation) Enum() *MathematicalOperation {
	p := new(MathematicalOperation)
	*p = x
	return p
}

func (x MathematicalOperation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MathematicalOperation) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[0].Descriptor()
}

func (MathematicalOperation) Type() protoreflect.EnumType {
	return &file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[0]
}

func (x MathematicalOperation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MathematicalOperation.Descriptor instead.
func (MathematicalOperation) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{0}
}

// type to be sent to UI so that offers can be displayed differently
type DisplayType int32

const (
	DisplayType_NOT_SPECIFIED DisplayType = 0
	DisplayType_HEADLINE      DisplayType = 1
	DisplayType_FRINGE        DisplayType = 2
)

// Enum value maps for DisplayType.
var (
	DisplayType_name = map[int32]string{
		0: "NOT_SPECIFIED",
		1: "HEADLINE",
		2: "FRINGE",
	}
	DisplayType_value = map[string]int32{
		"NOT_SPECIFIED": 0,
		"HEADLINE":      1,
		"FRINGE":        2,
	}
)

func (x DisplayType) Enum() *DisplayType {
	p := new(DisplayType)
	*p = x
	return p
}

func (x DisplayType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisplayType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[1].Descriptor()
}

func (DisplayType) Type() protoreflect.EnumType {
	return &file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[1]
}

func (x DisplayType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisplayType.Descriptor instead.
func (DisplayType) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{1}
}

// represents status of the offer
// when offer is created, status will be CREATED
// then it goes to approval process. after approval it will be approved. if all the info is valid.
// if the offer is approved it can go to ACTIVE if offer need to be applied
// offer can move from ACTIVE<->INACTIVE to pause and resume the offer
// TERMINATED is final state and after that state can't be changed. Once TERMINATED offer is no longer applicable ever
type RewardOfferStatus int32

const (
	RewardOfferStatus_RewardOfferStatus_UNSPECIFIED RewardOfferStatus = 0
	RewardOfferStatus_CREATED                       RewardOfferStatus = 1
	RewardOfferStatus_APPROVED                      RewardOfferStatus = 2
	RewardOfferStatus_ACTIVE                        RewardOfferStatus = 3
	RewardOfferStatus_INACTIVE                      RewardOfferStatus = 4
	RewardOfferStatus_TERMINATED                    RewardOfferStatus = 5
)

// Enum value maps for RewardOfferStatus.
var (
	RewardOfferStatus_name = map[int32]string{
		0: "RewardOfferStatus_UNSPECIFIED",
		1: "CREATED",
		2: "APPROVED",
		3: "ACTIVE",
		4: "INACTIVE",
		5: "TERMINATED",
	}
	RewardOfferStatus_value = map[string]int32{
		"RewardOfferStatus_UNSPECIFIED": 0,
		"CREATED":                       1,
		"APPROVED":                      2,
		"ACTIVE":                        3,
		"INACTIVE":                      4,
		"TERMINATED":                    5,
	}
)

func (x RewardOfferStatus) Enum() *RewardOfferStatus {
	p := new(RewardOfferStatus)
	*p = x
	return p
}

func (x RewardOfferStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardOfferStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[2].Descriptor()
}

func (RewardOfferStatus) Type() protoreflect.EnumType {
	return &file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[2]
}

func (x RewardOfferStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardOfferStatus.Descriptor instead.
func (RewardOfferStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{2}
}

type RewardNotificationType int32

const (
	RewardNotificationType_UNSPECIFIED_NOTIFICATION_TYPE RewardNotificationType = 0
	RewardNotificationType_SYSTEM_TRAY                   RewardNotificationType = 1
	RewardNotificationType_IN_APP_FULL_SCREEN            RewardNotificationType = 2
	RewardNotificationType_IN_APP                        RewardNotificationType = 3
	RewardNotificationType_IN_APP_CRITICAL               RewardNotificationType = 4
	RewardNotificationType_NUDGE                         RewardNotificationType = 5
	RewardNotificationType_EMAIL                         RewardNotificationType = 6
)

// Enum value maps for RewardNotificationType.
var (
	RewardNotificationType_name = map[int32]string{
		0: "UNSPECIFIED_NOTIFICATION_TYPE",
		1: "SYSTEM_TRAY",
		2: "IN_APP_FULL_SCREEN",
		3: "IN_APP",
		4: "IN_APP_CRITICAL",
		5: "NUDGE",
		6: "EMAIL",
	}
	RewardNotificationType_value = map[string]int32{
		"UNSPECIFIED_NOTIFICATION_TYPE": 0,
		"SYSTEM_TRAY":                   1,
		"IN_APP_FULL_SCREEN":            2,
		"IN_APP":                        3,
		"IN_APP_CRITICAL":               4,
		"NUDGE":                         5,
		"EMAIL":                         6,
	}
)

func (x RewardNotificationType) Enum() *RewardNotificationType {
	p := new(RewardNotificationType)
	*p = x
	return p
}

func (x RewardNotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardNotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[3].Descriptor()
}

func (RewardNotificationType) Type() protoreflect.EnumType {
	return &file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[3]
}

func (x RewardNotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardNotificationType.Descriptor instead.
func (RewardNotificationType) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{3}
}

// RewardOfferTag is used to add extra metadata to reward offer.
// This can be used to allow filtering of reward offers for display etc use cases. Like if we want to display
// in app referral offers to only users who onboarded through in app referral channel, then we can filter
// reward offers that have IN_APP_REFERRAL tag.
type RewardOfferTag int32

const (
	RewardOfferTag_REWARD_OFFER_TAG_UNSPECIFIED RewardOfferTag = 0
	// denotes that offer is relevant for user who didn't onboarded through referral.
	RewardOfferTag_NON_REFERRAL RewardOfferTag = 1
	// denotes that offer is relevant for user who onboarded through InApp referral.
	RewardOfferTag_IN_APP_REFERRAL RewardOfferTag = 2
	// denotes that offer is relevant for user who onboarded through Influencer referral.
	RewardOfferTag_INFLUENCER_REFERRAL RewardOfferTag = 3
	// denotes that offer is relevant for user who onboarded through customer support referral.
	RewardOfferTag_CUSTOMER_SUPPORT_REFERRAL RewardOfferTag = 4
	// denotes that offer is relevant for user who onboarded using Regular type finite code.
	RewardOfferTag_REGULAR_FINITE_CODE RewardOfferTag = 5
	// denotes that offer is relevant for user who onboarded using Golden Ticket type finite code.
	RewardOfferTag_GOLDEN_TICKET_FINITE_CODE RewardOfferTag = 6
	// denotes the offer is related to first fund addition.
	RewardOfferTag_FIRST_FUND_ADDITION RewardOfferTag = 7
	// denotes the offer is related to subsequent fund addition.
	RewardOfferTag_SUBSEQUENT_FUND_ADDITION RewardOfferTag = 8
	// denotes that offer is relevant for user who onboarded through google pay referral channel.
	RewardOfferTag_GPAY_REFERRAL RewardOfferTag = 9
	// denotes that offer is relevant for user who onboarded through phonepe referral channel.
	RewardOfferTag_PHONEPE_REFERRAL RewardOfferTag = 10
	// denotes that offer is relevant for user who onboarded through vantage circle referral channel.
	RewardOfferTag_VANTAGE_CIRCLE_REFERRAL RewardOfferTag = 11
	// denotes that offer is relevant for user who onboarded using GPAY_TYPE1 type finite code.
	RewardOfferTag_GPAY_TYPE1_FINITE_CODE RewardOfferTag = 12
	// denotes that offer is relevant for user who onboarded using GPAY_TYPE2 type finite code.
	RewardOfferTag_GPAY_TYPE2_FINITE_CODE RewardOfferTag = 13
	// denotes that offer is relevant for user who onboarded using GPAY_TYPE3 type finite code.
	RewardOfferTag_GPAY_TYPE3_FINITE_CODE RewardOfferTag = 14
	// denotes that offer is relevant for user who onboarded using GPAY_TYPE4 type finite code.
	RewardOfferTag_GPAY_TYPE4_FINITE_CODE RewardOfferTag = 15
	// denotes that offer is relevant for user who onboarded using PHONEPE_TYPE1 type finite code.
	RewardOfferTag_PHONEPE_TYPE1_FINITE_CODE RewardOfferTag = 16
	// denotes that offer is relevant for user who onboarded using PHONEPE_TYPE2 type type finite code.
	RewardOfferTag_PHONEPE_TYPE2_FINITE_CODE RewardOfferTag = 17
	// denotes that offer is relevant for user who onboarded using PHONEPE_TYPE3 type finite code.
	RewardOfferTag_PHONEPE_TYPE3_FINITE_CODE RewardOfferTag = 18
	// denotes that offer is relevant for user who onboarded using PHONEPE_TYPE4 type finite code.
	RewardOfferTag_PHONEPE_TYPE4_FINITE_CODE RewardOfferTag = 19
	// denotes that offer is relevant for user who onboarded using VANTAGE_CIRCLE_TYPE1 type finite code.
	RewardOfferTag_VANTAGE_CIRCLE_TYPE1_FINITE_CODE RewardOfferTag = 20
	// denotes that offer is relevant for user whose salary program is active
	RewardOfferTag_SALARY_PROGRAM_ACTIVE RewardOfferTag = 21
	// denotes that offer is relevant for user whose salary program is inactive
	RewardOfferTag_SALARY_PROGRAM_INACTIVE RewardOfferTag = 22
	// denotes that offer is relevant for user whose account tier is fi basic
	RewardOfferTag_ACCOUNT_TIER_FI_BASIC RewardOfferTag = 23
	// denotes that offer is relevant for user whose account tier is fi plus
	RewardOfferTag_ACCOUNT_TIER_FI_PLUS RewardOfferTag = 24
	// denotes that offer is relevant for user whose account tier is fi infinite
	RewardOfferTag_ACCOUNT_TIER_FI_INFINITE RewardOfferTag = 25
	// denotes that offer is relevant for user whose account tier is fi salary
	RewardOfferTag_ACCOUNT_TIER_FI_SALARY RewardOfferTag = 26
	// denotes that offer is relevant for user whose account tier is fi salary lite
	RewardOfferTag_ACCOUNT_TIER_FI_SALARY_LITE RewardOfferTag = 30
	// denotes that offer is relevant for user whose account tier is aa salary
	RewardOfferTag_ACCOUNT_TIER_FI_AA_SALARY RewardOfferTag = 34
	// denotes that offer is relevant for user whose account tier is aa salary band 1
	RewardOfferTag_ACCOUNT_TIER_FI_AA_SALARY_BAND_1 RewardOfferTag = 35
	// denotes that offer is relevant for user whose account tier is aa salary band 2
	RewardOfferTag_ACCOUNT_TIER_FI_AA_SALARY_BAND_2 RewardOfferTag = 36
	// denotes that offer is relevant for user whose account tier is aa salary band 3
	RewardOfferTag_ACCOUNT_TIER_FI_AA_SALARY_BAND_3 RewardOfferTag = 37
	// denotes that offer is relevant for user whose account tier is fi regular
	RewardOfferTag_ACCOUNT_TIER_FI_REGULAR RewardOfferTag = 39
	// denotes that offer is relevant for user whose account tier is salary basic
	RewardOfferTag_ACCOUNT_TIER_FI_SALARY_BASIC RewardOfferTag = 40
	// denotes that offer is relevant for user who onboarded through ACQUISITION referral program.
	RewardOfferTag_ACQUISITION_REFERRAL RewardOfferTag = 27
	// denotes the offer is relevant for full salary account users
	RewardOfferTag_FULL_SALARY_ACCOUNT_OFFER RewardOfferTag = 28
	// denotes the offer is relevant for salary lite account users
	RewardOfferTag_SALARY_LITE_ACCOUNT_OFFER RewardOfferTag = 29
	// denotes the offer is relevant for AA salary account users
	RewardOfferTag_AA_SALARY_ACCOUNT_OFFER RewardOfferTag = 38
	// denotes that offer is relevant for fi lite users
	RewardOfferTag_ACCOUNT_TYPE_FI_LITE RewardOfferTag = 31
	// denotes that offer is relevant for user onboarded through Timesprime channel
	RewardOfferTag_TIMESPRIME_REFERRAL RewardOfferTag = 32
	// denotes that offer is relevant for user onboarded through Timespoint channel
	RewardOfferTag_TIMESPOINT_REFERRAL RewardOfferTag = 33
	// denotes health insurance offer provided for salary program users
	RewardOfferTag_SALARY_PROGRAM_HEALTH_INSURANCE_OFFER RewardOfferTag = 41
)

// Enum value maps for RewardOfferTag.
var (
	RewardOfferTag_name = map[int32]string{
		0:  "REWARD_OFFER_TAG_UNSPECIFIED",
		1:  "NON_REFERRAL",
		2:  "IN_APP_REFERRAL",
		3:  "INFLUENCER_REFERRAL",
		4:  "CUSTOMER_SUPPORT_REFERRAL",
		5:  "REGULAR_FINITE_CODE",
		6:  "GOLDEN_TICKET_FINITE_CODE",
		7:  "FIRST_FUND_ADDITION",
		8:  "SUBSEQUENT_FUND_ADDITION",
		9:  "GPAY_REFERRAL",
		10: "PHONEPE_REFERRAL",
		11: "VANTAGE_CIRCLE_REFERRAL",
		12: "GPAY_TYPE1_FINITE_CODE",
		13: "GPAY_TYPE2_FINITE_CODE",
		14: "GPAY_TYPE3_FINITE_CODE",
		15: "GPAY_TYPE4_FINITE_CODE",
		16: "PHONEPE_TYPE1_FINITE_CODE",
		17: "PHONEPE_TYPE2_FINITE_CODE",
		18: "PHONEPE_TYPE3_FINITE_CODE",
		19: "PHONEPE_TYPE4_FINITE_CODE",
		20: "VANTAGE_CIRCLE_TYPE1_FINITE_CODE",
		21: "SALARY_PROGRAM_ACTIVE",
		22: "SALARY_PROGRAM_INACTIVE",
		23: "ACCOUNT_TIER_FI_BASIC",
		24: "ACCOUNT_TIER_FI_PLUS",
		25: "ACCOUNT_TIER_FI_INFINITE",
		26: "ACCOUNT_TIER_FI_SALARY",
		30: "ACCOUNT_TIER_FI_SALARY_LITE",
		34: "ACCOUNT_TIER_FI_AA_SALARY",
		35: "ACCOUNT_TIER_FI_AA_SALARY_BAND_1",
		36: "ACCOUNT_TIER_FI_AA_SALARY_BAND_2",
		37: "ACCOUNT_TIER_FI_AA_SALARY_BAND_3",
		39: "ACCOUNT_TIER_FI_REGULAR",
		40: "ACCOUNT_TIER_FI_SALARY_BASIC",
		27: "ACQUISITION_REFERRAL",
		28: "FULL_SALARY_ACCOUNT_OFFER",
		29: "SALARY_LITE_ACCOUNT_OFFER",
		38: "AA_SALARY_ACCOUNT_OFFER",
		31: "ACCOUNT_TYPE_FI_LITE",
		32: "TIMESPRIME_REFERRAL",
		33: "TIMESPOINT_REFERRAL",
		41: "SALARY_PROGRAM_HEALTH_INSURANCE_OFFER",
	}
	RewardOfferTag_value = map[string]int32{
		"REWARD_OFFER_TAG_UNSPECIFIED":          0,
		"NON_REFERRAL":                          1,
		"IN_APP_REFERRAL":                       2,
		"INFLUENCER_REFERRAL":                   3,
		"CUSTOMER_SUPPORT_REFERRAL":             4,
		"REGULAR_FINITE_CODE":                   5,
		"GOLDEN_TICKET_FINITE_CODE":             6,
		"FIRST_FUND_ADDITION":                   7,
		"SUBSEQUENT_FUND_ADDITION":              8,
		"GPAY_REFERRAL":                         9,
		"PHONEPE_REFERRAL":                      10,
		"VANTAGE_CIRCLE_REFERRAL":               11,
		"GPAY_TYPE1_FINITE_CODE":                12,
		"GPAY_TYPE2_FINITE_CODE":                13,
		"GPAY_TYPE3_FINITE_CODE":                14,
		"GPAY_TYPE4_FINITE_CODE":                15,
		"PHONEPE_TYPE1_FINITE_CODE":             16,
		"PHONEPE_TYPE2_FINITE_CODE":             17,
		"PHONEPE_TYPE3_FINITE_CODE":             18,
		"PHONEPE_TYPE4_FINITE_CODE":             19,
		"VANTAGE_CIRCLE_TYPE1_FINITE_CODE":      20,
		"SALARY_PROGRAM_ACTIVE":                 21,
		"SALARY_PROGRAM_INACTIVE":               22,
		"ACCOUNT_TIER_FI_BASIC":                 23,
		"ACCOUNT_TIER_FI_PLUS":                  24,
		"ACCOUNT_TIER_FI_INFINITE":              25,
		"ACCOUNT_TIER_FI_SALARY":                26,
		"ACCOUNT_TIER_FI_SALARY_LITE":           30,
		"ACCOUNT_TIER_FI_AA_SALARY":             34,
		"ACCOUNT_TIER_FI_AA_SALARY_BAND_1":      35,
		"ACCOUNT_TIER_FI_AA_SALARY_BAND_2":      36,
		"ACCOUNT_TIER_FI_AA_SALARY_BAND_3":      37,
		"ACCOUNT_TIER_FI_REGULAR":               39,
		"ACCOUNT_TIER_FI_SALARY_BASIC":          40,
		"ACQUISITION_REFERRAL":                  27,
		"FULL_SALARY_ACCOUNT_OFFER":             28,
		"SALARY_LITE_ACCOUNT_OFFER":             29,
		"AA_SALARY_ACCOUNT_OFFER":               38,
		"ACCOUNT_TYPE_FI_LITE":                  31,
		"TIMESPRIME_REFERRAL":                   32,
		"TIMESPOINT_REFERRAL":                   33,
		"SALARY_PROGRAM_HEALTH_INSURANCE_OFFER": 41,
	}
)

func (x RewardOfferTag) Enum() *RewardOfferTag {
	p := new(RewardOfferTag)
	*p = x
	return p
}

func (x RewardOfferTag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardOfferTag) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[4].Descriptor()
}

func (RewardOfferTag) Type() protoreflect.EnumType {
	return &file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[4]
}

func (x RewardOfferTag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardOfferTag.Descriptor instead.
func (RewardOfferTag) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{4}
}

type RewardClass int32

const (
	RewardClass_REWARD_CLASS_UNSPECIFIED  RewardClass = 0
	RewardClass_REWARD_CLASS_ANCHOR       RewardClass = 1
	RewardClass_REWARD_CLASS_BRAND        RewardClass = 2
	RewardClass_REWARD_CLASS_GROWTH       RewardClass = 3
	RewardClass_REWARD_CLASS_EXPERIMENTAL RewardClass = 4
	RewardClass_REWARD_CLASS_POWER_UP     RewardClass = 5
	RewardClass_REWARD_CLASS_REFUNDS      RewardClass = 6
	RewardClass_REWARD_CLASS_TESTING      RewardClass = 7
)

// Enum value maps for RewardClass.
var (
	RewardClass_name = map[int32]string{
		0: "REWARD_CLASS_UNSPECIFIED",
		1: "REWARD_CLASS_ANCHOR",
		2: "REWARD_CLASS_BRAND",
		3: "REWARD_CLASS_GROWTH",
		4: "REWARD_CLASS_EXPERIMENTAL",
		5: "REWARD_CLASS_POWER_UP",
		6: "REWARD_CLASS_REFUNDS",
		7: "REWARD_CLASS_TESTING",
	}
	RewardClass_value = map[string]int32{
		"REWARD_CLASS_UNSPECIFIED":  0,
		"REWARD_CLASS_ANCHOR":       1,
		"REWARD_CLASS_BRAND":        2,
		"REWARD_CLASS_GROWTH":       3,
		"REWARD_CLASS_EXPERIMENTAL": 4,
		"REWARD_CLASS_POWER_UP":     5,
		"REWARD_CLASS_REFUNDS":      6,
		"REWARD_CLASS_TESTING":      7,
	}
)

func (x RewardClass) Enum() *RewardClass {
	p := new(RewardClass)
	*p = x
	return p
}

func (x RewardClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardClass) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[5].Descriptor()
}

func (RewardClass) Type() protoreflect.EnumType {
	return &file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[5]
}

func (x RewardClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardClass.Descriptor instead.
func (RewardClass) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{5}
}

type RewardPurpose int32

const (
	RewardPurpose_REWARD_PURPOSE_UNSPECIFIED   RewardPurpose = 0
	RewardPurpose_REWARD_PURPOSE_ACQUISITION   RewardPurpose = 1
	RewardPurpose_REWARD_PURPOSE_ACTIVATION    RewardPurpose = 2
	RewardPurpose_REWARD_PURPOSE_RETENTION     RewardPurpose = 3
	RewardPurpose_REWARD_PURPOSE_ENGAGEMENT    RewardPurpose = 4
	RewardPurpose_REWARD_PURPOSE_RE_ACTIVATION RewardPurpose = 5
)

// Enum value maps for RewardPurpose.
var (
	RewardPurpose_name = map[int32]string{
		0: "REWARD_PURPOSE_UNSPECIFIED",
		1: "REWARD_PURPOSE_ACQUISITION",
		2: "REWARD_PURPOSE_ACTIVATION",
		3: "REWARD_PURPOSE_RETENTION",
		4: "REWARD_PURPOSE_ENGAGEMENT",
		5: "REWARD_PURPOSE_RE_ACTIVATION",
	}
	RewardPurpose_value = map[string]int32{
		"REWARD_PURPOSE_UNSPECIFIED":   0,
		"REWARD_PURPOSE_ACQUISITION":   1,
		"REWARD_PURPOSE_ACTIVATION":    2,
		"REWARD_PURPOSE_RETENTION":     3,
		"REWARD_PURPOSE_ENGAGEMENT":    4,
		"REWARD_PURPOSE_RE_ACTIVATION": 5,
	}
)

func (x RewardPurpose) Enum() *RewardPurpose {
	p := new(RewardPurpose)
	*p = x
	return p
}

func (x RewardPurpose) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardPurpose) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[6].Descriptor()
}

func (RewardPurpose) Type() protoreflect.EnumType {
	return &file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[6]
}

func (x RewardPurpose) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardPurpose.Descriptor instead.
func (RewardPurpose) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{6}
}

type RewardProduct int32

const (
	RewardProduct_REWARD_PRODUCT_UNSPECIFIED        RewardProduct = 0
	RewardProduct_REWARD_PRODUCT_ACQUISITION        RewardProduct = 1
	RewardProduct_REWARD_PRODUCT_REFERRAL           RewardProduct = 2
	RewardProduct_REWARD_PRODUCT_ADD_FUNDS          RewardProduct = 3
	RewardProduct_REWARD_PRODUCT_ONBOARDING         RewardProduct = 4
	RewardProduct_REWARD_PRODUCT_RE_ONBOARDING      RewardProduct = 5
	RewardProduct_REWARD_PRODUCT_VKYC               RewardProduct = 6
	RewardProduct_REWARD_PRODUCT_FI_LITE            RewardProduct = 7
	RewardProduct_REWARD_PRODUCT_ASK_FI             RewardProduct = 8
	RewardProduct_REWARD_PRODUCT_ANALYZER           RewardProduct = 9
	RewardProduct_REWARD_PRODUCT_INSIGHTS           RewardProduct = 10
	RewardProduct_REWARD_PRODUCT_DEBIT_CARD         RewardProduct = 11
	RewardProduct_REWARD_PRODUCT_CREDIT_CARD        RewardProduct = 12
	RewardProduct_REWARD_PRODUCT_PAY                RewardProduct = 13
	RewardProduct_REWARD_PRODUCT_BILL_PAY           RewardProduct = 14
	RewardProduct_REWARD_PRODUCT_PERSONAL_LOANS     RewardProduct = 15
	RewardProduct_REWARD_PRODUCT_LAMF               RewardProduct = 16
	RewardProduct_REWARD_PRODUCT_TIERING            RewardProduct = 17
	RewardProduct_REWARD_PRODUCT_CONNECTED_ACCOUNTS RewardProduct = 18
	RewardProduct_REWARD_PRODUCT_CX                 RewardProduct = 19
	RewardProduct_REWARD_PRODUCT_US_STOCK           RewardProduct = 20
	RewardProduct_REWARD_PRODUCT_JUMP               RewardProduct = 21
	RewardProduct_REWARD_PRODUCT_FIT                RewardProduct = 22
	RewardProduct_REWARD_PRODUCT_MF                 RewardProduct = 23
	RewardProduct_REWARD_PRODUCT_FD_SD              RewardProduct = 24
	RewardProduct_REWARD_PRODUCT_SALARY_B2B         RewardProduct = 25
	RewardProduct_REWARD_PRODUCT_SALARY_B2C         RewardProduct = 26
	RewardProduct_REWARD_PRODUCT_REWARDS            RewardProduct = 27
	RewardProduct_REWARD_PRODUCT_HR                 RewardProduct = 28
)

// Enum value maps for RewardProduct.
var (
	RewardProduct_name = map[int32]string{
		0:  "REWARD_PRODUCT_UNSPECIFIED",
		1:  "REWARD_PRODUCT_ACQUISITION",
		2:  "REWARD_PRODUCT_REFERRAL",
		3:  "REWARD_PRODUCT_ADD_FUNDS",
		4:  "REWARD_PRODUCT_ONBOARDING",
		5:  "REWARD_PRODUCT_RE_ONBOARDING",
		6:  "REWARD_PRODUCT_VKYC",
		7:  "REWARD_PRODUCT_FI_LITE",
		8:  "REWARD_PRODUCT_ASK_FI",
		9:  "REWARD_PRODUCT_ANALYZER",
		10: "REWARD_PRODUCT_INSIGHTS",
		11: "REWARD_PRODUCT_DEBIT_CARD",
		12: "REWARD_PRODUCT_CREDIT_CARD",
		13: "REWARD_PRODUCT_PAY",
		14: "REWARD_PRODUCT_BILL_PAY",
		15: "REWARD_PRODUCT_PERSONAL_LOANS",
		16: "REWARD_PRODUCT_LAMF",
		17: "REWARD_PRODUCT_TIERING",
		18: "REWARD_PRODUCT_CONNECTED_ACCOUNTS",
		19: "REWARD_PRODUCT_CX",
		20: "REWARD_PRODUCT_US_STOCK",
		21: "REWARD_PRODUCT_JUMP",
		22: "REWARD_PRODUCT_FIT",
		23: "REWARD_PRODUCT_MF",
		24: "REWARD_PRODUCT_FD_SD",
		25: "REWARD_PRODUCT_SALARY_B2B",
		26: "REWARD_PRODUCT_SALARY_B2C",
		27: "REWARD_PRODUCT_REWARDS",
		28: "REWARD_PRODUCT_HR",
	}
	RewardProduct_value = map[string]int32{
		"REWARD_PRODUCT_UNSPECIFIED":        0,
		"REWARD_PRODUCT_ACQUISITION":        1,
		"REWARD_PRODUCT_REFERRAL":           2,
		"REWARD_PRODUCT_ADD_FUNDS":          3,
		"REWARD_PRODUCT_ONBOARDING":         4,
		"REWARD_PRODUCT_RE_ONBOARDING":      5,
		"REWARD_PRODUCT_VKYC":               6,
		"REWARD_PRODUCT_FI_LITE":            7,
		"REWARD_PRODUCT_ASK_FI":             8,
		"REWARD_PRODUCT_ANALYZER":           9,
		"REWARD_PRODUCT_INSIGHTS":           10,
		"REWARD_PRODUCT_DEBIT_CARD":         11,
		"REWARD_PRODUCT_CREDIT_CARD":        12,
		"REWARD_PRODUCT_PAY":                13,
		"REWARD_PRODUCT_BILL_PAY":           14,
		"REWARD_PRODUCT_PERSONAL_LOANS":     15,
		"REWARD_PRODUCT_LAMF":               16,
		"REWARD_PRODUCT_TIERING":            17,
		"REWARD_PRODUCT_CONNECTED_ACCOUNTS": 18,
		"REWARD_PRODUCT_CX":                 19,
		"REWARD_PRODUCT_US_STOCK":           20,
		"REWARD_PRODUCT_JUMP":               21,
		"REWARD_PRODUCT_FIT":                22,
		"REWARD_PRODUCT_MF":                 23,
		"REWARD_PRODUCT_FD_SD":              24,
		"REWARD_PRODUCT_SALARY_B2B":         25,
		"REWARD_PRODUCT_SALARY_B2C":         26,
		"REWARD_PRODUCT_REWARDS":            27,
		"REWARD_PRODUCT_HR":                 28,
	}
)

func (x RewardProduct) Enum() *RewardProduct {
	p := new(RewardProduct)
	*p = x
	return p
}

func (x RewardProduct) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardProduct) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[7].Descriptor()
}

func (RewardProduct) Type() protoreflect.EnumType {
	return &file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[7]
}

func (x RewardProduct) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardProduct.Descriptor instead.
func (RewardProduct) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{7}
}

// used to denote what will be generated by a reward offer
type GenerationType int32

const (
	GenerationType_GENERATION_TYPE_UNSPECIFIED GenerationType = 0
	// a reward will be generated
	GenerationType_GENERATION_TYPE_REWARD GenerationType = 1
	// a projection of reward (that can only be used for display purpose on its own) will be generated
	// ** NOTE **: projections require a reward generating reward offer that generates reward using a projections
	GenerationType_GENERATION_TYPE_PROJECTION GenerationType = 2
)

// Enum value maps for GenerationType.
var (
	GenerationType_name = map[int32]string{
		0: "GENERATION_TYPE_UNSPECIFIED",
		1: "GENERATION_TYPE_REWARD",
		2: "GENERATION_TYPE_PROJECTION",
	}
	GenerationType_value = map[string]int32{
		"GENERATION_TYPE_UNSPECIFIED": 0,
		"GENERATION_TYPE_REWARD":      1,
		"GENERATION_TYPE_PROJECTION":  2,
	}
)

func (x GenerationType) Enum() *GenerationType {
	p := new(GenerationType)
	*p = x
	return p
}

func (x GenerationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GenerationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[8].Descriptor()
}

func (GenerationType) Type() protoreflect.EnumType {
	return &file_api_rewards_rewardoffers_reward_offer_proto_enumTypes[8]
}

func (x GenerationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GenerationType.Descriptor instead.
func (GenerationType) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{8}
}

type ReviewRewardOfferRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardOfferId string `protobuf:"bytes,1,opt,name=reward_offer_id,json=rewardOfferId,proto3" json:"reward_offer_id,omitempty"`
}

func (x *ReviewRewardOfferRequest) Reset() {
	*x = ReviewRewardOfferRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewRewardOfferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRewardOfferRequest) ProtoMessage() {}

func (x *ReviewRewardOfferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRewardOfferRequest.ProtoReflect.Descriptor instead.
func (*ReviewRewardOfferRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{0}
}

func (x *ReviewRewardOfferRequest) GetRewardOfferId() string {
	if x != nil {
		return x.RewardOfferId
	}
	return ""
}

type ReviewRewardOfferResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status    *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Feedbacks []string    `protobuf:"bytes,2,rep,name=feedbacks,proto3" json:"feedbacks,omitempty"`
}

func (x *ReviewRewardOfferResponse) Reset() {
	*x = ReviewRewardOfferResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewRewardOfferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRewardOfferResponse) ProtoMessage() {}

func (x *ReviewRewardOfferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRewardOfferResponse.ProtoReflect.Descriptor instead.
func (*ReviewRewardOfferResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{1}
}

func (x *ReviewRewardOfferResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ReviewRewardOfferResponse) GetFeedbacks() []string {
	if x != nil {
		return x.Feedbacks
	}
	return nil
}

// proto for cash reward config. will be used to create and fetch the configs
// ex: start: 0, end: 10, percentage: 50% it means 50% of the rewards should be given between 0-10 inclusive
type RangeProbabilityConfigUnit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start      uint32  `protobuf:"varint,1,opt,name=start,proto3" json:"start,omitempty"`
	End        uint32  `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
	Percentage float32 `protobuf:"fixed32,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *RangeProbabilityConfigUnit) Reset() {
	*x = RangeProbabilityConfigUnit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RangeProbabilityConfigUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RangeProbabilityConfigUnit) ProtoMessage() {}

func (x *RangeProbabilityConfigUnit) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RangeProbabilityConfigUnit.ProtoReflect.Descriptor instead.
func (*RangeProbabilityConfigUnit) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{2}
}

func (x *RangeProbabilityConfigUnit) GetStart() uint32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *RangeProbabilityConfigUnit) GetEnd() uint32 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *RangeProbabilityConfigUnit) GetPercentage() float32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

// represents one entry of the config which contains multiple entries of RangeProbabilityConfigUnit
// for max cash reward for 120 the config may seem like this:
// ex: [(start: 0, end: 10, percentage: 40), (start: 10, end: 20, percentage: 30), (start: 20, end: 50, percentage: 10),
// (start: 50, end: 80, percentage: 10), (start: 80, end: 110, percentage: 5), (start: 110, end: 120, percentage: 5)]
// this will be calculated with provided probability
// if probability is 1 every time reward will be given
type RangeProbabilityConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfigUnits []*RangeProbabilityConfigUnit `protobuf:"bytes,1,rep,name=config_units,json=configUnits,proto3" json:"config_units,omitempty"`
	// TODO(1160): Move probability data type from float to int
	Probability float32 `protobuf:"fixed32,2,opt,name=probability,proto3" json:"probability,omitempty"`
}

func (x *RangeProbabilityConfig) Reset() {
	*x = RangeProbabilityConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RangeProbabilityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RangeProbabilityConfig) ProtoMessage() {}

func (x *RangeProbabilityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RangeProbabilityConfig.ProtoReflect.Descriptor instead.
func (*RangeProbabilityConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{3}
}

func (x *RangeProbabilityConfig) GetConfigUnits() []*RangeProbabilityConfigUnit {
	if x != nil {
		return x.ConfigUnits
	}
	return nil
}

func (x *RangeProbabilityConfig) GetProbability() float32 {
	if x != nil {
		return x.Probability
	}
	return 0
}

// this config will provide fix reward with probability
// if probability is 1 every time reward will be given
type FixedProbabilityConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value uint32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	// TODO(1160): Move probability data type from float to int
	Probability float32 `protobuf:"fixed32,2,opt,name=probability,proto3" json:"probability,omitempty"`
}

func (x *FixedProbabilityConfig) Reset() {
	*x = FixedProbabilityConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FixedProbabilityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FixedProbabilityConfig) ProtoMessage() {}

func (x *FixedProbabilityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FixedProbabilityConfig.ProtoReflect.Descriptor instead.
func (*FixedProbabilityConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{4}
}

func (x *FixedProbabilityConfig) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *FixedProbabilityConfig) GetProbability() float32 {
	if x != nil {
		return x.Probability
	}
	return 0
}

// this config will provide expression bases reward value ex: (TXN_AMOUNT * 0.15)
// if probability is 1 every time reward will be given
type ExpressionProbabilityConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Expression string `protobuf:"bytes,1,opt,name=expression,proto3" json:"expression,omitempty"`
	// TODO(1160): Move probability data type from float to int
	Probability float32 `protobuf:"fixed32,2,opt,name=probability,proto3" json:"probability,omitempty"`
}

func (x *ExpressionProbabilityConfig) Reset() {
	*x = ExpressionProbabilityConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpressionProbabilityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpressionProbabilityConfig) ProtoMessage() {}

func (x *ExpressionProbabilityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpressionProbabilityConfig.ProtoReflect.Descriptor instead.
func (*ExpressionProbabilityConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{5}
}

func (x *ExpressionProbabilityConfig) GetExpression() string {
	if x != nil {
		return x.Expression
	}
	return ""
}

func (x *ExpressionProbabilityConfig) GetProbability() float32 {
	if x != nil {
		return x.Probability
	}
	return 0
}

// this config will provide fixed multiplier to the reward units calculated from units_config.
// it can be used for setting up 2x, 5x type reward offers.
type FixedMultiplierConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Multiplier uint32 `protobuf:"varint,1,opt,name=multiplier,proto3" json:"multiplier,omitempty"`
}

func (x *FixedMultiplierConfig) Reset() {
	*x = FixedMultiplierConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FixedMultiplierConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FixedMultiplierConfig) ProtoMessage() {}

func (x *FixedMultiplierConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FixedMultiplierConfig.ProtoReflect.Descriptor instead.
func (*FixedMultiplierConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{6}
}

func (x *FixedMultiplierConfig) GetMultiplier() uint32 {
	if x != nil {
		return x.Multiplier
	}
	return 0
}

// config to calculate reward unit using an expression and probability ranges.
// It contains an expression that is used to calculate a seed unit value and then
// percentages of this seed unit value are used to create ranges for calculating the final reward unit.
// Eg: To give a reward amount based on TXN_AMOUNT such that amount is within range [0, 20 % of TXN_AMOUNT] with
// 50% probability, [20 % of TXN_AMOUNT, 40 % of TXN_AMOUNT] with 30% probability and  [40 % of TXN_AMOUNT, 80 % of TXN_AMOUNT]
// with 20% probability then the message ExpressionRangeProbabilityConfig would look like
// *****
// expression : 'TXN_AMOUNT'
// range_probability_units : [(unit_percentage_start: 0, unit_percentage_end: 20, probability_percent: 50), (unit_percentage_start: 20, unit_percentage_end: 40, probability_percent: 30), (unit_percentage_start: 40, unit_percentage_end: 80, probability_percent: 20)]
// ******
type ExpressionRangeProbabilityConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// expression to calculate a seed unit value (for use in creating ranges for RangeProbabilityConfigUnit).
	Expression            string                                                         `protobuf:"bytes,1,opt,name=expression,proto3" json:"expression,omitempty"`
	RangeProbabilityUnits []*ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit `protobuf:"bytes,2,rep,name=range_probability_units,json=rangeProbabilityUnits,proto3" json:"range_probability_units,omitempty"`
	// upper limit at which the units calculated using expression + range_probability_units should be capped to.
	UpperLimit int32 `protobuf:"varint,3,opt,name=upper_limit,json=upperLimit,proto3" json:"upper_limit,omitempty"`
	// lower limit to add a lower bound on the final units calculated using expression + range_probability_units
	// i.e if the units computed using expression + range_probability_units results in value lesser than lower_limit
	// then lower_limit value is used.
	LowerLimit int32 `protobuf:"varint,4,opt,name=lower_limit,json=lowerLimit,proto3" json:"lower_limit,omitempty"`
}

func (x *ExpressionRangeProbabilityConfig) Reset() {
	*x = ExpressionRangeProbabilityConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpressionRangeProbabilityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpressionRangeProbabilityConfig) ProtoMessage() {}

func (x *ExpressionRangeProbabilityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpressionRangeProbabilityConfig.ProtoReflect.Descriptor instead.
func (*ExpressionRangeProbabilityConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{7}
}

func (x *ExpressionRangeProbabilityConfig) GetExpression() string {
	if x != nil {
		return x.Expression
	}
	return ""
}

func (x *ExpressionRangeProbabilityConfig) GetRangeProbabilityUnits() []*ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit {
	if x != nil {
		return x.RangeProbabilityUnits
	}
	return nil
}

func (x *ExpressionRangeProbabilityConfig) GetUpperLimit() int32 {
	if x != nil {
		return x.UpperLimit
	}
	return 0
}

func (x *ExpressionRangeProbabilityConfig) GetLowerLimit() int32 {
	if x != nil {
		return x.LowerLimit
	}
	return 0
}

// config is used to calculate reward units using conditions and corresponding expression range probability distribution.
// It contains a list of conditions with an expression range probability distribution linked to each of those conditions.
// If a condition evaluates to true then corresponding expression range probability distribution is used to calculate reward units.
// **Note**: If multiple conditions are satisfied then distribution config for any one of them is used to calculate reward units.
type ConditionalExpressionRangeProbabilityConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfigUnits []*ConditionalExpressionRangeProbabilityConfig_ConfigUnit `protobuf:"bytes,1,rep,name=config_units,json=configUnits,proto3" json:"config_units,omitempty"`
}

func (x *ConditionalExpressionRangeProbabilityConfig) Reset() {
	*x = ConditionalExpressionRangeProbabilityConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConditionalExpressionRangeProbabilityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionalExpressionRangeProbabilityConfig) ProtoMessage() {}

func (x *ConditionalExpressionRangeProbabilityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionalExpressionRangeProbabilityConfig.ProtoReflect.Descriptor instead.
func (*ConditionalExpressionRangeProbabilityConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{8}
}

func (x *ConditionalExpressionRangeProbabilityConfig) GetConfigUnits() []*ConditionalExpressionRangeProbabilityConfig_ConfigUnit {
	if x != nil {
		return x.ConfigUnits
	}
	return nil
}

// config is used to calculate rewards units using condition expression and corresponding units_config.
// It contains a list of conditions with config linked to each of those conditions. If a condition evaluates to true,
// then corresponding config is used to calculated reward units.
// Once these reward units are calculated, they will be mathematically operated upon with an external value, i.e. 'PREVIOUS_VALUE'
// to come up with the final reward-units.
type ConditionalExpressionBoosterConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfigUnits []*ConditionalExpressionBoosterConfig_ConfigUnit `protobuf:"bytes,1,rep,name=config_units,json=configUnits,proto3" json:"config_units,omitempty"`
}

func (x *ConditionalExpressionBoosterConfig) Reset() {
	*x = ConditionalExpressionBoosterConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConditionalExpressionBoosterConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionalExpressionBoosterConfig) ProtoMessage() {}

func (x *ConditionalExpressionBoosterConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionalExpressionBoosterConfig.ProtoReflect.Descriptor instead.
func (*ConditionalExpressionBoosterConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{9}
}

func (x *ConditionalExpressionBoosterConfig) GetConfigUnits() []*ConditionalExpressionBoosterConfig_ConfigUnit {
	if x != nil {
		return x.ConfigUnits
	}
	return nil
}

// this config will provide reward value using reward value of some other reward option as seed.
// Different types of derivation are supported in this config.
// todo (utkarsh) : should we keep across reward options unit config separate, should unit config know about reward option entity ?
type DeriveFromSeedRewardOptionConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// index (1 based indexing) of reward option whose reward value should be used as a seed for this config.
	// index of seed_reward_option should be less than index of reward option for which this unit config
	// is used.
	SeedRewardOptionIndex int32 `protobuf:"varint,1,opt,name=seed_reward_option_index,json=seedRewardOptionIndex,proto3" json:"seed_reward_option_index,omitempty"`
	// Types that are assignable to DerivationConfig:
	//
	//	*DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig_
	//	*DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig_
	DerivationConfig isDeriveFromSeedRewardOptionConfig_DerivationConfig `protobuf_oneof:"derivation_config"`
}

func (x *DeriveFromSeedRewardOptionConfig) Reset() {
	*x = DeriveFromSeedRewardOptionConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeriveFromSeedRewardOptionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeriveFromSeedRewardOptionConfig) ProtoMessage() {}

func (x *DeriveFromSeedRewardOptionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeriveFromSeedRewardOptionConfig.ProtoReflect.Descriptor instead.
func (*DeriveFromSeedRewardOptionConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{10}
}

func (x *DeriveFromSeedRewardOptionConfig) GetSeedRewardOptionIndex() int32 {
	if x != nil {
		return x.SeedRewardOptionIndex
	}
	return 0
}

func (m *DeriveFromSeedRewardOptionConfig) GetDerivationConfig() isDeriveFromSeedRewardOptionConfig_DerivationConfig {
	if m != nil {
		return m.DerivationConfig
	}
	return nil
}

func (x *DeriveFromSeedRewardOptionConfig) GetDeltaDerivationConfig() *DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig {
	if x, ok := x.GetDerivationConfig().(*DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig_); ok {
		return x.DeltaDerivationConfig
	}
	return nil
}

func (x *DeriveFromSeedRewardOptionConfig) GetMultiplierDerivationConfig() *DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig {
	if x, ok := x.GetDerivationConfig().(*DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig_); ok {
		return x.MultiplierDerivationConfig
	}
	return nil
}

type isDeriveFromSeedRewardOptionConfig_DerivationConfig interface {
	isDeriveFromSeedRewardOptionConfig_DerivationConfig()
}

type DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig_ struct {
	DeltaDerivationConfig *DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig `protobuf:"bytes,2,opt,name=delta_derivation_config,json=deltaDerivationConfig,proto3,oneof"`
}

type DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig_ struct {
	MultiplierDerivationConfig *DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig `protobuf:"bytes,3,opt,name=multiplier_derivation_config,json=multiplierDerivationConfig,proto3,oneof"`
}

func (*DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig_) isDeriveFromSeedRewardOptionConfig_DerivationConfig() {
}

func (*DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig_) isDeriveFromSeedRewardOptionConfig_DerivationConfig() {
}

// config option will contain reward type and one of the unit config types
type RewardConfigOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardType                 rewards.RewardType          `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	DisplayConfig              *RewardConfigOption_Display `protobuf:"bytes,2,opt,name=display_config,json=displayConfig,proto3" json:"display_config,omitempty"`
	RewardProcessingTimeConfig *rewards.RewardTimeConfig   `protobuf:"bytes,3,opt,name=reward_processing_time_config,json=rewardProcessingTimeConfig,proto3" json:"reward_processing_time_config,omitempty"`
	// intentional field number gap to accommodate new fields in future before units_config
	//
	// Types that are assignable to UnitsConfig:
	//
	//	*RewardConfigOption_ExpressionProbabilityConfig
	//	*RewardConfigOption_FixedProbabilityConfig
	//	*RewardConfigOption_RangeProbabilityConfig
	//	*RewardConfigOption_ExpressionRangeProbabilityConfig
	//	*RewardConfigOption_DeriveFromSeedRewardOptionConfig
	//	*RewardConfigOption_ConditionalExpRangeProbabilityConfig
	UnitsConfig isRewardConfigOption_UnitsConfig `protobuf_oneof:"units_config"`
	// reward config contains reward specific configuration
	// useful for generating reward option
	//
	// Types that are assignable to RewardConfig:
	//
	//	*RewardConfigOption_LuckyDrawConfig
	//	*RewardConfigOption_SmartDepositConfig
	//	*RewardConfigOption_GiftHamperConfig
	//	*RewardConfigOption_CashConfig
	//	*RewardConfigOption_MetalCreditCardConfig
	//	*RewardConfigOption_EgvBasketConfig
	//	*RewardConfigOption_ThriweBenefitsPackageConfig
	//	*RewardConfigOption_UsstockRewardConfig
	//	*RewardConfigOption_FiCoinsRewardConfig
	RewardConfig isRewardConfigOption_RewardConfig `protobuf_oneof:"reward_config"`
	// constraint_expression denotes a constraint which is evaluated while generation reward options for a reward.
	// If the constraint evaluates to true, then only the current option config would be used for generating a reward option,
	// otherwise the option config won't be used for generating a reward option.
	// If constraint_expression field is empty, then it implies no constraint check is required i.e current option config would
	// always be used for generating a reward option.
	ConstraintExpression string `protobuf:"bytes,25,opt,name=constraint_expression,json=constraintExpression,proto3" json:"constraint_expression,omitempty"`
	// multiplier config can be used on top of the units_config layer for multiplying the generated reward units by
	// some factor
	//
	// Types that are assignable to MultiplierConfig:
	//
	//	*RewardConfigOption_FixedMultiplierConfig
	MultiplierConfig isRewardConfigOption_MultiplierConfig `protobuf_oneof:"multiplier_config"`
	// BoostersConfig holds the config for each booster that will be applied post the base reward value is calculated
	// via units_config oneof
	BoostersConfig *BoostersConfig `protobuf:"bytes,27,opt,name=boosters_config,json=boostersConfig,proto3" json:"boosters_config,omitempty"`
	// current usage includes specific handling of display details of reward option. it's an optional field and can be used for the following reward types if we want to distinguish between options:
	// 1. gift_hamper
	// 2. egv_basket
	ProductSku string `protobuf:"bytes,28,opt,name=product_sku,json=productSku,proto3" json:"product_sku,omitempty"`
	// flag to decide if projections are to be used for computing the reward units for the reward
	// ** NOTE ** reward units calculation using projections isn't a part of `units_config` as there are other steps
	// involved after projections are generated (like updating projections with actual values generated against them)
	// whereas the `units_config` is only concerned with calculation of reward units
	ShouldGenerateFromProjections bool `protobuf:"varint,29,opt,name=should_generate_from_projections,json=shouldGenerateFromProjections,proto3" json:"should_generate_from_projections,omitempty"`
	// filters to apply while fetching reward projections. these filters will apply on top of the action type/offer type
	// specific logic that's going to be used to fetch projections. won't be evaluated if `should_generate_from_projections`
	// is false.
	ProjectionsQueryFilters *RewardConfigOption_ProjectionsQueryFilters `protobuf:"bytes,30,opt,name=projections_query_filters,json=projectionsQueryFilters,proto3" json:"projections_query_filters,omitempty"`
	// upper and lower limit of generated reward units
	// todo(divyadeep): deprecate lower/upper limit support from within ExpressionRangeProbabilityConfig and use this
	// lower limit to add a lower bound on the final calculated units
	// i.e if the units computed result in value lesser than lower_limit
	// then lower_limit value is used.
	RewardUnitsLowerLimit uint32 `protobuf:"varint,31,opt,name=reward_units_lower_limit,json=rewardUnitsLowerLimit,proto3" json:"reward_units_lower_limit,omitempty"`
	// upper limit at which the calculated units should be capped to.
	RewardUnitsUpperLimit uint32 `protobuf:"varint,32,opt,name=reward_units_upper_limit,json=rewardUnitsUpperLimit,proto3" json:"reward_units_upper_limit,omitempty"`
	// stores the different configured unit caps on reward units generated from projections.
	RewardUnitsGeneratedFromProjectionUnitCaps *RewardUnitsGeneratedFromProjectionUnitCaps `protobuf:"bytes,34,opt,name=reward_units_generated_from_projection_unit_caps,json=rewardUnitsGeneratedFromProjectionUnitCaps,proto3" json:"reward_units_generated_from_projection_unit_caps,omitempty"`
}

func (x *RewardConfigOption) Reset() {
	*x = RewardConfigOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardConfigOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardConfigOption) ProtoMessage() {}

func (x *RewardConfigOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardConfigOption.ProtoReflect.Descriptor instead.
func (*RewardConfigOption) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{11}
}

func (x *RewardConfigOption) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

func (x *RewardConfigOption) GetDisplayConfig() *RewardConfigOption_Display {
	if x != nil {
		return x.DisplayConfig
	}
	return nil
}

func (x *RewardConfigOption) GetRewardProcessingTimeConfig() *rewards.RewardTimeConfig {
	if x != nil {
		return x.RewardProcessingTimeConfig
	}
	return nil
}

func (m *RewardConfigOption) GetUnitsConfig() isRewardConfigOption_UnitsConfig {
	if m != nil {
		return m.UnitsConfig
	}
	return nil
}

func (x *RewardConfigOption) GetExpressionProbabilityConfig() *ExpressionProbabilityConfig {
	if x, ok := x.GetUnitsConfig().(*RewardConfigOption_ExpressionProbabilityConfig); ok {
		return x.ExpressionProbabilityConfig
	}
	return nil
}

func (x *RewardConfigOption) GetFixedProbabilityConfig() *FixedProbabilityConfig {
	if x, ok := x.GetUnitsConfig().(*RewardConfigOption_FixedProbabilityConfig); ok {
		return x.FixedProbabilityConfig
	}
	return nil
}

func (x *RewardConfigOption) GetRangeProbabilityConfig() *RangeProbabilityConfig {
	if x, ok := x.GetUnitsConfig().(*RewardConfigOption_RangeProbabilityConfig); ok {
		return x.RangeProbabilityConfig
	}
	return nil
}

func (x *RewardConfigOption) GetExpressionRangeProbabilityConfig() *ExpressionRangeProbabilityConfig {
	if x, ok := x.GetUnitsConfig().(*RewardConfigOption_ExpressionRangeProbabilityConfig); ok {
		return x.ExpressionRangeProbabilityConfig
	}
	return nil
}

func (x *RewardConfigOption) GetDeriveFromSeedRewardOptionConfig() *DeriveFromSeedRewardOptionConfig {
	if x, ok := x.GetUnitsConfig().(*RewardConfigOption_DeriveFromSeedRewardOptionConfig); ok {
		return x.DeriveFromSeedRewardOptionConfig
	}
	return nil
}

func (x *RewardConfigOption) GetConditionalExpRangeProbabilityConfig() *ConditionalExpressionRangeProbabilityConfig {
	if x, ok := x.GetUnitsConfig().(*RewardConfigOption_ConditionalExpRangeProbabilityConfig); ok {
		return x.ConditionalExpRangeProbabilityConfig
	}
	return nil
}

func (m *RewardConfigOption) GetRewardConfig() isRewardConfigOption_RewardConfig {
	if m != nil {
		return m.RewardConfig
	}
	return nil
}

func (x *RewardConfigOption) GetLuckyDrawConfig() *LuckyDrawConfig {
	if x, ok := x.GetRewardConfig().(*RewardConfigOption_LuckyDrawConfig); ok {
		return x.LuckyDrawConfig
	}
	return nil
}

func (x *RewardConfigOption) GetSmartDepositConfig() *SmartDepositConfig {
	if x, ok := x.GetRewardConfig().(*RewardConfigOption_SmartDepositConfig); ok {
		return x.SmartDepositConfig
	}
	return nil
}

func (x *RewardConfigOption) GetGiftHamperConfig() *GiftHamperConfig {
	if x, ok := x.GetRewardConfig().(*RewardConfigOption_GiftHamperConfig); ok {
		return x.GiftHamperConfig
	}
	return nil
}

func (x *RewardConfigOption) GetCashConfig() *CashConfig {
	if x, ok := x.GetRewardConfig().(*RewardConfigOption_CashConfig); ok {
		return x.CashConfig
	}
	return nil
}

func (x *RewardConfigOption) GetMetalCreditCardConfig() *MetalCreditCardConfig {
	if x, ok := x.GetRewardConfig().(*RewardConfigOption_MetalCreditCardConfig); ok {
		return x.MetalCreditCardConfig
	}
	return nil
}

func (x *RewardConfigOption) GetEgvBasketConfig() *EgvBasketConfig {
	if x, ok := x.GetRewardConfig().(*RewardConfigOption_EgvBasketConfig); ok {
		return x.EgvBasketConfig
	}
	return nil
}

func (x *RewardConfigOption) GetThriweBenefitsPackageConfig() *ThriweBenefitsPackageConfig {
	if x, ok := x.GetRewardConfig().(*RewardConfigOption_ThriweBenefitsPackageConfig); ok {
		return x.ThriweBenefitsPackageConfig
	}
	return nil
}

func (x *RewardConfigOption) GetUsstockRewardConfig() *USStockRewardConfig {
	if x, ok := x.GetRewardConfig().(*RewardConfigOption_UsstockRewardConfig); ok {
		return x.UsstockRewardConfig
	}
	return nil
}

func (x *RewardConfigOption) GetFiCoinsRewardConfig() *FiCoinsRewardConfig {
	if x, ok := x.GetRewardConfig().(*RewardConfigOption_FiCoinsRewardConfig); ok {
		return x.FiCoinsRewardConfig
	}
	return nil
}

func (x *RewardConfigOption) GetConstraintExpression() string {
	if x != nil {
		return x.ConstraintExpression
	}
	return ""
}

func (m *RewardConfigOption) GetMultiplierConfig() isRewardConfigOption_MultiplierConfig {
	if m != nil {
		return m.MultiplierConfig
	}
	return nil
}

func (x *RewardConfigOption) GetFixedMultiplierConfig() *FixedMultiplierConfig {
	if x, ok := x.GetMultiplierConfig().(*RewardConfigOption_FixedMultiplierConfig); ok {
		return x.FixedMultiplierConfig
	}
	return nil
}

func (x *RewardConfigOption) GetBoostersConfig() *BoostersConfig {
	if x != nil {
		return x.BoostersConfig
	}
	return nil
}

func (x *RewardConfigOption) GetProductSku() string {
	if x != nil {
		return x.ProductSku
	}
	return ""
}

func (x *RewardConfigOption) GetShouldGenerateFromProjections() bool {
	if x != nil {
		return x.ShouldGenerateFromProjections
	}
	return false
}

func (x *RewardConfigOption) GetProjectionsQueryFilters() *RewardConfigOption_ProjectionsQueryFilters {
	if x != nil {
		return x.ProjectionsQueryFilters
	}
	return nil
}

func (x *RewardConfigOption) GetRewardUnitsLowerLimit() uint32 {
	if x != nil {
		return x.RewardUnitsLowerLimit
	}
	return 0
}

func (x *RewardConfigOption) GetRewardUnitsUpperLimit() uint32 {
	if x != nil {
		return x.RewardUnitsUpperLimit
	}
	return 0
}

func (x *RewardConfigOption) GetRewardUnitsGeneratedFromProjectionUnitCaps() *RewardUnitsGeneratedFromProjectionUnitCaps {
	if x != nil {
		return x.RewardUnitsGeneratedFromProjectionUnitCaps
	}
	return nil
}

type isRewardConfigOption_UnitsConfig interface {
	isRewardConfigOption_UnitsConfig()
}

type RewardConfigOption_ExpressionProbabilityConfig struct {
	ExpressionProbabilityConfig *ExpressionProbabilityConfig `protobuf:"bytes,11,opt,name=expression_probability_config,json=expressionProbabilityConfig,proto3,oneof"`
}

type RewardConfigOption_FixedProbabilityConfig struct {
	FixedProbabilityConfig *FixedProbabilityConfig `protobuf:"bytes,12,opt,name=fixed_probability_config,json=fixedProbabilityConfig,proto3,oneof"`
}

type RewardConfigOption_RangeProbabilityConfig struct {
	RangeProbabilityConfig *RangeProbabilityConfig `protobuf:"bytes,13,opt,name=range_probability_config,json=rangeProbabilityConfig,proto3,oneof"`
}

type RewardConfigOption_ExpressionRangeProbabilityConfig struct {
	ExpressionRangeProbabilityConfig *ExpressionRangeProbabilityConfig `protobuf:"bytes,14,opt,name=expression_range_probability_config,json=expressionRangeProbabilityConfig,proto3,oneof"`
}

type RewardConfigOption_DeriveFromSeedRewardOptionConfig struct {
	DeriveFromSeedRewardOptionConfig *DeriveFromSeedRewardOptionConfig `protobuf:"bytes,15,opt,name=derive_from_seed_reward_option_config,json=deriveFromSeedRewardOptionConfig,proto3,oneof"`
}

type RewardConfigOption_ConditionalExpRangeProbabilityConfig struct {
	ConditionalExpRangeProbabilityConfig *ConditionalExpressionRangeProbabilityConfig `protobuf:"bytes,16,opt,name=conditional_exp_range_probability_config,json=conditionalExpRangeProbabilityConfig,proto3,oneof"`
}

func (*RewardConfigOption_ExpressionProbabilityConfig) isRewardConfigOption_UnitsConfig() {}

func (*RewardConfigOption_FixedProbabilityConfig) isRewardConfigOption_UnitsConfig() {}

func (*RewardConfigOption_RangeProbabilityConfig) isRewardConfigOption_UnitsConfig() {}

func (*RewardConfigOption_ExpressionRangeProbabilityConfig) isRewardConfigOption_UnitsConfig() {}

func (*RewardConfigOption_DeriveFromSeedRewardOptionConfig) isRewardConfigOption_UnitsConfig() {}

func (*RewardConfigOption_ConditionalExpRangeProbabilityConfig) isRewardConfigOption_UnitsConfig() {}

type isRewardConfigOption_RewardConfig interface {
	isRewardConfigOption_RewardConfig()
}

type RewardConfigOption_LuckyDrawConfig struct {
	LuckyDrawConfig *LuckyDrawConfig `protobuf:"bytes,18,opt,name=lucky_draw_config,json=luckyDrawConfig,proto3,oneof"`
}

type RewardConfigOption_SmartDepositConfig struct {
	SmartDepositConfig *SmartDepositConfig `protobuf:"bytes,19,opt,name=smart_deposit_config,json=smartDepositConfig,proto3,oneof"`
}

type RewardConfigOption_GiftHamperConfig struct {
	GiftHamperConfig *GiftHamperConfig `protobuf:"bytes,20,opt,name=gift_hamper_config,json=giftHamperConfig,proto3,oneof"`
}

type RewardConfigOption_CashConfig struct {
	CashConfig *CashConfig `protobuf:"bytes,21,opt,name=cash_config,json=cashConfig,proto3,oneof"`
}

type RewardConfigOption_MetalCreditCardConfig struct {
	MetalCreditCardConfig *MetalCreditCardConfig `protobuf:"bytes,22,opt,name=metal_credit_card_config,json=metalCreditCardConfig,proto3,oneof"`
}

type RewardConfigOption_EgvBasketConfig struct {
	EgvBasketConfig *EgvBasketConfig `protobuf:"bytes,23,opt,name=egv_basket_config,json=egvBasketConfig,proto3,oneof"`
}

type RewardConfigOption_ThriweBenefitsPackageConfig struct {
	ThriweBenefitsPackageConfig *ThriweBenefitsPackageConfig `protobuf:"bytes,24,opt,name=thriwe_benefits_package_config,json=thriweBenefitsPackageConfig,proto3,oneof"`
}

type RewardConfigOption_UsstockRewardConfig struct {
	UsstockRewardConfig *USStockRewardConfig `protobuf:"bytes,33,opt,name=usstock_reward_config,json=usstockRewardConfig,proto3,oneof"`
}

type RewardConfigOption_FiCoinsRewardConfig struct {
	FiCoinsRewardConfig *FiCoinsRewardConfig `protobuf:"bytes,35,opt,name=fi_coins_reward_config,json=fiCoinsRewardConfig,proto3,oneof"`
}

func (*RewardConfigOption_LuckyDrawConfig) isRewardConfigOption_RewardConfig() {}

func (*RewardConfigOption_SmartDepositConfig) isRewardConfigOption_RewardConfig() {}

func (*RewardConfigOption_GiftHamperConfig) isRewardConfigOption_RewardConfig() {}

func (*RewardConfigOption_CashConfig) isRewardConfigOption_RewardConfig() {}

func (*RewardConfigOption_MetalCreditCardConfig) isRewardConfigOption_RewardConfig() {}

func (*RewardConfigOption_EgvBasketConfig) isRewardConfigOption_RewardConfig() {}

func (*RewardConfigOption_ThriweBenefitsPackageConfig) isRewardConfigOption_RewardConfig() {}

func (*RewardConfigOption_UsstockRewardConfig) isRewardConfigOption_RewardConfig() {}

func (*RewardConfigOption_FiCoinsRewardConfig) isRewardConfigOption_RewardConfig() {}

type isRewardConfigOption_MultiplierConfig interface {
	isRewardConfigOption_MultiplierConfig()
}

type RewardConfigOption_FixedMultiplierConfig struct {
	FixedMultiplierConfig *FixedMultiplierConfig `protobuf:"bytes,26,opt,name=fixed_multiplier_config,json=fixedMultiplierConfig,proto3,oneof"`
}

func (*RewardConfigOption_FixedMultiplierConfig) isRewardConfigOption_MultiplierConfig() {}

type BoostersConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boosters to be applied in the given order
	Boosters []*BoosterConfig `protobuf:"bytes,1,rep,name=boosters,proto3" json:"boosters,omitempty"`
	// to decide how the claim flow would reveal the reward value for each booster getting applied
	ClaimFlowRewardValueRevealType rewards.ClaimFlowRewardValueRevealType `protobuf:"varint,2,opt,name=claim_flow_reward_value_reveal_type,json=claimFlowRewardValueRevealType,proto3,enum=rewards.ClaimFlowRewardValueRevealType" json:"claim_flow_reward_value_reveal_type,omitempty"`
}

func (x *BoostersConfig) Reset() {
	*x = BoostersConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoostersConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoostersConfig) ProtoMessage() {}

func (x *BoostersConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoostersConfig.ProtoReflect.Descriptor instead.
func (*BoostersConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{12}
}

func (x *BoostersConfig) GetBoosters() []*BoosterConfig {
	if x != nil {
		return x.Boosters
	}
	return nil
}

func (x *BoostersConfig) GetClaimFlowRewardValueRevealType() rewards.ClaimFlowRewardValueRevealType {
	if x != nil {
		return x.ClaimFlowRewardValueRevealType
	}
	return rewards.ClaimFlowRewardValueRevealType(0)
}

type BoosterConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisplayDetails *BoosterConfig_DisplayDetails `protobuf:"bytes,1,opt,name=display_details,json=displayDetails,proto3" json:"display_details,omitempty"`
	// Types that are assignable to UnitsConfig:
	//
	//	*BoosterConfig_ConditionalExpressionBoosterConfig
	UnitsConfig isBoosterConfig_UnitsConfig `protobuf_oneof:"units_config"`
}

func (x *BoosterConfig) Reset() {
	*x = BoosterConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoosterConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoosterConfig) ProtoMessage() {}

func (x *BoosterConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoosterConfig.ProtoReflect.Descriptor instead.
func (*BoosterConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{13}
}

func (x *BoosterConfig) GetDisplayDetails() *BoosterConfig_DisplayDetails {
	if x != nil {
		return x.DisplayDetails
	}
	return nil
}

func (m *BoosterConfig) GetUnitsConfig() isBoosterConfig_UnitsConfig {
	if m != nil {
		return m.UnitsConfig
	}
	return nil
}

func (x *BoosterConfig) GetConditionalExpressionBoosterConfig() *ConditionalExpressionBoosterConfig {
	if x, ok := x.GetUnitsConfig().(*BoosterConfig_ConditionalExpressionBoosterConfig); ok {
		return x.ConditionalExpressionBoosterConfig
	}
	return nil
}

type isBoosterConfig_UnitsConfig interface {
	isBoosterConfig_UnitsConfig()
}

type BoosterConfig_ConditionalExpressionBoosterConfig struct {
	ConditionalExpressionBoosterConfig *ConditionalExpressionBoosterConfig `protobuf:"bytes,9,opt,name=conditional_expression_booster_config,json=conditionalExpressionBoosterConfig,proto3,oneof"`
}

func (*BoosterConfig_ConditionalExpressionBoosterConfig) isBoosterConfig_UnitsConfig() {}

// LuckyDrawConfig contains all the necessary config
// required for generating lucky draw reward for user.
type LuckyDrawConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LuckyDrawId string `protobuf:"bytes,1,opt,name=lucky_draw_id,json=luckyDrawId,proto3" json:"lucky_draw_id,omitempty"`
}

func (x *LuckyDrawConfig) Reset() {
	*x = LuckyDrawConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LuckyDrawConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuckyDrawConfig) ProtoMessage() {}

func (x *LuckyDrawConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuckyDrawConfig.ProtoReflect.Descriptor instead.
func (*LuckyDrawConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{14}
}

func (x *LuckyDrawConfig) GetLuckyDrawId() string {
	if x != nil {
		return x.LuckyDrawId
	}
	return ""
}

// SmartDepositConfig contains the necessary config
// required for generating the smart deposit reward.
type SmartDepositConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// used to calculate sd reward maturity date
	MaturityDateConfig *rewards.RewardTimeConfig `protobuf:"bytes,1,opt,name=maturity_date_config,json=maturityDateConfig,proto3" json:"maturity_date_config,omitempty"`
}

func (x *SmartDepositConfig) Reset() {
	*x = SmartDepositConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmartDepositConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmartDepositConfig) ProtoMessage() {}

func (x *SmartDepositConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmartDepositConfig.ProtoReflect.Descriptor instead.
func (*SmartDepositConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{15}
}

func (x *SmartDepositConfig) GetMaturityDateConfig() *rewards.RewardTimeConfig {
	if x != nil {
		return x.MaturityDateConfig
	}
	return nil
}

// GiftHamperConfig contains all the necessary config
// required for generating a gift hamper reward.
type GiftHamperConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of vendor who will provide the gift hamper
	VendorName string `protobuf:"bytes,1,opt,name=vendor_name,json=vendorName,proto3" json:"vendor_name,omitempty"`
	// vendor specific product identifier
	VendorProductId string `protobuf:"bytes,2,opt,name=vendor_product_id,json=vendorProductId,proto3" json:"vendor_product_id,omitempty"`
	// name of product like "Monsoon Harvest Hamper"
	ProductName string `protobuf:"bytes,3,opt,name=product_name,json=productName,proto3" json:"product_name,omitempty"`
}

func (x *GiftHamperConfig) Reset() {
	*x = GiftHamperConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftHamperConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftHamperConfig) ProtoMessage() {}

func (x *GiftHamperConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftHamperConfig.ProtoReflect.Descriptor instead.
func (*GiftHamperConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{16}
}

func (x *GiftHamperConfig) GetVendorName() string {
	if x != nil {
		return x.VendorName
	}
	return ""
}

func (x *GiftHamperConfig) GetVendorProductId() string {
	if x != nil {
		return x.VendorProductId
	}
	return ""
}

func (x *GiftHamperConfig) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

// CashConfig contains all the necessary config
// required for generating a cash reward.
type CashConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// expression used for creating txn remark to be used for displaying with cash txn.
	// if txn_remark_expression is not specified a default remark is used.
	// e.g. for bonus payment cash reward it can be something like "Bonus Payment BONUS_INSTALLMENT_COUNTER"
	TxnRemarkExpression string `protobuf:"bytes,1,opt,name=txn_remark_expression,json=txnRemarkExpression,proto3" json:"txn_remark_expression,omitempty"`
}

func (x *CashConfig) Reset() {
	*x = CashConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CashConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CashConfig) ProtoMessage() {}

func (x *CashConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CashConfig.ProtoReflect.Descriptor instead.
func (*CashConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{17}
}

func (x *CashConfig) GetTxnRemarkExpression() string {
	if x != nil {
		return x.TxnRemarkExpression
	}
	return ""
}

// MetalCreditCardConfig contains any configs related to MetalCreditCard reward type
type MetalCreditCardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MetalCreditCardConfig) Reset() {
	*x = MetalCreditCardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetalCreditCardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetalCreditCardConfig) ProtoMessage() {}

func (x *MetalCreditCardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetalCreditCardConfig.ProtoReflect.Descriptor instead.
func (*MetalCreditCardConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{18}
}

// EGV basket config contains the different offerIds (redemption side) that will lead to generation of EGVs for the user
type EgvBasketConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EgvOfferIds []string `protobuf:"bytes,1,rep,name=egv_offer_ids,json=egvOfferIds,proto3" json:"egv_offer_ids,omitempty"`
}

func (x *EgvBasketConfig) Reset() {
	*x = EgvBasketConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EgvBasketConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EgvBasketConfig) ProtoMessage() {}

func (x *EgvBasketConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EgvBasketConfig.ProtoReflect.Descriptor instead.
func (*EgvBasketConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{19}
}

func (x *EgvBasketConfig) GetEgvOfferIds() []string {
	if x != nil {
		return x.EgvOfferIds
	}
	return nil
}

// ThriweBenefitsPackageConfig contains the necessary config required for generating a thriwe benefits package reward.
type ThriweBenefitsPackageConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the id of offer configured in the offer service which would be used for fulfilling the thriwe benefits package reward.
	OfferId string `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
}

func (x *ThriweBenefitsPackageConfig) Reset() {
	*x = ThriweBenefitsPackageConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThriweBenefitsPackageConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThriweBenefitsPackageConfig) ProtoMessage() {}

func (x *ThriweBenefitsPackageConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThriweBenefitsPackageConfig.ProtoReflect.Descriptor instead.
func (*ThriweBenefitsPackageConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{20}
}

func (x *ThriweBenefitsPackageConfig) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

// this will contain all the config needed to calculate the reward
// if probability is 1 every time reward will be given
type RewardMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardAggregates        *RewardAggregates         `protobuf:"bytes,1,opt,name=reward_aggregates,json=rewardAggregates,proto3" json:"reward_aggregates,omitempty"`
	RewardLockTimeConfig    *rewards.RewardTimeConfig `protobuf:"bytes,2,opt,name=reward_lock_time_config,json=rewardLockTimeConfig,proto3" json:"reward_lock_time_config,omitempty"`
	DefaultDecideTimeInSecs uint32                    `protobuf:"varint,3,opt,name=default_decide_time_in_secs,json=defaultDecideTimeInSecs,proto3" json:"default_decide_time_in_secs,omitempty"`
	Probability             float32                   `protobuf:"fixed32,4,opt,name=probability,proto3" json:"probability,omitempty"`
	RewardConfigOptions     []*RewardConfigOption     `protobuf:"bytes,5,rep,name=reward_config_options,json=rewardConfigOptions,proto3" json:"reward_config_options,omitempty"`
	// for visibility in the app
	IsVisible bool `protobuf:"varint,6,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	// flag to decide whether or not reward should be auto processed after generation.
	// default value is false i.e do not auto process, ref : https://developers.google.com/protocol-buffers/docs/proto3#default
	AutoProcessReward bool                          `protobuf:"varint,8,opt,name=auto_process_reward,json=autoProcessReward,proto3" json:"auto_process_reward,omitempty"`
	RewardDisplayMeta *RewardMeta_RewardDisplayMeta `protobuf:"bytes,9,opt,name=reward_display_meta,json=rewardDisplayMeta,proto3" json:"reward_display_meta,omitempty"`
	// contains config for sending reward notifications.
	RewardNotificationConfig *RewardNotificationConfig `protobuf:"bytes,10,opt,name=reward_notification_config,json=rewardNotificationConfig,proto3" json:"reward_notification_config,omitempty"`
	// bool to configure whether reward should be unlocked for MinKYC users or not
	// Deprecated: use `is_implicit_locking_disabled` instead.
	//
	// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
	IsUnlockedForMinKyc bool `protobuf:"varint,11,opt,name=is_unlocked_for_min_kyc,json=isUnlockedForMinKyc,proto3" json:"is_unlocked_for_min_kyc,omitempty"`
	// config for time after which reward's default option will be auto-claimed. it can be empty if reward is not to be auto-claimed
	AutoClaimTimeConfig *rewards.RewardTimeConfig `protobuf:"bytes,12,opt,name=auto_claim_time_config,json=autoClaimTimeConfig,proto3" json:"auto_claim_time_config,omitempty"`
	// tags to be propagated to the reward.
	RewardTags []rewards.RewardTag `protobuf:"varint,13,rep,packed,name=reward_tags,json=rewardTags,proto3,enum=rewards.RewardTag" json:"reward_tags,omitempty"`
	// bool to configure whether reward should be implicitly locked or not.
	// implicit locking happens as soon as reward is generated, based on the state of the user.
	// for example, if we're implicit locking all rewards for all non 'FullKYC Savings Account' users, the generated reward
	// would get locked unless this flag is set.
	// the name of this field has been kept generic as the condition of implicit locking can change in future.
	// we've used `disabled` in the name of this variable as the default value for any unspecified bool variable is false,
	// and we do want to keep implicit locking enabled for all reward offers by default.
	IsImplicitLockingDisabled bool `protobuf:"varint,14,opt,name=is_implicit_locking_disabled,json=isImplicitLockingDisabled,proto3" json:"is_implicit_locking_disabled,omitempty"`
	// defines the time after which the reward will expire, if not set the reward will not auto expire
	RewardExpiryTimeConfig *rewards.RewardTimeConfig `protobuf:"bytes,15,opt,name=reward_expiry_time_config,json=rewardExpiryTimeConfig,proto3" json:"reward_expiry_time_config,omitempty"`
}

func (x *RewardMeta) Reset() {
	*x = RewardMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardMeta) ProtoMessage() {}

func (x *RewardMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardMeta.ProtoReflect.Descriptor instead.
func (*RewardMeta) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{21}
}

func (x *RewardMeta) GetRewardAggregates() *RewardAggregates {
	if x != nil {
		return x.RewardAggregates
	}
	return nil
}

func (x *RewardMeta) GetRewardLockTimeConfig() *rewards.RewardTimeConfig {
	if x != nil {
		return x.RewardLockTimeConfig
	}
	return nil
}

func (x *RewardMeta) GetDefaultDecideTimeInSecs() uint32 {
	if x != nil {
		return x.DefaultDecideTimeInSecs
	}
	return 0
}

func (x *RewardMeta) GetProbability() float32 {
	if x != nil {
		return x.Probability
	}
	return 0
}

func (x *RewardMeta) GetRewardConfigOptions() []*RewardConfigOption {
	if x != nil {
		return x.RewardConfigOptions
	}
	return nil
}

func (x *RewardMeta) GetIsVisible() bool {
	if x != nil {
		return x.IsVisible
	}
	return false
}

func (x *RewardMeta) GetAutoProcessReward() bool {
	if x != nil {
		return x.AutoProcessReward
	}
	return false
}

func (x *RewardMeta) GetRewardDisplayMeta() *RewardMeta_RewardDisplayMeta {
	if x != nil {
		return x.RewardDisplayMeta
	}
	return nil
}

func (x *RewardMeta) GetRewardNotificationConfig() *RewardNotificationConfig {
	if x != nil {
		return x.RewardNotificationConfig
	}
	return nil
}

// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
func (x *RewardMeta) GetIsUnlockedForMinKyc() bool {
	if x != nil {
		return x.IsUnlockedForMinKyc
	}
	return false
}

func (x *RewardMeta) GetAutoClaimTimeConfig() *rewards.RewardTimeConfig {
	if x != nil {
		return x.AutoClaimTimeConfig
	}
	return nil
}

func (x *RewardMeta) GetRewardTags() []rewards.RewardTag {
	if x != nil {
		return x.RewardTags
	}
	return nil
}

func (x *RewardMeta) GetIsImplicitLockingDisabled() bool {
	if x != nil {
		return x.IsImplicitLockingDisabled
	}
	return false
}

func (x *RewardMeta) GetRewardExpiryTimeConfig() *rewards.RewardTimeConfig {
	if x != nil {
		return x.RewardExpiryTimeConfig
	}
	return nil
}

// this will contain expression to evaluate the constraint ex: (TXN_AMOUNT > 100.50)
type ConstraintsMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Expression string `protobuf:"bytes,1,opt,name=expression,proto3" json:"expression,omitempty"`
}

func (x *ConstraintsMeta) Reset() {
	*x = ConstraintsMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConstraintsMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConstraintsMeta) ProtoMessage() {}

func (x *ConstraintsMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConstraintsMeta.ProtoReflect.Descriptor instead.
func (*ConstraintsMeta) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{22}
}

func (x *ConstraintsMeta) GetExpression() string {
	if x != nil {
		return x.Expression
	}
	return ""
}

// reward aggregates will help to control the rewards per offer at offer lvl as well as user lvl
type RewardAggregates struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserAggregate               uint32                   `protobuf:"varint,1,opt,name=user_aggregate,json=userAggregate,proto3" json:"user_aggregate,omitempty"`
	ActionAggregate             uint32                   `protobuf:"varint,2,opt,name=action_aggregate,json=actionAggregate,proto3" json:"action_aggregate,omitempty"`
	RewardUnitsCapUserAggregate *RewardUnitsCapAggregate `protobuf:"bytes,3,opt,name=reward_units_cap_user_aggregate,json=rewardUnitsCapUserAggregate,proto3" json:"reward_units_cap_user_aggregate,omitempty"`
	// credit card account level monthly capping of 1x rewards. should only be used for CREDIT_CARD_SPENDS_1X_OFFER rewardOffer type
	Cc_1XRewardUnitsCcAccountLevelMonthlyAggregateCap *RewardUnitsCapAggregate `protobuf:"bytes,4,opt,name=cc_1x_reward_units_cc_account_level_monthly_aggregate_cap,json=cc1xRewardUnitsCcAccountLevelMonthlyAggregateCap,proto3" json:"cc_1x_reward_units_cc_account_level_monthly_aggregate_cap,omitempty"`
	// monthly units cap aggregates for reward offer. these will apply on actor level on calendar month basis.
	// can be nil if no monthly cap has to be applied.
	RewardUnitsCapMonthlyUserAggregate *RewardUnitsCapAggregate `protobuf:"bytes,5,opt,name=reward_units_cap_monthly_user_aggregate,json=rewardUnitsCapMonthlyUserAggregate,proto3" json:"reward_units_cap_monthly_user_aggregate,omitempty"`
	// stores the aggregate limits for a user for time periods like a day, week, month etc.
	// these limits are sequential and are applied in the order of increasing time period aggregate like day -> week -> month -> quarter -> year
	// every aggregate must hold true for the user to be eligible for a new reward against this offer.
	UserAggregateInTimePeriod *UserAggregateInTimePeriod `protobuf:"bytes,6,opt,name=user_aggregate_in_time_period,json=userAggregateInTimePeriod,proto3" json:"user_aggregate_in_time_period,omitempty"`
}

func (x *RewardAggregates) Reset() {
	*x = RewardAggregates{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardAggregates) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardAggregates) ProtoMessage() {}

func (x *RewardAggregates) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardAggregates.ProtoReflect.Descriptor instead.
func (*RewardAggregates) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{23}
}

func (x *RewardAggregates) GetUserAggregate() uint32 {
	if x != nil {
		return x.UserAggregate
	}
	return 0
}

func (x *RewardAggregates) GetActionAggregate() uint32 {
	if x != nil {
		return x.ActionAggregate
	}
	return 0
}

func (x *RewardAggregates) GetRewardUnitsCapUserAggregate() *RewardUnitsCapAggregate {
	if x != nil {
		return x.RewardUnitsCapUserAggregate
	}
	return nil
}

func (x *RewardAggregates) GetCc_1XRewardUnitsCcAccountLevelMonthlyAggregateCap() *RewardUnitsCapAggregate {
	if x != nil {
		return x.Cc_1XRewardUnitsCcAccountLevelMonthlyAggregateCap
	}
	return nil
}

func (x *RewardAggregates) GetRewardUnitsCapMonthlyUserAggregate() *RewardUnitsCapAggregate {
	if x != nil {
		return x.RewardUnitsCapMonthlyUserAggregate
	}
	return nil
}

func (x *RewardAggregates) GetUserAggregateInTimePeriod() *UserAggregateInTimePeriod {
	if x != nil {
		return x.UserAggregateInTimePeriod
	}
	return nil
}

// To cap the units given for a reward to the user based on the reward-type, i.e. fi coins, cash, sd etc
type RewardUnitsCapAggregate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UnitsCaps []*RewardUnitsCapAggregate_RewardUnitsCap `protobuf:"bytes,1,rep,name=units_caps,json=unitsCaps,proto3" json:"units_caps,omitempty"`
	// field which tells whether the capping configs should be hard checks or soft checks.
	// hard checks: we strictly cannot exceed the capping values of the units.
	// soft checks: we are fine till the time the utilised units are < caps during new rewards persistence. this could mean
	// that we might exceed the actually allowed capping units ONCE for a user.
	// For e.g., if RewardUnitsCap is {RewardType: FI_COINS, Units: 100}, Current Utilisation: 60 Fi-Coins, New Reward Units to be given: 100, then:
	// In case of soft checks: Since current utilisation is less than the cap, we will give away 100 fi-coins as is without capping it to available units.
	// In case of hard checks: We will cap the units to (Max - Current): 40 fi-coins
	// In both the cases, if we have already utilised the cap values, then no reward (units) will be given out.
	IsHardCheck bool `protobuf:"varint,2,opt,name=is_hard_check,json=isHardCheck,proto3" json:"is_hard_check,omitempty"`
}

func (x *RewardUnitsCapAggregate) Reset() {
	*x = RewardUnitsCapAggregate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardUnitsCapAggregate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardUnitsCapAggregate) ProtoMessage() {}

func (x *RewardUnitsCapAggregate) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardUnitsCapAggregate.ProtoReflect.Descriptor instead.
func (*RewardUnitsCapAggregate) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{24}
}

func (x *RewardUnitsCapAggregate) GetUnitsCaps() []*RewardUnitsCapAggregate_RewardUnitsCap {
	if x != nil {
		return x.UnitsCaps
	}
	return nil
}

func (x *RewardUnitsCapAggregate) GetIsHardCheck() bool {
	if x != nil {
		return x.IsHardCheck
	}
	return false
}

// UserAggregateInTimePeriod stores the aggregate limits for a user for time periods like a day, week, month etc.
// these limits are sequential and are applied in the order of increasing time period aggregate like day -> week -> month -> quarter -> year
// every aggregate must hold true for the user to be eligible for a new reward against this offer/offer-group.
type UserAggregateInTimePeriod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// maximum number of rewards that can be given against this offer/offer-group to a user in a calendar month.
	MonthlyAggregate uint32 `protobuf:"varint,4,opt,name=monthly_aggregate,json=monthlyAggregate,proto3" json:"monthly_aggregate,omitempty"`
	// maximum number of rewards that can be given against this offer/offer-group to a user in a calendar quarter.
	QuarterlyAggregate uint32 `protobuf:"varint,5,opt,name=quarterly_aggregate,json=quarterlyAggregate,proto3" json:"quarterly_aggregate,omitempty"`
}

func (x *UserAggregateInTimePeriod) Reset() {
	*x = UserAggregateInTimePeriod{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAggregateInTimePeriod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAggregateInTimePeriod) ProtoMessage() {}

func (x *UserAggregateInTimePeriod) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAggregateInTimePeriod.ProtoReflect.Descriptor instead.
func (*UserAggregateInTimePeriod) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{25}
}

func (x *UserAggregateInTimePeriod) GetMonthlyAggregate() uint32 {
	if x != nil {
		return x.MonthlyAggregate
	}
	return 0
}

func (x *UserAggregateInTimePeriod) GetQuarterlyAggregate() uint32 {
	if x != nil {
		return x.QuarterlyAggregate
	}
	return 0
}

// RewardOfferGroup groups multiple reward offers under a single group.
// Mapping between RewardOfferGroup to RewardOffer is 1 to many.
// Current use case of RewardOfferGroup is to solve for across rewardOffer inventory.
type RewardOfferGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique id of reward offer group.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// controls the max number of rewards that can be given to a user across all reward offers in the group.
	UserRewardAggregate int32 `protobuf:"varint,2,opt,name=user_reward_aggregate,json=userRewardAggregate,proto3" json:"user_reward_aggregate,omitempty"`
	// group description
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// reward-units caps config for an actor across all reward-offers in a group
	RewardUnitsCapUserAggregate *RewardUnitsCapAggregate `protobuf:"bytes,4,opt,name=reward_units_cap_user_aggregate,json=rewardUnitsCapUserAggregate,proto3" json:"reward_units_cap_user_aggregate,omitempty"`
	// reward-units caps config for an actor across all reward-offers in a group, on a monthly level
	RewardUnitsCapUserMonthlyAggregate *RewardUnitsCapAggregate `protobuf:"bytes,5,opt,name=reward_units_cap_user_monthly_aggregate,json=rewardUnitsCapUserMonthlyAggregate,proto3" json:"reward_units_cap_user_monthly_aggregate,omitempty"`
	// stores the aggregate limits for a user for time periods like a day, week, month etc.
	// these limits are sequential and are applied in the order of increasing time period aggregate like day -> week -> month -> quarter -> year
	// every aggregate must hold true for the user to be eligible for a new reward against this offer group.
	UserRewardAggregateInTimePeriod *UserAggregateInTimePeriod `protobuf:"bytes,6,opt,name=user_reward_aggregate_in_time_period,json=userRewardAggregateInTimePeriod,proto3" json:"user_reward_aggregate_in_time_period,omitempty"`
}

func (x *RewardOfferGroup) Reset() {
	*x = RewardOfferGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOfferGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOfferGroup) ProtoMessage() {}

func (x *RewardOfferGroup) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOfferGroup.ProtoReflect.Descriptor instead.
func (*RewardOfferGroup) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{26}
}

func (x *RewardOfferGroup) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RewardOfferGroup) GetUserRewardAggregate() int32 {
	if x != nil {
		return x.UserRewardAggregate
	}
	return 0
}

func (x *RewardOfferGroup) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *RewardOfferGroup) GetRewardUnitsCapUserAggregate() *RewardUnitsCapAggregate {
	if x != nil {
		return x.RewardUnitsCapUserAggregate
	}
	return nil
}

func (x *RewardOfferGroup) GetRewardUnitsCapUserMonthlyAggregate() *RewardUnitsCapAggregate {
	if x != nil {
		return x.RewardUnitsCapUserMonthlyAggregate
	}
	return nil
}

func (x *RewardOfferGroup) GetUserRewardAggregateInTimePeriod() *UserAggregateInTimePeriod {
	if x != nil {
		return x.UserRewardAggregateInTimePeriod
	}
	return nil
}

// RewardOfferRewardUnitsActorUtilisation stores the utilised reward-units by an actor for a reward-offer
type RewardOfferRewardUnitsActorUtilisation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique id for the entry
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// reward offer id
	OfferId string `protobuf:"bytes,2,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	// actor id
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// current utilised reward-units, i.e. units already given out to the actor for that offer
	FiCoinsUnits uint32 `protobuf:"varint,4,opt,name=fi_coins_units,json=fiCoinsUnits,proto3" json:"fi_coins_units,omitempty"`
	CashUnits    uint32 `protobuf:"varint,5,opt,name=cash_units,json=cashUnits,proto3" json:"cash_units,omitempty"`
	SdCashUnits  uint32 `protobuf:"varint,6,opt,name=sd_cash_units,json=sdCashUnits,proto3" json:"sd_cash_units,omitempty"`
	// timestamps
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// amount spend on usstock as reward in given offer
	UsstockCashUnits  uint32 `protobuf:"varint,9,opt,name=usstock_cash_units,json=usstockCashUnits,proto3" json:"usstock_cash_units,omitempty"`
	CcBillEraserUnits uint32 `protobuf:"varint,10,opt,name=cc_bill_eraser_units,json=ccBillEraserUnits,proto3" json:"cc_bill_eraser_units,omitempty"`
}

func (x *RewardOfferRewardUnitsActorUtilisation) Reset() {
	*x = RewardOfferRewardUnitsActorUtilisation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOfferRewardUnitsActorUtilisation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOfferRewardUnitsActorUtilisation) ProtoMessage() {}

func (x *RewardOfferRewardUnitsActorUtilisation) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOfferRewardUnitsActorUtilisation.ProtoReflect.Descriptor instead.
func (*RewardOfferRewardUnitsActorUtilisation) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{27}
}

func (x *RewardOfferRewardUnitsActorUtilisation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RewardOfferRewardUnitsActorUtilisation) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *RewardOfferRewardUnitsActorUtilisation) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *RewardOfferRewardUnitsActorUtilisation) GetFiCoinsUnits() uint32 {
	if x != nil {
		return x.FiCoinsUnits
	}
	return 0
}

func (x *RewardOfferRewardUnitsActorUtilisation) GetCashUnits() uint32 {
	if x != nil {
		return x.CashUnits
	}
	return 0
}

func (x *RewardOfferRewardUnitsActorUtilisation) GetSdCashUnits() uint32 {
	if x != nil {
		return x.SdCashUnits
	}
	return 0
}

func (x *RewardOfferRewardUnitsActorUtilisation) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RewardOfferRewardUnitsActorUtilisation) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *RewardOfferRewardUnitsActorUtilisation) GetUsstockCashUnits() uint32 {
	if x != nil {
		return x.UsstockCashUnits
	}
	return 0
}

func (x *RewardOfferRewardUnitsActorUtilisation) GetCcBillEraserUnits() uint32 {
	if x != nil {
		return x.CcBillEraserUnits
	}
	return 0
}

// RewardOfferGroupRewardUnitsActorUtilisation stores the utilised reward-units by an actor for a group (of reward offers)
type RewardOfferGroupRewardUnitsActorUtilisation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique id for the entry
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// reward offer id
	OfferGroupId string `protobuf:"bytes,2,opt,name=offer_group_id,json=offerGroupId,proto3" json:"offer_group_id,omitempty"`
	// actor id
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// current utilised reward-units, i.e. units already given out to the actor for that offer
	FiCoinsUnits uint32 `protobuf:"varint,4,opt,name=fi_coins_units,json=fiCoinsUnits,proto3" json:"fi_coins_units,omitempty"`
	CashUnits    uint32 `protobuf:"varint,5,opt,name=cash_units,json=cashUnits,proto3" json:"cash_units,omitempty"`
	SdCashUnits  uint32 `protobuf:"varint,6,opt,name=sd_cash_units,json=sdCashUnits,proto3" json:"sd_cash_units,omitempty"`
	// timestamps
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// amount spend on usstock as reward in offer group
	UsstockCashUnits  uint32 `protobuf:"varint,9,opt,name=usstock_cash_units,json=usstockCashUnits,proto3" json:"usstock_cash_units,omitempty"`
	CcBillEraserUnits uint32 `protobuf:"varint,10,opt,name=cc_bill_eraser_units,json=ccBillEraserUnits,proto3" json:"cc_bill_eraser_units,omitempty"`
}

func (x *RewardOfferGroupRewardUnitsActorUtilisation) Reset() {
	*x = RewardOfferGroupRewardUnitsActorUtilisation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOfferGroupRewardUnitsActorUtilisation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOfferGroupRewardUnitsActorUtilisation) ProtoMessage() {}

func (x *RewardOfferGroupRewardUnitsActorUtilisation) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOfferGroupRewardUnitsActorUtilisation.ProtoReflect.Descriptor instead.
func (*RewardOfferGroupRewardUnitsActorUtilisation) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{28}
}

func (x *RewardOfferGroupRewardUnitsActorUtilisation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RewardOfferGroupRewardUnitsActorUtilisation) GetOfferGroupId() string {
	if x != nil {
		return x.OfferGroupId
	}
	return ""
}

func (x *RewardOfferGroupRewardUnitsActorUtilisation) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *RewardOfferGroupRewardUnitsActorUtilisation) GetFiCoinsUnits() uint32 {
	if x != nil {
		return x.FiCoinsUnits
	}
	return 0
}

func (x *RewardOfferGroupRewardUnitsActorUtilisation) GetCashUnits() uint32 {
	if x != nil {
		return x.CashUnits
	}
	return 0
}

func (x *RewardOfferGroupRewardUnitsActorUtilisation) GetSdCashUnits() uint32 {
	if x != nil {
		return x.SdCashUnits
	}
	return 0
}

func (x *RewardOfferGroupRewardUnitsActorUtilisation) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RewardOfferGroupRewardUnitsActorUtilisation) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *RewardOfferGroupRewardUnitsActorUtilisation) GetUsstockCashUnits() uint32 {
	if x != nil {
		return x.UsstockCashUnits
	}
	return 0
}

func (x *RewardOfferGroupRewardUnitsActorUtilisation) GetCcBillEraserUnits() uint32 {
	if x != nil {
		return x.CcBillEraserUnits
	}
	return 0
}

// RewardOfferGroupInventory contains information of inventory of a reward offer group.
// i.e the total and the remaining slots in that inventory.
type RewardOfferGroupInventory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OfferGroupId string `protobuf:"bytes,1,opt,name=offer_group_id,json=offerGroupId,proto3" json:"offer_group_id,omitempty"`
	// remaining count in inventory.
	RemainingCount uint32 `protobuf:"varint,2,opt,name=remaining_count,json=remainingCount,proto3" json:"remaining_count,omitempty"`
	// total count that was initially in the inventory.
	TotalCount uint32 `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RewardOfferGroupInventory) Reset() {
	*x = RewardOfferGroupInventory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOfferGroupInventory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOfferGroupInventory) ProtoMessage() {}

func (x *RewardOfferGroupInventory) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOfferGroupInventory.ProtoReflect.Descriptor instead.
func (*RewardOfferGroupInventory) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{29}
}

func (x *RewardOfferGroupInventory) GetOfferGroupId() string {
	if x != nil {
		return x.OfferGroupId
	}
	return ""
}

func (x *RewardOfferGroupInventory) GetRemainingCount() uint32 {
	if x != nil {
		return x.RemainingCount
	}
	return 0
}

func (x *RewardOfferGroupInventory) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// RewardOfferInventory contains information of total and available count of a reward offer.
type RewardOfferInventory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardOfferId string `protobuf:"bytes,1,opt,name=reward_offer_id,json=rewardOfferId,proto3" json:"reward_offer_id,omitempty"`
	// remaining count of offer in inventory.
	RemainingCount uint32 `protobuf:"varint,2,opt,name=remaining_count,json=remainingCount,proto3" json:"remaining_count,omitempty"`
	// total count of offers that were initially in the inventory.
	TotalCount uint32 `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RewardOfferInventory) Reset() {
	*x = RewardOfferInventory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOfferInventory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOfferInventory) ProtoMessage() {}

func (x *RewardOfferInventory) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOfferInventory.ProtoReflect.Descriptor instead.
func (*RewardOfferInventory) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{30}
}

func (x *RewardOfferInventory) GetRewardOfferId() string {
	if x != nil {
		return x.RewardOfferId
	}
	return ""
}

func (x *RewardOfferInventory) GetRemainingCount() uint32 {
	if x != nil {
		return x.RemainingCount
	}
	return 0
}

func (x *RewardOfferInventory) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// this contains display related configs
type DisplayMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisplaySince string      `protobuf:"bytes,1,opt,name=display_since,json=displaySince,proto3" json:"display_since,omitempty"`
	DisplayTill  string      `protobuf:"bytes,2,opt,name=display_till,json=displayTill,proto3" json:"display_till,omitempty"`
	DisplayType  DisplayType `protobuf:"varint,3,opt,name=display_type,json=displayType,proto3,enum=rewardoffers.DisplayType" json:"display_type,omitempty"`
	Title        string      `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// Deprecated in favour of steps_v1
	//
	// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
	Steps []string `protobuf:"bytes,5,rep,name=steps,proto3" json:"steps,omitempty"`
	// Deprecated in favour of tncs_v1
	//
	// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
	Tncs []string `protobuf:"bytes,6,rep,name=tncs,proto3" json:"tncs,omitempty"`
	// icon to be shown on the tiles of "Ways to Earn Rewards" page
	Icon       string `protobuf:"bytes,7,opt,name=icon,proto3" json:"icon,omitempty"`
	BgColor    string `protobuf:"bytes,8,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	ActionDesc string `protobuf:"bytes,9,opt,name=action_desc,json=actionDesc,proto3" json:"action_desc,omitempty"`
	Cta        *CTA   `protobuf:"bytes,10,opt,name=cta,proto3" json:"cta,omitempty"`
	// display_rank is used to enforce display ordering of reward offers on the APP.
	// lower the rank, earlier that reward offers should be displayed on the APP.
	DisplayRank int32 `protobuf:"varint,11,opt,name=display_rank,json=displayRank,proto3" json:"display_rank,omitempty"`
	// short description of the offer like "Earn upto 5000 fi coins on adding money"
	ShortDesc string `protobuf:"bytes,12,opt,name=short_desc,json=shortDesc,proto3" json:"short_desc,omitempty"`
	// tile_bg_color to control the background color of reward offer tile on the APP.
	// if this field is null a default bg color is used.
	TileBgColor string `protobuf:"bytes,13,opt,name=tile_bg_color,json=tileBgColor,proto3" json:"tile_bg_color,omitempty"`
	// tags to be displayed for reward offers. order matters.
	Tags []*DisplayMeta_Tag `protobuf:"bytes,14,rep,name=tags,proto3" json:"tags,omitempty"`
	// image url
	ImageUrl string `protobuf:"bytes,15,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	// secondary tile-bg-color: to have an alternate color for reward-offer-tile different from the original tile-bg-color
	// use case: for e.g., salary program intro page
	SecondaryTileBgColor string `protobuf:"bytes,16,opt,name=secondary_tile_bg_color,json=secondaryTileBgColor,proto3" json:"secondary_tile_bg_color,omitempty"`
	// TnC statement one-liner.
	// For e.g., "10 Fi-Coins for every Rs100 of salary" on the benefit card for salary intro page
	ShortTnc string `protobuf:"bytes,17,opt,name=short_tnc,json=shortTnc,proto3" json:"short_tnc,omitempty"`
	// field to decide whether the reward-offer is to be shown on salary intro page or not
	IsVisibleOnSalaryIntroPage bool `protobuf:"varint,18,opt,name=is_visible_on_salary_intro_page,json=isVisibleOnSalaryIntroPage,proto3" json:"is_visible_on_salary_intro_page,omitempty"`
	// todo (utkarsh) : convert this to generic config usable for different screens
	// display rank for showing offer on salary into page
	DisplayRankOnSalaryIntroPage int32 `protobuf:"varint,19,opt,name=display_rank_on_salary_intro_page,json=displayRankOnSalaryIntroPage,proto3" json:"display_rank_on_salary_intro_page,omitempty"`
	// map containing app platform (OS) to min/max app version supporting this reward offer. it will be used to decide
	// which app versions to show the reward offer on for that platform.
	// If a platform is not specified in this map then corresponding min/max check shouldn't be applied.
	PlatformToMinSupportedAppVersionMap map[string]uint32 `protobuf:"bytes,20,rep,name=platform_to_min_supported_app_version_map,json=platformToMinSupportedAppVersionMap,proto3" json:"platform_to_min_supported_app_version_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	PlatformToMaxSupportedAppVersionMap map[string]uint32 `protobuf:"bytes,21,rep,name=platform_to_max_supported_app_version_map,json=platformToMaxSupportedAppVersionMap,proto3" json:"platform_to_max_supported_app_version_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// steps and tncs info
	StepsV1 []*DisplayMeta_InfoPoint `protobuf:"bytes,22,rep,name=steps_v1,json=stepsV1,proto3" json:"steps_v1,omitempty"`
	TncsV1  []*DisplayMeta_InfoPoint `protobuf:"bytes,23,rep,name=tncs_v1,json=tncsV1,proto3" json:"tncs_v1,omitempty"`
	// to be used primarily for displaying cash equivalent of the corresponding reward
	// for eg- 5000 indicates user can get rewards worth a max of 5000Rs with this reward offer
	CashEquivalent uint32 `protobuf:"varint,24,opt,name=cash_equivalent,json=cashEquivalent,proto3" json:"cash_equivalent,omitempty"`
	// to be used for calculating cash equivalent of the corresponding reward based on user data eg- salary, spends, etc
	// for eg- max(0.01 * MONTHLY_SALARY, 1000) * 3 will calculate rewards user can get based on their monthly salary
	CashEquivalentCalculationExpression string `protobuf:"bytes,25,opt,name=cash_equivalent_calculation_expression,json=cashEquivalentCalculationExpression,proto3" json:"cash_equivalent_calculation_expression,omitempty"`
	// verbose description of the offer like "Earn cashback via UPI. Max cashback of upto 50. Cashback is credited at the end of month"
	// to be used in bottom sheet drawers displayed on clicking i button on tiering and salary intro screens
	VerboseDesc string `protobuf:"bytes,26,opt,name=verbose_desc,json=verboseDesc,proto3" json:"verbose_desc,omitempty"`
	// optional field, denotes the display constraint for the rewardOffer, useful for cases where we need to display a rewardOffer only to users who pass the display constraint.
	// e.g "DURATION_SINCE_ONBOARDING_IN_MINS < 24*60" exp can be used to display the rewardOffer only to users whose onboarding age is less than 1 day.
	// **Note** : only light weight expressions should be used for display constraint as it's evaluated in a sync flow.
	DisplayConstraintExpression string `protobuf:"bytes,27,opt,name=display_constraint_expression,json=displayConstraintExpression,proto3" json:"display_constraint_expression,omitempty"`
	// display details for V2 ways to earn rewards screen
	WaysToEarnRewardsV2ScreenConfig *WaysToEarnRewardsV2ScreenConfig `protobuf:"bytes,28,opt,name=ways_to_earn_rewards_v2_screen_config,json=waysToEarnRewardsV2ScreenConfig,proto3" json:"ways_to_earn_rewards_v2_screen_config,omitempty"`
}

func (x *DisplayMeta) Reset() {
	*x = DisplayMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayMeta) ProtoMessage() {}

func (x *DisplayMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayMeta.ProtoReflect.Descriptor instead.
func (*DisplayMeta) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{31}
}

func (x *DisplayMeta) GetDisplaySince() string {
	if x != nil {
		return x.DisplaySince
	}
	return ""
}

func (x *DisplayMeta) GetDisplayTill() string {
	if x != nil {
		return x.DisplayTill
	}
	return ""
}

func (x *DisplayMeta) GetDisplayType() DisplayType {
	if x != nil {
		return x.DisplayType
	}
	return DisplayType_NOT_SPECIFIED
}

func (x *DisplayMeta) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
func (x *DisplayMeta) GetSteps() []string {
	if x != nil {
		return x.Steps
	}
	return nil
}

// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
func (x *DisplayMeta) GetTncs() []string {
	if x != nil {
		return x.Tncs
	}
	return nil
}

func (x *DisplayMeta) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *DisplayMeta) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *DisplayMeta) GetActionDesc() string {
	if x != nil {
		return x.ActionDesc
	}
	return ""
}

func (x *DisplayMeta) GetCta() *CTA {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *DisplayMeta) GetDisplayRank() int32 {
	if x != nil {
		return x.DisplayRank
	}
	return 0
}

func (x *DisplayMeta) GetShortDesc() string {
	if x != nil {
		return x.ShortDesc
	}
	return ""
}

func (x *DisplayMeta) GetTileBgColor() string {
	if x != nil {
		return x.TileBgColor
	}
	return ""
}

func (x *DisplayMeta) GetTags() []*DisplayMeta_Tag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *DisplayMeta) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *DisplayMeta) GetSecondaryTileBgColor() string {
	if x != nil {
		return x.SecondaryTileBgColor
	}
	return ""
}

func (x *DisplayMeta) GetShortTnc() string {
	if x != nil {
		return x.ShortTnc
	}
	return ""
}

func (x *DisplayMeta) GetIsVisibleOnSalaryIntroPage() bool {
	if x != nil {
		return x.IsVisibleOnSalaryIntroPage
	}
	return false
}

func (x *DisplayMeta) GetDisplayRankOnSalaryIntroPage() int32 {
	if x != nil {
		return x.DisplayRankOnSalaryIntroPage
	}
	return 0
}

func (x *DisplayMeta) GetPlatformToMinSupportedAppVersionMap() map[string]uint32 {
	if x != nil {
		return x.PlatformToMinSupportedAppVersionMap
	}
	return nil
}

func (x *DisplayMeta) GetPlatformToMaxSupportedAppVersionMap() map[string]uint32 {
	if x != nil {
		return x.PlatformToMaxSupportedAppVersionMap
	}
	return nil
}

func (x *DisplayMeta) GetStepsV1() []*DisplayMeta_InfoPoint {
	if x != nil {
		return x.StepsV1
	}
	return nil
}

func (x *DisplayMeta) GetTncsV1() []*DisplayMeta_InfoPoint {
	if x != nil {
		return x.TncsV1
	}
	return nil
}

func (x *DisplayMeta) GetCashEquivalent() uint32 {
	if x != nil {
		return x.CashEquivalent
	}
	return 0
}

func (x *DisplayMeta) GetCashEquivalentCalculationExpression() string {
	if x != nil {
		return x.CashEquivalentCalculationExpression
	}
	return ""
}

func (x *DisplayMeta) GetVerboseDesc() string {
	if x != nil {
		return x.VerboseDesc
	}
	return ""
}

func (x *DisplayMeta) GetDisplayConstraintExpression() string {
	if x != nil {
		return x.DisplayConstraintExpression
	}
	return ""
}

func (x *DisplayMeta) GetWaysToEarnRewardsV2ScreenConfig() *WaysToEarnRewardsV2ScreenConfig {
	if x != nil {
		return x.WaysToEarnRewardsV2ScreenConfig
	}
	return nil
}

// contains metadata required for sending notifications
// NOTE : keep field names starting with RewardNotificationType to distinguish which field is used for which notification type
type RewardNotificationMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// nudge id to be used for sending the notification via nudge
	NudgeId string `protobuf:"bytes,1,opt,name=nudge_id,json=nudgeId,proto3" json:"nudge_id,omitempty"`
	// email template to be used for sending the notification via  email
	EmailTemplate comms.EmailType `protobuf:"varint,2,opt,name=email_template,json=emailTemplate,proto3,enum=comms.EmailType" json:"email_template,omitempty"`
}

func (x *RewardNotificationMetadata) Reset() {
	*x = RewardNotificationMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardNotificationMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardNotificationMetadata) ProtoMessage() {}

func (x *RewardNotificationMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardNotificationMetadata.ProtoReflect.Descriptor instead.
func (*RewardNotificationMetadata) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{32}
}

func (x *RewardNotificationMetadata) GetNudgeId() string {
	if x != nil {
		return x.NudgeId
	}
	return ""
}

func (x *RewardNotificationMetadata) GetEmailTemplate() comms.EmailType {
	if x != nil {
		return x.EmailTemplate
	}
	return comms.EmailType(0)
}

// contains config for sending reward notifications.
type RewardNotificationConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EarnedRewardNotificationConfig               *EarnedRewardNotificationConfig               `protobuf:"bytes,1,opt,name=earned_reward_notification_config,json=earnedRewardNotificationConfig,proto3" json:"earned_reward_notification_config,omitempty"`
	RewardProcessingSuccessfulNotificationConfig *RewardProcessingSuccessfulNotificationConfig `protobuf:"bytes,2,opt,name=reward_processing_successful_notification_config,json=rewardProcessingSuccessfulNotificationConfig,proto3" json:"reward_processing_successful_notification_config,omitempty"`
}

func (x *RewardNotificationConfig) Reset() {
	*x = RewardNotificationConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardNotificationConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardNotificationConfig) ProtoMessage() {}

func (x *RewardNotificationConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardNotificationConfig.ProtoReflect.Descriptor instead.
func (*RewardNotificationConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{33}
}

func (x *RewardNotificationConfig) GetEarnedRewardNotificationConfig() *EarnedRewardNotificationConfig {
	if x != nil {
		return x.EarnedRewardNotificationConfig
	}
	return nil
}

func (x *RewardNotificationConfig) GetRewardProcessingSuccessfulNotificationConfig() *RewardProcessingSuccessfulNotificationConfig {
	if x != nil {
		return x.RewardProcessingSuccessfulNotificationConfig
	}
	return nil
}

// config for sending notification when some reward is earned by the user.
type EarnedRewardNotificationConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated in favour of notifications
	//
	// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
	NotificationType RewardNotificationType `protobuf:"varint,1,opt,name=notification_type,json=notificationType,proto3,enum=rewardoffers.RewardNotificationType" json:"notification_type,omitempty"`
	// denotes a list of notifications to be sent.
	Notifications []*EarnedRewardNotificationConfig_NotificationConfig `protobuf:"bytes,2,rep,name=notifications,proto3" json:"notifications,omitempty"`
	// if true then use the sendEarnedRewardNotificationV1 flow to send notifications
	//
	// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
	UseNotificationV1 bool `protobuf:"varint,3,opt,name=use_notification_v1,json=useNotificationV1,proto3" json:"use_notification_v1,omitempty"`
}

func (x *EarnedRewardNotificationConfig) Reset() {
	*x = EarnedRewardNotificationConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EarnedRewardNotificationConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EarnedRewardNotificationConfig) ProtoMessage() {}

func (x *EarnedRewardNotificationConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EarnedRewardNotificationConfig.ProtoReflect.Descriptor instead.
func (*EarnedRewardNotificationConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{34}
}

// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
func (x *EarnedRewardNotificationConfig) GetNotificationType() RewardNotificationType {
	if x != nil {
		return x.NotificationType
	}
	return RewardNotificationType_UNSPECIFIED_NOTIFICATION_TYPE
}

func (x *EarnedRewardNotificationConfig) GetNotifications() []*EarnedRewardNotificationConfig_NotificationConfig {
	if x != nil {
		return x.Notifications
	}
	return nil
}

// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
func (x *EarnedRewardNotificationConfig) GetUseNotificationV1() bool {
	if x != nil {
		return x.UseNotificationV1
	}
	return false
}

type RewardProcessingSuccessfulNotificationConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes a list of notifications to be sent.
	Notifications []*RewardProcessingSuccessfulNotificationConfig_NotificationConfig `protobuf:"bytes,1,rep,name=notifications,proto3" json:"notifications,omitempty"`
}

func (x *RewardProcessingSuccessfulNotificationConfig) Reset() {
	*x = RewardProcessingSuccessfulNotificationConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardProcessingSuccessfulNotificationConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardProcessingSuccessfulNotificationConfig) ProtoMessage() {}

func (x *RewardProcessingSuccessfulNotificationConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardProcessingSuccessfulNotificationConfig.ProtoReflect.Descriptor instead.
func (*RewardProcessingSuccessfulNotificationConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{35}
}

func (x *RewardProcessingSuccessfulNotificationConfig) GetNotifications() []*RewardProcessingSuccessfulNotificationConfig_NotificationConfig {
	if x != nil {
		return x.Notifications
	}
	return nil
}

type Content struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title    string             `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Body     string             `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	ImageUrl string             `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	Deeplink *deeplink.Deeplink `protobuf:"bytes,4,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *Content) Reset() {
	*x = Content{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Content) ProtoMessage() {}

func (x *Content) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Content.ProtoReflect.Descriptor instead.
func (*Content) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{36}
}

func (x *Content) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Content) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *Content) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *Content) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

// Request to create an offer
type CreateRewardOfferRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// this offer is active since when
	ActiveSince string `protobuf:"bytes,2,opt,name=active_since,json=activeSince,proto3" json:"active_since,omitempty"`
	// this offer is active till when
	ActiveTill string `protobuf:"bytes,3,opt,name=active_till,json=activeTill,proto3" json:"active_till,omitempty"`
	// email of the user who created the offer
	CreatedBy string `protobuf:"bytes,4,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	// offer to be evaluated on which all actions
	ActionType string `protobuf:"bytes,5,opt,name=action_type,json=actionType,proto3" json:"action_type,omitempty"`
	// constraint data used by rule to evaluate constraints
	// this change from string to proto once constraints are finalized
	ConstraintMeta *ConstraintsMeta `protobuf:"bytes,6,opt,name=constraint_meta,json=constraintMeta,proto3" json:"constraint_meta,omitempty"`
	// reward meta using which reward is calculated post constraints are passed
	RewardMeta *RewardMeta `protobuf:"bytes,7,opt,name=reward_meta,json=rewardMeta,proto3" json:"reward_meta,omitempty"`
	// display related meta
	DisplayMeta *DisplayMeta `protobuf:"bytes,8,opt,name=display_meta,json=displayMeta,proto3" json:"display_meta,omitempty"`
	// if it's visible on the UI or not
	IsVisible bool `protobuf:"varint,9,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	// type of reward offer
	OfferType rewards.RewardOfferType `protobuf:"varint,10,opt,name=offer_type,json=offerType,proto3,enum=rewards.RewardOfferType" json:"offer_type,omitempty"`
	// tags associated with reward offer
	Tags []RewardOfferTag `protobuf:"varint,11,rep,packed,name=tags,proto3,enum=rewardoffers.RewardOfferTag" json:"tags,omitempty"`
	// id of rewardOffer group to which this reward offer is associated with.
	// can be empty if rewardOffer is not associated with any group.
	GroupId string `protobuf:"bytes,12,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	// denotes that current offer is linked to some other reward offer.
	// useful in appeasement reward offers for linking it to the original reward offer.
	RefRewardOfferId string `protobuf:"bytes,13,opt,name=ref_reward_offer_id,json=refRewardOfferId,proto3" json:"ref_reward_offer_id,omitempty"`
	// segment id for which reward offer is displayed
	//
	// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
	DisplaySegmentId string `protobuf:"bytes,14,opt,name=display_segment_id,json=displaySegmentId,proto3" json:"display_segment_id,omitempty"`
	// boolean to indicate if reward offer is excluded for segment
	// if true, reward offer will be displayed to all user NOT present in this segment
	//
	// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
	IsDisplaySegmentExcluded bool `protobuf:"varint,15,opt,name=is_display_segment_excluded,json=isDisplaySegmentExcluded,proto3" json:"is_display_segment_excluded,omitempty"`
	// platform on which reward offer is supported, if unspecified the offer will be platform independent
	SupportedPlatform common.Platform `protobuf:"varint,16,opt,name=supported_platform,json=supportedPlatform,proto3,enum=api.typesv2.common.Platform" json:"supported_platform,omitempty"`
	// segment id expression for which reward offer is displayed
	DisplaySegmentExpression string `protobuf:"bytes,17,opt,name=display_segment_expression,json=displaySegmentExpression,proto3" json:"display_segment_expression,omitempty"`
	// stores fields like reward_offer_category, reward_area and reward_purpose for analytics
	AnalyticsData *AnalyticsData `protobuf:"bytes,22,opt,name=analytics_data,json=analyticsData,proto3" json:"analytics_data,omitempty"`
	// type of event that could unlock the reward. if specified, the reward will always be generated in locked state (with EXPLICITLY_LOCKED as sub status).
	// in future, we could add support for a constraint expression evaluation to decide whether to generate a locked or unlocked reward.
	UnlockEvent rewards.CollectedDataType `protobuf:"varint,23,opt,name=unlock_event,json=unlockEvent,proto3,enum=rewards.CollectedDataType" json:"unlock_event,omitempty"`
	// stores information related to unlocking of locked rewards generated by the offer
	UnlockMeta *UnlockMeta `protobuf:"bytes,24,opt,name=unlock_meta,json=unlockMeta,proto3" json:"unlock_meta,omitempty"`
	// what this offer will generate (actual reward or a projection only)
	GenerationType GenerationType `protobuf:"varint,25,opt,name=generation_type,json=generationType,proto3,enum=rewardoffers.GenerationType" json:"generation_type,omitempty"`
	// additional details related to reward offer
	AdditionalDetails *AdditionalDetails `protobuf:"bytes,26,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
}

func (x *CreateRewardOfferRequest) Reset() {
	*x = CreateRewardOfferRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRewardOfferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRewardOfferRequest) ProtoMessage() {}

func (x *CreateRewardOfferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRewardOfferRequest.ProtoReflect.Descriptor instead.
func (*CreateRewardOfferRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{37}
}

func (x *CreateRewardOfferRequest) GetActiveSince() string {
	if x != nil {
		return x.ActiveSince
	}
	return ""
}

func (x *CreateRewardOfferRequest) GetActiveTill() string {
	if x != nil {
		return x.ActiveTill
	}
	return ""
}

func (x *CreateRewardOfferRequest) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *CreateRewardOfferRequest) GetActionType() string {
	if x != nil {
		return x.ActionType
	}
	return ""
}

func (x *CreateRewardOfferRequest) GetConstraintMeta() *ConstraintsMeta {
	if x != nil {
		return x.ConstraintMeta
	}
	return nil
}

func (x *CreateRewardOfferRequest) GetRewardMeta() *RewardMeta {
	if x != nil {
		return x.RewardMeta
	}
	return nil
}

func (x *CreateRewardOfferRequest) GetDisplayMeta() *DisplayMeta {
	if x != nil {
		return x.DisplayMeta
	}
	return nil
}

func (x *CreateRewardOfferRequest) GetIsVisible() bool {
	if x != nil {
		return x.IsVisible
	}
	return false
}

func (x *CreateRewardOfferRequest) GetOfferType() rewards.RewardOfferType {
	if x != nil {
		return x.OfferType
	}
	return rewards.RewardOfferType(0)
}

func (x *CreateRewardOfferRequest) GetTags() []RewardOfferTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *CreateRewardOfferRequest) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *CreateRewardOfferRequest) GetRefRewardOfferId() string {
	if x != nil {
		return x.RefRewardOfferId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
func (x *CreateRewardOfferRequest) GetDisplaySegmentId() string {
	if x != nil {
		return x.DisplaySegmentId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
func (x *CreateRewardOfferRequest) GetIsDisplaySegmentExcluded() bool {
	if x != nil {
		return x.IsDisplaySegmentExcluded
	}
	return false
}

func (x *CreateRewardOfferRequest) GetSupportedPlatform() common.Platform {
	if x != nil {
		return x.SupportedPlatform
	}
	return common.Platform(0)
}

func (x *CreateRewardOfferRequest) GetDisplaySegmentExpression() string {
	if x != nil {
		return x.DisplaySegmentExpression
	}
	return ""
}

func (x *CreateRewardOfferRequest) GetAnalyticsData() *AnalyticsData {
	if x != nil {
		return x.AnalyticsData
	}
	return nil
}

func (x *CreateRewardOfferRequest) GetUnlockEvent() rewards.CollectedDataType {
	if x != nil {
		return x.UnlockEvent
	}
	return rewards.CollectedDataType(0)
}

func (x *CreateRewardOfferRequest) GetUnlockMeta() *UnlockMeta {
	if x != nil {
		return x.UnlockMeta
	}
	return nil
}

func (x *CreateRewardOfferRequest) GetGenerationType() GenerationType {
	if x != nil {
		return x.GenerationType
	}
	return GenerationType_GENERATION_TYPE_UNSPECIFIED
}

func (x *CreateRewardOfferRequest) GetAdditionalDetails() *AdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

// Request to create an rewardOffer group
type CreateRewardOfferGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// controls the max number of rewards that can be given to a user across all reward offers in the group.
	UserRewardAggregate int32 `protobuf:"varint,1,opt,name=user_reward_aggregate,json=userRewardAggregate,proto3" json:"user_reward_aggregate,omitempty"`
	// description of group
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// reward-units caps config for an actor across all reward-offers in a group
	RewardUnitsCapUserAggregate *RewardUnitsCapAggregate `protobuf:"bytes,3,opt,name=reward_units_cap_user_aggregate,json=rewardUnitsCapUserAggregate,proto3" json:"reward_units_cap_user_aggregate,omitempty"`
	// reward-units caps config for an actor across all reward-offers in a group on a monthly level
	RewardUnitsCapUserMonthlyAggregate *RewardUnitsCapAggregate `protobuf:"bytes,4,opt,name=reward_units_cap_user_monthly_aggregate,json=rewardUnitsCapUserMonthlyAggregate,proto3" json:"reward_units_cap_user_monthly_aggregate,omitempty"`
	// controls the max number of rewards that can be give to a user across all reward offers in the group in a time period
	UserRewardAggregateInTimePeriod *UserAggregateInTimePeriod `protobuf:"bytes,5,opt,name=user_reward_aggregate_in_time_period,json=userRewardAggregateInTimePeriod,proto3" json:"user_reward_aggregate_in_time_period,omitempty"`
}

func (x *CreateRewardOfferGroupRequest) Reset() {
	*x = CreateRewardOfferGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRewardOfferGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRewardOfferGroupRequest) ProtoMessage() {}

func (x *CreateRewardOfferGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRewardOfferGroupRequest.ProtoReflect.Descriptor instead.
func (*CreateRewardOfferGroupRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{38}
}

func (x *CreateRewardOfferGroupRequest) GetUserRewardAggregate() int32 {
	if x != nil {
		return x.UserRewardAggregate
	}
	return 0
}

func (x *CreateRewardOfferGroupRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateRewardOfferGroupRequest) GetRewardUnitsCapUserAggregate() *RewardUnitsCapAggregate {
	if x != nil {
		return x.RewardUnitsCapUserAggregate
	}
	return nil
}

func (x *CreateRewardOfferGroupRequest) GetRewardUnitsCapUserMonthlyAggregate() *RewardUnitsCapAggregate {
	if x != nil {
		return x.RewardUnitsCapUserMonthlyAggregate
	}
	return nil
}

func (x *CreateRewardOfferGroupRequest) GetUserRewardAggregateInTimePeriod() *UserAggregateInTimePeriod {
	if x != nil {
		return x.UserRewardAggregateInTimePeriod
	}
	return nil
}

// Response to create an rewardOffer group request
type CreateRewardOfferGroupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// rewardOffer group
	RewardOfferGroup *RewardOfferGroup `protobuf:"bytes,2,opt,name=reward_offer_group,json=rewardOfferGroup,proto3" json:"reward_offer_group,omitempty"`
}

func (x *CreateRewardOfferGroupResponse) Reset() {
	*x = CreateRewardOfferGroupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRewardOfferGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRewardOfferGroupResponse) ProtoMessage() {}

func (x *CreateRewardOfferGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRewardOfferGroupResponse.ProtoReflect.Descriptor instead.
func (*CreateRewardOfferGroupResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{39}
}

func (x *CreateRewardOfferGroupResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateRewardOfferGroupResponse) GetRewardOfferGroup() *RewardOfferGroup {
	if x != nil {
		return x.RewardOfferGroup
	}
	return nil
}

// offer is an combination of
//   - constraints meta data (used to evaluate constraints in the rule)
//   - reward meta: info used to evaluate the reward
//   - action types: types of actions (txn, sign_up etc) for for which this offer need to be evaluated
type RewardOffer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// identifier of the offer, this will be referenced to a reward
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// status of offer
	Status RewardOfferStatus `protobuf:"varint,3,opt,name=status,proto3,enum=rewardoffers.RewardOfferStatus" json:"status,omitempty"`
	// this offer is active since when
	ActiveSince string `protobuf:"bytes,4,opt,name=active_since,json=activeSince,proto3" json:"active_since,omitempty"`
	// this offer is active till when
	ActiveTill string `protobuf:"bytes,5,opt,name=active_till,json=activeTill,proto3" json:"active_till,omitempty"`
	// offer time of creation
	CreatedAt string `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// email of the user who created the offer
	CreatedBy string `protobuf:"bytes,7,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	// offer to be evaluated on which all actions
	ActionType string `protobuf:"bytes,9,opt,name=action_type,json=actionType,proto3" json:"action_type,omitempty"`
	// constraint data used by rule to evaluate constraints
	// this change from string to proto once constraints are finalized
	ConstraintMeta *ConstraintsMeta `protobuf:"bytes,10,opt,name=constraint_meta,json=constraintMeta,proto3" json:"constraint_meta,omitempty"`
	// reward meta using which reward is calculated post constraints are passed
	RewardMeta *RewardMeta `protobuf:"bytes,11,opt,name=reward_meta,json=rewardMeta,proto3" json:"reward_meta,omitempty"`
	// display related meta
	DisplayMeta *DisplayMeta `protobuf:"bytes,12,opt,name=display_meta,json=displayMeta,proto3" json:"display_meta,omitempty"`
	// if it's visible on the UI or not
	IsVisible bool `protobuf:"varint,13,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	// type of reward offer
	OfferType rewards.RewardOfferType `protobuf:"varint,14,opt,name=offer_type,json=offerType,proto3,enum=rewards.RewardOfferType" json:"offer_type,omitempty"`
	// tags associated with reward offer
	Tags []RewardOfferTag `protobuf:"varint,15,rep,packed,name=tags,proto3,enum=rewardoffers.RewardOfferTag" json:"tags,omitempty"`
	// id of rewardOffer group to which this reward offer is associated with.
	// can be empty if rewardOffer is not associated with any group.
	GroupId string `protobuf:"bytes,16,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	// denotes that current offer is linked to some other reward offer.
	// useful in appeasement reward offers for linking it to the original reward offer for auditing purpose.
	RefRewardOfferId string `protobuf:"bytes,17,opt,name=ref_reward_offer_id,json=refRewardOfferId,proto3" json:"ref_reward_offer_id,omitempty"`
	// platform for which the reward offer is created
	SupportedPlatform common.Platform `protobuf:"varint,18,opt,name=supported_platform,json=supportedPlatform,proto3,enum=api.typesv2.common.Platform" json:"supported_platform,omitempty"`
	// segment id for which reward offer is displayed
	//
	// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
	DisplaySegmentId string `protobuf:"bytes,19,opt,name=display_segment_id,json=displaySegmentId,proto3" json:"display_segment_id,omitempty"`
	// boolean to indicate if reward offer is excluded for segment
	// if true, reward offer will be displayed to all user NOT present in this segment
	//
	// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
	IsDisplaySegmentExcluded bool `protobuf:"varint,20,opt,name=is_display_segment_excluded,json=isDisplaySegmentExcluded,proto3" json:"is_display_segment_excluded,omitempty"`
	// segment id expression for which reward offer is displayed
	DisplaySegmentExpression string `protobuf:"bytes,21,opt,name=display_segment_expression,json=displaySegmentExpression,proto3" json:"display_segment_expression,omitempty"`
	// stores fields like reward_offer_category, reward_area and reward_purpose for analytics
	AnalyticsData *AnalyticsData `protobuf:"bytes,22,opt,name=analytics_data,json=analyticsData,proto3" json:"analytics_data,omitempty"`
	// event that will unlock the reward. if this is not UNSPECIFIED, reward will be generated in LOCKED state
	UnlockEvent rewards.CollectedDataType `protobuf:"varint,23,opt,name=unlock_event,json=unlockEvent,proto3,enum=rewards.CollectedDataType" json:"unlock_event,omitempty"`
	// stores information related to unlocking of locked rewards generated by the offer
	UnlockMeta *UnlockMeta `protobuf:"bytes,24,opt,name=unlock_meta,json=unlockMeta,proto3" json:"unlock_meta,omitempty"`
	// stores what the reward offer will generate, an actual reward or a projection
	GenerationType GenerationType         `protobuf:"varint,25,opt,name=generation_type,json=generationType,proto3,enum=rewardoffers.GenerationType" json:"generation_type,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,26,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// additional details related to reward offer
	AdditionalDetails *AdditionalDetails `protobuf:"bytes,27,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
}

func (x *RewardOffer) Reset() {
	*x = RewardOffer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOffer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOffer) ProtoMessage() {}

func (x *RewardOffer) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOffer.ProtoReflect.Descriptor instead.
func (*RewardOffer) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{40}
}

func (x *RewardOffer) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RewardOffer) GetStatus() RewardOfferStatus {
	if x != nil {
		return x.Status
	}
	return RewardOfferStatus_RewardOfferStatus_UNSPECIFIED
}

func (x *RewardOffer) GetActiveSince() string {
	if x != nil {
		return x.ActiveSince
	}
	return ""
}

func (x *RewardOffer) GetActiveTill() string {
	if x != nil {
		return x.ActiveTill
	}
	return ""
}

func (x *RewardOffer) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *RewardOffer) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *RewardOffer) GetActionType() string {
	if x != nil {
		return x.ActionType
	}
	return ""
}

func (x *RewardOffer) GetConstraintMeta() *ConstraintsMeta {
	if x != nil {
		return x.ConstraintMeta
	}
	return nil
}

func (x *RewardOffer) GetRewardMeta() *RewardMeta {
	if x != nil {
		return x.RewardMeta
	}
	return nil
}

func (x *RewardOffer) GetDisplayMeta() *DisplayMeta {
	if x != nil {
		return x.DisplayMeta
	}
	return nil
}

func (x *RewardOffer) GetIsVisible() bool {
	if x != nil {
		return x.IsVisible
	}
	return false
}

func (x *RewardOffer) GetOfferType() rewards.RewardOfferType {
	if x != nil {
		return x.OfferType
	}
	return rewards.RewardOfferType(0)
}

func (x *RewardOffer) GetTags() []RewardOfferTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *RewardOffer) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *RewardOffer) GetRefRewardOfferId() string {
	if x != nil {
		return x.RefRewardOfferId
	}
	return ""
}

func (x *RewardOffer) GetSupportedPlatform() common.Platform {
	if x != nil {
		return x.SupportedPlatform
	}
	return common.Platform(0)
}

// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
func (x *RewardOffer) GetDisplaySegmentId() string {
	if x != nil {
		return x.DisplaySegmentId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
func (x *RewardOffer) GetIsDisplaySegmentExcluded() bool {
	if x != nil {
		return x.IsDisplaySegmentExcluded
	}
	return false
}

func (x *RewardOffer) GetDisplaySegmentExpression() string {
	if x != nil {
		return x.DisplaySegmentExpression
	}
	return ""
}

func (x *RewardOffer) GetAnalyticsData() *AnalyticsData {
	if x != nil {
		return x.AnalyticsData
	}
	return nil
}

func (x *RewardOffer) GetUnlockEvent() rewards.CollectedDataType {
	if x != nil {
		return x.UnlockEvent
	}
	return rewards.CollectedDataType(0)
}

func (x *RewardOffer) GetUnlockMeta() *UnlockMeta {
	if x != nil {
		return x.UnlockMeta
	}
	return nil
}

func (x *RewardOffer) GetGenerationType() GenerationType {
	if x != nil {
		return x.GenerationType
	}
	return GenerationType_GENERATION_TYPE_UNSPECIFIED
}

func (x *RewardOffer) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *RewardOffer) GetAdditionalDetails() *AdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

type RewardOfferResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// offer details
	RewardOffer *RewardOffer `protobuf:"bytes,2,opt,name=reward_offer,json=rewardOffer,proto3" json:"reward_offer,omitempty"`
	// ValidationFailureInfos denotes validation failures (if any) for create reward offer request.
	// If ValidationFailureInfos are present then Invalid Argument status is returned.
	// Reward offer will not be created if validation errors are present.
	ValidationFailureInfos []*RewardOfferResponse_ValidationFailureInfo `protobuf:"bytes,3,rep,name=validation_failure_infos,json=validationFailureInfos,proto3" json:"validation_failure_infos,omitempty"`
}

func (x *RewardOfferResponse) Reset() {
	*x = RewardOfferResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOfferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOfferResponse) ProtoMessage() {}

func (x *RewardOfferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOfferResponse.ProtoReflect.Descriptor instead.
func (*RewardOfferResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{41}
}

func (x *RewardOfferResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *RewardOfferResponse) GetRewardOffer() *RewardOffer {
	if x != nil {
		return x.RewardOffer
	}
	return nil
}

func (x *RewardOfferResponse) GetValidationFailureInfos() []*RewardOfferResponse_ValidationFailureInfo {
	if x != nil {
		return x.ValidationFailureInfos
	}
	return nil
}

type GetRewardOffersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// active since date time sting ex: 2020-05-11T10:04:05+05:30
	ActiveSince string `protobuf:"bytes,1,opt,name=active_since,json=activeSince,proto3" json:"active_since,omitempty"`
	// active till date time sting ex: 2020-05-11T10:04:05+05:30
	ActiveTill string `protobuf:"bytes,2,opt,name=active_till,json=activeTill,proto3" json:"active_till,omitempty"`
	// display since date time sting ex: 2020-05-11T10:04:05+05:30
	DisplaySince string `protobuf:"bytes,3,opt,name=display_since,json=displaySince,proto3" json:"display_since,omitempty"`
	// active till date time sting ex: 2020-05-11T10:04:05+05:30
	DisplayTill string `protobuf:"bytes,4,opt,name=display_till,json=displayTill,proto3" json:"display_till,omitempty"`
	// filter by status, if not passed RewardOfferStatus_UNSPECIFIED will be considered
	Status RewardOfferStatus `protobuf:"varint,5,opt,name=status,proto3,enum=rewardoffers.RewardOfferStatus" json:"status,omitempty"`
	// if it's visible on the UI or not, if not passed false will be considered
	IsVisible bool `protobuf:"varint,6,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	// filter on offer types
	OfferTypes []rewards.RewardOfferType `protobuf:"varint,7,rep,packed,name=offer_types,json=offerTypes,proto3,enum=rewards.RewardOfferType" json:"offer_types,omitempty"`
	// platform to filter in offers for (IOS/Android/unspecified)
	SupportedPlatform common.Platform `protobuf:"varint,8,opt,name=supported_platform,json=supportedPlatform,proto3,enum=api.typesv2.common.Platform" json:"supported_platform,omitempty"`
	// rewardOffer id
	Id string `protobuf:"bytes,9,opt,name=id,proto3" json:"id,omitempty"`
	// filter on segment id for which reward offer is displayed
	//
	// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
	DisplaySegmentId string `protobuf:"bytes,19,opt,name=display_segment_id,json=displaySegmentId,proto3" json:"display_segment_id,omitempty"`
	// filter on boolean to indicate if reward offer is excluded for segment
	//
	// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
	IsDisplaySegmentExcluded bool `protobuf:"varint,20,opt,name=is_display_segment_excluded,json=isDisplaySegmentExcluded,proto3" json:"is_display_segment_excluded,omitempty"`
	// filter on segment id expression for which reward offer is displayed
	DisplaySegmentExpression string `protobuf:"bytes,21,opt,name=display_segment_expression,json=displaySegmentExpression,proto3" json:"display_segment_expression,omitempty"`
	// filter on the generation type (actual or projections)
	GenerationType GenerationType `protobuf:"varint,22,opt,name=generation_type,json=generationType,proto3,enum=rewardoffers.GenerationType" json:"generation_type,omitempty"`
}

func (x *GetRewardOffersRequest) Reset() {
	*x = GetRewardOffersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOffersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOffersRequest) ProtoMessage() {}

func (x *GetRewardOffersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOffersRequest.ProtoReflect.Descriptor instead.
func (*GetRewardOffersRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{42}
}

func (x *GetRewardOffersRequest) GetActiveSince() string {
	if x != nil {
		return x.ActiveSince
	}
	return ""
}

func (x *GetRewardOffersRequest) GetActiveTill() string {
	if x != nil {
		return x.ActiveTill
	}
	return ""
}

func (x *GetRewardOffersRequest) GetDisplaySince() string {
	if x != nil {
		return x.DisplaySince
	}
	return ""
}

func (x *GetRewardOffersRequest) GetDisplayTill() string {
	if x != nil {
		return x.DisplayTill
	}
	return ""
}

func (x *GetRewardOffersRequest) GetStatus() RewardOfferStatus {
	if x != nil {
		return x.Status
	}
	return RewardOfferStatus_RewardOfferStatus_UNSPECIFIED
}

func (x *GetRewardOffersRequest) GetIsVisible() bool {
	if x != nil {
		return x.IsVisible
	}
	return false
}

func (x *GetRewardOffersRequest) GetOfferTypes() []rewards.RewardOfferType {
	if x != nil {
		return x.OfferTypes
	}
	return nil
}

func (x *GetRewardOffersRequest) GetSupportedPlatform() common.Platform {
	if x != nil {
		return x.SupportedPlatform
	}
	return common.Platform(0)
}

func (x *GetRewardOffersRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
func (x *GetRewardOffersRequest) GetDisplaySegmentId() string {
	if x != nil {
		return x.DisplaySegmentId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/rewards/rewardoffers/reward_offer.proto.
func (x *GetRewardOffersRequest) GetIsDisplaySegmentExcluded() bool {
	if x != nil {
		return x.IsDisplaySegmentExcluded
	}
	return false
}

func (x *GetRewardOffersRequest) GetDisplaySegmentExpression() string {
	if x != nil {
		return x.DisplaySegmentExpression
	}
	return ""
}

func (x *GetRewardOffersRequest) GetGenerationType() GenerationType {
	if x != nil {
		return x.GenerationType
	}
	return GenerationType_GENERATION_TYPE_UNSPECIFIED
}

type GetRewardOffersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RewardOffers []*RewardOffer `protobuf:"bytes,2,rep,name=reward_offers,json=rewardOffers,proto3" json:"reward_offers,omitempty"`
}

func (x *GetRewardOffersResponse) Reset() {
	*x = GetRewardOffersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOffersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOffersResponse) ProtoMessage() {}

func (x *GetRewardOffersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOffersResponse.ProtoReflect.Descriptor instead.
func (*GetRewardOffersResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{43}
}

func (x *GetRewardOffersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardOffersResponse) GetRewardOffers() []*RewardOffer {
	if x != nil {
		return x.RewardOffers
	}
	return nil
}

type GetRewardOffersByIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (x *GetRewardOffersByIdsRequest) Reset() {
	*x = GetRewardOffersByIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOffersByIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOffersByIdsRequest) ProtoMessage() {}

func (x *GetRewardOffersByIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOffersByIdsRequest.ProtoReflect.Descriptor instead.
func (*GetRewardOffersByIdsRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{44}
}

func (x *GetRewardOffersByIdsRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type GetRewardOffersByIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RewardOffers []*RewardOffer `protobuf:"bytes,2,rep,name=reward_offers,json=rewardOffers,proto3" json:"reward_offers,omitempty"`
}

func (x *GetRewardOffersByIdsResponse) Reset() {
	*x = GetRewardOffersByIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOffersByIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOffersByIdsResponse) ProtoMessage() {}

func (x *GetRewardOffersByIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOffersByIdsResponse.ProtoReflect.Descriptor instead.
func (*GetRewardOffersByIdsResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{45}
}

func (x *GetRewardOffersByIdsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardOffersByIdsResponse) GetRewardOffers() []*RewardOffer {
	if x != nil {
		return x.RewardOffers
	}
	return nil
}

type GetRewardOffersForActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// active since date time sting ex: 2020-05-11T10:04:05+05:30
	ActiveSince string `protobuf:"bytes,1,opt,name=active_since,json=activeSince,proto3" json:"active_since,omitempty"`
	// active till date time sting ex: 2020-05-11T10:04:05+05:30
	ActiveTill string `protobuf:"bytes,2,opt,name=active_till,json=activeTill,proto3" json:"active_till,omitempty"`
	// display since date time sting ex: 2020-05-11T10:04:05+05:30
	DisplaySince string `protobuf:"bytes,3,opt,name=display_since,json=displaySince,proto3" json:"display_since,omitempty"`
	// active till date time sting ex: 2020-05-11T10:04:05+05:30
	DisplayTill string `protobuf:"bytes,4,opt,name=display_till,json=displayTill,proto3" json:"display_till,omitempty"`
	// filter by status, if not passed filter on status will not be applied
	Status RewardOfferStatus `protobuf:"varint,5,opt,name=status,proto3,enum=rewardoffers.RewardOfferStatus" json:"status,omitempty"`
	// if it's visible on the UI or not
	IsVisible bool `protobuf:"varint,6,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	// id of actor
	ActorId string `protobuf:"bytes,7,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// platform of actor's device
	SupportedPlatform common.Platform `protobuf:"varint,8,opt,name=supported_platform,json=supportedPlatform,proto3,enum=api.typesv2.common.Platform" json:"supported_platform,omitempty"`
	// id of rewardOffer
	Id string `protobuf:"bytes,9,opt,name=id,proto3" json:"id,omitempty"`
	// filter on the generation type (actual or projections)
	GenerationType GenerationType `protobuf:"varint,10,opt,name=generation_type,json=generationType,proto3,enum=rewardoffers.GenerationType" json:"generation_type,omitempty"`
}

func (x *GetRewardOffersForActorRequest) Reset() {
	*x = GetRewardOffersForActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOffersForActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOffersForActorRequest) ProtoMessage() {}

func (x *GetRewardOffersForActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOffersForActorRequest.ProtoReflect.Descriptor instead.
func (*GetRewardOffersForActorRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{46}
}

func (x *GetRewardOffersForActorRequest) GetActiveSince() string {
	if x != nil {
		return x.ActiveSince
	}
	return ""
}

func (x *GetRewardOffersForActorRequest) GetActiveTill() string {
	if x != nil {
		return x.ActiveTill
	}
	return ""
}

func (x *GetRewardOffersForActorRequest) GetDisplaySince() string {
	if x != nil {
		return x.DisplaySince
	}
	return ""
}

func (x *GetRewardOffersForActorRequest) GetDisplayTill() string {
	if x != nil {
		return x.DisplayTill
	}
	return ""
}

func (x *GetRewardOffersForActorRequest) GetStatus() RewardOfferStatus {
	if x != nil {
		return x.Status
	}
	return RewardOfferStatus_RewardOfferStatus_UNSPECIFIED
}

func (x *GetRewardOffersForActorRequest) GetIsVisible() bool {
	if x != nil {
		return x.IsVisible
	}
	return false
}

func (x *GetRewardOffersForActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRewardOffersForActorRequest) GetSupportedPlatform() common.Platform {
	if x != nil {
		return x.SupportedPlatform
	}
	return common.Platform(0)
}

func (x *GetRewardOffersForActorRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetRewardOffersForActorRequest) GetGenerationType() GenerationType {
	if x != nil {
		return x.GenerationType
	}
	return GenerationType_GENERATION_TYPE_UNSPECIFIED
}

type GetRewardOffersForActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                          *rpc.Status                                    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RewardOffers                    []*RewardOffer                                 `protobuf:"bytes,2,rep,name=reward_offers,json=rewardOffers,proto3" json:"reward_offers,omitempty"`
	ActorLevelRewardOfferInventory  []*RewardOfferInventory                        `protobuf:"bytes,3,rep,name=actor_level_reward_offer_inventory,json=actorLevelRewardOfferInventory,proto3" json:"actor_level_reward_offer_inventory,omitempty"`
	ActionLevelRewardOfferInventory []*RewardOfferInventory                        `protobuf:"bytes,4,rep,name=action_level_reward_offer_inventory,json=actionLevelRewardOfferInventory,proto3" json:"action_level_reward_offer_inventory,omitempty"`
	ActorLevelOfferGroupInventory   []*RewardOfferGroupInventory                   `protobuf:"bytes,5,rep,name=actor_level_offer_group_inventory,json=actorLevelOfferGroupInventory,proto3" json:"actor_level_offer_group_inventory,omitempty"`
	ActorLevelRewardUnitsUtil       []*RewardOfferRewardUnitsActorUtilisation      `protobuf:"bytes,6,rep,name=actor_level_reward_units_util,json=actorLevelRewardUnitsUtil,proto3" json:"actor_level_reward_units_util,omitempty"`
	ActorLevelGroupRewardsUnitsUtil []*RewardOfferGroupRewardUnitsActorUtilisation `protobuf:"bytes,7,rep,name=actor_level_group_rewards_units_util,json=actorLevelGroupRewardsUnitsUtil,proto3" json:"actor_level_group_rewards_units_util,omitempty"`
	OfferGroupIdToOfferGroupMap     map[string]*RewardOfferGroup                   `protobuf:"bytes,8,rep,name=offer_group_id_to_offer_group_map,json=offerGroupIdToOfferGroupMap,proto3" json:"offer_group_id_to_offer_group_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetRewardOffersForActorResponse) Reset() {
	*x = GetRewardOffersForActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOffersForActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOffersForActorResponse) ProtoMessage() {}

func (x *GetRewardOffersForActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOffersForActorResponse.ProtoReflect.Descriptor instead.
func (*GetRewardOffersForActorResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{47}
}

func (x *GetRewardOffersForActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardOffersForActorResponse) GetRewardOffers() []*RewardOffer {
	if x != nil {
		return x.RewardOffers
	}
	return nil
}

func (x *GetRewardOffersForActorResponse) GetActorLevelRewardOfferInventory() []*RewardOfferInventory {
	if x != nil {
		return x.ActorLevelRewardOfferInventory
	}
	return nil
}

func (x *GetRewardOffersForActorResponse) GetActionLevelRewardOfferInventory() []*RewardOfferInventory {
	if x != nil {
		return x.ActionLevelRewardOfferInventory
	}
	return nil
}

func (x *GetRewardOffersForActorResponse) GetActorLevelOfferGroupInventory() []*RewardOfferGroupInventory {
	if x != nil {
		return x.ActorLevelOfferGroupInventory
	}
	return nil
}

func (x *GetRewardOffersForActorResponse) GetActorLevelRewardUnitsUtil() []*RewardOfferRewardUnitsActorUtilisation {
	if x != nil {
		return x.ActorLevelRewardUnitsUtil
	}
	return nil
}

func (x *GetRewardOffersForActorResponse) GetActorLevelGroupRewardsUnitsUtil() []*RewardOfferGroupRewardUnitsActorUtilisation {
	if x != nil {
		return x.ActorLevelGroupRewardsUnitsUtil
	}
	return nil
}

func (x *GetRewardOffersForActorResponse) GetOfferGroupIdToOfferGroupMap() map[string]*RewardOfferGroup {
	if x != nil {
		return x.OfferGroupIdToOfferGroupMap
	}
	return nil
}

type GetRewardOffersForScreenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor_id is mandatory
	ActorId string                                  `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Filter  *GetRewardOffersForScreenRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *GetRewardOffersForScreenRequest) Reset() {
	*x = GetRewardOffersForScreenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOffersForScreenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOffersForScreenRequest) ProtoMessage() {}

func (x *GetRewardOffersForScreenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOffersForScreenRequest.ProtoReflect.Descriptor instead.
func (*GetRewardOffersForScreenRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{48}
}

func (x *GetRewardOffersForScreenRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRewardOffersForScreenRequest) GetFilter() *GetRewardOffersForScreenRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type GetRewardOffersForScreenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RewardOffers []*RewardOffer `protobuf:"bytes,2,rep,name=reward_offers,json=rewardOffers,proto3" json:"reward_offers,omitempty"`
}

func (x *GetRewardOffersForScreenResponse) Reset() {
	*x = GetRewardOffersForScreenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOffersForScreenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOffersForScreenResponse) ProtoMessage() {}

func (x *GetRewardOffersForScreenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOffersForScreenResponse.ProtoReflect.Descriptor instead.
func (*GetRewardOffersForScreenResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{49}
}

func (x *GetRewardOffersForScreenResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardOffersForScreenResponse) GetRewardOffers() []*RewardOffer {
	if x != nil {
		return x.RewardOffers
	}
	return nil
}

type UpdateRewardOfferStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// identifier of the offer
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// status of the offer to be updated
	Status RewardOfferStatus `protobuf:"varint,2,opt,name=status,proto3,enum=rewardoffers.RewardOfferStatus" json:"status,omitempty"`
}

func (x *UpdateRewardOfferStatusRequest) Reset() {
	*x = UpdateRewardOfferStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRewardOfferStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRewardOfferStatusRequest) ProtoMessage() {}

func (x *UpdateRewardOfferStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRewardOfferStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateRewardOfferStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{50}
}

func (x *UpdateRewardOfferStatusRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateRewardOfferStatusRequest) GetStatus() RewardOfferStatus {
	if x != nil {
		return x.Status
	}
	return RewardOfferStatus_RewardOfferStatus_UNSPECIFIED
}

type UpdateRewardOfferDisplayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// identifier of the offer
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// display meta used for updating the given offer
	DisplayMeta *DisplayMeta `protobuf:"bytes,2,opt,name=displayMeta,proto3" json:"displayMeta,omitempty"`
	// existing tags on offer would be replaced with these new offer_tags.
	// replacement is done only if offer_tags list is non empty.
	OfferTags []RewardOfferTag `protobuf:"varint,3,rep,packed,name=offer_tags,json=offerTags,proto3,enum=rewardoffers.RewardOfferTag" json:"offer_tags,omitempty"`
	// if it's visible on the UI or not
	IsVisible common.BooleanEnum `protobuf:"varint,4,opt,name=is_visible,json=isVisible,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_visible,omitempty"`
}

func (x *UpdateRewardOfferDisplayRequest) Reset() {
	*x = UpdateRewardOfferDisplayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRewardOfferDisplayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRewardOfferDisplayRequest) ProtoMessage() {}

func (x *UpdateRewardOfferDisplayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRewardOfferDisplayRequest.ProtoReflect.Descriptor instead.
func (*UpdateRewardOfferDisplayRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{51}
}

func (x *UpdateRewardOfferDisplayRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateRewardOfferDisplayRequest) GetDisplayMeta() *DisplayMeta {
	if x != nil {
		return x.DisplayMeta
	}
	return nil
}

func (x *UpdateRewardOfferDisplayRequest) GetOfferTags() []RewardOfferTag {
	if x != nil {
		return x.OfferTags
	}
	return nil
}

func (x *UpdateRewardOfferDisplayRequest) GetIsVisible() common.BooleanEnum {
	if x != nil {
		return x.IsVisible
	}
	return common.BooleanEnum(0)
}

type UpdateRewardOfferRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// identifier of the offer
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// display meta used for updating the given offer
	DisplayMeta *DisplayMeta `protobuf:"bytes,2,opt,name=display_meta,json=displayMeta,proto3" json:"display_meta,omitempty"`
	// existing tags on offer would be replaced with these new offer_tags.
	// replacement is done only if offer_tags list is non empty.
	OfferTags []RewardOfferTag `protobuf:"varint,3,rep,packed,name=offer_tags,json=offerTags,proto3,enum=rewardoffers.RewardOfferTag" json:"offer_tags,omitempty"`
	// used to update the active_till date of active reward offers.
	// note : will only update if the offer is still active
	ActiveTill *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=active_till,json=activeTill,proto3" json:"active_till,omitempty"`
	// will be allowed only if the offer is in created state
	ConstraintMeta string `protobuf:"bytes,5,opt,name=constraint_meta,json=constraintMeta,proto3" json:"constraint_meta,omitempty"`
	// will be allowed only if the offer is in created state
	RewardOfferType rewards.RewardOfferType `protobuf:"varint,6,opt,name=reward_offer_type,json=rewardOfferType,proto3,enum=rewards.RewardOfferType" json:"reward_offer_type,omitempty"`
	// if this is set then only we will update reward offer type
	ShouldUpdateRewardOfferType bool `protobuf:"varint,7,opt,name=should_update_reward_offer_type,json=shouldUpdateRewardOfferType,proto3" json:"should_update_reward_offer_type,omitempty"`
	// will be allowed only if the offer is in created state
	ActionType string `protobuf:"bytes,8,opt,name=action_type,json=actionType,proto3" json:"action_type,omitempty"`
	// will be allowed only if the offer is in created state
	UnlockMeta *UnlockMeta `protobuf:"bytes,9,opt,name=unlock_meta,json=unlockMeta,proto3" json:"unlock_meta,omitempty"`
	// will be allowd only if the offer is in created state
	UnlockEvent rewards.CollectedDataType `protobuf:"varint,10,opt,name=unlock_event,json=unlockEvent,proto3,enum=rewards.CollectedDataType" json:"unlock_event,omitempty"`
	// will be allowed only if the offer is in created state
	RewardMeta string `protobuf:"bytes,11,opt,name=reward_meta,json=rewardMeta,proto3" json:"reward_meta,omitempty"`
	// additional details related to reward offer
	AdditionalDetails *AdditionalDetails `protobuf:"bytes,12,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
	// used to update the active_since time of the reward offer.
	// note : will only be updated if the offer is in created state
	ActiveSince *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=active_since,json=activeSince,proto3" json:"active_since,omitempty"`
	// used to update id of rewardOffer group to which this reward offer is associated with.
	// note : will only be updated if the offer is in created state
	GroupId string `protobuf:"bytes,14,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	// segment id expression for which reward offer is displayed
	// note : will only be updated if the offer is in created state
	DisplaySegmentExpression string `protobuf:"bytes,15,opt,name=display_segment_expression,json=displaySegmentExpression,proto3" json:"display_segment_expression,omitempty"`
	// if it's visible on the UI or not
	IsVisible common.BooleanEnum `protobuf:"varint,16,opt,name=is_visible,json=isVisible,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_visible,omitempty"`
	// stores fields like reward_offer_category, reward_area and reward_purpose for analytics
	AnalyticsData *AnalyticsData `protobuf:"bytes,17,opt,name=analytics_data,json=analyticsData,proto3" json:"analytics_data,omitempty"`
}

func (x *UpdateRewardOfferRequest) Reset() {
	*x = UpdateRewardOfferRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRewardOfferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRewardOfferRequest) ProtoMessage() {}

func (x *UpdateRewardOfferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRewardOfferRequest.ProtoReflect.Descriptor instead.
func (*UpdateRewardOfferRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{52}
}

func (x *UpdateRewardOfferRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateRewardOfferRequest) GetDisplayMeta() *DisplayMeta {
	if x != nil {
		return x.DisplayMeta
	}
	return nil
}

func (x *UpdateRewardOfferRequest) GetOfferTags() []RewardOfferTag {
	if x != nil {
		return x.OfferTags
	}
	return nil
}

func (x *UpdateRewardOfferRequest) GetActiveTill() *timestamppb.Timestamp {
	if x != nil {
		return x.ActiveTill
	}
	return nil
}

func (x *UpdateRewardOfferRequest) GetConstraintMeta() string {
	if x != nil {
		return x.ConstraintMeta
	}
	return ""
}

func (x *UpdateRewardOfferRequest) GetRewardOfferType() rewards.RewardOfferType {
	if x != nil {
		return x.RewardOfferType
	}
	return rewards.RewardOfferType(0)
}

func (x *UpdateRewardOfferRequest) GetShouldUpdateRewardOfferType() bool {
	if x != nil {
		return x.ShouldUpdateRewardOfferType
	}
	return false
}

func (x *UpdateRewardOfferRequest) GetActionType() string {
	if x != nil {
		return x.ActionType
	}
	return ""
}

func (x *UpdateRewardOfferRequest) GetUnlockMeta() *UnlockMeta {
	if x != nil {
		return x.UnlockMeta
	}
	return nil
}

func (x *UpdateRewardOfferRequest) GetUnlockEvent() rewards.CollectedDataType {
	if x != nil {
		return x.UnlockEvent
	}
	return rewards.CollectedDataType(0)
}

func (x *UpdateRewardOfferRequest) GetRewardMeta() string {
	if x != nil {
		return x.RewardMeta
	}
	return ""
}

func (x *UpdateRewardOfferRequest) GetAdditionalDetails() *AdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

func (x *UpdateRewardOfferRequest) GetActiveSince() *timestamppb.Timestamp {
	if x != nil {
		return x.ActiveSince
	}
	return nil
}

func (x *UpdateRewardOfferRequest) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *UpdateRewardOfferRequest) GetDisplaySegmentExpression() string {
	if x != nil {
		return x.DisplaySegmentExpression
	}
	return ""
}

func (x *UpdateRewardOfferRequest) GetIsVisible() common.BooleanEnum {
	if x != nil {
		return x.IsVisible
	}
	return common.BooleanEnum(0)
}

func (x *UpdateRewardOfferRequest) GetAnalyticsData() *AnalyticsData {
	if x != nil {
		return x.AnalyticsData
	}
	return nil
}

type AnalyticsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardClass   RewardClass   `protobuf:"varint,1,opt,name=reward_class,json=rewardClass,proto3,enum=rewardoffers.RewardClass" json:"reward_class,omitempty"`
	RewardPurpose RewardPurpose `protobuf:"varint,2,opt,name=reward_purpose,json=rewardPurpose,proto3,enum=rewardoffers.RewardPurpose" json:"reward_purpose,omitempty"`
	RewardProduct RewardProduct `protobuf:"varint,3,opt,name=reward_product,json=rewardProduct,proto3,enum=rewardoffers.RewardProduct" json:"reward_product,omitempty"`
}

func (x *AnalyticsData) Reset() {
	*x = AnalyticsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalyticsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsData) ProtoMessage() {}

func (x *AnalyticsData) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsData.ProtoReflect.Descriptor instead.
func (*AnalyticsData) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{53}
}

func (x *AnalyticsData) GetRewardClass() RewardClass {
	if x != nil {
		return x.RewardClass
	}
	return RewardClass_REWARD_CLASS_UNSPECIFIED
}

func (x *AnalyticsData) GetRewardPurpose() RewardPurpose {
	if x != nil {
		return x.RewardPurpose
	}
	return RewardPurpose_REWARD_PURPOSE_UNSPECIFIED
}

func (x *AnalyticsData) GetRewardProduct() RewardProduct {
	if x != nil {
		return x.RewardProduct
	}
	return RewardProduct_REWARD_PRODUCT_UNSPECIFIED
}

type CTA struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Deeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	Desc     string             `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	IconUrl  string             `protobuf:"bytes,4,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
}

func (x *CTA) Reset() {
	*x = CTA{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CTA) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CTA) ProtoMessage() {}

func (x *CTA) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CTA.ProtoReflect.Descriptor instead.
func (*CTA) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{54}
}

func (x *CTA) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CTA) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *CTA) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *CTA) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

type UnlockMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// CTA to be shown to the user to redirect them to unlock action specific screen
	Cta *CTA `protobuf:"bytes,1,opt,name=cta,proto3" json:"cta,omitempty"`
	// constraint to be evaluated for deciding whether the reward is to be unlocked or not
	Constraint *ConstraintsMeta `protobuf:"bytes,2,opt,name=constraint,proto3" json:"constraint,omitempty"`
}

func (x *UnlockMeta) Reset() {
	*x = UnlockMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnlockMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlockMeta) ProtoMessage() {}

func (x *UnlockMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlockMeta.ProtoReflect.Descriptor instead.
func (*UnlockMeta) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{55}
}

func (x *UnlockMeta) GetCta() *CTA {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *UnlockMeta) GetConstraint() *ConstraintsMeta {
	if x != nil {
		return x.Constraint
	}
	return nil
}

// USStockRewardConfig contains the necessary config required for generating usstocks as reward
// note: amount for which usstocks reward will be given depends on  units_config inside RewardConfigOption
type USStockRewardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// represent the stock id which is being given as reward
	StockId string `protobuf:"bytes,1,opt,name=stock_id,json=stockId,proto3" json:"stock_id,omitempty"`
}

func (x *USStockRewardConfig) Reset() {
	*x = USStockRewardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *USStockRewardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*USStockRewardConfig) ProtoMessage() {}

func (x *USStockRewardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use USStockRewardConfig.ProtoReflect.Descriptor instead.
func (*USStockRewardConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{56}
}

func (x *USStockRewardConfig) GetStockId() string {
	if x != nil {
		return x.StockId
	}
	return ""
}

type FiCoinsRewardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// flag to know whether a reward type FI_COINS reward offer will be given in the form of FI_POINTS
	// if unspecified, it will be considered as FI_COINS as older offers will not have this field
	IsFiPoints common.BooleanEnum `protobuf:"varint,1,opt,name=is_fi_points,json=isFiPoints,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_fi_points,omitempty"`
}

func (x *FiCoinsRewardConfig) Reset() {
	*x = FiCoinsRewardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiCoinsRewardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiCoinsRewardConfig) ProtoMessage() {}

func (x *FiCoinsRewardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiCoinsRewardConfig.ProtoReflect.Descriptor instead.
func (*FiCoinsRewardConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{57}
}

func (x *FiCoinsRewardConfig) GetIsFiPoints() common.BooleanEnum {
	if x != nil {
		return x.IsFiPoints
	}
	return common.BooleanEnum(0)
}

type EarningPotential struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// icon to be shown along with the earning potential
	Icon string `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	// denotes the potential reward units that the user can earn because of the offer
	PotentialEarnings string `protobuf:"bytes,2,opt,name=potential_earnings,json=potentialEarnings,proto3" json:"potential_earnings,omitempty"`
	// any subtext that may be required to be shown below the earning potential
	SubText string `protobuf:"bytes,3,opt,name=sub_text,json=subText,proto3" json:"sub_text,omitempty"`
}

func (x *EarningPotential) Reset() {
	*x = EarningPotential{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EarningPotential) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EarningPotential) ProtoMessage() {}

func (x *EarningPotential) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EarningPotential.ProtoReflect.Descriptor instead.
func (*EarningPotential) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{58}
}

func (x *EarningPotential) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *EarningPotential) GetPotentialEarnings() string {
	if x != nil {
		return x.PotentialEarnings
	}
	return ""
}

func (x *EarningPotential) GetSubText() string {
	if x != nil {
		return x.SubText
	}
	return ""
}

type IconAndText struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// URL of icon
	Icon string `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	// text to be shown along with icon
	Text string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
}

func (x *IconAndText) Reset() {
	*x = IconAndText{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IconAndText) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IconAndText) ProtoMessage() {}

func (x *IconAndText) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IconAndText.ProtoReflect.Descriptor instead.
func (*IconAndText) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{59}
}

func (x *IconAndText) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *IconAndText) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type WaysToEarnRewardsV2ScreenConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title to be shown in V2 design
	// figma - https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=3899-9767&mode=design&t=IL3MWYaoK4VtPIHZ-0
	OfferRowTitle string `protobuf:"bytes,1,opt,name=offer_row_title,json=offerRowTitle,proto3" json:"offer_row_title,omitempty"`
	// main_icon_small stores the URL to icon that is to be shown on reward offer row
	OfferRowIcon string `protobuf:"bytes,2,opt,name=offer_row_icon,json=offerRowIcon,proto3" json:"offer_row_icon,omitempty"`
	// main_icon_large stores the URL to icon that is to be shown on reward offer details screen (large image)
	OfferDetailsIcon string `protobuf:"bytes,3,opt,name=offer_details_icon,json=offerDetailsIcon,proto3" json:"offer_details_icon,omitempty"`
	// background color of the main image header
	// figma - https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=3899-10616&mode=design&t=IL3MWYaoK4VtPIHZ-0
	OfferDetailsHeaderBgColor string `protobuf:"bytes,4,opt,name=offer_details_header_bg_color,json=offerDetailsHeaderBgColor,proto3" json:"offer_details_header_bg_color,omitempty"`
	// stores the earning potential to be shown on the reward offer row in WaysToEarn screen in V2 design
	OfferRowEarningPotential *EarningPotential `protobuf:"bytes,5,opt,name=offer_row_earning_potential,json=offerRowEarningPotential,proto3" json:"offer_row_earning_potential,omitempty"`
	// offer_row_sub_section stores any additional details we may want to show in a reward offer row in V2 design
	// eg. "Use 249 Fi-coins to avail offer"
	OfferRowSubSection *IconAndText `protobuf:"bytes,6,opt,name=offer_row_sub_section,json=offerRowSubSection,proto3" json:"offer_row_sub_section,omitempty"`
	// short text to summarise the earning potential of the reward offer in the offer details screen
	// figma - https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=3510-7906&mode=design&t=IL3MWYaoK4VtPIHZ-0
	OfferDetailsEarningPotentialSummary *IconAndText `protobuf:"bytes,7,opt,name=offer_details_earning_potential_summary,json=offerDetailsEarningPotentialSummary,proto3" json:"offer_details_earning_potential_summary,omitempty"`
	// tag to be shown in upper right corner of reward offer row
	// figma - https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=3899-9767&mode=design&t=IL3MWYaoK4VtPIHZ-0
	OfferRowTopLeftTag *WaysToEarnRewardsV2ScreenConfig_Tag `protobuf:"bytes,8,opt,name=offer_row_top_left_tag,json=offerRowTopLeftTag,proto3" json:"offer_row_top_left_tag,omitempty"`
	// tag to be shown in upper the offer details screen
	// figma - https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=3510-7939&mode=design&t=IL3MWYaoK4VtPIHZ-0
	OfferDetailsTag *WaysToEarnRewardsV2ScreenConfig_Tag `protobuf:"bytes,9,opt,name=offer_details_tag,json=offerDetailsTag,proto3" json:"offer_details_tag,omitempty"`
}

func (x *WaysToEarnRewardsV2ScreenConfig) Reset() {
	*x = WaysToEarnRewardsV2ScreenConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaysToEarnRewardsV2ScreenConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaysToEarnRewardsV2ScreenConfig) ProtoMessage() {}

func (x *WaysToEarnRewardsV2ScreenConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaysToEarnRewardsV2ScreenConfig.ProtoReflect.Descriptor instead.
func (*WaysToEarnRewardsV2ScreenConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{60}
}

func (x *WaysToEarnRewardsV2ScreenConfig) GetOfferRowTitle() string {
	if x != nil {
		return x.OfferRowTitle
	}
	return ""
}

func (x *WaysToEarnRewardsV2ScreenConfig) GetOfferRowIcon() string {
	if x != nil {
		return x.OfferRowIcon
	}
	return ""
}

func (x *WaysToEarnRewardsV2ScreenConfig) GetOfferDetailsIcon() string {
	if x != nil {
		return x.OfferDetailsIcon
	}
	return ""
}

func (x *WaysToEarnRewardsV2ScreenConfig) GetOfferDetailsHeaderBgColor() string {
	if x != nil {
		return x.OfferDetailsHeaderBgColor
	}
	return ""
}

func (x *WaysToEarnRewardsV2ScreenConfig) GetOfferRowEarningPotential() *EarningPotential {
	if x != nil {
		return x.OfferRowEarningPotential
	}
	return nil
}

func (x *WaysToEarnRewardsV2ScreenConfig) GetOfferRowSubSection() *IconAndText {
	if x != nil {
		return x.OfferRowSubSection
	}
	return nil
}

func (x *WaysToEarnRewardsV2ScreenConfig) GetOfferDetailsEarningPotentialSummary() *IconAndText {
	if x != nil {
		return x.OfferDetailsEarningPotentialSummary
	}
	return nil
}

func (x *WaysToEarnRewardsV2ScreenConfig) GetOfferRowTopLeftTag() *WaysToEarnRewardsV2ScreenConfig_Tag {
	if x != nil {
		return x.OfferRowTopLeftTag
	}
	return nil
}

func (x *WaysToEarnRewardsV2ScreenConfig) GetOfferDetailsTag() *WaysToEarnRewardsV2ScreenConfig_Tag {
	if x != nil {
		return x.OfferDetailsTag
	}
	return nil
}

// stores the different configured unit caps on reward units generated from projections.
// NOTE : these caps only apply to the generation reward computation from already generated projections currently
// and not while projection generation itself.
type RewardUnitsGeneratedFromProjectionUnitCaps struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OfferTypeToUnitCapsMap  map[string]uint32 `protobuf:"bytes,1,rep,name=offer_type_to_unit_caps_map,json=offerTypeToUnitCapsMap,proto3" json:"offer_type_to_unit_caps_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ActionTypeToUnitCapsMap map[string]uint32 `protobuf:"bytes,2,rep,name=action_type_to_unit_caps_map,json=actionTypeToUnitCapsMap,proto3" json:"action_type_to_unit_caps_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	OfferIdToUnitCapsMap    map[string]uint32 `protobuf:"bytes,3,rep,name=offer_id_to_unit_caps_map,json=offerIdToUnitCapsMap,proto3" json:"offer_id_to_unit_caps_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	DailyUnitsCap           uint32            `protobuf:"varint,4,opt,name=daily_units_cap,json=dailyUnitsCap,proto3" json:"daily_units_cap,omitempty"`
}

func (x *RewardUnitsGeneratedFromProjectionUnitCaps) Reset() {
	*x = RewardUnitsGeneratedFromProjectionUnitCaps{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardUnitsGeneratedFromProjectionUnitCaps) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardUnitsGeneratedFromProjectionUnitCaps) ProtoMessage() {}

func (x *RewardUnitsGeneratedFromProjectionUnitCaps) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardUnitsGeneratedFromProjectionUnitCaps.ProtoReflect.Descriptor instead.
func (*RewardUnitsGeneratedFromProjectionUnitCaps) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{61}
}

func (x *RewardUnitsGeneratedFromProjectionUnitCaps) GetOfferTypeToUnitCapsMap() map[string]uint32 {
	if x != nil {
		return x.OfferTypeToUnitCapsMap
	}
	return nil
}

func (x *RewardUnitsGeneratedFromProjectionUnitCaps) GetActionTypeToUnitCapsMap() map[string]uint32 {
	if x != nil {
		return x.ActionTypeToUnitCapsMap
	}
	return nil
}

func (x *RewardUnitsGeneratedFromProjectionUnitCaps) GetOfferIdToUnitCapsMap() map[string]uint32 {
	if x != nil {
		return x.OfferIdToUnitCapsMap
	}
	return nil
}

func (x *RewardUnitsGeneratedFromProjectionUnitCaps) GetDailyUnitsCap() uint32 {
	if x != nil {
		return x.DailyUnitsCap
	}
	return 0
}

type AdditionalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stores watson config related to the issue category corresponding to which
	// a ticket needs to be created when something goes wrong in a reward created
	// against this reward offer
	WatsonMeta *WatsonMeta `protobuf:"bytes,1,opt,name=watson_meta,json=watsonMeta,proto3" json:"watson_meta,omitempty"`
}

func (x *AdditionalDetails) Reset() {
	*x = AdditionalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalDetails) ProtoMessage() {}

func (x *AdditionalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalDetails.ProtoReflect.Descriptor instead.
func (*AdditionalDetails) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{62}
}

func (x *AdditionalDetails) GetWatsonMeta() *WatsonMeta {
	if x != nil {
		return x.WatsonMeta
	}
	return nil
}

// stores watson config related to the issue category corresponding to which
// a ticket needs to be created when something goes wrong in a reward created
// against this reward offer
type WatsonMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// this should be set to true when we want to create a watson event
	// when a reward is not processed or generated(only valid cases)
	// against this reward offer
	ShouldCreateWatsonEvent bool   `protobuf:"varint,1,opt,name=should_create_watson_event,json=shouldCreateWatsonEvent,proto3" json:"should_create_watson_event,omitempty"`
	IssueCategoryId         string `protobuf:"bytes,2,opt,name=issue_category_id,json=issueCategoryId,proto3" json:"issue_category_id,omitempty"`
}

func (x *WatsonMeta) Reset() {
	*x = WatsonMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatsonMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatsonMeta) ProtoMessage() {}

func (x *WatsonMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatsonMeta.ProtoReflect.Descriptor instead.
func (*WatsonMeta) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{63}
}

func (x *WatsonMeta) GetShouldCreateWatsonEvent() bool {
	if x != nil {
		return x.ShouldCreateWatsonEvent
	}
	return false
}

func (x *WatsonMeta) GetIssueCategoryId() string {
	if x != nil {
		return x.IssueCategoryId
	}
	return ""
}

type ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// fraction of unit (calculated using expression) to be taken as start of current range.
	UnitPercentageStart float32 `protobuf:"fixed32,1,opt,name=unit_percentage_start,json=unitPercentageStart,proto3" json:"unit_percentage_start,omitempty"`
	// fraction of unit (calculated using expression) to be taken as end of current range.
	UnitPercentageEnd float32 `protobuf:"fixed32,2,opt,name=unit_percentage_end,json=unitPercentageEnd,proto3" json:"unit_percentage_end,omitempty"`
	// probability percentage that final reward units are in current range.
	Percentage float32 `protobuf:"fixed32,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit) Reset() {
	*x = ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit) ProtoMessage() {}

func (x *ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit.ProtoReflect.Descriptor instead.
func (*ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{7, 0}
}

func (x *ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit) GetUnitPercentageStart() float32 {
	if x != nil {
		return x.UnitPercentageStart
	}
	return 0
}

func (x *ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit) GetUnitPercentageEnd() float32 {
	if x != nil {
		return x.UnitPercentageEnd
	}
	return 0
}

func (x *ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit) GetPercentage() float32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

// Each config unit contains a condition expression and an ExpressionRangeProbabilityConfig.
// If the condition is satisfied then corresponding expression_range_probability_config of that config unit is used to calculate reward units.
type ConditionalExpressionRangeProbabilityConfig_ConfigUnit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// condition expression like 'TXN_AMOUNT>100'
	ConditionExpression string `protobuf:"bytes,1,opt,name=condition_expression,json=conditionExpression,proto3" json:"condition_expression,omitempty"`
	// ExpressionRangeProbabilityConfig used to calculate reward units if condition in config unit is satisfied.
	ExpressionRangeProbabilityConfig *ExpressionRangeProbabilityConfig `protobuf:"bytes,2,opt,name=expression_range_probability_config,json=expressionRangeProbabilityConfig,proto3" json:"expression_range_probability_config,omitempty"`
}

func (x *ConditionalExpressionRangeProbabilityConfig_ConfigUnit) Reset() {
	*x = ConditionalExpressionRangeProbabilityConfig_ConfigUnit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConditionalExpressionRangeProbabilityConfig_ConfigUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionalExpressionRangeProbabilityConfig_ConfigUnit) ProtoMessage() {}

func (x *ConditionalExpressionRangeProbabilityConfig_ConfigUnit) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionalExpressionRangeProbabilityConfig_ConfigUnit.ProtoReflect.Descriptor instead.
func (*ConditionalExpressionRangeProbabilityConfig_ConfigUnit) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ConditionalExpressionRangeProbabilityConfig_ConfigUnit) GetConditionExpression() string {
	if x != nil {
		return x.ConditionExpression
	}
	return ""
}

func (x *ConditionalExpressionRangeProbabilityConfig_ConfigUnit) GetExpressionRangeProbabilityConfig() *ExpressionRangeProbabilityConfig {
	if x != nil {
		return x.ExpressionRangeProbabilityConfig
	}
	return nil
}

// Each config unit contains a condition expression and a units_config (oneof).
// If the condition is satisfied then corresponding units_config of that config unit is used to calculate reward units.
// further, these calculated reward units will be utilised for performing the mathematical operation with the previous value.
type ConditionalExpressionBoosterConfig_ConfigUnit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// condition expression like 'TXN_AMOUNT>100'
	ConditionExpression string `protobuf:"bytes,1,opt,name=condition_expression,json=conditionExpression,proto3" json:"condition_expression,omitempty"`
	// fixed operation to be performed with the previous-value (calculated reward value) that will be fed to this config.
	// this operation will be performed on the units after they are generated via the units_config below
	OperationWithPreviousValue MathematicalOperation                                         `protobuf:"varint,2,opt,name=operation_with_previous_value,json=operationWithPreviousValue,proto3,enum=rewardoffers.MathematicalOperation" json:"operation_with_previous_value,omitempty"`
	DisplayDetails             *ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails `protobuf:"bytes,3,opt,name=display_details,json=displayDetails,proto3" json:"display_details,omitempty"`
	// upper cap to be applied on the units computed after the mathematical operation (denoted by operation_with_previous_value field) is applied on the previous value.
	// e.g if the value after applying the mathematical operation was 1000 units and units upper cap was configured to 500 units, then final value would be 500 units.
	// for now, if this value is zero then the given units capping shouldn't be applied to ensure backward compatibility
	// todo (team) : remove the optional check and treat this param as mandatory in units calculation logic after the existing rewardOffers are migrated to use this field.
	UnitsUpperCapAfterOperationWithPreviousValue int32 `protobuf:"varint,4,opt,name=units_upper_cap_after_operation_with_previous_value,json=unitsUpperCapAfterOperationWithPreviousValue,proto3" json:"units_upper_cap_after_operation_with_previous_value,omitempty"`
	// Types that are assignable to UnitsConfig:
	//
	//	*ConditionalExpressionBoosterConfig_ConfigUnit_ExpressionRangeProbabilityConfig
	UnitsConfig isConditionalExpressionBoosterConfig_ConfigUnit_UnitsConfig `protobuf_oneof:"units_config"`
}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit) Reset() {
	*x = ConditionalExpressionBoosterConfig_ConfigUnit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionalExpressionBoosterConfig_ConfigUnit) ProtoMessage() {}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionalExpressionBoosterConfig_ConfigUnit.ProtoReflect.Descriptor instead.
func (*ConditionalExpressionBoosterConfig_ConfigUnit) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit) GetConditionExpression() string {
	if x != nil {
		return x.ConditionExpression
	}
	return ""
}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit) GetOperationWithPreviousValue() MathematicalOperation {
	if x != nil {
		return x.OperationWithPreviousValue
	}
	return MathematicalOperation_MATHEMATICAL_OPERATION_UNSPECIFIED
}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit) GetDisplayDetails() *ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails {
	if x != nil {
		return x.DisplayDetails
	}
	return nil
}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit) GetUnitsUpperCapAfterOperationWithPreviousValue() int32 {
	if x != nil {
		return x.UnitsUpperCapAfterOperationWithPreviousValue
	}
	return 0
}

func (m *ConditionalExpressionBoosterConfig_ConfigUnit) GetUnitsConfig() isConditionalExpressionBoosterConfig_ConfigUnit_UnitsConfig {
	if m != nil {
		return m.UnitsConfig
	}
	return nil
}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit) GetExpressionRangeProbabilityConfig() *ExpressionRangeProbabilityConfig {
	if x, ok := x.GetUnitsConfig().(*ConditionalExpressionBoosterConfig_ConfigUnit_ExpressionRangeProbabilityConfig); ok {
		return x.ExpressionRangeProbabilityConfig
	}
	return nil
}

type isConditionalExpressionBoosterConfig_ConfigUnit_UnitsConfig interface {
	isConditionalExpressionBoosterConfig_ConfigUnit_UnitsConfig()
}

type ConditionalExpressionBoosterConfig_ConfigUnit_ExpressionRangeProbabilityConfig struct {
	// using expression based range probability config to calculated the reward units
	ExpressionRangeProbabilityConfig *ExpressionRangeProbabilityConfig `protobuf:"bytes,9,opt,name=expression_range_probability_config,json=expressionRangeProbabilityConfig,proto3,oneof"`
}

func (*ConditionalExpressionBoosterConfig_ConfigUnit_ExpressionRangeProbabilityConfig) isConditionalExpressionBoosterConfig_ConfigUnit_UnitsConfig() {
}

type ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tags associated with booster
	Tags []rewards.RewardTag `protobuf:"varint,1,rep,packed,name=tags,proto3,enum=rewards.RewardTag" json:"tags,omitempty"`
	// title for the booster getting applied, for e.g. "2x boost applied for Fi Plus accounts"
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// title color
	TitleColor string `protobuf:"bytes,3,opt,name=title_color,json=titleColor,proto3" json:"title_color,omitempty"`
	// bg color for displaying booster details
	BgColor string `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails) Reset() {
	*x = ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails) ProtoMessage() {}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails.ProtoReflect.Descriptor instead.
func (*ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{9, 0, 0}
}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails) GetTags() []rewards.RewardTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails) GetTitleColor() string {
	if x != nil {
		return x.TitleColor
	}
	return ""
}

func (x *ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

// Using DeltaDerivation reward value is computed as {reward value of seed_reward_option} + delta
type DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Delta int32 `protobuf:"varint,1,opt,name=delta,proto3" json:"delta,omitempty"`
}

func (x *DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig) Reset() {
	*x = DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig) ProtoMessage() {}

func (x *DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig.ProtoReflect.Descriptor instead.
func (*DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{10, 0}
}

func (x *DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig) GetDelta() int32 {
	if x != nil {
		return x.Delta
	}
	return 0
}

// Using MultiplierDerivation reward value is computed as {unit value of seed_reward_option} * multiplier
type DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Multiplier float32 `protobuf:"fixed32,1,opt,name=multiplier,proto3" json:"multiplier,omitempty"`
}

func (x *DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig) Reset() {
	*x = DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig) ProtoMessage() {}

func (x *DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig.ProtoReflect.Descriptor instead.
func (*DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{10, 1}
}

func (x *DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig) GetMultiplier() float32 {
	if x != nil {
		return x.Multiplier
	}
	return 0
}

type RewardConfigOption_Display struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BeforeClaimTextExpression string                                            `protobuf:"bytes,1,opt,name=before_claim_text_expression,json=beforeClaimTextExpression,proto3" json:"before_claim_text_expression,omitempty"`
	AfterClaimTextExpression  string                                            `protobuf:"bytes,2,opt,name=after_claim_text_expression,json=afterClaimTextExpression,proto3" json:"after_claim_text_expression,omitempty"`
	Icon                      string                                            `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	BgColor                   string                                            `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	HtmlFormattedDetails      []*RewardConfigOption_Display_HtmlFormattedDetail `protobuf:"bytes,6,rep,name=html_formatted_details,json=htmlFormattedDetails,proto3" json:"html_formatted_details,omitempty"`
	// banner text displayed on top of the reward option card at the time of choosing the option e.g. "10% EXTRA REWARD"
	// Design : https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=5953%3A65408
	// can be empty if banner text does not needs to be displayed.
	BeforeClaimBannerText string `protobuf:"bytes,7,opt,name=before_claim_banner_text,json=beforeClaimBannerText,proto3" json:"before_claim_banner_text,omitempty"`
	// tags to be propagated to the reward.
	// current use case involves display purposes.
	Tags []rewards.RewardTag `protobuf:"varint,8,rep,packed,name=tags,proto3,enum=rewards.RewardTag" json:"tags,omitempty"`
}

func (x *RewardConfigOption_Display) Reset() {
	*x = RewardConfigOption_Display{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardConfigOption_Display) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardConfigOption_Display) ProtoMessage() {}

func (x *RewardConfigOption_Display) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardConfigOption_Display.ProtoReflect.Descriptor instead.
func (*RewardConfigOption_Display) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{11, 0}
}

func (x *RewardConfigOption_Display) GetBeforeClaimTextExpression() string {
	if x != nil {
		return x.BeforeClaimTextExpression
	}
	return ""
}

func (x *RewardConfigOption_Display) GetAfterClaimTextExpression() string {
	if x != nil {
		return x.AfterClaimTextExpression
	}
	return ""
}

func (x *RewardConfigOption_Display) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *RewardConfigOption_Display) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *RewardConfigOption_Display) GetHtmlFormattedDetails() []*RewardConfigOption_Display_HtmlFormattedDetail {
	if x != nil {
		return x.HtmlFormattedDetails
	}
	return nil
}

func (x *RewardConfigOption_Display) GetBeforeClaimBannerText() string {
	if x != nil {
		return x.BeforeClaimBannerText
	}
	return ""
}

func (x *RewardConfigOption_Display) GetTags() []rewards.RewardTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

// ProjectionsFetchFilters contains the filters that can be applied while fetching the reward projections for
// generating an actual reward.
type RewardConfigOption_ProjectionsQueryFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// action types that would have generated projection of rewards using which we want to compute the reward units
	CollectedDataTypes []rewards.CollectedDataType `protobuf:"varint,1,rep,packed,name=collected_data_types,json=collectedDataTypes,proto3,enum=rewards.CollectedDataType" json:"collected_data_types,omitempty"`
	// offer types that would have generated projection of rewards using which we want to compute the reward units
	RewardOfferTypes []rewards.RewardOfferType `protobuf:"varint,2,rep,packed,name=reward_offer_types,json=rewardOfferTypes,proto3,enum=rewards.RewardOfferType" json:"reward_offer_types,omitempty"`
	// list of offer IDs that would have generated projection of rewards using which we want to compute the reward units
	RewardOfferIds []string `protobuf:"bytes,3,rep,name=reward_offer_ids,json=rewardOfferIds,proto3" json:"reward_offer_ids,omitempty"`
}

func (x *RewardConfigOption_ProjectionsQueryFilters) Reset() {
	*x = RewardConfigOption_ProjectionsQueryFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardConfigOption_ProjectionsQueryFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardConfigOption_ProjectionsQueryFilters) ProtoMessage() {}

func (x *RewardConfigOption_ProjectionsQueryFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardConfigOption_ProjectionsQueryFilters.ProtoReflect.Descriptor instead.
func (*RewardConfigOption_ProjectionsQueryFilters) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{11, 1}
}

func (x *RewardConfigOption_ProjectionsQueryFilters) GetCollectedDataTypes() []rewards.CollectedDataType {
	if x != nil {
		return x.CollectedDataTypes
	}
	return nil
}

func (x *RewardConfigOption_ProjectionsQueryFilters) GetRewardOfferTypes() []rewards.RewardOfferType {
	if x != nil {
		return x.RewardOfferTypes
	}
	return nil
}

func (x *RewardConfigOption_ProjectionsQueryFilters) GetRewardOfferIds() []string {
	if x != nil {
		return x.RewardOfferIds
	}
	return nil
}

// html formatted details
// e.g for a Gift Hamper Reward, a detail entry can look like
//
//	{
//	 header : "Description"
//	 body :"<b>Gift Box Contains :</b> <br>
//	 1 Toasted Millet Muesli <br>
//	 1 Choco chip oat clusters and Ragi flakes with banana <br>
//	 1 Box of 12 Crunchy granola bars - dark chocolate & espresso."
//	}
type RewardConfigOption_Display_HtmlFormattedDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header string `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Body   string `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *RewardConfigOption_Display_HtmlFormattedDetail) Reset() {
	*x = RewardConfigOption_Display_HtmlFormattedDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardConfigOption_Display_HtmlFormattedDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardConfigOption_Display_HtmlFormattedDetail) ProtoMessage() {}

func (x *RewardConfigOption_Display_HtmlFormattedDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardConfigOption_Display_HtmlFormattedDetail.ProtoReflect.Descriptor instead.
func (*RewardConfigOption_Display_HtmlFormattedDetail) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{11, 0, 0}
}

func (x *RewardConfigOption_Display_HtmlFormattedDetail) GetHeader() string {
	if x != nil {
		return x.Header
	}
	return ""
}

func (x *RewardConfigOption_Display_HtmlFormattedDetail) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

// todo: add display details specific to boosters
// before and after text fields expressions. Why expressions? to convey that we'd support placeholders as well
type BoosterConfig_DisplayDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BoosterConfig_DisplayDetails) Reset() {
	*x = BoosterConfig_DisplayDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoosterConfig_DisplayDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoosterConfig_DisplayDetails) ProtoMessage() {}

func (x *BoosterConfig_DisplayDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoosterConfig_DisplayDetails.ProtoReflect.Descriptor instead.
func (*BoosterConfig_DisplayDetails) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{13, 0}
}

// RewardDisplayMeta contains display related metadata for reward.
type RewardMeta_RewardDisplayMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// background image to display on reward tile when reward is in unclaimed state.
	TileBgImageBeforeClaim string `protobuf:"bytes,1,opt,name=tile_bg_image_before_claim,json=tileBgImageBeforeClaim,proto3" json:"tile_bg_image_before_claim,omitempty"`
	// background image to display on reward tile after reward is claimed.
	TileBgImageAfterClaim string `protobuf:"bytes,2,opt,name=tile_bg_image_after_claim,json=tileBgImageAfterClaim,proto3" json:"tile_bg_image_after_claim,omitempty"`
	// theme for displaying reward tile on APP. Based on theme, the APP decides the images to show on reward tile
	// and corresponding animation to play while claiming the reward
	RewardTileThemeType rewards.RewardTileThemeType `protobuf:"varint,3,opt,name=reward_tile_theme_type,json=rewardTileThemeType,proto3,enum=rewards.RewardTileThemeType" json:"reward_tile_theme_type,omitempty"`
	// attributes to be used for nudging(upgrade tier, spend fi-coins etc) the user at the end of the claim flow.
	// e.g. "TIER" -> the user will be nudged for tier upgrade if possible after the claim flow.
	// e.g. "SALARY_PROGRAM" -> the user will be nudged to convert to salary account after the claim flow
	PostClaimNudgeAttributes []string `protobuf:"bytes,4,rep,name=post_claim_nudge_attributes,json=postClaimNudgeAttributes,proto3" json:"post_claim_nudge_attributes,omitempty"`
	// flag to decide whether we want the flower animation to be skippable or not. it's named this way to keep the default
	// value as 'false' and only make animation un-skippable if offer is configured that way
	IsAnimationUnskippable bool `protobuf:"varint,21,opt,name=is_animation_unskippable,json=isAnimationUnskippable,proto3" json:"is_animation_unskippable,omitempty"`
	// flag to decide whether we'd want to skip the whole animation altogether
	// use case: in case booster is applied, we'd want to skip animation to give time for further frames
	SkipAnimation bool `protobuf:"varint,22,opt,name=skip_animation,json=skipAnimation,proto3" json:"skip_animation,omitempty"`
	// Determines how fast the money plant animation should run
	// note: The expected default value is 1.5, 1.0 for normal speed, 1.5 for faster, 0.5 for slower and so on
	AnimationSpeed float32 `protobuf:"fixed32,23,opt,name=animation_speed,json=animationSpeed,proto3" json:"animation_speed,omitempty"`
}

func (x *RewardMeta_RewardDisplayMeta) Reset() {
	*x = RewardMeta_RewardDisplayMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardMeta_RewardDisplayMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardMeta_RewardDisplayMeta) ProtoMessage() {}

func (x *RewardMeta_RewardDisplayMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardMeta_RewardDisplayMeta.ProtoReflect.Descriptor instead.
func (*RewardMeta_RewardDisplayMeta) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{21, 0}
}

func (x *RewardMeta_RewardDisplayMeta) GetTileBgImageBeforeClaim() string {
	if x != nil {
		return x.TileBgImageBeforeClaim
	}
	return ""
}

func (x *RewardMeta_RewardDisplayMeta) GetTileBgImageAfterClaim() string {
	if x != nil {
		return x.TileBgImageAfterClaim
	}
	return ""
}

func (x *RewardMeta_RewardDisplayMeta) GetRewardTileThemeType() rewards.RewardTileThemeType {
	if x != nil {
		return x.RewardTileThemeType
	}
	return rewards.RewardTileThemeType(0)
}

func (x *RewardMeta_RewardDisplayMeta) GetPostClaimNudgeAttributes() []string {
	if x != nil {
		return x.PostClaimNudgeAttributes
	}
	return nil
}

func (x *RewardMeta_RewardDisplayMeta) GetIsAnimationUnskippable() bool {
	if x != nil {
		return x.IsAnimationUnskippable
	}
	return false
}

func (x *RewardMeta_RewardDisplayMeta) GetSkipAnimation() bool {
	if x != nil {
		return x.SkipAnimation
	}
	return false
}

func (x *RewardMeta_RewardDisplayMeta) GetAnimationSpeed() float32 {
	if x != nil {
		return x.AnimationSpeed
	}
	return 0
}

type RewardUnitsCapAggregate_RewardUnitsCap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardType rewards.RewardType `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	Units      uint32             `protobuf:"varint,2,opt,name=units,proto3" json:"units,omitempty"`
}

func (x *RewardUnitsCapAggregate_RewardUnitsCap) Reset() {
	*x = RewardUnitsCapAggregate_RewardUnitsCap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardUnitsCapAggregate_RewardUnitsCap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardUnitsCapAggregate_RewardUnitsCap) ProtoMessage() {}

func (x *RewardUnitsCapAggregate_RewardUnitsCap) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardUnitsCapAggregate_RewardUnitsCap.ProtoReflect.Descriptor instead.
func (*RewardUnitsCapAggregate_RewardUnitsCap) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{24, 0}
}

func (x *RewardUnitsCapAggregate_RewardUnitsCap) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

func (x *RewardUnitsCapAggregate_RewardUnitsCap) GetUnits() uint32 {
	if x != nil {
		return x.Units
	}
	return 0
}

type DisplayMeta_Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text      string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	TextColor string `protobuf:"bytes,2,opt,name=text_color,json=textColor,proto3" json:"text_color,omitempty"`
	BgColor   string `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *DisplayMeta_Tag) Reset() {
	*x = DisplayMeta_Tag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayMeta_Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayMeta_Tag) ProtoMessage() {}

func (x *DisplayMeta_Tag) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayMeta_Tag.ProtoReflect.Descriptor instead.
func (*DisplayMeta_Tag) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{31, 2}
}

func (x *DisplayMeta_Tag) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *DisplayMeta_Tag) GetTextColor() string {
	if x != nil {
		return x.TextColor
	}
	return ""
}

func (x *DisplayMeta_Tag) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

type DisplayMeta_InfoPoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// info text
	Text *common.Text `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// optional, for deep-linking text to a screen/flow
	Deeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *DisplayMeta_InfoPoint) Reset() {
	*x = DisplayMeta_InfoPoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayMeta_InfoPoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayMeta_InfoPoint) ProtoMessage() {}

func (x *DisplayMeta_InfoPoint) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayMeta_InfoPoint.ProtoReflect.Descriptor instead.
func (*DisplayMeta_InfoPoint) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{31, 3}
}

func (x *DisplayMeta_InfoPoint) GetText() *common.Text {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *DisplayMeta_InfoPoint) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type EarnedRewardNotificationConfig_NotificationConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the type of notification to be sent.
	NotificationType RewardNotificationType `protobuf:"varint,1,opt,name=notification_type,json=notificationType,proto3,enum=rewardoffers.RewardNotificationType" json:"notification_type,omitempty"`
	// denotes the max number of rewards for which the notifications should be triggered e.g if this value is 2 then it specifies that the notification should be triggered for only the first 2 rewards generated from the current rewardOffer
	// note : 0 denotes that no cap is configured and so notifications should not be capped.
	RewardCountLevelNotificationCap uint32 `protobuf:"varint,2,opt,name=reward_count_level_notification_cap,json=rewardCountLevelNotificationCap,proto3" json:"reward_count_level_notification_cap,omitempty"`
	// defines the delay time in sending the notification, if not set then delay time will be considered as 0 by default
	NotificationDelayTime *rewards.RewardTimeConfig `protobuf:"bytes,3,opt,name=notification_delay_time,json=notificationDelayTime,proto3" json:"notification_delay_time,omitempty"`
	// metadata required for sending a particular type of notification
	NotificationMetadata *RewardNotificationMetadata `protobuf:"bytes,4,opt,name=notification_metadata,json=notificationMetadata,proto3" json:"notification_metadata,omitempty"`
	// contains the content of the notification, like title, body
	Content *Content `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *EarnedRewardNotificationConfig_NotificationConfig) Reset() {
	*x = EarnedRewardNotificationConfig_NotificationConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EarnedRewardNotificationConfig_NotificationConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EarnedRewardNotificationConfig_NotificationConfig) ProtoMessage() {}

func (x *EarnedRewardNotificationConfig_NotificationConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EarnedRewardNotificationConfig_NotificationConfig.ProtoReflect.Descriptor instead.
func (*EarnedRewardNotificationConfig_NotificationConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{34, 0}
}

func (x *EarnedRewardNotificationConfig_NotificationConfig) GetNotificationType() RewardNotificationType {
	if x != nil {
		return x.NotificationType
	}
	return RewardNotificationType_UNSPECIFIED_NOTIFICATION_TYPE
}

func (x *EarnedRewardNotificationConfig_NotificationConfig) GetRewardCountLevelNotificationCap() uint32 {
	if x != nil {
		return x.RewardCountLevelNotificationCap
	}
	return 0
}

func (x *EarnedRewardNotificationConfig_NotificationConfig) GetNotificationDelayTime() *rewards.RewardTimeConfig {
	if x != nil {
		return x.NotificationDelayTime
	}
	return nil
}

func (x *EarnedRewardNotificationConfig_NotificationConfig) GetNotificationMetadata() *RewardNotificationMetadata {
	if x != nil {
		return x.NotificationMetadata
	}
	return nil
}

func (x *EarnedRewardNotificationConfig_NotificationConfig) GetContent() *Content {
	if x != nil {
		return x.Content
	}
	return nil
}

type RewardProcessingSuccessfulNotificationConfig_NotificationConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the type of notification to be sent.
	NotificationType RewardNotificationType `protobuf:"varint,1,opt,name=notification_type,json=notificationType,proto3,enum=rewardoffers.RewardNotificationType" json:"notification_type,omitempty"`
	// defines the delay time in sending the notification, if not set then delay time will be considered as 0 by default
	NotificationDelayTime *rewards.RewardTimeConfig `protobuf:"bytes,2,opt,name=notification_delay_time,json=notificationDelayTime,proto3" json:"notification_delay_time,omitempty"`
	// contains the content of the notification, like title, body
	Content *Content `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	// metadata required for sending a particular type of notification
	NotificationMetadata *RewardNotificationMetadata `protobuf:"bytes,4,opt,name=notification_metadata,json=notificationMetadata,proto3" json:"notification_metadata,omitempty"`
}

func (x *RewardProcessingSuccessfulNotificationConfig_NotificationConfig) Reset() {
	*x = RewardProcessingSuccessfulNotificationConfig_NotificationConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardProcessingSuccessfulNotificationConfig_NotificationConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardProcessingSuccessfulNotificationConfig_NotificationConfig) ProtoMessage() {}

func (x *RewardProcessingSuccessfulNotificationConfig_NotificationConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardProcessingSuccessfulNotificationConfig_NotificationConfig.ProtoReflect.Descriptor instead.
func (*RewardProcessingSuccessfulNotificationConfig_NotificationConfig) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{35, 0}
}

func (x *RewardProcessingSuccessfulNotificationConfig_NotificationConfig) GetNotificationType() RewardNotificationType {
	if x != nil {
		return x.NotificationType
	}
	return RewardNotificationType_UNSPECIFIED_NOTIFICATION_TYPE
}

func (x *RewardProcessingSuccessfulNotificationConfig_NotificationConfig) GetNotificationDelayTime() *rewards.RewardTimeConfig {
	if x != nil {
		return x.NotificationDelayTime
	}
	return nil
}

func (x *RewardProcessingSuccessfulNotificationConfig_NotificationConfig) GetContent() *Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *RewardProcessingSuccessfulNotificationConfig_NotificationConfig) GetNotificationMetadata() *RewardNotificationMetadata {
	if x != nil {
		return x.NotificationMetadata
	}
	return nil
}

type RewardOfferResponse_ValidationFailureInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// failure message for validation failure for create RewardOffer request
	FailureMessage string `protobuf:"bytes,1,opt,name=failure_message,json=failureMessage,proto3" json:"failure_message,omitempty"`
}

func (x *RewardOfferResponse_ValidationFailureInfo) Reset() {
	*x = RewardOfferResponse_ValidationFailureInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOfferResponse_ValidationFailureInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOfferResponse_ValidationFailureInfo) ProtoMessage() {}

func (x *RewardOfferResponse_ValidationFailureInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOfferResponse_ValidationFailureInfo.ProtoReflect.Descriptor instead.
func (*RewardOfferResponse_ValidationFailureInfo) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{41, 0}
}

func (x *RewardOfferResponse_ValidationFailureInfo) GetFailureMessage() string {
	if x != nil {
		return x.FailureMessage
	}
	return ""
}

// useful for filtering relevant offers
type GetRewardOffersForScreenRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tags          []RewardOfferTag          `protobuf:"varint,1,rep,packed,name=tags,proto3,enum=rewardoffers.RewardOfferTag" json:"tags,omitempty"`
	OfferTypes    []rewards.RewardOfferType `protobuf:"varint,2,rep,packed,name=offer_types,json=offerTypes,proto3,enum=rewards.RewardOfferType" json:"offer_types,omitempty"`
	RewardOfferId string                    `protobuf:"bytes,3,opt,name=reward_offer_id,json=rewardOfferId,proto3" json:"reward_offer_id,omitempty"`
	// filter on the generation type (actual or projections)
	GenerationType GenerationType `protobuf:"varint,4,opt,name=generation_type,json=generationType,proto3,enum=rewardoffers.GenerationType" json:"generation_type,omitempty"`
}

func (x *GetRewardOffersForScreenRequest_Filter) Reset() {
	*x = GetRewardOffersForScreenRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOffersForScreenRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOffersForScreenRequest_Filter) ProtoMessage() {}

func (x *GetRewardOffersForScreenRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOffersForScreenRequest_Filter.ProtoReflect.Descriptor instead.
func (*GetRewardOffersForScreenRequest_Filter) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{48, 0}
}

func (x *GetRewardOffersForScreenRequest_Filter) GetTags() []RewardOfferTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *GetRewardOffersForScreenRequest_Filter) GetOfferTypes() []rewards.RewardOfferType {
	if x != nil {
		return x.OfferTypes
	}
	return nil
}

func (x *GetRewardOffersForScreenRequest_Filter) GetRewardOfferId() string {
	if x != nil {
		return x.RewardOfferId
	}
	return ""
}

func (x *GetRewardOffersForScreenRequest_Filter) GetGenerationType() GenerationType {
	if x != nil {
		return x.GenerationType
	}
	return GenerationType_GENERATION_TYPE_UNSPECIFIED
}

type WaysToEarnRewardsV2ScreenConfig_Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IconAndText *IconAndText `protobuf:"bytes,1,opt,name=icon_and_text,json=iconAndText,proto3" json:"icon_and_text,omitempty"`
	BgColor     string       `protobuf:"bytes,2,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *WaysToEarnRewardsV2ScreenConfig_Tag) Reset() {
	*x = WaysToEarnRewardsV2ScreenConfig_Tag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaysToEarnRewardsV2ScreenConfig_Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaysToEarnRewardsV2ScreenConfig_Tag) ProtoMessage() {}

func (x *WaysToEarnRewardsV2ScreenConfig_Tag) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaysToEarnRewardsV2ScreenConfig_Tag.ProtoReflect.Descriptor instead.
func (*WaysToEarnRewardsV2ScreenConfig_Tag) Descriptor() ([]byte, []int) {
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP(), []int{60, 0}
}

func (x *WaysToEarnRewardsV2ScreenConfig_Tag) GetIconAndText() *IconAndText {
	if x != nil {
		return x.IconAndText
	}
	return nil
}

func (x *WaysToEarnRewardsV2ScreenConfig_Tag) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

var File_api_rewards_rewardoffers_reward_offer_proto protoreflect.FileDescriptor

var file_api_rewards_rewardoffers_reward_offer_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x1a, 0x1e, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x42, 0x0a, 0x18, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5e, 0x0a,
	0x19, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1c, 0x0a, 0x09, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x73, 0x22, 0x64, 0x0a,
	0x1a, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03,
	0x65, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x16, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f,
	0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4b,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x0b,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70,
	0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0x50, 0x0a,
	0x16, 0x46, 0x69, 0x78, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22,
	0x5f, 0x0a, 0x1b, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f,
	0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e,
	0x0a, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20,
	0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x22, 0x37, 0x0a, 0x15, 0x46, 0x69, 0x78, 0x65, 0x64, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c,
	0x69, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x22, 0xab, 0x03, 0x0a, 0x20, 0x45, 0x78,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f,
	0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e,
	0x0a, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x81,
	0x01, 0x0a, 0x17, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x49, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50,
	0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x15, 0x72, 0x61, 0x6e,
	0x67, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x6e, 0x69,
	0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75, 0x70, 0x70, 0x65, 0x72, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x1a, 0xa0, 0x01, 0x0a, 0x1a, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72,
	0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55,
	0x6e, 0x69, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x13, 0x75, 0x6e, 0x69, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x75, 0x6e, 0x69, 0x74, 0x5f,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x75, 0x6e, 0x69, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x22, 0xd7, 0x02, 0x0a, 0x2b, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x67, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55,
	0x6e, 0x69, 0x74, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x73,
	0x1a, 0xbe, 0x01, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x12,
	0x31, 0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x7d, 0x0a, 0x23, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x45,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72,
	0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x20, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x22, 0xac, 0x06, 0x0a, 0x22, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x6f, 0x73, 0x74,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5e, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x0b, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x1a, 0xa5, 0x05, 0x0a, 0x0a, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x31, 0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x66, 0x0a, 0x1d, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x70, 0x72, 0x65,
	0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x2e, 0x4d, 0x61, 0x74, 0x68, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x1a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x57, 0x69, 0x74, 0x68, 0x50, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x73, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x69, 0x0a, 0x33, 0x75, 0x6e, 0x69, 0x74, 0x73,
	0x5f, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f,
	0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x2c, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x55, 0x70, 0x70, 0x65, 0x72,
	0x43, 0x61, 0x70, 0x41, 0x66, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x57, 0x69, 0x74, 0x68, 0x50, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x7f, 0x0a, 0x23, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x45,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72,
	0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48,
	0x00, 0x52, 0x20, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x1a, 0x8a, 0x01, 0x0a, 0x0e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x42, 0x0e, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0xed, 0x03, 0x0a, 0x20, 0x44, 0x65, 0x72, 0x69, 0x76, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x53,
	0x65, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x37, 0x0a, 0x18, 0x73, 0x65, 0x65, 0x64, 0x5f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x73, 0x65, 0x65, 0x64, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x7e,
	0x0a, 0x17, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x64, 0x65, 0x72, 0x69, 0x76, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x44, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x44,
	0x65, 0x72, 0x69, 0x76, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x65, 0x65, 0x64, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x44, 0x65, 0x6c, 0x74, 0x61, 0x44, 0x65, 0x72, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x15, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x44, 0x65,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x8d,
	0x01, 0x0a, 0x1c, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x5f, 0x64, 0x65,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x44, 0x65, 0x72, 0x69, 0x76, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x53,
	0x65, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72,
	0x44, 0x65, 0x72, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x48, 0x00, 0x52, 0x1a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x44, 0x65,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x2d,
	0x0a, 0x15, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x44, 0x65, 0x72, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x1a, 0x3c, 0x0a,
	0x1a, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x44, 0x65, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x42, 0x13, 0x0a, 0x11, 0x64,
	0x65, 0x72, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0xc0, 0x19, 0x0a, 0x12, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x59, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x66, 0x0a, 0x1d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x1a,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x6f, 0x0a, 0x1d, 0x65, 0x78,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x2e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x62, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x1b,
	0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x60, 0x0a, 0x18, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x16, 0x66, 0x69, 0x78, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x62,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x60, 0x0a,
	0x18, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x16, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72,
	0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x7f, 0x0a, 0x23, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x45, 0x78, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x20,
	0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50,
	0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x81, 0x01, 0x0a, 0x25, 0x64, 0x65, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d,
	0x5f, 0x73, 0x65, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x44, 0x65, 0x72, 0x69, 0x76, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x65, 0x65, 0x64, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x48, 0x00, 0x52, 0x20, 0x64, 0x65, 0x72, 0x69, 0x76, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x65,
	0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x93, 0x01, 0x0a, 0x28, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70,
	0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x48, 0x00, 0x52, 0x24, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x45, 0x78, 0x70, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4b, 0x0a, 0x11, 0x6c, 0x75,
	0x63, 0x6b, 0x79, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x0f, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72, 0x61,
	0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x54, 0x0a, 0x14, 0x73, 0x6d, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x12, 0x73, 0x6d, 0x61, 0x72, 0x74,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4e, 0x0a,
	0x12, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x68, 0x61, 0x6d, 0x70, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x48, 0x61, 0x6d,
	0x70, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x10, 0x67, 0x69, 0x66,
	0x74, 0x48, 0x61, 0x6d, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3b, 0x0a,
	0x0b, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x73, 0x2e, 0x43, 0x61, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x0a,
	0x63, 0x61, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5e, 0x0a, 0x18, 0x6d, 0x65,
	0x74, 0x61, 0x6c, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x48, 0x01, 0x52, 0x15, 0x6d, 0x65, 0x74, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4b, 0x0a, 0x11, 0x65, 0x67,
	0x76, 0x5f, 0x62, 0x61, 0x73, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x45, 0x67, 0x76, 0x42, 0x61, 0x73, 0x6b, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x0f, 0x65, 0x67, 0x76, 0x42, 0x61, 0x73, 0x6b, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x70, 0x0a, 0x1e, 0x74, 0x68, 0x72, 0x69, 0x77,
	0x65, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x54,
	0x68, 0x72, 0x69, 0x77, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x1b, 0x74, 0x68,
	0x72, 0x69, 0x77, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x57, 0x0a, 0x15, 0x75, 0x73, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x55, 0x53, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x13, 0x75,
	0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x58, 0x0a, 0x16, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x23, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x73, 0x2e, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x13, 0x66, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x33, 0x0a, 0x15,
	0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x6f, 0x6e,
	0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x5d, 0x0a, 0x17, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x70, 0x6c, 0x69, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x73, 0x2e, 0x46, 0x69, 0x78, 0x65, 0x64, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x02, 0x52, 0x15, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x45, 0x0a, 0x0f, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x73, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x5f, 0x73, 0x6b, 0x75, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x6b, 0x75, 0x12, 0x47, 0x0a, 0x20, 0x73, 0x68, 0x6f, 0x75,
	0x6c, 0x64, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x1d, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x1d, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x74, 0x0a, 0x19, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x1e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x17,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x15, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x4c, 0x6f, 0x77, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x37, 0x0a, 0x18, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73,
	0x5f, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x15, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x55,
	0x70, 0x70, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x9e, 0x01, 0x0a, 0x30, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x73, 0x18, 0x22,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x61, 0x70, 0x73, 0x52, 0x2a,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x61, 0x70, 0x73, 0x1a, 0xf4, 0x03, 0x0a, 0x07, 0x44,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x48, 0x0a, 0x1c, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65,
	0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x19, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x54, 0x65, 0x78, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x46, 0x0a, 0x1b, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x18,
	0x61, 0x66, 0x74, 0x65, 0x72, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x65, 0x78, 0x74, 0x45, 0x78,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x72, 0x0a, 0x16, 0x68, 0x74, 0x6d,
	0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x2e, 0x48, 0x74, 0x6d, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65,
	0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x14, 0x68, 0x74, 0x6d, 0x6c, 0x46, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x37, 0x0a,
	0x18, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x62, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x15, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x42, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x54, 0x65, 0x78, 0x74, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x1a, 0x41,
	0x0a, 0x13, 0x48, 0x74, 0x6d, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64,
	0x79, 0x1a, 0xd9, 0x01, 0x0a, 0x17, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x4c, 0x0a,
	0x14, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x12, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x10, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x0e, 0x0a,
	0x0c, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x0f, 0x0a,
	0x0d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x13,
	0x0a, 0x11, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x22, 0xbf, 0x01, 0x0a, 0x0e, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x73,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x37, 0x0a, 0x08, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12,
	0x74, 0x0a, 0x23, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x61,
	0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x46, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x76, 0x65, 0x61,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x1e, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x46, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x76, 0x65, 0x61,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x8e, 0x02, 0x0a, 0x0d, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x53, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x85, 0x01, 0x0a,
	0x25, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x65, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00,
	0x52, 0x22, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x45, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x1a, 0x10, 0x0a, 0x0e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x35, 0x0a, 0x0f, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x44,
	0x72, 0x61, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x75, 0x63,
	0x6b, 0x79, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x49, 0x64, 0x22, 0x61, 0x0a,
	0x12, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x4b, 0x0a, 0x14, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x12, 0x6d, 0x61,
	0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0x82, 0x01, 0x0a, 0x10, 0x47, 0x69, 0x66, 0x74, 0x48, 0x61, 0x6d, 0x70, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x40, 0x0a, 0x0a, 0x43, 0x61, 0x73, 0x68, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x32, 0x0a, 0x15, 0x74, 0x78, 0x6e, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x74, 0x78, 0x6e, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x45, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x17, 0x0a, 0x15, 0x4d, 0x65, 0x74, 0x61, 0x6c,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0x35, 0x0a, 0x0f, 0x45, 0x67, 0x76, 0x42, 0x61, 0x73, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x22, 0x0a, 0x0d, 0x65, 0x67, 0x76, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x67, 0x76, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x38, 0x0a, 0x1b, 0x54, 0x68, 0x72, 0x69, 0x77,
	0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49,
	0x64, 0x22, 0xad, 0x0b, 0x0a, 0x0a, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61,
	0x12, 0x55, 0x0a, 0x11, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x10, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x12, 0x5a, 0x0a, 0x17, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x14, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x45, 0x0a, 0x1b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x64,
	0x65, 0x63, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x65,
	0x63, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x20,
	0x00, 0x52, 0x17, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x63, 0x69, 0x64, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x53, 0x65, 0x63, 0x73, 0x12, 0x2c, 0x0a, 0x0b, 0x70, 0x72,
	0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x0a, 0x05, 0x25, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0b, 0x70, 0x72, 0x6f,
	0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x5e, 0x0a, 0x15, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x08, 0x01, 0x52, 0x13, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x76,
	0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73,
	0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x5a, 0x0a, 0x13, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x11, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d,
	0x65, 0x74, 0x61, 0x12, 0x6e, 0x0a, 0x1a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x18, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x38, 0x0a, 0x17, 0x69, 0x73, 0x5f, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b,
	0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x6b, 0x79, 0x63, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x13, 0x69, 0x73, 0x55, 0x6e, 0x6c, 0x6f,
	0x63, 0x6b, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x4d, 0x69, 0x6e, 0x4b, 0x79, 0x63, 0x12, 0x4e, 0x0a,
	0x16, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x13, 0x61, 0x75, 0x74, 0x6f, 0x43, 0x6c,
	0x61, 0x69, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x33, 0x0a,
	0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0d, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x12, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x54, 0x61, 0x67, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61,
	0x67, 0x73, 0x12, 0x3f, 0x0a, 0x1c, 0x69, 0x73, 0x5f, 0x69, 0x6d, 0x70, 0x6c, 0x69, 0x63, 0x69,
	0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x69, 0x73, 0x49, 0x6d, 0x70, 0x6c,
	0x69, 0x63, 0x69, 0x74, 0x4c, 0x6f, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x54, 0x0a, 0x19, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x16, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0xa5, 0x03, 0x0a, 0x11, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x12,
	0x3a, 0x0a, 0x1a, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x62, 0x67, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x16, 0x74, 0x69, 0x6c, 0x65, 0x42, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x38, 0x0a, 0x19, 0x74,
	0x69, 0x6c, 0x65, 0x5f, 0x62, 0x67, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x66, 0x74,
	0x65, 0x72, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15,
	0x74, 0x69, 0x6c, 0x65, 0x42, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x66, 0x74, 0x65, 0x72,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x51, 0x0a, 0x16, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x74, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x13, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6c, 0x65, 0x54,
	0x68, 0x65, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x1b, 0x70, 0x6f, 0x73, 0x74,
	0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x5f, 0x61, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x18, 0x70,
	0x6f, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x69, 0x73, 0x5f, 0x61, 0x6e,
	0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x6e, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x69, 0x73, 0x41, 0x6e, 0x69,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x41,
	0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x6e, 0x69, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0e, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x65, 0x65,
	0x64, 0x22, 0x3a, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73,
	0x4d, 0x65, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xd5, 0x04,
	0x0a, 0x10, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x12, 0x6b, 0x0a, 0x1f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x52, 0x1b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74,
	0x73, 0x43, 0x61, 0x70, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x12, 0x9a, 0x01, 0x0a, 0x39, 0x63, 0x63, 0x5f, 0x31, 0x78, 0x5f, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x63, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c,
	0x79, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x61, 0x70, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73,
	0x43, 0x61, 0x70, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x52, 0x30, 0x63, 0x63,
	0x31, 0x78, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x63, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x6e, 0x74, 0x68,
	0x6c, 0x79, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x12, 0x7a,
	0x0a, 0x27, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63,
	0x61, 0x70, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x52, 0x22, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e,
	0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x55, 0x73, 0x65,
	0x72, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x69, 0x0a, 0x1d, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x19, 0x75, 0x73, 0x65, 0x72,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x50,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0xf0, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x12, 0x53, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73,
	0x43, 0x61, 0x70, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x2e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x52, 0x09, 0x75, 0x6e, 0x69,
	0x74, 0x73, 0x43, 0x61, 0x70, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x68, 0x61, 0x72,
	0x64, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69,
	0x73, 0x48, 0x61, 0x72, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x1a, 0x5c, 0x0a, 0x0e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x12, 0x34, 0x0a, 0x0b,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x22, 0x79, 0x0a, 0x19, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x50,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79,
	0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x10, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x12, 0x2f, 0x0a, 0x13, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x5f,
	0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x12, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x22, 0xd9, 0x03, 0x0a, 0x10, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6b,
	0x0a, 0x1f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63,
	0x61, 0x70, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69,
	0x74, 0x73, 0x43, 0x61, 0x70, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x52, 0x1b,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x7a, 0x0a, 0x27, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x61, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x52, 0x22, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73,
	0x43, 0x61, 0x70, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x41, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x76, 0x0a, 0x24, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65,
	0x5f, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x1f,
	0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22,
	0xac, 0x03, 0x0a, 0x26, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x55,
	0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x0e, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x66, 0x69, 0x43, 0x6f, 0x69, 0x6e,
	0x73, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x61, 0x73, 0x68,
	0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x64, 0x5f, 0x63, 0x61, 0x73, 0x68,
	0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x64,
	0x43, 0x61, 0x73, 0x68, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x2c, 0x0a, 0x12, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x75, 0x73, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x43, 0x61, 0x73, 0x68, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x2f, 0x0a,
	0x14, 0x63, 0x63, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x65, 0x72, 0x61, 0x73, 0x65, 0x72, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x63, 0x63, 0x42,
	0x69, 0x6c, 0x6c, 0x45, 0x72, 0x61, 0x73, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x22, 0xbc,
	0x03, 0x0a, 0x2b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24,
	0x0a, 0x0e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x24, 0x0a, 0x0e, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x75, 0x6e, 0x69, 0x74,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x66, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73,
	0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x75, 0x6e,
	0x69, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x61, 0x73, 0x68, 0x55,
	0x6e, 0x69, 0x74, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x64, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x64, 0x43,
	0x61, 0x73, 0x68, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2c,
	0x0a, 0x12, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x75, 0x73, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x43, 0x61, 0x73, 0x68, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x14,
	0x63, 0x63, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x65, 0x72, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x63, 0x63, 0x42, 0x69,
	0x6c, 0x6c, 0x45, 0x72, 0x61, 0x73, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x22, 0x8b, 0x01,
	0x0a, 0x19, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x24, 0x0a, 0x0e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x64, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x72, 0x65, 0x6d, 0x61,
	0x69, 0x6e, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x88, 0x01, 0x0a, 0x14,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x65, 0x6e,
	0x74, 0x6f, 0x72, 0x79, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f,
	0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xa1, 0x0f, 0x0a, 0x0b, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x69, 0x6c, 0x6c, 0x12, 0x3c,
	0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x05, 0x73,
	0x74, 0x65, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x05,
	0x73, 0x74, 0x65, 0x70, 0x73, 0x12, 0x16, 0x0a, 0x04, 0x74, 0x6e, 0x63, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x74, 0x6e, 0x63, 0x73, 0x12, 0x1b, 0x0a,
	0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x08, 0x62, 0x67,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x28,
	0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x12, 0x23, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x54, 0x41, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x21, 0x0a,
	0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x61, 0x6e, 0x6b,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x73, 0x63, 0x12,
	0x22, 0x0a, 0x0d, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x69, 0x6c, 0x65, 0x42, 0x67, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x12, 0x31, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x54, 0x61, 0x67,
	0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x35, 0x0a, 0x17, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79,
	0x5f, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x54,
	0x69, 0x6c, 0x65, 0x42, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x68,
	0x6f, 0x72, 0x74, 0x5f, 0x74, 0x6e, 0x63, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x68, 0x6f, 0x72, 0x74, 0x54, 0x6e, 0x63, 0x12, 0x43, 0x0a, 0x1f, 0x69, 0x73, 0x5f, 0x76, 0x69,
	0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f,
	0x69, 0x6e, 0x74, 0x72, 0x6f, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x1a, 0x69, 0x73, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4f, 0x6e, 0x53, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x50, 0x61, 0x67, 0x65, 0x12, 0x47, 0x0a, 0x21,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x6e, 0x5f,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x5f, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x52, 0x61, 0x6e, 0x6b, 0x4f, 0x6e, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x74, 0x72,
	0x6f, 0x50, 0x61, 0x67, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x29, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x5f, 0x74, 0x6f, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x6d, 0x61, 0x70, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4d, 0x65, 0x74, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x6f, 0x4d,
	0x69, 0x6e, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x41, 0x70, 0x70, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x23, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x6f, 0x4d, 0x69, 0x6e, 0x53, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d,
	0x61, 0x70, 0x12, 0x9a, 0x01, 0x0a, 0x29, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f,
	0x74, 0x6f, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x61, 0x70,
	0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x74,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x6f, 0x4d, 0x61, 0x78, 0x53,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x23, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x54, 0x6f, 0x4d, 0x61, 0x78, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x64, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x12,
	0x3e, 0x0a, 0x08, 0x73, 0x74, 0x65, 0x70, 0x73, 0x5f, 0x76, 0x31, 0x18, 0x16, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x49, 0x6e, 0x66,
	0x6f, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x07, 0x73, 0x74, 0x65, 0x70, 0x73, 0x56, 0x31, 0x12,
	0x3c, 0x0a, 0x07, 0x74, 0x6e, 0x63, 0x73, 0x5f, 0x76, 0x31, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x49, 0x6e, 0x66, 0x6f,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x06, 0x74, 0x6e, 0x63, 0x73, 0x56, 0x31, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x65, 0x71, 0x75, 0x69, 0x76, 0x61, 0x6c, 0x65, 0x6e, 0x74,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x63, 0x61, 0x73, 0x68, 0x45, 0x71, 0x75, 0x69,
	0x76, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x12, 0x53, 0x0a, 0x26, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x65,
	0x71, 0x75, 0x69, 0x76, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x6c, 0x63, 0x75, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x23, 0x63, 0x61, 0x73, 0x68, 0x45, 0x71, 0x75, 0x69,
	0x76, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x76,
	0x65, 0x72, 0x62, 0x6f, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x62, 0x6f, 0x73, 0x65, 0x44, 0x65, 0x73, 0x63, 0x12, 0x42,
	0x0a, 0x1d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72,
	0x61, 0x69, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f,
	0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x7d, 0x0a, 0x25, 0x77, 0x61, 0x79, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x65, 0x61,
	0x72, 0x6e, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5f, 0x76, 0x32, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x1c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x2e, 0x57, 0x61, 0x79, 0x73, 0x54, 0x6f, 0x45, 0x61, 0x72, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x56, 0x32, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x1f, 0x77, 0x61, 0x79, 0x73, 0x54, 0x6f, 0x45, 0x61, 0x72, 0x6e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x56, 0x32, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x1a, 0x56, 0x0a, 0x28, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x6f, 0x4d,
	0x69, 0x6e, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x41, 0x70, 0x70, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x56, 0x0a, 0x28, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x6f, 0x4d, 0x61, 0x78, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x64, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x53, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x74, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x62,
	0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62,
	0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x1a, 0x72, 0x0a, 0x09, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0x70, 0x0a, 0x1a, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x75, 0x64, 0x67,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x75, 0x64, 0x67,
	0x65, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x0e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x73, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0xb8, 0x02, 0x0a,
	0x18, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x77, 0x0a, 0x21, 0x65, 0x61, 0x72,
	0x6e, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x1e, 0x65, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0xa2, 0x01, 0x0a, 0x30, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x66, 0x75, 0x6c, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x2c, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x66, 0x75, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xad, 0x05, 0x0a, 0x1e, 0x45, 0x61, 0x72, 0x6e,
	0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x55, 0x0a, 0x11, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x10, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x65, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x32, 0x0a, 0x13, 0x75, 0x73, 0x65, 0x5f,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x31, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x75, 0x73, 0x65, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x31, 0x1a, 0x98, 0x03, 0x0a,
	0x12, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x51, 0x0a, 0x11, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4c, 0x0a, 0x23, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x70, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x1f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x61, 0x70, 0x12, 0x51, 0x0a, 0x17, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x15, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x6c, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x5d, 0x0a, 0x15, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x52, 0x14, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2f, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xf0, 0x03, 0x0a, 0x2c, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x73, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x4d, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0xca, 0x02,
	0x0a, 0x12, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x51, 0x0a, 0x11, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x51, 0x0a, 0x17, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x15, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x5d, 0x0a, 0x15, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x14, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x89, 0x01, 0x0a, 0x07, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79,
	0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x37, 0x0a,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0x88, 0x09, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x69,
	0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x54, 0x69, 0x6c, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x72, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x52,
	0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x12,
	0x39, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0a,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x3c, 0x0a, 0x0c, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x76,
	0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73,
	0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x30, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1c,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x2d, 0x0a,
	0x13, 0x72, 0x65, 0x66, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x66, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x12,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x41,
	0x0a, 0x1b, 0x69, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x18, 0x69, 0x73, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x64, 0x12, 0x4b, 0x0a, 0x12, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x11, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x3c,
	0x0a, 0x1a, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x18, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x3d, 0x0a, 0x0c, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0b, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x39, 0x0a, 0x0b, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0a,
	0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x0f, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x19, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x4e, 0x0a, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x22, 0xd6, 0x03, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x13, 0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6b, 0x0a, 0x1f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x70,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x52, 0x1b, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x7a, 0x0a, 0x27, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69,
	0x74, 0x73, 0x43, 0x61, 0x70, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x52, 0x22,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x55, 0x73,
	0x65, 0x72, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x12, 0x76, 0x0a, 0x24, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x1f, 0x75, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0x93, 0x01, 0x0a, 0x1e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x4c, 0x0a, 0x12, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x10,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x22, 0x9e, 0x0a, 0x0a, 0x0b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x37, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1f, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6c, 0x6c, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x46, 0x0a, 0x0f,
	0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73,
	0x4d, 0x65, 0x74, 0x61, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x12,
	0x3c, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x1d, 0x0a,
	0x0a, 0x69, 0x73, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x69, 0x73, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x0a,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0f, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x61,
	0x67, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x72, 0x65, 0x66, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x72, 0x65, 0x66, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x4b, 0x0a, 0x12, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x11, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x30,
	0x0a, 0x12, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x41, 0x0a, 0x1b, 0x69, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x18, 0x69, 0x73, 0x44, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x64, 0x12, 0x3c, 0x0a, 0x1a, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x42, 0x0a, 0x0e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x0c, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0b, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x0a, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x12,
	0x45, 0x0a, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x4e, 0x0a, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x22, 0xad, 0x02, 0x0a, 0x13, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c,
	0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52,
	0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x12, 0x71, 0x0a, 0x18,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x16, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a,
	0x40, 0x0a, 0x15, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0x8e, 0x05, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6c, 0x6c,
	0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x69, 0x6e, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x54, 0x69, 0x6c, 0x6c, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x12, 0x39, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x12, 0x73,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x11, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x1b, 0x69, 0x73,
	0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x18, 0x69, 0x73, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x12, 0x3c, 0x0a,
	0x1a, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x18, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x0f, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x7e, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x52, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x22, 0x2f, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03,
	0x69, 0x64, 0x73, 0x22, 0x83, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x0c, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x22, 0xc3, 0x03, 0x0a, 0x1e, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6c, 0x6c,
	0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x69, 0x6e, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x54, 0x69, 0x6c, 0x6c, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x12, 0x73,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x11, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x45, 0x0a, 0x0f, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22,
	0xe7, 0x07, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x0c, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x12, 0x6e, 0x0a, 0x22, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49,
	0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x1e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49,
	0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x70, 0x0a, 0x23, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x1f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x71, 0x0a, 0x21, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x1d,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x76, 0x0a,
	0x1d, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x55,
	0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x19, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74,
	0x73, 0x55, 0x74, 0x69, 0x6c, 0x12, 0x88, 0x01, 0x0a, 0x24, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x1f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x55, 0x74, 0x69, 0x6c,
	0x12, 0x96, 0x01, 0x0a, 0x21, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x54, 0x6f, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x1b, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x54, 0x6f, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x61, 0x70, 0x1a, 0x6e, 0x0a, 0x20, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x54, 0x6f, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x34, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xf1, 0x02, 0x0a, 0x1f, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xe4, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x12, 0x39, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x26,
	0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x87, 0x01,
	0x0a, 0x20, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x73, 0x46, 0x6f, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x22, 0x69, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0xeb, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x4d, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d,
	0x65, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x61, 0x67, 0x73,
	0x12, 0x3e, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61,
	0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x69, 0x73, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x22, 0xbf, 0x07, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3c, 0x0a,
	0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x0a, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x09, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x54, 0x61, 0x67, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x54, 0x69, 0x6c, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61,
	0x69, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x44,
	0x0a, 0x11, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x1f, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x73,
	0x68, 0x6f, 0x75, 0x6c, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x75,
	0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0a, 0x75, 0x6e, 0x6c, 0x6f,
	0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x0c, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x4e, 0x0a, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x73, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x11, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64,
	0x12, 0x3c, 0x0a, 0x1a, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x53, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3e,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x09, 0x69, 0x73, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x42,
	0x0a, 0x0e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x22, 0xd5, 0x01, 0x0a, 0x0d, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x3c, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x12, 0x42, 0x0a, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x75, 0x72,
	0x70, 0x6f, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50,
	0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x0d, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x22, 0x81, 0x01, 0x0a, 0x03, 0x43,
	0x54, 0x41, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x22, 0x70,
	0x0a, 0x0a, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x03,
	0x63, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x54, 0x41, 0x52, 0x03, 0x63, 0x74,
	0x61, 0x12, 0x3d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73,
	0x4d, 0x65, 0x74, 0x61, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74,
	0x22, 0x30, 0x0a, 0x13, 0x55, 0x53, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x49, 0x64, 0x22, 0x62, 0x0a, 0x13, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4b, 0x0a, 0x0c, 0x69, 0x73, 0x5f,
	0x66, 0x69, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x69, 0x73, 0x46, 0x69,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22, 0x70, 0x0a, 0x10, 0x45, 0x61, 0x72, 0x6e, 0x69, 0x6e,
	0x67, 0x50, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x2d,
	0x0a, 0x12, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x65, 0x61, 0x72, 0x6e,
	0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x6f, 0x74, 0x65,
	0x6e, 0x74, 0x69, 0x61, 0x6c, 0x45, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x75, 0x62, 0x54, 0x65, 0x78, 0x74, 0x22, 0x35, 0x0a, 0x0b, 0x49, 0x63, 0x6f, 0x6e,
	0x41, 0x6e, 0x64, 0x54, 0x65, 0x78, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x22,
	0xa4, 0x06, 0x0a, 0x1f, 0x57, 0x61, 0x79, 0x73, 0x54, 0x6f, 0x45, 0x61, 0x72, 0x6e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x56, 0x32, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x77,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x49, 0x63, 0x6f,
	0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x49, 0x63, 0x6f, 0x6e, 0x12,
	0x40, 0x0a, 0x1d, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x67, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x5d, 0x0a, 0x1b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x77, 0x5f, 0x65,
	0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x45, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x6f, 0x74,
	0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x18, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x6f, 0x77,
	0x45, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x12, 0x4c, 0x0a, 0x15, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x77, 0x5f, 0x73, 0x75,
	0x62, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x41, 0x6e, 0x64, 0x54, 0x65, 0x78, 0x74, 0x52, 0x12, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x52, 0x6f, 0x77, 0x53, 0x75, 0x62, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6f,
	0x0a, 0x27, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f,
	0x65, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61,
	0x6c, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x41, 0x6e, 0x64, 0x54, 0x65, 0x78, 0x74, 0x52, 0x23, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x50,
	0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12,
	0x65, 0x0a, 0x16, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x77, 0x5f, 0x74, 0x6f, 0x70,
	0x5f, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x57,
	0x61, 0x79, 0x73, 0x54, 0x6f, 0x45, 0x61, 0x72, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x56, 0x32, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54,
	0x61, 0x67, 0x52, 0x12, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x54, 0x6f, 0x70, 0x4c,
	0x65, 0x66, 0x74, 0x54, 0x61, 0x67, 0x12, 0x5d, 0x0a, 0x11, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x2e, 0x57, 0x61, 0x79, 0x73, 0x54, 0x6f, 0x45, 0x61, 0x72, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x56, 0x32, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x54, 0x61, 0x67, 0x52, 0x0f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x54, 0x61, 0x67, 0x1a, 0x5f, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x12, 0x3d, 0x0a, 0x0d,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x41, 0x6e, 0x64, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b,
	0x69, 0x63, 0x6f, 0x6e, 0x41, 0x6e, 0x64, 0x54, 0x65, 0x78, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x62,
	0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62,
	0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xed, 0x05, 0x0a, 0x2a, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x46,
	0x72, 0x6f, 0x6d, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x69,
	0x74, 0x43, 0x61, 0x70, 0x73, 0x12, 0x91, 0x01, 0x0a, 0x1b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x70,
	0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x54, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x46,
	0x72, 0x6f, 0x6d, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x69,
	0x74, 0x43, 0x61, 0x70, 0x73, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x54,
	0x6f, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x61, 0x70, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x16, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x55, 0x6e,
	0x69, 0x74, 0x43, 0x61, 0x70, 0x73, 0x4d, 0x61, 0x70, 0x12, 0x94, 0x01, 0x0a, 0x1c, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x5f, 0x63, 0x61, 0x70, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x55, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x61, 0x70, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x61, 0x70, 0x73, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x17, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x54, 0x6f, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x61, 0x70, 0x73, 0x4d, 0x61, 0x70,
	0x12, 0x8b, 0x01, 0x0a, 0x19, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x6f,
	0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x61, 0x70, 0x73, 0x2e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x54, 0x6f, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x61, 0x70, 0x73,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49,
	0x64, 0x54, 0x6f, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x61, 0x70, 0x73, 0x4d, 0x61, 0x70, 0x12, 0x26,
	0x0a, 0x0f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x61,
	0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x55, 0x6e,
	0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x1a, 0x49, 0x0a, 0x1b, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x54, 0x6f, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x61, 0x70, 0x73, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x4a, 0x0a, 0x1c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x54,
	0x6f, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x61, 0x70, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x47, 0x0a,
	0x19, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x54, 0x6f, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x61,
	0x70, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x4e, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a, 0x0b, 0x77,
	0x61, 0x74, 0x73, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x57, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0a, 0x77, 0x61, 0x74, 0x73,
	0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x75, 0x0a, 0x0a, 0x57, 0x61, 0x74, 0x73, 0x6f, 0x6e,
	0x4d, 0x65, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x1a, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x5f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x73,
	0x73, 0x75, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x2a, 0x61, 0x0a,
	0x15, 0x4d, 0x61, 0x74, 0x68, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x22, 0x4d, 0x41, 0x54, 0x48, 0x45, 0x4d,
	0x41, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e,
	0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02,
	0x2a, 0x3a, 0x0a, 0x0b, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x11, 0x0a, 0x0d, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x48, 0x45, 0x41, 0x44, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x01,
	0x12, 0x0a, 0x0a, 0x06, 0x46, 0x52, 0x49, 0x4e, 0x47, 0x45, 0x10, 0x02, 0x2a, 0x7b, 0x0a, 0x11,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x02, 0x12,
	0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45, 0x52,
	0x4d, 0x49, 0x4e, 0x41, 0x54, 0x45, 0x44, 0x10, 0x05, 0x2a, 0x9b, 0x01, 0x0a, 0x16, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x59, 0x53, 0x54, 0x45,
	0x4d, 0x5f, 0x54, 0x52, 0x41, 0x59, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x4e, 0x5f, 0x41,
	0x50, 0x50, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x02,
	0x12, 0x0a, 0x0a, 0x06, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f,
	0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10,
	0x04, 0x12, 0x09, 0x0a, 0x05, 0x4e, 0x55, 0x44, 0x47, 0x45, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05,
	0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x06, 0x2a, 0xdc, 0x09, 0x0a, 0x0e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45,
	0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x41, 0x47, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c,
	0x4e, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x13,
	0x0a, 0x0f, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41,
	0x4c, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x4e, 0x46, 0x4c, 0x55, 0x45, 0x4e, 0x43, 0x45,
	0x52, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x52,
	0x45, 0x47, 0x55, 0x4c, 0x41, 0x52, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4f,
	0x44, 0x45, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x47, 0x4f, 0x4c, 0x44, 0x45, 0x4e, 0x5f, 0x54,
	0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x10, 0x06, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x46, 0x55, 0x4e,
	0x44, 0x5f, 0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x1c, 0x0a, 0x18,
	0x53, 0x55, 0x42, 0x53, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f,
	0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x08, 0x12, 0x11, 0x0a, 0x0d, 0x47, 0x50,
	0x41, 0x59, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x10, 0x09, 0x12, 0x14, 0x0a,
	0x10, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41,
	0x4c, 0x10, 0x0a, 0x12, 0x1b, 0x0a, 0x17, 0x56, 0x41, 0x4e, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x43,
	0x49, 0x52, 0x43, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x10, 0x0b,
	0x12, 0x1a, 0x0a, 0x16, 0x47, 0x50, 0x41, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x31, 0x5f, 0x46,
	0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x0c, 0x12, 0x1a, 0x0a, 0x16,
	0x47, 0x50, 0x41, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x32, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x54,
	0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x0d, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x50, 0x41, 0x59,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x33, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4f,
	0x44, 0x45, 0x10, 0x0e, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x50, 0x41, 0x59, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x34, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x0f,
	0x12, 0x1d, 0x0a, 0x19, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x50, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x31, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x10, 0x12,
	0x1d, 0x0a, 0x19, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x50, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x32,
	0x5f, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x11, 0x12, 0x1d,
	0x0a, 0x19, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x50, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x33, 0x5f,
	0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x12, 0x12, 0x1d, 0x0a,
	0x19, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x50, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x34, 0x5f, 0x46,
	0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x13, 0x12, 0x24, 0x0a, 0x20,
	0x56, 0x41, 0x4e, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x49, 0x52, 0x43, 0x4c, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x31, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x10, 0x14, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x41, 0x4d, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x15, 0x12, 0x1b, 0x0a,
	0x17, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f,
	0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x16, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x42, 0x41,
	0x53, 0x49, 0x43, 0x10, 0x17, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x50, 0x4c, 0x55, 0x53, 0x10, 0x18, 0x12,
	0x1c, 0x0a, 0x18, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f,
	0x46, 0x49, 0x5f, 0x49, 0x4e, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x10, 0x19, 0x12, 0x1a, 0x0a,
	0x16, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49,
	0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x10, 0x1a, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x53, 0x41, 0x4c,
	0x41, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x10, 0x1e, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x41, 0x41,
	0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x10, 0x22, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x41, 0x41, 0x5f,
	0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x4e, 0x44, 0x5f, 0x31, 0x10, 0x23, 0x12,
	0x24, 0x0a, 0x20, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f,
	0x46, 0x49, 0x5f, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x4e,
	0x44, 0x5f, 0x32, 0x10, 0x24, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41,
	0x52, 0x59, 0x5f, 0x42, 0x41, 0x4e, 0x44, 0x5f, 0x33, 0x10, 0x25, 0x12, 0x1b, 0x0a, 0x17, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x52,
	0x45, 0x47, 0x55, 0x4c, 0x41, 0x52, 0x10, 0x27, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x53, 0x41, 0x4c, 0x41,
	0x52, 0x59, 0x5f, 0x42, 0x41, 0x53, 0x49, 0x43, 0x10, 0x28, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x43,
	0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52,
	0x41, 0x4c, 0x10, 0x1b, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x53, 0x41, 0x4c,
	0x41, 0x52, 0x59, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x46, 0x46, 0x45,
	0x52, 0x10, 0x1c, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x4c, 0x49,
	0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x10, 0x1d, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x26, 0x12,
	0x18, 0x0a, 0x14, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x10, 0x1f, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x49, 0x4d,
	0x45, 0x53, 0x50, 0x52, 0x49, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c,
	0x10, 0x20, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x49, 0x4d, 0x45, 0x53, 0x50, 0x4f, 0x49, 0x4e, 0x54,
	0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x10, 0x21, 0x12, 0x29, 0x0a, 0x25, 0x53,
	0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x48, 0x45,
	0x41, 0x4c, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x10, 0x29, 0x2a, 0xe3, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x43,
	0x4c, 0x41, 0x53, 0x53, 0x5f, 0x41, 0x4e, 0x43, 0x48, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x16, 0x0a,
	0x12, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x42, 0x52,
	0x41, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f,
	0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x47, 0x52, 0x4f, 0x57, 0x54, 0x48, 0x10, 0x03, 0x12, 0x1d,
	0x0a, 0x19, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x45,
	0x58, 0x50, 0x45, 0x52, 0x49, 0x4d, 0x45, 0x4e, 0x54, 0x41, 0x4c, 0x10, 0x04, 0x12, 0x19, 0x0a,
	0x15, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x50, 0x4f,
	0x57, 0x45, 0x52, 0x5f, 0x55, 0x50, 0x10, 0x05, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x57, 0x41,
	0x52, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x53,
	0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4c, 0x41,
	0x53, 0x53, 0x5f, 0x54, 0x45, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x07, 0x2a, 0xcd, 0x01, 0x0a,
	0x0d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x1e,
	0x0a, 0x1a, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e,
	0x0a, 0x1a, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45,
	0x5f, 0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x1d,
	0x0a, 0x19, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x1c, 0x0a,
	0x18, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f,
	0x52, 0x45, 0x54, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x52,
	0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x45, 0x4e,
	0x47, 0x41, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45,
	0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x52, 0x45, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x2a, 0xcf, 0x06, 0x0a,
	0x0d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x1e,
	0x0a, 0x1a, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e,
	0x0a, 0x1a, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54,
	0x5f, 0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x1b,
	0x0a, 0x17, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54,
	0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x52,
	0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x41, 0x44,
	0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x57,
	0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x4f, 0x4e, 0x42, 0x4f,
	0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x57, 0x41,
	0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x5f, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45,
	0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x56, 0x4b, 0x59,
	0x43, 0x10, 0x06, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52,
	0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x10, 0x07, 0x12,
	0x19, 0x0a, 0x15, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43,
	0x54, 0x5f, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x49, 0x10, 0x08, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45,
	0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x41, 0x4e, 0x41,
	0x4c, 0x59, 0x5a, 0x45, 0x52, 0x10, 0x09, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x57, 0x41, 0x52,
	0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x49, 0x4e, 0x53, 0x49, 0x47, 0x48,
	0x54, 0x53, 0x10, 0x0a, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x10, 0x0b, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52,
	0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x10, 0x0c, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52,
	0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x0d, 0x12, 0x1b, 0x0a, 0x17, 0x52,
	0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x42, 0x49,
	0x4c, 0x4c, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x0e, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x57, 0x41,
	0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f,
	0x4e, 0x41, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10, 0x0f, 0x12, 0x17, 0x0a, 0x13, 0x52,
	0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x4c, 0x41,
	0x4d, 0x46, 0x10, 0x10, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x11,
	0x12, 0x25, 0x0a, 0x21, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55,
	0x43, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x53, 0x10, 0x12, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x57, 0x41, 0x52,
	0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x43, 0x58, 0x10, 0x13, 0x12, 0x1b,
	0x0a, 0x17, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54,
	0x5f, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x10, 0x14, 0x12, 0x17, 0x0a, 0x13, 0x52,
	0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x4a, 0x55,
	0x4d, 0x50, 0x10, 0x15, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x46, 0x49, 0x54, 0x10, 0x16, 0x12, 0x15, 0x0a, 0x11,
	0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x4d,
	0x46, 0x10, 0x17, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52,
	0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x46, 0x44, 0x5f, 0x53, 0x44, 0x10, 0x18, 0x12, 0x1d, 0x0a,
	0x19, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f,
	0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x32, 0x42, 0x10, 0x19, 0x12, 0x1d, 0x0a, 0x19,
	0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x53,
	0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x32, 0x43, 0x10, 0x1a, 0x12, 0x1a, 0x0a, 0x16, 0x52,
	0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x52, 0x45,
	0x57, 0x41, 0x52, 0x44, 0x53, 0x10, 0x1b, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x57, 0x41, 0x52,
	0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x48, 0x52, 0x10, 0x1c, 0x2a, 0x6d,
	0x0a, 0x0e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1f, 0x0a, 0x1b, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x1e, 0x0a,
	0x1a, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x32, 0xd9, 0x08,
	0x0a, 0x0c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x12, 0x75,
	0x0a, 0x16, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2b, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x12, 0x26, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x21, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x0f, 0x67, 0x65, 0x74, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x12, 0x24, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x25, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x14, 0x67, 0x65, 0x74,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x49, 0x64,
	0x73, 0x12, 0x29, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x78, 0x0a, 0x17, 0x67, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x7b, 0x0a, 0x18, 0x67, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x12, 0x2d, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x46,
	0x6f, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2e, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x46, 0x6f,
	0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x6c, 0x0a, 0x17, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x6e, 0x0a, 0x18, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x2d, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x60, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x12, 0x26, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x66, 0x0a, 0x11, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x12, 0x26, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x5a, 0x2f, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_rewards_rewardoffers_reward_offer_proto_rawDescOnce sync.Once
	file_api_rewards_rewardoffers_reward_offer_proto_rawDescData = file_api_rewards_rewardoffers_reward_offer_proto_rawDesc
)

func file_api_rewards_rewardoffers_reward_offer_proto_rawDescGZIP() []byte {
	file_api_rewards_rewardoffers_reward_offer_proto_rawDescOnce.Do(func() {
		file_api_rewards_rewardoffers_reward_offer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_rewards_rewardoffers_reward_offer_proto_rawDescData)
	})
	return file_api_rewards_rewardoffers_reward_offer_proto_rawDescData
}

var file_api_rewards_rewardoffers_reward_offer_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_api_rewards_rewardoffers_reward_offer_proto_msgTypes = make([]protoimpl.MessageInfo, 89)
var file_api_rewards_rewardoffers_reward_offer_proto_goTypes = []interface{}{
	(MathematicalOperation)(0),                                           // 0: rewardoffers.MathematicalOperation
	(DisplayType)(0),                                                     // 1: rewardoffers.DisplayType
	(RewardOfferStatus)(0),                                               // 2: rewardoffers.RewardOfferStatus
	(RewardNotificationType)(0),                                          // 3: rewardoffers.RewardNotificationType
	(RewardOfferTag)(0),                                                  // 4: rewardoffers.RewardOfferTag
	(RewardClass)(0),                                                     // 5: rewardoffers.RewardClass
	(RewardPurpose)(0),                                                   // 6: rewardoffers.RewardPurpose
	(RewardProduct)(0),                                                   // 7: rewardoffers.RewardProduct
	(GenerationType)(0),                                                  // 8: rewardoffers.GenerationType
	(*ReviewRewardOfferRequest)(nil),                                     // 9: rewardoffers.ReviewRewardOfferRequest
	(*ReviewRewardOfferResponse)(nil),                                    // 10: rewardoffers.ReviewRewardOfferResponse
	(*RangeProbabilityConfigUnit)(nil),                                   // 11: rewardoffers.RangeProbabilityConfigUnit
	(*RangeProbabilityConfig)(nil),                                       // 12: rewardoffers.RangeProbabilityConfig
	(*FixedProbabilityConfig)(nil),                                       // 13: rewardoffers.FixedProbabilityConfig
	(*ExpressionProbabilityConfig)(nil),                                  // 14: rewardoffers.ExpressionProbabilityConfig
	(*FixedMultiplierConfig)(nil),                                        // 15: rewardoffers.FixedMultiplierConfig
	(*ExpressionRangeProbabilityConfig)(nil),                             // 16: rewardoffers.ExpressionRangeProbabilityConfig
	(*ConditionalExpressionRangeProbabilityConfig)(nil),                  // 17: rewardoffers.ConditionalExpressionRangeProbabilityConfig
	(*ConditionalExpressionBoosterConfig)(nil),                           // 18: rewardoffers.ConditionalExpressionBoosterConfig
	(*DeriveFromSeedRewardOptionConfig)(nil),                             // 19: rewardoffers.DeriveFromSeedRewardOptionConfig
	(*RewardConfigOption)(nil),                                           // 20: rewardoffers.RewardConfigOption
	(*BoostersConfig)(nil),                                               // 21: rewardoffers.BoostersConfig
	(*BoosterConfig)(nil),                                                // 22: rewardoffers.BoosterConfig
	(*LuckyDrawConfig)(nil),                                              // 23: rewardoffers.LuckyDrawConfig
	(*SmartDepositConfig)(nil),                                           // 24: rewardoffers.SmartDepositConfig
	(*GiftHamperConfig)(nil),                                             // 25: rewardoffers.GiftHamperConfig
	(*CashConfig)(nil),                                                   // 26: rewardoffers.CashConfig
	(*MetalCreditCardConfig)(nil),                                        // 27: rewardoffers.MetalCreditCardConfig
	(*EgvBasketConfig)(nil),                                              // 28: rewardoffers.EgvBasketConfig
	(*ThriweBenefitsPackageConfig)(nil),                                  // 29: rewardoffers.ThriweBenefitsPackageConfig
	(*RewardMeta)(nil),                                                   // 30: rewardoffers.RewardMeta
	(*ConstraintsMeta)(nil),                                              // 31: rewardoffers.ConstraintsMeta
	(*RewardAggregates)(nil),                                             // 32: rewardoffers.RewardAggregates
	(*RewardUnitsCapAggregate)(nil),                                      // 33: rewardoffers.RewardUnitsCapAggregate
	(*UserAggregateInTimePeriod)(nil),                                    // 34: rewardoffers.UserAggregateInTimePeriod
	(*RewardOfferGroup)(nil),                                             // 35: rewardoffers.RewardOfferGroup
	(*RewardOfferRewardUnitsActorUtilisation)(nil),                       // 36: rewardoffers.RewardOfferRewardUnitsActorUtilisation
	(*RewardOfferGroupRewardUnitsActorUtilisation)(nil),                  // 37: rewardoffers.RewardOfferGroupRewardUnitsActorUtilisation
	(*RewardOfferGroupInventory)(nil),                                    // 38: rewardoffers.RewardOfferGroupInventory
	(*RewardOfferInventory)(nil),                                         // 39: rewardoffers.RewardOfferInventory
	(*DisplayMeta)(nil),                                                  // 40: rewardoffers.DisplayMeta
	(*RewardNotificationMetadata)(nil),                                   // 41: rewardoffers.RewardNotificationMetadata
	(*RewardNotificationConfig)(nil),                                     // 42: rewardoffers.RewardNotificationConfig
	(*EarnedRewardNotificationConfig)(nil),                               // 43: rewardoffers.EarnedRewardNotificationConfig
	(*RewardProcessingSuccessfulNotificationConfig)(nil),                 // 44: rewardoffers.RewardProcessingSuccessfulNotificationConfig
	(*Content)(nil),                                                      // 45: rewardoffers.Content
	(*CreateRewardOfferRequest)(nil),                                     // 46: rewardoffers.CreateRewardOfferRequest
	(*CreateRewardOfferGroupRequest)(nil),                                // 47: rewardoffers.CreateRewardOfferGroupRequest
	(*CreateRewardOfferGroupResponse)(nil),                               // 48: rewardoffers.CreateRewardOfferGroupResponse
	(*RewardOffer)(nil),                                                  // 49: rewardoffers.RewardOffer
	(*RewardOfferResponse)(nil),                                          // 50: rewardoffers.RewardOfferResponse
	(*GetRewardOffersRequest)(nil),                                       // 51: rewardoffers.GetRewardOffersRequest
	(*GetRewardOffersResponse)(nil),                                      // 52: rewardoffers.GetRewardOffersResponse
	(*GetRewardOffersByIdsRequest)(nil),                                  // 53: rewardoffers.GetRewardOffersByIdsRequest
	(*GetRewardOffersByIdsResponse)(nil),                                 // 54: rewardoffers.GetRewardOffersByIdsResponse
	(*GetRewardOffersForActorRequest)(nil),                               // 55: rewardoffers.GetRewardOffersForActorRequest
	(*GetRewardOffersForActorResponse)(nil),                              // 56: rewardoffers.GetRewardOffersForActorResponse
	(*GetRewardOffersForScreenRequest)(nil),                              // 57: rewardoffers.GetRewardOffersForScreenRequest
	(*GetRewardOffersForScreenResponse)(nil),                             // 58: rewardoffers.GetRewardOffersForScreenResponse
	(*UpdateRewardOfferStatusRequest)(nil),                               // 59: rewardoffers.UpdateRewardOfferStatusRequest
	(*UpdateRewardOfferDisplayRequest)(nil),                              // 60: rewardoffers.UpdateRewardOfferDisplayRequest
	(*UpdateRewardOfferRequest)(nil),                                     // 61: rewardoffers.UpdateRewardOfferRequest
	(*AnalyticsData)(nil),                                                // 62: rewardoffers.AnalyticsData
	(*CTA)(nil),                                                          // 63: rewardoffers.CTA
	(*UnlockMeta)(nil),                                                   // 64: rewardoffers.UnlockMeta
	(*USStockRewardConfig)(nil),                                          // 65: rewardoffers.USStockRewardConfig
	(*FiCoinsRewardConfig)(nil),                                          // 66: rewardoffers.FiCoinsRewardConfig
	(*EarningPotential)(nil),                                             // 67: rewardoffers.EarningPotential
	(*IconAndText)(nil),                                                  // 68: rewardoffers.IconAndText
	(*WaysToEarnRewardsV2ScreenConfig)(nil),                              // 69: rewardoffers.WaysToEarnRewardsV2ScreenConfig
	(*RewardUnitsGeneratedFromProjectionUnitCaps)(nil),                   // 70: rewardoffers.RewardUnitsGeneratedFromProjectionUnitCaps
	(*AdditionalDetails)(nil),                                            // 71: rewardoffers.AdditionalDetails
	(*WatsonMeta)(nil),                                                   // 72: rewardoffers.WatsonMeta
	(*ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit)(nil),  // 73: rewardoffers.ExpressionRangeProbabilityConfig.RangeProbabilityConfigUnit
	(*ConditionalExpressionRangeProbabilityConfig_ConfigUnit)(nil),       // 74: rewardoffers.ConditionalExpressionRangeProbabilityConfig.ConfigUnit
	(*ConditionalExpressionBoosterConfig_ConfigUnit)(nil),                // 75: rewardoffers.ConditionalExpressionBoosterConfig.ConfigUnit
	(*ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails)(nil), // 76: rewardoffers.ConditionalExpressionBoosterConfig.ConfigUnit.DisplayDetails
	(*DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig)(nil),       // 77: rewardoffers.DeriveFromSeedRewardOptionConfig.DeltaDerivationConfig
	(*DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig)(nil),  // 78: rewardoffers.DeriveFromSeedRewardOptionConfig.MultiplierDerivationConfig
	(*RewardConfigOption_Display)(nil),                                   // 79: rewardoffers.RewardConfigOption.Display
	(*RewardConfigOption_ProjectionsQueryFilters)(nil),                   // 80: rewardoffers.RewardConfigOption.ProjectionsQueryFilters
	(*RewardConfigOption_Display_HtmlFormattedDetail)(nil),               // 81: rewardoffers.RewardConfigOption.Display.HtmlFormattedDetail
	(*BoosterConfig_DisplayDetails)(nil),                                 // 82: rewardoffers.BoosterConfig.DisplayDetails
	(*RewardMeta_RewardDisplayMeta)(nil),                                 // 83: rewardoffers.RewardMeta.RewardDisplayMeta
	(*RewardUnitsCapAggregate_RewardUnitsCap)(nil),                       // 84: rewardoffers.RewardUnitsCapAggregate.RewardUnitsCap
	nil,                           // 85: rewardoffers.DisplayMeta.PlatformToMinSupportedAppVersionMapEntry
	nil,                           // 86: rewardoffers.DisplayMeta.PlatformToMaxSupportedAppVersionMapEntry
	(*DisplayMeta_Tag)(nil),       // 87: rewardoffers.DisplayMeta.Tag
	(*DisplayMeta_InfoPoint)(nil), // 88: rewardoffers.DisplayMeta.InfoPoint
	(*EarnedRewardNotificationConfig_NotificationConfig)(nil),               // 89: rewardoffers.EarnedRewardNotificationConfig.NotificationConfig
	(*RewardProcessingSuccessfulNotificationConfig_NotificationConfig)(nil), // 90: rewardoffers.RewardProcessingSuccessfulNotificationConfig.NotificationConfig
	(*RewardOfferResponse_ValidationFailureInfo)(nil),                       // 91: rewardoffers.RewardOfferResponse.ValidationFailureInfo
	nil, // 92: rewardoffers.GetRewardOffersForActorResponse.OfferGroupIdToOfferGroupMapEntry
	(*GetRewardOffersForScreenRequest_Filter)(nil), // 93: rewardoffers.GetRewardOffersForScreenRequest.Filter
	(*WaysToEarnRewardsV2ScreenConfig_Tag)(nil),    // 94: rewardoffers.WaysToEarnRewardsV2ScreenConfig.Tag
	nil,                              // 95: rewardoffers.RewardUnitsGeneratedFromProjectionUnitCaps.OfferTypeToUnitCapsMapEntry
	nil,                              // 96: rewardoffers.RewardUnitsGeneratedFromProjectionUnitCaps.ActionTypeToUnitCapsMapEntry
	nil,                              // 97: rewardoffers.RewardUnitsGeneratedFromProjectionUnitCaps.OfferIdToUnitCapsMapEntry
	(*rpc.Status)(nil),               // 98: rpc.Status
	(rewards.RewardType)(0),          // 99: rewards.RewardType
	(*rewards.RewardTimeConfig)(nil), // 100: rewards.RewardTimeConfig
	(rewards.ClaimFlowRewardValueRevealType)(0), // 101: rewards.ClaimFlowRewardValueRevealType
	(rewards.RewardTag)(0),                      // 102: rewards.RewardTag
	(*timestamppb.Timestamp)(nil),               // 103: google.protobuf.Timestamp
	(comms.EmailType)(0),                        // 104: comms.EmailType
	(*deeplink.Deeplink)(nil),                   // 105: frontend.deeplink.Deeplink
	(rewards.RewardOfferType)(0),                // 106: rewards.RewardOfferType
	(common.Platform)(0),                        // 107: api.typesv2.common.Platform
	(rewards.CollectedDataType)(0),              // 108: rewards.CollectedDataType
	(common.BooleanEnum)(0),                     // 109: api.typesv2.common.BooleanEnum
	(rewards.RewardTileThemeType)(0),            // 110: rewards.RewardTileThemeType
	(*common.Text)(nil),                         // 111: api.typesv2.common.Text
}
var file_api_rewards_rewardoffers_reward_offer_proto_depIdxs = []int32{
	98,  // 0: rewardoffers.ReviewRewardOfferResponse.status:type_name -> rpc.Status
	11,  // 1: rewardoffers.RangeProbabilityConfig.config_units:type_name -> rewardoffers.RangeProbabilityConfigUnit
	73,  // 2: rewardoffers.ExpressionRangeProbabilityConfig.range_probability_units:type_name -> rewardoffers.ExpressionRangeProbabilityConfig.RangeProbabilityConfigUnit
	74,  // 3: rewardoffers.ConditionalExpressionRangeProbabilityConfig.config_units:type_name -> rewardoffers.ConditionalExpressionRangeProbabilityConfig.ConfigUnit
	75,  // 4: rewardoffers.ConditionalExpressionBoosterConfig.config_units:type_name -> rewardoffers.ConditionalExpressionBoosterConfig.ConfigUnit
	77,  // 5: rewardoffers.DeriveFromSeedRewardOptionConfig.delta_derivation_config:type_name -> rewardoffers.DeriveFromSeedRewardOptionConfig.DeltaDerivationConfig
	78,  // 6: rewardoffers.DeriveFromSeedRewardOptionConfig.multiplier_derivation_config:type_name -> rewardoffers.DeriveFromSeedRewardOptionConfig.MultiplierDerivationConfig
	99,  // 7: rewardoffers.RewardConfigOption.reward_type:type_name -> rewards.RewardType
	79,  // 8: rewardoffers.RewardConfigOption.display_config:type_name -> rewardoffers.RewardConfigOption.Display
	100, // 9: rewardoffers.RewardConfigOption.reward_processing_time_config:type_name -> rewards.RewardTimeConfig
	14,  // 10: rewardoffers.RewardConfigOption.expression_probability_config:type_name -> rewardoffers.ExpressionProbabilityConfig
	13,  // 11: rewardoffers.RewardConfigOption.fixed_probability_config:type_name -> rewardoffers.FixedProbabilityConfig
	12,  // 12: rewardoffers.RewardConfigOption.range_probability_config:type_name -> rewardoffers.RangeProbabilityConfig
	16,  // 13: rewardoffers.RewardConfigOption.expression_range_probability_config:type_name -> rewardoffers.ExpressionRangeProbabilityConfig
	19,  // 14: rewardoffers.RewardConfigOption.derive_from_seed_reward_option_config:type_name -> rewardoffers.DeriveFromSeedRewardOptionConfig
	17,  // 15: rewardoffers.RewardConfigOption.conditional_exp_range_probability_config:type_name -> rewardoffers.ConditionalExpressionRangeProbabilityConfig
	23,  // 16: rewardoffers.RewardConfigOption.lucky_draw_config:type_name -> rewardoffers.LuckyDrawConfig
	24,  // 17: rewardoffers.RewardConfigOption.smart_deposit_config:type_name -> rewardoffers.SmartDepositConfig
	25,  // 18: rewardoffers.RewardConfigOption.gift_hamper_config:type_name -> rewardoffers.GiftHamperConfig
	26,  // 19: rewardoffers.RewardConfigOption.cash_config:type_name -> rewardoffers.CashConfig
	27,  // 20: rewardoffers.RewardConfigOption.metal_credit_card_config:type_name -> rewardoffers.MetalCreditCardConfig
	28,  // 21: rewardoffers.RewardConfigOption.egv_basket_config:type_name -> rewardoffers.EgvBasketConfig
	29,  // 22: rewardoffers.RewardConfigOption.thriwe_benefits_package_config:type_name -> rewardoffers.ThriweBenefitsPackageConfig
	65,  // 23: rewardoffers.RewardConfigOption.usstock_reward_config:type_name -> rewardoffers.USStockRewardConfig
	66,  // 24: rewardoffers.RewardConfigOption.fi_coins_reward_config:type_name -> rewardoffers.FiCoinsRewardConfig
	15,  // 25: rewardoffers.RewardConfigOption.fixed_multiplier_config:type_name -> rewardoffers.FixedMultiplierConfig
	21,  // 26: rewardoffers.RewardConfigOption.boosters_config:type_name -> rewardoffers.BoostersConfig
	80,  // 27: rewardoffers.RewardConfigOption.projections_query_filters:type_name -> rewardoffers.RewardConfigOption.ProjectionsQueryFilters
	70,  // 28: rewardoffers.RewardConfigOption.reward_units_generated_from_projection_unit_caps:type_name -> rewardoffers.RewardUnitsGeneratedFromProjectionUnitCaps
	22,  // 29: rewardoffers.BoostersConfig.boosters:type_name -> rewardoffers.BoosterConfig
	101, // 30: rewardoffers.BoostersConfig.claim_flow_reward_value_reveal_type:type_name -> rewards.ClaimFlowRewardValueRevealType
	82,  // 31: rewardoffers.BoosterConfig.display_details:type_name -> rewardoffers.BoosterConfig.DisplayDetails
	18,  // 32: rewardoffers.BoosterConfig.conditional_expression_booster_config:type_name -> rewardoffers.ConditionalExpressionBoosterConfig
	100, // 33: rewardoffers.SmartDepositConfig.maturity_date_config:type_name -> rewards.RewardTimeConfig
	32,  // 34: rewardoffers.RewardMeta.reward_aggregates:type_name -> rewardoffers.RewardAggregates
	100, // 35: rewardoffers.RewardMeta.reward_lock_time_config:type_name -> rewards.RewardTimeConfig
	20,  // 36: rewardoffers.RewardMeta.reward_config_options:type_name -> rewardoffers.RewardConfigOption
	83,  // 37: rewardoffers.RewardMeta.reward_display_meta:type_name -> rewardoffers.RewardMeta.RewardDisplayMeta
	42,  // 38: rewardoffers.RewardMeta.reward_notification_config:type_name -> rewardoffers.RewardNotificationConfig
	100, // 39: rewardoffers.RewardMeta.auto_claim_time_config:type_name -> rewards.RewardTimeConfig
	102, // 40: rewardoffers.RewardMeta.reward_tags:type_name -> rewards.RewardTag
	100, // 41: rewardoffers.RewardMeta.reward_expiry_time_config:type_name -> rewards.RewardTimeConfig
	33,  // 42: rewardoffers.RewardAggregates.reward_units_cap_user_aggregate:type_name -> rewardoffers.RewardUnitsCapAggregate
	33,  // 43: rewardoffers.RewardAggregates.cc_1x_reward_units_cc_account_level_monthly_aggregate_cap:type_name -> rewardoffers.RewardUnitsCapAggregate
	33,  // 44: rewardoffers.RewardAggregates.reward_units_cap_monthly_user_aggregate:type_name -> rewardoffers.RewardUnitsCapAggregate
	34,  // 45: rewardoffers.RewardAggregates.user_aggregate_in_time_period:type_name -> rewardoffers.UserAggregateInTimePeriod
	84,  // 46: rewardoffers.RewardUnitsCapAggregate.units_caps:type_name -> rewardoffers.RewardUnitsCapAggregate.RewardUnitsCap
	33,  // 47: rewardoffers.RewardOfferGroup.reward_units_cap_user_aggregate:type_name -> rewardoffers.RewardUnitsCapAggregate
	33,  // 48: rewardoffers.RewardOfferGroup.reward_units_cap_user_monthly_aggregate:type_name -> rewardoffers.RewardUnitsCapAggregate
	34,  // 49: rewardoffers.RewardOfferGroup.user_reward_aggregate_in_time_period:type_name -> rewardoffers.UserAggregateInTimePeriod
	103, // 50: rewardoffers.RewardOfferRewardUnitsActorUtilisation.created_at:type_name -> google.protobuf.Timestamp
	103, // 51: rewardoffers.RewardOfferRewardUnitsActorUtilisation.updated_at:type_name -> google.protobuf.Timestamp
	103, // 52: rewardoffers.RewardOfferGroupRewardUnitsActorUtilisation.created_at:type_name -> google.protobuf.Timestamp
	103, // 53: rewardoffers.RewardOfferGroupRewardUnitsActorUtilisation.updated_at:type_name -> google.protobuf.Timestamp
	1,   // 54: rewardoffers.DisplayMeta.display_type:type_name -> rewardoffers.DisplayType
	63,  // 55: rewardoffers.DisplayMeta.cta:type_name -> rewardoffers.CTA
	87,  // 56: rewardoffers.DisplayMeta.tags:type_name -> rewardoffers.DisplayMeta.Tag
	85,  // 57: rewardoffers.DisplayMeta.platform_to_min_supported_app_version_map:type_name -> rewardoffers.DisplayMeta.PlatformToMinSupportedAppVersionMapEntry
	86,  // 58: rewardoffers.DisplayMeta.platform_to_max_supported_app_version_map:type_name -> rewardoffers.DisplayMeta.PlatformToMaxSupportedAppVersionMapEntry
	88,  // 59: rewardoffers.DisplayMeta.steps_v1:type_name -> rewardoffers.DisplayMeta.InfoPoint
	88,  // 60: rewardoffers.DisplayMeta.tncs_v1:type_name -> rewardoffers.DisplayMeta.InfoPoint
	69,  // 61: rewardoffers.DisplayMeta.ways_to_earn_rewards_v2_screen_config:type_name -> rewardoffers.WaysToEarnRewardsV2ScreenConfig
	104, // 62: rewardoffers.RewardNotificationMetadata.email_template:type_name -> comms.EmailType
	43,  // 63: rewardoffers.RewardNotificationConfig.earned_reward_notification_config:type_name -> rewardoffers.EarnedRewardNotificationConfig
	44,  // 64: rewardoffers.RewardNotificationConfig.reward_processing_successful_notification_config:type_name -> rewardoffers.RewardProcessingSuccessfulNotificationConfig
	3,   // 65: rewardoffers.EarnedRewardNotificationConfig.notification_type:type_name -> rewardoffers.RewardNotificationType
	89,  // 66: rewardoffers.EarnedRewardNotificationConfig.notifications:type_name -> rewardoffers.EarnedRewardNotificationConfig.NotificationConfig
	90,  // 67: rewardoffers.RewardProcessingSuccessfulNotificationConfig.notifications:type_name -> rewardoffers.RewardProcessingSuccessfulNotificationConfig.NotificationConfig
	105, // 68: rewardoffers.Content.deeplink:type_name -> frontend.deeplink.Deeplink
	31,  // 69: rewardoffers.CreateRewardOfferRequest.constraint_meta:type_name -> rewardoffers.ConstraintsMeta
	30,  // 70: rewardoffers.CreateRewardOfferRequest.reward_meta:type_name -> rewardoffers.RewardMeta
	40,  // 71: rewardoffers.CreateRewardOfferRequest.display_meta:type_name -> rewardoffers.DisplayMeta
	106, // 72: rewardoffers.CreateRewardOfferRequest.offer_type:type_name -> rewards.RewardOfferType
	4,   // 73: rewardoffers.CreateRewardOfferRequest.tags:type_name -> rewardoffers.RewardOfferTag
	107, // 74: rewardoffers.CreateRewardOfferRequest.supported_platform:type_name -> api.typesv2.common.Platform
	62,  // 75: rewardoffers.CreateRewardOfferRequest.analytics_data:type_name -> rewardoffers.AnalyticsData
	108, // 76: rewardoffers.CreateRewardOfferRequest.unlock_event:type_name -> rewards.CollectedDataType
	64,  // 77: rewardoffers.CreateRewardOfferRequest.unlock_meta:type_name -> rewardoffers.UnlockMeta
	8,   // 78: rewardoffers.CreateRewardOfferRequest.generation_type:type_name -> rewardoffers.GenerationType
	71,  // 79: rewardoffers.CreateRewardOfferRequest.additional_details:type_name -> rewardoffers.AdditionalDetails
	33,  // 80: rewardoffers.CreateRewardOfferGroupRequest.reward_units_cap_user_aggregate:type_name -> rewardoffers.RewardUnitsCapAggregate
	33,  // 81: rewardoffers.CreateRewardOfferGroupRequest.reward_units_cap_user_monthly_aggregate:type_name -> rewardoffers.RewardUnitsCapAggregate
	34,  // 82: rewardoffers.CreateRewardOfferGroupRequest.user_reward_aggregate_in_time_period:type_name -> rewardoffers.UserAggregateInTimePeriod
	98,  // 83: rewardoffers.CreateRewardOfferGroupResponse.status:type_name -> rpc.Status
	35,  // 84: rewardoffers.CreateRewardOfferGroupResponse.reward_offer_group:type_name -> rewardoffers.RewardOfferGroup
	2,   // 85: rewardoffers.RewardOffer.status:type_name -> rewardoffers.RewardOfferStatus
	31,  // 86: rewardoffers.RewardOffer.constraint_meta:type_name -> rewardoffers.ConstraintsMeta
	30,  // 87: rewardoffers.RewardOffer.reward_meta:type_name -> rewardoffers.RewardMeta
	40,  // 88: rewardoffers.RewardOffer.display_meta:type_name -> rewardoffers.DisplayMeta
	106, // 89: rewardoffers.RewardOffer.offer_type:type_name -> rewards.RewardOfferType
	4,   // 90: rewardoffers.RewardOffer.tags:type_name -> rewardoffers.RewardOfferTag
	107, // 91: rewardoffers.RewardOffer.supported_platform:type_name -> api.typesv2.common.Platform
	62,  // 92: rewardoffers.RewardOffer.analytics_data:type_name -> rewardoffers.AnalyticsData
	108, // 93: rewardoffers.RewardOffer.unlock_event:type_name -> rewards.CollectedDataType
	64,  // 94: rewardoffers.RewardOffer.unlock_meta:type_name -> rewardoffers.UnlockMeta
	8,   // 95: rewardoffers.RewardOffer.generation_type:type_name -> rewardoffers.GenerationType
	103, // 96: rewardoffers.RewardOffer.updated_at:type_name -> google.protobuf.Timestamp
	71,  // 97: rewardoffers.RewardOffer.additional_details:type_name -> rewardoffers.AdditionalDetails
	98,  // 98: rewardoffers.RewardOfferResponse.status:type_name -> rpc.Status
	49,  // 99: rewardoffers.RewardOfferResponse.reward_offer:type_name -> rewardoffers.RewardOffer
	91,  // 100: rewardoffers.RewardOfferResponse.validation_failure_infos:type_name -> rewardoffers.RewardOfferResponse.ValidationFailureInfo
	2,   // 101: rewardoffers.GetRewardOffersRequest.status:type_name -> rewardoffers.RewardOfferStatus
	106, // 102: rewardoffers.GetRewardOffersRequest.offer_types:type_name -> rewards.RewardOfferType
	107, // 103: rewardoffers.GetRewardOffersRequest.supported_platform:type_name -> api.typesv2.common.Platform
	8,   // 104: rewardoffers.GetRewardOffersRequest.generation_type:type_name -> rewardoffers.GenerationType
	98,  // 105: rewardoffers.GetRewardOffersResponse.status:type_name -> rpc.Status
	49,  // 106: rewardoffers.GetRewardOffersResponse.reward_offers:type_name -> rewardoffers.RewardOffer
	98,  // 107: rewardoffers.GetRewardOffersByIdsResponse.status:type_name -> rpc.Status
	49,  // 108: rewardoffers.GetRewardOffersByIdsResponse.reward_offers:type_name -> rewardoffers.RewardOffer
	2,   // 109: rewardoffers.GetRewardOffersForActorRequest.status:type_name -> rewardoffers.RewardOfferStatus
	107, // 110: rewardoffers.GetRewardOffersForActorRequest.supported_platform:type_name -> api.typesv2.common.Platform
	8,   // 111: rewardoffers.GetRewardOffersForActorRequest.generation_type:type_name -> rewardoffers.GenerationType
	98,  // 112: rewardoffers.GetRewardOffersForActorResponse.status:type_name -> rpc.Status
	49,  // 113: rewardoffers.GetRewardOffersForActorResponse.reward_offers:type_name -> rewardoffers.RewardOffer
	39,  // 114: rewardoffers.GetRewardOffersForActorResponse.actor_level_reward_offer_inventory:type_name -> rewardoffers.RewardOfferInventory
	39,  // 115: rewardoffers.GetRewardOffersForActorResponse.action_level_reward_offer_inventory:type_name -> rewardoffers.RewardOfferInventory
	38,  // 116: rewardoffers.GetRewardOffersForActorResponse.actor_level_offer_group_inventory:type_name -> rewardoffers.RewardOfferGroupInventory
	36,  // 117: rewardoffers.GetRewardOffersForActorResponse.actor_level_reward_units_util:type_name -> rewardoffers.RewardOfferRewardUnitsActorUtilisation
	37,  // 118: rewardoffers.GetRewardOffersForActorResponse.actor_level_group_rewards_units_util:type_name -> rewardoffers.RewardOfferGroupRewardUnitsActorUtilisation
	92,  // 119: rewardoffers.GetRewardOffersForActorResponse.offer_group_id_to_offer_group_map:type_name -> rewardoffers.GetRewardOffersForActorResponse.OfferGroupIdToOfferGroupMapEntry
	93,  // 120: rewardoffers.GetRewardOffersForScreenRequest.filter:type_name -> rewardoffers.GetRewardOffersForScreenRequest.Filter
	98,  // 121: rewardoffers.GetRewardOffersForScreenResponse.status:type_name -> rpc.Status
	49,  // 122: rewardoffers.GetRewardOffersForScreenResponse.reward_offers:type_name -> rewardoffers.RewardOffer
	2,   // 123: rewardoffers.UpdateRewardOfferStatusRequest.status:type_name -> rewardoffers.RewardOfferStatus
	40,  // 124: rewardoffers.UpdateRewardOfferDisplayRequest.displayMeta:type_name -> rewardoffers.DisplayMeta
	4,   // 125: rewardoffers.UpdateRewardOfferDisplayRequest.offer_tags:type_name -> rewardoffers.RewardOfferTag
	109, // 126: rewardoffers.UpdateRewardOfferDisplayRequest.is_visible:type_name -> api.typesv2.common.BooleanEnum
	40,  // 127: rewardoffers.UpdateRewardOfferRequest.display_meta:type_name -> rewardoffers.DisplayMeta
	4,   // 128: rewardoffers.UpdateRewardOfferRequest.offer_tags:type_name -> rewardoffers.RewardOfferTag
	103, // 129: rewardoffers.UpdateRewardOfferRequest.active_till:type_name -> google.protobuf.Timestamp
	106, // 130: rewardoffers.UpdateRewardOfferRequest.reward_offer_type:type_name -> rewards.RewardOfferType
	64,  // 131: rewardoffers.UpdateRewardOfferRequest.unlock_meta:type_name -> rewardoffers.UnlockMeta
	108, // 132: rewardoffers.UpdateRewardOfferRequest.unlock_event:type_name -> rewards.CollectedDataType
	71,  // 133: rewardoffers.UpdateRewardOfferRequest.additional_details:type_name -> rewardoffers.AdditionalDetails
	103, // 134: rewardoffers.UpdateRewardOfferRequest.active_since:type_name -> google.protobuf.Timestamp
	109, // 135: rewardoffers.UpdateRewardOfferRequest.is_visible:type_name -> api.typesv2.common.BooleanEnum
	62,  // 136: rewardoffers.UpdateRewardOfferRequest.analytics_data:type_name -> rewardoffers.AnalyticsData
	5,   // 137: rewardoffers.AnalyticsData.reward_class:type_name -> rewardoffers.RewardClass
	6,   // 138: rewardoffers.AnalyticsData.reward_purpose:type_name -> rewardoffers.RewardPurpose
	7,   // 139: rewardoffers.AnalyticsData.reward_product:type_name -> rewardoffers.RewardProduct
	105, // 140: rewardoffers.CTA.deeplink:type_name -> frontend.deeplink.Deeplink
	63,  // 141: rewardoffers.UnlockMeta.cta:type_name -> rewardoffers.CTA
	31,  // 142: rewardoffers.UnlockMeta.constraint:type_name -> rewardoffers.ConstraintsMeta
	109, // 143: rewardoffers.FiCoinsRewardConfig.is_fi_points:type_name -> api.typesv2.common.BooleanEnum
	67,  // 144: rewardoffers.WaysToEarnRewardsV2ScreenConfig.offer_row_earning_potential:type_name -> rewardoffers.EarningPotential
	68,  // 145: rewardoffers.WaysToEarnRewardsV2ScreenConfig.offer_row_sub_section:type_name -> rewardoffers.IconAndText
	68,  // 146: rewardoffers.WaysToEarnRewardsV2ScreenConfig.offer_details_earning_potential_summary:type_name -> rewardoffers.IconAndText
	94,  // 147: rewardoffers.WaysToEarnRewardsV2ScreenConfig.offer_row_top_left_tag:type_name -> rewardoffers.WaysToEarnRewardsV2ScreenConfig.Tag
	94,  // 148: rewardoffers.WaysToEarnRewardsV2ScreenConfig.offer_details_tag:type_name -> rewardoffers.WaysToEarnRewardsV2ScreenConfig.Tag
	95,  // 149: rewardoffers.RewardUnitsGeneratedFromProjectionUnitCaps.offer_type_to_unit_caps_map:type_name -> rewardoffers.RewardUnitsGeneratedFromProjectionUnitCaps.OfferTypeToUnitCapsMapEntry
	96,  // 150: rewardoffers.RewardUnitsGeneratedFromProjectionUnitCaps.action_type_to_unit_caps_map:type_name -> rewardoffers.RewardUnitsGeneratedFromProjectionUnitCaps.ActionTypeToUnitCapsMapEntry
	97,  // 151: rewardoffers.RewardUnitsGeneratedFromProjectionUnitCaps.offer_id_to_unit_caps_map:type_name -> rewardoffers.RewardUnitsGeneratedFromProjectionUnitCaps.OfferIdToUnitCapsMapEntry
	72,  // 152: rewardoffers.AdditionalDetails.watson_meta:type_name -> rewardoffers.WatsonMeta
	16,  // 153: rewardoffers.ConditionalExpressionRangeProbabilityConfig.ConfigUnit.expression_range_probability_config:type_name -> rewardoffers.ExpressionRangeProbabilityConfig
	0,   // 154: rewardoffers.ConditionalExpressionBoosterConfig.ConfigUnit.operation_with_previous_value:type_name -> rewardoffers.MathematicalOperation
	76,  // 155: rewardoffers.ConditionalExpressionBoosterConfig.ConfigUnit.display_details:type_name -> rewardoffers.ConditionalExpressionBoosterConfig.ConfigUnit.DisplayDetails
	16,  // 156: rewardoffers.ConditionalExpressionBoosterConfig.ConfigUnit.expression_range_probability_config:type_name -> rewardoffers.ExpressionRangeProbabilityConfig
	102, // 157: rewardoffers.ConditionalExpressionBoosterConfig.ConfigUnit.DisplayDetails.tags:type_name -> rewards.RewardTag
	81,  // 158: rewardoffers.RewardConfigOption.Display.html_formatted_details:type_name -> rewardoffers.RewardConfigOption.Display.HtmlFormattedDetail
	102, // 159: rewardoffers.RewardConfigOption.Display.tags:type_name -> rewards.RewardTag
	108, // 160: rewardoffers.RewardConfigOption.ProjectionsQueryFilters.collected_data_types:type_name -> rewards.CollectedDataType
	106, // 161: rewardoffers.RewardConfigOption.ProjectionsQueryFilters.reward_offer_types:type_name -> rewards.RewardOfferType
	110, // 162: rewardoffers.RewardMeta.RewardDisplayMeta.reward_tile_theme_type:type_name -> rewards.RewardTileThemeType
	99,  // 163: rewardoffers.RewardUnitsCapAggregate.RewardUnitsCap.reward_type:type_name -> rewards.RewardType
	111, // 164: rewardoffers.DisplayMeta.InfoPoint.text:type_name -> api.typesv2.common.Text
	105, // 165: rewardoffers.DisplayMeta.InfoPoint.deeplink:type_name -> frontend.deeplink.Deeplink
	3,   // 166: rewardoffers.EarnedRewardNotificationConfig.NotificationConfig.notification_type:type_name -> rewardoffers.RewardNotificationType
	100, // 167: rewardoffers.EarnedRewardNotificationConfig.NotificationConfig.notification_delay_time:type_name -> rewards.RewardTimeConfig
	41,  // 168: rewardoffers.EarnedRewardNotificationConfig.NotificationConfig.notification_metadata:type_name -> rewardoffers.RewardNotificationMetadata
	45,  // 169: rewardoffers.EarnedRewardNotificationConfig.NotificationConfig.content:type_name -> rewardoffers.Content
	3,   // 170: rewardoffers.RewardProcessingSuccessfulNotificationConfig.NotificationConfig.notification_type:type_name -> rewardoffers.RewardNotificationType
	100, // 171: rewardoffers.RewardProcessingSuccessfulNotificationConfig.NotificationConfig.notification_delay_time:type_name -> rewards.RewardTimeConfig
	45,  // 172: rewardoffers.RewardProcessingSuccessfulNotificationConfig.NotificationConfig.content:type_name -> rewardoffers.Content
	41,  // 173: rewardoffers.RewardProcessingSuccessfulNotificationConfig.NotificationConfig.notification_metadata:type_name -> rewardoffers.RewardNotificationMetadata
	35,  // 174: rewardoffers.GetRewardOffersForActorResponse.OfferGroupIdToOfferGroupMapEntry.value:type_name -> rewardoffers.RewardOfferGroup
	4,   // 175: rewardoffers.GetRewardOffersForScreenRequest.Filter.tags:type_name -> rewardoffers.RewardOfferTag
	106, // 176: rewardoffers.GetRewardOffersForScreenRequest.Filter.offer_types:type_name -> rewards.RewardOfferType
	8,   // 177: rewardoffers.GetRewardOffersForScreenRequest.Filter.generation_type:type_name -> rewardoffers.GenerationType
	68,  // 178: rewardoffers.WaysToEarnRewardsV2ScreenConfig.Tag.icon_and_text:type_name -> rewardoffers.IconAndText
	47,  // 179: rewardoffers.RewardOffers.createRewardOfferGroup:input_type -> rewardoffers.CreateRewardOfferGroupRequest
	46,  // 180: rewardoffers.RewardOffers.createRewardOffer:input_type -> rewardoffers.CreateRewardOfferRequest
	51,  // 181: rewardoffers.RewardOffers.getRewardOffers:input_type -> rewardoffers.GetRewardOffersRequest
	53,  // 182: rewardoffers.RewardOffers.getRewardOffersByIds:input_type -> rewardoffers.GetRewardOffersByIdsRequest
	55,  // 183: rewardoffers.RewardOffers.getRewardOffersForActor:input_type -> rewardoffers.GetRewardOffersForActorRequest
	57,  // 184: rewardoffers.RewardOffers.getRewardOffersForScreen:input_type -> rewardoffers.GetRewardOffersForScreenRequest
	59,  // 185: rewardoffers.RewardOffers.updateRewardOfferStatus:input_type -> rewardoffers.UpdateRewardOfferStatusRequest
	60,  // 186: rewardoffers.RewardOffers.updateRewardOfferDisplay:input_type -> rewardoffers.UpdateRewardOfferDisplayRequest
	61,  // 187: rewardoffers.RewardOffers.UpdateRewardOffer:input_type -> rewardoffers.UpdateRewardOfferRequest
	9,   // 188: rewardoffers.RewardOffers.ReviewRewardOffer:input_type -> rewardoffers.ReviewRewardOfferRequest
	48,  // 189: rewardoffers.RewardOffers.createRewardOfferGroup:output_type -> rewardoffers.CreateRewardOfferGroupResponse
	50,  // 190: rewardoffers.RewardOffers.createRewardOffer:output_type -> rewardoffers.RewardOfferResponse
	52,  // 191: rewardoffers.RewardOffers.getRewardOffers:output_type -> rewardoffers.GetRewardOffersResponse
	54,  // 192: rewardoffers.RewardOffers.getRewardOffersByIds:output_type -> rewardoffers.GetRewardOffersByIdsResponse
	56,  // 193: rewardoffers.RewardOffers.getRewardOffersForActor:output_type -> rewardoffers.GetRewardOffersForActorResponse
	58,  // 194: rewardoffers.RewardOffers.getRewardOffersForScreen:output_type -> rewardoffers.GetRewardOffersForScreenResponse
	50,  // 195: rewardoffers.RewardOffers.updateRewardOfferStatus:output_type -> rewardoffers.RewardOfferResponse
	50,  // 196: rewardoffers.RewardOffers.updateRewardOfferDisplay:output_type -> rewardoffers.RewardOfferResponse
	50,  // 197: rewardoffers.RewardOffers.UpdateRewardOffer:output_type -> rewardoffers.RewardOfferResponse
	10,  // 198: rewardoffers.RewardOffers.ReviewRewardOffer:output_type -> rewardoffers.ReviewRewardOfferResponse
	189, // [189:199] is the sub-list for method output_type
	179, // [179:189] is the sub-list for method input_type
	179, // [179:179] is the sub-list for extension type_name
	179, // [179:179] is the sub-list for extension extendee
	0,   // [0:179] is the sub-list for field type_name
}

func init() { file_api_rewards_rewardoffers_reward_offer_proto_init() }
func file_api_rewards_rewardoffers_reward_offer_proto_init() {
	if File_api_rewards_rewardoffers_reward_offer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewRewardOfferRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewRewardOfferResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RangeProbabilityConfigUnit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RangeProbabilityConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FixedProbabilityConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpressionProbabilityConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FixedMultiplierConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpressionRangeProbabilityConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConditionalExpressionRangeProbabilityConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConditionalExpressionBoosterConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeriveFromSeedRewardOptionConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardConfigOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoostersConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoosterConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LuckyDrawConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmartDepositConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftHamperConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CashConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MetalCreditCardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EgvBasketConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThriweBenefitsPackageConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConstraintsMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardAggregates); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardUnitsCapAggregate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAggregateInTimePeriod); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOfferGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOfferRewardUnitsActorUtilisation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOfferGroupRewardUnitsActorUtilisation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOfferGroupInventory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOfferInventory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisplayMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardNotificationMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardNotificationConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EarnedRewardNotificationConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardProcessingSuccessfulNotificationConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Content); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRewardOfferRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRewardOfferGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRewardOfferGroupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOffer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOfferResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOffersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOffersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOffersByIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOffersByIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOffersForActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOffersForActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOffersForScreenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOffersForScreenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRewardOfferStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRewardOfferDisplayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRewardOfferRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnalyticsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CTA); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnlockMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*USStockRewardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiCoinsRewardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EarningPotential); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IconAndText); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaysToEarnRewardsV2ScreenConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardUnitsGeneratedFromProjectionUnitCaps); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdditionalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatsonMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConditionalExpressionRangeProbabilityConfig_ConfigUnit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConditionalExpressionBoosterConfig_ConfigUnit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardConfigOption_Display); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardConfigOption_ProjectionsQueryFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardConfigOption_Display_HtmlFormattedDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoosterConfig_DisplayDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardMeta_RewardDisplayMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardUnitsCapAggregate_RewardUnitsCap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisplayMeta_Tag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisplayMeta_InfoPoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EarnedRewardNotificationConfig_NotificationConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardProcessingSuccessfulNotificationConfig_NotificationConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOfferResponse_ValidationFailureInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOffersForScreenRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaysToEarnRewardsV2ScreenConfig_Tag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig_)(nil),
		(*DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig_)(nil),
	}
	file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*RewardConfigOption_ExpressionProbabilityConfig)(nil),
		(*RewardConfigOption_FixedProbabilityConfig)(nil),
		(*RewardConfigOption_RangeProbabilityConfig)(nil),
		(*RewardConfigOption_ExpressionRangeProbabilityConfig)(nil),
		(*RewardConfigOption_DeriveFromSeedRewardOptionConfig)(nil),
		(*RewardConfigOption_ConditionalExpRangeProbabilityConfig)(nil),
		(*RewardConfigOption_LuckyDrawConfig)(nil),
		(*RewardConfigOption_SmartDepositConfig)(nil),
		(*RewardConfigOption_GiftHamperConfig)(nil),
		(*RewardConfigOption_CashConfig)(nil),
		(*RewardConfigOption_MetalCreditCardConfig)(nil),
		(*RewardConfigOption_EgvBasketConfig)(nil),
		(*RewardConfigOption_ThriweBenefitsPackageConfig)(nil),
		(*RewardConfigOption_UsstockRewardConfig)(nil),
		(*RewardConfigOption_FiCoinsRewardConfig)(nil),
		(*RewardConfigOption_FixedMultiplierConfig)(nil),
	}
	file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*BoosterConfig_ConditionalExpressionBoosterConfig)(nil),
	}
	file_api_rewards_rewardoffers_reward_offer_proto_msgTypes[66].OneofWrappers = []interface{}{
		(*ConditionalExpressionBoosterConfig_ConfigUnit_ExpressionRangeProbabilityConfig)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_rewards_rewardoffers_reward_offer_proto_rawDesc,
			NumEnums:      9,
			NumMessages:   89,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_rewards_rewardoffers_reward_offer_proto_goTypes,
		DependencyIndexes: file_api_rewards_rewardoffers_reward_offer_proto_depIdxs,
		EnumInfos:         file_api_rewards_rewardoffers_reward_offer_proto_enumTypes,
		MessageInfos:      file_api_rewards_rewardoffers_reward_offer_proto_msgTypes,
	}.Build()
	File_api_rewards_rewardoffers_reward_offer_proto = out.File
	file_api_rewards_rewardoffers_reward_offer_proto_rawDesc = nil
	file_api_rewards_rewardoffers_reward_offer_proto_goTypes = nil
	file_api_rewards_rewardoffers_reward_offer_proto_depIdxs = nil
}
