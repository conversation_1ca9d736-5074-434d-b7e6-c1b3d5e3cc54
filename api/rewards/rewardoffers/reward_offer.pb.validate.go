// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/rewards/rewardoffers/reward_offer.proto

package rewardoffers

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	comms "github.com/epifi/gamma/api/comms"

	rewards "github.com/epifi/gamma/api/rewards"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Platform(0)

	_ = comms.EmailType(0)

	_ = rewards.RewardType(0)
)

// Validate checks the field values on ReviewRewardOfferRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewRewardOfferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewRewardOfferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewRewardOfferRequestMultiError, or nil if none found.
func (m *ReviewRewardOfferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewRewardOfferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RewardOfferId

	if len(errors) > 0 {
		return ReviewRewardOfferRequestMultiError(errors)
	}

	return nil
}

// ReviewRewardOfferRequestMultiError is an error wrapping multiple validation
// errors returned by ReviewRewardOfferRequest.ValidateAll() if the designated
// constraints aren't met.
type ReviewRewardOfferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewRewardOfferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewRewardOfferRequestMultiError) AllErrors() []error { return m }

// ReviewRewardOfferRequestValidationError is the validation error returned by
// ReviewRewardOfferRequest.Validate if the designated constraints aren't met.
type ReviewRewardOfferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewRewardOfferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewRewardOfferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewRewardOfferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewRewardOfferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewRewardOfferRequestValidationError) ErrorName() string {
	return "ReviewRewardOfferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewRewardOfferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewRewardOfferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewRewardOfferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewRewardOfferRequestValidationError{}

// Validate checks the field values on ReviewRewardOfferResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewRewardOfferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewRewardOfferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewRewardOfferResponseMultiError, or nil if none found.
func (m *ReviewRewardOfferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewRewardOfferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewRewardOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewRewardOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewRewardOfferResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReviewRewardOfferResponseMultiError(errors)
	}

	return nil
}

// ReviewRewardOfferResponseMultiError is an error wrapping multiple validation
// errors returned by ReviewRewardOfferResponse.ValidateAll() if the
// designated constraints aren't met.
type ReviewRewardOfferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewRewardOfferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewRewardOfferResponseMultiError) AllErrors() []error { return m }

// ReviewRewardOfferResponseValidationError is the validation error returned by
// ReviewRewardOfferResponse.Validate if the designated constraints aren't met.
type ReviewRewardOfferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewRewardOfferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewRewardOfferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewRewardOfferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewRewardOfferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewRewardOfferResponseValidationError) ErrorName() string {
	return "ReviewRewardOfferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewRewardOfferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewRewardOfferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewRewardOfferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewRewardOfferResponseValidationError{}

// Validate checks the field values on RangeProbabilityConfigUnit with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RangeProbabilityConfigUnit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RangeProbabilityConfigUnit with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RangeProbabilityConfigUnitMultiError, or nil if none found.
func (m *RangeProbabilityConfigUnit) ValidateAll() error {
	return m.validate(true)
}

func (m *RangeProbabilityConfigUnit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Start

	// no validation rules for End

	// no validation rules for Percentage

	if len(errors) > 0 {
		return RangeProbabilityConfigUnitMultiError(errors)
	}

	return nil
}

// RangeProbabilityConfigUnitMultiError is an error wrapping multiple
// validation errors returned by RangeProbabilityConfigUnit.ValidateAll() if
// the designated constraints aren't met.
type RangeProbabilityConfigUnitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RangeProbabilityConfigUnitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RangeProbabilityConfigUnitMultiError) AllErrors() []error { return m }

// RangeProbabilityConfigUnitValidationError is the validation error returned
// by RangeProbabilityConfigUnit.Validate if the designated constraints aren't met.
type RangeProbabilityConfigUnitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RangeProbabilityConfigUnitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RangeProbabilityConfigUnitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RangeProbabilityConfigUnitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RangeProbabilityConfigUnitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RangeProbabilityConfigUnitValidationError) ErrorName() string {
	return "RangeProbabilityConfigUnitValidationError"
}

// Error satisfies the builtin error interface
func (e RangeProbabilityConfigUnitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRangeProbabilityConfigUnit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RangeProbabilityConfigUnitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RangeProbabilityConfigUnitValidationError{}

// Validate checks the field values on RangeProbabilityConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RangeProbabilityConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RangeProbabilityConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RangeProbabilityConfigMultiError, or nil if none found.
func (m *RangeProbabilityConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *RangeProbabilityConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetConfigUnits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RangeProbabilityConfigValidationError{
						field:  fmt.Sprintf("ConfigUnits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RangeProbabilityConfigValidationError{
						field:  fmt.Sprintf("ConfigUnits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RangeProbabilityConfigValidationError{
					field:  fmt.Sprintf("ConfigUnits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Probability

	if len(errors) > 0 {
		return RangeProbabilityConfigMultiError(errors)
	}

	return nil
}

// RangeProbabilityConfigMultiError is an error wrapping multiple validation
// errors returned by RangeProbabilityConfig.ValidateAll() if the designated
// constraints aren't met.
type RangeProbabilityConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RangeProbabilityConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RangeProbabilityConfigMultiError) AllErrors() []error { return m }

// RangeProbabilityConfigValidationError is the validation error returned by
// RangeProbabilityConfig.Validate if the designated constraints aren't met.
type RangeProbabilityConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RangeProbabilityConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RangeProbabilityConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RangeProbabilityConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RangeProbabilityConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RangeProbabilityConfigValidationError) ErrorName() string {
	return "RangeProbabilityConfigValidationError"
}

// Error satisfies the builtin error interface
func (e RangeProbabilityConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRangeProbabilityConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RangeProbabilityConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RangeProbabilityConfigValidationError{}

// Validate checks the field values on FixedProbabilityConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FixedProbabilityConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FixedProbabilityConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FixedProbabilityConfigMultiError, or nil if none found.
func (m *FixedProbabilityConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *FixedProbabilityConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Value

	// no validation rules for Probability

	if len(errors) > 0 {
		return FixedProbabilityConfigMultiError(errors)
	}

	return nil
}

// FixedProbabilityConfigMultiError is an error wrapping multiple validation
// errors returned by FixedProbabilityConfig.ValidateAll() if the designated
// constraints aren't met.
type FixedProbabilityConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FixedProbabilityConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FixedProbabilityConfigMultiError) AllErrors() []error { return m }

// FixedProbabilityConfigValidationError is the validation error returned by
// FixedProbabilityConfig.Validate if the designated constraints aren't met.
type FixedProbabilityConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FixedProbabilityConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FixedProbabilityConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FixedProbabilityConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FixedProbabilityConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FixedProbabilityConfigValidationError) ErrorName() string {
	return "FixedProbabilityConfigValidationError"
}

// Error satisfies the builtin error interface
func (e FixedProbabilityConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFixedProbabilityConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FixedProbabilityConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FixedProbabilityConfigValidationError{}

// Validate checks the field values on ExpressionProbabilityConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExpressionProbabilityConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExpressionProbabilityConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExpressionProbabilityConfigMultiError, or nil if none found.
func (m *ExpressionProbabilityConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ExpressionProbabilityConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Expression

	// no validation rules for Probability

	if len(errors) > 0 {
		return ExpressionProbabilityConfigMultiError(errors)
	}

	return nil
}

// ExpressionProbabilityConfigMultiError is an error wrapping multiple
// validation errors returned by ExpressionProbabilityConfig.ValidateAll() if
// the designated constraints aren't met.
type ExpressionProbabilityConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExpressionProbabilityConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExpressionProbabilityConfigMultiError) AllErrors() []error { return m }

// ExpressionProbabilityConfigValidationError is the validation error returned
// by ExpressionProbabilityConfig.Validate if the designated constraints
// aren't met.
type ExpressionProbabilityConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExpressionProbabilityConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExpressionProbabilityConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExpressionProbabilityConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExpressionProbabilityConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExpressionProbabilityConfigValidationError) ErrorName() string {
	return "ExpressionProbabilityConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ExpressionProbabilityConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExpressionProbabilityConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExpressionProbabilityConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExpressionProbabilityConfigValidationError{}

// Validate checks the field values on FixedMultiplierConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FixedMultiplierConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FixedMultiplierConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FixedMultiplierConfigMultiError, or nil if none found.
func (m *FixedMultiplierConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *FixedMultiplierConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Multiplier

	if len(errors) > 0 {
		return FixedMultiplierConfigMultiError(errors)
	}

	return nil
}

// FixedMultiplierConfigMultiError is an error wrapping multiple validation
// errors returned by FixedMultiplierConfig.ValidateAll() if the designated
// constraints aren't met.
type FixedMultiplierConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FixedMultiplierConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FixedMultiplierConfigMultiError) AllErrors() []error { return m }

// FixedMultiplierConfigValidationError is the validation error returned by
// FixedMultiplierConfig.Validate if the designated constraints aren't met.
type FixedMultiplierConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FixedMultiplierConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FixedMultiplierConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FixedMultiplierConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FixedMultiplierConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FixedMultiplierConfigValidationError) ErrorName() string {
	return "FixedMultiplierConfigValidationError"
}

// Error satisfies the builtin error interface
func (e FixedMultiplierConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFixedMultiplierConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FixedMultiplierConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FixedMultiplierConfigValidationError{}

// Validate checks the field values on ExpressionRangeProbabilityConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ExpressionRangeProbabilityConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExpressionRangeProbabilityConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ExpressionRangeProbabilityConfigMultiError, or nil if none found.
func (m *ExpressionRangeProbabilityConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ExpressionRangeProbabilityConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Expression

	for idx, item := range m.GetRangeProbabilityUnits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExpressionRangeProbabilityConfigValidationError{
						field:  fmt.Sprintf("RangeProbabilityUnits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExpressionRangeProbabilityConfigValidationError{
						field:  fmt.Sprintf("RangeProbabilityUnits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExpressionRangeProbabilityConfigValidationError{
					field:  fmt.Sprintf("RangeProbabilityUnits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UpperLimit

	// no validation rules for LowerLimit

	if len(errors) > 0 {
		return ExpressionRangeProbabilityConfigMultiError(errors)
	}

	return nil
}

// ExpressionRangeProbabilityConfigMultiError is an error wrapping multiple
// validation errors returned by
// ExpressionRangeProbabilityConfig.ValidateAll() if the designated
// constraints aren't met.
type ExpressionRangeProbabilityConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExpressionRangeProbabilityConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExpressionRangeProbabilityConfigMultiError) AllErrors() []error { return m }

// ExpressionRangeProbabilityConfigValidationError is the validation error
// returned by ExpressionRangeProbabilityConfig.Validate if the designated
// constraints aren't met.
type ExpressionRangeProbabilityConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExpressionRangeProbabilityConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExpressionRangeProbabilityConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExpressionRangeProbabilityConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExpressionRangeProbabilityConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExpressionRangeProbabilityConfigValidationError) ErrorName() string {
	return "ExpressionRangeProbabilityConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ExpressionRangeProbabilityConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExpressionRangeProbabilityConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExpressionRangeProbabilityConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExpressionRangeProbabilityConfigValidationError{}

// Validate checks the field values on
// ConditionalExpressionRangeProbabilityConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ConditionalExpressionRangeProbabilityConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ConditionalExpressionRangeProbabilityConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ConditionalExpressionRangeProbabilityConfigMultiError, or nil if none found.
func (m *ConditionalExpressionRangeProbabilityConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ConditionalExpressionRangeProbabilityConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetConfigUnits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ConditionalExpressionRangeProbabilityConfigValidationError{
						field:  fmt.Sprintf("ConfigUnits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ConditionalExpressionRangeProbabilityConfigValidationError{
						field:  fmt.Sprintf("ConfigUnits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ConditionalExpressionRangeProbabilityConfigValidationError{
					field:  fmt.Sprintf("ConfigUnits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ConditionalExpressionRangeProbabilityConfigMultiError(errors)
	}

	return nil
}

// ConditionalExpressionRangeProbabilityConfigMultiError is an error wrapping
// multiple validation errors returned by
// ConditionalExpressionRangeProbabilityConfig.ValidateAll() if the designated
// constraints aren't met.
type ConditionalExpressionRangeProbabilityConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConditionalExpressionRangeProbabilityConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConditionalExpressionRangeProbabilityConfigMultiError) AllErrors() []error { return m }

// ConditionalExpressionRangeProbabilityConfigValidationError is the validation
// error returned by ConditionalExpressionRangeProbabilityConfig.Validate if
// the designated constraints aren't met.
type ConditionalExpressionRangeProbabilityConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConditionalExpressionRangeProbabilityConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConditionalExpressionRangeProbabilityConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConditionalExpressionRangeProbabilityConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConditionalExpressionRangeProbabilityConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConditionalExpressionRangeProbabilityConfigValidationError) ErrorName() string {
	return "ConditionalExpressionRangeProbabilityConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ConditionalExpressionRangeProbabilityConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConditionalExpressionRangeProbabilityConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConditionalExpressionRangeProbabilityConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConditionalExpressionRangeProbabilityConfigValidationError{}

// Validate checks the field values on ConditionalExpressionBoosterConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ConditionalExpressionBoosterConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConditionalExpressionBoosterConfig
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConditionalExpressionBoosterConfigMultiError, or nil if none found.
func (m *ConditionalExpressionBoosterConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ConditionalExpressionBoosterConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetConfigUnits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ConditionalExpressionBoosterConfigValidationError{
						field:  fmt.Sprintf("ConfigUnits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ConditionalExpressionBoosterConfigValidationError{
						field:  fmt.Sprintf("ConfigUnits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ConditionalExpressionBoosterConfigValidationError{
					field:  fmt.Sprintf("ConfigUnits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ConditionalExpressionBoosterConfigMultiError(errors)
	}

	return nil
}

// ConditionalExpressionBoosterConfigMultiError is an error wrapping multiple
// validation errors returned by
// ConditionalExpressionBoosterConfig.ValidateAll() if the designated
// constraints aren't met.
type ConditionalExpressionBoosterConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConditionalExpressionBoosterConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConditionalExpressionBoosterConfigMultiError) AllErrors() []error { return m }

// ConditionalExpressionBoosterConfigValidationError is the validation error
// returned by ConditionalExpressionBoosterConfig.Validate if the designated
// constraints aren't met.
type ConditionalExpressionBoosterConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConditionalExpressionBoosterConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConditionalExpressionBoosterConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConditionalExpressionBoosterConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConditionalExpressionBoosterConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConditionalExpressionBoosterConfigValidationError) ErrorName() string {
	return "ConditionalExpressionBoosterConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ConditionalExpressionBoosterConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConditionalExpressionBoosterConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConditionalExpressionBoosterConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConditionalExpressionBoosterConfigValidationError{}

// Validate checks the field values on DeriveFromSeedRewardOptionConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeriveFromSeedRewardOptionConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeriveFromSeedRewardOptionConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeriveFromSeedRewardOptionConfigMultiError, or nil if none found.
func (m *DeriveFromSeedRewardOptionConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *DeriveFromSeedRewardOptionConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SeedRewardOptionIndex

	switch v := m.DerivationConfig.(type) {
	case *DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig_:
		if v == nil {
			err := DeriveFromSeedRewardOptionConfigValidationError{
				field:  "DerivationConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDeltaDerivationConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeriveFromSeedRewardOptionConfigValidationError{
						field:  "DeltaDerivationConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeriveFromSeedRewardOptionConfigValidationError{
						field:  "DeltaDerivationConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeltaDerivationConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeriveFromSeedRewardOptionConfigValidationError{
					field:  "DeltaDerivationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig_:
		if v == nil {
			err := DeriveFromSeedRewardOptionConfigValidationError{
				field:  "DerivationConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMultiplierDerivationConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeriveFromSeedRewardOptionConfigValidationError{
						field:  "MultiplierDerivationConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeriveFromSeedRewardOptionConfigValidationError{
						field:  "MultiplierDerivationConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMultiplierDerivationConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeriveFromSeedRewardOptionConfigValidationError{
					field:  "MultiplierDerivationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return DeriveFromSeedRewardOptionConfigMultiError(errors)
	}

	return nil
}

// DeriveFromSeedRewardOptionConfigMultiError is an error wrapping multiple
// validation errors returned by
// DeriveFromSeedRewardOptionConfig.ValidateAll() if the designated
// constraints aren't met.
type DeriveFromSeedRewardOptionConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeriveFromSeedRewardOptionConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeriveFromSeedRewardOptionConfigMultiError) AllErrors() []error { return m }

// DeriveFromSeedRewardOptionConfigValidationError is the validation error
// returned by DeriveFromSeedRewardOptionConfig.Validate if the designated
// constraints aren't met.
type DeriveFromSeedRewardOptionConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeriveFromSeedRewardOptionConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeriveFromSeedRewardOptionConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeriveFromSeedRewardOptionConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeriveFromSeedRewardOptionConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeriveFromSeedRewardOptionConfigValidationError) ErrorName() string {
	return "DeriveFromSeedRewardOptionConfigValidationError"
}

// Error satisfies the builtin error interface
func (e DeriveFromSeedRewardOptionConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeriveFromSeedRewardOptionConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeriveFromSeedRewardOptionConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeriveFromSeedRewardOptionConfigValidationError{}

// Validate checks the field values on RewardConfigOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardConfigOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardConfigOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardConfigOptionMultiError, or nil if none found.
func (m *RewardConfigOption) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardConfigOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _RewardConfigOption_RewardType_NotInLookup[m.GetRewardType()]; ok {
		err := RewardConfigOptionValidationError{
			field:  "RewardType",
			reason: "value must not be in list [REWARD_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDisplayConfig() == nil {
		err := RewardConfigOptionValidationError{
			field:  "DisplayConfig",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDisplayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardConfigOptionValidationError{
					field:  "DisplayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardConfigOptionValidationError{
					field:  "DisplayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardConfigOptionValidationError{
				field:  "DisplayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetRewardProcessingTimeConfig() == nil {
		err := RewardConfigOptionValidationError{
			field:  "RewardProcessingTimeConfig",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRewardProcessingTimeConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardConfigOptionValidationError{
					field:  "RewardProcessingTimeConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardConfigOptionValidationError{
					field:  "RewardProcessingTimeConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardProcessingTimeConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardConfigOptionValidationError{
				field:  "RewardProcessingTimeConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ConstraintExpression

	if all {
		switch v := interface{}(m.GetBoostersConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardConfigOptionValidationError{
					field:  "BoostersConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardConfigOptionValidationError{
					field:  "BoostersConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBoostersConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardConfigOptionValidationError{
				field:  "BoostersConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProductSku

	// no validation rules for ShouldGenerateFromProjections

	if all {
		switch v := interface{}(m.GetProjectionsQueryFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardConfigOptionValidationError{
					field:  "ProjectionsQueryFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardConfigOptionValidationError{
					field:  "ProjectionsQueryFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProjectionsQueryFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardConfigOptionValidationError{
				field:  "ProjectionsQueryFilters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RewardUnitsLowerLimit

	// no validation rules for RewardUnitsUpperLimit

	if all {
		switch v := interface{}(m.GetRewardUnitsGeneratedFromProjectionUnitCaps()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardConfigOptionValidationError{
					field:  "RewardUnitsGeneratedFromProjectionUnitCaps",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardConfigOptionValidationError{
					field:  "RewardUnitsGeneratedFromProjectionUnitCaps",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardUnitsGeneratedFromProjectionUnitCaps()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardConfigOptionValidationError{
				field:  "RewardUnitsGeneratedFromProjectionUnitCaps",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.UnitsConfig.(type) {
	case *RewardConfigOption_ExpressionProbabilityConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "UnitsConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExpressionProbabilityConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "ExpressionProbabilityConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "ExpressionProbabilityConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExpressionProbabilityConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "ExpressionProbabilityConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RewardConfigOption_FixedProbabilityConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "UnitsConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFixedProbabilityConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "FixedProbabilityConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "FixedProbabilityConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFixedProbabilityConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "FixedProbabilityConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RewardConfigOption_RangeProbabilityConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "UnitsConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRangeProbabilityConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "RangeProbabilityConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "RangeProbabilityConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRangeProbabilityConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "RangeProbabilityConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RewardConfigOption_ExpressionRangeProbabilityConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "UnitsConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExpressionRangeProbabilityConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "ExpressionRangeProbabilityConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "ExpressionRangeProbabilityConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExpressionRangeProbabilityConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "ExpressionRangeProbabilityConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RewardConfigOption_DeriveFromSeedRewardOptionConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "UnitsConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDeriveFromSeedRewardOptionConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "DeriveFromSeedRewardOptionConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "DeriveFromSeedRewardOptionConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeriveFromSeedRewardOptionConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "DeriveFromSeedRewardOptionConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RewardConfigOption_ConditionalExpRangeProbabilityConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "UnitsConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetConditionalExpRangeProbabilityConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "ConditionalExpRangeProbabilityConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "ConditionalExpRangeProbabilityConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetConditionalExpRangeProbabilityConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "ConditionalExpRangeProbabilityConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.RewardConfig.(type) {
	case *RewardConfigOption_LuckyDrawConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "RewardConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLuckyDrawConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "LuckyDrawConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "LuckyDrawConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLuckyDrawConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "LuckyDrawConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RewardConfigOption_SmartDepositConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "RewardConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSmartDepositConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "SmartDepositConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "SmartDepositConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSmartDepositConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "SmartDepositConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RewardConfigOption_GiftHamperConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "RewardConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGiftHamperConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "GiftHamperConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "GiftHamperConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGiftHamperConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "GiftHamperConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RewardConfigOption_CashConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "RewardConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCashConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "CashConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "CashConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCashConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "CashConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RewardConfigOption_MetalCreditCardConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "RewardConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMetalCreditCardConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "MetalCreditCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "MetalCreditCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMetalCreditCardConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "MetalCreditCardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RewardConfigOption_EgvBasketConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "RewardConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEgvBasketConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "EgvBasketConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "EgvBasketConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEgvBasketConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "EgvBasketConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RewardConfigOption_ThriweBenefitsPackageConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "RewardConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetThriweBenefitsPackageConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "ThriweBenefitsPackageConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "ThriweBenefitsPackageConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetThriweBenefitsPackageConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "ThriweBenefitsPackageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RewardConfigOption_UsstockRewardConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "RewardConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUsstockRewardConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "UsstockRewardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "UsstockRewardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUsstockRewardConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "UsstockRewardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RewardConfigOption_FiCoinsRewardConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "RewardConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFiCoinsRewardConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "FiCoinsRewardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "FiCoinsRewardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFiCoinsRewardConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "FiCoinsRewardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.MultiplierConfig.(type) {
	case *RewardConfigOption_FixedMultiplierConfig:
		if v == nil {
			err := RewardConfigOptionValidationError{
				field:  "MultiplierConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFixedMultiplierConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "FixedMultiplierConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOptionValidationError{
						field:  "FixedMultiplierConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFixedMultiplierConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOptionValidationError{
					field:  "FixedMultiplierConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RewardConfigOptionMultiError(errors)
	}

	return nil
}

// RewardConfigOptionMultiError is an error wrapping multiple validation errors
// returned by RewardConfigOption.ValidateAll() if the designated constraints
// aren't met.
type RewardConfigOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardConfigOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardConfigOptionMultiError) AllErrors() []error { return m }

// RewardConfigOptionValidationError is the validation error returned by
// RewardConfigOption.Validate if the designated constraints aren't met.
type RewardConfigOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardConfigOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardConfigOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardConfigOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardConfigOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardConfigOptionValidationError) ErrorName() string {
	return "RewardConfigOptionValidationError"
}

// Error satisfies the builtin error interface
func (e RewardConfigOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardConfigOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardConfigOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardConfigOptionValidationError{}

var _RewardConfigOption_RewardType_NotInLookup = map[rewards.RewardType]struct{}{
	0: {},
}

// Validate checks the field values on BoostersConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BoostersConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BoostersConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BoostersConfigMultiError,
// or nil if none found.
func (m *BoostersConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *BoostersConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBoosters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BoostersConfigValidationError{
						field:  fmt.Sprintf("Boosters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BoostersConfigValidationError{
						field:  fmt.Sprintf("Boosters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BoostersConfigValidationError{
					field:  fmt.Sprintf("Boosters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ClaimFlowRewardValueRevealType

	if len(errors) > 0 {
		return BoostersConfigMultiError(errors)
	}

	return nil
}

// BoostersConfigMultiError is an error wrapping multiple validation errors
// returned by BoostersConfig.ValidateAll() if the designated constraints
// aren't met.
type BoostersConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BoostersConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BoostersConfigMultiError) AllErrors() []error { return m }

// BoostersConfigValidationError is the validation error returned by
// BoostersConfig.Validate if the designated constraints aren't met.
type BoostersConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BoostersConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BoostersConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BoostersConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BoostersConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BoostersConfigValidationError) ErrorName() string { return "BoostersConfigValidationError" }

// Error satisfies the builtin error interface
func (e BoostersConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBoostersConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BoostersConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BoostersConfigValidationError{}

// Validate checks the field values on BoosterConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BoosterConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BoosterConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BoosterConfigMultiError, or
// nil if none found.
func (m *BoosterConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *BoosterConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDisplayDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BoosterConfigValidationError{
					field:  "DisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BoosterConfigValidationError{
					field:  "DisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BoosterConfigValidationError{
				field:  "DisplayDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.UnitsConfig.(type) {
	case *BoosterConfig_ConditionalExpressionBoosterConfig:
		if v == nil {
			err := BoosterConfigValidationError{
				field:  "UnitsConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetConditionalExpressionBoosterConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BoosterConfigValidationError{
						field:  "ConditionalExpressionBoosterConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BoosterConfigValidationError{
						field:  "ConditionalExpressionBoosterConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetConditionalExpressionBoosterConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BoosterConfigValidationError{
					field:  "ConditionalExpressionBoosterConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return BoosterConfigMultiError(errors)
	}

	return nil
}

// BoosterConfigMultiError is an error wrapping multiple validation errors
// returned by BoosterConfig.ValidateAll() if the designated constraints
// aren't met.
type BoosterConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BoosterConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BoosterConfigMultiError) AllErrors() []error { return m }

// BoosterConfigValidationError is the validation error returned by
// BoosterConfig.Validate if the designated constraints aren't met.
type BoosterConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BoosterConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BoosterConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BoosterConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BoosterConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BoosterConfigValidationError) ErrorName() string { return "BoosterConfigValidationError" }

// Error satisfies the builtin error interface
func (e BoosterConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBoosterConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BoosterConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BoosterConfigValidationError{}

// Validate checks the field values on LuckyDrawConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LuckyDrawConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuckyDrawConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuckyDrawConfigMultiError, or nil if none found.
func (m *LuckyDrawConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *LuckyDrawConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LuckyDrawId

	if len(errors) > 0 {
		return LuckyDrawConfigMultiError(errors)
	}

	return nil
}

// LuckyDrawConfigMultiError is an error wrapping multiple validation errors
// returned by LuckyDrawConfig.ValidateAll() if the designated constraints
// aren't met.
type LuckyDrawConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuckyDrawConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuckyDrawConfigMultiError) AllErrors() []error { return m }

// LuckyDrawConfigValidationError is the validation error returned by
// LuckyDrawConfig.Validate if the designated constraints aren't met.
type LuckyDrawConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuckyDrawConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuckyDrawConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuckyDrawConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuckyDrawConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuckyDrawConfigValidationError) ErrorName() string { return "LuckyDrawConfigValidationError" }

// Error satisfies the builtin error interface
func (e LuckyDrawConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuckyDrawConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuckyDrawConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuckyDrawConfigValidationError{}

// Validate checks the field values on SmartDepositConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SmartDepositConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SmartDepositConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SmartDepositConfigMultiError, or nil if none found.
func (m *SmartDepositConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *SmartDepositConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMaturityDateConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SmartDepositConfigValidationError{
					field:  "MaturityDateConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SmartDepositConfigValidationError{
					field:  "MaturityDateConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaturityDateConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SmartDepositConfigValidationError{
				field:  "MaturityDateConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SmartDepositConfigMultiError(errors)
	}

	return nil
}

// SmartDepositConfigMultiError is an error wrapping multiple validation errors
// returned by SmartDepositConfig.ValidateAll() if the designated constraints
// aren't met.
type SmartDepositConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SmartDepositConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SmartDepositConfigMultiError) AllErrors() []error { return m }

// SmartDepositConfigValidationError is the validation error returned by
// SmartDepositConfig.Validate if the designated constraints aren't met.
type SmartDepositConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SmartDepositConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SmartDepositConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SmartDepositConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SmartDepositConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SmartDepositConfigValidationError) ErrorName() string {
	return "SmartDepositConfigValidationError"
}

// Error satisfies the builtin error interface
func (e SmartDepositConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSmartDepositConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SmartDepositConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SmartDepositConfigValidationError{}

// Validate checks the field values on GiftHamperConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GiftHamperConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GiftHamperConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GiftHamperConfigMultiError, or nil if none found.
func (m *GiftHamperConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *GiftHamperConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VendorName

	// no validation rules for VendorProductId

	// no validation rules for ProductName

	if len(errors) > 0 {
		return GiftHamperConfigMultiError(errors)
	}

	return nil
}

// GiftHamperConfigMultiError is an error wrapping multiple validation errors
// returned by GiftHamperConfig.ValidateAll() if the designated constraints
// aren't met.
type GiftHamperConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GiftHamperConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GiftHamperConfigMultiError) AllErrors() []error { return m }

// GiftHamperConfigValidationError is the validation error returned by
// GiftHamperConfig.Validate if the designated constraints aren't met.
type GiftHamperConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GiftHamperConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GiftHamperConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GiftHamperConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GiftHamperConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GiftHamperConfigValidationError) ErrorName() string { return "GiftHamperConfigValidationError" }

// Error satisfies the builtin error interface
func (e GiftHamperConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGiftHamperConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GiftHamperConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GiftHamperConfigValidationError{}

// Validate checks the field values on CashConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CashConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CashConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CashConfigMultiError, or
// nil if none found.
func (m *CashConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *CashConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnRemarkExpression

	if len(errors) > 0 {
		return CashConfigMultiError(errors)
	}

	return nil
}

// CashConfigMultiError is an error wrapping multiple validation errors
// returned by CashConfig.ValidateAll() if the designated constraints aren't met.
type CashConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CashConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CashConfigMultiError) AllErrors() []error { return m }

// CashConfigValidationError is the validation error returned by
// CashConfig.Validate if the designated constraints aren't met.
type CashConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CashConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CashConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CashConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CashConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CashConfigValidationError) ErrorName() string { return "CashConfigValidationError" }

// Error satisfies the builtin error interface
func (e CashConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCashConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CashConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CashConfigValidationError{}

// Validate checks the field values on MetalCreditCardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MetalCreditCardConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MetalCreditCardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MetalCreditCardConfigMultiError, or nil if none found.
func (m *MetalCreditCardConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *MetalCreditCardConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return MetalCreditCardConfigMultiError(errors)
	}

	return nil
}

// MetalCreditCardConfigMultiError is an error wrapping multiple validation
// errors returned by MetalCreditCardConfig.ValidateAll() if the designated
// constraints aren't met.
type MetalCreditCardConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MetalCreditCardConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MetalCreditCardConfigMultiError) AllErrors() []error { return m }

// MetalCreditCardConfigValidationError is the validation error returned by
// MetalCreditCardConfig.Validate if the designated constraints aren't met.
type MetalCreditCardConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MetalCreditCardConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MetalCreditCardConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MetalCreditCardConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MetalCreditCardConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MetalCreditCardConfigValidationError) ErrorName() string {
	return "MetalCreditCardConfigValidationError"
}

// Error satisfies the builtin error interface
func (e MetalCreditCardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMetalCreditCardConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MetalCreditCardConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MetalCreditCardConfigValidationError{}

// Validate checks the field values on EgvBasketConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EgvBasketConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EgvBasketConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EgvBasketConfigMultiError, or nil if none found.
func (m *EgvBasketConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *EgvBasketConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return EgvBasketConfigMultiError(errors)
	}

	return nil
}

// EgvBasketConfigMultiError is an error wrapping multiple validation errors
// returned by EgvBasketConfig.ValidateAll() if the designated constraints
// aren't met.
type EgvBasketConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EgvBasketConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EgvBasketConfigMultiError) AllErrors() []error { return m }

// EgvBasketConfigValidationError is the validation error returned by
// EgvBasketConfig.Validate if the designated constraints aren't met.
type EgvBasketConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EgvBasketConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EgvBasketConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EgvBasketConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EgvBasketConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EgvBasketConfigValidationError) ErrorName() string { return "EgvBasketConfigValidationError" }

// Error satisfies the builtin error interface
func (e EgvBasketConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEgvBasketConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EgvBasketConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EgvBasketConfigValidationError{}

// Validate checks the field values on ThriweBenefitsPackageConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ThriweBenefitsPackageConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThriweBenefitsPackageConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ThriweBenefitsPackageConfigMultiError, or nil if none found.
func (m *ThriweBenefitsPackageConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ThriweBenefitsPackageConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	if len(errors) > 0 {
		return ThriweBenefitsPackageConfigMultiError(errors)
	}

	return nil
}

// ThriweBenefitsPackageConfigMultiError is an error wrapping multiple
// validation errors returned by ThriweBenefitsPackageConfig.ValidateAll() if
// the designated constraints aren't met.
type ThriweBenefitsPackageConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThriweBenefitsPackageConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThriweBenefitsPackageConfigMultiError) AllErrors() []error { return m }

// ThriweBenefitsPackageConfigValidationError is the validation error returned
// by ThriweBenefitsPackageConfig.Validate if the designated constraints
// aren't met.
type ThriweBenefitsPackageConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThriweBenefitsPackageConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThriweBenefitsPackageConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThriweBenefitsPackageConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThriweBenefitsPackageConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThriweBenefitsPackageConfigValidationError) ErrorName() string {
	return "ThriweBenefitsPackageConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ThriweBenefitsPackageConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThriweBenefitsPackageConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThriweBenefitsPackageConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThriweBenefitsPackageConfigValidationError{}

// Validate checks the field values on RewardMeta with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RewardMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RewardMetaMultiError, or
// nil if none found.
func (m *RewardMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRewardAggregates() == nil {
		err := RewardMetaValidationError{
			field:  "RewardAggregates",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRewardAggregates()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardMetaValidationError{
					field:  "RewardAggregates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardMetaValidationError{
					field:  "RewardAggregates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardAggregates()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardMetaValidationError{
				field:  "RewardAggregates",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetRewardLockTimeConfig() == nil {
		err := RewardMetaValidationError{
			field:  "RewardLockTimeConfig",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRewardLockTimeConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardMetaValidationError{
					field:  "RewardLockTimeConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardMetaValidationError{
					field:  "RewardLockTimeConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardLockTimeConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardMetaValidationError{
				field:  "RewardLockTimeConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetDefaultDecideTimeInSecs() <= 0 {
		err := RewardMetaValidationError{
			field:  "DefaultDecideTimeInSecs",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetProbability() <= 0 {
		err := RewardMetaValidationError{
			field:  "Probability",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetRewardConfigOptions()) < 1 {
		err := RewardMetaValidationError{
			field:  "RewardConfigOptions",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetRewardConfigOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardMetaValidationError{
						field:  fmt.Sprintf("RewardConfigOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardMetaValidationError{
						field:  fmt.Sprintf("RewardConfigOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardMetaValidationError{
					field:  fmt.Sprintf("RewardConfigOptions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsVisible

	// no validation rules for AutoProcessReward

	if all {
		switch v := interface{}(m.GetRewardDisplayMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardMetaValidationError{
					field:  "RewardDisplayMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardMetaValidationError{
					field:  "RewardDisplayMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardDisplayMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardMetaValidationError{
				field:  "RewardDisplayMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetRewardNotificationConfig() == nil {
		err := RewardMetaValidationError{
			field:  "RewardNotificationConfig",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRewardNotificationConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardMetaValidationError{
					field:  "RewardNotificationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardMetaValidationError{
					field:  "RewardNotificationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardNotificationConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardMetaValidationError{
				field:  "RewardNotificationConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsUnlockedForMinKyc

	if all {
		switch v := interface{}(m.GetAutoClaimTimeConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardMetaValidationError{
					field:  "AutoClaimTimeConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardMetaValidationError{
					field:  "AutoClaimTimeConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAutoClaimTimeConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardMetaValidationError{
				field:  "AutoClaimTimeConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsImplicitLockingDisabled

	if all {
		switch v := interface{}(m.GetRewardExpiryTimeConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardMetaValidationError{
					field:  "RewardExpiryTimeConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardMetaValidationError{
					field:  "RewardExpiryTimeConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardExpiryTimeConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardMetaValidationError{
				field:  "RewardExpiryTimeConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardMetaMultiError(errors)
	}

	return nil
}

// RewardMetaMultiError is an error wrapping multiple validation errors
// returned by RewardMeta.ValidateAll() if the designated constraints aren't met.
type RewardMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardMetaMultiError) AllErrors() []error { return m }

// RewardMetaValidationError is the validation error returned by
// RewardMeta.Validate if the designated constraints aren't met.
type RewardMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardMetaValidationError) ErrorName() string { return "RewardMetaValidationError" }

// Error satisfies the builtin error interface
func (e RewardMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardMetaValidationError{}

// Validate checks the field values on ConstraintsMeta with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ConstraintsMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConstraintsMeta with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConstraintsMetaMultiError, or nil if none found.
func (m *ConstraintsMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *ConstraintsMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetExpression()) < 1 {
		err := ConstraintsMetaValidationError{
			field:  "Expression",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ConstraintsMetaMultiError(errors)
	}

	return nil
}

// ConstraintsMetaMultiError is an error wrapping multiple validation errors
// returned by ConstraintsMeta.ValidateAll() if the designated constraints
// aren't met.
type ConstraintsMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConstraintsMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConstraintsMetaMultiError) AllErrors() []error { return m }

// ConstraintsMetaValidationError is the validation error returned by
// ConstraintsMeta.Validate if the designated constraints aren't met.
type ConstraintsMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConstraintsMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConstraintsMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConstraintsMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConstraintsMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConstraintsMetaValidationError) ErrorName() string { return "ConstraintsMetaValidationError" }

// Error satisfies the builtin error interface
func (e ConstraintsMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConstraintsMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConstraintsMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConstraintsMetaValidationError{}

// Validate checks the field values on RewardAggregates with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RewardAggregates) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardAggregates with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardAggregatesMultiError, or nil if none found.
func (m *RewardAggregates) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardAggregates) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserAggregate

	// no validation rules for ActionAggregate

	if all {
		switch v := interface{}(m.GetRewardUnitsCapUserAggregate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardAggregatesValidationError{
					field:  "RewardUnitsCapUserAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardAggregatesValidationError{
					field:  "RewardUnitsCapUserAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardUnitsCapUserAggregate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardAggregatesValidationError{
				field:  "RewardUnitsCapUserAggregate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCc_1XRewardUnitsCcAccountLevelMonthlyAggregateCap()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardAggregatesValidationError{
					field:  "Cc_1XRewardUnitsCcAccountLevelMonthlyAggregateCap",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardAggregatesValidationError{
					field:  "Cc_1XRewardUnitsCcAccountLevelMonthlyAggregateCap",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCc_1XRewardUnitsCcAccountLevelMonthlyAggregateCap()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardAggregatesValidationError{
				field:  "Cc_1XRewardUnitsCcAccountLevelMonthlyAggregateCap",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardUnitsCapMonthlyUserAggregate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardAggregatesValidationError{
					field:  "RewardUnitsCapMonthlyUserAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardAggregatesValidationError{
					field:  "RewardUnitsCapMonthlyUserAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardUnitsCapMonthlyUserAggregate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardAggregatesValidationError{
				field:  "RewardUnitsCapMonthlyUserAggregate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserAggregateInTimePeriod()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardAggregatesValidationError{
					field:  "UserAggregateInTimePeriod",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardAggregatesValidationError{
					field:  "UserAggregateInTimePeriod",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserAggregateInTimePeriod()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardAggregatesValidationError{
				field:  "UserAggregateInTimePeriod",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardAggregatesMultiError(errors)
	}

	return nil
}

// RewardAggregatesMultiError is an error wrapping multiple validation errors
// returned by RewardAggregates.ValidateAll() if the designated constraints
// aren't met.
type RewardAggregatesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardAggregatesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardAggregatesMultiError) AllErrors() []error { return m }

// RewardAggregatesValidationError is the validation error returned by
// RewardAggregates.Validate if the designated constraints aren't met.
type RewardAggregatesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardAggregatesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardAggregatesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardAggregatesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardAggregatesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardAggregatesValidationError) ErrorName() string { return "RewardAggregatesValidationError" }

// Error satisfies the builtin error interface
func (e RewardAggregatesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardAggregates.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardAggregatesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardAggregatesValidationError{}

// Validate checks the field values on RewardUnitsCapAggregate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardUnitsCapAggregate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardUnitsCapAggregate with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardUnitsCapAggregateMultiError, or nil if none found.
func (m *RewardUnitsCapAggregate) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardUnitsCapAggregate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUnitsCaps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardUnitsCapAggregateValidationError{
						field:  fmt.Sprintf("UnitsCaps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardUnitsCapAggregateValidationError{
						field:  fmt.Sprintf("UnitsCaps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardUnitsCapAggregateValidationError{
					field:  fmt.Sprintf("UnitsCaps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsHardCheck

	if len(errors) > 0 {
		return RewardUnitsCapAggregateMultiError(errors)
	}

	return nil
}

// RewardUnitsCapAggregateMultiError is an error wrapping multiple validation
// errors returned by RewardUnitsCapAggregate.ValidateAll() if the designated
// constraints aren't met.
type RewardUnitsCapAggregateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardUnitsCapAggregateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardUnitsCapAggregateMultiError) AllErrors() []error { return m }

// RewardUnitsCapAggregateValidationError is the validation error returned by
// RewardUnitsCapAggregate.Validate if the designated constraints aren't met.
type RewardUnitsCapAggregateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardUnitsCapAggregateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardUnitsCapAggregateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardUnitsCapAggregateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardUnitsCapAggregateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardUnitsCapAggregateValidationError) ErrorName() string {
	return "RewardUnitsCapAggregateValidationError"
}

// Error satisfies the builtin error interface
func (e RewardUnitsCapAggregateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardUnitsCapAggregate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardUnitsCapAggregateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardUnitsCapAggregateValidationError{}

// Validate checks the field values on UserAggregateInTimePeriod with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UserAggregateInTimePeriod) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserAggregateInTimePeriod with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserAggregateInTimePeriodMultiError, or nil if none found.
func (m *UserAggregateInTimePeriod) ValidateAll() error {
	return m.validate(true)
}

func (m *UserAggregateInTimePeriod) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MonthlyAggregate

	// no validation rules for QuarterlyAggregate

	if len(errors) > 0 {
		return UserAggregateInTimePeriodMultiError(errors)
	}

	return nil
}

// UserAggregateInTimePeriodMultiError is an error wrapping multiple validation
// errors returned by UserAggregateInTimePeriod.ValidateAll() if the
// designated constraints aren't met.
type UserAggregateInTimePeriodMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserAggregateInTimePeriodMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserAggregateInTimePeriodMultiError) AllErrors() []error { return m }

// UserAggregateInTimePeriodValidationError is the validation error returned by
// UserAggregateInTimePeriod.Validate if the designated constraints aren't met.
type UserAggregateInTimePeriodValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserAggregateInTimePeriodValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserAggregateInTimePeriodValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserAggregateInTimePeriodValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserAggregateInTimePeriodValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserAggregateInTimePeriodValidationError) ErrorName() string {
	return "UserAggregateInTimePeriodValidationError"
}

// Error satisfies the builtin error interface
func (e UserAggregateInTimePeriodValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserAggregateInTimePeriod.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserAggregateInTimePeriodValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserAggregateInTimePeriodValidationError{}

// Validate checks the field values on RewardOfferGroup with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RewardOfferGroup) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardOfferGroup with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardOfferGroupMultiError, or nil if none found.
func (m *RewardOfferGroup) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardOfferGroup) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for UserRewardAggregate

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetRewardUnitsCapUserAggregate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferGroupValidationError{
					field:  "RewardUnitsCapUserAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferGroupValidationError{
					field:  "RewardUnitsCapUserAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardUnitsCapUserAggregate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferGroupValidationError{
				field:  "RewardUnitsCapUserAggregate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardUnitsCapUserMonthlyAggregate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferGroupValidationError{
					field:  "RewardUnitsCapUserMonthlyAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferGroupValidationError{
					field:  "RewardUnitsCapUserMonthlyAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardUnitsCapUserMonthlyAggregate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferGroupValidationError{
				field:  "RewardUnitsCapUserMonthlyAggregate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserRewardAggregateInTimePeriod()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferGroupValidationError{
					field:  "UserRewardAggregateInTimePeriod",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferGroupValidationError{
					field:  "UserRewardAggregateInTimePeriod",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserRewardAggregateInTimePeriod()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferGroupValidationError{
				field:  "UserRewardAggregateInTimePeriod",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardOfferGroupMultiError(errors)
	}

	return nil
}

// RewardOfferGroupMultiError is an error wrapping multiple validation errors
// returned by RewardOfferGroup.ValidateAll() if the designated constraints
// aren't met.
type RewardOfferGroupMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardOfferGroupMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardOfferGroupMultiError) AllErrors() []error { return m }

// RewardOfferGroupValidationError is the validation error returned by
// RewardOfferGroup.Validate if the designated constraints aren't met.
type RewardOfferGroupValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardOfferGroupValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardOfferGroupValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardOfferGroupValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardOfferGroupValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardOfferGroupValidationError) ErrorName() string { return "RewardOfferGroupValidationError" }

// Error satisfies the builtin error interface
func (e RewardOfferGroupValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardOfferGroup.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardOfferGroupValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardOfferGroupValidationError{}

// Validate checks the field values on RewardOfferRewardUnitsActorUtilisation
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RewardOfferRewardUnitsActorUtilisation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RewardOfferRewardUnitsActorUtilisation with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// RewardOfferRewardUnitsActorUtilisationMultiError, or nil if none found.
func (m *RewardOfferRewardUnitsActorUtilisation) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardOfferRewardUnitsActorUtilisation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for OfferId

	// no validation rules for ActorId

	// no validation rules for FiCoinsUnits

	// no validation rules for CashUnits

	// no validation rules for SdCashUnits

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferRewardUnitsActorUtilisationValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferRewardUnitsActorUtilisationValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferRewardUnitsActorUtilisationValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferRewardUnitsActorUtilisationValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferRewardUnitsActorUtilisationValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferRewardUnitsActorUtilisationValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UsstockCashUnits

	// no validation rules for CcBillEraserUnits

	if len(errors) > 0 {
		return RewardOfferRewardUnitsActorUtilisationMultiError(errors)
	}

	return nil
}

// RewardOfferRewardUnitsActorUtilisationMultiError is an error wrapping
// multiple validation errors returned by
// RewardOfferRewardUnitsActorUtilisation.ValidateAll() if the designated
// constraints aren't met.
type RewardOfferRewardUnitsActorUtilisationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardOfferRewardUnitsActorUtilisationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardOfferRewardUnitsActorUtilisationMultiError) AllErrors() []error { return m }

// RewardOfferRewardUnitsActorUtilisationValidationError is the validation
// error returned by RewardOfferRewardUnitsActorUtilisation.Validate if the
// designated constraints aren't met.
type RewardOfferRewardUnitsActorUtilisationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardOfferRewardUnitsActorUtilisationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardOfferRewardUnitsActorUtilisationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardOfferRewardUnitsActorUtilisationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardOfferRewardUnitsActorUtilisationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardOfferRewardUnitsActorUtilisationValidationError) ErrorName() string {
	return "RewardOfferRewardUnitsActorUtilisationValidationError"
}

// Error satisfies the builtin error interface
func (e RewardOfferRewardUnitsActorUtilisationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardOfferRewardUnitsActorUtilisation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardOfferRewardUnitsActorUtilisationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardOfferRewardUnitsActorUtilisationValidationError{}

// Validate checks the field values on
// RewardOfferGroupRewardUnitsActorUtilisation with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RewardOfferGroupRewardUnitsActorUtilisation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RewardOfferGroupRewardUnitsActorUtilisation with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// RewardOfferGroupRewardUnitsActorUtilisationMultiError, or nil if none found.
func (m *RewardOfferGroupRewardUnitsActorUtilisation) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardOfferGroupRewardUnitsActorUtilisation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for OfferGroupId

	// no validation rules for ActorId

	// no validation rules for FiCoinsUnits

	// no validation rules for CashUnits

	// no validation rules for SdCashUnits

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferGroupRewardUnitsActorUtilisationValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferGroupRewardUnitsActorUtilisationValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferGroupRewardUnitsActorUtilisationValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferGroupRewardUnitsActorUtilisationValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferGroupRewardUnitsActorUtilisationValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferGroupRewardUnitsActorUtilisationValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UsstockCashUnits

	// no validation rules for CcBillEraserUnits

	if len(errors) > 0 {
		return RewardOfferGroupRewardUnitsActorUtilisationMultiError(errors)
	}

	return nil
}

// RewardOfferGroupRewardUnitsActorUtilisationMultiError is an error wrapping
// multiple validation errors returned by
// RewardOfferGroupRewardUnitsActorUtilisation.ValidateAll() if the designated
// constraints aren't met.
type RewardOfferGroupRewardUnitsActorUtilisationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardOfferGroupRewardUnitsActorUtilisationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardOfferGroupRewardUnitsActorUtilisationMultiError) AllErrors() []error { return m }

// RewardOfferGroupRewardUnitsActorUtilisationValidationError is the validation
// error returned by RewardOfferGroupRewardUnitsActorUtilisation.Validate if
// the designated constraints aren't met.
type RewardOfferGroupRewardUnitsActorUtilisationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardOfferGroupRewardUnitsActorUtilisationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardOfferGroupRewardUnitsActorUtilisationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardOfferGroupRewardUnitsActorUtilisationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardOfferGroupRewardUnitsActorUtilisationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardOfferGroupRewardUnitsActorUtilisationValidationError) ErrorName() string {
	return "RewardOfferGroupRewardUnitsActorUtilisationValidationError"
}

// Error satisfies the builtin error interface
func (e RewardOfferGroupRewardUnitsActorUtilisationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardOfferGroupRewardUnitsActorUtilisation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardOfferGroupRewardUnitsActorUtilisationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardOfferGroupRewardUnitsActorUtilisationValidationError{}

// Validate checks the field values on RewardOfferGroupInventory with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardOfferGroupInventory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardOfferGroupInventory with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardOfferGroupInventoryMultiError, or nil if none found.
func (m *RewardOfferGroupInventory) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardOfferGroupInventory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferGroupId

	// no validation rules for RemainingCount

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return RewardOfferGroupInventoryMultiError(errors)
	}

	return nil
}

// RewardOfferGroupInventoryMultiError is an error wrapping multiple validation
// errors returned by RewardOfferGroupInventory.ValidateAll() if the
// designated constraints aren't met.
type RewardOfferGroupInventoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardOfferGroupInventoryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardOfferGroupInventoryMultiError) AllErrors() []error { return m }

// RewardOfferGroupInventoryValidationError is the validation error returned by
// RewardOfferGroupInventory.Validate if the designated constraints aren't met.
type RewardOfferGroupInventoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardOfferGroupInventoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardOfferGroupInventoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardOfferGroupInventoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardOfferGroupInventoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardOfferGroupInventoryValidationError) ErrorName() string {
	return "RewardOfferGroupInventoryValidationError"
}

// Error satisfies the builtin error interface
func (e RewardOfferGroupInventoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardOfferGroupInventory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardOfferGroupInventoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardOfferGroupInventoryValidationError{}

// Validate checks the field values on RewardOfferInventory with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardOfferInventory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardOfferInventory with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardOfferInventoryMultiError, or nil if none found.
func (m *RewardOfferInventory) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardOfferInventory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RewardOfferId

	// no validation rules for RemainingCount

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return RewardOfferInventoryMultiError(errors)
	}

	return nil
}

// RewardOfferInventoryMultiError is an error wrapping multiple validation
// errors returned by RewardOfferInventory.ValidateAll() if the designated
// constraints aren't met.
type RewardOfferInventoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardOfferInventoryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardOfferInventoryMultiError) AllErrors() []error { return m }

// RewardOfferInventoryValidationError is the validation error returned by
// RewardOfferInventory.Validate if the designated constraints aren't met.
type RewardOfferInventoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardOfferInventoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardOfferInventoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardOfferInventoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardOfferInventoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardOfferInventoryValidationError) ErrorName() string {
	return "RewardOfferInventoryValidationError"
}

// Error satisfies the builtin error interface
func (e RewardOfferInventoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardOfferInventory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardOfferInventoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardOfferInventoryValidationError{}

// Validate checks the field values on DisplayMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DisplayMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DisplayMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DisplayMetaMultiError, or
// nil if none found.
func (m *DisplayMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *DisplayMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DisplaySince

	// no validation rules for DisplayTill

	// no validation rules for DisplayType

	if utf8.RuneCountInString(m.GetTitle()) < 1 {
		err := DisplayMetaValidationError{
			field:  "Title",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetIcon()) < 1 {
		err := DisplayMetaValidationError{
			field:  "Icon",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetBgColor()) < 1 {
		err := DisplayMetaValidationError{
			field:  "BgColor",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActionDesc()) < 1 {
		err := DisplayMetaValidationError{
			field:  "ActionDesc",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DisplayMetaValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DisplayMetaValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DisplayMetaValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DisplayRank

	// no validation rules for ShortDesc

	// no validation rules for TileBgColor

	for idx, item := range m.GetTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DisplayMetaValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DisplayMetaValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DisplayMetaValidationError{
					field:  fmt.Sprintf("Tags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ImageUrl

	// no validation rules for SecondaryTileBgColor

	// no validation rules for ShortTnc

	// no validation rules for IsVisibleOnSalaryIntroPage

	// no validation rules for DisplayRankOnSalaryIntroPage

	// no validation rules for PlatformToMinSupportedAppVersionMap

	// no validation rules for PlatformToMaxSupportedAppVersionMap

	for idx, item := range m.GetStepsV1() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DisplayMetaValidationError{
						field:  fmt.Sprintf("StepsV1[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DisplayMetaValidationError{
						field:  fmt.Sprintf("StepsV1[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DisplayMetaValidationError{
					field:  fmt.Sprintf("StepsV1[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetTncsV1() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DisplayMetaValidationError{
						field:  fmt.Sprintf("TncsV1[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DisplayMetaValidationError{
						field:  fmt.Sprintf("TncsV1[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DisplayMetaValidationError{
					field:  fmt.Sprintf("TncsV1[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CashEquivalent

	// no validation rules for CashEquivalentCalculationExpression

	// no validation rules for VerboseDesc

	// no validation rules for DisplayConstraintExpression

	if all {
		switch v := interface{}(m.GetWaysToEarnRewardsV2ScreenConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DisplayMetaValidationError{
					field:  "WaysToEarnRewardsV2ScreenConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DisplayMetaValidationError{
					field:  "WaysToEarnRewardsV2ScreenConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWaysToEarnRewardsV2ScreenConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DisplayMetaValidationError{
				field:  "WaysToEarnRewardsV2ScreenConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DisplayMetaMultiError(errors)
	}

	return nil
}

// DisplayMetaMultiError is an error wrapping multiple validation errors
// returned by DisplayMeta.ValidateAll() if the designated constraints aren't met.
type DisplayMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DisplayMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DisplayMetaMultiError) AllErrors() []error { return m }

// DisplayMetaValidationError is the validation error returned by
// DisplayMeta.Validate if the designated constraints aren't met.
type DisplayMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DisplayMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DisplayMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DisplayMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DisplayMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DisplayMetaValidationError) ErrorName() string { return "DisplayMetaValidationError" }

// Error satisfies the builtin error interface
func (e DisplayMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDisplayMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DisplayMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DisplayMetaValidationError{}

// Validate checks the field values on RewardNotificationMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardNotificationMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardNotificationMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardNotificationMetadataMultiError, or nil if none found.
func (m *RewardNotificationMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardNotificationMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NudgeId

	// no validation rules for EmailTemplate

	if len(errors) > 0 {
		return RewardNotificationMetadataMultiError(errors)
	}

	return nil
}

// RewardNotificationMetadataMultiError is an error wrapping multiple
// validation errors returned by RewardNotificationMetadata.ValidateAll() if
// the designated constraints aren't met.
type RewardNotificationMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardNotificationMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardNotificationMetadataMultiError) AllErrors() []error { return m }

// RewardNotificationMetadataValidationError is the validation error returned
// by RewardNotificationMetadata.Validate if the designated constraints aren't met.
type RewardNotificationMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardNotificationMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardNotificationMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardNotificationMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardNotificationMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardNotificationMetadataValidationError) ErrorName() string {
	return "RewardNotificationMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e RewardNotificationMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardNotificationMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardNotificationMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardNotificationMetadataValidationError{}

// Validate checks the field values on RewardNotificationConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardNotificationConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardNotificationConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardNotificationConfigMultiError, or nil if none found.
func (m *RewardNotificationConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardNotificationConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEarnedRewardNotificationConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardNotificationConfigValidationError{
					field:  "EarnedRewardNotificationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardNotificationConfigValidationError{
					field:  "EarnedRewardNotificationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEarnedRewardNotificationConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardNotificationConfigValidationError{
				field:  "EarnedRewardNotificationConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardProcessingSuccessfulNotificationConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardNotificationConfigValidationError{
					field:  "RewardProcessingSuccessfulNotificationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardNotificationConfigValidationError{
					field:  "RewardProcessingSuccessfulNotificationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardProcessingSuccessfulNotificationConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardNotificationConfigValidationError{
				field:  "RewardProcessingSuccessfulNotificationConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardNotificationConfigMultiError(errors)
	}

	return nil
}

// RewardNotificationConfigMultiError is an error wrapping multiple validation
// errors returned by RewardNotificationConfig.ValidateAll() if the designated
// constraints aren't met.
type RewardNotificationConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardNotificationConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardNotificationConfigMultiError) AllErrors() []error { return m }

// RewardNotificationConfigValidationError is the validation error returned by
// RewardNotificationConfig.Validate if the designated constraints aren't met.
type RewardNotificationConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardNotificationConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardNotificationConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardNotificationConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardNotificationConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardNotificationConfigValidationError) ErrorName() string {
	return "RewardNotificationConfigValidationError"
}

// Error satisfies the builtin error interface
func (e RewardNotificationConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardNotificationConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardNotificationConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardNotificationConfigValidationError{}

// Validate checks the field values on EarnedRewardNotificationConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EarnedRewardNotificationConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EarnedRewardNotificationConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// EarnedRewardNotificationConfigMultiError, or nil if none found.
func (m *EarnedRewardNotificationConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *EarnedRewardNotificationConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NotificationType

	for idx, item := range m.GetNotifications() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EarnedRewardNotificationConfigValidationError{
						field:  fmt.Sprintf("Notifications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EarnedRewardNotificationConfigValidationError{
						field:  fmt.Sprintf("Notifications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EarnedRewardNotificationConfigValidationError{
					field:  fmt.Sprintf("Notifications[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UseNotificationV1

	if len(errors) > 0 {
		return EarnedRewardNotificationConfigMultiError(errors)
	}

	return nil
}

// EarnedRewardNotificationConfigMultiError is an error wrapping multiple
// validation errors returned by EarnedRewardNotificationConfig.ValidateAll()
// if the designated constraints aren't met.
type EarnedRewardNotificationConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EarnedRewardNotificationConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EarnedRewardNotificationConfigMultiError) AllErrors() []error { return m }

// EarnedRewardNotificationConfigValidationError is the validation error
// returned by EarnedRewardNotificationConfig.Validate if the designated
// constraints aren't met.
type EarnedRewardNotificationConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EarnedRewardNotificationConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EarnedRewardNotificationConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EarnedRewardNotificationConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EarnedRewardNotificationConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EarnedRewardNotificationConfigValidationError) ErrorName() string {
	return "EarnedRewardNotificationConfigValidationError"
}

// Error satisfies the builtin error interface
func (e EarnedRewardNotificationConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEarnedRewardNotificationConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EarnedRewardNotificationConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EarnedRewardNotificationConfigValidationError{}

// Validate checks the field values on
// RewardProcessingSuccessfulNotificationConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RewardProcessingSuccessfulNotificationConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RewardProcessingSuccessfulNotificationConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// RewardProcessingSuccessfulNotificationConfigMultiError, or nil if none found.
func (m *RewardProcessingSuccessfulNotificationConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardProcessingSuccessfulNotificationConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNotifications() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardProcessingSuccessfulNotificationConfigValidationError{
						field:  fmt.Sprintf("Notifications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardProcessingSuccessfulNotificationConfigValidationError{
						field:  fmt.Sprintf("Notifications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardProcessingSuccessfulNotificationConfigValidationError{
					field:  fmt.Sprintf("Notifications[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RewardProcessingSuccessfulNotificationConfigMultiError(errors)
	}

	return nil
}

// RewardProcessingSuccessfulNotificationConfigMultiError is an error wrapping
// multiple validation errors returned by
// RewardProcessingSuccessfulNotificationConfig.ValidateAll() if the
// designated constraints aren't met.
type RewardProcessingSuccessfulNotificationConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardProcessingSuccessfulNotificationConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardProcessingSuccessfulNotificationConfigMultiError) AllErrors() []error { return m }

// RewardProcessingSuccessfulNotificationConfigValidationError is the
// validation error returned by
// RewardProcessingSuccessfulNotificationConfig.Validate if the designated
// constraints aren't met.
type RewardProcessingSuccessfulNotificationConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardProcessingSuccessfulNotificationConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardProcessingSuccessfulNotificationConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardProcessingSuccessfulNotificationConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardProcessingSuccessfulNotificationConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardProcessingSuccessfulNotificationConfigValidationError) ErrorName() string {
	return "RewardProcessingSuccessfulNotificationConfigValidationError"
}

// Error satisfies the builtin error interface
func (e RewardProcessingSuccessfulNotificationConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardProcessingSuccessfulNotificationConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardProcessingSuccessfulNotificationConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardProcessingSuccessfulNotificationConfigValidationError{}

// Validate checks the field values on Content with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Content) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Content with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ContentMultiError, or nil if none found.
func (m *Content) ValidateAll() error {
	return m.validate(true)
}

func (m *Content) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for Body

	// no validation rules for ImageUrl

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContentValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContentValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContentValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ContentMultiError(errors)
	}

	return nil
}

// ContentMultiError is an error wrapping multiple validation errors returned
// by Content.ValidateAll() if the designated constraints aren't met.
type ContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContentMultiError) AllErrors() []error { return m }

// ContentValidationError is the validation error returned by Content.Validate
// if the designated constraints aren't met.
type ContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContentValidationError) ErrorName() string { return "ContentValidationError" }

// Error satisfies the builtin error interface
func (e ContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContentValidationError{}

// Validate checks the field values on CreateRewardOfferRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRewardOfferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRewardOfferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRewardOfferRequestMultiError, or nil if none found.
func (m *CreateRewardOfferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRewardOfferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActiveSince

	// no validation rules for ActiveTill

	// no validation rules for CreatedBy

	// no validation rules for ActionType

	if all {
		switch v := interface{}(m.GetConstraintMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRewardOfferRequestValidationError{
					field:  "ConstraintMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRewardOfferRequestValidationError{
					field:  "ConstraintMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConstraintMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRewardOfferRequestValidationError{
				field:  "ConstraintMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRewardOfferRequestValidationError{
					field:  "RewardMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRewardOfferRequestValidationError{
					field:  "RewardMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRewardOfferRequestValidationError{
				field:  "RewardMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisplayMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRewardOfferRequestValidationError{
					field:  "DisplayMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRewardOfferRequestValidationError{
					field:  "DisplayMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRewardOfferRequestValidationError{
				field:  "DisplayMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsVisible

	// no validation rules for OfferType

	// no validation rules for GroupId

	// no validation rules for RefRewardOfferId

	// no validation rules for DisplaySegmentId

	// no validation rules for IsDisplaySegmentExcluded

	// no validation rules for SupportedPlatform

	// no validation rules for DisplaySegmentExpression

	if all {
		switch v := interface{}(m.GetAnalyticsData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRewardOfferRequestValidationError{
					field:  "AnalyticsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRewardOfferRequestValidationError{
					field:  "AnalyticsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnalyticsData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRewardOfferRequestValidationError{
				field:  "AnalyticsData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UnlockEvent

	if all {
		switch v := interface{}(m.GetUnlockMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRewardOfferRequestValidationError{
					field:  "UnlockMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRewardOfferRequestValidationError{
					field:  "UnlockMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnlockMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRewardOfferRequestValidationError{
				field:  "UnlockMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GenerationType

	if all {
		switch v := interface{}(m.GetAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRewardOfferRequestValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRewardOfferRequestValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRewardOfferRequestValidationError{
				field:  "AdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRewardOfferRequestMultiError(errors)
	}

	return nil
}

// CreateRewardOfferRequestMultiError is an error wrapping multiple validation
// errors returned by CreateRewardOfferRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateRewardOfferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRewardOfferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRewardOfferRequestMultiError) AllErrors() []error { return m }

// CreateRewardOfferRequestValidationError is the validation error returned by
// CreateRewardOfferRequest.Validate if the designated constraints aren't met.
type CreateRewardOfferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRewardOfferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRewardOfferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRewardOfferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRewardOfferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRewardOfferRequestValidationError) ErrorName() string {
	return "CreateRewardOfferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRewardOfferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRewardOfferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRewardOfferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRewardOfferRequestValidationError{}

// Validate checks the field values on CreateRewardOfferGroupRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRewardOfferGroupRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRewardOfferGroupRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateRewardOfferGroupRequestMultiError, or nil if none found.
func (m *CreateRewardOfferGroupRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRewardOfferGroupRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserRewardAggregate

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetRewardUnitsCapUserAggregate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRewardOfferGroupRequestValidationError{
					field:  "RewardUnitsCapUserAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRewardOfferGroupRequestValidationError{
					field:  "RewardUnitsCapUserAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardUnitsCapUserAggregate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRewardOfferGroupRequestValidationError{
				field:  "RewardUnitsCapUserAggregate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardUnitsCapUserMonthlyAggregate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRewardOfferGroupRequestValidationError{
					field:  "RewardUnitsCapUserMonthlyAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRewardOfferGroupRequestValidationError{
					field:  "RewardUnitsCapUserMonthlyAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardUnitsCapUserMonthlyAggregate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRewardOfferGroupRequestValidationError{
				field:  "RewardUnitsCapUserMonthlyAggregate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserRewardAggregateInTimePeriod()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRewardOfferGroupRequestValidationError{
					field:  "UserRewardAggregateInTimePeriod",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRewardOfferGroupRequestValidationError{
					field:  "UserRewardAggregateInTimePeriod",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserRewardAggregateInTimePeriod()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRewardOfferGroupRequestValidationError{
				field:  "UserRewardAggregateInTimePeriod",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRewardOfferGroupRequestMultiError(errors)
	}

	return nil
}

// CreateRewardOfferGroupRequestMultiError is an error wrapping multiple
// validation errors returned by CreateRewardOfferGroupRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateRewardOfferGroupRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRewardOfferGroupRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRewardOfferGroupRequestMultiError) AllErrors() []error { return m }

// CreateRewardOfferGroupRequestValidationError is the validation error
// returned by CreateRewardOfferGroupRequest.Validate if the designated
// constraints aren't met.
type CreateRewardOfferGroupRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRewardOfferGroupRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRewardOfferGroupRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRewardOfferGroupRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRewardOfferGroupRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRewardOfferGroupRequestValidationError) ErrorName() string {
	return "CreateRewardOfferGroupRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRewardOfferGroupRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRewardOfferGroupRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRewardOfferGroupRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRewardOfferGroupRequestValidationError{}

// Validate checks the field values on CreateRewardOfferGroupResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRewardOfferGroupResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRewardOfferGroupResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateRewardOfferGroupResponseMultiError, or nil if none found.
func (m *CreateRewardOfferGroupResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRewardOfferGroupResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRewardOfferGroupResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRewardOfferGroupResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRewardOfferGroupResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardOfferGroup()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRewardOfferGroupResponseValidationError{
					field:  "RewardOfferGroup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRewardOfferGroupResponseValidationError{
					field:  "RewardOfferGroup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardOfferGroup()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRewardOfferGroupResponseValidationError{
				field:  "RewardOfferGroup",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRewardOfferGroupResponseMultiError(errors)
	}

	return nil
}

// CreateRewardOfferGroupResponseMultiError is an error wrapping multiple
// validation errors returned by CreateRewardOfferGroupResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateRewardOfferGroupResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRewardOfferGroupResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRewardOfferGroupResponseMultiError) AllErrors() []error { return m }

// CreateRewardOfferGroupResponseValidationError is the validation error
// returned by CreateRewardOfferGroupResponse.Validate if the designated
// constraints aren't met.
type CreateRewardOfferGroupResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRewardOfferGroupResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRewardOfferGroupResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRewardOfferGroupResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRewardOfferGroupResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRewardOfferGroupResponseValidationError) ErrorName() string {
	return "CreateRewardOfferGroupResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRewardOfferGroupResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRewardOfferGroupResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRewardOfferGroupResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRewardOfferGroupResponseValidationError{}

// Validate checks the field values on RewardOffer with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RewardOffer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardOffer with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RewardOfferMultiError, or
// nil if none found.
func (m *RewardOffer) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardOffer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Status

	// no validation rules for ActiveSince

	// no validation rules for ActiveTill

	// no validation rules for CreatedAt

	// no validation rules for CreatedBy

	// no validation rules for ActionType

	if all {
		switch v := interface{}(m.GetConstraintMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "ConstraintMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "ConstraintMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConstraintMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferValidationError{
				field:  "ConstraintMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "RewardMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "RewardMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferValidationError{
				field:  "RewardMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisplayMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "DisplayMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "DisplayMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferValidationError{
				field:  "DisplayMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsVisible

	// no validation rules for OfferType

	// no validation rules for GroupId

	// no validation rules for RefRewardOfferId

	// no validation rules for SupportedPlatform

	// no validation rules for DisplaySegmentId

	// no validation rules for IsDisplaySegmentExcluded

	// no validation rules for DisplaySegmentExpression

	if all {
		switch v := interface{}(m.GetAnalyticsData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "AnalyticsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "AnalyticsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnalyticsData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferValidationError{
				field:  "AnalyticsData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UnlockEvent

	if all {
		switch v := interface{}(m.GetUnlockMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "UnlockMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "UnlockMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnlockMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferValidationError{
				field:  "UnlockMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GenerationType

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferValidationError{
				field:  "AdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardOfferMultiError(errors)
	}

	return nil
}

// RewardOfferMultiError is an error wrapping multiple validation errors
// returned by RewardOffer.ValidateAll() if the designated constraints aren't met.
type RewardOfferMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardOfferMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardOfferMultiError) AllErrors() []error { return m }

// RewardOfferValidationError is the validation error returned by
// RewardOffer.Validate if the designated constraints aren't met.
type RewardOfferValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardOfferValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardOfferValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardOfferValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardOfferValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardOfferValidationError) ErrorName() string { return "RewardOfferValidationError" }

// Error satisfies the builtin error interface
func (e RewardOfferValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardOffer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardOfferValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardOfferValidationError{}

// Validate checks the field values on RewardOfferResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardOfferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardOfferResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardOfferResponseMultiError, or nil if none found.
func (m *RewardOfferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardOfferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardOffer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferResponseValidationError{
					field:  "RewardOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferResponseValidationError{
					field:  "RewardOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardOffer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferResponseValidationError{
				field:  "RewardOffer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetValidationFailureInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardOfferResponseValidationError{
						field:  fmt.Sprintf("ValidationFailureInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardOfferResponseValidationError{
						field:  fmt.Sprintf("ValidationFailureInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardOfferResponseValidationError{
					field:  fmt.Sprintf("ValidationFailureInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RewardOfferResponseMultiError(errors)
	}

	return nil
}

// RewardOfferResponseMultiError is an error wrapping multiple validation
// errors returned by RewardOfferResponse.ValidateAll() if the designated
// constraints aren't met.
type RewardOfferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardOfferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardOfferResponseMultiError) AllErrors() []error { return m }

// RewardOfferResponseValidationError is the validation error returned by
// RewardOfferResponse.Validate if the designated constraints aren't met.
type RewardOfferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardOfferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardOfferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardOfferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardOfferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardOfferResponseValidationError) ErrorName() string {
	return "RewardOfferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RewardOfferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardOfferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardOfferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardOfferResponseValidationError{}

// Validate checks the field values on GetRewardOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardOffersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardOffersRequestMultiError, or nil if none found.
func (m *GetRewardOffersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOffersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActiveSince

	// no validation rules for ActiveTill

	// no validation rules for DisplaySince

	// no validation rules for DisplayTill

	// no validation rules for Status

	// no validation rules for IsVisible

	// no validation rules for SupportedPlatform

	// no validation rules for Id

	// no validation rules for DisplaySegmentId

	// no validation rules for IsDisplaySegmentExcluded

	// no validation rules for DisplaySegmentExpression

	// no validation rules for GenerationType

	if len(errors) > 0 {
		return GetRewardOffersRequestMultiError(errors)
	}

	return nil
}

// GetRewardOffersRequestMultiError is an error wrapping multiple validation
// errors returned by GetRewardOffersRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRewardOffersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOffersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOffersRequestMultiError) AllErrors() []error { return m }

// GetRewardOffersRequestValidationError is the validation error returned by
// GetRewardOffersRequest.Validate if the designated constraints aren't met.
type GetRewardOffersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOffersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOffersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOffersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOffersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOffersRequestValidationError) ErrorName() string {
	return "GetRewardOffersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOffersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOffersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOffersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOffersRequestValidationError{}

// Validate checks the field values on GetRewardOffersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardOffersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOffersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardOffersResponseMultiError, or nil if none found.
func (m *GetRewardOffersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOffersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOffersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRewardOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardOffersResponseValidationError{
						field:  fmt.Sprintf("RewardOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardOffersResponseValidationError{
						field:  fmt.Sprintf("RewardOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardOffersResponseValidationError{
					field:  fmt.Sprintf("RewardOffers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRewardOffersResponseMultiError(errors)
	}

	return nil
}

// GetRewardOffersResponseMultiError is an error wrapping multiple validation
// errors returned by GetRewardOffersResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRewardOffersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOffersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOffersResponseMultiError) AllErrors() []error { return m }

// GetRewardOffersResponseValidationError is the validation error returned by
// GetRewardOffersResponse.Validate if the designated constraints aren't met.
type GetRewardOffersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOffersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOffersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOffersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOffersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOffersResponseValidationError) ErrorName() string {
	return "GetRewardOffersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOffersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOffersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOffersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOffersResponseValidationError{}

// Validate checks the field values on GetRewardOffersByIdsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardOffersByIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOffersByIdsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardOffersByIdsRequestMultiError, or nil if none found.
func (m *GetRewardOffersByIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOffersByIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetRewardOffersByIdsRequestMultiError(errors)
	}

	return nil
}

// GetRewardOffersByIdsRequestMultiError is an error wrapping multiple
// validation errors returned by GetRewardOffersByIdsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetRewardOffersByIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOffersByIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOffersByIdsRequestMultiError) AllErrors() []error { return m }

// GetRewardOffersByIdsRequestValidationError is the validation error returned
// by GetRewardOffersByIdsRequest.Validate if the designated constraints
// aren't met.
type GetRewardOffersByIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOffersByIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOffersByIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOffersByIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOffersByIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOffersByIdsRequestValidationError) ErrorName() string {
	return "GetRewardOffersByIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOffersByIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOffersByIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOffersByIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOffersByIdsRequestValidationError{}

// Validate checks the field values on GetRewardOffersByIdsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardOffersByIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOffersByIdsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardOffersByIdsResponseMultiError, or nil if none found.
func (m *GetRewardOffersByIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOffersByIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOffersByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOffersByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOffersByIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRewardOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardOffersByIdsResponseValidationError{
						field:  fmt.Sprintf("RewardOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardOffersByIdsResponseValidationError{
						field:  fmt.Sprintf("RewardOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardOffersByIdsResponseValidationError{
					field:  fmt.Sprintf("RewardOffers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRewardOffersByIdsResponseMultiError(errors)
	}

	return nil
}

// GetRewardOffersByIdsResponseMultiError is an error wrapping multiple
// validation errors returned by GetRewardOffersByIdsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetRewardOffersByIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOffersByIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOffersByIdsResponseMultiError) AllErrors() []error { return m }

// GetRewardOffersByIdsResponseValidationError is the validation error returned
// by GetRewardOffersByIdsResponse.Validate if the designated constraints
// aren't met.
type GetRewardOffersByIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOffersByIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOffersByIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOffersByIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOffersByIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOffersByIdsResponseValidationError) ErrorName() string {
	return "GetRewardOffersByIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOffersByIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOffersByIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOffersByIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOffersByIdsResponseValidationError{}

// Validate checks the field values on GetRewardOffersForActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardOffersForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOffersForActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRewardOffersForActorRequestMultiError, or nil if none found.
func (m *GetRewardOffersForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOffersForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActiveSince

	// no validation rules for ActiveTill

	// no validation rules for DisplaySince

	// no validation rules for DisplayTill

	// no validation rules for Status

	// no validation rules for IsVisible

	// no validation rules for ActorId

	// no validation rules for SupportedPlatform

	// no validation rules for Id

	// no validation rules for GenerationType

	if len(errors) > 0 {
		return GetRewardOffersForActorRequestMultiError(errors)
	}

	return nil
}

// GetRewardOffersForActorRequestMultiError is an error wrapping multiple
// validation errors returned by GetRewardOffersForActorRequest.ValidateAll()
// if the designated constraints aren't met.
type GetRewardOffersForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOffersForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOffersForActorRequestMultiError) AllErrors() []error { return m }

// GetRewardOffersForActorRequestValidationError is the validation error
// returned by GetRewardOffersForActorRequest.Validate if the designated
// constraints aren't met.
type GetRewardOffersForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOffersForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOffersForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOffersForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOffersForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOffersForActorRequestValidationError) ErrorName() string {
	return "GetRewardOffersForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOffersForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOffersForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOffersForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOffersForActorRequestValidationError{}

// Validate checks the field values on GetRewardOffersForActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardOffersForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOffersForActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRewardOffersForActorResponseMultiError, or nil if none found.
func (m *GetRewardOffersForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOffersForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOffersForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOffersForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOffersForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRewardOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardOffersForActorResponseValidationError{
						field:  fmt.Sprintf("RewardOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardOffersForActorResponseValidationError{
						field:  fmt.Sprintf("RewardOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardOffersForActorResponseValidationError{
					field:  fmt.Sprintf("RewardOffers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetActorLevelRewardOfferInventory() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardOffersForActorResponseValidationError{
						field:  fmt.Sprintf("ActorLevelRewardOfferInventory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardOffersForActorResponseValidationError{
						field:  fmt.Sprintf("ActorLevelRewardOfferInventory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardOffersForActorResponseValidationError{
					field:  fmt.Sprintf("ActorLevelRewardOfferInventory[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetActionLevelRewardOfferInventory() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardOffersForActorResponseValidationError{
						field:  fmt.Sprintf("ActionLevelRewardOfferInventory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardOffersForActorResponseValidationError{
						field:  fmt.Sprintf("ActionLevelRewardOfferInventory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardOffersForActorResponseValidationError{
					field:  fmt.Sprintf("ActionLevelRewardOfferInventory[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetActorLevelOfferGroupInventory() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardOffersForActorResponseValidationError{
						field:  fmt.Sprintf("ActorLevelOfferGroupInventory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardOffersForActorResponseValidationError{
						field:  fmt.Sprintf("ActorLevelOfferGroupInventory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardOffersForActorResponseValidationError{
					field:  fmt.Sprintf("ActorLevelOfferGroupInventory[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetActorLevelRewardUnitsUtil() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardOffersForActorResponseValidationError{
						field:  fmt.Sprintf("ActorLevelRewardUnitsUtil[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardOffersForActorResponseValidationError{
						field:  fmt.Sprintf("ActorLevelRewardUnitsUtil[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardOffersForActorResponseValidationError{
					field:  fmt.Sprintf("ActorLevelRewardUnitsUtil[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetActorLevelGroupRewardsUnitsUtil() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardOffersForActorResponseValidationError{
						field:  fmt.Sprintf("ActorLevelGroupRewardsUnitsUtil[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardOffersForActorResponseValidationError{
						field:  fmt.Sprintf("ActorLevelGroupRewardsUnitsUtil[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardOffersForActorResponseValidationError{
					field:  fmt.Sprintf("ActorLevelGroupRewardsUnitsUtil[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetOfferGroupIdToOfferGroupMap()))
		i := 0
		for key := range m.GetOfferGroupIdToOfferGroupMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetOfferGroupIdToOfferGroupMap()[key]
			_ = val

			// no validation rules for OfferGroupIdToOfferGroupMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetRewardOffersForActorResponseValidationError{
							field:  fmt.Sprintf("OfferGroupIdToOfferGroupMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetRewardOffersForActorResponseValidationError{
							field:  fmt.Sprintf("OfferGroupIdToOfferGroupMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetRewardOffersForActorResponseValidationError{
						field:  fmt.Sprintf("OfferGroupIdToOfferGroupMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetRewardOffersForActorResponseMultiError(errors)
	}

	return nil
}

// GetRewardOffersForActorResponseMultiError is an error wrapping multiple
// validation errors returned by GetRewardOffersForActorResponse.ValidateAll()
// if the designated constraints aren't met.
type GetRewardOffersForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOffersForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOffersForActorResponseMultiError) AllErrors() []error { return m }

// GetRewardOffersForActorResponseValidationError is the validation error
// returned by GetRewardOffersForActorResponse.Validate if the designated
// constraints aren't met.
type GetRewardOffersForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOffersForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOffersForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOffersForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOffersForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOffersForActorResponseValidationError) ErrorName() string {
	return "GetRewardOffersForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOffersForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOffersForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOffersForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOffersForActorResponseValidationError{}

// Validate checks the field values on GetRewardOffersForScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardOffersForScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOffersForScreenRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRewardOffersForScreenRequestMultiError, or nil if none found.
func (m *GetRewardOffersForScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOffersForScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOffersForScreenRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOffersForScreenRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOffersForScreenRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRewardOffersForScreenRequestMultiError(errors)
	}

	return nil
}

// GetRewardOffersForScreenRequestMultiError is an error wrapping multiple
// validation errors returned by GetRewardOffersForScreenRequest.ValidateAll()
// if the designated constraints aren't met.
type GetRewardOffersForScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOffersForScreenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOffersForScreenRequestMultiError) AllErrors() []error { return m }

// GetRewardOffersForScreenRequestValidationError is the validation error
// returned by GetRewardOffersForScreenRequest.Validate if the designated
// constraints aren't met.
type GetRewardOffersForScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOffersForScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOffersForScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOffersForScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOffersForScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOffersForScreenRequestValidationError) ErrorName() string {
	return "GetRewardOffersForScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOffersForScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOffersForScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOffersForScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOffersForScreenRequestValidationError{}

// Validate checks the field values on GetRewardOffersForScreenResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetRewardOffersForScreenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardOffersForScreenResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRewardOffersForScreenResponseMultiError, or nil if none found.
func (m *GetRewardOffersForScreenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOffersForScreenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardOffersForScreenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardOffersForScreenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardOffersForScreenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRewardOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardOffersForScreenResponseValidationError{
						field:  fmt.Sprintf("RewardOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardOffersForScreenResponseValidationError{
						field:  fmt.Sprintf("RewardOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardOffersForScreenResponseValidationError{
					field:  fmt.Sprintf("RewardOffers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRewardOffersForScreenResponseMultiError(errors)
	}

	return nil
}

// GetRewardOffersForScreenResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetRewardOffersForScreenResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRewardOffersForScreenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOffersForScreenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOffersForScreenResponseMultiError) AllErrors() []error { return m }

// GetRewardOffersForScreenResponseValidationError is the validation error
// returned by GetRewardOffersForScreenResponse.Validate if the designated
// constraints aren't met.
type GetRewardOffersForScreenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOffersForScreenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOffersForScreenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOffersForScreenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOffersForScreenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOffersForScreenResponseValidationError) ErrorName() string {
	return "GetRewardOffersForScreenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOffersForScreenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOffersForScreenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOffersForScreenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOffersForScreenResponseValidationError{}

// Validate checks the field values on UpdateRewardOfferStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateRewardOfferStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRewardOfferStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateRewardOfferStatusRequestMultiError, or nil if none found.
func (m *UpdateRewardOfferStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRewardOfferStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Status

	if len(errors) > 0 {
		return UpdateRewardOfferStatusRequestMultiError(errors)
	}

	return nil
}

// UpdateRewardOfferStatusRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateRewardOfferStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateRewardOfferStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRewardOfferStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRewardOfferStatusRequestMultiError) AllErrors() []error { return m }

// UpdateRewardOfferStatusRequestValidationError is the validation error
// returned by UpdateRewardOfferStatusRequest.Validate if the designated
// constraints aren't met.
type UpdateRewardOfferStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRewardOfferStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRewardOfferStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRewardOfferStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRewardOfferStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRewardOfferStatusRequestValidationError) ErrorName() string {
	return "UpdateRewardOfferStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRewardOfferStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRewardOfferStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRewardOfferStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRewardOfferStatusRequestValidationError{}

// Validate checks the field values on UpdateRewardOfferDisplayRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateRewardOfferDisplayRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRewardOfferDisplayRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateRewardOfferDisplayRequestMultiError, or nil if none found.
func (m *UpdateRewardOfferDisplayRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRewardOfferDisplayRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetDisplayMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRewardOfferDisplayRequestValidationError{
					field:  "DisplayMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRewardOfferDisplayRequestValidationError{
					field:  "DisplayMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRewardOfferDisplayRequestValidationError{
				field:  "DisplayMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsVisible

	if len(errors) > 0 {
		return UpdateRewardOfferDisplayRequestMultiError(errors)
	}

	return nil
}

// UpdateRewardOfferDisplayRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateRewardOfferDisplayRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateRewardOfferDisplayRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRewardOfferDisplayRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRewardOfferDisplayRequestMultiError) AllErrors() []error { return m }

// UpdateRewardOfferDisplayRequestValidationError is the validation error
// returned by UpdateRewardOfferDisplayRequest.Validate if the designated
// constraints aren't met.
type UpdateRewardOfferDisplayRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRewardOfferDisplayRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRewardOfferDisplayRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRewardOfferDisplayRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRewardOfferDisplayRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRewardOfferDisplayRequestValidationError) ErrorName() string {
	return "UpdateRewardOfferDisplayRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRewardOfferDisplayRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRewardOfferDisplayRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRewardOfferDisplayRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRewardOfferDisplayRequestValidationError{}

// Validate checks the field values on UpdateRewardOfferRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateRewardOfferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRewardOfferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateRewardOfferRequestMultiError, or nil if none found.
func (m *UpdateRewardOfferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRewardOfferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetDisplayMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRewardOfferRequestValidationError{
					field:  "DisplayMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRewardOfferRequestValidationError{
					field:  "DisplayMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRewardOfferRequestValidationError{
				field:  "DisplayMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActiveTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRewardOfferRequestValidationError{
					field:  "ActiveTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRewardOfferRequestValidationError{
					field:  "ActiveTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActiveTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRewardOfferRequestValidationError{
				field:  "ActiveTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ConstraintMeta

	// no validation rules for RewardOfferType

	// no validation rules for ShouldUpdateRewardOfferType

	// no validation rules for ActionType

	if all {
		switch v := interface{}(m.GetUnlockMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRewardOfferRequestValidationError{
					field:  "UnlockMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRewardOfferRequestValidationError{
					field:  "UnlockMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnlockMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRewardOfferRequestValidationError{
				field:  "UnlockMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UnlockEvent

	// no validation rules for RewardMeta

	if all {
		switch v := interface{}(m.GetAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRewardOfferRequestValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRewardOfferRequestValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRewardOfferRequestValidationError{
				field:  "AdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActiveSince()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRewardOfferRequestValidationError{
					field:  "ActiveSince",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRewardOfferRequestValidationError{
					field:  "ActiveSince",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActiveSince()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRewardOfferRequestValidationError{
				field:  "ActiveSince",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GroupId

	// no validation rules for DisplaySegmentExpression

	// no validation rules for IsVisible

	if all {
		switch v := interface{}(m.GetAnalyticsData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRewardOfferRequestValidationError{
					field:  "AnalyticsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRewardOfferRequestValidationError{
					field:  "AnalyticsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnalyticsData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRewardOfferRequestValidationError{
				field:  "AnalyticsData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateRewardOfferRequestMultiError(errors)
	}

	return nil
}

// UpdateRewardOfferRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateRewardOfferRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateRewardOfferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRewardOfferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRewardOfferRequestMultiError) AllErrors() []error { return m }

// UpdateRewardOfferRequestValidationError is the validation error returned by
// UpdateRewardOfferRequest.Validate if the designated constraints aren't met.
type UpdateRewardOfferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRewardOfferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRewardOfferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRewardOfferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRewardOfferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRewardOfferRequestValidationError) ErrorName() string {
	return "UpdateRewardOfferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRewardOfferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRewardOfferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRewardOfferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRewardOfferRequestValidationError{}

// Validate checks the field values on AnalyticsData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AnalyticsData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnalyticsData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AnalyticsDataMultiError, or
// nil if none found.
func (m *AnalyticsData) ValidateAll() error {
	return m.validate(true)
}

func (m *AnalyticsData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RewardClass

	// no validation rules for RewardPurpose

	// no validation rules for RewardProduct

	if len(errors) > 0 {
		return AnalyticsDataMultiError(errors)
	}

	return nil
}

// AnalyticsDataMultiError is an error wrapping multiple validation errors
// returned by AnalyticsData.ValidateAll() if the designated constraints
// aren't met.
type AnalyticsDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnalyticsDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnalyticsDataMultiError) AllErrors() []error { return m }

// AnalyticsDataValidationError is the validation error returned by
// AnalyticsData.Validate if the designated constraints aren't met.
type AnalyticsDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnalyticsDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnalyticsDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnalyticsDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnalyticsDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnalyticsDataValidationError) ErrorName() string { return "AnalyticsDataValidationError" }

// Error satisfies the builtin error interface
func (e AnalyticsDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnalyticsData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnalyticsDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnalyticsDataValidationError{}

// Validate checks the field values on CTA with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *CTA) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CTA with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CTAMultiError, or nil if none found.
func (m *CTA) ValidateAll() error {
	return m.validate(true)
}

func (m *CTA) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CTAValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CTAValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CTAValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Desc

	// no validation rules for IconUrl

	if len(errors) > 0 {
		return CTAMultiError(errors)
	}

	return nil
}

// CTAMultiError is an error wrapping multiple validation errors returned by
// CTA.ValidateAll() if the designated constraints aren't met.
type CTAMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CTAMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CTAMultiError) AllErrors() []error { return m }

// CTAValidationError is the validation error returned by CTA.Validate if the
// designated constraints aren't met.
type CTAValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CTAValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CTAValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CTAValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CTAValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CTAValidationError) ErrorName() string { return "CTAValidationError" }

// Error satisfies the builtin error interface
func (e CTAValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCTA.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CTAValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CTAValidationError{}

// Validate checks the field values on UnlockMeta with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UnlockMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnlockMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UnlockMetaMultiError, or
// nil if none found.
func (m *UnlockMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *UnlockMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnlockMetaValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnlockMetaValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnlockMetaValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConstraint()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnlockMetaValidationError{
					field:  "Constraint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnlockMetaValidationError{
					field:  "Constraint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConstraint()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnlockMetaValidationError{
				field:  "Constraint",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UnlockMetaMultiError(errors)
	}

	return nil
}

// UnlockMetaMultiError is an error wrapping multiple validation errors
// returned by UnlockMeta.ValidateAll() if the designated constraints aren't met.
type UnlockMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnlockMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnlockMetaMultiError) AllErrors() []error { return m }

// UnlockMetaValidationError is the validation error returned by
// UnlockMeta.Validate if the designated constraints aren't met.
type UnlockMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnlockMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnlockMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnlockMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnlockMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnlockMetaValidationError) ErrorName() string { return "UnlockMetaValidationError" }

// Error satisfies the builtin error interface
func (e UnlockMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnlockMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnlockMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnlockMetaValidationError{}

// Validate checks the field values on USStockRewardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *USStockRewardConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on USStockRewardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// USStockRewardConfigMultiError, or nil if none found.
func (m *USStockRewardConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *USStockRewardConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StockId

	if len(errors) > 0 {
		return USStockRewardConfigMultiError(errors)
	}

	return nil
}

// USStockRewardConfigMultiError is an error wrapping multiple validation
// errors returned by USStockRewardConfig.ValidateAll() if the designated
// constraints aren't met.
type USStockRewardConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m USStockRewardConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m USStockRewardConfigMultiError) AllErrors() []error { return m }

// USStockRewardConfigValidationError is the validation error returned by
// USStockRewardConfig.Validate if the designated constraints aren't met.
type USStockRewardConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e USStockRewardConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e USStockRewardConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e USStockRewardConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e USStockRewardConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e USStockRewardConfigValidationError) ErrorName() string {
	return "USStockRewardConfigValidationError"
}

// Error satisfies the builtin error interface
func (e USStockRewardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUSStockRewardConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = USStockRewardConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = USStockRewardConfigValidationError{}

// Validate checks the field values on FiCoinsRewardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FiCoinsRewardConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiCoinsRewardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FiCoinsRewardConfigMultiError, or nil if none found.
func (m *FiCoinsRewardConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *FiCoinsRewardConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _FiCoinsRewardConfig_IsFiPoints_NotInLookup[m.GetIsFiPoints()]; ok {
		err := FiCoinsRewardConfigValidationError{
			field:  "IsFiPoints",
			reason: "value must not be in list [BOOLEAN_ENUM_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FiCoinsRewardConfigMultiError(errors)
	}

	return nil
}

// FiCoinsRewardConfigMultiError is an error wrapping multiple validation
// errors returned by FiCoinsRewardConfig.ValidateAll() if the designated
// constraints aren't met.
type FiCoinsRewardConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiCoinsRewardConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiCoinsRewardConfigMultiError) AllErrors() []error { return m }

// FiCoinsRewardConfigValidationError is the validation error returned by
// FiCoinsRewardConfig.Validate if the designated constraints aren't met.
type FiCoinsRewardConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiCoinsRewardConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiCoinsRewardConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiCoinsRewardConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiCoinsRewardConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiCoinsRewardConfigValidationError) ErrorName() string {
	return "FiCoinsRewardConfigValidationError"
}

// Error satisfies the builtin error interface
func (e FiCoinsRewardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiCoinsRewardConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiCoinsRewardConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiCoinsRewardConfigValidationError{}

var _FiCoinsRewardConfig_IsFiPoints_NotInLookup = map[common.BooleanEnum]struct{}{
	0: {},
}

// Validate checks the field values on EarningPotential with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EarningPotential) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EarningPotential with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EarningPotentialMultiError, or nil if none found.
func (m *EarningPotential) ValidateAll() error {
	return m.validate(true)
}

func (m *EarningPotential) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Icon

	// no validation rules for PotentialEarnings

	// no validation rules for SubText

	if len(errors) > 0 {
		return EarningPotentialMultiError(errors)
	}

	return nil
}

// EarningPotentialMultiError is an error wrapping multiple validation errors
// returned by EarningPotential.ValidateAll() if the designated constraints
// aren't met.
type EarningPotentialMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EarningPotentialMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EarningPotentialMultiError) AllErrors() []error { return m }

// EarningPotentialValidationError is the validation error returned by
// EarningPotential.Validate if the designated constraints aren't met.
type EarningPotentialValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EarningPotentialValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EarningPotentialValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EarningPotentialValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EarningPotentialValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EarningPotentialValidationError) ErrorName() string { return "EarningPotentialValidationError" }

// Error satisfies the builtin error interface
func (e EarningPotentialValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEarningPotential.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EarningPotentialValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EarningPotentialValidationError{}

// Validate checks the field values on IconAndText with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IconAndText) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IconAndText with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IconAndTextMultiError, or
// nil if none found.
func (m *IconAndText) ValidateAll() error {
	return m.validate(true)
}

func (m *IconAndText) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Icon

	// no validation rules for Text

	if len(errors) > 0 {
		return IconAndTextMultiError(errors)
	}

	return nil
}

// IconAndTextMultiError is an error wrapping multiple validation errors
// returned by IconAndText.ValidateAll() if the designated constraints aren't met.
type IconAndTextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IconAndTextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IconAndTextMultiError) AllErrors() []error { return m }

// IconAndTextValidationError is the validation error returned by
// IconAndText.Validate if the designated constraints aren't met.
type IconAndTextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IconAndTextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IconAndTextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IconAndTextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IconAndTextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IconAndTextValidationError) ErrorName() string { return "IconAndTextValidationError" }

// Error satisfies the builtin error interface
func (e IconAndTextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIconAndText.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IconAndTextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IconAndTextValidationError{}

// Validate checks the field values on WaysToEarnRewardsV2ScreenConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WaysToEarnRewardsV2ScreenConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WaysToEarnRewardsV2ScreenConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// WaysToEarnRewardsV2ScreenConfigMultiError, or nil if none found.
func (m *WaysToEarnRewardsV2ScreenConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *WaysToEarnRewardsV2ScreenConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferRowTitle

	// no validation rules for OfferRowIcon

	// no validation rules for OfferDetailsIcon

	// no validation rules for OfferDetailsHeaderBgColor

	if all {
		switch v := interface{}(m.GetOfferRowEarningPotential()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WaysToEarnRewardsV2ScreenConfigValidationError{
					field:  "OfferRowEarningPotential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WaysToEarnRewardsV2ScreenConfigValidationError{
					field:  "OfferRowEarningPotential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferRowEarningPotential()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WaysToEarnRewardsV2ScreenConfigValidationError{
				field:  "OfferRowEarningPotential",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOfferRowSubSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WaysToEarnRewardsV2ScreenConfigValidationError{
					field:  "OfferRowSubSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WaysToEarnRewardsV2ScreenConfigValidationError{
					field:  "OfferRowSubSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferRowSubSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WaysToEarnRewardsV2ScreenConfigValidationError{
				field:  "OfferRowSubSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOfferDetailsEarningPotentialSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WaysToEarnRewardsV2ScreenConfigValidationError{
					field:  "OfferDetailsEarningPotentialSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WaysToEarnRewardsV2ScreenConfigValidationError{
					field:  "OfferDetailsEarningPotentialSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferDetailsEarningPotentialSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WaysToEarnRewardsV2ScreenConfigValidationError{
				field:  "OfferDetailsEarningPotentialSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOfferRowTopLeftTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WaysToEarnRewardsV2ScreenConfigValidationError{
					field:  "OfferRowTopLeftTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WaysToEarnRewardsV2ScreenConfigValidationError{
					field:  "OfferRowTopLeftTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferRowTopLeftTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WaysToEarnRewardsV2ScreenConfigValidationError{
				field:  "OfferRowTopLeftTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOfferDetailsTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WaysToEarnRewardsV2ScreenConfigValidationError{
					field:  "OfferDetailsTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WaysToEarnRewardsV2ScreenConfigValidationError{
					field:  "OfferDetailsTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferDetailsTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WaysToEarnRewardsV2ScreenConfigValidationError{
				field:  "OfferDetailsTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WaysToEarnRewardsV2ScreenConfigMultiError(errors)
	}

	return nil
}

// WaysToEarnRewardsV2ScreenConfigMultiError is an error wrapping multiple
// validation errors returned by WaysToEarnRewardsV2ScreenConfig.ValidateAll()
// if the designated constraints aren't met.
type WaysToEarnRewardsV2ScreenConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WaysToEarnRewardsV2ScreenConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WaysToEarnRewardsV2ScreenConfigMultiError) AllErrors() []error { return m }

// WaysToEarnRewardsV2ScreenConfigValidationError is the validation error
// returned by WaysToEarnRewardsV2ScreenConfig.Validate if the designated
// constraints aren't met.
type WaysToEarnRewardsV2ScreenConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WaysToEarnRewardsV2ScreenConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WaysToEarnRewardsV2ScreenConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WaysToEarnRewardsV2ScreenConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WaysToEarnRewardsV2ScreenConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WaysToEarnRewardsV2ScreenConfigValidationError) ErrorName() string {
	return "WaysToEarnRewardsV2ScreenConfigValidationError"
}

// Error satisfies the builtin error interface
func (e WaysToEarnRewardsV2ScreenConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWaysToEarnRewardsV2ScreenConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WaysToEarnRewardsV2ScreenConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WaysToEarnRewardsV2ScreenConfigValidationError{}

// Validate checks the field values on
// RewardUnitsGeneratedFromProjectionUnitCaps with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RewardUnitsGeneratedFromProjectionUnitCaps) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RewardUnitsGeneratedFromProjectionUnitCaps with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// RewardUnitsGeneratedFromProjectionUnitCapsMultiError, or nil if none found.
func (m *RewardUnitsGeneratedFromProjectionUnitCaps) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardUnitsGeneratedFromProjectionUnitCaps) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferTypeToUnitCapsMap

	// no validation rules for ActionTypeToUnitCapsMap

	// no validation rules for OfferIdToUnitCapsMap

	// no validation rules for DailyUnitsCap

	if len(errors) > 0 {
		return RewardUnitsGeneratedFromProjectionUnitCapsMultiError(errors)
	}

	return nil
}

// RewardUnitsGeneratedFromProjectionUnitCapsMultiError is an error wrapping
// multiple validation errors returned by
// RewardUnitsGeneratedFromProjectionUnitCaps.ValidateAll() if the designated
// constraints aren't met.
type RewardUnitsGeneratedFromProjectionUnitCapsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardUnitsGeneratedFromProjectionUnitCapsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardUnitsGeneratedFromProjectionUnitCapsMultiError) AllErrors() []error { return m }

// RewardUnitsGeneratedFromProjectionUnitCapsValidationError is the validation
// error returned by RewardUnitsGeneratedFromProjectionUnitCaps.Validate if
// the designated constraints aren't met.
type RewardUnitsGeneratedFromProjectionUnitCapsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardUnitsGeneratedFromProjectionUnitCapsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardUnitsGeneratedFromProjectionUnitCapsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardUnitsGeneratedFromProjectionUnitCapsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardUnitsGeneratedFromProjectionUnitCapsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardUnitsGeneratedFromProjectionUnitCapsValidationError) ErrorName() string {
	return "RewardUnitsGeneratedFromProjectionUnitCapsValidationError"
}

// Error satisfies the builtin error interface
func (e RewardUnitsGeneratedFromProjectionUnitCapsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardUnitsGeneratedFromProjectionUnitCaps.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardUnitsGeneratedFromProjectionUnitCapsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardUnitsGeneratedFromProjectionUnitCapsValidationError{}

// Validate checks the field values on AdditionalDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AdditionalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdditionalDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdditionalDetailsMultiError, or nil if none found.
func (m *AdditionalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AdditionalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetWatsonMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AdditionalDetailsValidationError{
					field:  "WatsonMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AdditionalDetailsValidationError{
					field:  "WatsonMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWatsonMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AdditionalDetailsValidationError{
				field:  "WatsonMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AdditionalDetailsMultiError(errors)
	}

	return nil
}

// AdditionalDetailsMultiError is an error wrapping multiple validation errors
// returned by AdditionalDetails.ValidateAll() if the designated constraints
// aren't met.
type AdditionalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdditionalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdditionalDetailsMultiError) AllErrors() []error { return m }

// AdditionalDetailsValidationError is the validation error returned by
// AdditionalDetails.Validate if the designated constraints aren't met.
type AdditionalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdditionalDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdditionalDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdditionalDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdditionalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdditionalDetailsValidationError) ErrorName() string {
	return "AdditionalDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AdditionalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdditionalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdditionalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdditionalDetailsValidationError{}

// Validate checks the field values on WatsonMeta with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WatsonMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatsonMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WatsonMetaMultiError, or
// nil if none found.
func (m *WatsonMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *WatsonMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ShouldCreateWatsonEvent

	// no validation rules for IssueCategoryId

	if len(errors) > 0 {
		return WatsonMetaMultiError(errors)
	}

	return nil
}

// WatsonMetaMultiError is an error wrapping multiple validation errors
// returned by WatsonMeta.ValidateAll() if the designated constraints aren't met.
type WatsonMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatsonMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatsonMetaMultiError) AllErrors() []error { return m }

// WatsonMetaValidationError is the validation error returned by
// WatsonMeta.Validate if the designated constraints aren't met.
type WatsonMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatsonMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatsonMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatsonMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatsonMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatsonMetaValidationError) ErrorName() string { return "WatsonMetaValidationError" }

// Error satisfies the builtin error interface
func (e WatsonMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatsonMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatsonMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatsonMetaValidationError{}

// Validate checks the field values on
// ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitMultiError, or
// nil if none found.
func (m *ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit) ValidateAll() error {
	return m.validate(true)
}

func (m *ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UnitPercentageStart

	// no validation rules for UnitPercentageEnd

	// no validation rules for Percentage

	if len(errors) > 0 {
		return ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitMultiError(errors)
	}

	return nil
}

// ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitMultiError is an
// error wrapping multiple validation errors returned by
// ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit.ValidateAll()
// if the designated constraints aren't met.
type ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitMultiError) AllErrors() []error {
	return m
}

// ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitValidationError
// is the validation error returned by
// ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit.Validate if the
// designated constraints aren't met.
type ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitValidationError) ErrorName() string {
	return "ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitValidationError"
}

// Error satisfies the builtin error interface
func (e ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnitValidationError{}

// Validate checks the field values on
// ConditionalExpressionRangeProbabilityConfig_ConfigUnit with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConditionalExpressionRangeProbabilityConfig_ConfigUnit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ConditionalExpressionRangeProbabilityConfig_ConfigUnit with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConditionalExpressionRangeProbabilityConfig_ConfigUnitMultiError, or nil if
// none found.
func (m *ConditionalExpressionRangeProbabilityConfig_ConfigUnit) ValidateAll() error {
	return m.validate(true)
}

func (m *ConditionalExpressionRangeProbabilityConfig_ConfigUnit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConditionExpression

	if all {
		switch v := interface{}(m.GetExpressionRangeProbabilityConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError{
					field:  "ExpressionRangeProbabilityConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError{
					field:  "ExpressionRangeProbabilityConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpressionRangeProbabilityConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError{
				field:  "ExpressionRangeProbabilityConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConditionalExpressionRangeProbabilityConfig_ConfigUnitMultiError(errors)
	}

	return nil
}

// ConditionalExpressionRangeProbabilityConfig_ConfigUnitMultiError is an error
// wrapping multiple validation errors returned by
// ConditionalExpressionRangeProbabilityConfig_ConfigUnit.ValidateAll() if the
// designated constraints aren't met.
type ConditionalExpressionRangeProbabilityConfig_ConfigUnitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConditionalExpressionRangeProbabilityConfig_ConfigUnitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConditionalExpressionRangeProbabilityConfig_ConfigUnitMultiError) AllErrors() []error {
	return m
}

// ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError is the
// validation error returned by
// ConditionalExpressionRangeProbabilityConfig_ConfigUnit.Validate if the
// designated constraints aren't met.
type ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError) ErrorName() string {
	return "ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError"
}

// Error satisfies the builtin error interface
func (e ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConditionalExpressionRangeProbabilityConfig_ConfigUnit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConditionalExpressionRangeProbabilityConfig_ConfigUnitValidationError{}

// Validate checks the field values on
// ConditionalExpressionBoosterConfig_ConfigUnit with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ConditionalExpressionBoosterConfig_ConfigUnit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ConditionalExpressionBoosterConfig_ConfigUnit with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ConditionalExpressionBoosterConfig_ConfigUnitMultiError, or nil if none found.
func (m *ConditionalExpressionBoosterConfig_ConfigUnit) ValidateAll() error {
	return m.validate(true)
}

func (m *ConditionalExpressionBoosterConfig_ConfigUnit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConditionExpression

	// no validation rules for OperationWithPreviousValue

	if all {
		switch v := interface{}(m.GetDisplayDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConditionalExpressionBoosterConfig_ConfigUnitValidationError{
					field:  "DisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConditionalExpressionBoosterConfig_ConfigUnitValidationError{
					field:  "DisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConditionalExpressionBoosterConfig_ConfigUnitValidationError{
				field:  "DisplayDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UnitsUpperCapAfterOperationWithPreviousValue

	switch v := m.UnitsConfig.(type) {
	case *ConditionalExpressionBoosterConfig_ConfigUnit_ExpressionRangeProbabilityConfig:
		if v == nil {
			err := ConditionalExpressionBoosterConfig_ConfigUnitValidationError{
				field:  "UnitsConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExpressionRangeProbabilityConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ConditionalExpressionBoosterConfig_ConfigUnitValidationError{
						field:  "ExpressionRangeProbabilityConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ConditionalExpressionBoosterConfig_ConfigUnitValidationError{
						field:  "ExpressionRangeProbabilityConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExpressionRangeProbabilityConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ConditionalExpressionBoosterConfig_ConfigUnitValidationError{
					field:  "ExpressionRangeProbabilityConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ConditionalExpressionBoosterConfig_ConfigUnitMultiError(errors)
	}

	return nil
}

// ConditionalExpressionBoosterConfig_ConfigUnitMultiError is an error wrapping
// multiple validation errors returned by
// ConditionalExpressionBoosterConfig_ConfigUnit.ValidateAll() if the
// designated constraints aren't met.
type ConditionalExpressionBoosterConfig_ConfigUnitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConditionalExpressionBoosterConfig_ConfigUnitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConditionalExpressionBoosterConfig_ConfigUnitMultiError) AllErrors() []error { return m }

// ConditionalExpressionBoosterConfig_ConfigUnitValidationError is the
// validation error returned by
// ConditionalExpressionBoosterConfig_ConfigUnit.Validate if the designated
// constraints aren't met.
type ConditionalExpressionBoosterConfig_ConfigUnitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConditionalExpressionBoosterConfig_ConfigUnitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConditionalExpressionBoosterConfig_ConfigUnitValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ConditionalExpressionBoosterConfig_ConfigUnitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConditionalExpressionBoosterConfig_ConfigUnitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConditionalExpressionBoosterConfig_ConfigUnitValidationError) ErrorName() string {
	return "ConditionalExpressionBoosterConfig_ConfigUnitValidationError"
}

// Error satisfies the builtin error interface
func (e ConditionalExpressionBoosterConfig_ConfigUnitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConditionalExpressionBoosterConfig_ConfigUnit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConditionalExpressionBoosterConfig_ConfigUnitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConditionalExpressionBoosterConfig_ConfigUnitValidationError{}

// Validate checks the field values on
// ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsMultiError, or
// nil if none found.
func (m *ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for TitleColor

	// no validation rules for BgColor

	if len(errors) > 0 {
		return ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsMultiError(errors)
	}

	return nil
}

// ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsMultiError is an
// error wrapping multiple validation errors returned by
// ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails.ValidateAll()
// if the designated constraints aren't met.
type ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsMultiError) AllErrors() []error {
	return m
}

// ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsValidationError
// is the validation error returned by
// ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails.Validate if
// the designated constraints aren't met.
type ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsValidationError) ErrorName() string {
	return "ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetailsValidationError{}

// Validate checks the field values on
// DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigMultiError, or nil if
// none found.
func (m *DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Delta

	if len(errors) > 0 {
		return DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigMultiError(errors)
	}

	return nil
}

// DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigMultiError is an error
// wrapping multiple validation errors returned by
// DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig.ValidateAll() if the
// designated constraints aren't met.
type DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigMultiError) AllErrors() []error {
	return m
}

// DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigValidationError is the
// validation error returned by
// DeriveFromSeedRewardOptionConfig_DeltaDerivationConfig.Validate if the
// designated constraints aren't met.
type DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigValidationError) ErrorName() string {
	return "DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigValidationError"
}

// Error satisfies the builtin error interface
func (e DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeriveFromSeedRewardOptionConfig_DeltaDerivationConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeriveFromSeedRewardOptionConfig_DeltaDerivationConfigValidationError{}

// Validate checks the field values on
// DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigMultiError, or
// nil if none found.
func (m *DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Multiplier

	if len(errors) > 0 {
		return DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigMultiError(errors)
	}

	return nil
}

// DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigMultiError is an
// error wrapping multiple validation errors returned by
// DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig.ValidateAll()
// if the designated constraints aren't met.
type DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigMultiError) AllErrors() []error {
	return m
}

// DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigValidationError
// is the validation error returned by
// DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig.Validate if the
// designated constraints aren't met.
type DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigValidationError) ErrorName() string {
	return "DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigValidationError"
}

// Error satisfies the builtin error interface
func (e DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeriveFromSeedRewardOptionConfig_MultiplierDerivationConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeriveFromSeedRewardOptionConfig_MultiplierDerivationConfigValidationError{}

// Validate checks the field values on RewardConfigOption_Display with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardConfigOption_Display) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardConfigOption_Display with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardConfigOption_DisplayMultiError, or nil if none found.
func (m *RewardConfigOption_Display) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardConfigOption_Display) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetBeforeClaimTextExpression()) < 1 {
		err := RewardConfigOption_DisplayValidationError{
			field:  "BeforeClaimTextExpression",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAfterClaimTextExpression()) < 1 {
		err := RewardConfigOption_DisplayValidationError{
			field:  "AfterClaimTextExpression",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetIcon()) < 1 {
		err := RewardConfigOption_DisplayValidationError{
			field:  "Icon",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetBgColor()) < 1 {
		err := RewardConfigOption_DisplayValidationError{
			field:  "BgColor",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetHtmlFormattedDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardConfigOption_DisplayValidationError{
						field:  fmt.Sprintf("HtmlFormattedDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardConfigOption_DisplayValidationError{
						field:  fmt.Sprintf("HtmlFormattedDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardConfigOption_DisplayValidationError{
					field:  fmt.Sprintf("HtmlFormattedDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BeforeClaimBannerText

	if len(errors) > 0 {
		return RewardConfigOption_DisplayMultiError(errors)
	}

	return nil
}

// RewardConfigOption_DisplayMultiError is an error wrapping multiple
// validation errors returned by RewardConfigOption_Display.ValidateAll() if
// the designated constraints aren't met.
type RewardConfigOption_DisplayMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardConfigOption_DisplayMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardConfigOption_DisplayMultiError) AllErrors() []error { return m }

// RewardConfigOption_DisplayValidationError is the validation error returned
// by RewardConfigOption_Display.Validate if the designated constraints aren't met.
type RewardConfigOption_DisplayValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardConfigOption_DisplayValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardConfigOption_DisplayValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardConfigOption_DisplayValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardConfigOption_DisplayValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardConfigOption_DisplayValidationError) ErrorName() string {
	return "RewardConfigOption_DisplayValidationError"
}

// Error satisfies the builtin error interface
func (e RewardConfigOption_DisplayValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardConfigOption_Display.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardConfigOption_DisplayValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardConfigOption_DisplayValidationError{}

// Validate checks the field values on
// RewardConfigOption_ProjectionsQueryFilters with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RewardConfigOption_ProjectionsQueryFilters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RewardConfigOption_ProjectionsQueryFilters with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// RewardConfigOption_ProjectionsQueryFiltersMultiError, or nil if none found.
func (m *RewardConfigOption_ProjectionsQueryFilters) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardConfigOption_ProjectionsQueryFilters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RewardConfigOption_ProjectionsQueryFiltersMultiError(errors)
	}

	return nil
}

// RewardConfigOption_ProjectionsQueryFiltersMultiError is an error wrapping
// multiple validation errors returned by
// RewardConfigOption_ProjectionsQueryFilters.ValidateAll() if the designated
// constraints aren't met.
type RewardConfigOption_ProjectionsQueryFiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardConfigOption_ProjectionsQueryFiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardConfigOption_ProjectionsQueryFiltersMultiError) AllErrors() []error { return m }

// RewardConfigOption_ProjectionsQueryFiltersValidationError is the validation
// error returned by RewardConfigOption_ProjectionsQueryFilters.Validate if
// the designated constraints aren't met.
type RewardConfigOption_ProjectionsQueryFiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardConfigOption_ProjectionsQueryFiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardConfigOption_ProjectionsQueryFiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardConfigOption_ProjectionsQueryFiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardConfigOption_ProjectionsQueryFiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardConfigOption_ProjectionsQueryFiltersValidationError) ErrorName() string {
	return "RewardConfigOption_ProjectionsQueryFiltersValidationError"
}

// Error satisfies the builtin error interface
func (e RewardConfigOption_ProjectionsQueryFiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardConfigOption_ProjectionsQueryFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardConfigOption_ProjectionsQueryFiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardConfigOption_ProjectionsQueryFiltersValidationError{}

// Validate checks the field values on
// RewardConfigOption_Display_HtmlFormattedDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RewardConfigOption_Display_HtmlFormattedDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RewardConfigOption_Display_HtmlFormattedDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// RewardConfigOption_Display_HtmlFormattedDetailMultiError, or nil if none found.
func (m *RewardConfigOption_Display_HtmlFormattedDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardConfigOption_Display_HtmlFormattedDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Header

	// no validation rules for Body

	if len(errors) > 0 {
		return RewardConfigOption_Display_HtmlFormattedDetailMultiError(errors)
	}

	return nil
}

// RewardConfigOption_Display_HtmlFormattedDetailMultiError is an error
// wrapping multiple validation errors returned by
// RewardConfigOption_Display_HtmlFormattedDetail.ValidateAll() if the
// designated constraints aren't met.
type RewardConfigOption_Display_HtmlFormattedDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardConfigOption_Display_HtmlFormattedDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardConfigOption_Display_HtmlFormattedDetailMultiError) AllErrors() []error { return m }

// RewardConfigOption_Display_HtmlFormattedDetailValidationError is the
// validation error returned by
// RewardConfigOption_Display_HtmlFormattedDetail.Validate if the designated
// constraints aren't met.
type RewardConfigOption_Display_HtmlFormattedDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardConfigOption_Display_HtmlFormattedDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardConfigOption_Display_HtmlFormattedDetailValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e RewardConfigOption_Display_HtmlFormattedDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardConfigOption_Display_HtmlFormattedDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardConfigOption_Display_HtmlFormattedDetailValidationError) ErrorName() string {
	return "RewardConfigOption_Display_HtmlFormattedDetailValidationError"
}

// Error satisfies the builtin error interface
func (e RewardConfigOption_Display_HtmlFormattedDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardConfigOption_Display_HtmlFormattedDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardConfigOption_Display_HtmlFormattedDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardConfigOption_Display_HtmlFormattedDetailValidationError{}

// Validate checks the field values on BoosterConfig_DisplayDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BoosterConfig_DisplayDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BoosterConfig_DisplayDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BoosterConfig_DisplayDetailsMultiError, or nil if none found.
func (m *BoosterConfig_DisplayDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *BoosterConfig_DisplayDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BoosterConfig_DisplayDetailsMultiError(errors)
	}

	return nil
}

// BoosterConfig_DisplayDetailsMultiError is an error wrapping multiple
// validation errors returned by BoosterConfig_DisplayDetails.ValidateAll() if
// the designated constraints aren't met.
type BoosterConfig_DisplayDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BoosterConfig_DisplayDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BoosterConfig_DisplayDetailsMultiError) AllErrors() []error { return m }

// BoosterConfig_DisplayDetailsValidationError is the validation error returned
// by BoosterConfig_DisplayDetails.Validate if the designated constraints
// aren't met.
type BoosterConfig_DisplayDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BoosterConfig_DisplayDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BoosterConfig_DisplayDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BoosterConfig_DisplayDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BoosterConfig_DisplayDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BoosterConfig_DisplayDetailsValidationError) ErrorName() string {
	return "BoosterConfig_DisplayDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e BoosterConfig_DisplayDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBoosterConfig_DisplayDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BoosterConfig_DisplayDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BoosterConfig_DisplayDetailsValidationError{}

// Validate checks the field values on RewardMeta_RewardDisplayMeta with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardMeta_RewardDisplayMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardMeta_RewardDisplayMeta with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardMeta_RewardDisplayMetaMultiError, or nil if none found.
func (m *RewardMeta_RewardDisplayMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardMeta_RewardDisplayMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TileBgImageBeforeClaim

	// no validation rules for TileBgImageAfterClaim

	// no validation rules for RewardTileThemeType

	// no validation rules for IsAnimationUnskippable

	// no validation rules for SkipAnimation

	// no validation rules for AnimationSpeed

	if len(errors) > 0 {
		return RewardMeta_RewardDisplayMetaMultiError(errors)
	}

	return nil
}

// RewardMeta_RewardDisplayMetaMultiError is an error wrapping multiple
// validation errors returned by RewardMeta_RewardDisplayMeta.ValidateAll() if
// the designated constraints aren't met.
type RewardMeta_RewardDisplayMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardMeta_RewardDisplayMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardMeta_RewardDisplayMetaMultiError) AllErrors() []error { return m }

// RewardMeta_RewardDisplayMetaValidationError is the validation error returned
// by RewardMeta_RewardDisplayMeta.Validate if the designated constraints
// aren't met.
type RewardMeta_RewardDisplayMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardMeta_RewardDisplayMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardMeta_RewardDisplayMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardMeta_RewardDisplayMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardMeta_RewardDisplayMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardMeta_RewardDisplayMetaValidationError) ErrorName() string {
	return "RewardMeta_RewardDisplayMetaValidationError"
}

// Error satisfies the builtin error interface
func (e RewardMeta_RewardDisplayMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardMeta_RewardDisplayMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardMeta_RewardDisplayMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardMeta_RewardDisplayMetaValidationError{}

// Validate checks the field values on RewardUnitsCapAggregate_RewardUnitsCap
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RewardUnitsCapAggregate_RewardUnitsCap) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RewardUnitsCapAggregate_RewardUnitsCap with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// RewardUnitsCapAggregate_RewardUnitsCapMultiError, or nil if none found.
func (m *RewardUnitsCapAggregate_RewardUnitsCap) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardUnitsCapAggregate_RewardUnitsCap) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RewardType

	// no validation rules for Units

	if len(errors) > 0 {
		return RewardUnitsCapAggregate_RewardUnitsCapMultiError(errors)
	}

	return nil
}

// RewardUnitsCapAggregate_RewardUnitsCapMultiError is an error wrapping
// multiple validation errors returned by
// RewardUnitsCapAggregate_RewardUnitsCap.ValidateAll() if the designated
// constraints aren't met.
type RewardUnitsCapAggregate_RewardUnitsCapMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardUnitsCapAggregate_RewardUnitsCapMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardUnitsCapAggregate_RewardUnitsCapMultiError) AllErrors() []error { return m }

// RewardUnitsCapAggregate_RewardUnitsCapValidationError is the validation
// error returned by RewardUnitsCapAggregate_RewardUnitsCap.Validate if the
// designated constraints aren't met.
type RewardUnitsCapAggregate_RewardUnitsCapValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardUnitsCapAggregate_RewardUnitsCapValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardUnitsCapAggregate_RewardUnitsCapValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardUnitsCapAggregate_RewardUnitsCapValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardUnitsCapAggregate_RewardUnitsCapValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardUnitsCapAggregate_RewardUnitsCapValidationError) ErrorName() string {
	return "RewardUnitsCapAggregate_RewardUnitsCapValidationError"
}

// Error satisfies the builtin error interface
func (e RewardUnitsCapAggregate_RewardUnitsCapValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardUnitsCapAggregate_RewardUnitsCap.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardUnitsCapAggregate_RewardUnitsCapValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardUnitsCapAggregate_RewardUnitsCapValidationError{}

// Validate checks the field values on DisplayMeta_Tag with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DisplayMeta_Tag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DisplayMeta_Tag with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DisplayMeta_TagMultiError, or nil if none found.
func (m *DisplayMeta_Tag) ValidateAll() error {
	return m.validate(true)
}

func (m *DisplayMeta_Tag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	// no validation rules for TextColor

	// no validation rules for BgColor

	if len(errors) > 0 {
		return DisplayMeta_TagMultiError(errors)
	}

	return nil
}

// DisplayMeta_TagMultiError is an error wrapping multiple validation errors
// returned by DisplayMeta_Tag.ValidateAll() if the designated constraints
// aren't met.
type DisplayMeta_TagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DisplayMeta_TagMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DisplayMeta_TagMultiError) AllErrors() []error { return m }

// DisplayMeta_TagValidationError is the validation error returned by
// DisplayMeta_Tag.Validate if the designated constraints aren't met.
type DisplayMeta_TagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DisplayMeta_TagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DisplayMeta_TagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DisplayMeta_TagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DisplayMeta_TagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DisplayMeta_TagValidationError) ErrorName() string { return "DisplayMeta_TagValidationError" }

// Error satisfies the builtin error interface
func (e DisplayMeta_TagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDisplayMeta_Tag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DisplayMeta_TagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DisplayMeta_TagValidationError{}

// Validate checks the field values on DisplayMeta_InfoPoint with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DisplayMeta_InfoPoint) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DisplayMeta_InfoPoint with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DisplayMeta_InfoPointMultiError, or nil if none found.
func (m *DisplayMeta_InfoPoint) ValidateAll() error {
	return m.validate(true)
}

func (m *DisplayMeta_InfoPoint) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DisplayMeta_InfoPointValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DisplayMeta_InfoPointValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DisplayMeta_InfoPointValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DisplayMeta_InfoPointValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DisplayMeta_InfoPointValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DisplayMeta_InfoPointValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DisplayMeta_InfoPointMultiError(errors)
	}

	return nil
}

// DisplayMeta_InfoPointMultiError is an error wrapping multiple validation
// errors returned by DisplayMeta_InfoPoint.ValidateAll() if the designated
// constraints aren't met.
type DisplayMeta_InfoPointMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DisplayMeta_InfoPointMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DisplayMeta_InfoPointMultiError) AllErrors() []error { return m }

// DisplayMeta_InfoPointValidationError is the validation error returned by
// DisplayMeta_InfoPoint.Validate if the designated constraints aren't met.
type DisplayMeta_InfoPointValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DisplayMeta_InfoPointValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DisplayMeta_InfoPointValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DisplayMeta_InfoPointValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DisplayMeta_InfoPointValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DisplayMeta_InfoPointValidationError) ErrorName() string {
	return "DisplayMeta_InfoPointValidationError"
}

// Error satisfies the builtin error interface
func (e DisplayMeta_InfoPointValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDisplayMeta_InfoPoint.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DisplayMeta_InfoPointValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DisplayMeta_InfoPointValidationError{}

// Validate checks the field values on
// EarnedRewardNotificationConfig_NotificationConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EarnedRewardNotificationConfig_NotificationConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// EarnedRewardNotificationConfig_NotificationConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// EarnedRewardNotificationConfig_NotificationConfigMultiError, or nil if none found.
func (m *EarnedRewardNotificationConfig_NotificationConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *EarnedRewardNotificationConfig_NotificationConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NotificationType

	// no validation rules for RewardCountLevelNotificationCap

	if all {
		switch v := interface{}(m.GetNotificationDelayTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EarnedRewardNotificationConfig_NotificationConfigValidationError{
					field:  "NotificationDelayTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EarnedRewardNotificationConfig_NotificationConfigValidationError{
					field:  "NotificationDelayTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotificationDelayTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EarnedRewardNotificationConfig_NotificationConfigValidationError{
				field:  "NotificationDelayTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNotificationMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EarnedRewardNotificationConfig_NotificationConfigValidationError{
					field:  "NotificationMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EarnedRewardNotificationConfig_NotificationConfigValidationError{
					field:  "NotificationMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotificationMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EarnedRewardNotificationConfig_NotificationConfigValidationError{
				field:  "NotificationMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EarnedRewardNotificationConfig_NotificationConfigValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EarnedRewardNotificationConfig_NotificationConfigValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EarnedRewardNotificationConfig_NotificationConfigValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EarnedRewardNotificationConfig_NotificationConfigMultiError(errors)
	}

	return nil
}

// EarnedRewardNotificationConfig_NotificationConfigMultiError is an error
// wrapping multiple validation errors returned by
// EarnedRewardNotificationConfig_NotificationConfig.ValidateAll() if the
// designated constraints aren't met.
type EarnedRewardNotificationConfig_NotificationConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EarnedRewardNotificationConfig_NotificationConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EarnedRewardNotificationConfig_NotificationConfigMultiError) AllErrors() []error { return m }

// EarnedRewardNotificationConfig_NotificationConfigValidationError is the
// validation error returned by
// EarnedRewardNotificationConfig_NotificationConfig.Validate if the
// designated constraints aren't met.
type EarnedRewardNotificationConfig_NotificationConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EarnedRewardNotificationConfig_NotificationConfigValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e EarnedRewardNotificationConfig_NotificationConfigValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e EarnedRewardNotificationConfig_NotificationConfigValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e EarnedRewardNotificationConfig_NotificationConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EarnedRewardNotificationConfig_NotificationConfigValidationError) ErrorName() string {
	return "EarnedRewardNotificationConfig_NotificationConfigValidationError"
}

// Error satisfies the builtin error interface
func (e EarnedRewardNotificationConfig_NotificationConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEarnedRewardNotificationConfig_NotificationConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EarnedRewardNotificationConfig_NotificationConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EarnedRewardNotificationConfig_NotificationConfigValidationError{}

// Validate checks the field values on
// RewardProcessingSuccessfulNotificationConfig_NotificationConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardProcessingSuccessfulNotificationConfig_NotificationConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RewardProcessingSuccessfulNotificationConfig_NotificationConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardProcessingSuccessfulNotificationConfig_NotificationConfigMultiError,
// or nil if none found.
func (m *RewardProcessingSuccessfulNotificationConfig_NotificationConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardProcessingSuccessfulNotificationConfig_NotificationConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NotificationType

	if all {
		switch v := interface{}(m.GetNotificationDelayTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError{
					field:  "NotificationDelayTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError{
					field:  "NotificationDelayTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotificationDelayTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError{
				field:  "NotificationDelayTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNotificationMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError{
					field:  "NotificationMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError{
					field:  "NotificationMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotificationMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError{
				field:  "NotificationMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardProcessingSuccessfulNotificationConfig_NotificationConfigMultiError(errors)
	}

	return nil
}

// RewardProcessingSuccessfulNotificationConfig_NotificationConfigMultiError is
// an error wrapping multiple validation errors returned by
// RewardProcessingSuccessfulNotificationConfig_NotificationConfig.ValidateAll()
// if the designated constraints aren't met.
type RewardProcessingSuccessfulNotificationConfig_NotificationConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardProcessingSuccessfulNotificationConfig_NotificationConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardProcessingSuccessfulNotificationConfig_NotificationConfigMultiError) AllErrors() []error {
	return m
}

// RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError
// is the validation error returned by
// RewardProcessingSuccessfulNotificationConfig_NotificationConfig.Validate if
// the designated constraints aren't met.
type RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError) ErrorName() string {
	return "RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError"
}

// Error satisfies the builtin error interface
func (e RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardProcessingSuccessfulNotificationConfig_NotificationConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardProcessingSuccessfulNotificationConfig_NotificationConfigValidationError{}

// Validate checks the field values on
// RewardOfferResponse_ValidationFailureInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RewardOfferResponse_ValidationFailureInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RewardOfferResponse_ValidationFailureInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// RewardOfferResponse_ValidationFailureInfoMultiError, or nil if none found.
func (m *RewardOfferResponse_ValidationFailureInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardOfferResponse_ValidationFailureInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FailureMessage

	if len(errors) > 0 {
		return RewardOfferResponse_ValidationFailureInfoMultiError(errors)
	}

	return nil
}

// RewardOfferResponse_ValidationFailureInfoMultiError is an error wrapping
// multiple validation errors returned by
// RewardOfferResponse_ValidationFailureInfo.ValidateAll() if the designated
// constraints aren't met.
type RewardOfferResponse_ValidationFailureInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardOfferResponse_ValidationFailureInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardOfferResponse_ValidationFailureInfoMultiError) AllErrors() []error { return m }

// RewardOfferResponse_ValidationFailureInfoValidationError is the validation
// error returned by RewardOfferResponse_ValidationFailureInfo.Validate if the
// designated constraints aren't met.
type RewardOfferResponse_ValidationFailureInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardOfferResponse_ValidationFailureInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardOfferResponse_ValidationFailureInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardOfferResponse_ValidationFailureInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardOfferResponse_ValidationFailureInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardOfferResponse_ValidationFailureInfoValidationError) ErrorName() string {
	return "RewardOfferResponse_ValidationFailureInfoValidationError"
}

// Error satisfies the builtin error interface
func (e RewardOfferResponse_ValidationFailureInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardOfferResponse_ValidationFailureInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardOfferResponse_ValidationFailureInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardOfferResponse_ValidationFailureInfoValidationError{}

// Validate checks the field values on GetRewardOffersForScreenRequest_Filter
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRewardOffersForScreenRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRewardOffersForScreenRequest_Filter with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetRewardOffersForScreenRequest_FilterMultiError, or nil if none found.
func (m *GetRewardOffersForScreenRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardOffersForScreenRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RewardOfferId

	// no validation rules for GenerationType

	if len(errors) > 0 {
		return GetRewardOffersForScreenRequest_FilterMultiError(errors)
	}

	return nil
}

// GetRewardOffersForScreenRequest_FilterMultiError is an error wrapping
// multiple validation errors returned by
// GetRewardOffersForScreenRequest_Filter.ValidateAll() if the designated
// constraints aren't met.
type GetRewardOffersForScreenRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardOffersForScreenRequest_FilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardOffersForScreenRequest_FilterMultiError) AllErrors() []error { return m }

// GetRewardOffersForScreenRequest_FilterValidationError is the validation
// error returned by GetRewardOffersForScreenRequest_Filter.Validate if the
// designated constraints aren't met.
type GetRewardOffersForScreenRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardOffersForScreenRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardOffersForScreenRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardOffersForScreenRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardOffersForScreenRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardOffersForScreenRequest_FilterValidationError) ErrorName() string {
	return "GetRewardOffersForScreenRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardOffersForScreenRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardOffersForScreenRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardOffersForScreenRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardOffersForScreenRequest_FilterValidationError{}

// Validate checks the field values on WaysToEarnRewardsV2ScreenConfig_Tag with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *WaysToEarnRewardsV2ScreenConfig_Tag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WaysToEarnRewardsV2ScreenConfig_Tag
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// WaysToEarnRewardsV2ScreenConfig_TagMultiError, or nil if none found.
func (m *WaysToEarnRewardsV2ScreenConfig_Tag) ValidateAll() error {
	return m.validate(true)
}

func (m *WaysToEarnRewardsV2ScreenConfig_Tag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIconAndText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WaysToEarnRewardsV2ScreenConfig_TagValidationError{
					field:  "IconAndText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WaysToEarnRewardsV2ScreenConfig_TagValidationError{
					field:  "IconAndText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIconAndText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WaysToEarnRewardsV2ScreenConfig_TagValidationError{
				field:  "IconAndText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if len(errors) > 0 {
		return WaysToEarnRewardsV2ScreenConfig_TagMultiError(errors)
	}

	return nil
}

// WaysToEarnRewardsV2ScreenConfig_TagMultiError is an error wrapping multiple
// validation errors returned by
// WaysToEarnRewardsV2ScreenConfig_Tag.ValidateAll() if the designated
// constraints aren't met.
type WaysToEarnRewardsV2ScreenConfig_TagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WaysToEarnRewardsV2ScreenConfig_TagMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WaysToEarnRewardsV2ScreenConfig_TagMultiError) AllErrors() []error { return m }

// WaysToEarnRewardsV2ScreenConfig_TagValidationError is the validation error
// returned by WaysToEarnRewardsV2ScreenConfig_Tag.Validate if the designated
// constraints aren't met.
type WaysToEarnRewardsV2ScreenConfig_TagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WaysToEarnRewardsV2ScreenConfig_TagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WaysToEarnRewardsV2ScreenConfig_TagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WaysToEarnRewardsV2ScreenConfig_TagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WaysToEarnRewardsV2ScreenConfig_TagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WaysToEarnRewardsV2ScreenConfig_TagValidationError) ErrorName() string {
	return "WaysToEarnRewardsV2ScreenConfig_TagValidationError"
}

// Error satisfies the builtin error interface
func (e WaysToEarnRewardsV2ScreenConfig_TagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWaysToEarnRewardsV2ScreenConfig_Tag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WaysToEarnRewardsV2ScreenConfig_TagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WaysToEarnRewardsV2ScreenConfig_TagValidationError{}
