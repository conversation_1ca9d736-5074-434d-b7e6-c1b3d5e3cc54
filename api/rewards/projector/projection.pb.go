//go:generate gen_sql -types=RewardOption,OptionsInfo

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/rewards/projector/projection.proto

package projector

import (
	rewards "github.com/epifi/gamma/api/rewards"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProjectionFieldMask int32

const (
	ProjectionFieldMask_PROJECTION_FIELD_MASK_UNSPECIFIED          ProjectionFieldMask = 0
	ProjectionFieldMask_PROJECTION_FIELD_MASK_ID                   ProjectionFieldMask = 1
	ProjectionFieldMask_PROJECTION_FIELD_MASK_ACTOR_ID             ProjectionFieldMask = 2
	ProjectionFieldMask_PROJECTION_FIELD_MASK_ACCOUNT_ID           ProjectionFieldMask = 3
	ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_TYPE          ProjectionFieldMask = 4
	ProjectionFieldMask_PROJECTION_FIELD_MASK_PROJECTED_OPTIONS    ProjectionFieldMask = 5
	ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS ProjectionFieldMask = 6
	ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_ID            ProjectionFieldMask = 7
	ProjectionFieldMask_PROJECTION_FIELD_MASK_ACTION_TYPE          ProjectionFieldMask = 8
	ProjectionFieldMask_PROJECTION_FIELD_MASK_ACTION_TIME          ProjectionFieldMask = 9
	ProjectionFieldMask_PROJECTION_FIELD_MASK_REF_ID               ProjectionFieldMask = 10
	ProjectionFieldMask_PROJECTION_FIELD_MASK_OFFER_TYPE           ProjectionFieldMask = 11
	ProjectionFieldMask_PROJECTION_FIELD_MASK_CREATED_AT           ProjectionFieldMask = 12
	ProjectionFieldMask_PROJECTION_FIELD_MASK_UPDATED_AT           ProjectionFieldMask = 13
	ProjectionFieldMask_PROJECTION_FIELD_MASK_DELETED_AT           ProjectionFieldMask = 14
	ProjectionFieldMask_PROJECTION_FIELD_MASK_OFFER_ID             ProjectionFieldMask = 15
)

// Enum value maps for ProjectionFieldMask.
var (
	ProjectionFieldMask_name = map[int32]string{
		0:  "PROJECTION_FIELD_MASK_UNSPECIFIED",
		1:  "PROJECTION_FIELD_MASK_ID",
		2:  "PROJECTION_FIELD_MASK_ACTOR_ID",
		3:  "PROJECTION_FIELD_MASK_ACCOUNT_ID",
		4:  "PROJECTION_FIELD_MASK_REWARD_TYPE",
		5:  "PROJECTION_FIELD_MASK_PROJECTED_OPTIONS",
		6:  "PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS",
		7:  "PROJECTION_FIELD_MASK_REWARD_ID",
		8:  "PROJECTION_FIELD_MASK_ACTION_TYPE",
		9:  "PROJECTION_FIELD_MASK_ACTION_TIME",
		10: "PROJECTION_FIELD_MASK_REF_ID",
		11: "PROJECTION_FIELD_MASK_OFFER_TYPE",
		12: "PROJECTION_FIELD_MASK_CREATED_AT",
		13: "PROJECTION_FIELD_MASK_UPDATED_AT",
		14: "PROJECTION_FIELD_MASK_DELETED_AT",
		15: "PROJECTION_FIELD_MASK_OFFER_ID",
	}
	ProjectionFieldMask_value = map[string]int32{
		"PROJECTION_FIELD_MASK_UNSPECIFIED":          0,
		"PROJECTION_FIELD_MASK_ID":                   1,
		"PROJECTION_FIELD_MASK_ACTOR_ID":             2,
		"PROJECTION_FIELD_MASK_ACCOUNT_ID":           3,
		"PROJECTION_FIELD_MASK_REWARD_TYPE":          4,
		"PROJECTION_FIELD_MASK_PROJECTED_OPTIONS":    5,
		"PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS": 6,
		"PROJECTION_FIELD_MASK_REWARD_ID":            7,
		"PROJECTION_FIELD_MASK_ACTION_TYPE":          8,
		"PROJECTION_FIELD_MASK_ACTION_TIME":          9,
		"PROJECTION_FIELD_MASK_REF_ID":               10,
		"PROJECTION_FIELD_MASK_OFFER_TYPE":           11,
		"PROJECTION_FIELD_MASK_CREATED_AT":           12,
		"PROJECTION_FIELD_MASK_UPDATED_AT":           13,
		"PROJECTION_FIELD_MASK_DELETED_AT":           14,
		"PROJECTION_FIELD_MASK_OFFER_ID":             15,
	}
)

func (x ProjectionFieldMask) Enum() *ProjectionFieldMask {
	p := new(ProjectionFieldMask)
	*p = x
	return p
}

func (x ProjectionFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProjectionFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_projector_projection_proto_enumTypes[0].Descriptor()
}

func (ProjectionFieldMask) Type() protoreflect.EnumType {
	return &file_api_rewards_projector_projection_proto_enumTypes[0]
}

func (x ProjectionFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProjectionFieldMask.Descriptor instead.
func (ProjectionFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_projector_projection_proto_rawDescGZIP(), []int{0}
}

// Projection of a reward that could be given to a user for an event
type Projection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique id of the projection
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// actor ID
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// further level of differentiating between different accounts of same actor (can be empty)
	AccountId string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// reward type of projection
	RewardType rewards.RewardType `protobuf:"varint,4,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	// Projections of options that are generated for the offer.
	ProjectedOptions *OptionsInfo `protobuf:"bytes,5,opt,name=projected_options,json=projectedOptions,proto3" json:"projected_options,omitempty"`
	// Actual contribution of the projection to generated reward
	RewardContributions *OptionsInfo `protobuf:"bytes,6,opt,name=reward_contributions,json=rewardContributions,proto3" json:"reward_contributions,omitempty"`
	// ID of the reward that's generated for this projection
	RewardId string `protobuf:"bytes,7,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
	// action type that generated the projection
	ActionType rewards.CollectedDataType `protobuf:"varint,8,opt,name=action_type,json=actionType,proto3,enum=rewards.CollectedDataType" json:"action_type,omitempty"`
	// time at which action was performed
	ActionTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=action_time,json=actionTime,proto3" json:"action_time,omitempty"`
	// reference ID of the action
	RefId string `protobuf:"bytes,10,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	// type of offer that generated the projection
	OfferType rewards.RewardOfferType `protobuf:"varint,11,opt,name=offer_type,json=offerType,proto3,enum=rewards.RewardOfferType" json:"offer_type,omitempty"`
	// timestamps
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	OfferId   string                 `protobuf:"bytes,15,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
}

func (x *Projection) Reset() {
	*x = Projection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_projection_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Projection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Projection) ProtoMessage() {}

func (x *Projection) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_projection_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Projection.ProtoReflect.Descriptor instead.
func (*Projection) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_projection_proto_rawDescGZIP(), []int{0}
}

func (x *Projection) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Projection) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *Projection) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Projection) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

func (x *Projection) GetProjectedOptions() *OptionsInfo {
	if x != nil {
		return x.ProjectedOptions
	}
	return nil
}

func (x *Projection) GetRewardContributions() *OptionsInfo {
	if x != nil {
		return x.RewardContributions
	}
	return nil
}

func (x *Projection) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

func (x *Projection) GetActionType() rewards.CollectedDataType {
	if x != nil {
		return x.ActionType
	}
	return rewards.CollectedDataType(0)
}

func (x *Projection) GetActionTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ActionTime
	}
	return nil
}

func (x *Projection) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

func (x *Projection) GetOfferType() rewards.RewardOfferType {
	if x != nil {
		return x.OfferType
	}
	return rewards.RewardOfferType(0)
}

func (x *Projection) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Projection) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Projection) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *Projection) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

// will generate a reward option for every configured option
type OptionsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardUnitsWithTypes []*RewardOption `protobuf:"bytes,1,rep,name=reward_units_with_types,json=rewardUnitsWithTypes,proto3" json:"reward_units_with_types,omitempty"`
}

func (x *OptionsInfo) Reset() {
	*x = OptionsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_projection_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OptionsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionsInfo) ProtoMessage() {}

func (x *OptionsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_projection_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionsInfo.ProtoReflect.Descriptor instead.
func (*OptionsInfo) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_projection_proto_rawDescGZIP(), []int{1}
}

func (x *OptionsInfo) GetRewardUnitsWithTypes() []*RewardOption {
	if x != nil {
		return x.RewardUnitsWithTypes
	}
	return nil
}

type RewardOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardType  rewards.RewardType `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	RewardUnits float32            `protobuf:"fixed32,2,opt,name=reward_units,json=rewardUnits,proto3" json:"reward_units,omitempty"`
	// Types that are assignable to RewardTypeSpecificMetadata:
	//
	//	*RewardOption_FiCoinsMetadata_
	RewardTypeSpecificMetadata isRewardOption_RewardTypeSpecificMetadata `protobuf_oneof:"RewardTypeSpecificMetadata"`
}

func (x *RewardOption) Reset() {
	*x = RewardOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_projection_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOption) ProtoMessage() {}

func (x *RewardOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_projection_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOption.ProtoReflect.Descriptor instead.
func (*RewardOption) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_projection_proto_rawDescGZIP(), []int{2}
}

func (x *RewardOption) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

func (x *RewardOption) GetRewardUnits() float32 {
	if x != nil {
		return x.RewardUnits
	}
	return 0
}

func (m *RewardOption) GetRewardTypeSpecificMetadata() isRewardOption_RewardTypeSpecificMetadata {
	if m != nil {
		return m.RewardTypeSpecificMetadata
	}
	return nil
}

func (x *RewardOption) GetFiCoinsMetadata() *RewardOption_FiCoinsMetadata {
	if x, ok := x.GetRewardTypeSpecificMetadata().(*RewardOption_FiCoinsMetadata_); ok {
		return x.FiCoinsMetadata
	}
	return nil
}

type isRewardOption_RewardTypeSpecificMetadata interface {
	isRewardOption_RewardTypeSpecificMetadata()
}

type RewardOption_FiCoinsMetadata_ struct {
	FiCoinsMetadata *RewardOption_FiCoinsMetadata `protobuf:"bytes,3,opt,name=fi_coins_metadata,json=fiCoinsMetadata,proto3,oneof"`
}

func (*RewardOption_FiCoinsMetadata_) isRewardOption_RewardTypeSpecificMetadata() {}

type RewardOption_FiCoinsMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// this flag will be true when the projection is given for reward type FI_COINS but given as FI_POINTS
	IsFiPoints bool `protobuf:"varint,1,opt,name=is_fi_points,json=isFiPoints,proto3" json:"is_fi_points,omitempty"`
}

func (x *RewardOption_FiCoinsMetadata) Reset() {
	*x = RewardOption_FiCoinsMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_projection_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOption_FiCoinsMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOption_FiCoinsMetadata) ProtoMessage() {}

func (x *RewardOption_FiCoinsMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_projection_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOption_FiCoinsMetadata.ProtoReflect.Descriptor instead.
func (*RewardOption_FiCoinsMetadata) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_projection_proto_rawDescGZIP(), []int{2, 0}
}

func (x *RewardOption_FiCoinsMetadata) GetIsFiPoints() bool {
	if x != nil {
		return x.IsFiPoints
	}
	return false
}

var File_api_rewards_projector_projection_proto protoreflect.FileDescriptor

var file_api_rewards_projector_projection_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcf, 0x05, 0x0a, 0x0a, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x11, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x49,
	0x0a, 0x14, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x13, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5d, 0x0a, 0x0b,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4e, 0x0a, 0x17, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x77, 0x69, 0x74, 0x68,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69,
	0x74, 0x73, 0x57, 0x69, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x91, 0x02, 0x0a, 0x0c,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x0b,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x55, 0x0a, 0x11, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e,
	0x73, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e,
	0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0f, 0x66, 0x69, 0x43,
	0x6f, 0x69, 0x6e, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x33, 0x0a, 0x0f,
	0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x20, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x46, 0x69, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x42, 0x1c, 0x0a, 0x1a, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x53,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2a,
	0xf9, 0x04, 0x0a, 0x13, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x52, 0x4f, 0x4a, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c,
	0x0a, 0x18, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e,
	0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x02,
	0x12, 0x24, 0x0a, 0x20, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x04, 0x12, 0x2b, 0x0a,
	0x27, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44,
	0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x05, 0x12, 0x2e, 0x0a, 0x2a, 0x50, 0x52,
	0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52,
	0x49, 0x42, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x06, 0x12, 0x23, 0x0a, 0x1f, 0x50, 0x52,
	0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x44, 0x10, 0x07, 0x12,
	0x25, 0x0a, 0x21, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x10, 0x08, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x09, 0x12, 0x20, 0x0a,
	0x1c, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x46, 0x5f, 0x49, 0x44, 0x10, 0x0a, 0x12,
	0x24, 0x0a, 0x20, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x10, 0x0b, 0x12, 0x24, 0x0a, 0x20, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0c, 0x12, 0x24, 0x0a, 0x20, 0x50,
	0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10,
	0x0d, 0x12, 0x24, 0x0a, 0x20, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54,
	0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0e, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x52, 0x4f, 0x4a, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x0f, 0x42, 0x5c, 0x0a, 0x2c, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5a, 0x2c, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_rewards_projector_projection_proto_rawDescOnce sync.Once
	file_api_rewards_projector_projection_proto_rawDescData = file_api_rewards_projector_projection_proto_rawDesc
)

func file_api_rewards_projector_projection_proto_rawDescGZIP() []byte {
	file_api_rewards_projector_projection_proto_rawDescOnce.Do(func() {
		file_api_rewards_projector_projection_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_rewards_projector_projection_proto_rawDescData)
	})
	return file_api_rewards_projector_projection_proto_rawDescData
}

var file_api_rewards_projector_projection_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_rewards_projector_projection_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_rewards_projector_projection_proto_goTypes = []interface{}{
	(ProjectionFieldMask)(0),             // 0: projector.ProjectionFieldMask
	(*Projection)(nil),                   // 1: projector.Projection
	(*OptionsInfo)(nil),                  // 2: projector.OptionsInfo
	(*RewardOption)(nil),                 // 3: projector.RewardOption
	(*RewardOption_FiCoinsMetadata)(nil), // 4: projector.RewardOption.FiCoinsMetadata
	(rewards.RewardType)(0),              // 5: rewards.RewardType
	(rewards.CollectedDataType)(0),       // 6: rewards.CollectedDataType
	(*timestamppb.Timestamp)(nil),        // 7: google.protobuf.Timestamp
	(rewards.RewardOfferType)(0),         // 8: rewards.RewardOfferType
}
var file_api_rewards_projector_projection_proto_depIdxs = []int32{
	5,  // 0: projector.Projection.reward_type:type_name -> rewards.RewardType
	2,  // 1: projector.Projection.projected_options:type_name -> projector.OptionsInfo
	2,  // 2: projector.Projection.reward_contributions:type_name -> projector.OptionsInfo
	6,  // 3: projector.Projection.action_type:type_name -> rewards.CollectedDataType
	7,  // 4: projector.Projection.action_time:type_name -> google.protobuf.Timestamp
	8,  // 5: projector.Projection.offer_type:type_name -> rewards.RewardOfferType
	7,  // 6: projector.Projection.created_at:type_name -> google.protobuf.Timestamp
	7,  // 7: projector.Projection.updated_at:type_name -> google.protobuf.Timestamp
	7,  // 8: projector.Projection.deleted_at:type_name -> google.protobuf.Timestamp
	3,  // 9: projector.OptionsInfo.reward_units_with_types:type_name -> projector.RewardOption
	5,  // 10: projector.RewardOption.reward_type:type_name -> rewards.RewardType
	4,  // 11: projector.RewardOption.fi_coins_metadata:type_name -> projector.RewardOption.FiCoinsMetadata
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_api_rewards_projector_projection_proto_init() }
func file_api_rewards_projector_projection_proto_init() {
	if File_api_rewards_projector_projection_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_rewards_projector_projection_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Projection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_projector_projection_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OptionsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_projector_projection_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_projector_projection_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOption_FiCoinsMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_rewards_projector_projection_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*RewardOption_FiCoinsMetadata_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_rewards_projector_projection_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_rewards_projector_projection_proto_goTypes,
		DependencyIndexes: file_api_rewards_projector_projection_proto_depIdxs,
		EnumInfos:         file_api_rewards_projector_projection_proto_enumTypes,
		MessageInfos:      file_api_rewards_projector_projection_proto_msgTypes,
	}.Build()
	File_api_rewards_projector_projection_proto = out.File
	file_api_rewards_projector_projection_proto_rawDesc = nil
	file_api_rewards_projector_projection_proto_goTypes = nil
	file_api_rewards_projector_projection_proto_depIdxs = nil
}
