syntax = "proto3";

package nps;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/nps";


message NpsScheme {

  string id = 1;
  string external_id = 2; // external_id is the unique identifier for the pension fund manager
  string nps_pfm_id = 3;
  string scheme_name = 4;
  string logo = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
}
