syntax = "proto3";

package frontend.investment.aggregator;

import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/widget_themes.proto";
import "api/frontend/deeplink/deeplink.proto";

option go_package = "github.com/epifi/gamma/api/frontend/investment/aggregator";
option java_package = "com.github.epifi.gamma.api.frontend.investment.aggregator";

/*
  Figma: https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=7920-16589&mode=dev
  All the available investment instruments will be shown with the summary of the investment.
*/

message CollectiveInstrumentSummaryComponent {
  repeated InstrumentSummaryComponent instrument_summary_component = 1;
  // max number of rows shown initially in the summary component.
  // number of columns is hardcoded to 3 in client.
  int32 visible_rows = 2;
  // if any instrument is not shown on basis of `visible_rows`
  // show `see_all`
  // on clicking, show all instrument and change `see_all` to `collapse`
  api.typesv2.ui.IconTextComponent see_all = 3;

  // on clicking, show only `visible_rows` and change `collapse` to `see_all`
  api.typesv2.ui.IconTextComponent collapse = 4;

  // background color for component
  string bg_color = 5;
  // separator color
  string separator_color = 6;
  // shadow for component
  repeated api.typesv2.ui.Shadow shadows = 7;
}

message InstrumentSummaryComponent {

  repeated InstrumentSummaryCard instrument_summary_cards = 1;
  // interval between changing the cards vertically
  int32 card_change_interval_seconds = 4;
  // whether to infinitely change cards or stop at last card.
  bool repeat_card_change = 5;

  message InstrumentSummaryCard {
    oneof InstrumentSummaryType {
      api.typesv2.common.VisualElement visual_element_component = 2;
      InstrumentSummaryBaseCard instrument_summary_base_card = 3;
    }
  }

  message InstrumentSummaryBaseCard {
    // Eg.
    // first row is Instrument Image
    // second row is instrument name
    // third row is amount
    repeated api.typesv2.ui.IconTextComponent rows = 6;
    // If individual rows doesn't have, this deeplink will be used for tap action.
    frontend.deeplink.Deeplink deeplink = 7;
  }
}
