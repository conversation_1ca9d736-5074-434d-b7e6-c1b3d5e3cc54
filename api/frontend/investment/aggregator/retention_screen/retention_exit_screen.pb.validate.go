// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/investment/aggregator/retention_screen/retention_exit_screen.proto

package retention_screen

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RetentionExitScreen with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RetentionExitScreen) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetentionExitScreen with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RetentionExitScreenMultiError, or nil if none found.
func (m *RetentionExitScreen) ValidateAll() error {
	return m.validate(true)
}

func (m *RetentionExitScreen) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetExitVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RetentionExitScreenValidationError{
					field:  "ExitVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RetentionExitScreenValidationError{
					field:  "ExitVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExitVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RetentionExitScreenValidationError{
				field:  "ExitVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExitTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RetentionExitScreenValidationError{
					field:  "ExitTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RetentionExitScreenValidationError{
					field:  "ExitTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExitTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RetentionExitScreenValidationError{
				field:  "ExitTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExitSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RetentionExitScreenValidationError{
					field:  "ExitSubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RetentionExitScreenValidationError{
					field:  "ExitSubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExitSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RetentionExitScreenValidationError{
				field:  "ExitSubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RetentionExitScreenValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RetentionExitScreenValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RetentionExitScreenValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RetentionExitScreenMultiError(errors)
	}

	return nil
}

// RetentionExitScreenMultiError is an error wrapping multiple validation
// errors returned by RetentionExitScreen.ValidateAll() if the designated
// constraints aren't met.
type RetentionExitScreenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetentionExitScreenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetentionExitScreenMultiError) AllErrors() []error { return m }

// RetentionExitScreenValidationError is the validation error returned by
// RetentionExitScreen.Validate if the designated constraints aren't met.
type RetentionExitScreenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetentionExitScreenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetentionExitScreenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetentionExitScreenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetentionExitScreenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetentionExitScreenValidationError) ErrorName() string {
	return "RetentionExitScreenValidationError"
}

// Error satisfies the builtin error interface
func (e RetentionExitScreenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetentionExitScreen.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetentionExitScreenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetentionExitScreenValidationError{}
