// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/rewards/exchanger_order.proto

package rewards

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Exchanger Order Status corresponding to backend order status. Used in frontend
type ExchangerOrderStatus int32

const (
	ExchangerOrderStatus_UNSPECIFIED_ORDER_STATUS   ExchangerOrderStatus = 0
	ExchangerOrderStatus_UNCLAIMED                  ExchangerOrderStatus = 1
	ExchangerOrderStatus_USER_INTERVENTION_REQUIRED ExchangerOrderStatus = 2
	ExchangerOrderStatus_IN_PROGRESS                ExchangerOrderStatus = 3
	ExchangerOrderStatus_FULFILLED                  ExchangerOrderStatus = 4
	ExchangerOrderStatus_FAILED                     ExchangerOrderStatus = 5
)

// Enum value maps for ExchangerOrderStatus.
var (
	ExchangerOrderStatus_name = map[int32]string{
		0: "UNSPECIFIED_ORDER_STATUS",
		1: "UNCLAIMED",
		2: "USER_INTERVENTION_REQUIRED",
		3: "IN_PROGRESS",
		4: "FULFILLED",
		5: "FAILED",
	}
	ExchangerOrderStatus_value = map[string]int32{
		"UNSPECIFIED_ORDER_STATUS":   0,
		"UNCLAIMED":                  1,
		"USER_INTERVENTION_REQUIRED": 2,
		"IN_PROGRESS":                3,
		"FULFILLED":                  4,
		"FAILED":                     5,
	}
)

func (x ExchangerOrderStatus) Enum() *ExchangerOrderStatus {
	p := new(ExchangerOrderStatus)
	*p = x
	return p
}

func (x ExchangerOrderStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExchangerOrderStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_rewards_exchanger_order_proto_enumTypes[0].Descriptor()
}

func (ExchangerOrderStatus) Type() protoreflect.EnumType {
	return &file_api_frontend_rewards_exchanger_order_proto_enumTypes[0]
}

func (x ExchangerOrderStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExchangerOrderStatus.Descriptor instead.
func (ExchangerOrderStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_rewards_exchanger_order_proto_rawDescGZIP(), []int{0}
}

// ExchangerOfferRedemptionCurrency denotes currency that can be used for redeeming an ExchangerOffer.
type ExchangerOfferRedemptionCurrency int32

const (
	ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_UNSPECIFIED ExchangerOfferRedemptionCurrency = 0
	ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS    ExchangerOfferRedemptionCurrency = 1
)

// Enum value maps for ExchangerOfferRedemptionCurrency.
var (
	ExchangerOfferRedemptionCurrency_name = map[int32]string{
		0: "EXCHANGER_OFFER_REDEMPTION_CURRENCY_UNSPECIFIED",
		1: "EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS",
	}
	ExchangerOfferRedemptionCurrency_value = map[string]int32{
		"EXCHANGER_OFFER_REDEMPTION_CURRENCY_UNSPECIFIED": 0,
		"EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS":    1,
	}
)

func (x ExchangerOfferRedemptionCurrency) Enum() *ExchangerOfferRedemptionCurrency {
	p := new(ExchangerOfferRedemptionCurrency)
	*p = x
	return p
}

func (x ExchangerOfferRedemptionCurrency) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExchangerOfferRedemptionCurrency) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_rewards_exchanger_order_proto_enumTypes[1].Descriptor()
}

func (ExchangerOfferRedemptionCurrency) Type() protoreflect.EnumType {
	return &file_api_frontend_rewards_exchanger_order_proto_enumTypes[1]
}

func (x ExchangerOfferRedemptionCurrency) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExchangerOfferRedemptionCurrency.Descriptor instead.
func (ExchangerOfferRedemptionCurrency) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_rewards_exchanger_order_proto_rawDescGZIP(), []int{1}
}

// fields to describe the exchanger-order-option
type ExchangerOrderOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique id of the option under the order
	Id             string                        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	DisplayDetails *ExchangerOrderOption_Display `protobuf:"bytes,2,opt,name=display_details,json=displayDetails,proto3" json:"display_details,omitempty"`
}

func (x *ExchangerOrderOption) Reset() {
	*x = ExchangerOrderOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_exchanger_order_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOrderOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOrderOption) ProtoMessage() {}

func (x *ExchangerOrderOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_exchanger_order_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOrderOption.ProtoReflect.Descriptor instead.
func (*ExchangerOrderOption) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_exchanger_order_proto_rawDescGZIP(), []int{0}
}

func (x *ExchangerOrderOption) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExchangerOrderOption) GetDisplayDetails() *ExchangerOrderOption_Display {
	if x != nil {
		return x.DisplayDetails
	}
	return nil
}

type ExchangerOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the order, i.e. attempted redemption
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// FE maintained status of the order
	Status ExchangerOrderStatus `protobuf:"varint,2,opt,name=status,proto3,enum=frontend.rewards.ExchangerOrderStatus" json:"status,omitempty"`
	// all the available options to choose from
	Options []*ExchangerOrderOption `protobuf:"bytes,3,rep,name=options,proto3" json:"options,omitempty"`
	// chosen option
	// this won't be present if the option is yet to be chosen
	// use-case: To show this instead of the "options" if its already claimed
	ChosenOption *ExchangerOrderOption `protobuf:"bytes,4,opt,name=chosen_option,json=chosenOption,proto3" json:"chosen_option,omitempty"`
	// timer for choosing the default option
	DefaultDecideTimeInSeconds uint32 `protobuf:"varint,5,opt,name=defaultDecideTimeInSeconds,proto3" json:"defaultDecideTimeInSeconds,omitempty"`
	// created_at/added_on date of the order
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// external id for display purposes
	ExternalId string `protobuf:"bytes,7,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// currency through which given ExchangerOffer can be redeemed like FI_COINS.
	RedemptionCurrency ExchangerOfferRedemptionCurrency `protobuf:"varint,8,opt,name=redemption_currency,json=redemptionCurrency,proto3,enum=frontend.rewards.ExchangerOfferRedemptionCurrency" json:"redemption_currency,omitempty"`
	// price of redeeming the ExchangerOffer.
	// deprecated in favour of redemption_value
	//
	// Deprecated: Marked as deprecated in api/frontend/rewards/exchanger_order.proto.
	RedemptionPrice float32 `protobuf:"fixed32,9,opt,name=redemption_price,json=redemptionPrice,proto3" json:"redemption_price,omitempty"`
	// list of tags applied to the offer that was redeemed
	Tags []*ui.IconTextComponent `protobuf:"bytes,10,rep,name=tags,proto3" json:"tags,omitempty"`
	// used to show the sections on offer details page, multiple sections can be used to show data related to different topics, eg. delivery details card, how to redeem section etc.
	// eg.  delivery details card can contain details about delivery address, delivery partner etc.
	RedeemedOfferDetailsSections []*OfferDetailsSection `protobuf:"bytes,11,rep,name=redeemed_offer_details_sections,json=redeemedOfferDetailsSections,proto3" json:"redeemed_offer_details_sections,omitempty"`
	// tags displayed on the exchanger order tile e.g "CREDIT CARD REWARD"
	// design : https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?node-id=14751-85442&t=vRsbSIR6i2QmpzyV-0
	DisplayTags []*common.Text `protobuf:"bytes,12,rep,name=display_tags,json=displayTags,proto3" json:"display_tags,omitempty"`
	// flag to decide whether or not to display redemption price,
	// if set to true then redemption price shouldn't be displayed on exchanger offer tile.
	ShouldNotDisplayRedemptionPrice bool `protobuf:"varint,13,opt,name=should_not_display_redemption_price,json=shouldNotDisplayRedemptionPrice,proto3" json:"should_not_display_redemption_price,omitempty"`
	// description of current status of exchanger order, like "This will be added to your coin balance on 12 May 2021"
	// can be empty
	StatusDesc string `protobuf:"bytes,14,opt,name=status_desc,json=statusDesc,proto3" json:"status_desc,omitempty"`
	// redemption value of the exchanger order
	RedemptionValue *ui.VerticalKeyValuePair `protobuf:"bytes,15,opt,name=redemption_value,json=redemptionValue,proto3" json:"redemption_value,omitempty"`
}

func (x *ExchangerOrder) Reset() {
	*x = ExchangerOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_exchanger_order_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOrder) ProtoMessage() {}

func (x *ExchangerOrder) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_exchanger_order_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOrder.ProtoReflect.Descriptor instead.
func (*ExchangerOrder) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_exchanger_order_proto_rawDescGZIP(), []int{1}
}

func (x *ExchangerOrder) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExchangerOrder) GetStatus() ExchangerOrderStatus {
	if x != nil {
		return x.Status
	}
	return ExchangerOrderStatus_UNSPECIFIED_ORDER_STATUS
}

func (x *ExchangerOrder) GetOptions() []*ExchangerOrderOption {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *ExchangerOrder) GetChosenOption() *ExchangerOrderOption {
	if x != nil {
		return x.ChosenOption
	}
	return nil
}

func (x *ExchangerOrder) GetDefaultDecideTimeInSeconds() uint32 {
	if x != nil {
		return x.DefaultDecideTimeInSeconds
	}
	return 0
}

func (x *ExchangerOrder) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ExchangerOrder) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *ExchangerOrder) GetRedemptionCurrency() ExchangerOfferRedemptionCurrency {
	if x != nil {
		return x.RedemptionCurrency
	}
	return ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/frontend/rewards/exchanger_order.proto.
func (x *ExchangerOrder) GetRedemptionPrice() float32 {
	if x != nil {
		return x.RedemptionPrice
	}
	return 0
}

func (x *ExchangerOrder) GetTags() []*ui.IconTextComponent {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ExchangerOrder) GetRedeemedOfferDetailsSections() []*OfferDetailsSection {
	if x != nil {
		return x.RedeemedOfferDetailsSections
	}
	return nil
}

func (x *ExchangerOrder) GetDisplayTags() []*common.Text {
	if x != nil {
		return x.DisplayTags
	}
	return nil
}

func (x *ExchangerOrder) GetShouldNotDisplayRedemptionPrice() bool {
	if x != nil {
		return x.ShouldNotDisplayRedemptionPrice
	}
	return false
}

func (x *ExchangerOrder) GetStatusDesc() string {
	if x != nil {
		return x.StatusDesc
	}
	return ""
}

func (x *ExchangerOrder) GetRedemptionValue() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.RedemptionValue
	}
	return nil
}

type ExchangerOrderOption_Display struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title like "Rs 8"
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// subtitle like "Cashback"
	Subtitle string `protobuf:"bytes,2,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	ImageUrl string `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	// desc visible on reward details page, e.g. "Cashback won by trading 500 Fi-Coins"
	Desc              string                                          `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	AdditionalDetails *ExchangerOrderOption_Display_AdditionalDetails `protobuf:"bytes,5,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
	DynamicFields     []*ExchangerOrderOption_Display_DynamicFields   `protobuf:"bytes,6,rep,name=dynamic_fields,json=dynamicFields,proto3" json:"dynamic_fields,omitempty"`
}

func (x *ExchangerOrderOption_Display) Reset() {
	*x = ExchangerOrderOption_Display{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_exchanger_order_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOrderOption_Display) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOrderOption_Display) ProtoMessage() {}

func (x *ExchangerOrderOption_Display) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_exchanger_order_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOrderOption_Display.ProtoReflect.Descriptor instead.
func (*ExchangerOrderOption_Display) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_exchanger_order_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ExchangerOrderOption_Display) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ExchangerOrderOption_Display) GetSubtitle() string {
	if x != nil {
		return x.Subtitle
	}
	return ""
}

func (x *ExchangerOrderOption_Display) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *ExchangerOrderOption_Display) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ExchangerOrderOption_Display) GetAdditionalDetails() *ExchangerOrderOption_Display_AdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

func (x *ExchangerOrderOption_Display) GetDynamicFields() []*ExchangerOrderOption_Display_DynamicFields {
	if x != nil {
		return x.DynamicFields
	}
	return nil
}

// additional details required to display information (like "TnCs", "Next steps", etc)
type ExchangerOrderOption_Display_AdditionalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BannerLogoUrl  string `protobuf:"bytes,1,opt,name=banner_logo_url,json=bannerLogoUrl,proto3" json:"banner_logo_url,omitempty"`
	BannerImageUrl string `protobuf:"bytes,2,opt,name=banner_image_url,json=bannerImageUrl,proto3" json:"banner_image_url,omitempty"`
	// for e.g. "Sony Playstation 5 Console"
	BannerTitle string `protobuf:"bytes,3,opt,name=banner_title,json=bannerTitle,proto3" json:"banner_title,omitempty"`
	// for e.g. "1 Playstation 5 delivered at your doorstep free of charge. Game on!"
	Desc      string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	NextSteps []string `protobuf:"bytes,5,rep,name=next_steps,json=nextSteps,proto3" json:"next_steps,omitempty"`
	// Deprecated: Marked as deprecated in api/frontend/rewards/exchanger_order.proto.
	Tncs []string `protobuf:"bytes,6,rep,name=tncs,proto3" json:"tncs,omitempty"`
	// text to show on order-card under "My Orders" page if order is in USER_INTERVENTION_REQUIRED, REWARD_FULFILLMENT_INITIATED state
	IntermediateStateText string `protobuf:"bytes,7,opt,name=intermediate_state_text,json=intermediateStateText,proto3" json:"intermediate_state_text,omitempty"`
	BannerBgColor         string `protobuf:"bytes,8,opt,name=banner_bg_color,json=bannerBgColor,proto3" json:"banner_bg_color,omitempty"`
	// tncs with HTML support
	TncsV2 []*common.Text `protobuf:"bytes,9,rep,name=tncs_v2,json=tncsV2,proto3" json:"tncs_v2,omitempty"`
}

func (x *ExchangerOrderOption_Display_AdditionalDetails) Reset() {
	*x = ExchangerOrderOption_Display_AdditionalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_exchanger_order_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOrderOption_Display_AdditionalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOrderOption_Display_AdditionalDetails) ProtoMessage() {}

func (x *ExchangerOrderOption_Display_AdditionalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_exchanger_order_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOrderOption_Display_AdditionalDetails.ProtoReflect.Descriptor instead.
func (*ExchangerOrderOption_Display_AdditionalDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_exchanger_order_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *ExchangerOrderOption_Display_AdditionalDetails) GetBannerLogoUrl() string {
	if x != nil {
		return x.BannerLogoUrl
	}
	return ""
}

func (x *ExchangerOrderOption_Display_AdditionalDetails) GetBannerImageUrl() string {
	if x != nil {
		return x.BannerImageUrl
	}
	return ""
}

func (x *ExchangerOrderOption_Display_AdditionalDetails) GetBannerTitle() string {
	if x != nil {
		return x.BannerTitle
	}
	return ""
}

func (x *ExchangerOrderOption_Display_AdditionalDetails) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ExchangerOrderOption_Display_AdditionalDetails) GetNextSteps() []string {
	if x != nil {
		return x.NextSteps
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/rewards/exchanger_order.proto.
func (x *ExchangerOrderOption_Display_AdditionalDetails) GetTncs() []string {
	if x != nil {
		return x.Tncs
	}
	return nil
}

func (x *ExchangerOrderOption_Display_AdditionalDetails) GetIntermediateStateText() string {
	if x != nil {
		return x.IntermediateStateText
	}
	return ""
}

func (x *ExchangerOrderOption_Display_AdditionalDetails) GetBannerBgColor() string {
	if x != nil {
		return x.BannerBgColor
	}
	return ""
}

func (x *ExchangerOrderOption_Display_AdditionalDetails) GetTncsV2() []*common.Text {
	if x != nil {
		return x.TncsV2
	}
	return nil
}

// DynamicFields can be used to send additional information that may be required for this particular order
// (like shipping address for a physical merchandise order)
// It helps in showing a new field on the APP for already supported offer types
// without requiring any client side change.
type ExchangerOrderOption_Display_DynamicFields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value      string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	IsCopyable bool   `protobuf:"varint,3,opt,name=is_copyable,json=isCopyable,proto3" json:"is_copyable,omitempty"`
}

func (x *ExchangerOrderOption_Display_DynamicFields) Reset() {
	*x = ExchangerOrderOption_Display_DynamicFields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_exchanger_order_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOrderOption_Display_DynamicFields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOrderOption_Display_DynamicFields) ProtoMessage() {}

func (x *ExchangerOrderOption_Display_DynamicFields) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_exchanger_order_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOrderOption_Display_DynamicFields.ProtoReflect.Descriptor instead.
func (*ExchangerOrderOption_Display_DynamicFields) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_exchanger_order_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *ExchangerOrderOption_Display_DynamicFields) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExchangerOrderOption_Display_DynamicFields) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *ExchangerOrderOption_Display_DynamicFields) GetIsCopyable() bool {
	if x != nil {
		return x.IsCopyable
	}
	return false
}

var File_api_frontend_rewards_exchanger_order_proto protoreflect.FileDescriptor

var file_api_frontend_rewards_exchanger_order_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x1a, 0x2d,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63,
	0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x76, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c,
	0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x89, 0x07, 0x0a, 0x14, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x57,
	0x0a, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x87, 0x06, 0x0a, 0x07, 0x44, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x75, 0x62,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x75, 0x62,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55,
	0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x6f, 0x0a, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x40, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x63, 0x0a, 0x0e, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e,
	0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x52, 0x0d, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x1a, 0xe6, 0x02, 0x0a,
	0x11, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x67,
	0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x6e,
	0x65, 0x78, 0x74, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x6e, 0x65, 0x78, 0x74, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x16, 0x0a, 0x04, 0x74, 0x6e,
	0x63, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x74, 0x6e,
	0x63, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x15, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x42, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x31, 0x0a, 0x07, 0x74, 0x6e, 0x63, 0x73, 0x5f, 0x76, 0x32, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x06, 0x74,
	0x6e, 0x63, 0x73, 0x56, 0x32, 0x1a, 0x5a, 0x0a, 0x0d, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x70, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x43, 0x6f, 0x70, 0x79, 0x61, 0x62, 0x6c,
	0x65, 0x22, 0xc1, 0x07, 0x0a, 0x0e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4b, 0x0a, 0x0d, 0x63, 0x68, 0x6f, 0x73, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x63, 0x68, 0x6f, 0x73, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x1a, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x65,
	0x63, 0x69, 0x64, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1a, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x44, 0x65, 0x63, 0x69, 0x64, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x53, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12,
	0x63, 0x0a, 0x13, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65,
	0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x52, 0x12, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x2d, 0x0a, 0x10, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x6c, 0x0a, 0x1f, 0x72, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x1c, 0x72, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x54, 0x61, 0x67, 0x73, 0x12, 0x4c, 0x0a, 0x23, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f,
	0x6e, 0x6f, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x72, 0x65, 0x64, 0x65,
	0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x1f, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x4e, 0x6f, 0x74, 0x44, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x44, 0x65, 0x73, 0x63, 0x12, 0x4f, 0x0a, 0x10, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x50, 0x61, 0x69, 0x72, 0x52, 0x0f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0x8f, 0x01, 0x0a, 0x14, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c,
	0x0a, 0x18, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09,
	0x55, 0x4e, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x49,
	0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09,
	0x46, 0x55, 0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x2a, 0x89, 0x01, 0x0a, 0x20, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x33, 0x0a, 0x2f,
	0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f,
	0x52, 0x45, 0x44, 0x45, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45,
	0x4e, 0x43, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x30, 0x0a, 0x2c, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x44, 0x45, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x46, 0x49, 0x5f, 0x43, 0x4f, 0x49, 0x4e,
	0x53, 0x10, 0x01, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_rewards_exchanger_order_proto_rawDescOnce sync.Once
	file_api_frontend_rewards_exchanger_order_proto_rawDescData = file_api_frontend_rewards_exchanger_order_proto_rawDesc
)

func file_api_frontend_rewards_exchanger_order_proto_rawDescGZIP() []byte {
	file_api_frontend_rewards_exchanger_order_proto_rawDescOnce.Do(func() {
		file_api_frontend_rewards_exchanger_order_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_rewards_exchanger_order_proto_rawDescData)
	})
	return file_api_frontend_rewards_exchanger_order_proto_rawDescData
}

var file_api_frontend_rewards_exchanger_order_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_frontend_rewards_exchanger_order_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_frontend_rewards_exchanger_order_proto_goTypes = []interface{}{
	(ExchangerOrderStatus)(0),                              // 0: frontend.rewards.ExchangerOrderStatus
	(ExchangerOfferRedemptionCurrency)(0),                  // 1: frontend.rewards.ExchangerOfferRedemptionCurrency
	(*ExchangerOrderOption)(nil),                           // 2: frontend.rewards.ExchangerOrderOption
	(*ExchangerOrder)(nil),                                 // 3: frontend.rewards.ExchangerOrder
	(*ExchangerOrderOption_Display)(nil),                   // 4: frontend.rewards.ExchangerOrderOption.Display
	(*ExchangerOrderOption_Display_AdditionalDetails)(nil), // 5: frontend.rewards.ExchangerOrderOption.Display.AdditionalDetails
	(*ExchangerOrderOption_Display_DynamicFields)(nil),     // 6: frontend.rewards.ExchangerOrderOption.Display.DynamicFields
	(*timestamppb.Timestamp)(nil),                          // 7: google.protobuf.Timestamp
	(*ui.IconTextComponent)(nil),                           // 8: api.typesv2.ui.IconTextComponent
	(*OfferDetailsSection)(nil),                            // 9: frontend.rewards.OfferDetailsSection
	(*common.Text)(nil),                                    // 10: api.typesv2.common.Text
	(*ui.VerticalKeyValuePair)(nil),                        // 11: api.typesv2.ui.VerticalKeyValuePair
}
var file_api_frontend_rewards_exchanger_order_proto_depIdxs = []int32{
	4,  // 0: frontend.rewards.ExchangerOrderOption.display_details:type_name -> frontend.rewards.ExchangerOrderOption.Display
	0,  // 1: frontend.rewards.ExchangerOrder.status:type_name -> frontend.rewards.ExchangerOrderStatus
	2,  // 2: frontend.rewards.ExchangerOrder.options:type_name -> frontend.rewards.ExchangerOrderOption
	2,  // 3: frontend.rewards.ExchangerOrder.chosen_option:type_name -> frontend.rewards.ExchangerOrderOption
	7,  // 4: frontend.rewards.ExchangerOrder.created_at:type_name -> google.protobuf.Timestamp
	1,  // 5: frontend.rewards.ExchangerOrder.redemption_currency:type_name -> frontend.rewards.ExchangerOfferRedemptionCurrency
	8,  // 6: frontend.rewards.ExchangerOrder.tags:type_name -> api.typesv2.ui.IconTextComponent
	9,  // 7: frontend.rewards.ExchangerOrder.redeemed_offer_details_sections:type_name -> frontend.rewards.OfferDetailsSection
	10, // 8: frontend.rewards.ExchangerOrder.display_tags:type_name -> api.typesv2.common.Text
	11, // 9: frontend.rewards.ExchangerOrder.redemption_value:type_name -> api.typesv2.ui.VerticalKeyValuePair
	5,  // 10: frontend.rewards.ExchangerOrderOption.Display.additional_details:type_name -> frontend.rewards.ExchangerOrderOption.Display.AdditionalDetails
	6,  // 11: frontend.rewards.ExchangerOrderOption.Display.dynamic_fields:type_name -> frontend.rewards.ExchangerOrderOption.Display.DynamicFields
	10, // 12: frontend.rewards.ExchangerOrderOption.Display.AdditionalDetails.tncs_v2:type_name -> api.typesv2.common.Text
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_api_frontend_rewards_exchanger_order_proto_init() }
func file_api_frontend_rewards_exchanger_order_proto_init() {
	if File_api_frontend_rewards_exchanger_order_proto != nil {
		return
	}
	file_api_frontend_rewards_display_components_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_rewards_exchanger_order_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOrderOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_exchanger_order_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_exchanger_order_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOrderOption_Display); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_exchanger_order_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOrderOption_Display_AdditionalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_exchanger_order_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOrderOption_Display_DynamicFields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_rewards_exchanger_order_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_rewards_exchanger_order_proto_goTypes,
		DependencyIndexes: file_api_frontend_rewards_exchanger_order_proto_depIdxs,
		EnumInfos:         file_api_frontend_rewards_exchanger_order_proto_enumTypes,
		MessageInfos:      file_api_frontend_rewards_exchanger_order_proto_msgTypes,
	}.Build()
	File_api_frontend_rewards_exchanger_order_proto = out.File
	file_api_frontend_rewards_exchanger_order_proto_rawDesc = nil
	file_api_frontend_rewards_exchanger_order_proto_goTypes = nil
	file_api_frontend_rewards_exchanger_order_proto_depIdxs = nil
}
