// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/rewards/exchanger_order.proto

package rewards

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ExchangerOrderOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExchangerOrderOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExchangerOrderOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExchangerOrderOptionMultiError, or nil if none found.
func (m *ExchangerOrderOption) ValidateAll() error {
	return m.validate(true)
}

func (m *ExchangerOrderOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetDisplayDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExchangerOrderOptionValidationError{
					field:  "DisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExchangerOrderOptionValidationError{
					field:  "DisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExchangerOrderOptionValidationError{
				field:  "DisplayDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExchangerOrderOptionMultiError(errors)
	}

	return nil
}

// ExchangerOrderOptionMultiError is an error wrapping multiple validation
// errors returned by ExchangerOrderOption.ValidateAll() if the designated
// constraints aren't met.
type ExchangerOrderOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExchangerOrderOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExchangerOrderOptionMultiError) AllErrors() []error { return m }

// ExchangerOrderOptionValidationError is the validation error returned by
// ExchangerOrderOption.Validate if the designated constraints aren't met.
type ExchangerOrderOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExchangerOrderOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExchangerOrderOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExchangerOrderOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExchangerOrderOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExchangerOrderOptionValidationError) ErrorName() string {
	return "ExchangerOrderOptionValidationError"
}

// Error satisfies the builtin error interface
func (e ExchangerOrderOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExchangerOrderOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExchangerOrderOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExchangerOrderOptionValidationError{}

// Validate checks the field values on ExchangerOrder with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExchangerOrder) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExchangerOrder with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExchangerOrderMultiError,
// or nil if none found.
func (m *ExchangerOrder) ValidateAll() error {
	return m.validate(true)
}

func (m *ExchangerOrder) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Status

	for idx, item := range m.GetOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExchangerOrderValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExchangerOrderValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExchangerOrderValidationError{
					field:  fmt.Sprintf("Options[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetChosenOption()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExchangerOrderValidationError{
					field:  "ChosenOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExchangerOrderValidationError{
					field:  "ChosenOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChosenOption()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExchangerOrderValidationError{
				field:  "ChosenOption",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DefaultDecideTimeInSeconds

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExchangerOrderValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExchangerOrderValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExchangerOrderValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExternalId

	// no validation rules for RedemptionCurrency

	// no validation rules for RedemptionPrice

	for idx, item := range m.GetTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExchangerOrderValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExchangerOrderValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExchangerOrderValidationError{
					field:  fmt.Sprintf("Tags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRedeemedOfferDetailsSections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExchangerOrderValidationError{
						field:  fmt.Sprintf("RedeemedOfferDetailsSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExchangerOrderValidationError{
						field:  fmt.Sprintf("RedeemedOfferDetailsSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExchangerOrderValidationError{
					field:  fmt.Sprintf("RedeemedOfferDetailsSections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDisplayTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExchangerOrderValidationError{
						field:  fmt.Sprintf("DisplayTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExchangerOrderValidationError{
						field:  fmt.Sprintf("DisplayTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExchangerOrderValidationError{
					field:  fmt.Sprintf("DisplayTags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ShouldNotDisplayRedemptionPrice

	// no validation rules for StatusDesc

	if all {
		switch v := interface{}(m.GetRedemptionValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExchangerOrderValidationError{
					field:  "RedemptionValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExchangerOrderValidationError{
					field:  "RedemptionValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedemptionValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExchangerOrderValidationError{
				field:  "RedemptionValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExchangerOrderMultiError(errors)
	}

	return nil
}

// ExchangerOrderMultiError is an error wrapping multiple validation errors
// returned by ExchangerOrder.ValidateAll() if the designated constraints
// aren't met.
type ExchangerOrderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExchangerOrderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExchangerOrderMultiError) AllErrors() []error { return m }

// ExchangerOrderValidationError is the validation error returned by
// ExchangerOrder.Validate if the designated constraints aren't met.
type ExchangerOrderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExchangerOrderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExchangerOrderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExchangerOrderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExchangerOrderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExchangerOrderValidationError) ErrorName() string { return "ExchangerOrderValidationError" }

// Error satisfies the builtin error interface
func (e ExchangerOrderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExchangerOrder.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExchangerOrderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExchangerOrderValidationError{}

// Validate checks the field values on ExchangerOrderOption_Display with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExchangerOrderOption_Display) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExchangerOrderOption_Display with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExchangerOrderOption_DisplayMultiError, or nil if none found.
func (m *ExchangerOrderOption_Display) ValidateAll() error {
	return m.validate(true)
}

func (m *ExchangerOrderOption_Display) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for Subtitle

	// no validation rules for ImageUrl

	// no validation rules for Desc

	if all {
		switch v := interface{}(m.GetAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExchangerOrderOption_DisplayValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExchangerOrderOption_DisplayValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExchangerOrderOption_DisplayValidationError{
				field:  "AdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDynamicFields() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExchangerOrderOption_DisplayValidationError{
						field:  fmt.Sprintf("DynamicFields[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExchangerOrderOption_DisplayValidationError{
						field:  fmt.Sprintf("DynamicFields[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExchangerOrderOption_DisplayValidationError{
					field:  fmt.Sprintf("DynamicFields[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ExchangerOrderOption_DisplayMultiError(errors)
	}

	return nil
}

// ExchangerOrderOption_DisplayMultiError is an error wrapping multiple
// validation errors returned by ExchangerOrderOption_Display.ValidateAll() if
// the designated constraints aren't met.
type ExchangerOrderOption_DisplayMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExchangerOrderOption_DisplayMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExchangerOrderOption_DisplayMultiError) AllErrors() []error { return m }

// ExchangerOrderOption_DisplayValidationError is the validation error returned
// by ExchangerOrderOption_Display.Validate if the designated constraints
// aren't met.
type ExchangerOrderOption_DisplayValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExchangerOrderOption_DisplayValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExchangerOrderOption_DisplayValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExchangerOrderOption_DisplayValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExchangerOrderOption_DisplayValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExchangerOrderOption_DisplayValidationError) ErrorName() string {
	return "ExchangerOrderOption_DisplayValidationError"
}

// Error satisfies the builtin error interface
func (e ExchangerOrderOption_DisplayValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExchangerOrderOption_Display.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExchangerOrderOption_DisplayValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExchangerOrderOption_DisplayValidationError{}

// Validate checks the field values on
// ExchangerOrderOption_Display_AdditionalDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExchangerOrderOption_Display_AdditionalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ExchangerOrderOption_Display_AdditionalDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ExchangerOrderOption_Display_AdditionalDetailsMultiError, or nil if none found.
func (m *ExchangerOrderOption_Display_AdditionalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ExchangerOrderOption_Display_AdditionalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BannerLogoUrl

	// no validation rules for BannerImageUrl

	// no validation rules for BannerTitle

	// no validation rules for Desc

	// no validation rules for IntermediateStateText

	// no validation rules for BannerBgColor

	for idx, item := range m.GetTncsV2() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExchangerOrderOption_Display_AdditionalDetailsValidationError{
						field:  fmt.Sprintf("TncsV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExchangerOrderOption_Display_AdditionalDetailsValidationError{
						field:  fmt.Sprintf("TncsV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExchangerOrderOption_Display_AdditionalDetailsValidationError{
					field:  fmt.Sprintf("TncsV2[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ExchangerOrderOption_Display_AdditionalDetailsMultiError(errors)
	}

	return nil
}

// ExchangerOrderOption_Display_AdditionalDetailsMultiError is an error
// wrapping multiple validation errors returned by
// ExchangerOrderOption_Display_AdditionalDetails.ValidateAll() if the
// designated constraints aren't met.
type ExchangerOrderOption_Display_AdditionalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExchangerOrderOption_Display_AdditionalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExchangerOrderOption_Display_AdditionalDetailsMultiError) AllErrors() []error { return m }

// ExchangerOrderOption_Display_AdditionalDetailsValidationError is the
// validation error returned by
// ExchangerOrderOption_Display_AdditionalDetails.Validate if the designated
// constraints aren't met.
type ExchangerOrderOption_Display_AdditionalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExchangerOrderOption_Display_AdditionalDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExchangerOrderOption_Display_AdditionalDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ExchangerOrderOption_Display_AdditionalDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExchangerOrderOption_Display_AdditionalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExchangerOrderOption_Display_AdditionalDetailsValidationError) ErrorName() string {
	return "ExchangerOrderOption_Display_AdditionalDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ExchangerOrderOption_Display_AdditionalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExchangerOrderOption_Display_AdditionalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExchangerOrderOption_Display_AdditionalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExchangerOrderOption_Display_AdditionalDetailsValidationError{}

// Validate checks the field values on
// ExchangerOrderOption_Display_DynamicFields with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExchangerOrderOption_Display_DynamicFields) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ExchangerOrderOption_Display_DynamicFields with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ExchangerOrderOption_Display_DynamicFieldsMultiError, or nil if none found.
func (m *ExchangerOrderOption_Display_DynamicFields) ValidateAll() error {
	return m.validate(true)
}

func (m *ExchangerOrderOption_Display_DynamicFields) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Value

	// no validation rules for IsCopyable

	if len(errors) > 0 {
		return ExchangerOrderOption_Display_DynamicFieldsMultiError(errors)
	}

	return nil
}

// ExchangerOrderOption_Display_DynamicFieldsMultiError is an error wrapping
// multiple validation errors returned by
// ExchangerOrderOption_Display_DynamicFields.ValidateAll() if the designated
// constraints aren't met.
type ExchangerOrderOption_Display_DynamicFieldsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExchangerOrderOption_Display_DynamicFieldsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExchangerOrderOption_Display_DynamicFieldsMultiError) AllErrors() []error { return m }

// ExchangerOrderOption_Display_DynamicFieldsValidationError is the validation
// error returned by ExchangerOrderOption_Display_DynamicFields.Validate if
// the designated constraints aren't met.
type ExchangerOrderOption_Display_DynamicFieldsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExchangerOrderOption_Display_DynamicFieldsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExchangerOrderOption_Display_DynamicFieldsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExchangerOrderOption_Display_DynamicFieldsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExchangerOrderOption_Display_DynamicFieldsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExchangerOrderOption_Display_DynamicFieldsValidationError) ErrorName() string {
	return "ExchangerOrderOption_Display_DynamicFieldsValidationError"
}

// Error satisfies the builtin error interface
func (e ExchangerOrderOption_Display_DynamicFieldsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExchangerOrderOption_Display_DynamicFields.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExchangerOrderOption_Display_DynamicFieldsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExchangerOrderOption_Display_DynamicFieldsValidationError{}
