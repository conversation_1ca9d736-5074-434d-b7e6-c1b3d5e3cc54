// Code generated by MockGen. DO NOT EDIT.
// Source: api/frontend/stockguardian/matrix/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	matrix "github.com/epifi/gamma/api/frontend/stockguardian/matrix"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockMatrixClient is a mock of MatrixClient interface.
type MockMatrixClient struct {
	ctrl     *gomock.Controller
	recorder *MockMatrixClientMockRecorder
}

// MockMatrixClientMockRecorder is the mock recorder for MockMatrixClient.
type MockMatrixClientMockRecorder struct {
	mock *MockMatrixClient
}

// NewMockMatrixClient creates a new mock instance.
func NewMockMatrixClient(ctrl *gomock.Controller) *MockMatrixClient {
	mock := &MockMatrixClient{ctrl: ctrl}
	mock.recorder = &MockMatrixClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMatrixClient) EXPECT() *MockMatrixClientMockRecorder {
	return m.recorder
}

// ResetCustomerApplicationStage mocks base method.
func (m *MockMatrixClient) ResetCustomerApplicationStage(ctx context.Context, in *matrix.ResetCustomerApplicationStageRequest, opts ...grpc.CallOption) (*matrix.ResetCustomerApplicationStageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResetCustomerApplicationStage", varargs...)
	ret0, _ := ret[0].(*matrix.ResetCustomerApplicationStageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResetCustomerApplicationStage indicates an expected call of ResetCustomerApplicationStage.
func (mr *MockMatrixClientMockRecorder) ResetCustomerApplicationStage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetCustomerApplicationStage", reflect.TypeOf((*MockMatrixClient)(nil).ResetCustomerApplicationStage), varargs...)
}

// SkipCustomerApplicationStage mocks base method.
func (m *MockMatrixClient) SkipCustomerApplicationStage(ctx context.Context, in *matrix.SkipCustomerApplicationStageRequest, opts ...grpc.CallOption) (*matrix.SkipCustomerApplicationStageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SkipCustomerApplicationStage", varargs...)
	ret0, _ := ret[0].(*matrix.SkipCustomerApplicationStageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SkipCustomerApplicationStage indicates an expected call of SkipCustomerApplicationStage.
func (mr *MockMatrixClientMockRecorder) SkipCustomerApplicationStage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SkipCustomerApplicationStage", reflect.TypeOf((*MockMatrixClient)(nil).SkipCustomerApplicationStage), varargs...)
}

// MockMatrixServer is a mock of MatrixServer interface.
type MockMatrixServer struct {
	ctrl     *gomock.Controller
	recorder *MockMatrixServerMockRecorder
}

// MockMatrixServerMockRecorder is the mock recorder for MockMatrixServer.
type MockMatrixServerMockRecorder struct {
	mock *MockMatrixServer
}

// NewMockMatrixServer creates a new mock instance.
func NewMockMatrixServer(ctrl *gomock.Controller) *MockMatrixServer {
	mock := &MockMatrixServer{ctrl: ctrl}
	mock.recorder = &MockMatrixServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMatrixServer) EXPECT() *MockMatrixServerMockRecorder {
	return m.recorder
}

// ResetCustomerApplicationStage mocks base method.
func (m *MockMatrixServer) ResetCustomerApplicationStage(arg0 context.Context, arg1 *matrix.ResetCustomerApplicationStageRequest) (*matrix.ResetCustomerApplicationStageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetCustomerApplicationStage", arg0, arg1)
	ret0, _ := ret[0].(*matrix.ResetCustomerApplicationStageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResetCustomerApplicationStage indicates an expected call of ResetCustomerApplicationStage.
func (mr *MockMatrixServerMockRecorder) ResetCustomerApplicationStage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetCustomerApplicationStage", reflect.TypeOf((*MockMatrixServer)(nil).ResetCustomerApplicationStage), arg0, arg1)
}

// SkipCustomerApplicationStage mocks base method.
func (m *MockMatrixServer) SkipCustomerApplicationStage(arg0 context.Context, arg1 *matrix.SkipCustomerApplicationStageRequest) (*matrix.SkipCustomerApplicationStageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SkipCustomerApplicationStage", arg0, arg1)
	ret0, _ := ret[0].(*matrix.SkipCustomerApplicationStageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SkipCustomerApplicationStage indicates an expected call of SkipCustomerApplicationStage.
func (mr *MockMatrixServerMockRecorder) SkipCustomerApplicationStage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SkipCustomerApplicationStage", reflect.TypeOf((*MockMatrixServer)(nil).SkipCustomerApplicationStage), arg0, arg1)
}

// MockUnsafeMatrixServer is a mock of UnsafeMatrixServer interface.
type MockUnsafeMatrixServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeMatrixServerMockRecorder
}

// MockUnsafeMatrixServerMockRecorder is the mock recorder for MockUnsafeMatrixServer.
type MockUnsafeMatrixServerMockRecorder struct {
	mock *MockUnsafeMatrixServer
}

// NewMockUnsafeMatrixServer creates a new mock instance.
func NewMockUnsafeMatrixServer(ctrl *gomock.Controller) *MockUnsafeMatrixServer {
	mock := &MockUnsafeMatrixServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeMatrixServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeMatrixServer) EXPECT() *MockUnsafeMatrixServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedMatrixServer mocks base method.
func (m *MockUnsafeMatrixServer) mustEmbedUnimplementedMatrixServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMatrixServer")
}

// mustEmbedUnimplementedMatrixServer indicates an expected call of mustEmbedUnimplementedMatrixServer.
func (mr *MockUnsafeMatrixServerMockRecorder) mustEmbedUnimplementedMatrixServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMatrixServer", reflect.TypeOf((*MockUnsafeMatrixServer)(nil).mustEmbedUnimplementedMatrixServer))
}
