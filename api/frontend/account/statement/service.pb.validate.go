// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/account/statement/service.proto

package statement

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accounts "github.com/epifi/gamma/api/accounts"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accounts.Type(0)
)

// Validate checks the field values on GenerateAccountStatementRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateAccountStatementRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateAccountStatementRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GenerateAccountStatementRequestMultiError, or nil if none found.
func (m *GenerateAccountStatementRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateAccountStatementRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateAccountStatementRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateAccountStatementRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateAccountStatementRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateAccountStatementRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateAccountStatementRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateAccountStatementRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _GenerateAccountStatementRequest_AccountType_NotInLookup[m.GetAccountType()]; ok {
		err := GenerateAccountStatementRequestValidationError{
			field:  "AccountType",
			reason: "value must not be in list [TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAccountId()) < 1 {
		err := GenerateAccountStatementRequestValidationError{
			field:  "AccountId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFromDate() == nil {
		err := GenerateAccountStatementRequestValidationError{
			field:  "FromDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateAccountStatementRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateAccountStatementRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateAccountStatementRequestValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetToDate() == nil {
		err := GenerateAccountStatementRequestValidationError{
			field:  "ToDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetToDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateAccountStatementRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateAccountStatementRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateAccountStatementRequestValidationError{
				field:  "ToDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _GenerateAccountStatementRequest_Format_NotInLookup[m.GetFormat()]; ok {
		err := GenerateAccountStatementRequestValidationError{
			field:  "Format",
			reason: "value must not be in list [STATEMENT_FORMAT_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GenerateAccountStatementRequestMultiError(errors)
	}

	return nil
}

// GenerateAccountStatementRequestMultiError is an error wrapping multiple
// validation errors returned by GenerateAccountStatementRequest.ValidateAll()
// if the designated constraints aren't met.
type GenerateAccountStatementRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateAccountStatementRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateAccountStatementRequestMultiError) AllErrors() []error { return m }

// GenerateAccountStatementRequestValidationError is the validation error
// returned by GenerateAccountStatementRequest.Validate if the designated
// constraints aren't met.
type GenerateAccountStatementRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateAccountStatementRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateAccountStatementRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateAccountStatementRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateAccountStatementRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateAccountStatementRequestValidationError) ErrorName() string {
	return "GenerateAccountStatementRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateAccountStatementRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateAccountStatementRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateAccountStatementRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateAccountStatementRequestValidationError{}

var _GenerateAccountStatementRequest_AccountType_NotInLookup = map[accounts.Type]struct{}{
	0: {},
}

var _GenerateAccountStatementRequest_Format_NotInLookup = map[StatementFormat]struct{}{
	0: {},
}

// Validate checks the field values on GenerateAccountStatementResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GenerateAccountStatementResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateAccountStatementResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GenerateAccountStatementResponseMultiError, or nil if none found.
func (m *GenerateAccountStatementResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateAccountStatementResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateAccountStatementResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateAccountStatementResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateAccountStatementResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateAccountStatementResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateAccountStatementResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateAccountStatementResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateAccountStatementResponseMultiError(errors)
	}

	return nil
}

// GenerateAccountStatementResponseMultiError is an error wrapping multiple
// validation errors returned by
// GenerateAccountStatementResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateAccountStatementResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateAccountStatementResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateAccountStatementResponseMultiError) AllErrors() []error { return m }

// GenerateAccountStatementResponseValidationError is the validation error
// returned by GenerateAccountStatementResponse.Validate if the designated
// constraints aren't met.
type GenerateAccountStatementResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateAccountStatementResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateAccountStatementResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateAccountStatementResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateAccountStatementResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateAccountStatementResponseValidationError) ErrorName() string {
	return "GenerateAccountStatementResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateAccountStatementResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateAccountStatementResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateAccountStatementResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateAccountStatementResponseValidationError{}
