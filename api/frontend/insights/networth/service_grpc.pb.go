// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/frontend/insights/networth/service.proto

package networth

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NetWorth_GetNetWorthDashboard_FullMethodName                  = "/frontend.insights.networth.NetWorth/GetNetWorthDashboard"
	NetWorth_DepositDeclaration_FullMethodName                    = "/frontend.insights.networth.NetWorth/DepositDeclaration"
	NetWorth_GetManualFormConfig_FullMethodName                   = "/frontend.insights.networth.NetWorth/GetManualFormConfig"
	NetWorth_SubmitManualForm_FullMethodName                      = "/frontend.insights.networth.NetWorth/SubmitManualForm"
	NetWorth_GetManualAssetDashboard_FullMethodName               = "/frontend.insights.networth.NetWorth/GetManualAssetDashboard"
	NetWorth_DeleteManualAsset_FullMethodName                     = "/frontend.insights.networth.NetWorth/DeleteManualAsset"
	NetWorth_GetNetWorthSummaryForHome_FullMethodName             = "/frontend.insights.networth.NetWorth/GetNetWorthSummaryForHome"
	NetWorth_GetNextNetWorthRefreshAction_FullMethodName          = "/frontend.insights.networth.NetWorth/GetNextNetWorthRefreshAction"
	NetWorth_UpdateManualAssets_FullMethodName                    = "/frontend.insights.networth.NetWorth/UpdateManualAssets"
	NetWorth_SearchAssetFormFieldOptions_FullMethodName           = "/frontend.insights.networth.NetWorth/SearchAssetFormFieldOptions"
	NetWorth_GetCreditScoreSummaryForHome_FullMethodName          = "/frontend.insights.networth.NetWorth/GetCreditScoreSummaryForHome"
	NetWorth_GetEpfSummaryForHome_FullMethodName                  = "/frontend.insights.networth.NetWorth/GetEpfSummaryForHome"
	NetWorth_GetMfSummaryForHome_FullMethodName                   = "/frontend.insights.networth.NetWorth/GetMfSummaryForHome"
	NetWorth_GetWealthBuilderLandingPage_FullMethodName           = "/frontend.insights.networth.NetWorth/GetWealthBuilderLandingPage"
	NetWorth_GetAssetImportFlowStatus_FullMethodName              = "/frontend.insights.networth.NetWorth/GetAssetImportFlowStatus"
	NetWorth_GetConnectMoreAssetsScreen_FullMethodName            = "/frontend.insights.networth.NetWorth/GetConnectMoreAssetsScreen"
	NetWorth_GetWealthBuilderDashboardComponent_FullMethodName    = "/frontend.insights.networth.NetWorth/GetWealthBuilderDashboardComponent"
	NetWorth_MagicImportFiles_FullMethodName                      = "/frontend.insights.networth.NetWorth/MagicImportFiles"
	NetWorth_SubmitManualForms_FullMethodName                     = "/frontend.insights.networth.NetWorth/SubmitManualForms"
	NetWorth_GetMagicImportIdeas_FullMethodName                   = "/frontend.insights.networth.NetWorth/GetMagicImportIdeas"
	NetWorth_GetNetworthDataFile_FullMethodName                   = "/frontend.insights.networth.NetWorth/GetNetworthDataFile"
	NetWorth_GetMagicImportScreenOptions_FullMethodName           = "/frontend.insights.networth.NetWorth/GetMagicImportScreenOptions"
	NetWorth_GetWealthSduiBottomSheetScreenOptions_FullMethodName = "/frontend.insights.networth.NetWorth/GetWealthSduiBottomSheetScreenOptions"
	NetWorth_GetInteractiveTalkToAiScreen_FullMethodName          = "/frontend.insights.networth.NetWorth/GetInteractiveTalkToAiScreen"
)

// NetWorthClient is the client API for NetWorth service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NetWorthClient interface {
	// GetNetWorthDashboard rpc will get all the details for net worth dashboard.
	// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-%E2%80%A2-FFF?type=design&node-id=1-5591&mode=design&t=AwPidyMULqEY9jbe-0
	GetNetWorthDashboard(ctx context.Context, in *GetNetWorthDashboardRequest, opts ...grpc.CallOption) (*GetNetWorthDashboardResponse, error)
	// Use to manually record a Deposit created by user on some other platform.
	// One use case for this is to calculate the net worth of the user.
	// https://drive.google.com/file/d/1KQI6qM3FMttsoIrHpPsRF49F7CqTw8I0/view?usp=drive_link
	DepositDeclaration(ctx context.Context, in *DepositDeclarationRequest, opts ...grpc.CallOption) (*DepositDeclarationResponse, error)
	GetManualFormConfig(ctx context.Context, in *GetManualFormConfigRequest, opts ...grpc.CallOption) (*GetManualFormConfigResponse, error)
	SubmitManualForm(ctx context.Context, in *SubmitManualFormRequest, opts ...grpc.CallOption) (*SubmitManualFormResponse, error)
	// GetManualAssetDashboard gets dashboard for a particular asset based on type
	GetManualAssetDashboard(ctx context.Context, in *GetManualAssetDashboardRequest, opts ...grpc.CallOption) (*GetManualAssetDashboardResponse, error)
	DeleteManualAsset(ctx context.Context, in *DeleteManualAssetRequest, opts ...grpc.CallOption) (*DeleteManualAssetResponse, error)
	// RPC provides Net Worth(Savings Account, Assets, Loans & Liabilities) of all connected accounts
	// Used in Home screen for rendering the Net Worth card
	// Figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=5097-50184&mode=design&t=3DhsGDfnbVVKGstY-0
	// OK : success
	// INTERNAL : internal error
	GetNetWorthSummaryForHome(ctx context.Context, in *GetNetWorthSummaryForHomeRequest, opts ...grpc.CallOption) (*GetNetWorthSummaryForHomeResponse, error)
	// RPC used for orchestrating Net worth refresh flows.
	// This rpc is to be called by the client on receiving the deeplink 'NET_WORTH_REFRESH_GET_NEXT_ACTION' and then redirect
	// to the deeplink provided in the response
	GetNextNetWorthRefreshAction(ctx context.Context, in *GetNextNetWorthRefreshActionRequest, opts ...grpc.CallOption) (*GetNextNetWorthRefreshActionResponse, error)
	// RPC used for updating all manual assets provided by the user
	// This rpc is to be called by the client on receiving the deeplink 'NET_WORTH_REFRESH_UPDATE_MANUAL_ASSETS' and then redirect
	// to the deeplink provided in the response
	UpdateManualAssets(ctx context.Context, in *UpdateManualAssetsRequest, opts ...grpc.CallOption) (*UpdateManualAssetsResponse, error)
	// SearchAssetFieldOptions returns a list of relevant options for a search-text and a field name in the form to add a manual asset,
	// e.g., for a list of SEBI-registered portfolio managers, i.e., companies for the field "AMC Name" in PMS.
	SearchAssetFormFieldOptions(ctx context.Context, in *SearchAssetFormFieldOptionsRequest, opts ...grpc.CallOption) (*SearchAssetFormFieldOptionsResponse, error)
	// Rpc to provide credit score details in dashboard card
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=4963-41902&t=K6m5KdnunF2JI7fB-1
	GetCreditScoreSummaryForHome(ctx context.Context, in *GetCreditScoreSummaryForHomeRequest, opts ...grpc.CallOption) (*GetCreditScoreSummaryForHomeResponse, error)
	// Rpc to provide EPF details in dashboard card
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=4963-41902&t=K6m5KdnunF2JI7fB-1
	GetEpfSummaryForHome(ctx context.Context, in *GetEpfSummaryForHomeRequest, opts ...grpc.CallOption) (*GetEpfSummaryForHomeResponse, error)
	// Rpc to provide MF details in dashboard card
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=4963-41902&t=K6m5KdnunF2JI7fB-1
	GetMfSummaryForHome(ctx context.Context, in *GetMfSummaryForHomeRequest, opts ...grpc.CallOption) (*GetMfSummaryForHomeResponse, error)
	// Rpc to provide the wealth builder landing page components like dashboard and analysis card
	GetWealthBuilderLandingPage(ctx context.Context, in *GetWealthBuilderLandingPageRequest, opts ...grpc.CallOption) (*GetWealthBuilderLandingPageResponse, error)
	// GetSecretImportFlowStatus returns the polling status for secret import flow
	GetAssetImportFlowStatus(ctx context.Context, in *GetAssetImportFlowStatusRequest, opts ...grpc.CallOption) (*GetAssetImportFlowStatusResponse, error)
	// GetConnectMoreAssetsScreen to get the screen for connecting more assets and liabilities
	// This is only used for widget generation for non-connected assets and liabilities
	// generates widgets for assets and liabilities in request of api
	// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7351-45880&t=pxg8lOjhsfTmCMR2-4
	// revamped figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=15731-22183&t=7kO8lb4vTxCWsGma-4
	GetConnectMoreAssetsScreen(ctx context.Context, in *GetConnectMoreAssetsScreenRequest, opts ...grpc.CallOption) (*GetConnectMoreAssetsScreenResponse, error)
	// GetWealthBuilderDashboardComponent to provide all details for wealth builder landing component
	// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-46845&t=pxg8lOjhsfTmCMR2-4
	GetWealthBuilderDashboardComponent(ctx context.Context, in *GetWealthBuilderDashboardComponentRequest, opts ...grpc.CallOption) (*GetWealthBuilderDashboardComponentResponse, error)
	MagicImportFiles(ctx context.Context, in *MagicImportFilesRequest, opts ...grpc.CallOption) (*MagicImportFilesResponse, error)
	SubmitManualForms(ctx context.Context, in *SubmitManualFormsRequest, opts ...grpc.CallOption) (*SubmitManualFormsResponse, error)
	// GetMagicImportIdeas returns a SDUI section which has ideas on user can use Magic import feature
	// e.g: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=16048-23163&t=qmTzSq6nX4YOgW1k-0
	GetMagicImportIdeas(ctx context.Context, in *GetMagicImportIdeasRequest, opts ...grpc.CallOption) (*GetMagicImportIdeasResponse, error)
	// GetNetworthDataFile generates a comprehensive financial data file containing user's networth summary,
	// mutual fund holdings, account aggregator data, credit reports, EPF details, and transaction history.
	// Returns base64-encoded JSON data as a downloadable text file, with concurrent data fetching for performance.
	GetNetworthDataFile(ctx context.Context, in *GetNetworthDataFileRequest, opts ...grpc.CallOption) (*GetNetworthDataFileResponse, error)
	// GetMagicImportScreenOptions returns [NetworthMagicImportScreenOptions]
	// Client calls this RPC, if the deeplink [NETWORTH_MAGIC_IMPORT_SCREEN] doesn't have screen options
	GetMagicImportScreenOptions(ctx context.Context, in *GetMagicImportScreenOptionsRequest, opts ...grpc.CallOption) (*GetMagicImportScreenOptionsResponse, error)
	// GetWealthSduiBottomSheetScreenOptions returns [WealthSduiBottomSheetScreenOptions] for the given metadata
	// Client calls this RPC, if the deeplink [WEALTH_SDUI_BOTTOM_SHEET] doesn't contain [content] field to render the screen
	GetWealthSduiBottomSheetScreenOptions(ctx context.Context, in *GetWealthSduiBottomSheetScreenOptionsRequest, opts ...grpc.CallOption) (*GetWealthSduiBottomSheetScreenOptionsResponse, error)
	// GetInteractiveTalkToAiScreen returns UI data for interactive talk to AI deeplink screen
	// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-FFF--Wealth-Builder?node-id=17842-23805&t=PaH3BTqHoTjgUPSp-0
	GetInteractiveTalkToAiScreen(ctx context.Context, in *GetInteractiveTalkToAiScreenRequest, opts ...grpc.CallOption) (*GetInteractiveTalkToAiScreenResponse, error)
}

type netWorthClient struct {
	cc grpc.ClientConnInterface
}

func NewNetWorthClient(cc grpc.ClientConnInterface) NetWorthClient {
	return &netWorthClient{cc}
}

func (c *netWorthClient) GetNetWorthDashboard(ctx context.Context, in *GetNetWorthDashboardRequest, opts ...grpc.CallOption) (*GetNetWorthDashboardResponse, error) {
	out := new(GetNetWorthDashboardResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetNetWorthDashboard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) DepositDeclaration(ctx context.Context, in *DepositDeclarationRequest, opts ...grpc.CallOption) (*DepositDeclarationResponse, error) {
	out := new(DepositDeclarationResponse)
	err := c.cc.Invoke(ctx, NetWorth_DepositDeclaration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetManualFormConfig(ctx context.Context, in *GetManualFormConfigRequest, opts ...grpc.CallOption) (*GetManualFormConfigResponse, error) {
	out := new(GetManualFormConfigResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetManualFormConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) SubmitManualForm(ctx context.Context, in *SubmitManualFormRequest, opts ...grpc.CallOption) (*SubmitManualFormResponse, error) {
	out := new(SubmitManualFormResponse)
	err := c.cc.Invoke(ctx, NetWorth_SubmitManualForm_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetManualAssetDashboard(ctx context.Context, in *GetManualAssetDashboardRequest, opts ...grpc.CallOption) (*GetManualAssetDashboardResponse, error) {
	out := new(GetManualAssetDashboardResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetManualAssetDashboard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) DeleteManualAsset(ctx context.Context, in *DeleteManualAssetRequest, opts ...grpc.CallOption) (*DeleteManualAssetResponse, error) {
	out := new(DeleteManualAssetResponse)
	err := c.cc.Invoke(ctx, NetWorth_DeleteManualAsset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetNetWorthSummaryForHome(ctx context.Context, in *GetNetWorthSummaryForHomeRequest, opts ...grpc.CallOption) (*GetNetWorthSummaryForHomeResponse, error) {
	out := new(GetNetWorthSummaryForHomeResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetNetWorthSummaryForHome_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetNextNetWorthRefreshAction(ctx context.Context, in *GetNextNetWorthRefreshActionRequest, opts ...grpc.CallOption) (*GetNextNetWorthRefreshActionResponse, error) {
	out := new(GetNextNetWorthRefreshActionResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetNextNetWorthRefreshAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) UpdateManualAssets(ctx context.Context, in *UpdateManualAssetsRequest, opts ...grpc.CallOption) (*UpdateManualAssetsResponse, error) {
	out := new(UpdateManualAssetsResponse)
	err := c.cc.Invoke(ctx, NetWorth_UpdateManualAssets_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) SearchAssetFormFieldOptions(ctx context.Context, in *SearchAssetFormFieldOptionsRequest, opts ...grpc.CallOption) (*SearchAssetFormFieldOptionsResponse, error) {
	out := new(SearchAssetFormFieldOptionsResponse)
	err := c.cc.Invoke(ctx, NetWorth_SearchAssetFormFieldOptions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetCreditScoreSummaryForHome(ctx context.Context, in *GetCreditScoreSummaryForHomeRequest, opts ...grpc.CallOption) (*GetCreditScoreSummaryForHomeResponse, error) {
	out := new(GetCreditScoreSummaryForHomeResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetCreditScoreSummaryForHome_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetEpfSummaryForHome(ctx context.Context, in *GetEpfSummaryForHomeRequest, opts ...grpc.CallOption) (*GetEpfSummaryForHomeResponse, error) {
	out := new(GetEpfSummaryForHomeResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetEpfSummaryForHome_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetMfSummaryForHome(ctx context.Context, in *GetMfSummaryForHomeRequest, opts ...grpc.CallOption) (*GetMfSummaryForHomeResponse, error) {
	out := new(GetMfSummaryForHomeResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetMfSummaryForHome_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetWealthBuilderLandingPage(ctx context.Context, in *GetWealthBuilderLandingPageRequest, opts ...grpc.CallOption) (*GetWealthBuilderLandingPageResponse, error) {
	out := new(GetWealthBuilderLandingPageResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetWealthBuilderLandingPage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetAssetImportFlowStatus(ctx context.Context, in *GetAssetImportFlowStatusRequest, opts ...grpc.CallOption) (*GetAssetImportFlowStatusResponse, error) {
	out := new(GetAssetImportFlowStatusResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetAssetImportFlowStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetConnectMoreAssetsScreen(ctx context.Context, in *GetConnectMoreAssetsScreenRequest, opts ...grpc.CallOption) (*GetConnectMoreAssetsScreenResponse, error) {
	out := new(GetConnectMoreAssetsScreenResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetConnectMoreAssetsScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetWealthBuilderDashboardComponent(ctx context.Context, in *GetWealthBuilderDashboardComponentRequest, opts ...grpc.CallOption) (*GetWealthBuilderDashboardComponentResponse, error) {
	out := new(GetWealthBuilderDashboardComponentResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetWealthBuilderDashboardComponent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) MagicImportFiles(ctx context.Context, in *MagicImportFilesRequest, opts ...grpc.CallOption) (*MagicImportFilesResponse, error) {
	out := new(MagicImportFilesResponse)
	err := c.cc.Invoke(ctx, NetWorth_MagicImportFiles_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) SubmitManualForms(ctx context.Context, in *SubmitManualFormsRequest, opts ...grpc.CallOption) (*SubmitManualFormsResponse, error) {
	out := new(SubmitManualFormsResponse)
	err := c.cc.Invoke(ctx, NetWorth_SubmitManualForms_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetMagicImportIdeas(ctx context.Context, in *GetMagicImportIdeasRequest, opts ...grpc.CallOption) (*GetMagicImportIdeasResponse, error) {
	out := new(GetMagicImportIdeasResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetMagicImportIdeas_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetNetworthDataFile(ctx context.Context, in *GetNetworthDataFileRequest, opts ...grpc.CallOption) (*GetNetworthDataFileResponse, error) {
	out := new(GetNetworthDataFileResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetNetworthDataFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetMagicImportScreenOptions(ctx context.Context, in *GetMagicImportScreenOptionsRequest, opts ...grpc.CallOption) (*GetMagicImportScreenOptionsResponse, error) {
	out := new(GetMagicImportScreenOptionsResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetMagicImportScreenOptions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetWealthSduiBottomSheetScreenOptions(ctx context.Context, in *GetWealthSduiBottomSheetScreenOptionsRequest, opts ...grpc.CallOption) (*GetWealthSduiBottomSheetScreenOptionsResponse, error) {
	out := new(GetWealthSduiBottomSheetScreenOptionsResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetWealthSduiBottomSheetScreenOptions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetInteractiveTalkToAiScreen(ctx context.Context, in *GetInteractiveTalkToAiScreenRequest, opts ...grpc.CallOption) (*GetInteractiveTalkToAiScreenResponse, error) {
	out := new(GetInteractiveTalkToAiScreenResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetInteractiveTalkToAiScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NetWorthServer is the server API for NetWorth service.
// All implementations should embed UnimplementedNetWorthServer
// for forward compatibility
type NetWorthServer interface {
	// GetNetWorthDashboard rpc will get all the details for net worth dashboard.
	// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-%E2%80%A2-FFF?type=design&node-id=1-5591&mode=design&t=AwPidyMULqEY9jbe-0
	GetNetWorthDashboard(context.Context, *GetNetWorthDashboardRequest) (*GetNetWorthDashboardResponse, error)
	// Use to manually record a Deposit created by user on some other platform.
	// One use case for this is to calculate the net worth of the user.
	// https://drive.google.com/file/d/1KQI6qM3FMttsoIrHpPsRF49F7CqTw8I0/view?usp=drive_link
	DepositDeclaration(context.Context, *DepositDeclarationRequest) (*DepositDeclarationResponse, error)
	GetManualFormConfig(context.Context, *GetManualFormConfigRequest) (*GetManualFormConfigResponse, error)
	SubmitManualForm(context.Context, *SubmitManualFormRequest) (*SubmitManualFormResponse, error)
	// GetManualAssetDashboard gets dashboard for a particular asset based on type
	GetManualAssetDashboard(context.Context, *GetManualAssetDashboardRequest) (*GetManualAssetDashboardResponse, error)
	DeleteManualAsset(context.Context, *DeleteManualAssetRequest) (*DeleteManualAssetResponse, error)
	// RPC provides Net Worth(Savings Account, Assets, Loans & Liabilities) of all connected accounts
	// Used in Home screen for rendering the Net Worth card
	// Figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=5097-50184&mode=design&t=3DhsGDfnbVVKGstY-0
	// OK : success
	// INTERNAL : internal error
	GetNetWorthSummaryForHome(context.Context, *GetNetWorthSummaryForHomeRequest) (*GetNetWorthSummaryForHomeResponse, error)
	// RPC used for orchestrating Net worth refresh flows.
	// This rpc is to be called by the client on receiving the deeplink 'NET_WORTH_REFRESH_GET_NEXT_ACTION' and then redirect
	// to the deeplink provided in the response
	GetNextNetWorthRefreshAction(context.Context, *GetNextNetWorthRefreshActionRequest) (*GetNextNetWorthRefreshActionResponse, error)
	// RPC used for updating all manual assets provided by the user
	// This rpc is to be called by the client on receiving the deeplink 'NET_WORTH_REFRESH_UPDATE_MANUAL_ASSETS' and then redirect
	// to the deeplink provided in the response
	UpdateManualAssets(context.Context, *UpdateManualAssetsRequest) (*UpdateManualAssetsResponse, error)
	// SearchAssetFieldOptions returns a list of relevant options for a search-text and a field name in the form to add a manual asset,
	// e.g., for a list of SEBI-registered portfolio managers, i.e., companies for the field "AMC Name" in PMS.
	SearchAssetFormFieldOptions(context.Context, *SearchAssetFormFieldOptionsRequest) (*SearchAssetFormFieldOptionsResponse, error)
	// Rpc to provide credit score details in dashboard card
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=4963-41902&t=K6m5KdnunF2JI7fB-1
	GetCreditScoreSummaryForHome(context.Context, *GetCreditScoreSummaryForHomeRequest) (*GetCreditScoreSummaryForHomeResponse, error)
	// Rpc to provide EPF details in dashboard card
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=4963-41902&t=K6m5KdnunF2JI7fB-1
	GetEpfSummaryForHome(context.Context, *GetEpfSummaryForHomeRequest) (*GetEpfSummaryForHomeResponse, error)
	// Rpc to provide MF details in dashboard card
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=4963-41902&t=K6m5KdnunF2JI7fB-1
	GetMfSummaryForHome(context.Context, *GetMfSummaryForHomeRequest) (*GetMfSummaryForHomeResponse, error)
	// Rpc to provide the wealth builder landing page components like dashboard and analysis card
	GetWealthBuilderLandingPage(context.Context, *GetWealthBuilderLandingPageRequest) (*GetWealthBuilderLandingPageResponse, error)
	// GetSecretImportFlowStatus returns the polling status for secret import flow
	GetAssetImportFlowStatus(context.Context, *GetAssetImportFlowStatusRequest) (*GetAssetImportFlowStatusResponse, error)
	// GetConnectMoreAssetsScreen to get the screen for connecting more assets and liabilities
	// This is only used for widget generation for non-connected assets and liabilities
	// generates widgets for assets and liabilities in request of api
	// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7351-45880&t=pxg8lOjhsfTmCMR2-4
	// revamped figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=15731-22183&t=7kO8lb4vTxCWsGma-4
	GetConnectMoreAssetsScreen(context.Context, *GetConnectMoreAssetsScreenRequest) (*GetConnectMoreAssetsScreenResponse, error)
	// GetWealthBuilderDashboardComponent to provide all details for wealth builder landing component
	// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-46845&t=pxg8lOjhsfTmCMR2-4
	GetWealthBuilderDashboardComponent(context.Context, *GetWealthBuilderDashboardComponentRequest) (*GetWealthBuilderDashboardComponentResponse, error)
	MagicImportFiles(context.Context, *MagicImportFilesRequest) (*MagicImportFilesResponse, error)
	SubmitManualForms(context.Context, *SubmitManualFormsRequest) (*SubmitManualFormsResponse, error)
	// GetMagicImportIdeas returns a SDUI section which has ideas on user can use Magic import feature
	// e.g: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=16048-23163&t=qmTzSq6nX4YOgW1k-0
	GetMagicImportIdeas(context.Context, *GetMagicImportIdeasRequest) (*GetMagicImportIdeasResponse, error)
	// GetNetworthDataFile generates a comprehensive financial data file containing user's networth summary,
	// mutual fund holdings, account aggregator data, credit reports, EPF details, and transaction history.
	// Returns base64-encoded JSON data as a downloadable text file, with concurrent data fetching for performance.
	GetNetworthDataFile(context.Context, *GetNetworthDataFileRequest) (*GetNetworthDataFileResponse, error)
	// GetMagicImportScreenOptions returns [NetworthMagicImportScreenOptions]
	// Client calls this RPC, if the deeplink [NETWORTH_MAGIC_IMPORT_SCREEN] doesn't have screen options
	GetMagicImportScreenOptions(context.Context, *GetMagicImportScreenOptionsRequest) (*GetMagicImportScreenOptionsResponse, error)
	// GetWealthSduiBottomSheetScreenOptions returns [WealthSduiBottomSheetScreenOptions] for the given metadata
	// Client calls this RPC, if the deeplink [WEALTH_SDUI_BOTTOM_SHEET] doesn't contain [content] field to render the screen
	GetWealthSduiBottomSheetScreenOptions(context.Context, *GetWealthSduiBottomSheetScreenOptionsRequest) (*GetWealthSduiBottomSheetScreenOptionsResponse, error)
	// GetInteractiveTalkToAiScreen returns UI data for interactive talk to AI deeplink screen
	// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-FFF--Wealth-Builder?node-id=17842-23805&t=PaH3BTqHoTjgUPSp-0
	GetInteractiveTalkToAiScreen(context.Context, *GetInteractiveTalkToAiScreenRequest) (*GetInteractiveTalkToAiScreenResponse, error)
}

// UnimplementedNetWorthServer should be embedded to have forward compatible implementations.
type UnimplementedNetWorthServer struct {
}

func (UnimplementedNetWorthServer) GetNetWorthDashboard(context.Context, *GetNetWorthDashboardRequest) (*GetNetWorthDashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNetWorthDashboard not implemented")
}
func (UnimplementedNetWorthServer) DepositDeclaration(context.Context, *DepositDeclarationRequest) (*DepositDeclarationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DepositDeclaration not implemented")
}
func (UnimplementedNetWorthServer) GetManualFormConfig(context.Context, *GetManualFormConfigRequest) (*GetManualFormConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetManualFormConfig not implemented")
}
func (UnimplementedNetWorthServer) SubmitManualForm(context.Context, *SubmitManualFormRequest) (*SubmitManualFormResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitManualForm not implemented")
}
func (UnimplementedNetWorthServer) GetManualAssetDashboard(context.Context, *GetManualAssetDashboardRequest) (*GetManualAssetDashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetManualAssetDashboard not implemented")
}
func (UnimplementedNetWorthServer) DeleteManualAsset(context.Context, *DeleteManualAssetRequest) (*DeleteManualAssetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteManualAsset not implemented")
}
func (UnimplementedNetWorthServer) GetNetWorthSummaryForHome(context.Context, *GetNetWorthSummaryForHomeRequest) (*GetNetWorthSummaryForHomeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNetWorthSummaryForHome not implemented")
}
func (UnimplementedNetWorthServer) GetNextNetWorthRefreshAction(context.Context, *GetNextNetWorthRefreshActionRequest) (*GetNextNetWorthRefreshActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextNetWorthRefreshAction not implemented")
}
func (UnimplementedNetWorthServer) UpdateManualAssets(context.Context, *UpdateManualAssetsRequest) (*UpdateManualAssetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateManualAssets not implemented")
}
func (UnimplementedNetWorthServer) SearchAssetFormFieldOptions(context.Context, *SearchAssetFormFieldOptionsRequest) (*SearchAssetFormFieldOptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchAssetFormFieldOptions not implemented")
}
func (UnimplementedNetWorthServer) GetCreditScoreSummaryForHome(context.Context, *GetCreditScoreSummaryForHomeRequest) (*GetCreditScoreSummaryForHomeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCreditScoreSummaryForHome not implemented")
}
func (UnimplementedNetWorthServer) GetEpfSummaryForHome(context.Context, *GetEpfSummaryForHomeRequest) (*GetEpfSummaryForHomeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEpfSummaryForHome not implemented")
}
func (UnimplementedNetWorthServer) GetMfSummaryForHome(context.Context, *GetMfSummaryForHomeRequest) (*GetMfSummaryForHomeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMfSummaryForHome not implemented")
}
func (UnimplementedNetWorthServer) GetWealthBuilderLandingPage(context.Context, *GetWealthBuilderLandingPageRequest) (*GetWealthBuilderLandingPageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWealthBuilderLandingPage not implemented")
}
func (UnimplementedNetWorthServer) GetAssetImportFlowStatus(context.Context, *GetAssetImportFlowStatusRequest) (*GetAssetImportFlowStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssetImportFlowStatus not implemented")
}
func (UnimplementedNetWorthServer) GetConnectMoreAssetsScreen(context.Context, *GetConnectMoreAssetsScreenRequest) (*GetConnectMoreAssetsScreenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConnectMoreAssetsScreen not implemented")
}
func (UnimplementedNetWorthServer) GetWealthBuilderDashboardComponent(context.Context, *GetWealthBuilderDashboardComponentRequest) (*GetWealthBuilderDashboardComponentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWealthBuilderDashboardComponent not implemented")
}
func (UnimplementedNetWorthServer) MagicImportFiles(context.Context, *MagicImportFilesRequest) (*MagicImportFilesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MagicImportFiles not implemented")
}
func (UnimplementedNetWorthServer) SubmitManualForms(context.Context, *SubmitManualFormsRequest) (*SubmitManualFormsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitManualForms not implemented")
}
func (UnimplementedNetWorthServer) GetMagicImportIdeas(context.Context, *GetMagicImportIdeasRequest) (*GetMagicImportIdeasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMagicImportIdeas not implemented")
}
func (UnimplementedNetWorthServer) GetNetworthDataFile(context.Context, *GetNetworthDataFileRequest) (*GetNetworthDataFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNetworthDataFile not implemented")
}
func (UnimplementedNetWorthServer) GetMagicImportScreenOptions(context.Context, *GetMagicImportScreenOptionsRequest) (*GetMagicImportScreenOptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMagicImportScreenOptions not implemented")
}
func (UnimplementedNetWorthServer) GetWealthSduiBottomSheetScreenOptions(context.Context, *GetWealthSduiBottomSheetScreenOptionsRequest) (*GetWealthSduiBottomSheetScreenOptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWealthSduiBottomSheetScreenOptions not implemented")
}
func (UnimplementedNetWorthServer) GetInteractiveTalkToAiScreen(context.Context, *GetInteractiveTalkToAiScreenRequest) (*GetInteractiveTalkToAiScreenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInteractiveTalkToAiScreen not implemented")
}

// UnsafeNetWorthServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NetWorthServer will
// result in compilation errors.
type UnsafeNetWorthServer interface {
	mustEmbedUnimplementedNetWorthServer()
}

func RegisterNetWorthServer(s grpc.ServiceRegistrar, srv NetWorthServer) {
	s.RegisterService(&NetWorth_ServiceDesc, srv)
}

func _NetWorth_GetNetWorthDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNetWorthDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetNetWorthDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetNetWorthDashboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetNetWorthDashboard(ctx, req.(*GetNetWorthDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_DepositDeclaration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DepositDeclarationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).DepositDeclaration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_DepositDeclaration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).DepositDeclaration(ctx, req.(*DepositDeclarationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetManualFormConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetManualFormConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetManualFormConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetManualFormConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetManualFormConfig(ctx, req.(*GetManualFormConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_SubmitManualForm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitManualFormRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).SubmitManualForm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_SubmitManualForm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).SubmitManualForm(ctx, req.(*SubmitManualFormRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetManualAssetDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetManualAssetDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetManualAssetDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetManualAssetDashboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetManualAssetDashboard(ctx, req.(*GetManualAssetDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_DeleteManualAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteManualAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).DeleteManualAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_DeleteManualAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).DeleteManualAsset(ctx, req.(*DeleteManualAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetNetWorthSummaryForHome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNetWorthSummaryForHomeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetNetWorthSummaryForHome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetNetWorthSummaryForHome_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetNetWorthSummaryForHome(ctx, req.(*GetNetWorthSummaryForHomeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetNextNetWorthRefreshAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextNetWorthRefreshActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetNextNetWorthRefreshAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetNextNetWorthRefreshAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetNextNetWorthRefreshAction(ctx, req.(*GetNextNetWorthRefreshActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_UpdateManualAssets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateManualAssetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).UpdateManualAssets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_UpdateManualAssets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).UpdateManualAssets(ctx, req.(*UpdateManualAssetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_SearchAssetFormFieldOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAssetFormFieldOptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).SearchAssetFormFieldOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_SearchAssetFormFieldOptions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).SearchAssetFormFieldOptions(ctx, req.(*SearchAssetFormFieldOptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetCreditScoreSummaryForHome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCreditScoreSummaryForHomeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetCreditScoreSummaryForHome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetCreditScoreSummaryForHome_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetCreditScoreSummaryForHome(ctx, req.(*GetCreditScoreSummaryForHomeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetEpfSummaryForHome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEpfSummaryForHomeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetEpfSummaryForHome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetEpfSummaryForHome_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetEpfSummaryForHome(ctx, req.(*GetEpfSummaryForHomeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetMfSummaryForHome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMfSummaryForHomeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetMfSummaryForHome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetMfSummaryForHome_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetMfSummaryForHome(ctx, req.(*GetMfSummaryForHomeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetWealthBuilderLandingPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWealthBuilderLandingPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetWealthBuilderLandingPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetWealthBuilderLandingPage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetWealthBuilderLandingPage(ctx, req.(*GetWealthBuilderLandingPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetAssetImportFlowStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAssetImportFlowStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetAssetImportFlowStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetAssetImportFlowStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetAssetImportFlowStatus(ctx, req.(*GetAssetImportFlowStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetConnectMoreAssetsScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConnectMoreAssetsScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetConnectMoreAssetsScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetConnectMoreAssetsScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetConnectMoreAssetsScreen(ctx, req.(*GetConnectMoreAssetsScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetWealthBuilderDashboardComponent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWealthBuilderDashboardComponentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetWealthBuilderDashboardComponent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetWealthBuilderDashboardComponent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetWealthBuilderDashboardComponent(ctx, req.(*GetWealthBuilderDashboardComponentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_MagicImportFiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MagicImportFilesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).MagicImportFiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_MagicImportFiles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).MagicImportFiles(ctx, req.(*MagicImportFilesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_SubmitManualForms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitManualFormsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).SubmitManualForms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_SubmitManualForms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).SubmitManualForms(ctx, req.(*SubmitManualFormsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetMagicImportIdeas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicImportIdeasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetMagicImportIdeas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetMagicImportIdeas_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetMagicImportIdeas(ctx, req.(*GetMagicImportIdeasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetNetworthDataFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNetworthDataFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetNetworthDataFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetNetworthDataFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetNetworthDataFile(ctx, req.(*GetNetworthDataFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetMagicImportScreenOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicImportScreenOptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetMagicImportScreenOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetMagicImportScreenOptions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetMagicImportScreenOptions(ctx, req.(*GetMagicImportScreenOptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetWealthSduiBottomSheetScreenOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWealthSduiBottomSheetScreenOptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetWealthSduiBottomSheetScreenOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetWealthSduiBottomSheetScreenOptions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetWealthSduiBottomSheetScreenOptions(ctx, req.(*GetWealthSduiBottomSheetScreenOptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetInteractiveTalkToAiScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInteractiveTalkToAiScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetInteractiveTalkToAiScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetInteractiveTalkToAiScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetInteractiveTalkToAiScreen(ctx, req.(*GetInteractiveTalkToAiScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NetWorth_ServiceDesc is the grpc.ServiceDesc for NetWorth service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NetWorth_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "frontend.insights.networth.NetWorth",
	HandlerType: (*NetWorthServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNetWorthDashboard",
			Handler:    _NetWorth_GetNetWorthDashboard_Handler,
		},
		{
			MethodName: "DepositDeclaration",
			Handler:    _NetWorth_DepositDeclaration_Handler,
		},
		{
			MethodName: "GetManualFormConfig",
			Handler:    _NetWorth_GetManualFormConfig_Handler,
		},
		{
			MethodName: "SubmitManualForm",
			Handler:    _NetWorth_SubmitManualForm_Handler,
		},
		{
			MethodName: "GetManualAssetDashboard",
			Handler:    _NetWorth_GetManualAssetDashboard_Handler,
		},
		{
			MethodName: "DeleteManualAsset",
			Handler:    _NetWorth_DeleteManualAsset_Handler,
		},
		{
			MethodName: "GetNetWorthSummaryForHome",
			Handler:    _NetWorth_GetNetWorthSummaryForHome_Handler,
		},
		{
			MethodName: "GetNextNetWorthRefreshAction",
			Handler:    _NetWorth_GetNextNetWorthRefreshAction_Handler,
		},
		{
			MethodName: "UpdateManualAssets",
			Handler:    _NetWorth_UpdateManualAssets_Handler,
		},
		{
			MethodName: "SearchAssetFormFieldOptions",
			Handler:    _NetWorth_SearchAssetFormFieldOptions_Handler,
		},
		{
			MethodName: "GetCreditScoreSummaryForHome",
			Handler:    _NetWorth_GetCreditScoreSummaryForHome_Handler,
		},
		{
			MethodName: "GetEpfSummaryForHome",
			Handler:    _NetWorth_GetEpfSummaryForHome_Handler,
		},
		{
			MethodName: "GetMfSummaryForHome",
			Handler:    _NetWorth_GetMfSummaryForHome_Handler,
		},
		{
			MethodName: "GetWealthBuilderLandingPage",
			Handler:    _NetWorth_GetWealthBuilderLandingPage_Handler,
		},
		{
			MethodName: "GetAssetImportFlowStatus",
			Handler:    _NetWorth_GetAssetImportFlowStatus_Handler,
		},
		{
			MethodName: "GetConnectMoreAssetsScreen",
			Handler:    _NetWorth_GetConnectMoreAssetsScreen_Handler,
		},
		{
			MethodName: "GetWealthBuilderDashboardComponent",
			Handler:    _NetWorth_GetWealthBuilderDashboardComponent_Handler,
		},
		{
			MethodName: "MagicImportFiles",
			Handler:    _NetWorth_MagicImportFiles_Handler,
		},
		{
			MethodName: "SubmitManualForms",
			Handler:    _NetWorth_SubmitManualForms_Handler,
		},
		{
			MethodName: "GetMagicImportIdeas",
			Handler:    _NetWorth_GetMagicImportIdeas_Handler,
		},
		{
			MethodName: "GetNetworthDataFile",
			Handler:    _NetWorth_GetNetworthDataFile_Handler,
		},
		{
			MethodName: "GetMagicImportScreenOptions",
			Handler:    _NetWorth_GetMagicImportScreenOptions_Handler,
		},
		{
			MethodName: "GetWealthSduiBottomSheetScreenOptions",
			Handler:    _NetWorth_GetWealthSduiBottomSheetScreenOptions_Handler,
		},
		{
			MethodName: "GetInteractiveTalkToAiScreen",
			Handler:    _NetWorth_GetInteractiveTalkToAiScreen_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/frontend/insights/networth/service.proto",
}
