// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/insights/networth/ui/ui.proto

package ui

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	enums "github.com/epifi/gamma/api/frontend/insights/networth/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = enums.NetworthSectionType(0)
)

// Validate checks the field values on WealthBuilderLandingComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WealthBuilderLandingComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WealthBuilderLandingComponent with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// WealthBuilderLandingComponentMultiError, or nil if none found.
func (m *WealthBuilderLandingComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *WealthBuilderLandingComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetWealthBuilderLandingDashboard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingComponentValidationError{
					field:  "WealthBuilderLandingDashboard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingComponentValidationError{
					field:  "WealthBuilderLandingDashboard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWealthBuilderLandingDashboard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingComponentValidationError{
				field:  "WealthBuilderLandingDashboard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFooterCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingComponentValidationError{
					field:  "FooterCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingComponentValidationError{
					field:  "FooterCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooterCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingComponentValidationError{
				field:  "FooterCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingComponentValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingComponentValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingComponentValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WealthBuilderLandingComponentMultiError(errors)
	}

	return nil
}

// WealthBuilderLandingComponentMultiError is an error wrapping multiple
// validation errors returned by WealthBuilderLandingComponent.ValidateAll()
// if the designated constraints aren't met.
type WealthBuilderLandingComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WealthBuilderLandingComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WealthBuilderLandingComponentMultiError) AllErrors() []error { return m }

// WealthBuilderLandingComponentValidationError is the validation error
// returned by WealthBuilderLandingComponent.Validate if the designated
// constraints aren't met.
type WealthBuilderLandingComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WealthBuilderLandingComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WealthBuilderLandingComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WealthBuilderLandingComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WealthBuilderLandingComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WealthBuilderLandingComponentValidationError) ErrorName() string {
	return "WealthBuilderLandingComponentValidationError"
}

// Error satisfies the builtin error interface
func (e WealthBuilderLandingComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWealthBuilderLandingComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WealthBuilderLandingComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WealthBuilderLandingComponentValidationError{}

// Validate checks the field values on WealthBuilderLandingCta with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WealthBuilderLandingCta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WealthBuilderLandingCta with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WealthBuilderLandingCtaMultiError, or nil if none found.
func (m *WealthBuilderLandingCta) ValidateAll() error {
	return m.validate(true)
}

func (m *WealthBuilderLandingCta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingCtaValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	if all {
		switch v := interface{}(m.GetBorderProperty()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "BorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "BorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderProperty()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingCtaValidationError{
				field:  "BorderProperty",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingCtaValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingCtaValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingCtaValidationError{
				field:  "Tag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsPrimary

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingCtaValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Identifier

	if all {
		switch v := interface{}(m.GetArrowColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "ArrowColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingCtaValidationError{
					field:  "ArrowColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetArrowColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingCtaValidationError{
				field:  "ArrowColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WealthBuilderLandingCtaMultiError(errors)
	}

	return nil
}

// WealthBuilderLandingCtaMultiError is an error wrapping multiple validation
// errors returned by WealthBuilderLandingCta.ValidateAll() if the designated
// constraints aren't met.
type WealthBuilderLandingCtaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WealthBuilderLandingCtaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WealthBuilderLandingCtaMultiError) AllErrors() []error { return m }

// WealthBuilderLandingCtaValidationError is the validation error returned by
// WealthBuilderLandingCta.Validate if the designated constraints aren't met.
type WealthBuilderLandingCtaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WealthBuilderLandingCtaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WealthBuilderLandingCtaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WealthBuilderLandingCtaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WealthBuilderLandingCtaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WealthBuilderLandingCtaValidationError) ErrorName() string {
	return "WealthBuilderLandingCtaValidationError"
}

// Error satisfies the builtin error interface
func (e WealthBuilderLandingCtaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWealthBuilderLandingCta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WealthBuilderLandingCtaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WealthBuilderLandingCtaValidationError{}

// Validate checks the field values on WealthBuilderLandingDashboard with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WealthBuilderLandingDashboard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WealthBuilderLandingDashboard with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// WealthBuilderLandingDashboardMultiError, or nil if none found.
func (m *WealthBuilderLandingDashboard) ValidateAll() error {
	return m.validate(true)
}

func (m *WealthBuilderLandingDashboard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDashboardHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingDashboardValidationError{
					field:  "DashboardHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingDashboardValidationError{
					field:  "DashboardHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDashboardHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingDashboardValidationError{
				field:  "DashboardHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetWealthBuilderLandingSections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WealthBuilderLandingDashboardValidationError{
						field:  fmt.Sprintf("WealthBuilderLandingSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WealthBuilderLandingDashboardValidationError{
						field:  fmt.Sprintf("WealthBuilderLandingSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WealthBuilderLandingDashboardValidationError{
					field:  fmt.Sprintf("WealthBuilderLandingSections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCollapsibleDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingDashboardValidationError{
					field:  "CollapsibleDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingDashboardValidationError{
					field:  "CollapsibleDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCollapsibleDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingDashboardValidationError{
				field:  "CollapsibleDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActionCta() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WealthBuilderLandingDashboardValidationError{
						field:  fmt.Sprintf("ActionCta[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WealthBuilderLandingDashboardValidationError{
						field:  fmt.Sprintf("ActionCta[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WealthBuilderLandingDashboardValidationError{
					field:  fmt.Sprintf("ActionCta[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingDashboardValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingDashboardValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingDashboardValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	for idx, item := range m.GetFooterCtas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WealthBuilderLandingDashboardValidationError{
						field:  fmt.Sprintf("FooterCtas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WealthBuilderLandingDashboardValidationError{
						field:  fmt.Sprintf("FooterCtas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WealthBuilderLandingDashboardValidationError{
					field:  fmt.Sprintf("FooterCtas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFooterBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingDashboardValidationError{
					field:  "FooterBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingDashboardValidationError{
					field:  "FooterBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooterBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingDashboardValidationError{
				field:  "FooterBanner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WealthBuilderLandingDashboardMultiError(errors)
	}

	return nil
}

// WealthBuilderLandingDashboardMultiError is an error wrapping multiple
// validation errors returned by WealthBuilderLandingDashboard.ValidateAll()
// if the designated constraints aren't met.
type WealthBuilderLandingDashboardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WealthBuilderLandingDashboardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WealthBuilderLandingDashboardMultiError) AllErrors() []error { return m }

// WealthBuilderLandingDashboardValidationError is the validation error
// returned by WealthBuilderLandingDashboard.Validate if the designated
// constraints aren't met.
type WealthBuilderLandingDashboardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WealthBuilderLandingDashboardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WealthBuilderLandingDashboardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WealthBuilderLandingDashboardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WealthBuilderLandingDashboardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WealthBuilderLandingDashboardValidationError) ErrorName() string {
	return "WealthBuilderLandingDashboardValidationError"
}

// Error satisfies the builtin error interface
func (e WealthBuilderLandingDashboardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWealthBuilderLandingDashboard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WealthBuilderLandingDashboardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WealthBuilderLandingDashboardValidationError{}

// Validate checks the field values on WealthBuilderLandingSection with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WealthBuilderLandingSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WealthBuilderLandingSection with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WealthBuilderLandingSectionMultiError, or nil if none found.
func (m *WealthBuilderLandingSection) ValidateAll() error {
	return m.validate(true)
}

func (m *WealthBuilderLandingSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for SectionType

	if all {
		switch v := interface{}(m.GetSectionHeaderGenericState()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingSectionValidationError{
					field:  "SectionHeaderGenericState",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingSectionValidationError{
					field:  "SectionHeaderGenericState",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSectionHeaderGenericState()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingSectionValidationError{
				field:  "SectionHeaderGenericState",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetWidgets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WealthBuilderLandingSectionValidationError{
						field:  fmt.Sprintf("Widgets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WealthBuilderLandingSectionValidationError{
						field:  fmt.Sprintf("Widgets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WealthBuilderLandingSectionValidationError{
					field:  fmt.Sprintf("Widgets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return WealthBuilderLandingSectionMultiError(errors)
	}

	return nil
}

// WealthBuilderLandingSectionMultiError is an error wrapping multiple
// validation errors returned by WealthBuilderLandingSection.ValidateAll() if
// the designated constraints aren't met.
type WealthBuilderLandingSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WealthBuilderLandingSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WealthBuilderLandingSectionMultiError) AllErrors() []error { return m }

// WealthBuilderLandingSectionValidationError is the validation error returned
// by WealthBuilderLandingSection.Validate if the designated constraints
// aren't met.
type WealthBuilderLandingSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WealthBuilderLandingSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WealthBuilderLandingSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WealthBuilderLandingSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WealthBuilderLandingSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WealthBuilderLandingSectionValidationError) ErrorName() string {
	return "WealthBuilderLandingSectionValidationError"
}

// Error satisfies the builtin error interface
func (e WealthBuilderLandingSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWealthBuilderLandingSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WealthBuilderLandingSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WealthBuilderLandingSectionValidationError{}

// Validate checks the field values on DashboardHeader with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DashboardHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DashboardHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DashboardHeaderMultiError, or nil if none found.
func (m *DashboardHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *DashboardHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.DashboardHeader.(type) {
	case *DashboardHeader_SectionHeaderZeroState:
		if v == nil {
			err := DashboardHeaderValidationError{
				field:  "DashboardHeader",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSectionHeaderZeroState()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DashboardHeaderValidationError{
						field:  "SectionHeaderZeroState",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DashboardHeaderValidationError{
						field:  "SectionHeaderZeroState",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSectionHeaderZeroState()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DashboardHeaderValidationError{
					field:  "SectionHeaderZeroState",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DashboardHeader_SectionHeaderConnectedState:
		if v == nil {
			err := DashboardHeaderValidationError{
				field:  "DashboardHeader",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSectionHeaderConnectedState()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DashboardHeaderValidationError{
						field:  "SectionHeaderConnectedState",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DashboardHeaderValidationError{
						field:  "SectionHeaderConnectedState",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSectionHeaderConnectedState()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DashboardHeaderValidationError{
					field:  "SectionHeaderConnectedState",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return DashboardHeaderMultiError(errors)
	}

	return nil
}

// DashboardHeaderMultiError is an error wrapping multiple validation errors
// returned by DashboardHeader.ValidateAll() if the designated constraints
// aren't met.
type DashboardHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DashboardHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DashboardHeaderMultiError) AllErrors() []error { return m }

// DashboardHeaderValidationError is the validation error returned by
// DashboardHeader.Validate if the designated constraints aren't met.
type DashboardHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DashboardHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DashboardHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DashboardHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DashboardHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DashboardHeaderValidationError) ErrorName() string { return "DashboardHeaderValidationError" }

// Error satisfies the builtin error interface
func (e DashboardHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDashboardHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DashboardHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DashboardHeaderValidationError{}

// Validate checks the field values on DashboardHeaderZeroState with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DashboardHeaderZeroState) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DashboardHeaderZeroState with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DashboardHeaderZeroStateMultiError, or nil if none found.
func (m *DashboardHeaderZeroState) ValidateAll() error {
	return m.validate(true)
}

func (m *DashboardHeaderZeroState) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DashboardHeaderZeroStateValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DashboardHeaderZeroStateValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DashboardHeaderZeroStateValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActionCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DashboardHeaderZeroStateValidationError{
					field:  "ActionCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DashboardHeaderZeroStateValidationError{
					field:  "ActionCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DashboardHeaderZeroStateValidationError{
				field:  "ActionCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DashboardHeaderZeroStateMultiError(errors)
	}

	return nil
}

// DashboardHeaderZeroStateMultiError is an error wrapping multiple validation
// errors returned by DashboardHeaderZeroState.ValidateAll() if the designated
// constraints aren't met.
type DashboardHeaderZeroStateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DashboardHeaderZeroStateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DashboardHeaderZeroStateMultiError) AllErrors() []error { return m }

// DashboardHeaderZeroStateValidationError is the validation error returned by
// DashboardHeaderZeroState.Validate if the designated constraints aren't met.
type DashboardHeaderZeroStateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DashboardHeaderZeroStateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DashboardHeaderZeroStateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DashboardHeaderZeroStateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DashboardHeaderZeroStateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DashboardHeaderZeroStateValidationError) ErrorName() string {
	return "DashboardHeaderZeroStateValidationError"
}

// Error satisfies the builtin error interface
func (e DashboardHeaderZeroStateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDashboardHeaderZeroState.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DashboardHeaderZeroStateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DashboardHeaderZeroStateValidationError{}

// Validate checks the field values on DashboardHeaderConnectedState with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DashboardHeaderConnectedState) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DashboardHeaderConnectedState with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DashboardHeaderConnectedStateMultiError, or nil if none found.
func (m *DashboardHeaderConnectedState) ValidateAll() error {
	return m.validate(true)
}

func (m *DashboardHeaderConnectedState) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DashboardHeaderConnectedStateValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DashboardHeaderConnectedStateValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DashboardHeaderConnectedStateValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWealthDisplayDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DashboardHeaderConnectedStateValidationError{
					field:  "WealthDisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DashboardHeaderConnectedStateValidationError{
					field:  "WealthDisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWealthDisplayDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DashboardHeaderConnectedStateValidationError{
				field:  "WealthDisplayDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DashboardHeaderConnectedStateValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DashboardHeaderConnectedStateValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DashboardHeaderConnectedStateValidationError{
					field:  fmt.Sprintf("Tags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetVisibilityDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DashboardHeaderConnectedStateValidationError{
					field:  "VisibilityDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DashboardHeaderConnectedStateValidationError{
					field:  "VisibilityDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVisibilityDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DashboardHeaderConnectedStateValidationError{
				field:  "VisibilityDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DashboardHeaderConnectedStateMultiError(errors)
	}

	return nil
}

// DashboardHeaderConnectedStateMultiError is an error wrapping multiple
// validation errors returned by DashboardHeaderConnectedState.ValidateAll()
// if the designated constraints aren't met.
type DashboardHeaderConnectedStateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DashboardHeaderConnectedStateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DashboardHeaderConnectedStateMultiError) AllErrors() []error { return m }

// DashboardHeaderConnectedStateValidationError is the validation error
// returned by DashboardHeaderConnectedState.Validate if the designated
// constraints aren't met.
type DashboardHeaderConnectedStateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DashboardHeaderConnectedStateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DashboardHeaderConnectedStateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DashboardHeaderConnectedStateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DashboardHeaderConnectedStateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DashboardHeaderConnectedStateValidationError) ErrorName() string {
	return "DashboardHeaderConnectedStateValidationError"
}

// Error satisfies the builtin error interface
func (e DashboardHeaderConnectedStateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDashboardHeaderConnectedState.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DashboardHeaderConnectedStateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DashboardHeaderConnectedStateValidationError{}

// Validate checks the field values on SectionHeaderGenericState with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SectionHeaderGenericState) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SectionHeaderGenericState with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SectionHeaderGenericStateMultiError, or nil if none found.
func (m *SectionHeaderGenericState) ValidateAll() error {
	return m.validate(true)
}

func (m *SectionHeaderGenericState) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionHeaderGenericStateValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionHeaderGenericStateValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionHeaderGenericStateValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWealthDisplayDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionHeaderGenericStateValidationError{
					field:  "WealthDisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionHeaderGenericStateValidationError{
					field:  "WealthDisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWealthDisplayDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionHeaderGenericStateValidationError{
				field:  "WealthDisplayDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToggleComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionHeaderGenericStateValidationError{
					field:  "ToggleComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionHeaderGenericStateValidationError{
					field:  "ToggleComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToggleComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionHeaderGenericStateValidationError{
				field:  "ToggleComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SectionHeaderGenericStateMultiError(errors)
	}

	return nil
}

// SectionHeaderGenericStateMultiError is an error wrapping multiple validation
// errors returned by SectionHeaderGenericState.ValidateAll() if the
// designated constraints aren't met.
type SectionHeaderGenericStateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SectionHeaderGenericStateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SectionHeaderGenericStateMultiError) AllErrors() []error { return m }

// SectionHeaderGenericStateValidationError is the validation error returned by
// SectionHeaderGenericState.Validate if the designated constraints aren't met.
type SectionHeaderGenericStateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SectionHeaderGenericStateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SectionHeaderGenericStateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SectionHeaderGenericStateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SectionHeaderGenericStateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SectionHeaderGenericStateValidationError) ErrorName() string {
	return "SectionHeaderGenericStateValidationError"
}

// Error satisfies the builtin error interface
func (e SectionHeaderGenericStateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSectionHeaderGenericState.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SectionHeaderGenericStateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SectionHeaderGenericStateValidationError{}

// Validate checks the field values on WidgetV2 with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WidgetV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WidgetV2 with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WidgetV2MultiError, or nil
// if none found.
func (m *WidgetV2) ValidateAll() error {
	return m.validate(true)
}

func (m *WidgetV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Type

	// no validation rules for CacheControl

	// no validation rules for State

	// no validation rules for WidgetAnalyticsPayload

	if all {
		switch v := interface{}(m.GetTotalValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WidgetV2ValidationError{
					field:  "TotalValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WidgetV2ValidationError{
					field:  "TotalValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WidgetV2ValidationError{
				field:  "TotalValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Params.(type) {
	case *WidgetV2_WealthLandingWidgetParams:
		if v == nil {
			err := WidgetV2ValidationError{
				field:  "Params",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWealthLandingWidgetParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WidgetV2ValidationError{
						field:  "WealthLandingWidgetParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WidgetV2ValidationError{
						field:  "WealthLandingWidgetParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWealthLandingWidgetParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WidgetV2ValidationError{
					field:  "WealthLandingWidgetParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WidgetV2_WbFeatureEntryPointWidgetParams:
		if v == nil {
			err := WidgetV2ValidationError{
				field:  "Params",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWbFeatureEntryPointWidgetParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WidgetV2ValidationError{
						field:  "WbFeatureEntryPointWidgetParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WidgetV2ValidationError{
						field:  "WbFeatureEntryPointWidgetParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWbFeatureEntryPointWidgetParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WidgetV2ValidationError{
					field:  "WbFeatureEntryPointWidgetParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return WidgetV2MultiError(errors)
	}

	return nil
}

// WidgetV2MultiError is an error wrapping multiple validation errors returned
// by WidgetV2.ValidateAll() if the designated constraints aren't met.
type WidgetV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WidgetV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WidgetV2MultiError) AllErrors() []error { return m }

// WidgetV2ValidationError is the validation error returned by
// WidgetV2.Validate if the designated constraints aren't met.
type WidgetV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WidgetV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WidgetV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WidgetV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WidgetV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WidgetV2ValidationError) ErrorName() string { return "WidgetV2ValidationError" }

// Error satisfies the builtin error interface
func (e WidgetV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWidgetV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WidgetV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WidgetV2ValidationError{}

// Validate checks the field values on WealthBuilderLandingWidgetParams with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *WealthBuilderLandingWidgetParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WealthBuilderLandingWidgetParams with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// WealthBuilderLandingWidgetParamsMultiError, or nil if none found.
func (m *WealthBuilderLandingWidgetParams) ValidateAll() error {
	return m.validate(true)
}

func (m *WealthBuilderLandingWidgetParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Weight

	if all {
		switch v := interface{}(m.GetWidgetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "WidgetTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "WidgetTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWidgetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingWidgetParamsValidationError{
				field:  "WidgetTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWealthDisplayDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "WealthDisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "WealthDisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWealthDisplayDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingWidgetParamsValidationError{
				field:  "WealthDisplayDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "RightTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "RightTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingWidgetParamsValidationError{
				field:  "RightTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "BottomTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "BottomTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingWidgetParamsValidationError{
				field:  "BottomTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomRightIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "BottomRightIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "BottomRightIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomRightIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingWidgetParamsValidationError{
				field:  "BottomRightIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingWidgetParamsValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingWidgetParamsValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBorderProperty()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "BorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "BorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderProperty()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingWidgetParamsValidationError{
				field:  "BorderProperty",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTopRightNudge()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "TopRightNudge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "TopRightNudge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopRightNudge()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingWidgetParamsValidationError{
				field:  "TopRightNudge",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSduiBorderProperty()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "SduiBorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthBuilderLandingWidgetParamsValidationError{
					field:  "SduiBorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSduiBorderProperty()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthBuilderLandingWidgetParamsValidationError{
				field:  "SduiBorderProperty",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WealthBuilderLandingWidgetParamsMultiError(errors)
	}

	return nil
}

// WealthBuilderLandingWidgetParamsMultiError is an error wrapping multiple
// validation errors returned by
// WealthBuilderLandingWidgetParams.ValidateAll() if the designated
// constraints aren't met.
type WealthBuilderLandingWidgetParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WealthBuilderLandingWidgetParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WealthBuilderLandingWidgetParamsMultiError) AllErrors() []error { return m }

// WealthBuilderLandingWidgetParamsValidationError is the validation error
// returned by WealthBuilderLandingWidgetParams.Validate if the designated
// constraints aren't met.
type WealthBuilderLandingWidgetParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WealthBuilderLandingWidgetParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WealthBuilderLandingWidgetParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WealthBuilderLandingWidgetParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WealthBuilderLandingWidgetParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WealthBuilderLandingWidgetParamsValidationError) ErrorName() string {
	return "WealthBuilderLandingWidgetParamsValidationError"
}

// Error satisfies the builtin error interface
func (e WealthBuilderLandingWidgetParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWealthBuilderLandingWidgetParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WealthBuilderLandingWidgetParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WealthBuilderLandingWidgetParamsValidationError{}

// Validate checks the field values on WbFeatureEntryPointWidgetParams with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WbFeatureEntryPointWidgetParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WbFeatureEntryPointWidgetParams with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// WbFeatureEntryPointWidgetParamsMultiError, or nil if none found.
func (m *WbFeatureEntryPointWidgetParams) ValidateAll() error {
	return m.validate(true)
}

func (m *WbFeatureEntryPointWidgetParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Weight

	if all {
		switch v := interface{}(m.GetCenterImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WbFeatureEntryPointWidgetParamsValidationError{
					field:  "CenterImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WbFeatureEntryPointWidgetParamsValidationError{
					field:  "CenterImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCenterImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WbFeatureEntryPointWidgetParamsValidationError{
				field:  "CenterImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WbFeatureEntryPointWidgetParamsValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WbFeatureEntryPointWidgetParamsValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WbFeatureEntryPointWidgetParamsValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSduiBorderProperty()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WbFeatureEntryPointWidgetParamsValidationError{
					field:  "SduiBorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WbFeatureEntryPointWidgetParamsValidationError{
					field:  "SduiBorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSduiBorderProperty()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WbFeatureEntryPointWidgetParamsValidationError{
				field:  "SduiBorderProperty",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WbFeatureEntryPointWidgetParamsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WbFeatureEntryPointWidgetParamsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WbFeatureEntryPointWidgetParamsValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WbFeatureEntryPointWidgetParamsMultiError(errors)
	}

	return nil
}

// WbFeatureEntryPointWidgetParamsMultiError is an error wrapping multiple
// validation errors returned by WbFeatureEntryPointWidgetParams.ValidateAll()
// if the designated constraints aren't met.
type WbFeatureEntryPointWidgetParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WbFeatureEntryPointWidgetParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WbFeatureEntryPointWidgetParamsMultiError) AllErrors() []error { return m }

// WbFeatureEntryPointWidgetParamsValidationError is the validation error
// returned by WbFeatureEntryPointWidgetParams.Validate if the designated
// constraints aren't met.
type WbFeatureEntryPointWidgetParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WbFeatureEntryPointWidgetParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WbFeatureEntryPointWidgetParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WbFeatureEntryPointWidgetParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WbFeatureEntryPointWidgetParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WbFeatureEntryPointWidgetParamsValidationError) ErrorName() string {
	return "WbFeatureEntryPointWidgetParamsValidationError"
}

// Error satisfies the builtin error interface
func (e WbFeatureEntryPointWidgetParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWbFeatureEntryPointWidgetParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WbFeatureEntryPointWidgetParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WbFeatureEntryPointWidgetParamsValidationError{}

// Validate checks the field values on CollapsibleDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollapsibleDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollapsibleDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CollapsibleDetailsMultiError, or nil if none found.
func (m *CollapsibleDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CollapsibleDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetShowMoreCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollapsibleDetailsValidationError{
					field:  "ShowMoreCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollapsibleDetailsValidationError{
					field:  "ShowMoreCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShowMoreCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollapsibleDetailsValidationError{
				field:  "ShowMoreCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetShowLessCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollapsibleDetailsValidationError{
					field:  "ShowLessCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollapsibleDetailsValidationError{
					field:  "ShowLessCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShowLessCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollapsibleDetailsValidationError{
				field:  "ShowLessCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CollapsibleDetailsMultiError(errors)
	}

	return nil
}

// CollapsibleDetailsMultiError is an error wrapping multiple validation errors
// returned by CollapsibleDetails.ValidateAll() if the designated constraints
// aren't met.
type CollapsibleDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollapsibleDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollapsibleDetailsMultiError) AllErrors() []error { return m }

// CollapsibleDetailsValidationError is the validation error returned by
// CollapsibleDetails.Validate if the designated constraints aren't met.
type CollapsibleDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollapsibleDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollapsibleDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollapsibleDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollapsibleDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollapsibleDetailsValidationError) ErrorName() string {
	return "CollapsibleDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CollapsibleDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollapsibleDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollapsibleDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollapsibleDetailsValidationError{}

// Validate checks the field values on WealthDisplayDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WealthDisplayDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WealthDisplayDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WealthDisplayDetailsMultiError, or nil if none found.
func (m *WealthDisplayDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *WealthDisplayDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCurrencySymbol()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthDisplayDetailsValidationError{
					field:  "CurrencySymbol",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthDisplayDetailsValidationError{
					field:  "CurrencySymbol",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrencySymbol()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthDisplayDetailsValidationError{
				field:  "CurrencySymbol",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalDisplayValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthDisplayDetailsValidationError{
					field:  "TotalDisplayValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthDisplayDetailsValidationError{
					field:  "TotalDisplayValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalDisplayValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthDisplayDetailsValidationError{
				field:  "TotalDisplayValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthDisplayDetailsValidationError{
					field:  "TotalValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthDisplayDetailsValidationError{
					field:  "TotalValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthDisplayDetailsValidationError{
				field:  "TotalValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WealthDisplayDetailsMultiError(errors)
	}

	return nil
}

// WealthDisplayDetailsMultiError is an error wrapping multiple validation
// errors returned by WealthDisplayDetails.ValidateAll() if the designated
// constraints aren't met.
type WealthDisplayDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WealthDisplayDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WealthDisplayDetailsMultiError) AllErrors() []error { return m }

// WealthDisplayDetailsValidationError is the validation error returned by
// WealthDisplayDetails.Validate if the designated constraints aren't met.
type WealthDisplayDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WealthDisplayDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WealthDisplayDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WealthDisplayDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WealthDisplayDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WealthDisplayDetailsValidationError) ErrorName() string {
	return "WealthDisplayDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e WealthDisplayDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWealthDisplayDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WealthDisplayDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WealthDisplayDetailsValidationError{}

// Validate checks the field values on VisibilityDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VisibilityDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VisibilityDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VisibilityDetailsMultiError, or nil if none found.
func (m *VisibilityDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *VisibilityDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHide()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VisibilityDetailsValidationError{
					field:  "Hide",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VisibilityDetailsValidationError{
					field:  "Hide",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHide()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VisibilityDetailsValidationError{
				field:  "Hide",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetShow()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VisibilityDetailsValidationError{
					field:  "Show",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VisibilityDetailsValidationError{
					field:  "Show",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShow()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VisibilityDetailsValidationError{
				field:  "Show",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VisibilityDetailsMultiError(errors)
	}

	return nil
}

// VisibilityDetailsMultiError is an error wrapping multiple validation errors
// returned by VisibilityDetails.ValidateAll() if the designated constraints
// aren't met.
type VisibilityDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VisibilityDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VisibilityDetailsMultiError) AllErrors() []error { return m }

// VisibilityDetailsValidationError is the validation error returned by
// VisibilityDetails.Validate if the designated constraints aren't met.
type VisibilityDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VisibilityDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VisibilityDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VisibilityDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VisibilityDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VisibilityDetailsValidationError) ErrorName() string {
	return "VisibilityDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e VisibilityDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVisibilityDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VisibilityDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VisibilityDetailsValidationError{}

// Validate checks the field values on InteractiveTalkToAiComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InteractiveTalkToAiComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InteractiveTalkToAiComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InteractiveTalkToAiComponentMultiError, or nil if none found.
func (m *InteractiveTalkToAiComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *InteractiveTalkToAiComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCloseIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "CloseIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "CloseIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCloseIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponentValidationError{
				field:  "CloseIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIntroSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "IntroSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "IntroSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIntroSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponentValidationError{
				field:  "IntroSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "HeaderSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "HeaderSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponentValidationError{
				field:  "HeaderSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetChooseAnApp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "ChooseAnApp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "ChooseAnApp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChooseAnApp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponentValidationError{
				field:  "ChooseAnApp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDropdownSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "DropdownSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "DropdownSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDropdownSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponentValidationError{
				field:  "DropdownSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAiOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InteractiveTalkToAiComponentValidationError{
						field:  fmt.Sprintf("AiOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InteractiveTalkToAiComponentValidationError{
						field:  fmt.Sprintf("AiOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InteractiveTalkToAiComponentValidationError{
					field:  fmt.Sprintf("AiOptions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetExportCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "ExportCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "ExportCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExportCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponentValidationError{
				field:  "ExportCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisclaimerText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "DisclaimerText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "DisclaimerText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisclaimerText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponentValidationError{
				field:  "DisclaimerText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "BgImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "BgImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponentValidationError{
				field:  "BgImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNestedBottomSheetBgColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "NestedBottomSheetBgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponentValidationError{
					field:  "NestedBottomSheetBgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNestedBottomSheetBgColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponentValidationError{
				field:  "NestedBottomSheetBgColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InteractiveTalkToAiComponentMultiError(errors)
	}

	return nil
}

// InteractiveTalkToAiComponentMultiError is an error wrapping multiple
// validation errors returned by InteractiveTalkToAiComponent.ValidateAll() if
// the designated constraints aren't met.
type InteractiveTalkToAiComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InteractiveTalkToAiComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InteractiveTalkToAiComponentMultiError) AllErrors() []error { return m }

// InteractiveTalkToAiComponentValidationError is the validation error returned
// by InteractiveTalkToAiComponent.Validate if the designated constraints
// aren't met.
type InteractiveTalkToAiComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InteractiveTalkToAiComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InteractiveTalkToAiComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InteractiveTalkToAiComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InteractiveTalkToAiComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InteractiveTalkToAiComponentValidationError) ErrorName() string {
	return "InteractiveTalkToAiComponentValidationError"
}

// Error satisfies the builtin error interface
func (e InteractiveTalkToAiComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInteractiveTalkToAiComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InteractiveTalkToAiComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InteractiveTalkToAiComponentValidationError{}

// Validate checks the field values on
// InteractiveTalkToAiComponent_HeaderSection with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InteractiveTalkToAiComponent_HeaderSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InteractiveTalkToAiComponent_HeaderSection with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// InteractiveTalkToAiComponent_HeaderSectionMultiError, or nil if none found.
func (m *InteractiveTalkToAiComponent_HeaderSection) ValidateAll() error {
	return m.validate(true)
}

func (m *InteractiveTalkToAiComponent_HeaderSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_HeaderSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_HeaderSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponent_HeaderSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_HeaderSectionValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_HeaderSectionValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponent_HeaderSectionValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSuggestedQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InteractiveTalkToAiComponent_HeaderSectionValidationError{
						field:  fmt.Sprintf("SuggestedQuestions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InteractiveTalkToAiComponent_HeaderSectionValidationError{
						field:  fmt.Sprintf("SuggestedQuestions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InteractiveTalkToAiComponent_HeaderSectionValidationError{
					field:  fmt.Sprintf("SuggestedQuestions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return InteractiveTalkToAiComponent_HeaderSectionMultiError(errors)
	}

	return nil
}

// InteractiveTalkToAiComponent_HeaderSectionMultiError is an error wrapping
// multiple validation errors returned by
// InteractiveTalkToAiComponent_HeaderSection.ValidateAll() if the designated
// constraints aren't met.
type InteractiveTalkToAiComponent_HeaderSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InteractiveTalkToAiComponent_HeaderSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InteractiveTalkToAiComponent_HeaderSectionMultiError) AllErrors() []error { return m }

// InteractiveTalkToAiComponent_HeaderSectionValidationError is the validation
// error returned by InteractiveTalkToAiComponent_HeaderSection.Validate if
// the designated constraints aren't met.
type InteractiveTalkToAiComponent_HeaderSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InteractiveTalkToAiComponent_HeaderSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InteractiveTalkToAiComponent_HeaderSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InteractiveTalkToAiComponent_HeaderSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InteractiveTalkToAiComponent_HeaderSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InteractiveTalkToAiComponent_HeaderSectionValidationError) ErrorName() string {
	return "InteractiveTalkToAiComponent_HeaderSectionValidationError"
}

// Error satisfies the builtin error interface
func (e InteractiveTalkToAiComponent_HeaderSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInteractiveTalkToAiComponent_HeaderSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InteractiveTalkToAiComponent_HeaderSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InteractiveTalkToAiComponent_HeaderSectionValidationError{}

// Validate checks the field values on InteractiveTalkToAiComponent_FileItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InteractiveTalkToAiComponent_FileItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InteractiveTalkToAiComponent_FileItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InteractiveTalkToAiComponent_FileItemMultiError, or nil if none found.
func (m *InteractiveTalkToAiComponent_FileItem) ValidateAll() error {
	return m.validate(true)
}

func (m *InteractiveTalkToAiComponent_FileItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFileComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_FileItemValidationError{
					field:  "FileComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_FileItemValidationError{
					field:  "FileComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponent_FileItemValidationError{
				field:  "FileComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NetWorthDataFileType

	// no validation rules for IsSelected

	if len(errors) > 0 {
		return InteractiveTalkToAiComponent_FileItemMultiError(errors)
	}

	return nil
}

// InteractiveTalkToAiComponent_FileItemMultiError is an error wrapping
// multiple validation errors returned by
// InteractiveTalkToAiComponent_FileItem.ValidateAll() if the designated
// constraints aren't met.
type InteractiveTalkToAiComponent_FileItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InteractiveTalkToAiComponent_FileItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InteractiveTalkToAiComponent_FileItemMultiError) AllErrors() []error { return m }

// InteractiveTalkToAiComponent_FileItemValidationError is the validation error
// returned by InteractiveTalkToAiComponent_FileItem.Validate if the
// designated constraints aren't met.
type InteractiveTalkToAiComponent_FileItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InteractiveTalkToAiComponent_FileItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InteractiveTalkToAiComponent_FileItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InteractiveTalkToAiComponent_FileItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InteractiveTalkToAiComponent_FileItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InteractiveTalkToAiComponent_FileItemValidationError) ErrorName() string {
	return "InteractiveTalkToAiComponent_FileItemValidationError"
}

// Error satisfies the builtin error interface
func (e InteractiveTalkToAiComponent_FileItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInteractiveTalkToAiComponent_FileItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InteractiveTalkToAiComponent_FileItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InteractiveTalkToAiComponent_FileItemValidationError{}

// Validate checks the field values on
// InteractiveTalkToAiComponent_DropdownSection with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InteractiveTalkToAiComponent_DropdownSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InteractiveTalkToAiComponent_DropdownSection with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// InteractiveTalkToAiComponent_DropdownSectionMultiError, or nil if none found.
func (m *InteractiveTalkToAiComponent_DropdownSection) ValidateAll() error {
	return m.validate(true)
}

func (m *InteractiveTalkToAiComponent_DropdownSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_DropdownSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_DropdownSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponent_DropdownSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_DropdownSectionValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_DropdownSectionValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponent_DropdownSectionValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFileItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InteractiveTalkToAiComponent_DropdownSectionValidationError{
						field:  fmt.Sprintf("FileItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InteractiveTalkToAiComponent_DropdownSectionValidationError{
						field:  fmt.Sprintf("FileItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InteractiveTalkToAiComponent_DropdownSectionValidationError{
					field:  fmt.Sprintf("FileItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBgColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_DropdownSectionValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_DropdownSectionValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponent_DropdownSectionValidationError{
				field:  "BgColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SelectAtLeastOneFileWarningMessage

	if all {
		switch v := interface{}(m.GetCloseCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_DropdownSectionValidationError{
					field:  "CloseCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_DropdownSectionValidationError{
					field:  "CloseCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCloseCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponent_DropdownSectionValidationError{
				field:  "CloseCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConfirmCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_DropdownSectionValidationError{
					field:  "ConfirmCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_DropdownSectionValidationError{
					field:  "ConfirmCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfirmCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponent_DropdownSectionValidationError{
				field:  "ConfirmCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InteractiveTalkToAiComponent_DropdownSectionMultiError(errors)
	}

	return nil
}

// InteractiveTalkToAiComponent_DropdownSectionMultiError is an error wrapping
// multiple validation errors returned by
// InteractiveTalkToAiComponent_DropdownSection.ValidateAll() if the
// designated constraints aren't met.
type InteractiveTalkToAiComponent_DropdownSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InteractiveTalkToAiComponent_DropdownSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InteractiveTalkToAiComponent_DropdownSectionMultiError) AllErrors() []error { return m }

// InteractiveTalkToAiComponent_DropdownSectionValidationError is the
// validation error returned by
// InteractiveTalkToAiComponent_DropdownSection.Validate if the designated
// constraints aren't met.
type InteractiveTalkToAiComponent_DropdownSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InteractiveTalkToAiComponent_DropdownSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InteractiveTalkToAiComponent_DropdownSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InteractiveTalkToAiComponent_DropdownSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InteractiveTalkToAiComponent_DropdownSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InteractiveTalkToAiComponent_DropdownSectionValidationError) ErrorName() string {
	return "InteractiveTalkToAiComponent_DropdownSectionValidationError"
}

// Error satisfies the builtin error interface
func (e InteractiveTalkToAiComponent_DropdownSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInteractiveTalkToAiComponent_DropdownSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InteractiveTalkToAiComponent_DropdownSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InteractiveTalkToAiComponent_DropdownSectionValidationError{}

// Validate checks the field values on InteractiveTalkToAiComponent_AiOption
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InteractiveTalkToAiComponent_AiOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InteractiveTalkToAiComponent_AiOption
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InteractiveTalkToAiComponent_AiOptionMultiError, or nil if none found.
func (m *InteractiveTalkToAiComponent_AiOption) ValidateAll() error {
	return m.validate(true)
}

func (m *InteractiveTalkToAiComponent_AiOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_AiOptionValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_AiOptionValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponent_AiOptionValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_AiOptionValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiComponent_AiOptionValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiComponent_AiOptionValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsSelected

	// no validation rules for AiAppIdentifier

	if len(errors) > 0 {
		return InteractiveTalkToAiComponent_AiOptionMultiError(errors)
	}

	return nil
}

// InteractiveTalkToAiComponent_AiOptionMultiError is an error wrapping
// multiple validation errors returned by
// InteractiveTalkToAiComponent_AiOption.ValidateAll() if the designated
// constraints aren't met.
type InteractiveTalkToAiComponent_AiOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InteractiveTalkToAiComponent_AiOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InteractiveTalkToAiComponent_AiOptionMultiError) AllErrors() []error { return m }

// InteractiveTalkToAiComponent_AiOptionValidationError is the validation error
// returned by InteractiveTalkToAiComponent_AiOption.Validate if the
// designated constraints aren't met.
type InteractiveTalkToAiComponent_AiOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InteractiveTalkToAiComponent_AiOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InteractiveTalkToAiComponent_AiOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InteractiveTalkToAiComponent_AiOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InteractiveTalkToAiComponent_AiOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InteractiveTalkToAiComponent_AiOptionValidationError) ErrorName() string {
	return "InteractiveTalkToAiComponent_AiOptionValidationError"
}

// Error satisfies the builtin error interface
func (e InteractiveTalkToAiComponent_AiOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInteractiveTalkToAiComponent_AiOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InteractiveTalkToAiComponent_AiOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InteractiveTalkToAiComponent_AiOptionValidationError{}
