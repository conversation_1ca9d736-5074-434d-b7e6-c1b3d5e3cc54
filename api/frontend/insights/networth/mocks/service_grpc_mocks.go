// Code generated by MockGen. DO NOT EDIT.
// Source: api/frontend/insights/networth/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	networth "github.com/epifi/gamma/api/frontend/insights/networth"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockNetWorthClient is a mock of NetWorthClient interface.
type MockNetWorthClient struct {
	ctrl     *gomock.Controller
	recorder *MockNetWorthClientMockRecorder
}

// MockNetWorthClientMockRecorder is the mock recorder for MockNetWorthClient.
type MockNetWorthClientMockRecorder struct {
	mock *MockNetWorthClient
}

// NewMockNetWorthClient creates a new mock instance.
func NewMockNetWorthClient(ctrl *gomock.Controller) *MockNetWorthClient {
	mock := &MockNetWorthClient{ctrl: ctrl}
	mock.recorder = &MockNetWorthClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetWorthClient) EXPECT() *MockNetWorthClientMockRecorder {
	return m.recorder
}

// DeleteManualAsset mocks base method.
func (m *MockNetWorthClient) DeleteManualAsset(ctx context.Context, in *networth.DeleteManualAssetRequest, opts ...grpc.CallOption) (*networth.DeleteManualAssetResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteManualAsset", varargs...)
	ret0, _ := ret[0].(*networth.DeleteManualAssetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteManualAsset indicates an expected call of DeleteManualAsset.
func (mr *MockNetWorthClientMockRecorder) DeleteManualAsset(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteManualAsset", reflect.TypeOf((*MockNetWorthClient)(nil).DeleteManualAsset), varargs...)
}

// DepositDeclaration mocks base method.
func (m *MockNetWorthClient) DepositDeclaration(ctx context.Context, in *networth.DepositDeclarationRequest, opts ...grpc.CallOption) (*networth.DepositDeclarationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DepositDeclaration", varargs...)
	ret0, _ := ret[0].(*networth.DepositDeclarationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DepositDeclaration indicates an expected call of DepositDeclaration.
func (mr *MockNetWorthClientMockRecorder) DepositDeclaration(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DepositDeclaration", reflect.TypeOf((*MockNetWorthClient)(nil).DepositDeclaration), varargs...)
}

// GetAssetImportFlowStatus mocks base method.
func (m *MockNetWorthClient) GetAssetImportFlowStatus(ctx context.Context, in *networth.GetAssetImportFlowStatusRequest, opts ...grpc.CallOption) (*networth.GetAssetImportFlowStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAssetImportFlowStatus", varargs...)
	ret0, _ := ret[0].(*networth.GetAssetImportFlowStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetImportFlowStatus indicates an expected call of GetAssetImportFlowStatus.
func (mr *MockNetWorthClientMockRecorder) GetAssetImportFlowStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetImportFlowStatus", reflect.TypeOf((*MockNetWorthClient)(nil).GetAssetImportFlowStatus), varargs...)
}

// GetConnectMoreAssetsScreen mocks base method.
func (m *MockNetWorthClient) GetConnectMoreAssetsScreen(ctx context.Context, in *networth.GetConnectMoreAssetsScreenRequest, opts ...grpc.CallOption) (*networth.GetConnectMoreAssetsScreenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConnectMoreAssetsScreen", varargs...)
	ret0, _ := ret[0].(*networth.GetConnectMoreAssetsScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConnectMoreAssetsScreen indicates an expected call of GetConnectMoreAssetsScreen.
func (mr *MockNetWorthClientMockRecorder) GetConnectMoreAssetsScreen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConnectMoreAssetsScreen", reflect.TypeOf((*MockNetWorthClient)(nil).GetConnectMoreAssetsScreen), varargs...)
}

// GetCreditScoreSummaryForHome mocks base method.
func (m *MockNetWorthClient) GetCreditScoreSummaryForHome(ctx context.Context, in *networth.GetCreditScoreSummaryForHomeRequest, opts ...grpc.CallOption) (*networth.GetCreditScoreSummaryForHomeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCreditScoreSummaryForHome", varargs...)
	ret0, _ := ret[0].(*networth.GetCreditScoreSummaryForHomeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCreditScoreSummaryForHome indicates an expected call of GetCreditScoreSummaryForHome.
func (mr *MockNetWorthClientMockRecorder) GetCreditScoreSummaryForHome(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCreditScoreSummaryForHome", reflect.TypeOf((*MockNetWorthClient)(nil).GetCreditScoreSummaryForHome), varargs...)
}

// GetEpfSummaryForHome mocks base method.
func (m *MockNetWorthClient) GetEpfSummaryForHome(ctx context.Context, in *networth.GetEpfSummaryForHomeRequest, opts ...grpc.CallOption) (*networth.GetEpfSummaryForHomeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEpfSummaryForHome", varargs...)
	ret0, _ := ret[0].(*networth.GetEpfSummaryForHomeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEpfSummaryForHome indicates an expected call of GetEpfSummaryForHome.
func (mr *MockNetWorthClientMockRecorder) GetEpfSummaryForHome(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEpfSummaryForHome", reflect.TypeOf((*MockNetWorthClient)(nil).GetEpfSummaryForHome), varargs...)
}

// GetInteractiveTalkToAiScreen mocks base method.
func (m *MockNetWorthClient) GetInteractiveTalkToAiScreen(ctx context.Context, in *networth.GetInteractiveTalkToAiScreenRequest, opts ...grpc.CallOption) (*networth.GetInteractiveTalkToAiScreenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInteractiveTalkToAiScreen", varargs...)
	ret0, _ := ret[0].(*networth.GetInteractiveTalkToAiScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractiveTalkToAiScreen indicates an expected call of GetInteractiveTalkToAiScreen.
func (mr *MockNetWorthClientMockRecorder) GetInteractiveTalkToAiScreen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractiveTalkToAiScreen", reflect.TypeOf((*MockNetWorthClient)(nil).GetInteractiveTalkToAiScreen), varargs...)
}

// GetMagicImportIdeas mocks base method.
func (m *MockNetWorthClient) GetMagicImportIdeas(ctx context.Context, in *networth.GetMagicImportIdeasRequest, opts ...grpc.CallOption) (*networth.GetMagicImportIdeasResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMagicImportIdeas", varargs...)
	ret0, _ := ret[0].(*networth.GetMagicImportIdeasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicImportIdeas indicates an expected call of GetMagicImportIdeas.
func (mr *MockNetWorthClientMockRecorder) GetMagicImportIdeas(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicImportIdeas", reflect.TypeOf((*MockNetWorthClient)(nil).GetMagicImportIdeas), varargs...)
}

// GetMagicImportScreenOptions mocks base method.
func (m *MockNetWorthClient) GetMagicImportScreenOptions(ctx context.Context, in *networth.GetMagicImportScreenOptionsRequest, opts ...grpc.CallOption) (*networth.GetMagicImportScreenOptionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMagicImportScreenOptions", varargs...)
	ret0, _ := ret[0].(*networth.GetMagicImportScreenOptionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicImportScreenOptions indicates an expected call of GetMagicImportScreenOptions.
func (mr *MockNetWorthClientMockRecorder) GetMagicImportScreenOptions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicImportScreenOptions", reflect.TypeOf((*MockNetWorthClient)(nil).GetMagicImportScreenOptions), varargs...)
}

// GetManualAssetDashboard mocks base method.
func (m *MockNetWorthClient) GetManualAssetDashboard(ctx context.Context, in *networth.GetManualAssetDashboardRequest, opts ...grpc.CallOption) (*networth.GetManualAssetDashboardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetManualAssetDashboard", varargs...)
	ret0, _ := ret[0].(*networth.GetManualAssetDashboardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManualAssetDashboard indicates an expected call of GetManualAssetDashboard.
func (mr *MockNetWorthClientMockRecorder) GetManualAssetDashboard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManualAssetDashboard", reflect.TypeOf((*MockNetWorthClient)(nil).GetManualAssetDashboard), varargs...)
}

// GetManualFormConfig mocks base method.
func (m *MockNetWorthClient) GetManualFormConfig(ctx context.Context, in *networth.GetManualFormConfigRequest, opts ...grpc.CallOption) (*networth.GetManualFormConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetManualFormConfig", varargs...)
	ret0, _ := ret[0].(*networth.GetManualFormConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManualFormConfig indicates an expected call of GetManualFormConfig.
func (mr *MockNetWorthClientMockRecorder) GetManualFormConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManualFormConfig", reflect.TypeOf((*MockNetWorthClient)(nil).GetManualFormConfig), varargs...)
}

// GetMfSummaryForHome mocks base method.
func (m *MockNetWorthClient) GetMfSummaryForHome(ctx context.Context, in *networth.GetMfSummaryForHomeRequest, opts ...grpc.CallOption) (*networth.GetMfSummaryForHomeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMfSummaryForHome", varargs...)
	ret0, _ := ret[0].(*networth.GetMfSummaryForHomeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMfSummaryForHome indicates an expected call of GetMfSummaryForHome.
func (mr *MockNetWorthClientMockRecorder) GetMfSummaryForHome(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMfSummaryForHome", reflect.TypeOf((*MockNetWorthClient)(nil).GetMfSummaryForHome), varargs...)
}

// GetNetWorthDashboard mocks base method.
func (m *MockNetWorthClient) GetNetWorthDashboard(ctx context.Context, in *networth.GetNetWorthDashboardRequest, opts ...grpc.CallOption) (*networth.GetNetWorthDashboardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNetWorthDashboard", varargs...)
	ret0, _ := ret[0].(*networth.GetNetWorthDashboardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetWorthDashboard indicates an expected call of GetNetWorthDashboard.
func (mr *MockNetWorthClientMockRecorder) GetNetWorthDashboard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetWorthDashboard", reflect.TypeOf((*MockNetWorthClient)(nil).GetNetWorthDashboard), varargs...)
}

// GetNetWorthSummaryForHome mocks base method.
func (m *MockNetWorthClient) GetNetWorthSummaryForHome(ctx context.Context, in *networth.GetNetWorthSummaryForHomeRequest, opts ...grpc.CallOption) (*networth.GetNetWorthSummaryForHomeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNetWorthSummaryForHome", varargs...)
	ret0, _ := ret[0].(*networth.GetNetWorthSummaryForHomeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetWorthSummaryForHome indicates an expected call of GetNetWorthSummaryForHome.
func (mr *MockNetWorthClientMockRecorder) GetNetWorthSummaryForHome(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetWorthSummaryForHome", reflect.TypeOf((*MockNetWorthClient)(nil).GetNetWorthSummaryForHome), varargs...)
}

// GetNetworthDataFile mocks base method.
func (m *MockNetWorthClient) GetNetworthDataFile(ctx context.Context, in *networth.GetNetworthDataFileRequest, opts ...grpc.CallOption) (*networth.GetNetworthDataFileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNetworthDataFile", varargs...)
	ret0, _ := ret[0].(*networth.GetNetworthDataFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetworthDataFile indicates an expected call of GetNetworthDataFile.
func (mr *MockNetWorthClientMockRecorder) GetNetworthDataFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetworthDataFile", reflect.TypeOf((*MockNetWorthClient)(nil).GetNetworthDataFile), varargs...)
}

// GetNextNetWorthRefreshAction mocks base method.
func (m *MockNetWorthClient) GetNextNetWorthRefreshAction(ctx context.Context, in *networth.GetNextNetWorthRefreshActionRequest, opts ...grpc.CallOption) (*networth.GetNextNetWorthRefreshActionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNextNetWorthRefreshAction", varargs...)
	ret0, _ := ret[0].(*networth.GetNextNetWorthRefreshActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNextNetWorthRefreshAction indicates an expected call of GetNextNetWorthRefreshAction.
func (mr *MockNetWorthClientMockRecorder) GetNextNetWorthRefreshAction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNextNetWorthRefreshAction", reflect.TypeOf((*MockNetWorthClient)(nil).GetNextNetWorthRefreshAction), varargs...)
}

// GetWealthBuilderDashboardComponent mocks base method.
func (m *MockNetWorthClient) GetWealthBuilderDashboardComponent(ctx context.Context, in *networth.GetWealthBuilderDashboardComponentRequest, opts ...grpc.CallOption) (*networth.GetWealthBuilderDashboardComponentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWealthBuilderDashboardComponent", varargs...)
	ret0, _ := ret[0].(*networth.GetWealthBuilderDashboardComponentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthBuilderDashboardComponent indicates an expected call of GetWealthBuilderDashboardComponent.
func (mr *MockNetWorthClientMockRecorder) GetWealthBuilderDashboardComponent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthBuilderDashboardComponent", reflect.TypeOf((*MockNetWorthClient)(nil).GetWealthBuilderDashboardComponent), varargs...)
}

// GetWealthBuilderLandingPage mocks base method.
func (m *MockNetWorthClient) GetWealthBuilderLandingPage(ctx context.Context, in *networth.GetWealthBuilderLandingPageRequest, opts ...grpc.CallOption) (*networth.GetWealthBuilderLandingPageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWealthBuilderLandingPage", varargs...)
	ret0, _ := ret[0].(*networth.GetWealthBuilderLandingPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthBuilderLandingPage indicates an expected call of GetWealthBuilderLandingPage.
func (mr *MockNetWorthClientMockRecorder) GetWealthBuilderLandingPage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthBuilderLandingPage", reflect.TypeOf((*MockNetWorthClient)(nil).GetWealthBuilderLandingPage), varargs...)
}

// GetWealthSduiBottomSheetScreenOptions mocks base method.
func (m *MockNetWorthClient) GetWealthSduiBottomSheetScreenOptions(ctx context.Context, in *networth.GetWealthSduiBottomSheetScreenOptionsRequest, opts ...grpc.CallOption) (*networth.GetWealthSduiBottomSheetScreenOptionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWealthSduiBottomSheetScreenOptions", varargs...)
	ret0, _ := ret[0].(*networth.GetWealthSduiBottomSheetScreenOptionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthSduiBottomSheetScreenOptions indicates an expected call of GetWealthSduiBottomSheetScreenOptions.
func (mr *MockNetWorthClientMockRecorder) GetWealthSduiBottomSheetScreenOptions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthSduiBottomSheetScreenOptions", reflect.TypeOf((*MockNetWorthClient)(nil).GetWealthSduiBottomSheetScreenOptions), varargs...)
}

// MagicImportFiles mocks base method.
func (m *MockNetWorthClient) MagicImportFiles(ctx context.Context, in *networth.MagicImportFilesRequest, opts ...grpc.CallOption) (*networth.MagicImportFilesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MagicImportFiles", varargs...)
	ret0, _ := ret[0].(*networth.MagicImportFilesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MagicImportFiles indicates an expected call of MagicImportFiles.
func (mr *MockNetWorthClientMockRecorder) MagicImportFiles(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MagicImportFiles", reflect.TypeOf((*MockNetWorthClient)(nil).MagicImportFiles), varargs...)
}

// SearchAssetFormFieldOptions mocks base method.
func (m *MockNetWorthClient) SearchAssetFormFieldOptions(ctx context.Context, in *networth.SearchAssetFormFieldOptionsRequest, opts ...grpc.CallOption) (*networth.SearchAssetFormFieldOptionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchAssetFormFieldOptions", varargs...)
	ret0, _ := ret[0].(*networth.SearchAssetFormFieldOptionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAssetFormFieldOptions indicates an expected call of SearchAssetFormFieldOptions.
func (mr *MockNetWorthClientMockRecorder) SearchAssetFormFieldOptions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAssetFormFieldOptions", reflect.TypeOf((*MockNetWorthClient)(nil).SearchAssetFormFieldOptions), varargs...)
}

// SubmitManualForm mocks base method.
func (m *MockNetWorthClient) SubmitManualForm(ctx context.Context, in *networth.SubmitManualFormRequest, opts ...grpc.CallOption) (*networth.SubmitManualFormResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitManualForm", varargs...)
	ret0, _ := ret[0].(*networth.SubmitManualFormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitManualForm indicates an expected call of SubmitManualForm.
func (mr *MockNetWorthClientMockRecorder) SubmitManualForm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitManualForm", reflect.TypeOf((*MockNetWorthClient)(nil).SubmitManualForm), varargs...)
}

// SubmitManualForms mocks base method.
func (m *MockNetWorthClient) SubmitManualForms(ctx context.Context, in *networth.SubmitManualFormsRequest, opts ...grpc.CallOption) (*networth.SubmitManualFormsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitManualForms", varargs...)
	ret0, _ := ret[0].(*networth.SubmitManualFormsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitManualForms indicates an expected call of SubmitManualForms.
func (mr *MockNetWorthClientMockRecorder) SubmitManualForms(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitManualForms", reflect.TypeOf((*MockNetWorthClient)(nil).SubmitManualForms), varargs...)
}

// UpdateManualAssets mocks base method.
func (m *MockNetWorthClient) UpdateManualAssets(ctx context.Context, in *networth.UpdateManualAssetsRequest, opts ...grpc.CallOption) (*networth.UpdateManualAssetsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateManualAssets", varargs...)
	ret0, _ := ret[0].(*networth.UpdateManualAssetsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateManualAssets indicates an expected call of UpdateManualAssets.
func (mr *MockNetWorthClientMockRecorder) UpdateManualAssets(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateManualAssets", reflect.TypeOf((*MockNetWorthClient)(nil).UpdateManualAssets), varargs...)
}

// MockNetWorthServer is a mock of NetWorthServer interface.
type MockNetWorthServer struct {
	ctrl     *gomock.Controller
	recorder *MockNetWorthServerMockRecorder
}

// MockNetWorthServerMockRecorder is the mock recorder for MockNetWorthServer.
type MockNetWorthServerMockRecorder struct {
	mock *MockNetWorthServer
}

// NewMockNetWorthServer creates a new mock instance.
func NewMockNetWorthServer(ctrl *gomock.Controller) *MockNetWorthServer {
	mock := &MockNetWorthServer{ctrl: ctrl}
	mock.recorder = &MockNetWorthServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetWorthServer) EXPECT() *MockNetWorthServerMockRecorder {
	return m.recorder
}

// DeleteManualAsset mocks base method.
func (m *MockNetWorthServer) DeleteManualAsset(arg0 context.Context, arg1 *networth.DeleteManualAssetRequest) (*networth.DeleteManualAssetResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteManualAsset", arg0, arg1)
	ret0, _ := ret[0].(*networth.DeleteManualAssetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteManualAsset indicates an expected call of DeleteManualAsset.
func (mr *MockNetWorthServerMockRecorder) DeleteManualAsset(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteManualAsset", reflect.TypeOf((*MockNetWorthServer)(nil).DeleteManualAsset), arg0, arg1)
}

// DepositDeclaration mocks base method.
func (m *MockNetWorthServer) DepositDeclaration(arg0 context.Context, arg1 *networth.DepositDeclarationRequest) (*networth.DepositDeclarationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DepositDeclaration", arg0, arg1)
	ret0, _ := ret[0].(*networth.DepositDeclarationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DepositDeclaration indicates an expected call of DepositDeclaration.
func (mr *MockNetWorthServerMockRecorder) DepositDeclaration(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DepositDeclaration", reflect.TypeOf((*MockNetWorthServer)(nil).DepositDeclaration), arg0, arg1)
}

// GetAssetImportFlowStatus mocks base method.
func (m *MockNetWorthServer) GetAssetImportFlowStatus(arg0 context.Context, arg1 *networth.GetAssetImportFlowStatusRequest) (*networth.GetAssetImportFlowStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetImportFlowStatus", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetAssetImportFlowStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetImportFlowStatus indicates an expected call of GetAssetImportFlowStatus.
func (mr *MockNetWorthServerMockRecorder) GetAssetImportFlowStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetImportFlowStatus", reflect.TypeOf((*MockNetWorthServer)(nil).GetAssetImportFlowStatus), arg0, arg1)
}

// GetConnectMoreAssetsScreen mocks base method.
func (m *MockNetWorthServer) GetConnectMoreAssetsScreen(arg0 context.Context, arg1 *networth.GetConnectMoreAssetsScreenRequest) (*networth.GetConnectMoreAssetsScreenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConnectMoreAssetsScreen", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetConnectMoreAssetsScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConnectMoreAssetsScreen indicates an expected call of GetConnectMoreAssetsScreen.
func (mr *MockNetWorthServerMockRecorder) GetConnectMoreAssetsScreen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConnectMoreAssetsScreen", reflect.TypeOf((*MockNetWorthServer)(nil).GetConnectMoreAssetsScreen), arg0, arg1)
}

// GetCreditScoreSummaryForHome mocks base method.
func (m *MockNetWorthServer) GetCreditScoreSummaryForHome(arg0 context.Context, arg1 *networth.GetCreditScoreSummaryForHomeRequest) (*networth.GetCreditScoreSummaryForHomeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCreditScoreSummaryForHome", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetCreditScoreSummaryForHomeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCreditScoreSummaryForHome indicates an expected call of GetCreditScoreSummaryForHome.
func (mr *MockNetWorthServerMockRecorder) GetCreditScoreSummaryForHome(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCreditScoreSummaryForHome", reflect.TypeOf((*MockNetWorthServer)(nil).GetCreditScoreSummaryForHome), arg0, arg1)
}

// GetEpfSummaryForHome mocks base method.
func (m *MockNetWorthServer) GetEpfSummaryForHome(arg0 context.Context, arg1 *networth.GetEpfSummaryForHomeRequest) (*networth.GetEpfSummaryForHomeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEpfSummaryForHome", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetEpfSummaryForHomeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEpfSummaryForHome indicates an expected call of GetEpfSummaryForHome.
func (mr *MockNetWorthServerMockRecorder) GetEpfSummaryForHome(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEpfSummaryForHome", reflect.TypeOf((*MockNetWorthServer)(nil).GetEpfSummaryForHome), arg0, arg1)
}

// GetInteractiveTalkToAiScreen mocks base method.
func (m *MockNetWorthServer) GetInteractiveTalkToAiScreen(arg0 context.Context, arg1 *networth.GetInteractiveTalkToAiScreenRequest) (*networth.GetInteractiveTalkToAiScreenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInteractiveTalkToAiScreen", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetInteractiveTalkToAiScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractiveTalkToAiScreen indicates an expected call of GetInteractiveTalkToAiScreen.
func (mr *MockNetWorthServerMockRecorder) GetInteractiveTalkToAiScreen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractiveTalkToAiScreen", reflect.TypeOf((*MockNetWorthServer)(nil).GetInteractiveTalkToAiScreen), arg0, arg1)
}

// GetMagicImportIdeas mocks base method.
func (m *MockNetWorthServer) GetMagicImportIdeas(arg0 context.Context, arg1 *networth.GetMagicImportIdeasRequest) (*networth.GetMagicImportIdeasResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicImportIdeas", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetMagicImportIdeasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicImportIdeas indicates an expected call of GetMagicImportIdeas.
func (mr *MockNetWorthServerMockRecorder) GetMagicImportIdeas(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicImportIdeas", reflect.TypeOf((*MockNetWorthServer)(nil).GetMagicImportIdeas), arg0, arg1)
}

// GetMagicImportScreenOptions mocks base method.
func (m *MockNetWorthServer) GetMagicImportScreenOptions(arg0 context.Context, arg1 *networth.GetMagicImportScreenOptionsRequest) (*networth.GetMagicImportScreenOptionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicImportScreenOptions", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetMagicImportScreenOptionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicImportScreenOptions indicates an expected call of GetMagicImportScreenOptions.
func (mr *MockNetWorthServerMockRecorder) GetMagicImportScreenOptions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicImportScreenOptions", reflect.TypeOf((*MockNetWorthServer)(nil).GetMagicImportScreenOptions), arg0, arg1)
}

// GetManualAssetDashboard mocks base method.
func (m *MockNetWorthServer) GetManualAssetDashboard(arg0 context.Context, arg1 *networth.GetManualAssetDashboardRequest) (*networth.GetManualAssetDashboardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetManualAssetDashboard", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetManualAssetDashboardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManualAssetDashboard indicates an expected call of GetManualAssetDashboard.
func (mr *MockNetWorthServerMockRecorder) GetManualAssetDashboard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManualAssetDashboard", reflect.TypeOf((*MockNetWorthServer)(nil).GetManualAssetDashboard), arg0, arg1)
}

// GetManualFormConfig mocks base method.
func (m *MockNetWorthServer) GetManualFormConfig(arg0 context.Context, arg1 *networth.GetManualFormConfigRequest) (*networth.GetManualFormConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetManualFormConfig", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetManualFormConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManualFormConfig indicates an expected call of GetManualFormConfig.
func (mr *MockNetWorthServerMockRecorder) GetManualFormConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManualFormConfig", reflect.TypeOf((*MockNetWorthServer)(nil).GetManualFormConfig), arg0, arg1)
}

// GetMfSummaryForHome mocks base method.
func (m *MockNetWorthServer) GetMfSummaryForHome(arg0 context.Context, arg1 *networth.GetMfSummaryForHomeRequest) (*networth.GetMfSummaryForHomeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMfSummaryForHome", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetMfSummaryForHomeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMfSummaryForHome indicates an expected call of GetMfSummaryForHome.
func (mr *MockNetWorthServerMockRecorder) GetMfSummaryForHome(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMfSummaryForHome", reflect.TypeOf((*MockNetWorthServer)(nil).GetMfSummaryForHome), arg0, arg1)
}

// GetNetWorthDashboard mocks base method.
func (m *MockNetWorthServer) GetNetWorthDashboard(arg0 context.Context, arg1 *networth.GetNetWorthDashboardRequest) (*networth.GetNetWorthDashboardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNetWorthDashboard", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetNetWorthDashboardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetWorthDashboard indicates an expected call of GetNetWorthDashboard.
func (mr *MockNetWorthServerMockRecorder) GetNetWorthDashboard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetWorthDashboard", reflect.TypeOf((*MockNetWorthServer)(nil).GetNetWorthDashboard), arg0, arg1)
}

// GetNetWorthSummaryForHome mocks base method.
func (m *MockNetWorthServer) GetNetWorthSummaryForHome(arg0 context.Context, arg1 *networth.GetNetWorthSummaryForHomeRequest) (*networth.GetNetWorthSummaryForHomeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNetWorthSummaryForHome", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetNetWorthSummaryForHomeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetWorthSummaryForHome indicates an expected call of GetNetWorthSummaryForHome.
func (mr *MockNetWorthServerMockRecorder) GetNetWorthSummaryForHome(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetWorthSummaryForHome", reflect.TypeOf((*MockNetWorthServer)(nil).GetNetWorthSummaryForHome), arg0, arg1)
}

// GetNetworthDataFile mocks base method.
func (m *MockNetWorthServer) GetNetworthDataFile(arg0 context.Context, arg1 *networth.GetNetworthDataFileRequest) (*networth.GetNetworthDataFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNetworthDataFile", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetNetworthDataFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetworthDataFile indicates an expected call of GetNetworthDataFile.
func (mr *MockNetWorthServerMockRecorder) GetNetworthDataFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetworthDataFile", reflect.TypeOf((*MockNetWorthServer)(nil).GetNetworthDataFile), arg0, arg1)
}

// GetNextNetWorthRefreshAction mocks base method.
func (m *MockNetWorthServer) GetNextNetWorthRefreshAction(arg0 context.Context, arg1 *networth.GetNextNetWorthRefreshActionRequest) (*networth.GetNextNetWorthRefreshActionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNextNetWorthRefreshAction", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetNextNetWorthRefreshActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNextNetWorthRefreshAction indicates an expected call of GetNextNetWorthRefreshAction.
func (mr *MockNetWorthServerMockRecorder) GetNextNetWorthRefreshAction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNextNetWorthRefreshAction", reflect.TypeOf((*MockNetWorthServer)(nil).GetNextNetWorthRefreshAction), arg0, arg1)
}

// GetWealthBuilderDashboardComponent mocks base method.
func (m *MockNetWorthServer) GetWealthBuilderDashboardComponent(arg0 context.Context, arg1 *networth.GetWealthBuilderDashboardComponentRequest) (*networth.GetWealthBuilderDashboardComponentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWealthBuilderDashboardComponent", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetWealthBuilderDashboardComponentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthBuilderDashboardComponent indicates an expected call of GetWealthBuilderDashboardComponent.
func (mr *MockNetWorthServerMockRecorder) GetWealthBuilderDashboardComponent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthBuilderDashboardComponent", reflect.TypeOf((*MockNetWorthServer)(nil).GetWealthBuilderDashboardComponent), arg0, arg1)
}

// GetWealthBuilderLandingPage mocks base method.
func (m *MockNetWorthServer) GetWealthBuilderLandingPage(arg0 context.Context, arg1 *networth.GetWealthBuilderLandingPageRequest) (*networth.GetWealthBuilderLandingPageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWealthBuilderLandingPage", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetWealthBuilderLandingPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthBuilderLandingPage indicates an expected call of GetWealthBuilderLandingPage.
func (mr *MockNetWorthServerMockRecorder) GetWealthBuilderLandingPage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthBuilderLandingPage", reflect.TypeOf((*MockNetWorthServer)(nil).GetWealthBuilderLandingPage), arg0, arg1)
}

// GetWealthSduiBottomSheetScreenOptions mocks base method.
func (m *MockNetWorthServer) GetWealthSduiBottomSheetScreenOptions(arg0 context.Context, arg1 *networth.GetWealthSduiBottomSheetScreenOptionsRequest) (*networth.GetWealthSduiBottomSheetScreenOptionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWealthSduiBottomSheetScreenOptions", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetWealthSduiBottomSheetScreenOptionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthSduiBottomSheetScreenOptions indicates an expected call of GetWealthSduiBottomSheetScreenOptions.
func (mr *MockNetWorthServerMockRecorder) GetWealthSduiBottomSheetScreenOptions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthSduiBottomSheetScreenOptions", reflect.TypeOf((*MockNetWorthServer)(nil).GetWealthSduiBottomSheetScreenOptions), arg0, arg1)
}

// MagicImportFiles mocks base method.
func (m *MockNetWorthServer) MagicImportFiles(arg0 context.Context, arg1 *networth.MagicImportFilesRequest) (*networth.MagicImportFilesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MagicImportFiles", arg0, arg1)
	ret0, _ := ret[0].(*networth.MagicImportFilesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MagicImportFiles indicates an expected call of MagicImportFiles.
func (mr *MockNetWorthServerMockRecorder) MagicImportFiles(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MagicImportFiles", reflect.TypeOf((*MockNetWorthServer)(nil).MagicImportFiles), arg0, arg1)
}

// SearchAssetFormFieldOptions mocks base method.
func (m *MockNetWorthServer) SearchAssetFormFieldOptions(arg0 context.Context, arg1 *networth.SearchAssetFormFieldOptionsRequest) (*networth.SearchAssetFormFieldOptionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAssetFormFieldOptions", arg0, arg1)
	ret0, _ := ret[0].(*networth.SearchAssetFormFieldOptionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAssetFormFieldOptions indicates an expected call of SearchAssetFormFieldOptions.
func (mr *MockNetWorthServerMockRecorder) SearchAssetFormFieldOptions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAssetFormFieldOptions", reflect.TypeOf((*MockNetWorthServer)(nil).SearchAssetFormFieldOptions), arg0, arg1)
}

// SubmitManualForm mocks base method.
func (m *MockNetWorthServer) SubmitManualForm(arg0 context.Context, arg1 *networth.SubmitManualFormRequest) (*networth.SubmitManualFormResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitManualForm", arg0, arg1)
	ret0, _ := ret[0].(*networth.SubmitManualFormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitManualForm indicates an expected call of SubmitManualForm.
func (mr *MockNetWorthServerMockRecorder) SubmitManualForm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitManualForm", reflect.TypeOf((*MockNetWorthServer)(nil).SubmitManualForm), arg0, arg1)
}

// SubmitManualForms mocks base method.
func (m *MockNetWorthServer) SubmitManualForms(arg0 context.Context, arg1 *networth.SubmitManualFormsRequest) (*networth.SubmitManualFormsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitManualForms", arg0, arg1)
	ret0, _ := ret[0].(*networth.SubmitManualFormsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitManualForms indicates an expected call of SubmitManualForms.
func (mr *MockNetWorthServerMockRecorder) SubmitManualForms(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitManualForms", reflect.TypeOf((*MockNetWorthServer)(nil).SubmitManualForms), arg0, arg1)
}

// UpdateManualAssets mocks base method.
func (m *MockNetWorthServer) UpdateManualAssets(arg0 context.Context, arg1 *networth.UpdateManualAssetsRequest) (*networth.UpdateManualAssetsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateManualAssets", arg0, arg1)
	ret0, _ := ret[0].(*networth.UpdateManualAssetsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateManualAssets indicates an expected call of UpdateManualAssets.
func (mr *MockNetWorthServerMockRecorder) UpdateManualAssets(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateManualAssets", reflect.TypeOf((*MockNetWorthServer)(nil).UpdateManualAssets), arg0, arg1)
}

// MockUnsafeNetWorthServer is a mock of UnsafeNetWorthServer interface.
type MockUnsafeNetWorthServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeNetWorthServerMockRecorder
}

// MockUnsafeNetWorthServerMockRecorder is the mock recorder for MockUnsafeNetWorthServer.
type MockUnsafeNetWorthServerMockRecorder struct {
	mock *MockUnsafeNetWorthServer
}

// NewMockUnsafeNetWorthServer creates a new mock instance.
func NewMockUnsafeNetWorthServer(ctrl *gomock.Controller) *MockUnsafeNetWorthServer {
	mock := &MockUnsafeNetWorthServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeNetWorthServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeNetWorthServer) EXPECT() *MockUnsafeNetWorthServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedNetWorthServer mocks base method.
func (m *MockUnsafeNetWorthServer) mustEmbedUnimplementedNetWorthServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedNetWorthServer")
}

// mustEmbedUnimplementedNetWorthServer indicates an expected call of mustEmbedUnimplementedNetWorthServer.
func (mr *MockUnsafeNetWorthServerMockRecorder) mustEmbedUnimplementedNetWorthServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedNetWorthServer", reflect.TypeOf((*MockUnsafeNetWorthServer)(nil).mustEmbedUnimplementedNetWorthServer))
}
