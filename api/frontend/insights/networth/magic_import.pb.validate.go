// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/insights/networth/magic_import.proto

package networth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MagicImportedAssetsListScreen with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MagicImportedAssetsListScreen) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MagicImportedAssetsListScreen with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// MagicImportedAssetsListScreenMultiError, or nil if none found.
func (m *MagicImportedAssetsListScreen) ValidateAll() error {
	return m.validate(true)
}

func (m *MagicImportedAssetsListScreen) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeaderBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MagicImportedAssetsListScreenValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MagicImportedAssetsListScreenValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MagicImportedAssetsListScreenValidationError{
				field:  "HeaderBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAssetsSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MagicImportedAssetsListScreenValidationError{
					field:  "AssetsSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MagicImportedAssetsListScreenValidationError{
					field:  "AssetsSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAssetsSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MagicImportedAssetsListScreenValidationError{
				field:  "AssetsSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetImportedAssetsListComponents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MagicImportedAssetsListScreenValidationError{
						field:  fmt.Sprintf("ImportedAssetsListComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MagicImportedAssetsListScreenValidationError{
						field:  fmt.Sprintf("ImportedAssetsListComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MagicImportedAssetsListScreenValidationError{
					field:  fmt.Sprintf("ImportedAssetsListComponents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFooterComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MagicImportedAssetsListScreenValidationError{
					field:  "FooterComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MagicImportedAssetsListScreenValidationError{
					field:  "FooterComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooterComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MagicImportedAssetsListScreenValidationError{
				field:  "FooterComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MagicImportedAssetsListScreenMultiError(errors)
	}

	return nil
}

// MagicImportedAssetsListScreenMultiError is an error wrapping multiple
// validation errors returned by MagicImportedAssetsListScreen.ValidateAll()
// if the designated constraints aren't met.
type MagicImportedAssetsListScreenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MagicImportedAssetsListScreenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MagicImportedAssetsListScreenMultiError) AllErrors() []error { return m }

// MagicImportedAssetsListScreenValidationError is the validation error
// returned by MagicImportedAssetsListScreen.Validate if the designated
// constraints aren't met.
type MagicImportedAssetsListScreenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MagicImportedAssetsListScreenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MagicImportedAssetsListScreenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MagicImportedAssetsListScreenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MagicImportedAssetsListScreenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MagicImportedAssetsListScreenValidationError) ErrorName() string {
	return "MagicImportedAssetsListScreenValidationError"
}

// Error satisfies the builtin error interface
func (e MagicImportedAssetsListScreenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMagicImportedAssetsListScreen.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MagicImportedAssetsListScreenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MagicImportedAssetsListScreenValidationError{}

// Validate checks the field values on AssetsComponentHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AssetsComponentHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssetsComponentHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssetsComponentHeaderMultiError, or nil if none found.
func (m *AssetsComponentHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *AssetsComponentHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetsComponentHeaderValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetsComponentHeaderValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetsComponentHeaderValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConnectAssetsCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetsComponentHeaderValidationError{
					field:  "ConnectAssetsCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetsComponentHeaderValidationError{
					field:  "ConnectAssetsCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConnectAssetsCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetsComponentHeaderValidationError{
				field:  "ConnectAssetsCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetsComponentHeaderValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetsComponentHeaderValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetsComponentHeaderValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetsComponentHeaderValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetsComponentHeaderValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetsComponentHeaderValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AssetsComponentHeaderMultiError(errors)
	}

	return nil
}

// AssetsComponentHeaderMultiError is an error wrapping multiple validation
// errors returned by AssetsComponentHeader.ValidateAll() if the designated
// constraints aren't met.
type AssetsComponentHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssetsComponentHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssetsComponentHeaderMultiError) AllErrors() []error { return m }

// AssetsComponentHeaderValidationError is the validation error returned by
// AssetsComponentHeader.Validate if the designated constraints aren't met.
type AssetsComponentHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssetsComponentHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssetsComponentHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssetsComponentHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssetsComponentHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssetsComponentHeaderValidationError) ErrorName() string {
	return "AssetsComponentHeaderValidationError"
}

// Error satisfies the builtin error interface
func (e AssetsComponentHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssetsComponentHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssetsComponentHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssetsComponentHeaderValidationError{}

// Validate checks the field values on AssetsSummary with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AssetsSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssetsSummary with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AssetsSummaryMultiError, or
// nil if none found.
func (m *AssetsSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *AssetsSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetsSummaryValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetsSummaryValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetsSummaryValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAssetsInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetsSummaryValidationError{
					field:  "AssetsInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetsSummaryValidationError{
					field:  "AssetsInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAssetsInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetsSummaryValidationError{
				field:  "AssetsInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeedbackComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetsSummaryValidationError{
					field:  "FeedbackComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetsSummaryValidationError{
					field:  "FeedbackComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeedbackComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetsSummaryValidationError{
				field:  "FeedbackComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAiCommentary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetsSummaryValidationError{
					field:  "AiCommentary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetsSummaryValidationError{
					field:  "AiCommentary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAiCommentary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetsSummaryValidationError{
				field:  "AiCommentary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AssetsSummaryMultiError(errors)
	}

	return nil
}

// AssetsSummaryMultiError is an error wrapping multiple validation errors
// returned by AssetsSummary.ValidateAll() if the designated constraints
// aren't met.
type AssetsSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssetsSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssetsSummaryMultiError) AllErrors() []error { return m }

// AssetsSummaryValidationError is the validation error returned by
// AssetsSummary.Validate if the designated constraints aren't met.
type AssetsSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssetsSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssetsSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssetsSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssetsSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssetsSummaryValidationError) ErrorName() string { return "AssetsSummaryValidationError" }

// Error satisfies the builtin error interface
func (e AssetsSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssetsSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssetsSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssetsSummaryValidationError{}

// Validate checks the field values on FeedbackComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FeedbackComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeedbackComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FeedbackComponentMultiError, or nil if none found.
func (m *FeedbackComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *FeedbackComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetThumbsUp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeedbackComponentValidationError{
					field:  "ThumbsUp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeedbackComponentValidationError{
					field:  "ThumbsUp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetThumbsUp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeedbackComponentValidationError{
				field:  "ThumbsUp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetThumbsDown()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeedbackComponentValidationError{
					field:  "ThumbsDown",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeedbackComponentValidationError{
					field:  "ThumbsDown",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetThumbsDown()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeedbackComponentValidationError{
				field:  "ThumbsDown",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetThumbsUpNormalView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeedbackComponentValidationError{
					field:  "ThumbsUpNormalView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeedbackComponentValidationError{
					field:  "ThumbsUpNormalView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetThumbsUpNormalView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeedbackComponentValidationError{
				field:  "ThumbsUpNormalView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetThumbsDownNormalView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeedbackComponentValidationError{
					field:  "ThumbsDownNormalView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeedbackComponentValidationError{
					field:  "ThumbsDownNormalView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetThumbsDownNormalView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeedbackComponentValidationError{
				field:  "ThumbsDownNormalView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FeedbackComponentMultiError(errors)
	}

	return nil
}

// FeedbackComponentMultiError is an error wrapping multiple validation errors
// returned by FeedbackComponent.ValidateAll() if the designated constraints
// aren't met.
type FeedbackComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeedbackComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeedbackComponentMultiError) AllErrors() []error { return m }

// FeedbackComponentValidationError is the validation error returned by
// FeedbackComponent.Validate if the designated constraints aren't met.
type FeedbackComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeedbackComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeedbackComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeedbackComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeedbackComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeedbackComponentValidationError) ErrorName() string {
	return "FeedbackComponentValidationError"
}

// Error satisfies the builtin error interface
func (e FeedbackComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeedbackComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeedbackComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeedbackComponentValidationError{}

// Validate checks the field values on FeedbackView with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FeedbackView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeedbackView with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FeedbackViewMultiError, or
// nil if none found.
func (m *FeedbackView) ValidateAll() error {
	return m.validate(true)
}

func (m *FeedbackView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNormalView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeedbackViewValidationError{
					field:  "NormalView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeedbackViewValidationError{
					field:  "NormalView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNormalView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeedbackViewValidationError{
				field:  "NormalView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FeedbackViewMultiError(errors)
	}

	return nil
}

// FeedbackViewMultiError is an error wrapping multiple validation errors
// returned by FeedbackView.ValidateAll() if the designated constraints aren't met.
type FeedbackViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeedbackViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeedbackViewMultiError) AllErrors() []error { return m }

// FeedbackViewValidationError is the validation error returned by
// FeedbackView.Validate if the designated constraints aren't met.
type FeedbackViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeedbackViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeedbackViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeedbackViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeedbackViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeedbackViewValidationError) ErrorName() string { return "FeedbackViewValidationError" }

// Error satisfies the builtin error interface
func (e FeedbackViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeedbackView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeedbackViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeedbackViewValidationError{}

// Validate checks the field values on ImportedAssetsListComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ImportedAssetsListComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImportedAssetsListComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ImportedAssetsListComponentMultiError, or nil if none found.
func (m *ImportedAssetsListComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *ImportedAssetsListComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportedAssetsListComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportedAssetsListComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportedAssetsListComponentValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetImportedAssetsListItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ImportedAssetsListComponentValidationError{
						field:  fmt.Sprintf("ImportedAssetsListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ImportedAssetsListComponentValidationError{
						field:  fmt.Sprintf("ImportedAssetsListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ImportedAssetsListComponentValidationError{
					field:  fmt.Sprintf("ImportedAssetsListItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBorderColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportedAssetsListComponentValidationError{
					field:  "BorderColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportedAssetsListComponentValidationError{
					field:  "BorderColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportedAssetsListComponentValidationError{
				field:  "BorderColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportedAssetsListComponentValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportedAssetsListComponentValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportedAssetsListComponentValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDividerColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportedAssetsListComponentValidationError{
					field:  "DividerColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportedAssetsListComponentValidationError{
					field:  "DividerColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDividerColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportedAssetsListComponentValidationError{
				field:  "DividerColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAssetsComponentHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportedAssetsListComponentValidationError{
					field:  "AssetsComponentHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportedAssetsListComponentValidationError{
					field:  "AssetsComponentHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAssetsComponentHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportedAssetsListComponentValidationError{
				field:  "AssetsComponentHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsComponentDisabled

	if len(errors) > 0 {
		return ImportedAssetsListComponentMultiError(errors)
	}

	return nil
}

// ImportedAssetsListComponentMultiError is an error wrapping multiple
// validation errors returned by ImportedAssetsListComponent.ValidateAll() if
// the designated constraints aren't met.
type ImportedAssetsListComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImportedAssetsListComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImportedAssetsListComponentMultiError) AllErrors() []error { return m }

// ImportedAssetsListComponentValidationError is the validation error returned
// by ImportedAssetsListComponent.Validate if the designated constraints
// aren't met.
type ImportedAssetsListComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImportedAssetsListComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImportedAssetsListComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImportedAssetsListComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImportedAssetsListComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImportedAssetsListComponentValidationError) ErrorName() string {
	return "ImportedAssetsListComponentValidationError"
}

// Error satisfies the builtin error interface
func (e ImportedAssetsListComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImportedAssetsListComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImportedAssetsListComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImportedAssetsListComponentValidationError{}

// Validate checks the field values on ImportedAssetsListItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ImportedAssetsListItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImportedAssetsListItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ImportedAssetsListItemMultiError, or nil if none found.
func (m *ImportedAssetsListItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ImportedAssetsListItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAssetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportedAssetsListItemValidationError{
					field:  "AssetName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportedAssetsListItemValidationError{
					field:  "AssetName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAssetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportedAssetsListItemValidationError{
				field:  "AssetName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAssetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportedAssetsListItemValidationError{
					field:  "AssetValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportedAssetsListItemValidationError{
					field:  "AssetValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAssetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportedAssetsListItemValidationError{
				field:  "AssetValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImportError()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportedAssetsListItemValidationError{
					field:  "ImportError",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportedAssetsListItemValidationError{
					field:  "ImportError",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImportError()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportedAssetsListItemValidationError{
				field:  "ImportError",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEditDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportedAssetsListItemValidationError{
					field:  "EditDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportedAssetsListItemValidationError{
					field:  "EditDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEditDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportedAssetsListItemValidationError{
				field:  "EditDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsSelected

	// no validation rules for IsEditable

	// no validation rules for HasMissingInfo

	// no validation rules for FileName

	// no validation rules for IsSelectable

	if len(errors) > 0 {
		return ImportedAssetsListItemMultiError(errors)
	}

	return nil
}

// ImportedAssetsListItemMultiError is an error wrapping multiple validation
// errors returned by ImportedAssetsListItem.ValidateAll() if the designated
// constraints aren't met.
type ImportedAssetsListItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImportedAssetsListItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImportedAssetsListItemMultiError) AllErrors() []error { return m }

// ImportedAssetsListItemValidationError is the validation error returned by
// ImportedAssetsListItem.Validate if the designated constraints aren't met.
type ImportedAssetsListItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImportedAssetsListItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImportedAssetsListItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImportedAssetsListItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImportedAssetsListItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImportedAssetsListItemValidationError) ErrorName() string {
	return "ImportedAssetsListItemValidationError"
}

// Error satisfies the builtin error interface
func (e ImportedAssetsListItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImportedAssetsListItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImportedAssetsListItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImportedAssetsListItemValidationError{}

// Validate checks the field values on ImportedAssetsListItemEditDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ImportedAssetsListItemEditDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImportedAssetsListItemEditDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ImportedAssetsListItemEditDetailsMultiError, or nil if none found.
func (m *ImportedAssetsListItemEditDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ImportedAssetsListItemEditDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportedAssetsListItemEditDetailsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportedAssetsListItemEditDetailsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportedAssetsListItemEditDetailsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetManualForm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportedAssetsListItemEditDetailsValidationError{
					field:  "ManualForm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportedAssetsListItemEditDetailsValidationError{
					field:  "ManualForm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetManualForm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportedAssetsListItemEditDetailsValidationError{
				field:  "ManualForm",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFormIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImportedAssetsListItemEditDetailsValidationError{
					field:  "FormIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImportedAssetsListItemEditDetailsValidationError{
					field:  "FormIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFormIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImportedAssetsListItemEditDetailsValidationError{
				field:  "FormIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ImportedAssetsListItemEditDetailsMultiError(errors)
	}

	return nil
}

// ImportedAssetsListItemEditDetailsMultiError is an error wrapping multiple
// validation errors returned by
// ImportedAssetsListItemEditDetails.ValidateAll() if the designated
// constraints aren't met.
type ImportedAssetsListItemEditDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImportedAssetsListItemEditDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImportedAssetsListItemEditDetailsMultiError) AllErrors() []error { return m }

// ImportedAssetsListItemEditDetailsValidationError is the validation error
// returned by ImportedAssetsListItemEditDetails.Validate if the designated
// constraints aren't met.
type ImportedAssetsListItemEditDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImportedAssetsListItemEditDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImportedAssetsListItemEditDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImportedAssetsListItemEditDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImportedAssetsListItemEditDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImportedAssetsListItemEditDetailsValidationError) ErrorName() string {
	return "ImportedAssetsListItemEditDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ImportedAssetsListItemEditDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImportedAssetsListItemEditDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImportedAssetsListItemEditDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImportedAssetsListItemEditDetailsValidationError{}

// Validate checks the field values on FooterComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FooterComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FooterComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FooterComponentMultiError, or nil if none found.
func (m *FooterComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *FooterComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetShareButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FooterComponentValidationError{
					field:  "ShareButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FooterComponentValidationError{
					field:  "ShareButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShareButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FooterComponentValidationError{
				field:  "ShareButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddToNetworthButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FooterComponentValidationError{
					field:  "AddToNetworthButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FooterComponentValidationError{
					field:  "AddToNetworthButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddToNetworthButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FooterComponentValidationError{
				field:  "AddToNetworthButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisclaimer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FooterComponentValidationError{
					field:  "Disclaimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FooterComponentValidationError{
					field:  "Disclaimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisclaimer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FooterComponentValidationError{
				field:  "Disclaimer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShareText

	// no validation rules for ShareImage

	// no validation rules for ShareImageVersion

	if len(errors) > 0 {
		return FooterComponentMultiError(errors)
	}

	return nil
}

// FooterComponentMultiError is an error wrapping multiple validation errors
// returned by FooterComponent.ValidateAll() if the designated constraints
// aren't met.
type FooterComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FooterComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FooterComponentMultiError) AllErrors() []error { return m }

// FooterComponentValidationError is the validation error returned by
// FooterComponent.Validate if the designated constraints aren't met.
type FooterComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FooterComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FooterComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FooterComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FooterComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FooterComponentValidationError) ErrorName() string { return "FooterComponentValidationError" }

// Error satisfies the builtin error interface
func (e FooterComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFooterComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FooterComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FooterComponentValidationError{}
