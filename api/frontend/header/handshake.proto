syntax = "proto3";

package frontend.header;

option go_package = "github.com/epifi/gamma/api/frontend/header";
option java_package = "com.github.epifi.gamma.api.frontend.header";


message HandshakeInfo {
  // handshake token generated in verify otp rpc
  // client is expected to populate token
  string token = 1;
  // requester_actor_id refers to the caller
  string requester_actor_id = 2;
  // provider_actor_id refers to client
  string provider_actor_id = 3;
}
