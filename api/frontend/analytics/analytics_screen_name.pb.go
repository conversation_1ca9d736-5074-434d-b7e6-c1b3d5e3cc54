// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/analytics/analytics_screen_name.proto

package analytics

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// An enum to identify all the important/relevant screens in client apps. This enum is used mainly in client-only
// scenarios to keep track of the entry point of various screens. Keeping the screen names in a common enum in protos
// would enable different clients to have same contract, and also allow Backend services to utilise the same enum, when
// needed
// Details: https://docs.google.com/document/d/1iYGZGLv3BPcFBcG0SsJSLm461go_mLpPGKwNp02WxRg/edit
type AnalyticsScreenName int32

const (
	AnalyticsScreenName_ANALYTICS_SCREEN_NAME_UNSPECIFIED AnalyticsScreenName = 0
	// Landing screen for Analyser
	AnalyticsScreenName_ANALYZER AnalyticsScreenName = 1
	// Askfi/Search results screen
	AnalyticsScreenName_ASKFI_SEARCH_RESULTS AnalyticsScreenName = 2
	// Home landing screen
	AnalyticsScreenName_HOME_LANDING AnalyticsScreenName = 3
	// The Savings account/Fi account Tab shown in the Home pull down UI
	AnalyticsScreenName_HOME_FI_ACCOUNT_TAB AnalyticsScreenName = 4
	// The connected account tab shown in the Home pull down UI
	AnalyticsScreenName_HOME_CONNECTED_ACCOUNT_TAB AnalyticsScreenName = 5
	// The Smart deposits tab shown in Home Pull Down UI
	AnalyticsScreenName_HOME_SMART_DEPOSIT_TAB AnalyticsScreenName = 6
	// The Fixed Deposits tab shown in Home Pull Down UI
	AnalyticsScreenName_HOME_FIXED_DEPOSIT_TAB AnalyticsScreenName = 7
	// The P2P investments/Jump tab shown in Home Pull Down UI
	AnalyticsScreenName_HOME_P2P_JUMP_TAB AnalyticsScreenName = 8
	// The Fi account details screen, opened when user taps on the Home Saving account title
	AnalyticsScreenName_HOME_FI_ACCOUNT_DETAILS AnalyticsScreenName = 9
	// The Pay module landing screen
	AnalyticsScreenName_PAY_LANDING AnalyticsScreenName = 10
	// Help module landing screen
	AnalyticsScreenName_HELP_LANDING AnalyticsScreenName = 11
	// Fittt module landing screen
	AnalyticsScreenName_FITTT_LANDING AnalyticsScreenName = 12
	// Profile module landing screen
	AnalyticsScreenName_PROFILE_LANDING AnalyticsScreenName = 13
	// Profile setting screen
	AnalyticsScreenName_PROFILE_SETTINGS AnalyticsScreenName = 14
	// The enter amount screen in Payment flow
	AnalyticsScreenName_PAY_ENTER_AMOUNT AnalyticsScreenName = 15
	// Category Analyser screen
	AnalyticsScreenName_CATEGORY_ANALYSER AnalyticsScreenName = 16
	// Debit card limits home fragment
	AnalyticsScreenName_DEBIT_CARD_LANDING AnalyticsScreenName = 17
	// Screen for viewing debit card limits
	AnalyticsScreenName_DEBIT_CARD_LIMITS_HOME AnalyticsScreenName = 18
	// Screen for updating Debit card limits
	AnalyticsScreenName_DEBIT_CARD_UPDATE_LIMIT AnalyticsScreenName = 19
	// Screen for scanning QR code for new card request
	AnalyticsScreenName_DEBIT_CARD_ACTIVATE_QR_CODE AnalyticsScreenName = 20
	// Screen for filling reasons for new Debit card request
	AnalyticsScreenName_DEBIT_CARD_REQUEST_CUSTOM_REASONS AnalyticsScreenName = 21
	// Screen for starting New card request flow
	AnalyticsScreenName_DEBIT_CARD_NEW_CARD_REQUEST AnalyticsScreenName = 22
	// Screen showing the list of Debit card request reasons
	AnalyticsScreenName_DEBIT_CARD_REQUEST_REASONS_LIST AnalyticsScreenName = 23
	// Success screen for new Debit card request
	AnalyticsScreenName_DEBIT_CARD_REQUEST_SUCCESS AnalyticsScreenName = 24
	// Debit card offers home/landing screen
	AnalyticsScreenName_DEBIT_CARD_OFFERS_HOME AnalyticsScreenName = 25
	// Debit card offer details screen
	AnalyticsScreenName_DEBIT_CARD_OFFER_DETAILS AnalyticsScreenName = 26
	// Debit card settings screen
	AnalyticsScreenName_DEBIT_CARD_SETTINGS AnalyticsScreenName = 27
	// Debit card ATM pin setting screen
	AnalyticsScreenName_DEBIT_CARD_ATM_PIN_OPTIONS AnalyticsScreenName = 28
	// Debit card ATM pin reset screen
	AnalyticsScreenName_DEBIT_CARD_RESET_ATM_PIN_OPTIONS AnalyticsScreenName = 29
	// Save/Deposit module's template list screen
	AnalyticsScreenName_DEPOSITS_TEMPLATES_LIST AnalyticsScreenName = 30
	// Add money screen shown during Deposit creation/SD add more funds
	AnalyticsScreenName_DEPOSIT_ADD_MONEY AnalyticsScreenName = 31
	// Add name screen displayed during Smart/Fixed deposit creation
	AnalyticsScreenName_DEPOSIT_ADD_NAME AnalyticsScreenName = 32
	// Dialog shown when starting Smart/Fixed deposit pre-closure, with information about benefits of retaining the
	// deposit
	AnalyticsScreenName_DEPOSIT_CLOSURE_NUDGES AnalyticsScreenName = 33
	// 2nd dialog shown when user continues pre-closing the Smart/Fixed deposit
	AnalyticsScreenName_DEPOSIT_CLOSURE_CONFIRMATION AnalyticsScreenName = 34
	// The Interest payout frequency screen shown during Fixed deposit creation
	AnalyticsScreenName_DEPOSIT_PAYOUT_MODE_SELECTION AnalyticsScreenName = 35
	// The payment selection screen shown during Deposit creation
	AnalyticsScreenName_DEPOSIT_SELECT_PAYMENT_METHOD AnalyticsScreenName = 36
	// Fittt module collection information screen
	AnalyticsScreenName_FITTT_COLLECTION_INFO AnalyticsScreenName = 37
	// Fittt screen for exploring all collections
	AnalyticsScreenName_FITTT_EXPLORE_COLLECTIONS AnalyticsScreenName = 38
	// Fittt module introduction screen with carousel
	AnalyticsScreenName_FITTT_INTRO_SCREEN AnalyticsScreenName = 39
	// Fittt screen for user's active subscriptions
	AnalyticsScreenName_FITTT_ALL_SUBSCRIPTIONS AnalyticsScreenName = 40
	// Fittt screen for user's rules
	AnalyticsScreenName_FITTT_EXPLORE_MY_RULES AnalyticsScreenName = 41
	// Fittt screen for previewing rule subscription
	AnalyticsScreenName_FITTT_RULE_SUBSCRIPTION_PREVIEW AnalyticsScreenName = 42
	// View all fittt rules
	AnalyticsScreenName_FITTT_ALL_RULES AnalyticsScreenName = 43
	// Fittt Sports rules intro screen
	AnalyticsScreenName_FITTT_SPORTS_INTRO AnalyticsScreenName = 44
	// Fittt Sports rule landing screen
	AnalyticsScreenName_FITTT_SPORTS_CHALLENGE_LANDING AnalyticsScreenName = 45
	// Fittt Sports rule reward screen
	AnalyticsScreenName_FITTT_SPORTS_CHALLENGE_REWARD AnalyticsScreenName = 46
	// Match details screen for Fittt sport rules
	AnalyticsScreenName_FITTT_SPORTS_CHALLENGE_MATCH_DETAILS AnalyticsScreenName = 47
	// Fittt rule execution history
	AnalyticsScreenName_FITTT_RULE_EXECUTION_HISTORY AnalyticsScreenName = 48
	// Fittt rule subscription details
	AnalyticsScreenName_FITTT_SUBSCRIPTION_DETAILS AnalyticsScreenName = 49
	// Help module Article detail screen
	AnalyticsScreenName_HELP_ARTICLE AnalyticsScreenName = 50
	// Help category screen
	AnalyticsScreenName_HELP_CATEGORY AnalyticsScreenName = 51
	// Help Stories topic screen
	AnalyticsScreenName_HELP_STORIES_TOPIC AnalyticsScreenName = 52
	// Account details screen for Smart/Fixed deposits
	AnalyticsScreenName_DEPOSIT_ACCOUNT_DETAILS AnalyticsScreenName = 53
	// In-app notifications center screen
	AnalyticsScreenName_NOTIFICATION_CENTER AnalyticsScreenName = 54
	// Rewards promotion banner screen
	AnalyticsScreenName_REWARDS_PROMO_BANNER_SCREEN AnalyticsScreenName = 55
	// First introduction screen in Onboarding flow
	AnalyticsScreenName_ONBOARDING_INTRO_SCREEN AnalyticsScreenName = 56
	// Screen for starting fund transfer when closing min-kyc account
	AnalyticsScreenName_ONBOARDING_ACCOUNT_CLOSURE_TRANSFER_INIT AnalyticsScreenName = 57
	// Onboarding add funds screen
	AnalyticsScreenName_ONBOARDING_ADD_MONEY AnalyticsScreenName = 58
	// Onboarding address confirmation screen
	AnalyticsScreenName_ONBOARDING_CONFIRM_ADDRESS AnalyticsScreenName = 59
	// Onboarding App features list screen
	AnalyticsScreenName_ONBOARDING_APP_FEATURES_SCREEN AnalyticsScreenName = 60
	// Onboarding duplicate account (de-dupe check) screen
	AnalyticsScreenName_ONBOARDING_ACCOUNT_DUPLICATE AnalyticsScreenName = 61
	// Screen for showing detailed information in onboarding flow
	AnalyticsScreenName_ONBOARDING_INFO_ACK AnalyticsScreenName = 62
	// Screen for adding Parent's name and Nominee selection in Onboarding
	AnalyticsScreenName_ONBOARDING_KYC_PARENT_NAME AnalyticsScreenName = 63
	// Screen for Email account selection and verification, in onboarding
	AnalyticsScreenName_ONBOARDING_EMAIL_ACCOUNT_VERIFICATION AnalyticsScreenName = 64
	// Screen for showing 'All set' state during onboarding account creation flow
	AnalyticsScreenName_ONBOARDING_ALL_SET AnalyticsScreenName = 65
	// Screen for entering Finite code in onboarding
	AnalyticsScreenName_ONBOARDING_ENTER_FINITE_CODE AnalyticsScreenName = 66
	// Screen for entering Pan number and Date of birth
	AnalyticsScreenName_ONBOARDING_KYC_ENTER_PAN_DOB AnalyticsScreenName = 67
	// Screen for getting user consent in Onboarding flow
	AnalyticsScreenName_ONBOARDING_TNC_SCREEN AnalyticsScreenName = 68
	// Screen for getting consent so that whatsapp communication can be sent to users
	AnalyticsScreenName_ONBOARDING_WHATSAPP_CONSENT_SCREEN AnalyticsScreenName = 69
	// Screen showing the 'Open Account' Cta in onboarding flow
	AnalyticsScreenName_ONBOARDING_CREATE_ACCOUNT_CONSENT AnalyticsScreenName = 70
	// Screen for setting debit card pin in onboarding flow
	AnalyticsScreenName_ONBOARDING_DEBIT_CARD_SET_PIN AnalyticsScreenName = 71
	// Screen for collecting User consent for credit report check
	AnalyticsScreenName_ONBOARDING_SCREENER_CREDIT_REPORT_CONSENT AnalyticsScreenName = 72
	// Screen for collecting Employment details during onboarding screener flow
	AnalyticsScreenName_ONBOARDING_SCREENER_EMPLOYMENT_DECLARATION AnalyticsScreenName = 73
	// Screen for showing Experian T&C
	AnalyticsScreenName_ONBOARDING_SCREENER_EXPERIAN_TNC AnalyticsScreenName = 74
	// Screen for when users employment declaration verification is not successful and the have credit report available.
	// Users need to provide consent to check their Credit Report
	AnalyticsScreenName_ONBOARDING_SCREENER_MANDATE_CREDIT_REPORT_CONSENT AnalyticsScreenName = 75
	// Screen to allow user to verify their work status by giving us permission to read their gmail. We can verify it
	// based on their transactions
	AnalyticsScreenName_ONBOARDING_SCREENER_VERIFY_WITH_GMAIL AnalyticsScreenName = 76
	// Screen is an upcoming stage in work status verification. But we need to show user that this is an alternative to
	// gmail-verification which they can try later. This screen & deeplink will be deprecated once we have linkedin
	// verification implemented
	AnalyticsScreenName_ONBOARDING_SCREENER_LINKEDIN_VERIFICATION AnalyticsScreenName = 77
	// Screen to allow user to enter their work-email to verify their work status
	AnalyticsScreenName_ONBOARDING_SCREENER_ENTER_WORK_EMAIL AnalyticsScreenName = 78
	// Screen to allow user to enter & verify the otp sent to their work email
	AnalyticsScreenName_ONBOARDING_SCREENER_WORK_EMAIL_OTP AnalyticsScreenName = 79
	// Screen for showing All transactions for various accounts of a user
	AnalyticsScreenName_PAY_ALL_TRANSACTIONS AnalyticsScreenName = 80
	// Screen to allow users to search for transactions
	AnalyticsScreenName_PAY_SEARCH_TRANSACTIONS AnalyticsScreenName = 81
	// Info screen before creating, authorizing or executing a recurring payment
	AnalyticsScreenName_PAY_AUTO_PAY_AMOUNT_SCREEN AnalyticsScreenName = 82
	// Screen used for creating / editing recurring payment rule. Ideally this should be happening through Fittt. So, for
	// now we have copied components from Fittt and modified it as per requirements
	AnalyticsScreenName_PAY_CREATE_RECURRING_PAYMENT AnalyticsScreenName = 83
	// Screen to show details for a recurring payment
	AnalyticsScreenName_PAY_RECURRING_PAY_DETAILS AnalyticsScreenName = 84
	// Screen to show list of activities for a recurring payment
	AnalyticsScreenName_PAY_RECURRING_PAY_EXECUTION_INFO AnalyticsScreenName = 85
	// Screen to list all recurring payment based on status
	AnalyticsScreenName_PAY_FILTER_RECURRING_PAYMENTS AnalyticsScreenName = 86
	// Home/landing page for auto pay
	AnalyticsScreenName_PAY_AUTO_PAY_HOME AnalyticsScreenName = 87
	// Screen to show intro for auto-pay
	AnalyticsScreenName_PAY_AUTO_PAY_INTRO AnalyticsScreenName = 88
	// Bank transfer form screen in Pay module
	AnalyticsScreenName_PAY_BANK_TRANSFER AnalyticsScreenName = 89
	// This screen collects inputs from user for the dispute & allows them to raise a dispute or ask for help
	AnalyticsScreenName_PAY_DISPUTE_INPUT AnalyticsScreenName = 90
	// Entry point for all intent based payment received by the app
	AnalyticsScreenName_PAY_INTENT_SCREEN AnalyticsScreenName = 91
	// Screen for for Payment via Phone number
	AnalyticsScreenName_PAY_PHONE_PAYMENT AnalyticsScreenName = 92
	// Scan QR code for UPI payment
	AnalyticsScreenName_PAY_QR_SCAN AnalyticsScreenName = 93
	// Pay search screen for searching upi ids/users
	AnalyticsScreenName_PAY_SEARCH AnalyticsScreenName = 94
	// Screen to set/change NPCI PIN
	AnalyticsScreenName_PAY_UPDATE_NPCI_PIN AnalyticsScreenName = 95
	// Pay timeline screen for an entity (merchant/user)
	AnalyticsScreenName_PAY_TIMELINE AnalyticsScreenName = 96
	// Shows lists of categories to select from for a transaction
	AnalyticsScreenName_PAY_TRANSACTION_CATEGORY AnalyticsScreenName = 97
	// Screen to show to categorise multiple transactions
	AnalyticsScreenName_PAY_TRANSACTION_CATEGORY_SIMILAR_TXN AnalyticsScreenName = 98
	// Transaction receipt screen for a payment
	AnalyticsScreenName_PAY_TRANSACTION_RECEIPT AnalyticsScreenName = 99
	// Screen responsible for collecting and validating the UPI id
	AnalyticsScreenName_PAY_UPI_PAYMENT AnalyticsScreenName = 100
	// Screen in user's profile module, to show list of user's accounts
	AnalyticsScreenName_PROFILE_ACCOUNTS_LIST AnalyticsScreenName = 101
	// Account settings screen in User profile
	AnalyticsScreenName_PROFILE_ACCOUNT_SETTINGS AnalyticsScreenName = 102
	// Settings screen for a user's connected account
	AnalyticsScreenName_PROFILE_CONNECTED_ACCOUNT_SETTINGS AnalyticsScreenName = 103
	// Privacy and security screen in Profile
	AnalyticsScreenName_PROFILE_PRIVACY_AND_SECURITY AnalyticsScreenName = 104
	// Screen in User Profile to show user's QR code
	AnalyticsScreenName_PROFILE_SHOW_QR AnalyticsScreenName = 105
	// Screen to show legal terms in User profile
	AnalyticsScreenName_PROFILE_SETTINGS_LEGAL AnalyticsScreenName = 106
	// Dialog to show Logout CTA
	AnalyticsScreenName_PROFILE_APP_LOGOUT_DIALOG AnalyticsScreenName = 107
	// Profile Notification settings screen
	AnalyticsScreenName_PROFILE_NOTIFICATION_SETTINGS AnalyticsScreenName = 108
	// Edit profile screen in User profile section
	AnalyticsScreenName_PROFILE_EDIT AnalyticsScreenName = 109
	// Screen to view user's personal details
	AnalyticsScreenName_PROFILE_PERSONAL_DETAILS AnalyticsScreenName = 110
	// Screen to show user's referral history
	AnalyticsScreenName_REFERRALS_HISTORY AnalyticsScreenName = 111
	// Screen to show Users referral code, invited users etc.
	AnalyticsScreenName_REFERRALS_INVITE_FRIENDS AnalyticsScreenName = 112
	// Screen to show the qualifying action details and Cta for referrals
	AnalyticsScreenName_REFERRALS_QUALIFYING_ACTION_REQUIRED AnalyticsScreenName = 113
	// Referral terms screen
	AnalyticsScreenName_REFERRALS_TNC AnalyticsScreenName = 114
	// Screen to show maximum referral rewards reached
	AnalyticsScreenName_REFERRALS_REWARD_CAP_REACHED AnalyticsScreenName = 115
	// Screen for resetting referral rewards cap
	AnalyticsScreenName_REFERRALS_REWARD_RESET_CAP AnalyticsScreenName = 116
	// Reward claim screen
	AnalyticsScreenName_REWARDS_CLAIM AnalyticsScreenName = 117
	// Reward details screen
	AnalyticsScreenName_REWARDS_DETAIL AnalyticsScreenName = 118
	// Rewards ways to earn screen
	AnalyticsScreenName_REWARDS_WAYS_TO_EARN AnalyticsScreenName = 119
	// Screen to show information about Rewards
	AnalyticsScreenName_REWARDS_INFO AnalyticsScreenName = 120
	// Landing/home screen for rewards
	AnalyticsScreenName_REWARDS_LANDING AnalyticsScreenName = 121
	// Screen for showing collected offers
	AnalyticsScreenName_OFFERS_COLLECTED AnalyticsScreenName = 122
	// Screen for showing details about an offer
	AnalyticsScreenName_OFFERS_DETAILS AnalyticsScreenName = 123
	// Screen to show details of redeemed offer
	AnalyticsScreenName_OFFERS_EXCHANGED_OFFER_DETAILS AnalyticsScreenName = 124
	// Screen to show successful redemption of Gift voucher
	AnalyticsScreenName_OFFERS_REDEEMED_GIFT_VOUCHER AnalyticsScreenName = 125
	// Screen to show details of redeemed Fi coins
	AnalyticsScreenName_OFFERS_COIN_EXCHANGE_CLAIM AnalyticsScreenName = 126
	// Screen for showing details of exchange reward once it is successful
	AnalyticsScreenName_OFFERS_COIN_EXCHANGE_SUCCESS AnalyticsScreenName = 127
	// Offers landing/home screen
	AnalyticsScreenName_OFFERS_LANDING AnalyticsScreenName = 128
	// Offers terms and conditions screen
	AnalyticsScreenName_OFFERS_DETAIL_TNC AnalyticsScreenName = 129
	// Screen for editing a Reward Smart deposit
	AnalyticsScreenName_REWARDS_SD AnalyticsScreenName = 130
	// Salary introduction screen
	AnalyticsScreenName_SALARY_INTRO AnalyticsScreenName = 131
	// Salary module landing/home screen
	AnalyticsScreenName_SALARY_LANDING AnalyticsScreenName = 132
	// Screen for showing all benefits on salary program
	AnalyticsScreenName_SALARY_ALL_BENEFITS AnalyticsScreenName = 133
	// Screen to show all available active benefits
	AnalyticsScreenName_SALARY_ACTIVE_BENEFITS AnalyticsScreenName = 134
	// Askfi/Search landing screen
	AnalyticsScreenName_ASKFI_LANDING AnalyticsScreenName = 135
	// Poginated Screen for showing all financial activities from an ask-fi results screen
	AnalyticsScreenName_ASKFI_ALL_FINANCIAL_ACTIVITY AnalyticsScreenName = 136
	// Askfi suggestions screen
	AnalyticsScreenName_ASKFI_SUGGESTIONS AnalyticsScreenName = 137
	// Screen for showing Vkyc related information
	AnalyticsScreenName_VKYC_INFO AnalyticsScreenName = 138
	// Vkyc landing/home screen
	AnalyticsScreenName_VKYC_LANDING AnalyticsScreenName = 139
	// Screen to display information about a scheduled Vkyc call
	AnalyticsScreenName_VKYC_SCHEDULED AnalyticsScreenName = 140
	// Screen to display vkyc incompatible info and related actions
	AnalyticsScreenName_VKYC_DEVICE_INCOMPATIBLE AnalyticsScreenName = 141
	// Screen to show pending status of Vkyc
	AnalyticsScreenName_VKYC_STATUS AnalyticsScreenName = 142
	// Screen for Adhaar e-sign missing data submitted by user to be updated on KRA
	AnalyticsScreenName_WEALTH_ADHAAR_ESIGN AnalyticsScreenName = 143
	// Screen to capture image of PAN card from the user
	AnalyticsScreenName_WEALTH_COLLECT_PAN AnalyticsScreenName = 144
	// Screen to collects user's signature
	AnalyticsScreenName_WEALTH_COLLECT_SIGNATURE AnalyticsScreenName = 145
	// Screen to collect acknowledgement/consent from User for updating their KYC data. A SEBI mandatory screen
	AnalyticsScreenName_WEALTH_CONSENT AnalyticsScreenName = 146
	// Screen to show landing screen for Digilocker flow
	AnalyticsScreenName_WEALTH_DIGILOCKER_LANDING AnalyticsScreenName = 147
	// Screen to show Digilocker using webview
	AnalyticsScreenName_WEALTH_DIGILOCKER_WEBVIEW AnalyticsScreenName = 148
	// Screen used for fund selection in one time investment
	AnalyticsScreenName_WEALTH_OTI_FUND_SELECTION AnalyticsScreenName = 149
	// Landing screen for Wealth Onboarding
	AnalyticsScreenName_WEALTH_ONBOARDING_LANDING AnalyticsScreenName = 150
	// Displays monetary activities against a mutual fund for an user
	AnalyticsScreenName_WEALTH_MF_ACTIVITY AnalyticsScreenName = 151
	// Screen to list down the options available to invest in Mutual funds
	AnalyticsScreenName_WEALTH_MF_LIST AnalyticsScreenName = 152
	// Screen to display all mutual funds available against a collection
	AnalyticsScreenName_WEALTH_MF_ALL_COLLECTIONS AnalyticsScreenName = 153
	// Landing screen for Mutual Fund collections. This screen custom MF collections to users and allows the capability
	// to search, sort, bookmark, etc
	AnalyticsScreenName_WEALTH_MF_COLLECTIONS_LANDING AnalyticsScreenName = 154
	// Screen to display various details about a Mutual fund
	AnalyticsScreenName_WEALTH_MF_DETAILS AnalyticsScreenName = 155
	// Screen to show a user's investment details
	AnalyticsScreenName_WEALTH_MF_INVESTED_DETAILS AnalyticsScreenName = 156
	// Landing screen for p2p investments . We can set the amount of investment to be done. Can directly land here from
	// either dashboard or soon after eligibility checks are done and you are cleared to invest
	AnalyticsScreenName_WEALTH_P2P_LANDING AnalyticsScreenName = 157
	// Screen to show complete details for a Mutual fund/Investment order
	AnalyticsScreenName_WEALTH_ORDER_DETAILS AnalyticsScreenName = 158
	// This screen handles multiple scenarios based on WealthOnboardingAction status
	AnalyticsScreenName_WEALTH_ONBOARDING_STATUS AnalyticsScreenName = 159
	// Screen to record missing KYC information in BE during Wealth on-boarding
	AnalyticsScreenName_WEALTH_ONBOARDING_MISSING_KYC_DATA AnalyticsScreenName = 160
	// Generic screen to display title, description and a deeplink driven CTA
	AnalyticsScreenName_WEALTH_ONBOARDING_MISSING_KYC_INTRO AnalyticsScreenName = 161
	// Screen to record user consent before performing Liveness video verification
	AnalyticsScreenName_LIVENESS_CONSENT AnalyticsScreenName = 162
	// Screen to record user's video for liveness verification
	AnalyticsScreenName_LIVENESS_RECORDING AnalyticsScreenName = 163
	// Fragment which shows benefits of connected accounts. The actual purpose of the screen is to act like an dummy entry
	// point to connected accounts. clicking on connected accounts entry point (i.e: Notification or home banner) will be
	// taken to this screen and not to the OTP immediately.
	AnalyticsScreenName_AA_BENEFITS AnalyticsScreenName = 164
	// Screen to check with backend if the user is wealth onboarded or not. Based on backend provided deeplink start
	// various flow
	AnalyticsScreenName_AA_ACCOUNT_INIT AnalyticsScreenName = 165
	// Information screen for the user before going into the KYC
	AnalyticsScreenName_ONBOARDING_EKYC_CONSENT AnalyticsScreenName = 166
	// Screen to retry EKYC verification after Name Mismatch
	AnalyticsScreenName_ONBOARDING_EKYC_MISMATCH AnalyticsScreenName = 167
	// Screen for customising fit rules
	AnalyticsScreenName_FITTT_RULE_CUSTOMISATION AnalyticsScreenName = 168
	// Screen for tweaking various usage rules/restrictions for Debit card
	AnalyticsScreenName_DEBIT_CARD_USAGE_SETTING AnalyticsScreenName = 169
	// Screen for tracking Debit card dispatch
	AnalyticsScreenName_DEBIT_CARD_TRACKING AnalyticsScreenName = 170
	// Mutual funds landing/digest tab
	AnalyticsScreenName_HOME_MUTUAL_FUNDS_LANDING_TAB AnalyticsScreenName = 171
	// P2P Consent screen
	AnalyticsScreenName_P2P_LANDING_CONSENT AnalyticsScreenName = 172
	// P2P Activity details fragment. Is showed when user clicks on any activity in all Activity Screen
	AnalyticsScreenName_P2P_ACTIVITY_DETAILS AnalyticsScreenName = 173
	// P2P All Activities fragment. Is showed when user clicks on All Activity CTA on dashboard screen
	AnalyticsScreenName_P2P_ALL_ACTIVITIES AnalyticsScreenName = 174
	// P2P Eligibility check screen in Jump tab
	AnalyticsScreenName_P2P_ELIGIBILITY_CHECK AnalyticsScreenName = 175
	// Know more screen for P2P investments
	AnalyticsScreenName_P2P_KNOW_MORE AnalyticsScreenName = 176
	// Onboarding Account dededupe error screen
	AnalyticsScreenName_ONBOARDING_DEDUPE_ERROR AnalyticsScreenName = 177
	// Screen for showing detailed error status for onboarding
	AnalyticsScreenName_ONBOARDING_DETAILED_ERROR AnalyticsScreenName = 178
	// Screen for entering/selecting user phone number and OTP verification
	AnalyticsScreenName_ONBOARDING_PHONE_VERIFICATION AnalyticsScreenName = 180
	// Screen for taking user consent for Safetynet verification
	AnalyticsScreenName_ONBOARDING_SAFETY_NET_CONSENT AnalyticsScreenName = 181
	// Screen for showing screener state in Manual intervention for user
	AnalyticsScreenName_SCREENER_MANUAL_INTERVENTION AnalyticsScreenName = 182
	// Shows the detail of already exchanged coins to money
	AnalyticsScreenName_OFFERS_EXCHANGE_COIN_DETAIL AnalyticsScreenName = 183
	// Details the ways to earn a active reward
	AnalyticsScreenName_REWARDS_WAYS_TO_EARN_DETAIL AnalyticsScreenName = 184
	// Selection screen for selecting a salary transaction
	AnalyticsScreenName_SALARY_TRANSACTION_SELECTOR AnalyticsScreenName = 185
	// Fragment is shown when E-KYC limit in days has expired. User needs to complete E-KYC before moving on V-KYC
	AnalyticsScreenName_VKYC_EKYC_EXPIRED AnalyticsScreenName = 186
	// Showing Vkyc status post onboarding for user is complete
	AnalyticsScreenName_VKYC_STATUS_POST_ONBOARDING AnalyticsScreenName = 187
	// Displays Selection based choice to users
	AnalyticsScreenName_WEALTH_COLLECT_SINGLE_CHOICE AnalyticsScreenName = 188
	// Error handling for Wealth on-boarding
	AnalyticsScreenName_WEALTH_ONBOARDING_ERROR AnalyticsScreenName = 189
	// The P2P/Jump Landing screen
	AnalyticsScreenName_P2P_LANDING_TAB AnalyticsScreenName = 190
	// used when we want to show instructions before continuing to do an action
	AnalyticsScreenName_ONBOARDING_INSTRUCTIONS AnalyticsScreenName = 191
	// The ML-kit based Pay QR Scan screen
	AnalyticsScreenName_PAY_QR_SCAN_ML_KIT AnalyticsScreenName = 192
	// Entry point via Deeplink urls (Like marketing deeplink urls which users can click and deeplink into the app)
	AnalyticsScreenName_DEEPLINK_URL AnalyticsScreenName = 193
	// Entry point via the mobile device's system tray notifications
	AnalyticsScreenName_SYSTEM_TRAY_NOTIFICATION AnalyticsScreenName = 194
	// Entry point for editing user profile info
	AnalyticsScreenName_EDIT_PROFILE_INFO AnalyticsScreenName = 195
	// Screen to show all quick links on salary feature
	AnalyticsScreenName_SALARY_ALL_QUICK_LINKS AnalyticsScreenName = 196
	// Insights hub screen in Analyser module
	AnalyticsScreenName_INSIGHTS_HUB AnalyticsScreenName = 197
	// intro screen to connect accounts for screener.
	AnalyticsScreenName_SCREENER_CONNECTED_ACCOUNTS_INFO AnalyticsScreenName = 198
	// Tier introduction screen
	AnalyticsScreenName_TIER_INTRODUCTION_SCREEN AnalyticsScreenName = 199
	// Tier overview screen
	AnalyticsScreenName_TIER_OVERVIEW_SCREEN AnalyticsScreenName = 200
	// Tier upgrade success screen
	AnalyticsScreenName_TIER_UPGRADE_SUCCESS_SCREEN AnalyticsScreenName = 201
	// New Home landing screen
	AnalyticsScreenName_HOME_LANDING_V2 AnalyticsScreenName = 202
	// Screen name for the Home V2 Explore Tab Screen
	AnalyticsScreenName_HOME_EXPLORE_SCREEN AnalyticsScreenName = 203
	// Screen name for new invest landing screen
	AnalyticsScreenName_INVEST_LANDING AnalyticsScreenName = 204
	// The US stocks tab shown in Home Pull Down UI
	AnalyticsScreenName_HOME_US_STOCKS_TAB AnalyticsScreenName = 205
	// US stocks landing page explore tab
	AnalyticsScreenName_US_STOCKS_LANDING_EXPLORE AnalyticsScreenName = 206
	// US stocks landing page portfolio tab / your stocks
	AnalyticsScreenName_US_STOCKS_LANDING_YOUR_STOCKS AnalyticsScreenName = 207
	// US stocks landing page watchlist tab
	AnalyticsScreenName_US_STOCKS_LANDING_WATCHLIST AnalyticsScreenName = 208
	// US stocks collections screen
	AnalyticsScreenName_US_STOCKS_COLLECTION AnalyticsScreenName = 209
	// US stocks symbol details screen
	AnalyticsScreenName_US_STOCKS_DETAILS AnalyticsScreenName = 210
	// US stocks search screen
	AnalyticsScreenName_US_STOCKS_SEARCH AnalyticsScreenName = 211
	// Screen for jump available plans screen
	AnalyticsScreenName_P2P_INVESTMENT_AVAILABLE_PLANS_INFO AnalyticsScreenName = 381
	// Screen options for jump choose plan screen
	AnalyticsScreenName_P2P_INVESTMENT_CHOOSE_PLAN AnalyticsScreenName = 382
	// Screen options for jump unlock plan screen
	AnalyticsScreenName_P2P_INVESTMENT_UNLOCK_PLAN AnalyticsScreenName = 383
	// Screen to select or enter amount to withdraw
	// Client is supposed to call P2PInvestments's 'GetDeeplink' rpc for rendering this
	AnalyticsScreenName_P2P_WITHDRAW_MONEY_ENTER_AMOUNT AnalyticsScreenName = 385
	// Screen to show withdrawal amount break across different scheme
	// Client is supposed to call P2PInvestment's 'GetWithdrawMoneyAttributes' rpc for rendering this
	AnalyticsScreenName_P2P_WITHDRAW_MONEY_SUMMARY                       AnalyticsScreenName = 386
	AnalyticsScreenName_P2P_INVESTMENT_VIEW_BREAKUP_SCREEN               AnalyticsScreenName = 409
	AnalyticsScreenName_P2P_INVESTMENT_OTP_SCREEN                        AnalyticsScreenName = 460
	AnalyticsScreenName_P2P_CONFIRM_INVESTMENT_RENEWAL_SCREEN            AnalyticsScreenName = 461
	AnalyticsScreenName_P2P_INVESTMENT_CURRENT_STATUS_SCREEN             AnalyticsScreenName = 464
	AnalyticsScreenName_P2P_INVESTMENT_ACTIVITY_DETAILS_SCREEN           AnalyticsScreenName = 465
	AnalyticsScreenName_P2P_INVESTMENT_ALL_UPCOMING_RENEWALS_SCREEN      AnalyticsScreenName = 466
	AnalyticsScreenName_P2P_INVESTMENT_RENEWAL_CANCELLATION_NUDGE_SCREEN AnalyticsScreenName = 467
	AnalyticsScreenName_US_STOCK_BUY_ENTER_AMOUNT                        AnalyticsScreenName = 468
	AnalyticsScreenName_US_STOCK_BUY_SUMMARY                             AnalyticsScreenName = 469
	AnalyticsScreenName_US_STOCK_SELL_ENTER_AMOUNT                       AnalyticsScreenName = 470
	AnalyticsScreenName_US_STOCK_SELL_SUMMARY                            AnalyticsScreenName = 471
	AnalyticsScreenName_US_STOCK_ORDER_DETAILS                           AnalyticsScreenName = 472
	AnalyticsScreenName_US_STOCK_ONB_STMNTS_PAN_FETCH                    AnalyticsScreenName = 473
	AnalyticsScreenName_US_STOCK_ONB_DECLARATION                         AnalyticsScreenName = 474
	AnalyticsScreenName_US_STOCK_ONB_DISCLAIMER                          AnalyticsScreenName = 475
	AnalyticsScreenName_US_STOCK_ONB_EMPLOYER_DETAILS                    AnalyticsScreenName = 476
	AnalyticsScreenName_US_STOCK_ONB_INVESTMENT_RANGE                    AnalyticsScreenName = 478
	AnalyticsScreenName_US_STOCK_ONB_PAN_UPLOAD_OPTIONS                  AnalyticsScreenName = 479
	AnalyticsScreenName_US_STOCK_ONB_PAN__UPLOAD_CAMERA                  AnalyticsScreenName = 480
	AnalyticsScreenName_US_STOCK_ONB_PAN__UPLOAD_GALLERY                 AnalyticsScreenName = 481
	AnalyticsScreenName_US_STOCK_EXISTING_CONNECTED_ACCOUNT              AnalyticsScreenName = 482
	AnalyticsScreenName_US_STOCK_NEW_CONNECTED_ACCOUNT                   AnalyticsScreenName = 483
	AnalyticsScreenName_US_STOCK_SOF_POLLING                             AnalyticsScreenName = 484
	AnalyticsScreenName_US_STOCK_ONB_FUTURE_SCOPE                        AnalyticsScreenName = 485
	AnalyticsScreenName_US_STOCK_SOF_FUTURE_SCOPE                        AnalyticsScreenName = 486
	AnalyticsScreenName_US_STOCK_SET_UP_ACCOUNT_DIALOG                   AnalyticsScreenName = 487
	AnalyticsScreenName_US_STOCK_SET_UP_SOF_DIALOG                       AnalyticsScreenName = 488
	AnalyticsScreenName_US_STOCK_PREREQUISITE_DIALOG                     AnalyticsScreenName = 489
	// Investment risk profile questionnaire screen
	AnalyticsScreenName_INVESTMENT_RISK_PROFILE_QUESTIONNAIRE AnalyticsScreenName = 490
	// Investment risk profile dashboard screen
	AnalyticsScreenName_INVESTMENT_RISK_PROFILE_DASHBOARD AnalyticsScreenName = 491
	AnalyticsScreenName_VKYC_INTRO                        AnalyticsScreenName = 492
	AnalyticsScreenName_VKYC_PAN_TYPE_SELECTION           AnalyticsScreenName = 493
	AnalyticsScreenName_VKYC_INSTRUCTIONS                 AnalyticsScreenName = 494
	AnalyticsScreenName_US_STOCKS_SEARCH_LANDING          AnalyticsScreenName = 495
	AnalyticsScreenName_US_STOCK_LANDING_PAGE             AnalyticsScreenName = 496
	AnalyticsScreenName_US_STOCKS_SEARCH_RESULT           AnalyticsScreenName = 497
	// Reminder
	AnalyticsScreenName_REMINDERS_ENTRY   AnalyticsScreenName = 498
	AnalyticsScreenName_REMINDERS_LANDING AnalyticsScreenName = 499
	AnalyticsScreenName_REMINDERS_SET     AnalyticsScreenName = 500
	// auto save suggestions screen in the deposit creation flow
	AnalyticsScreenName_DEPOSIT_AUTO_SAVE_SUGGESTIONS AnalyticsScreenName = 501
	AnalyticsScreenName_VKYC_ERROR_SCREEN             AnalyticsScreenName = 502
	// Reminder
	AnalyticsScreenName_REMINDER_OVER_SPEND       AnalyticsScreenName = 503
	AnalyticsScreenName_REMINDER_DATE_CHANGE_PAGE AnalyticsScreenName = 504
	AnalyticsScreenName_REMINDER_NO_CC_PAGE       AnalyticsScreenName = 505
	// CX Language Selection
	AnalyticsScreenName_CX_LANGUAGE_SELECTION_SCREEN         AnalyticsScreenName = 506
	AnalyticsScreenName_CX_LANGUAGE_SELECTION_SUCCESS_SCREEN AnalyticsScreenName = 507
	AnalyticsScreenName_CX_LANGUAGE_OPTION_SUGGESTION_SCREEN AnalyticsScreenName = 508
	// Screen display individual generic screen - https://www.figma.com/file/WGeiokaZndC3vfTbwJ6WJh/Help-%E2%80%A2-FFF-%E2%80%A2-v1.0?type=design&node-id=3639%3A7348&t=GGXxlsKfeHT05jyM-1
	AnalyticsScreenName_STORY_SCREEN AnalyticsScreenName = 509
	// CX Ticket Screens
	AnalyticsScreenName_CX_TICKET_DETAIL_SCREEN AnalyticsScreenName = 510
	AnalyticsScreenName_CX_TICKET_LIST_SCREEN   AnalyticsScreenName = 511
	AnalyticsScreenName_CX_TICKET_TAB_SCREEN    AnalyticsScreenName = 512
	// CX Chat
	AnalyticsScreenName_CX_CHAT_SCREEN AnalyticsScreenName = 513
	// App Shortcuts entry point identifier. These entry points are surfaced, when user long-presses on the Launcher icon
	// in iOS/Android
	AnalyticsScreenName_APP_SHORTCUTS AnalyticsScreenName = 514
	// Disconnect account
	AnalyticsScreenName_DISCONNECT_ACCOUNT_CONFIRM_VIEW_CONTROLLER      AnalyticsScreenName = 515
	AnalyticsScreenName_DISCONNECT_ACCOUNT_BOTTOM_SHEET_VIEW_CONTROLLER AnalyticsScreenName = 516
	// Connected Account: connecting Fi to Fi flow to connect Fi Federal Savings Account
	AnalyticsScreenName_CA_LANDING_PAGE_FI_TO_FI_FLOW_SCREEN  AnalyticsScreenName = 517
	AnalyticsScreenName_CA_INIT_SDK_FI_TO_FI_FLOW_SCREEN      AnalyticsScreenName = 518
	AnalyticsScreenName_DEBIT_CARD_CONSOLIDATED_USAGE_SETTING AnalyticsScreenName = 519
	// DEBIT CARD ACTIVATION SCREENS
	// Screen that initiates the debit card activation flow via the secure pin . This screen will be enabled for the user 5-7 days post
	// the card receipt for the user.
	AnalyticsScreenName_DEBIT_CARD_PIN_ACTIVATION_SCREEN AnalyticsScreenName = 621
	// screen to be displayed when the card activation has been done successfully
	AnalyticsScreenName_DEBIT_CARD_SUCCESSFUL_ACTIVATION_SCREEN AnalyticsScreenName = 622
	// screen which will contain the qr scanner used to scan the qr code for card activation
	AnalyticsScreenName_DEBIT_CARD_QR_CODE_SCAN_SCREEN AnalyticsScreenName = 623
	// Screen to show processing state in payment flow
	AnalyticsScreenName_PAY_PROCESSING AnalyticsScreenName = 624
	// Screen to show status of payment in payment flow
	AnalyticsScreenName_POST_PAYMENT AnalyticsScreenName = 625
	// US Stocks onboarding Success Screen.
	AnalyticsScreenName_US_STOCKS_ONB_SUCCESS AnalyticsScreenName = 626
	// US STOCKS Onboarding Polling. This screen is a polling screen to check for completion of US STOCKS account creation
	AnalyticsScreenName_US_STOCKS_ONB_POLLING AnalyticsScreenName = 627
	// US STOCKS Onboarding Address ID Proof Screen to check for ID Proofs of Client
	AnalyticsScreenName_US_STOCKS_ONB_ADDRESS_ID_PROOF AnalyticsScreenName = 628
	// US STOCKS Onboarding Error Screen to show errors on any screen during Onboarding of US STOCKS
	AnalyticsScreenName_US_STOCKS_ONB_ERROR AnalyticsScreenName = 629
	// US STOCKS PN Onboarding landing screen
	AnalyticsScreenName_US_STOCKS_PN_ONB_LANDING AnalyticsScreenName = 630
	// Screen to update Maturity options for a Jump Investment.
	// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?type=design&node-id=15353-23902&t=akSt8sz2mwKyN7c5-4
	AnalyticsScreenName_P2P_UPDATE_MATURITY_CONSENT_DIALOG AnalyticsScreenName = 631
	// https://drive.google.com/file/d/12p_o9n4Gsv-1y2tYv3XRw1721j1qSRVA/view?usp=drive_link
	AnalyticsScreenName_DEPOSIT_ADD_MONEY_FREQUENCY AnalyticsScreenName = 632
	// Processing screen post user Swipes to Close Deposit.
	AnalyticsScreenName_DEPOSIT_CLOSURE_PROCESSING AnalyticsScreenName = 633
	// https://drive.google.com/file/d/19eN7NsgN12mZE_BJ3zGeXFI7zK_oHATS/view?usp=drive_link
	AnalyticsScreenName_DEPOSIT_CREATION_PROCESSING AnalyticsScreenName = 634
	// https://drive.google.com/file/d/17SAdlW6iBMslGP-CSggft4nMKJsZNfAa/view?usp=drive_link
	// https://drive.google.com/file/d/16JFMxfv9emcS_8RCDiKsMey_tjdcfqBg/view?usp=drive_link
	AnalyticsScreenName_DEPOSIT_CREATION_AUTO_SAVE_PROCESSING AnalyticsScreenName = 635
	// https://drive.google.com/file/d/1-ISBOobbiVpcGn4OKPiSH7T1Jh4Z7T6q/view?usp=drive_link
	// https://drive.google.com/file/d/1BY7M3E9qAN9zuF52bF6ajjsNh5DTJwf5/view?usp=drive_link
	AnalyticsScreenName_DEPOSIT_GENERATE_STATEMENT AnalyticsScreenName = 636
	// https://drive.google.com/file/d/1vS8hn8VQa16-YOhCnd88xQeT4ZS6D-PO/view?usp=drive_link
	AnalyticsScreenName_DEPOSIT_TERM_SELECTION AnalyticsScreenName = 637
	// https://drive.google.com/file/d/1jz2MtzfULImAO7rk11fSYgzBlvIyC7vz/view?usp=drive_link
	AnalyticsScreenName_DEPOSIT_EXPLAINER_DIALOG AnalyticsScreenName = 638
	// https://drive.google.com/file/d/1rCrAZHTAlq5pIkd2C--WHJmsMo2wLN2J/view?usp=drive_link
	AnalyticsScreenName_DEPOSIT_EXPLAINER_COMPARISON_DIALOG AnalyticsScreenName = 639
	// Onboarding screener choice screen where user can select any of the way to verify the screener
	AnalyticsScreenName_ONBOARDING_SCREENER_CHOICE AnalyticsScreenName = 640
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=28184-82719&mode=design&t=BhOlaRTpDBigUS4k-4
	AnalyticsScreenName_CC_INTRO_SCREEN AnalyticsScreenName = 641
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=25344-92911&mode=design&t=BhOlaRTpDBigUS4k-4
	AnalyticsScreenName_CC_ADDRESS_SELECTION_SCREEN AnalyticsScreenName = 642
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8569-36634&mode=design&t=BhOlaRTpDBigUS4k-4
	AnalyticsScreenName_CC_BILL_GENERATION_SCREEN AnalyticsScreenName = 643
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=6504-38493&mode=design&t=zBKNYfLWUHEEAal7-4
	AnalyticsScreenName_CC_LIVENESS_AND_FACE_MATCH_SCREEN AnalyticsScreenName = 644
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=6530-51906&mode=design&t=zBKNYfLWUHEEAal7-4
	AnalyticsScreenName_CC_VKYC_FLOW_SCREEN AnalyticsScreenName = 645
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=7194-24587&mode=design&t=k9lF6Nu2YHsYkySs-4
	AnalyticsScreenName_CC_CARD_CREATION_SCREEN AnalyticsScreenName = 646
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=23700-89970&mode=design&t=k9lF6Nu2YHsYkySs-4
	AnalyticsScreenName_CC_WELCOME_OFFER_SELECTION_SCREEN AnalyticsScreenName = 647
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=28184-82960&mode=design&t=vGdJxXI49Crq7hyy-4
	AnalyticsScreenName_CC_VALUE_BACK_SCREEN AnalyticsScreenName = 648
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=30903-98447&mode=design&t=HOtNGzh3XtqXOPLj-4
	AnalyticsScreenName_CC_ACCELERATED_REWARDS_SCREEN AnalyticsScreenName = 649
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=28184-83636&mode=design&t=vGdJxXI49Crq7hyy-4
	AnalyticsScreenName_CC_WELCOME_VOUCHERS_SCREEN AnalyticsScreenName = 650
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=23643-103872&mode=design&t=WYx3spVKzSOm3Eqd-4
	AnalyticsScreenName_CC_WELCOME_OFFER_SELECTION_SUCCESS_SCREEN AnalyticsScreenName = 651
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=10257-47824&mode=design&t=WYx3spVKzSOm3Eqd-4
	AnalyticsScreenName_CC_LANDING_SCREEN AnalyticsScreenName = 652
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=10436-44110&mode=design&t=PDmaDlD6I2mkHxRN-4
	AnalyticsScreenName_CC_CONTROLS AnalyticsScreenName = 653
	// Early Salary screen name
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20106-39249&mode=design&t=cIh1uXQposnj2v0T-4
	AnalyticsScreenName_ES_ELIGIBILITY_LANDING AnalyticsScreenName = 654
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20167-39908&mode=design&t=cIh1uXQposnj2v0T-4
	AnalyticsScreenName_ES_APPLICATION_LANDING AnalyticsScreenName = 655
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=24796-44371&mode=design&t=cIh1uXQposnj2v0T-4
	AnalyticsScreenName_ES_CUSTOM_OFFER_SELECTION_DIALOG AnalyticsScreenName = 656
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46984&mode=design&t=cIh1uXQposnj2v0T-4
	AnalyticsScreenName_ES_APPLICATION_DETAILS AnalyticsScreenName = 657
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=21791-44695&mode=design&t=cIh1uXQposnj2v0T-4
	AnalyticsScreenName_ES_AUTO_REPAYMENT AnalyticsScreenName = 658
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46899&mode=design&t=cIh1uXQposnj2v0T-4
	AnalyticsScreenName_ES_ADDRESS_CONFIRMATION AnalyticsScreenName = 659
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46585&mode=design&t=cIh1uXQposnj2v0T-4
	AnalyticsScreenName_ES_INITIATE_ESIGN AnalyticsScreenName = 660
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46605&mode=design&t=cIh1uXQposnj2v0T-4
	AnalyticsScreenName_ES_ESIGN_VIEW_DOCUMENT AnalyticsScreenName = 661
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46638&mode=design&t=cIh1uXQposnj2v0T-4
	AnalyticsScreenName_ES_APPLICATION_STATUS AnalyticsScreenName = 662
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20182-48203&mode=design&t=cIh1uXQposnj2v0T-4
	AnalyticsScreenName_ES_DASHBOARD AnalyticsScreenName = 663
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46255&mode=design&t=cIh1uXQposnj2v0T-4
	AnalyticsScreenName_ES_LOAN_DETAILS AnalyticsScreenName = 664
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46765&mode=design&t=cIh1uXQposnj2v0T-4
	AnalyticsScreenName_ES_APPLICATION_STATUS_POLL AnalyticsScreenName = 665
	// Personal Loans Screen name
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-19636&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_LANDING AnalyticsScreenName = 666
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20843&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_OFFER_DETAILS AnalyticsScreenName = 667
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-19985&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_APPLICATION_DETAILS AnalyticsScreenName = 668
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-19636&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_NOT_QUALIFIED_USERS AnalyticsScreenName = 669
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=18721-35584&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_INITIATE_MANDATE AnalyticsScreenName = 670
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20349&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_INITIATE_ESIGN AnalyticsScreenName = 671
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20412&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_ESIGN_VIEW_DOCUMENT AnalyticsScreenName = 672
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20455&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_APPLICATION_CONFIRMATION_VIA_OTP AnalyticsScreenName = 673
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20204&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_ADDRESS_CONFIRMATION AnalyticsScreenName = 674
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=16440-32289&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_EMPLOYMENT_DETAILS AnalyticsScreenName = 675
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=16051-31940&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_POLLING_SCREEN AnalyticsScreenName = 676
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20439&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_APPLICATION_STATUS AnalyticsScreenName = 677
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-21079&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_LOAN_DASHBOARD AnalyticsScreenName = 678
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=3526-13511&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_PRE_PAY AnalyticsScreenName = 679
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=9783-10358&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_ACTIVITY_STATUS AnalyticsScreenName = 680
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=21974-44764&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_DOWNTIME AnalyticsScreenName = 681
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=19433-37057&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_ENTER_DETAILS AnalyticsScreenName = 682
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20509&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_LOAN_DETAILS AnalyticsScreenName = 683
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=19433-37194&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_REVIEW_DETAILS AnalyticsScreenName = 684
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=19433-37255&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_OCCUPATION_DETAILS AnalyticsScreenName = 685
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15453-29350&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_UPDATED_RATE AnalyticsScreenName = 686
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=5263-15963&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_TRANSACTION_RECEIPT AnalyticsScreenName = 687
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=5528-24094&mode=design&t=FGfaipheeFQ0XgCg-4
	AnalyticsScreenName_PL_ALL_TRANSACTIONS AnalyticsScreenName = 688
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=9480-41391&mode=design&t=yZizPhbECUhGjE7k-4
	AnalyticsScreenName_CC_USAGE_SCREEN AnalyticsScreenName = 689
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=9468-42159&mode=design&t=yZizPhbECUhGjE7k-4
	AnalyticsScreenName_CC_LIMITS_SCREEN AnalyticsScreenName = 690
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=15217-65138&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_ALL_TRANSACTIONS_SCREEN AnalyticsScreenName = 691
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=23004-84719&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_TRANSACTION_RECEIPT_SCREEN AnalyticsScreenName = 692
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=11436-46606&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_BILL_REPAYMENT_SELECTION_SCREEN AnalyticsScreenName = 693
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=11436-56480&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_CUSTOM_AMOUNT_BILL_PAY_SCREEN AnalyticsScreenName = 694
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=11361-49404&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_PAYMENT_STATUS_SCREEN AnalyticsScreenName = 695
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=6093-28009&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_SET_CARD_PREFERENCES_SCREEN AnalyticsScreenName = 696
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8921-37964&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_SET_CARD_PIN AnalyticsScreenName = 697
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8914-36659&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_PHYSICAL_CARD_TRACKING_SCREEN AnalyticsScreenName = 698
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=14298-60202&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_VIEW_STATEMENT_SCREEN AnalyticsScreenName = 699
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=14298-61459&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_EXPORT_STATEMENT_SCREEN AnalyticsScreenName = 700
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=10401-42747&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_FREEZE_UNFREEZE_SCREEN AnalyticsScreenName = 701
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=10206-42332&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_NEW_CARD_REQUEST_ADDRESS_SELECTION_SCREEN AnalyticsScreenName = 702
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8977-38866&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_NEW_CARD_REQUEST AnalyticsScreenName = 703
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17696-72536&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_EMI_DASH_BOARD_SCREEN AnalyticsScreenName = 704
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17503-73225&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_VIEW_ALL_EMI_TRANSACTIONS_SCREEN AnalyticsScreenName = 705
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=16896-72866&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_EMI_TRANSACTION_LOAN_OFFERS_SCREEN AnalyticsScreenName = 706
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=15971-68591&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_EMI_PREVIEW_PRE_CLOSE_LOAN_SCREEN AnalyticsScreenName = 707
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=15576-66172&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_EMI_LOAN_ACCOUNT_DETAILS_SCREEN AnalyticsScreenName = 708
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=15993-67391&mode=design&t=K09kUhQFxea4ekHF-4
	AnalyticsScreenName_CC_EMI_PRE_CLOSE_LOAN_SCREEN AnalyticsScreenName = 709
	// https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=1-4040&mode=design&t=LpJ2VJ0A9BM8056O-4
	AnalyticsScreenName_CC_SECURED_OPEN_FD_SCREEN AnalyticsScreenName = 710
	// https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=779-17822&mode=design&t=LpJ2VJ0A9BM8056O-4
	AnalyticsScreenName_CC_SECURED_FD_DETAILS_SCREEN AnalyticsScreenName = 711
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=27648-82126&mode=design&t=Z1NgpXUrnD4fydIF-4
	AnalyticsScreenName_CC_REAL_TIME_ELIGIBILITY_CHECK_INTRO_SCREEN AnalyticsScreenName = 712
	// https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=12532-44765&mode=dev
	AnalyticsScreenName_DEPOSIT_CREATION_SCREEN AnalyticsScreenName = 713
	// This screen is used for mentioning the screen name in the events for the clicking of the tabs
	// Debit card and credit card tab screen
	AnalyticsScreenName_CARD_TABS_SCREEN AnalyticsScreenName = 714
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=31062-31806&mode=design&t=RDFo7azNWxu6eAL0-4
	AnalyticsScreenName_CC_BILL_GENERATION_DATE_SELECTION_SCREEN AnalyticsScreenName = 715
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=9468-42546&mode=design&t=HPH0hT8EGJF2mHma-4
	AnalyticsScreenName_CC_EDIT_LIMITS_SCREEN AnalyticsScreenName = 716
	// https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=1-4040&mode=design&t=LpJ2VJ0A9BM8056O-4
	AnalyticsScreenName_CC_SECURED_TENURE_SELECTION_BOTTOM_SHEET_SCREEN AnalyticsScreenName = 717
	// https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=84-16721&mode=design&t=dqR9qTEzJXlhyEtX-4
	AnalyticsScreenName_CC_SECURED_FD_INFO_SCREEN AnalyticsScreenName = 718
	// https://drive.google.com/file/d/1KQI6qM3FMttsoIrHpPsRF49F7CqTw8I0/view?usp=drive_link
	AnalyticsScreenName_DEPOSIT_DECLARATION AnalyticsScreenName = 719
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=5582%3A176745&t=pmOfrvWkNVTHSL94-0
	AnalyticsScreenName_CC_AUTH_OPTIONS_SCREEN AnalyticsScreenName = 720
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?type=design&node-id=23643-102517
	AnalyticsScreenName_CC_BENEFITS_SCREEN AnalyticsScreenName = 721
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8921%3A37964&t=poCwdMZL3AsSfgjE-0
	AnalyticsScreenName_CC_CUG_SET_PIN_SCREEN AnalyticsScreenName = 722
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=16896%3A72866&t=nRbFU86jEoEPH14b-0
	AnalyticsScreenName_CC_EMI_VIEW_ACTIVE_SCREEN AnalyticsScreenName = 723
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9964%3A43930&t=jqxjczLXGkoBZD15-0
	AnalyticsScreenName_CC_SUCCESS_INFO_DIALOG_SCREEN AnalyticsScreenName = 724
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=7375%3A29396&t=H5VYHf5n2iX8LzJ8-0
	AnalyticsScreenName_CC_INITIATE_SCREEN AnalyticsScreenName = 725
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=24225-92363&t=o1i95sLagG5q5yeX-0
	AnalyticsScreenName_CC_LOUNGE_ACCESS_SCREEN AnalyticsScreenName = 726
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9560%3A42186&t=URYN34rw4H90m85W-1
	AnalyticsScreenName_CC_OTP_SCREEN AnalyticsScreenName = 727
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=6079%3A28065
	AnalyticsScreenName_CC_QR_ACTIVATION_SCREEN AnalyticsScreenName = 728
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9964%3A43390&t=QeFu4Mo7uS9l24Gk-00
	AnalyticsScreenName_CC_SELECT_STATEMENT_DURATION_DIALOG_SCREEN AnalyticsScreenName = 729
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8987%3A41480
	AnalyticsScreenName_CC_REQUEST_CUSTOM_REASONS_SCREEN AnalyticsScreenName = 730
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=14298%3A60202&t=bwOQTI4B5wq6JnKc-0
	AnalyticsScreenName_CC_STATEMENT_DETAILS_SCREEN AnalyticsScreenName = 731
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8977%3A38866
	AnalyticsScreenName_CC_NEW_REQUEST_SCREEN AnalyticsScreenName = 732
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8977%3A39331
	AnalyticsScreenName_CC_REQUEST_REASONS_SCREEN AnalyticsScreenName = 733
	// https://www.figma.com/file/UO85a3b8ayLW9ZSFPhzRLi/CC-Rewards?node-id=841%3A24887&t=D7P20hHBcVc2FB1v-1
	AnalyticsScreenName_CC_THREE_TOP_REWARDS_SCREEN AnalyticsScreenName = 734
	// https://www.figma.com/file/UO85a3b8ayLW9ZSFPhzRLi/CC-Rewards?node-id=813%3A33344&t=ECIIGBnCeqXjplGj-1
	AnalyticsScreenName_CC_EXTRA_REWARDS_DETAILS_DIALOG_SCREEN AnalyticsScreenName = 735
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&mode=design&t=FWtpsPZoXO2AqqxS-0
	AnalyticsScreenName_CC_AUTH_POLL_SCREEN AnalyticsScreenName = 736
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=6530-41211&mode=design&t=mNjTX7T6SuHlx5yO-4
	AnalyticsScreenName_CC_GENERIC_HALT_SCREEN AnalyticsScreenName = 737
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=7303-23078&mode=design&t=7fnFmT6QTRZhjnYH-0
	AnalyticsScreenName_CC_KNOW_MORE_SCREEN AnalyticsScreenName = 738
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8219-38725&mode=design&t=eWsbZLvgaMCbDAwY-0
	AnalyticsScreenName_CC_LIVENESS_SUMMARY_POLL_SCREEN AnalyticsScreenName = 739
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=7577-33283&mode=design&t=mNjTX7T6SuHlx5yO-4
	AnalyticsScreenName_CC_PERMANENT_USER_FAILURE_SCREEN AnalyticsScreenName = 740
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8219-38725&mode=design&t=WYc6YnBGbJRPHNyM-0
	AnalyticsScreenName_CC_REWARDS_POLL_SCREEN AnalyticsScreenName = 741
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=11852-55825&mode=design&t=yXHu61e3JnMk36XP-0
	AnalyticsScreenName_CC_DISPUTE_DETAILS_SCREEN AnalyticsScreenName = 742
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=11852-55825&mode=design&t=iLQw0iza1lYKwequ-0
	AnalyticsScreenName_CC_DISPUTE_DIALOG_SCREEN AnalyticsScreenName = 743
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=11852-56301&mode=design&t=rvK0bjihgibWowKF-0
	AnalyticsScreenName_CC_DISPUTE_QUESTIONS_SCREEN AnalyticsScreenName = 744
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=6350%3A67623&t=NXDJ5qRMyIHocAho-0
	AnalyticsScreenName_CC_DISPUTE_SUMMARY_SCREEN AnalyticsScreenName = 745
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8219-38725&mode=design&t=bd035CoA56vIw7cH-0
	AnalyticsScreenName_CC_STATUS_POLL_SCREEN AnalyticsScreenName = 746
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8219%3A40372
	AnalyticsScreenName_CC_ENABLE_PAYMENTS_SCREEN AnalyticsScreenName = 747
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=17723-74334&t=fDkO48GHuLIDsnhm-0
	AnalyticsScreenName_CC_CONFIRM_SELECTION_DIALOG_SCREEN AnalyticsScreenName = 748
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=19887-78816&mode=design&t=pL2CWq6O0t0h9sPY-0
	AnalyticsScreenName_CC_WAITLISTED_SCREEN AnalyticsScreenName = 749
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=19887-78816&mode=design&t=pL2CWq6O0t0h9sPY-0
	AnalyticsScreenName_CC_WAITLIST_SCREEN AnalyticsScreenName = 750
	// Screen to decide the onboarding flow (which product/feature) depending on intent
	// https://www.figma.com/file/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?type=design&node-id=19488-11008&mode=design&t=AnlM2qeucAxI9nF3-0
	AnalyticsScreenName_ONBOARDING_INTENT_SELECTION AnalyticsScreenName = 751
	// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=317%3A28334&mode=dev
	AnalyticsScreenName_NET_WORTH_DEPOSITS_CA_NO_ACC_DISCOVERED_SCREEN AnalyticsScreenName = 752
	// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=317-27417&mode=design&t=qEocfuXx66ZPkul3-4
	AnalyticsScreenName_NET_WORTH_DEPOSITS_SCREEN AnalyticsScreenName = 753
	// Screen for BKYC consent
	AnalyticsScreenName_BKYC_CONSENT_SCREEN AnalyticsScreenName = 754
	// Screen for BKYC Handshake
	AnalyticsScreenName_BKYC_HANDSHAKE_SCREEN AnalyticsScreenName = 755
	// Screen for BKYC user details fetched
	AnalyticsScreenName_BKYC_USER_DETAILS_SCREEN AnalyticsScreenName = 756
	// Screen for BKYC selection choice
	AnalyticsScreenName_BKYC_CHOICE_SCREEN AnalyticsScreenName = 757
	// Screen for generic erro
	AnalyticsScreenName_DEFAULT_ERROR_SCREEN AnalyticsScreenName = 758
	// Fi lite lending - name and gender form screen. https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-32126&mode=dev
	AnalyticsScreenName_PL_NAME_GENDER_SCREEN AnalyticsScreenName = 759
	// Fi lite lending - PAN and DOB form screen. https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-32242&mode=dev
	AnalyticsScreenName_PL_PAN_DOB_SCREEN AnalyticsScreenName = 760
	// Fi lite lending - bank details form screen for penny drop. https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-32913&mode=dev
	AnalyticsScreenName_PL_BANKING_DETAILS_SCREEN AnalyticsScreenName = 761
	// Fi lite lending - credit report check screen. https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-31729&mode=dev
	AnalyticsScreenName_PL_CREDIT_REPORT_FETCH_CONSENT_SCREEN AnalyticsScreenName = 762
	// Fi lite lending - landing page check eligibility screen. https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=27387-8565&mode=dev
	AnalyticsScreenName_PL_LOAN_ELIGIBILITY_LANDING_SCREEN AnalyticsScreenName = 763
	// Loan Application/Eligibility Review details screen with all the application data filled by the user till now
	AnalyticsScreenName_PL_APPLICATION_REVIEW_DETAILS_SCREEN AnalyticsScreenName = 764
	// Loan Application/Eligibility loading screen after submission of all the application data filled by the user
	AnalyticsScreenName_PL_CHECK_ELIGIBILITY_LOADING_SCREEN AnalyticsScreenName = 765
	// Loan offer available success screen - https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-31262&mode=dev
	AnalyticsScreenName_PL_ELIGIBILITY_OFFER_AVAILABLE_SCREEN AnalyticsScreenName = 766
	// Loan offer not available screen - https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=29325-52592&mode=dev
	AnalyticsScreenName_PL_ELIGIBILITY_NO_OFFER_AVAILABLE_SCREEN AnalyticsScreenName = 767
	// non qualified user loan dashboard screen - https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=7697-26950&mode=design&t=ohlCCexI5CqtVe8n-0
	AnalyticsScreenName_PL_NON_ELIGIBLE_USER_LANDING_SCREEN AnalyticsScreenName = 768
	// Screen for credit card intro https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=1399-20302&mode=dev
	AnalyticsScreenName_CC_HORIZONTAL_INTRO AnalyticsScreenName = 769
	// Figma: https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?type=design&node-id=9701-42332&mode=design&t=2EHzNmCp0NDQRrgc-4
	AnalyticsScreenName_CC_AMPLI_FI_SCREEN AnalyticsScreenName = 770
	// Credit card ineligible user screen - https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=2539-20531&mode=design&t=G1MP0tw5tHn8d1n0-0
	AnalyticsScreenName_CC_INELIGIBLE_USER_SCREEN AnalyticsScreenName = 771
	// https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/%E2%9A%A1%EF%B8%8F-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?type=design&node-id=34462-59053&mode=design&t=XFj9NqLrmbQmddK2-0
	AnalyticsScreenName_ONBOARDING_SAVINGS_ACCOUNT_INTRO AnalyticsScreenName = 772
	// Client calls the ResetOnboardingStage RPC
	AnalyticsScreenName_RESET_ONBOARDING_STAGE_RPC AnalyticsScreenName = 773
	// Networth Dashboard screen - https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-•-FFF?node-id=157%3A13236&mode=dev
	AnalyticsScreenName_NETWORTH_DASHBOARD_SCREEN AnalyticsScreenName = 774
	// this deeplink calls a rpc with mentioned payload and redirects based on next_action sent back.
	// the rpc being called should have a next_deeplink in response.
	AnalyticsScreenName_RPC_BASED_REDIRECTION AnalyticsScreenName = 775
	// // https://www.figma.com/file/x1hL90FILdP836CGYpOQwZ/Fi-lite-Onboarding?type=design&node-id=2267-87461&mode=design&t=319NTHmrko9s6Foy-4
	AnalyticsScreenName_PL_ACQ_TO_LEND_LANDING_SCREEN AnalyticsScreenName = 776
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=30915-43461&mode=dev
	AnalyticsScreenName_PL_EDIT_LOAN_BOTTOM_SHEET AnalyticsScreenName = 777
	// LoansInfoScreenOptions used as a generic intro/error/popup screen
	// which might have center image, title, desc, bullet points, term infos, Cta for deeplink navigation
	// e.g.-
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=31119-26825&mode=design&t=6T29915B1N6Q7zST-4
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=29358-62846&mode=design&t=6T29915B1N6Q7zST-4
	AnalyticsScreenName_LOANS_INFO_SCREEN AnalyticsScreenName = 778
	// Screen/bottomsheet to show details on how to unlock a locked reward
	// https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=17542-92350&mode=design&t=fcwnnWfhsNsHnDKr-0
	AnalyticsScreenName_REWARD_UNLOCK_DETAILS_BOTTOMSHEET AnalyticsScreenName = 779
	// Screen to show details about and offer being redeemed
	// https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=14751-85330&mode=design&t=fcwnnWfhsNsHnDKr-4
	AnalyticsScreenName_OFFER_REDEEM_INFO_BOTTOMSHEET AnalyticsScreenName = 780
	// Screen for starting the Vistara offer conversion flow
	// https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=14849-83928&mode=design&t=<DUfDdqpjthYBBlVz-4></DUfDdqpjthYBBlVz-4>
	AnalyticsScreenName_OFFER_VISTARA_CONVERSION_BOTTOMSHEET AnalyticsScreenName = 781
	// Screen for entering the user details for Vista offer conversion/redemption
	// https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=14849-84668&mode=design&t=DUfDdqpjthYBBlVz-4
	AnalyticsScreenName_OFFER_VISTARA_ENTER_DETAILS_BOTTOMSHEET AnalyticsScreenName = 782
	// A dialog screen to select the addess for offer redemption
	// https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=3905-0&mode=design&t=DUfDdqpjthYBBlVz-4
	AnalyticsScreenName_OFFER_SELECT_ADDRESS_DIALOG AnalyticsScreenName = 783
	// Screen for calculator functionality for Salary
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=9203%3A75647&t=qQTT97Bl6HdZlPD8-4
	AnalyticsScreenName_SALARY_CALCULATOR_SCREEN AnalyticsScreenName = 784
	// Screen for enabling permissions required for Early salary loans
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=11188%3A12539&t=pqnX0EtVisnkLZwO-1
	AnalyticsScreenName_ES_ENABLE_PERMISSIONS_SCREEN AnalyticsScreenName = 785
	// Screen for referring colleagues for salary account
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=6465%3A65794
	AnalyticsScreenName_SALARY_REFERRAL_LANDING_SCREEN AnalyticsScreenName = 786
	// Screen for Policy information for Salary
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=10078-79364&t=Zv5GKZKiOOk81zT4-4
	AnalyticsScreenName_SALARY_POLICY_INFO_SCREEN AnalyticsScreenName = 787
	// Screen for showing details about sending cancellation of Enach
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=13350-98579&mode=design&t=iLgHFHqZGIouTBMI-4
	AnalyticsScreenName_SALARY_ENACH_CANCEL_MADATE_SETUP_SCREEN AnalyticsScreenName = 788
	// Screen for setting up user details for enach mandate setup
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=13509-102092&mode=design&t=D2xF5EmgLckVOEmi-4
	AnalyticsScreenName_SALARY_ENACH_MANDATE_SETUP_SCREEN AnalyticsScreenName = 789
	// Screen for showing the status of the Enach mandate setup
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=12804-94550&mode=dev
	AnalyticsScreenName_SALARY_ENACH_SETUP_STATUS AnalyticsScreenName = 790
	// Screen for showing the tabs for introduction to Enach before starting the setup flow
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=13233-121072&mode=design&t=i9kw6EswYXJuVlVl-4
	AnalyticsScreenName_SALARY_ENACH_INTRO_SCREEN AnalyticsScreenName = 791
	// Screen for showing the upgrade screen for Salary
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=15689-102488&mode=design&t=SGt3YBju8n0DJpjz-4
	AnalyticsScreenName_SALARY_ENACH_FULL_UPGRADE_SCREEN AnalyticsScreenName = 792
	// Screen for showing preview of the scanned cheque
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=10037-80096&t=DP9MDCaFW2Od3H9x-4
	AnalyticsScreenName_CANCELLED_CHEQUE_IN_PREVIEW_SCREEN AnalyticsScreenName = 793
	// Screen for showing the status of Salary verification after selecting the salary transaction
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=559-59310&mode=design&t=WpvHWitvXwZnFdOl-4
	AnalyticsScreenName_SALARY_VERIFICATION_STATUS_SCREEN AnalyticsScreenName = 794
	// Screen, shown as a dialog about the Coin amount pending/in processing
	// https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=17542-92715&mode=design&t=<h1dzv7nA0hau5Kx5-0></h1dzv7nA0hau5Kx5-0>
	AnalyticsScreenName_REWARD_PENDING_DETAILS_SCREEN AnalyticsScreenName = 795
	// Screen to display a list/grid of user's unopened rewards
	// https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=16524-90681&mode=design&t=ThSRcrfkIwhSjBPC-0
	AnalyticsScreenName_REWARD_UNOPENED_LIST_SCREEN AnalyticsScreenName = 796
	// screen that will be used while searching for FAQ categories
	// figma: https://www.figma.com/file/GGiPlpxpvYa2eHdg68m0pg/Help-%E2%80%A2-Workfile?type=design&node-id=6757-63692&mode=dev
	AnalyticsScreenName_CX_CATEGORY_SEARCH_SCREEN AnalyticsScreenName = 797
	// Screen for DC Order Revamp
	AnalyticsScreenName_DC_PHYSICAL_ORDER_SCREEN AnalyticsScreenName = 798
	// screen to show CX category details
	AnalyticsScreenName_CX_CATEGORY_DETAILS_SCREEN AnalyticsScreenName = 799
	// screen for showing dashboard for loans
	// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=447-10393&mode=design&t=AiaZVWMZruzMHTZW-0
	AnalyticsScreenName_LOANS_DASHBOARD_SCREEN AnalyticsScreenName = 800
	// screen for showing overview of a specific loan, such as emi details, loan interest rate, principal amt, paid amt etc.
	// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-15210&mode=design&t=AiaZVWMZruzMHTZW-0
	AnalyticsScreenName_LOANS_OVERVIEW_SCREEN AnalyticsScreenName = 801
	// screen for showing all the details regarding the loan
	// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-15163&mode=design&t=AiaZVWMZruzMHTZW-0
	AnalyticsScreenName_LOANS_DETAILS_SCREEN AnalyticsScreenName = 802
	// screen for showing loan payment details and upcoming emi schedule.
	// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=1304-13603&mode=design&t=AiaZVWMZruzMHTZW-0
	AnalyticsScreenName_LOANS_PAYMENT_DETAILS_SCREEN AnalyticsScreenName = 803
	// screen for selecting employer for loans
	AnalyticsScreenName_PL_EMPLOYER_SELECTION_SCREEN AnalyticsScreenName = 804
	// screen for location permissions for loans
	AnalyticsScreenName_PL_LOCATION_PERMISSION_SCREEN AnalyticsScreenName = 805
	// HelpLanding V2
	AnalyticsScreenName_HELP_LANDING_SCREEN_V2 AnalyticsScreenName = 806
	// loading screen that triggers otp generation for mf holdings import https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3927&mode=design&t=kSKONPbGKIO38wtR-4
	AnalyticsScreenName_MF_HOLDINGS_IMPORT_GENERATE_OTP_SCREEN AnalyticsScreenName = 807
	// screen to input mf holdings import otp https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3810&mode=design&t=kSKONPbGKIO38wtR-4
	AnalyticsScreenName_MF_HOLDINGS_IMPORT_OTP_SUBMISSION_SCREEN AnalyticsScreenName = 808
	// screen to show import progress post otp submission https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-4143&mode=design&t=kSKONPbGKIO38wtR-4
	AnalyticsScreenName_MF_HOLDINGS_IMPORT_OTP_SUCCESS_LOADING_SCREEN AnalyticsScreenName = 809
	// screen for user to manually enter phone number for mf holdings import https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3945&mode=design&t=kSKONPbGKIO38wtR-4
	AnalyticsScreenName_MF_HOLDINGS_IMPORT_PHONE_NUMBER_SUBMISSION_SCREEN AnalyticsScreenName = 810
	// screen for user to manually enter email for mf holdings import https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3992&mode=design&t=kSKONPbGKIO38wtR-4
	AnalyticsScreenName_MF_HOLDINGS_IMPORT_EMAIL_SUBMISSION_SCREEN AnalyticsScreenName = 811
	// mutual fund import consent screen v2 https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3759&mode=design&t=kSKONPbGKIO38wtR-4
	AnalyticsScreenName_MF_HOLDINGS_IMPORT_CONSENT_SCREEN_V2 AnalyticsScreenName = 812
	// This screen will be used to prompt user to connect a bank account.
	// E.g. In Fi Lite Pay, if no account is connected then user will be prompted
	// to connect a bank account which then could be used to Pay.
	// figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=19320-18968&mode=design&t=Sval55XxIC9inTWk-0
	AnalyticsScreenName_CONNECT_BANK_ACCOUNTS_INTRO_SCREEN AnalyticsScreenName = 813
	// https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=15143-18604&mode=design&t=woYX9XiNBN1oObov-0
	// screen to upload FILE
	AnalyticsScreenName_UPLOAD_FILE AnalyticsScreenName = 814
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153%3A35008
	// US Stocks Wallet Landing Screen
	AnalyticsScreenName_US_STOCKS_WALLET_LANDING_SCREEN AnalyticsScreenName = 815
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153%3A37080&mode=design&t=ZSndRrtzjFyIUnvB-1
	// US Stocks Wallet Add Funds Screen
	AnalyticsScreenName_US_STOCKS_WALLET_ADD_FUNDS_SCREEN AnalyticsScreenName = 816
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153%3A37003&mode=design&t=ZSndRrtzjFyIUnvB-1
	// US Stocks Wallet Withdraw Screen
	AnalyticsScreenName_US_STOCKS_WALLET_WITHDRAW_SCREEN AnalyticsScreenName = 817
	AnalyticsScreenName_LAMF_LANDING_SCREEN                                   AnalyticsScreenName = 818
	AnalyticsScreenName_LAMF_JOURNEY_SCREEN                                   AnalyticsScreenName = 819
	AnalyticsScreenName_LAMF_ADD_ACCOUNT_DETAILS_USER_INPUT_SCREEN            AnalyticsScreenName = 820
	AnalyticsScreenName_LAMF_CAMS_PORTFOLIO_FETCH_OTP_VERIFICATION_SCREEN     AnalyticsScreenName = 821
	AnalyticsScreenName_LAMF_KARVY_PORTFOLIO_FETCH_OTP_VERIFICATION_SCREEN    AnalyticsScreenName = 822
	AnalyticsScreenName_LAMF_VIEW_OFFER_SCREEN                                AnalyticsScreenName = 823
	AnalyticsScreenName_LAMF_OFFER_SELECTION_SCREEN                           AnalyticsScreenName = 824
	AnalyticsScreenName_LAMF_APPLICATION_DETAIL_SCREEN                        AnalyticsScreenName = 825
	AnalyticsScreenName_LAMF_APPLICATION_ADDITIONAL_DETAILS_USER_INPUT_SCREEN AnalyticsScreenName = 826
	AnalyticsScreenName_LAMF_KYC_SCREEN                                       AnalyticsScreenName = 827
	AnalyticsScreenName_LAMF_ESIGN_SCREEN                                     AnalyticsScreenName = 828
	AnalyticsScreenName_LAMF_MANDATE_SCREEN                                   AnalyticsScreenName = 829
	AnalyticsScreenName_LAMF_CAMS_LIEN_MARK_OTP_VERIFICATION_SCREEN           AnalyticsScreenName = 830
	AnalyticsScreenName_LAMF_KARVY_LIEN_MARK_OTP_VERIFICATION_SCREEN          AnalyticsScreenName = 831
	AnalyticsScreenName_LAMF_LIEN_PROGRESS_UPDATE_SCREEN                      AnalyticsScreenName = 832
	AnalyticsScreenName_LAMF_ESIGN_PROGRESS_UPDATE_SCREEN                     AnalyticsScreenName = 833
	AnalyticsScreenName_LAMF_MANDATE_INTRO_SCREEN                             AnalyticsScreenName = 834
	AnalyticsScreenName_LAMF_APPLICATION_SUCCESS_SCREEN                       AnalyticsScreenName = 835
	AnalyticsScreenName_LAMF_OFFER_FUND_DETAILS_SCREEN                        AnalyticsScreenName = 836
	AnalyticsScreenName_LAMF_INSUFFICIENT_FOLIO_ERROR_SCREEN                  AnalyticsScreenName = 837
	AnalyticsScreenName_LAMF_PARTIAL_LIEN_MARK_ERROR_SCREEN                   AnalyticsScreenName = 838
	AnalyticsScreenName_LAMF_PERMANENT_FAILURE_FULL_SCREEN                    AnalyticsScreenName = 839
	AnalyticsScreenName_LAMF_HOLDING_SCREEN                                   AnalyticsScreenName = 840
	AnalyticsScreenName_LAMF_DASHBOARD_SCREEN                                 AnalyticsScreenName = 841
	AnalyticsScreenName_LAMF_LOAN_OVERVIEW_SCREEN                             AnalyticsScreenName = 842
	AnalyticsScreenName_LAMF_PAYMENT_DETAILS_SCREEN                           AnalyticsScreenName = 843
	AnalyticsScreenName_LAMF_LOAN_DETAILS_SCREEN                              AnalyticsScreenName = 844
	AnalyticsScreenName_LAMF_INITIATE_SI_SETUP_SCREEN                         AnalyticsScreenName = 845
	AnalyticsScreenName_LAMF_PRE_PAY_SCREEN                                   AnalyticsScreenName = 846
	AnalyticsScreenName_LAMF_FUND_ELIGIBILITY_SCREEN                          AnalyticsScreenName = 847
	AnalyticsScreenName_LAMF_NO_FUNDS_FOUND_FAILURE_SCREEN                    AnalyticsScreenName = 848
	// Figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=36659-29100&mode=dev
	AnalyticsScreenName_CC_LOUNGE_ACCESS_V2_SCREEN AnalyticsScreenName = 849
	// Figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=27502-82619&mode=design&t=ictcVPmkK7Bzz1wL-4
	AnalyticsScreenName_CC_COLLECTED_LOUNGE_PASSES_SCREEN AnalyticsScreenName = 850
	// Screen to take selfie image for lending flows
	AnalyticsScreenName_LOANS_SELFIE_SCREEN AnalyticsScreenName = 851
	// Screen to collect form data from user
	AnalyticsScreenName_LOANS_FORM_DETAILS_SCREEN AnalyticsScreenName = 852
	// Preapproved Loan S1 - shows the loan offer info - V2
	AnalyticsScreenName_PL_OFFER_DETAILS_V2 AnalyticsScreenName = 853
	// PL S2 - Shows application info and the Terms and conditions page - V2
	AnalyticsScreenName_PL_APPLICATION_DETAILS_V2 AnalyticsScreenName = 854
	// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10082-40718&mode=design&t=fEGbs85eYqgpYdX1-0
	AnalyticsScreenName_LOANS_INCOME_VERIFICATION_INTRO_SCREEN AnalyticsScreenName = 855
	// https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=11201-113965&mode=<dev></dev>
	AnalyticsScreenName_CA_FI_TO_FI_POLLING_SCREEN AnalyticsScreenName = 856
	// https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=10505-120314&mode=dev
	AnalyticsScreenName_CA_FI_TO_FI_TERMINAL_SCREEN AnalyticsScreenName = 857
	// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10082-40545&mode=design&t=fEGbs85eYqgpYdX1-0
	AnalyticsScreenName_LOANS_INCOME_VERIFICATION_RESULT_SCREEN AnalyticsScreenName = 858
	// Figma link: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=35086-65441&mode=design&t=Jcs4ENky4VNTTl5e-4
	AnalyticsScreenName_CREDIT_CARD_BILLING_DETAILS_BOTTOM_VIEW_SCREEN AnalyticsScreenName = 859
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21011%3A26702&mode=dev
	AnalyticsScreenName_US_STOCKS_ONBOARDING_RISKY_PROILE_TERMINAL_SCREEN AnalyticsScreenName = 860
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21011%3A25979&mode=dev
	AnalyticsScreenName_US_STOCKS_ONBOARDING_CREATE_PROFILE_SCREEN AnalyticsScreenName = 861
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21011%3A26228&mode=dev
	AnalyticsScreenName_US_STOCKS_ONBOARDING_COLLECT_RISK_LEVEL_SCREEN AnalyticsScreenName = 862
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21202%3A16924&mode=dev
	AnalyticsScreenName_US_STOCKS_ONBOARDING_RISK_DISCLOSURE_SCREEN AnalyticsScreenName = 863
	// https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=4743-29973&mode=design&t=61MUz6ktBYAlcm8Z-4
	AnalyticsScreenName_CC_ALL_ELIGIBLE_CARDS_SCREEN AnalyticsScreenName = 864
	// https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=4751-30530&mode=design&t=BwXyZn6Xce97eOjp-4
	AnalyticsScreenName_CC_NETWORK_SELECTION_SCREEN AnalyticsScreenName = 865
	// Intermediate screen to be shown to the user for landing them on the PWA flow.
	// On receiving a deeplink with his screen name, the client needs to call the GetPwaDeeplink Loans FE rpc with the request params (loan_request_id/loan_account_id) to get appropriate PWA deeplink for redirecting to the PWA flow.
	// screen_options: api.typesv2.deeplink_screen_option.preapprovedloans.LoansPWALandingScreenOptions
	AnalyticsScreenName_LOANS_PWA_LANDING_SCREEN AnalyticsScreenName = 866
	// screen to redirect the user to a PWA flow, the screen options contain a web url which the client should open in a web view for redirecting the user to the PWA flow.
	// screen_options: api.typesv2.deeplink_screen_option.pkg.PWARedirectionScreenOptions
	// sample pwa screen figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10651-39414&mode=design&t=HaJNCIwtunJUHdcu-0
	AnalyticsScreenName_PWA_REDIRECTION_SCREEN AnalyticsScreenName = 867
	// screen to display bottom sheet for loan's usecases.
	// screen_options: api.typesv2.deeplink_screen_option.pkg.LoansBottomSheetScreenOptions
	// figma : https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10681-40109&mode=design&t=HaJNCIwtunJUHdcu-0
	AnalyticsScreenName_LOANS_BOTTOM_SHEET_SCREEN AnalyticsScreenName = 868
	// ApplyForLoan() Rpc will be called whenever client receives this deeplink
	// screen_options: api.typesv2.deeplink_screen_option.pkg.ApplyForLoanScreenOptions
	AnalyticsScreenName_APPLY_FOR_LOAN_SCREEN AnalyticsScreenName = 869
	// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=9767-33940&mode=design&t=9JUf66Uqq9yVYQbc-0
	AnalyticsScreenName_PL_INITIATE_MANDATE_V2_SCREEN AnalyticsScreenName = 870
	// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10204-50117&mode=design&t=9JUf66Uqq9yVYQbc-0
	AnalyticsScreenName_PL_ALTERNATE_ACCOUNTS_SCREEN AnalyticsScreenName = 871
	// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10204-50199&mode=design&t=9JUf66Uqq9yVYQbc-0
	AnalyticsScreenName_PL_MANDATE_SETUP_SCREEN AnalyticsScreenName = 872
	// benefit screen for saving account closure
	// Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-3384&mode=design&t=CRwxtzdEnhr4kgl6-4
	AnalyticsScreenName_SA_CLOSURE_BENEFIT_SCREEN AnalyticsScreenName = 873
	// feedback screen for account closure
	// Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-3622&mode=design&t=QaLHIsX3YnmLnY1U-4
	AnalyticsScreenName_SA_CLOSURE_FEEDBACK_SCREEN AnalyticsScreenName = 874
	// crrieria screen for account closure
	// Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-3892&mode=design&t=HTKzSO6UMNfip928-4
	AnalyticsScreenName_SA_CLOSURE_CRITERIA_SCREEN AnalyticsScreenName = 875
	// pan dob screen for account closure
	// Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-3660&mode=design&t=HTKzSO6UMNfip928-4
	AnalyticsScreenName_PAN_DOB_INPUT_SCREEN AnalyticsScreenName = 876
	// conform account closure screen
	// Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-4878&mode=design&t=HTKzSO6UMNfip928-4
	AnalyticsScreenName_SA_CLOSURE_SUBMIT_REQUEST_SWIPE_ACTION_SCREEN AnalyticsScreenName = 877
	// info screen for account closure user for credit/debit/all freeze
	// Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-3783&mode=design&t=HTKzSO6UMNfip928-4
	AnalyticsScreenName_FULL_SCREEN_INFO_VIEW_SCREEN AnalyticsScreenName = 878
	// resolve screen for account closure
	// Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-4860&mode=design&t=HTKzSO6UMNfip928-4
	AnalyticsScreenName_SA_CLOSURE_RESOLVE_ISSUE_SCREEN AnalyticsScreenName = 879
	// request submitted screen for account closure
	// Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-4878&mode=design&t=HTKzSO6UMNfip928-4
	AnalyticsScreenName_SA_CLOSURE_REQUEST_SUBMITTED_SCREEN AnalyticsScreenName = 880
	// mf holding import progress screen
	// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-•-FFF?node-id=13988%3A4143&mode=dev
	AnalyticsScreenName_MF_HOLDINGS_IMPORT_PROGRESS_SCREEN AnalyticsScreenName = 881
	// shows a loader screen for the given time
	// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=11980-39468&mode=design&t=aY7iBNs7wWpacNRY-4
	AnalyticsScreenName_LOANS_TIMED_LOADER_SCREEN AnalyticsScreenName = 882
	// screen to show tiering success screen in onboarding add funds flow
	// figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=271-4523&mode=design&t=6QdRNpZBHIWJfxn7-0
	AnalyticsScreenName_ONBOARDING_ADD_FUNDS_TIERING_SUCCESS_SCREEN AnalyticsScreenName = 883
	AnalyticsScreenName_LAMF_KFS_SCREEN                             AnalyticsScreenName = 884
	// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=12418-32528&mode=design&t=7lvO50N6k4ZpCffp-4
	AnalyticsScreenName_LOANS_WEBVIEW_DIGILOCKER AnalyticsScreenName = 885
	// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=6865-40084&mode=design&t=1WdaJupvl7JxkUjv-0
	AnalyticsScreenName_LAMF_FUND_VERIFICATION_FAILURE_SCREEN AnalyticsScreenName = 886
	// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=796-18746&mode=design&t=SLnAF9lfdz8B4bsq-0
	AnalyticsScreenName_LAMF_VERIFY_MF_PROGRESS_UPDATE_SCREEN AnalyticsScreenName = 887
	// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=6724-36820&mode=design&t=SLnAF9lfdz8B4bsq-0
	AnalyticsScreenName_LAMF_LOAN_DETAIL_VERIFICATION_SCREEN AnalyticsScreenName = 888
	// https://www.figma.com/file/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=6213%3A146440&t=yCYsZOpAcDpVohlP-4
	AnalyticsScreenName_DC_PHYSICAL_BENEFITS_SCREEN AnalyticsScreenName = 889
	// https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=9387-35619&mode=design&t=nev9Kv61kmKaae8H-0
	AnalyticsScreenName_DC_CHECK_STATUS_FLOW_SCREEN AnalyticsScreenName = 890
	// https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=9387-35619&mode=design&t=nev9Kv61kmKaae8H-0
	AnalyticsScreenName_DC_INITIATE_FLOW_SCREEN AnalyticsScreenName = 891
	// https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=10214%3A38287&t=ysv2mA1h1Im9yPs7-4
	AnalyticsScreenName_ORDER_CARD_SUCCESS_SCREEN AnalyticsScreenName = 892
	// Screen to show the success message once the workflow for new card request is completed
	AnalyticsScreenName_NEW_CARD_REQUEST_SUCCESS_SCREEN AnalyticsScreenName = 893
	AnalyticsScreenName_DC_RESET_ATM_PIN_OPTIONS_SCREEN AnalyticsScreenName = 894
	// Polling fragment for RPC : getRequestStatusSync
	AnalyticsScreenName_CC_SYNC_POLL_SCREEN AnalyticsScreenName = 895
	// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=7183%3A22787
	AnalyticsScreenName_CC_STATION_HALT_SCREEN AnalyticsScreenName = 896
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=38152-40247&mode=design&t=ALXMmmJXnlKNlvQi-4
	AnalyticsScreenName_LOANS_LANDING_SCREEN_V2 AnalyticsScreenName = 897
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=38152-40190&mode=design&t=ALXMmmJXnlKNlvQi-4
	AnalyticsScreenName_LOANS_AMOUNT_SELECTOR_SCREEN AnalyticsScreenName = 898
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=38152-40313&mode=design&t=ALXMmmJXnlKNlvQi-4
	AnalyticsScreenName_LOANS_DETAILS_SELECTION_V2 AnalyticsScreenName = 899
	// Card details screen for reset pin via debit card
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=8022%3A71005&mode=design&t=h58zOd3dgZ3DMmrO-1
	AnalyticsScreenName_RESET_PIN_CARD_DETAILS_SCREEN AnalyticsScreenName = 900
	// Generic Terminal screen for UPI related use cases like UPI International
	// Figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?type=design&node-id=14063-93268&t=lPHC9WiC60P4BVxa-0
	AnalyticsScreenName_PAY_TERMINAL_STATUS_SCREEN AnalyticsScreenName = 901
	// ENach Management Polling Screen
	AnalyticsScreenName_RECURRING_PAYMENT_ACTION_STATUS_SCREEN AnalyticsScreenName = 902
	// External Enach management screen
	AnalyticsScreenName_RECURRING_PAYMENT_WEB_VIEW_SCREEN AnalyticsScreenName = 903
	// Pin set screen v2 post Aadhar OTP implementation
	// https://www.figma.com/file/xzcr5E8lsH1ihsh3RmqQTU/Pay-%E2%80%A2-FFF-%E2%80%A2--v1.3?node-id=9929-23547&t=8S4mD3lPzpg13mFN-0
	AnalyticsScreenName_NPCI_PIN_SET_SCREEN_V2 AnalyticsScreenName = 904
	// Post VPA Migration Success Screen in non-unified flow
	// figma.com/file/xzcr5E8lsH1ihsh3RmqQTU/Pay-•-FFF-•--v1.3?node-id=9-15383&t=jLxnxQvPowcQR8QI-0
	AnalyticsScreenName_VPA_MIGRATION_SUCCESS_SCREEN_V1 AnalyticsScreenName = 905
	// TPAP Bank Selection screen
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=5833%3A60144
	AnalyticsScreenName_TPAP_BANK_SELECTION_SCREEN AnalyticsScreenName = 906
	// TPAP Account Selection screen
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=5833%3A60135&t=GnIKPHkckqtiS4Sk-0
	AnalyticsScreenName_TPAP_ACCOUNT_SELECTION_SCREEN AnalyticsScreenName = 907
	// TPAP Self Transfer screen
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=6429%3A68236&t=ywAju9MzCCx4CjzK-0
	AnalyticsScreenName_TPAP_SELF_TRANSFER_SCREEN AnalyticsScreenName = 908
	// TPAP Linking success screen
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=7723%3A71399
	AnalyticsScreenName_TPAP_LINKING_SUCCESS_SCREEN AnalyticsScreenName = 909
	// Unified TPAP flow VPA Migration Intro Screen
	AnalyticsScreenName_VPA_MIGRATION_INTRO_SCREEN AnalyticsScreenName = 910
	// Unified TPAP flow VPA Migration Success Screen
	AnalyticsScreenName_VPA_MIGRATION_SUCCESS_SCREEN_V2 AnalyticsScreenName = 911
	// Screen where user can select an account to enable international UPI payments via QR scanning flow
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?type=design&node-id=14051-94034&t=AsIo4odA5dJP63eu-0
	AnalyticsScreenName_UPI_INTERNATIONAL_ACTIVATION_SCREEN AnalyticsScreenName = 912
	// Screen where user will enter amount and select currency for international UPI payments
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?type=design&node-id=14128-99426&t=H03zUCoNFz3moSUN-0
	AnalyticsScreenName_UPI_INTERNATION_ENTER_AMOUNT_SCREEN AnalyticsScreenName = 913
	// UPI Tips videos screen
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=12848-87062&t=IEIwA9WMq43ehOpK-0
	AnalyticsScreenName_UPI_TIPS_SCREEN AnalyticsScreenName = 914
	// Intro screen for UPI Mapper
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=10600%3A82987&t=8SQcht9rnIJLnPgQ-0
	AnalyticsScreenName_UPI_MAPPER_INTRO_SCREEN AnalyticsScreenName = 915
	// Details screen for UPI Mapper
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=9782%3A84002&t=88mgI7GF6ZKOySSA-0
	AnalyticsScreenName_UPI_MAPPER_DETAILS_SCREEN AnalyticsScreenName = 916
	// Add money screen for UPI Lite
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=16172%3A99492&mode=dev
	AnalyticsScreenName_UPI_LITE_ADD_MONEY_SCREEN AnalyticsScreenName = 917
	// Introduction screen for UPI Lite
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=15433%3A98560
	AnalyticsScreenName_UPI_LITE_INTRO_SCREEN AnalyticsScreenName = 918
	// Pay x AskFi Search Screen
	AnalyticsScreenName_PAY_ASK_FI_SEARCH_SCREEN AnalyticsScreenName = 919
	// Screen for entering the Fi coins for Vendor points conversion
	// https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=14849-83928&mode=design&t=g2ZiSUaQJv4koNz2-4
	AnalyticsScreenName_OFFER_FI_COINS_CONVERSION_BOTTOMSHEET AnalyticsScreenName = 920
	// Screen for entering the user details for Vista offer conversion/redemption
	// https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=14849-84668&mode=design&t=DUfDdqpjthYBBlVz-4
	AnalyticsScreenName_OFFER_FI_COINS_ENTER_DETAILS_BOTTOMSHEET AnalyticsScreenName = 921
	// Screen for redirecting users for Employer doc submission:
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=540%3A85908
	AnalyticsScreenName_SALARY_EMPLOYER_DOC_SUBMISSION_BOTTOMSHEET AnalyticsScreenName = 922
	// Screen for searching Employer names
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=11867-84275&mode=dev
	AnalyticsScreenName_SALARY_EMPLOYER_SEARCH_BOTTOMSHEET AnalyticsScreenName = 923
	// Salary verification success/confirmed screen
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=540%3A81469
	AnalyticsScreenName_SALARY_EMPLOYER_CONFIRMATION_BOTTOMSHEET AnalyticsScreenName = 924
	// Salary verification failure bottomsheet:
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=12143-89950&t=04g8kygIhf0ip4PJ-4
	AnalyticsScreenName_SALARY_EMPLOYER_VERIFICATION_FAILED_BOTTOMSHEET AnalyticsScreenName = 925
	// Screen for showing Salary-Enach benefits
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=13233-121074&mode=design&t=aoCYiKyHtt7ejKF7-4
	AnalyticsScreenName_SALARY_ENACH_BENEFITS_SCREEN AnalyticsScreenName = 926
	// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=6724-36851&mode=design&t=laf3rWXOSA2nC4mh-4
	AnalyticsScreenName_LAMF_FUND_VERIFICATION_BOTTOM_SHEET AnalyticsScreenName = 927
	// https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=9483-107713&mode=dev
	AnalyticsScreenName_AA_CONSENT_RENEWAL_DIALOG AnalyticsScreenName = 928
	// To start connected SDK for renewal flow
	AnalyticsScreenName_AA_CONSENT_RENEWAL_SDK_SCREEN AnalyticsScreenName = 929
	// To start connected SDK for connect flow
	AnalyticsScreenName_AA_SDK_SCREEN AnalyticsScreenName = 930
	// To start connected SDK for fi to fi flow
	AnalyticsScreenName_AA_FI_TO_FI_SDK_SCREEN AnalyticsScreenName = 931
	// Post consent approval, polling screen
	AnalyticsScreenName_AA_POLLING_SCREEN AnalyticsScreenName = 932
	// Post polling terminal screen
	AnalyticsScreenName_AA_TERMINAL_SCREEN AnalyticsScreenName = 933
	// Reoobe screen, when users phone number changes and want to connect account
	AnalyticsScreenName_AA_REOOBE_SCREEN AnalyticsScreenName = 934
	// Transaction coming soon, when for a particular bank/entity transaction not enabled
	AnalyticsScreenName_AA_TRANSACTION_COMING_SOON AnalyticsScreenName = 935
	// Reports and Downloads on profile screen
	AnalyticsScreenName_PROFILE_REPORTS_DOWNLOADS AnalyticsScreenName = 936
	// Service request on profile screen
	AnalyticsScreenName_PROFILE_SERVICE_REQUESTS AnalyticsScreenName = 937
	// Cheque book order details
	// https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=7365%3A32090&t=oPnJNmnahjMAOhbj-0
	AnalyticsScreenName_CHEQUE_BOOK_ORDER_DETAILS AnalyticsScreenName = 938
	// Legality sign flow on profile screen
	// https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=9281-35326&t=OIbXcnFeyqxSKgsu-4
	AnalyticsScreenName_LEGALITY_SIGN_FLOW AnalyticsScreenName = 939
	// Order cheque book on profile screen
	// https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=7365%3A32164&t=kwGVXjf1ioZorS9a-0
	AnalyticsScreenName_ORDER_CHEQUEBOOK AnalyticsScreenName = 940
	// Savings sign prompt on profile screen
	// https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=9589-37949&t=OIbXcnFeyqxSKgsu-4
	AnalyticsScreenName_SAVINGS_SIGN_PROMPT AnalyticsScreenName = 941
	// Polling screen after placing a chequebook request
	// https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=7365%3A32616&t=NJxprTu4lXJmLO7r-0
	AnalyticsScreenName_CHEQUE_BOOK_POLLING_SCREEN AnalyticsScreenName = 942
	// https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=7365-32528&t=KTbGNKzOF76g92hj-4
	AnalyticsScreenName_ALFRED_REQUEST_SUMMARY AnalyticsScreenName = 943
	// Export logs screen
	AnalyticsScreenName_EXPORT_HEALTH_LOG_SCREEN AnalyticsScreenName = 944
	// Open source licenses
	AnalyticsScreenName_PROFILE_OPEN_SOURCE_LICENSES AnalyticsScreenName = 945
	// Notification settings screen on profile screen
	AnalyticsScreenName_PROFILE_SETTINGS_NOTIFICATION AnalyticsScreenName = 946
	// Enter VPA dialog from Onboarding add funds and add funds
	// https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=1586-63113&mode=dev
	AnalyticsScreenName_DIALOG_ENTER_VPA AnalyticsScreenName = 947
	// Payment method selection, bottom sheet
	// https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=2083-54863&mode=design&t=GgW16c26T4XzKVKT-4
	AnalyticsScreenName_PAYMENT_METHOD_SELECTION_BOTTOM_SHEET AnalyticsScreenName = 948
	// Personal details edit
	AnalyticsScreenName_PROFILE_PERSONAL_DETAILS_EDIT AnalyticsScreenName = 949
	// Profile crop photo
	AnalyticsScreenName_PROFILE_EDIT_IMAGE_SCREEN AnalyticsScreenName = 950
	// Profile selection dialog is showing all the options for changing or deleting pic from user profile
	AnalyticsScreenName_PROFILE_DIALOG_EDIT_OPTION AnalyticsScreenName = 951
	// Screen used to show Information like DOB and address that going to be updated
	// https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=12733-15559&mode=design&t=KFWusVBmRQXnEaz2-4
	AnalyticsScreenName_INFO_ACKNOWLEDGEMENT_V2 AnalyticsScreenName = 952
	// https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?type=design&node-id=13015-57250&mode=design&t=UQTeuem2EFxku9TQ-0
	AnalyticsScreenName_CC_CREDIT_REPORT_ADDRESS_SCREEN AnalyticsScreenName = 953
	// https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-AmpliFi-Credit-Card-%E2%80%A2-FFF?type=design&node-id=6226-23209&mode=design&t=qRSwyCrbsNCWRuU4-0
	//
	//	// screen to collect the consent from the user on the intro credit card screen
	AnalyticsScreenName_CC_CONSENT_BOTTOM_SHEET AnalyticsScreenName = 954
	// analytics screen name for loans esign webview flow
	AnalyticsScreenName_LOANS_ESIGN_WEBVIEW AnalyticsScreenName = 955
	// analytics screen name for loans digio sdk
	AnalyticsScreenName_LOANS_MANDATE_DIGIO_SDK          AnalyticsScreenName = 956
	AnalyticsScreenName_ADD_ADDRESS                      AnalyticsScreenName = 957
	AnalyticsScreenName_EKYC_CONSENT                     AnalyticsScreenName = 958
	AnalyticsScreenName_NAME_DOB_MISMATCH                AnalyticsScreenName = 959
	AnalyticsScreenName_EKYC_VERIFICATION                AnalyticsScreenName = 960
	AnalyticsScreenName_LIVENESS                         AnalyticsScreenName = 961
	AnalyticsScreenName_LIVENESS_POLLING                 AnalyticsScreenName = 962
	AnalyticsScreenName_ONB_ADD_FUNDS                    AnalyticsScreenName = 963
	AnalyticsScreenName_ONB_ADD_MONEY                    AnalyticsScreenName = 964
	AnalyticsScreenName_ONB_ADD_FUNDS_TIERING_SUCCESS    AnalyticsScreenName = 965
	AnalyticsScreenName_FI_BENEFITS                      AnalyticsScreenName = 966
	AnalyticsScreenName_ATM_PIN_VERIFICATION             AnalyticsScreenName = 967
	AnalyticsScreenName_ACC_DELETION_ACK                 AnalyticsScreenName = 968
	AnalyticsScreenName_LIVENESS_MANUAL_VERIFICATION     AnalyticsScreenName = 969
	AnalyticsScreenName_LOGIN_VERIFICATION_ERROR         AnalyticsScreenName = 970
	AnalyticsScreenName_FINITE_CODE_VERIFICATION         AnalyticsScreenName = 971
	AnalyticsScreenName_INCREASE_BONUS_FOR_AFFLUENT_USER AnalyticsScreenName = 972
	AnalyticsScreenName_RE_KYC_SCREEN                    AnalyticsScreenName = 973
	AnalyticsScreenName_PAN_AUTH_SCREEN                  AnalyticsScreenName = 974
	AnalyticsScreenName_AFU_POLL                         AnalyticsScreenName = 975
	AnalyticsScreenName_ACC_CREATION_POLL                AnalyticsScreenName = 976
	AnalyticsScreenName_ONB_NEXT_ACTION_POLL             AnalyticsScreenName = 977
	AnalyticsScreenName_CREDIT_REPORT_AVAILABILITY       AnalyticsScreenName = 978
	AnalyticsScreenName_CREDIT_REPORT_VERIFICATION       AnalyticsScreenName = 979
	AnalyticsScreenName_EMPLOYMENT_VERIFICATION          AnalyticsScreenName = 980
	AnalyticsScreenName_SCREENER_ERROR                   AnalyticsScreenName = 981
	AnalyticsScreenName_SCREENER_REJECT_STATE            AnalyticsScreenName = 982
	AnalyticsScreenName_GMAIL_VERIFICATION_STATUS        AnalyticsScreenName = 983
	AnalyticsScreenName_LINKEDIN_VERIFICATION            AnalyticsScreenName = 984
	AnalyticsScreenName_SIGN_OUT_TRANSITION              AnalyticsScreenName = 985
	AnalyticsScreenName_VKYC_NEXT_ACTION                 AnalyticsScreenName = 986
	AnalyticsScreenName_VKYC_REGISTER_NOTIFICATION       AnalyticsScreenName = 987
	AnalyticsScreenName_VKYC_HEADLESS                    AnalyticsScreenName = 988
	AnalyticsScreenName_EPAN_INIT                        AnalyticsScreenName = 989
	AnalyticsScreenName_VKYC_INSTRUCTION_OVERLAY         AnalyticsScreenName = 990
	AnalyticsScreenName_VKYC_SCHEDULED_ACK               AnalyticsScreenName = 991
	// Screen in lending flow to fetch debit card details required to complete mandate step
	AnalyticsScreenName_LOANS_DEBIT_CARD_DETAILS_SCREEN       AnalyticsScreenName = 992
	AnalyticsScreenName_DC_CARD_RENEWAL_TYPE_SELECTION_SCREEN AnalyticsScreenName = 993
	// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=1%3A5456&mode=dev
	AnalyticsScreenName_EPF_PASSBOOK_IMPORT_CONFIRM_PHONE_NUMBER_SCREEN AnalyticsScreenName = 994
	// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=1%3A5426&mode=dev
	AnalyticsScreenName_DMF_GENERIC_LOADING_SCREEN AnalyticsScreenName = 995
	// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=1%3A5405&mode=dev
	AnalyticsScreenName_EPF_PASSBOOK_IMPORT_UAN_LIST_SCREEN AnalyticsScreenName = 996
	// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=1%3A5790&mode=dev
	AnalyticsScreenName_EPF_PASSBOOK_IMPORT_OTP_SCREEN AnalyticsScreenName = 997
	// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=1%3A5604&mode=dev
	AnalyticsScreenName_EPF_DASHBOARD_SCREEN AnalyticsScreenName = 998
	// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=3712%3A52671&mode=dev
	AnalyticsScreenName_MANUAL_ASSET_FORM_SCREEN AnalyticsScreenName = 999
	// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=3712%3A51904&mode=dev
	AnalyticsScreenName_MANUAL_ASSET_DASHBOARD_SCREEN AnalyticsScreenName = 1000
	// https://www.figma.com/file/cgVb3yUeGGELbA6xGiUo1c/%F0%9F%87%AE%F0%9F%87%B3-Ind.-Stocks?node-id=1%3A418&mode=dev
	AnalyticsScreenName_INDIAN_STOCKS_DASHBOARD_SCREEN AnalyticsScreenName = 1001
	// https://www.figma.com/file/cgVb3yUeGGELbA6xGiUo1c/%F0%9F%87%AE%F0%9F%87%B3-Ind.-Stocks?node-id=33%3A3017&mode=dev
	AnalyticsScreenName_INDIAN_STOCKS_INSTRUMENT_DETAILS_SCREEN AnalyticsScreenName = 1002
	// https://www.figma.com/file/cgVb3yUeGGELbA6xGiUo1c/%F0%9F%87%AE%F0%9F%87%B3-Ind.-Stocks?node-id=33%3A209&mode=dev
	AnalyticsScreenName_INDIAN_STOCKS_ORDER_RECEIPT_SCREEN AnalyticsScreenName = 1003
	// https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=1780-149208&mode=design&t=Chl75i0hAJT6OTpc-4
	AnalyticsScreenName_ADD_FUND_UPGRADE_UPI_BOTTOM_SHEET AnalyticsScreenName = 1004
	// Onboarding screen names
	AnalyticsScreenName_ADD_FUNDS_ONB_V1_SUCCESS_TRANSITION_SCREEN AnalyticsScreenName = 1005
	AnalyticsScreenName_AFFLUENT_TRANSITION_SCREEN                 AnalyticsScreenName = 1006
	AnalyticsScreenName_FEATURE_BENEFITS_SCREEN                    AnalyticsScreenName = 1007
	AnalyticsScreenName_GENERIC_BOTTOMSHEET_SCREEN                 AnalyticsScreenName = 1008
	AnalyticsScreenName_EPAN_ENTRYPOINT_SCREEN                     AnalyticsScreenName = 1009
	AnalyticsScreenName_GENERIC_TRANSITION_SCREEN                  AnalyticsScreenName = 1010
	AnalyticsScreenName_ACCOUNT_DELETION_SCREEN                    AnalyticsScreenName = 1011
	AnalyticsScreenName_SCREENER_BOTTOMSHEET_SCREEN                AnalyticsScreenName = 1012
	AnalyticsScreenName_GENERIC_RECORD_CONSENT_SCREEN              AnalyticsScreenName = 1013
	AnalyticsScreenName_DEVICE_SECURITY_LOCK_SCREEN                AnalyticsScreenName = 1014
	AnalyticsScreenName_NEXT_ONBOARDING_ACTION_SCREEN              AnalyticsScreenName = 1015
	AnalyticsScreenName_ONBOARDING_PERMISSIONS_SCREEN              AnalyticsScreenName = 1016
	AnalyticsScreenName_CKYC_DROPOFF_BOTTOMSHEET_SCREEN            AnalyticsScreenName = 1017
	AnalyticsScreenName_VKYC_REVIEW_SCREEN                         AnalyticsScreenName = 1018
	AnalyticsScreenName_VKYC_LOADING_SCREEN                        AnalyticsScreenName = 1019
	AnalyticsScreenName_VKYC_WAITING_SCREEN                        AnalyticsScreenName = 1020
	AnalyticsScreenName_VKYC_VERIFYING_DOCS_SCREEN                 AnalyticsScreenName = 1021
	AnalyticsScreenName_VKYC_REJECTED_SCREEN                       AnalyticsScreenName = 1022
	AnalyticsScreenName_VKYC_AGENTS_BUSY_SCREEN                    AnalyticsScreenName = 1023
	AnalyticsScreenName_VKYC_UPLOAD_DOCUMENT_SCREEN                AnalyticsScreenName = 1024
	AnalyticsScreenName_VKYC_INSTRUCTIONS_V2_SCREEN                AnalyticsScreenName = 1025
	AnalyticsScreenName_EKYC_STATUS_SCREEN                         AnalyticsScreenName = 1026
	AnalyticsScreenName_VKYC_RETRY_CALL_SCREEN                     AnalyticsScreenName = 1027
	AnalyticsScreenName_VKYC_INSTRUCTIONS_V1_SCREEN                AnalyticsScreenName = 1028
	AnalyticsScreenName_VKYC_LANDING_V2_SCREEN                     AnalyticsScreenName = 1029
	AnalyticsScreenName_VKYC_PANCARD_TYPE_SELECTION_SCREEN         AnalyticsScreenName = 1030
	AnalyticsScreenName_VKYC_NOTIFY_ME_SCREEN                      AnalyticsScreenName = 1031
	AnalyticsScreenName_CONFIRM_PHONE_NUMBER_BOTTOMSHEET           AnalyticsScreenName = 1032
	AnalyticsScreenName_UPDATE_USER_DETAILS_SCREEN                 AnalyticsScreenName = 1033
	AnalyticsScreenName_PHONE_VERIFICATION_OTP_SCREEN              AnalyticsScreenName = 1034
	AnalyticsScreenName_LIVENESS_SUCCESS_SCREEN                    AnalyticsScreenName = 1035
	AnalyticsScreenName_AUTH_FACTOR_UPDATE_STATUS_SCREEN           AnalyticsScreenName = 1036
	AnalyticsScreenName_APPLE_SIGNIN_MISSING_EMAIL_SCREEN          AnalyticsScreenName = 1037
	AnalyticsScreenName_DEVICE_LOCK_SCREEN                         AnalyticsScreenName = 1038
	AnalyticsScreenName_CIBIL_OTP_VERIFICATION                     AnalyticsScreenName = 1039
	AnalyticsScreenName_ENTER_MOBILE_NUMBER                        AnalyticsScreenName = 1040
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=11903-30010&t=r0Nn2ivQrNSsQfNY-4
	AnalyticsScreenName_US_STOCK_INTRO AnalyticsScreenName = 1041
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21011-26091&mode=design&t=mBl3pG8kg0o2On3o-0
	AnalyticsScreenName_US_STOCK_ONB_DROPDOWN AnalyticsScreenName = 1042
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1196%3A5514
	AnalyticsScreenName_US_STOCK_ACTIVITY_SCREEN AnalyticsScreenName = 1043
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=5331%3A22214&t=8Ddx6YfGyOkgQke0-4
	AnalyticsScreenName_US_STOCK_REMITTANCE_FORM_BOTTOM_SHEET AnalyticsScreenName = 1044
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1341%3A5375&mode=dev
	AnalyticsScreenName_US_STOCK_CANCEL_ORDER_DIALOG AnalyticsScreenName = 1045
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=677%3A3852
	//
	// Deprecated: Marked as deprecated in api/frontend/analytics/analytics_screen_name.proto.
	AnalyticsScreenName_US_STOCK_ORDER_SUCCESS_SCREEN AnalyticsScreenName = 1046
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=343%3A3991&mode=dev
	AnalyticsScreenName_US_STOCK_ONB_PAN_UPLOAD_SUCCESS AnalyticsScreenName = 1047
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=4428%3A19345&t=vMRKrMhk2B734aGn-4
	AnalyticsScreenName_US_STOCK_PAN_RE_UPLOAD AnalyticsScreenName = 1048
	// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&mode=design&t=xw9BYIpOtC0d783p-0
	AnalyticsScreenName_ANALYSER_BENEFITS_SCREEN AnalyticsScreenName = 1049
	// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13185-5709&mode=design&t=lIDQd7OItXucSJDF-0
	AnalyticsScreenName_CREDIT_ANALYSER_DETAILS_BOTTOM_SHEET AnalyticsScreenName = 1050
	// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=1469-5094&mode=design&t=2b9Nj5pRILTBJ6OF-0
	AnalyticsScreenName_CREDIT_ANALYSER_CONSENT_DIALOG AnalyticsScreenName = 1051
	// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=1469-5357&mode=design&t=qZfJs5B71tFu8ykb-0
	AnalyticsScreenName_CREDIT_ANALYSER_OTP_DIALOG AnalyticsScreenName = 1052
	// https://www.figma.com/file/KIOv6btvo1HwPNPP6j7sEb/Spend-Analyzer-%E2%80%A2-FFF?node-id=704%3A19750
	AnalyticsScreenName_ANALYSER_FILTER_DIALOG AnalyticsScreenName = 1053
	// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=6077%3A16568&mode=dev
	AnalyticsScreenName_ANALYSER_SINGLE_SELECT_FILTER AnalyticsScreenName = 1054
	// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=6077%3A16504&mode=dev
	AnalyticsScreenName_ANALYSER_MULTI_SELECT_FILTER AnalyticsScreenName = 1055
	// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=809%3A5754&mode=dev
	AnalyticsScreenName_ANALYSER_DURATION_FILTER AnalyticsScreenName = 1056
	// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=10202%3A24917&mode=dev
	AnalyticsScreenName_MF_ANALYSER_LANDING_SCREEN             AnalyticsScreenName = 1057
	AnalyticsScreenName_SIM_SELECTION_BOTTOM_SHEET             AnalyticsScreenName = 1058
	AnalyticsScreenName_DEVICE_REGISTRATION_BOTTOM_SHEET       AnalyticsScreenName = 1059
	AnalyticsScreenName_DEVICE_REGISTRATION_ERROR_BOTTOM_SHEET AnalyticsScreenName = 1060
	// New deposit screen for secured card onboarding
	// Figma: https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4021-9874&mode=design&t=AtwqdcJuU4BRleWh-0
	AnalyticsScreenName_SECURED_CREDIT_CARD_DEPOSIT_SCREEN_V2 AnalyticsScreenName = 1061
	// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-•-FFF?type=design&node-id=1-5700&mode=design&t=wju1nFRF1IfDAMFd-0
	AnalyticsScreenName_EPF_PASSBOOK_IMPORT_MANUAL_UAN_SCREEN AnalyticsScreenName = 1062
	// Networth introduction screen - https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=1%3A5457&mode=dev
	AnalyticsScreenName_NETWORTH_INTRO_SCREEN AnalyticsScreenName = 1063
	// Networth asset types as a widget list - https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=157%3A19119&mode=dev
	AnalyticsScreenName_NETWORTH_WIDGETS_LIST AnalyticsScreenName = 1064
	// MF Auto Invest screen - https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=10-6159&mode=design&t=3UnaraRspBLbqmb5-0
	AnalyticsScreenName_MF_AUTO_INVEST_SCREEN AnalyticsScreenName = 1065
	// Rule activation success screen - https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=17566-60058&mode=design&t=jFLIqD2Vk02W2kl5-0
	AnalyticsScreenName_FITTT_RULE_ACTIVATION_SUCCESS_SCREEN AnalyticsScreenName = 1066
	// Mutual Funds One Time Investment Screen - https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=17566-58621&mode=design&t=jFLIqD2Vk02W2kl5-0
	AnalyticsScreenName_MF_OTI_SCREEN AnalyticsScreenName = 1067
	// Generic info popUp - https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=2543%3A67272&mode=dev
	AnalyticsScreenName_INFO_POP_UP_V2     AnalyticsScreenName = 1068
	AnalyticsScreenName_GENERIC_OTP_SCREEN AnalyticsScreenName = 1069
	// https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=12532-45540&mode=design&t=qOjAZnzsf53MZvnG-0
	AnalyticsScreenName_DEPOSIT_SUMMARY_SCREEN AnalyticsScreenName = 1070
	// https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=12933-47599&mode=design&t=qOjAZnzsf53MZvnG-0
	AnalyticsScreenName_DEPOSIT_MATURITY_DATE_SELECTION AnalyticsScreenName = 1071
	AnalyticsScreenName_ADD_NOMINEE_SCREEN              AnalyticsScreenName = 1072
	AnalyticsScreenName_ADD_NOMINEE_DETAILS_SCREEN      AnalyticsScreenName = 1073
	// https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=12995-48581&mode=design&t=KA6WtdIe214mlw3m-0
	AnalyticsScreenName_DEPOSIT_DETAILS           AnalyticsScreenName = 1074
	AnalyticsScreenName_DEPOSIT_STATEMENTS_SCREEN AnalyticsScreenName = 1075
	// https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=12918-45155&mode=design&t=w3Oor9eUNxjlvgBX-0
	AnalyticsScreenName_DEPOSIT_MATURITY_AND_PAYMENT_SELECTION AnalyticsScreenName = 1076
	// https://www.figma.com/file/GGiPlpxpvYa2eHdg68m0pg/Help-•-Workfile?type=design&node-id=9504-63890&mode=design&t=ZOK0rtOMbUXIMBFL-0
	AnalyticsScreenName_HELP_CATEGORY_LIST AnalyticsScreenName = 1077
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=677%3A3852
	AnalyticsScreenName_US_STOCK_ORDER_STATUS_SCREEN            AnalyticsScreenName = 1078
	AnalyticsScreenName_AUTH_LIVENESS_SUMMARY_STATUS_POLLSCREEN AnalyticsScreenName = 1079
	AnalyticsScreenName_IMAGE_CAPTURE_SCREEN                    AnalyticsScreenName = 1080
	AnalyticsScreenName_IMAGE_CAPTURED_PREVIEW_SCREEN           AnalyticsScreenName = 1081
	AnalyticsScreenName_USER_DETAILS_FORM_SCREEN                AnalyticsScreenName = 1082
	// https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3314-44623&mode=design&t=3piCJeYsGoFu5YcP-4
	AnalyticsScreenName_TIERING_EARNED_BENEFIT_SCREEN AnalyticsScreenName = 1083
	// Screen to link phone email in LAMF
	// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=5674-30676&mode=design&t=9fvkFSsYXGLMsk2O-0
	// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=5679-31208&mode=design&t=9fvkFSsYXGLMsk2O-0
	AnalyticsScreenName_LAMF_LINK_MF_SCREEN AnalyticsScreenName = 1084
	// Bottom sheet to skip linking mutual funds in LAMF
	// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=5722-42359&mode=design&t=9fvkFSsYXGLMsk2O-0
	AnalyticsScreenName_LAMF_SKIP_LINKING_MF_SCREEN AnalyticsScreenName = 1085
	// Screen for entering account details for penny drop
	// https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4799-51956&mode=design&t=LlfJryFfDdzoIZRZ-4
	AnalyticsScreenName_PENNY_DROP_ACCOUNT_DETAILS_SCREEN AnalyticsScreenName = 1086
	// Screen for polling of  cardStatus
	// https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=5319-25810&mode=design&t=LlfJryFfDdzoIZRZ-4
	AnalyticsScreenName_CARD_STATUS_POLL_SCREEN AnalyticsScreenName = 1087
	// Screen for polling penny drop status
	// https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4799-52288&mode=design&t=LlfJryFfDdzoIZRZ-4
	AnalyticsScreenName_PENNY_DROP_STATUS_POLL_SCREEN AnalyticsScreenName = 1088
	// Screen for searching bank details using IFSC code
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=27124%3A47261&mode=dev
	AnalyticsScreenName_BANK_INFO_SEARCH_SCREEN AnalyticsScreenName = 1089
	// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=16241-46903&mode=design&t=gijE8vQgnUDILbuZ-4
	AnalyticsScreenName_LOANS_CALCULATION_BOTTOMSHEET AnalyticsScreenName = 1090
	// Net Worth Refresh Screens
	// Screen for calling 'GetNextNetWorthRefreshAction' api with a loading screen
	AnalyticsScreenName_NET_WORTH_REFRESH_GET_NEXT_ACTION AnalyticsScreenName = 1091
	// Screen for showing bottom sheet when multiple instruments needs to be refreshed
	AnalyticsScreenName_NET_WORTH_REFRESH_INIT_BOTTOM_SHEET AnalyticsScreenName = 1092
	// Screen for showing form to update all manual assets
	AnalyticsScreenName_NET_WORTH_REFRESH_MANUAL_ASSETS_REFRESH AnalyticsScreenName = 1093
	// Screen for calling 'UpdateManualAssets' api to update all manual assets with a loading screen
	AnalyticsScreenName_NET_WORTH_REFRESH_UPDATE_MANUAL_ASSETS AnalyticsScreenName = 1094
	// Screen for showing success status after all assets are refreshed
	AnalyticsScreenName_NET_WORTH_REFRESH_SUCCESS_SCREEN AnalyticsScreenName = 1095
	// CONSENT SCREEN FOR CREDIT CARD LIMIT
	// Figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=40754-7114&mode=design&t=zI6b8qMpkpwCdeuS-4
	AnalyticsScreenName_CC_LIMIT_CONSENT_SCREEN AnalyticsScreenName = 1096
	// ACCEPT THE CHANGE IN CREDIT CARD LIMIT
	// Figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=41507-40335&mode=design&t=LKg87L0fIznK7RjT-4
	AnalyticsScreenName_CC_ACCEPT_LIMIT_CHANGE_SCREEN AnalyticsScreenName = 1097
	// Celebration Popup
	// Use case : 1. This is being used in tieiring earned benefits page when user opens the page for the first time on every month
	// https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-7332&mode=design&t=C8MVAZ1k7PtG8bnN-4
	AnalyticsScreenName_CELEBRATION_POPUP AnalyticsScreenName = 1098
	// earn benefits history
	// https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-6909&mode=design&t=adMrAaVS1582ag9m-4
	AnalyticsScreenName_TIERING_EARNED_BENEFIT_HISTORY_SCREEN AnalyticsScreenName = 1099
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41416-154329&mode=design&t=zpA35OflGRRe5Hr0-4
	AnalyticsScreenName_LOANS_DOCUMENTS_SCREEN AnalyticsScreenName = 1100
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41234-52155&mode=design&t=fTYpRhjsruhHypBT-4
	AnalyticsScreenName_LOANS_PREPAY_V2_SCREEN AnalyticsScreenName = 1101
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41234-52183&mode=design&t=fTYpRhjsruhHypBT-4
	AnalyticsScreenName_LOANS_REPAYMENT_METHODS_SCREEN AnalyticsScreenName = 1102
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41234-52243&mode=design&t=vBzUvqfnnSiSHxXC-4
	AnalyticsScreenName_LOANS_PAY_VIA_CX_SCREEN AnalyticsScreenName = 1103
	// Screen for authenticating user for updating email of given folios in lamf flow
	AnalyticsScreenName_LAMF_EMAIL_UPDATE_NFT_USER_AUTH_OTP_SCREEN AnalyticsScreenName = 1104
	// Screen for authenticating user for updating phone number of given folios in lamf flow
	AnalyticsScreenName_LAMF_MOBILE_UPDATE_NFT_USER_AUTH_OTP_SCREEN AnalyticsScreenName = 1105
	// Screen for verification of new email in updating email of folios in lamf flow
	AnalyticsScreenName_LAMF_EMAIL_UPDATE_NFT_NEW_EMAIL_VERIFICATION_OTP_SCREEN AnalyticsScreenName = 1106
	// Screen for verification of new mobile in updating mobile of folios in lamf flow
	AnalyticsScreenName_LAMF_MOBILE_UPDATE_NFT_NEW_MOBILE_VERIFICATION_OTP_SCREEN AnalyticsScreenName = 1107
	// Screen for linking user's mutual fund with primary phone and email
	AnalyticsScreenName_LAMF_MF_LINK_SCREEN AnalyticsScreenName = 1108
	// Failure screen when mf link fails to link a few of user's mutual fund.
	AnalyticsScreenName_LAMF_MF_PARTIAL_LINK_FAILURE_SCREEN AnalyticsScreenName = 1109
	// Failure screen when mf link fails to link any of user's mutual fund.
	AnalyticsScreenName_LAMF_MF_LINK_FAILURE AnalyticsScreenName = 1110
	// Reward Bottom Sheet Dialog in Txn Receipt Screen
	// Figma : https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=4240-12243&mode=design&t=czILGoiezN25iTOV-0
	AnalyticsScreenName_TRANSACTION_REWARDS_SUMMARY_DIALOG AnalyticsScreenName = 1111
	// https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=20262-17277&mode=design&t=PJsQK5cCDMKwaS6c-4
	// Deeplink for calling 'GetInvestmentSipsModificationScreen'
	AnalyticsScreenName_GET_INVESTMENT_SIPS_MODIFICATION_SCREEN AnalyticsScreenName = 1112
	AnalyticsScreenName_VKYC_CALL_QUALITY_CHECK                 AnalyticsScreenName = 1113
	AnalyticsScreenName_LOANS_GENERIC_INFO_BOTTOM_SHEET         AnalyticsScreenName = 1114
	// [CX] Contact Us Landing Screen
	// Figma: https://www.figma.com/file/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?type=design&node-id=5816-13118&mode=design&t=jGUBCnhfvhQtXPcF-0
	AnalyticsScreenName_CX_CONTACT_US_LANDING AnalyticsScreenName = 1115
	// [CX] Screen where the user gets provided with options on how he will want to rersolve his issue, that includes a list of issue resolution suggestions and the option to call or chat with a customer executive
	// Figma: https://www.figma.com/file/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?type=design&node-id=5816-14543&mode=design&t=jGUBCnhfvhQtXPcF-0
	AnalyticsScreenName_CX_CONTACT_US_TERMINAL AnalyticsScreenName = 1116
	// [CX] Contact Us Issue Selection Bottom Sheet
	// shows up when pressing view all
	// Figma: https://www.figma.com/file/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?type=design&node-id=5816-14614&mode=design&t=OB5ogG50zaEqTSl1-0
	AnalyticsScreenName_CX_CONTACT_US_ISSUE_SELECTION_BOTTOM_SHEET AnalyticsScreenName = 1117
	// Figma: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=20267-18029&mode=design&t=MpXlnPMqEd5FnHKk-4
	AnalyticsScreenName_UPDATE_STANDING_INSTRUCTION_PIN_SCREEN AnalyticsScreenName = 1118
	// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=44338-40419&mode=design&t=CLBGpXyEbE6thRlm-4
	AnalyticsScreenName_LOANS_MOBILE_NUMBER_INTRO_SCREEN AnalyticsScreenName = 1119
	// figma link: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=14169-14092&mode=design&t=aiZJNa1FTxB04wvS-0
	AnalyticsScreenName_LOANS_GENERIC_INTRO_SCREEN AnalyticsScreenName = 1120
	AnalyticsScreenName_LOANS_STATUS_POLL_SCREEN   AnalyticsScreenName = 1121
	// figma link: https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=18165-510666&mode=design&t=DaQGjHUg2kZaCNqi-0
	AnalyticsScreenName_EPAN_INSTRUCTIONS_SCREEN AnalyticsScreenName = 1122
	// Screen for taking lat long from the user
	// Figma: https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/SimpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=6080-20230&mode=design&t=bjzJMZ3EIMa49ZKV-4
	AnalyticsScreenName_MAP_ADDRESS_POINTER_SCREEN AnalyticsScreenName = 1123
	// Screen for adding new address
	// this screen existed before but was not mapped to any deeplink
	// Figma: https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/SimpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=6082-21119&mode=design&t=bjzJMZ3EIMa49ZKV-4
	AnalyticsScreenName_ADD_NEW_ADDRESS_DETAILS_SCREEN AnalyticsScreenName = 1124
	// Disclaimer before user proceeds with ENACH mandate cancellation
	// figma link: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=23674-48951&mode=design&t=aQRVrWH8CCWb7CsQ-0
	AnalyticsScreenName_ENACH_CANCELLATION_DISCLAIMER_BOTTOM_SHEET AnalyticsScreenName = 1125
	// Screen where user waits for some time before checking if linking of user's mutual fund was successful or not.
	AnalyticsScreenName_LAMF_MF_INTERMEDIATE_LINK_SCREEN  AnalyticsScreenName = 1126
	AnalyticsScreenName_VKYC_INSTRUCTIONS_WEB_VIEW_SCREEN AnalyticsScreenName = 1127
	// Screen to show terminal state(either success/failure) for aa salary program flows like -
	// registration flows : income estimation from account check polls exhausted/ thus failure state | figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51726&mode=design&t=8hb8sMUkCPzdPEJq-0
	// verification flows : money transfer failure state | figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51292&mode=design&t=8hb8sMUkCPzdPEJq-0
	// activation flows : salary activated success terminal state | figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51258&mode=design&t=8hb8sMUkCPzdPEJq-0
	AnalyticsScreenName_AA_SALARY_PROGRAM_FLOWS_TERMINAL_SCREEN AnalyticsScreenName = 1128
	// AA_SALARY_PROGRAM_FLOWS_AMOUNT_TRANSFER_SETUP_SCREEN indicates the screen where user declares the amount of money to be transferred.
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51447&mode=design&t=LCaSFfUaiubQKZSm-0
	AnalyticsScreenName_AA_SALARY_PROGRAM_FLOWS_AMOUNT_TRANSFER_SETUP_SCREEN AnalyticsScreenName = 1129
	// api to load landing page for aa salary flow
	// orchestrator api is called which decides on the screen to land user
	AnalyticsScreenName_AA_SALARY_LANDING_SCREEN AnalyticsScreenName = 1130
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=21121-108057&mode=design&t=48uwAqezJBArRkbb-4
	// screen options: api/types/deeplink_screen_option/salaryprogram/aa_salary_flows_screen_options.proto
	AnalyticsScreenName_AA_SALARY_ADD_FUNDS_VIA_OFF_APP_TRANSFER AnalyticsScreenName = 1131
	// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=29250-53141&mode=design&t=0yKDKXhDtylNwz2r-4
	// https://drive.google.com/file/d/16NBDLjcB_wNhTaZwjLadM2OyZfKcna7h/view?usp=drive_link
	// screen options: api/types/deeplink_screen_option/usstocks/screen_options.proto
	AnalyticsScreenName_USSTOCKS_WALLET_BOTTOM_SHEET AnalyticsScreenName = 1132
	// Screen to display a list/grid of user's opened rewards
	// https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=6478-18494&t=3KQ1F13a1uGPqXKW-0
	AnalyticsScreenName_ALL_OPENED_REWARDS_LIST_SCREEN  AnalyticsScreenName = 1133
	AnalyticsScreenName_LOANS_WEB_VIEW_WITH_POLL_SCREEN AnalyticsScreenName = 1134
	// figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-64651&m=dev
	AnalyticsScreenName_LOANS_CUSTOM_PLAN_SELECTION_BOTTOM_SHEET AnalyticsScreenName = 1135
	// Screen for showing loans offer details
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48296&t=fvrDnDMXVVtjw7ls-4
	AnalyticsScreenName_LOANS_OFFER_DETAILS_SCREEN AnalyticsScreenName = 1136
	// Screen to view all recent activities
	// refer : https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=5727-12199&t=RQHbhg3cscJCSfYo-0
	AnalyticsScreenName_HELP_RECENT_ACTIVITIES AnalyticsScreenName = 1137
	// Screen to view details of a single recent activity
	// refer : https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=5727-12519&t=RQHbhg3cscJCSfYo-0
	AnalyticsScreenName_HELP_RECENT_ACTIVITY_DETAILS AnalyticsScreenName = 1138
	// This screen refers to link mf screen where all folios are linked and portfolio fetch is required from user
	AnalyticsScreenName_LAMF_MF_LINK_SUCCESSFUL_SCREEN AnalyticsScreenName = 1139
	// Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=46393-26295&t=q6zuheZrcZJvN1GB-4
	AnalyticsScreenName_LOANS_ALTERNATE_OFFER_SCREEN AnalyticsScreenName = 1140
	// bottom sheet with options. On submit/continue the corresponding option deeplink is opened or any custom client handled api is called
	// https://www.figma.com/design/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?node-id=8517-10655&t=EdZ92LttpzP4EDW3-4
	AnalyticsScreenName_REQUEST_CHOICE_SCROLLABLE_BOTTOM_SHEET AnalyticsScreenName = 1141
	// https://www.figma.com/design/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?node-id=8517-10700&t=EdZ92LttpzP4EDW3-4
	AnalyticsScreenName_INFO_ACKNOWLEDGEMENT_BOTTOM_SHEET AnalyticsScreenName = 1142
	// Polling screen for liveness status
	AnalyticsScreenName_LIVENESS_POLL_SCREEN AnalyticsScreenName = 1143
	// asset landing page
	// figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7756-1401&t=Wj2bIQWtVkFUKYcg-4
	// drive: https://drive.google.com/file/d/1xVMUwkYN7u17hSh6OMn-4ihR6-E_YWKx/view?usp=drive_link
	AnalyticsScreenName_ASSET_LANDING_PAGE AnalyticsScreenName = 1144
	// https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=30205-28886&t=tNrEZaCh13KMfPZZ-0
	AnalyticsScreenName_US_STOCKS_ACCOUNT_CREATION_INITIATED_SCREEN AnalyticsScreenName = 1145
	// https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=30205-28907&t=tNrEZaCh13KMfPZZ-0
	AnalyticsScreenName_US_STOCKS_ACCOUNT_CREATION_SUCCESS_SCREEN AnalyticsScreenName = 1146
	// screen to check loan eligibility for a user. Client needs to call CheckEligibility rpc on navigating to this screen.
	AnalyticsScreenName_LOANS_CHECK_ELIGIBILITY_SCREEN AnalyticsScreenName = 1147
	// Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=18840-60250&t=0L1qYKhUCZdrPcKW-4
	// DC Dashboard v2
	AnalyticsScreenName_DC_DASHBOARD_V2_SCREEN AnalyticsScreenName = 1148
	// Figma: https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=22372-2275&t=3Ipf2AmvgSrNChos-4
	// AA salary source of fund screen
	AnalyticsScreenName_AA_SALARY_SOURCE_OF_FUND_SCREEN AnalyticsScreenName = 1149
	// Figma: https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=22401-79801&t=3OHqlyoyGx45FdbP-4
	// https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=22453-83901&t=3OHqlyoyGx45FdbP-4
	// AA salary screen to pull salary data
	AnalyticsScreenName_AA_SALARY_DATA_PULL AnalyticsScreenName = 1150
	// Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=18840-58366&t=Fs7r5x36oykQDcVu-4
	// Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=18951-67112&t=Fs7r5x36oykQDcVu-4
	// Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=19486-9232&t=Fs7r5x36oykQDcVu-4
	// Fully SDUI driven bottom sheet
	AnalyticsScreenName_SDUI_BOTTOM_SHEET AnalyticsScreenName = 1151
	// Screen in the onboarding flow to allow users to choose among a variety of soft intents and personalise their app experience
	// https://www.figma.com/design/GNtKeutYAxPhAyBVvFxbMr/D2H-%E2%80%A2-Workfile?node-id=1778-22348&t=1LHThNz1BtxXaVKz-1
	AnalyticsScreenName_ONBOARDING_SOFT_INTENT_SELECTION AnalyticsScreenName = 1152
	// Figma: https://www.figma.com/design/kLX73V4pRucy8JM71ajH50/%F0%9F%9B%A0-US-stocks%2Fworkfile?node-id=7099-12536&t=yu4yZvNAbaubz3BB-4
	AnalyticsScreenName_USS_FUNDS_TRANSFER_PROGRESS_SCREEN AnalyticsScreenName = 1153
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48196-49837&t=AfIAgEeR8jJ3Srx2-4
	// this will be used to fetch user current location in loan application journey
	AnalyticsScreenName_LOAN_ADDRESS_VERIFICATION_INTRO_SCREEN AnalyticsScreenName = 1154
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48196-50114&t=9ZqzZkIVaGc6qZwD-4
	// this will be used to add the address of users, in this values will be auto populated based on user's current location or address selected
	// by the user from search results.
	AnalyticsScreenName_LOAN_ADD_NEW_ADDRESS_DETAILS_SCREEN AnalyticsScreenName = 1155
	// Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48233-51792&t=vsE6GEVpUMInkzdf-4
	AnalyticsScreenName_LOANS_MULTIPLE_OFFER_DETAILS_SCREEN AnalyticsScreenName = 1156
	// Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48233-51791&t=vsE6GEVpUMInkzdf-4
	AnalyticsScreenName_LOANS_MULTIPLE_OFFER_SELECTION_SCREEN AnalyticsScreenName = 1157
	// This screen will start vendor sdk(uqudo) to capture image and perform NFC scan of emirates id for nr onboarding.
	// https://docs.uqudo.com/docs
	AnalyticsScreenName_INITIATE_UQUDO_SDK AnalyticsScreenName = 1158
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48196-49912&t=L7KoARBVyyxuiEd7-4
	AnalyticsScreenName_LOAN_ADDRESS_AUTOCOMPLETE_BOTTOMSHEET AnalyticsScreenName = 1159
	// Secret Analyser Screen where we will show different analyser with charts and line items
	// Figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16174-29092&t=DPWf9k3dz2r3ATsl-1
	AnalyticsScreenName_SECRET_ANALYSER_SCREEN AnalyticsScreenName = 1160
	// Screen name for the in-house Video Kyc call screen:
	// https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=28192-26785&t=9Neg9Sf71mHz815M-4
	AnalyticsScreenName_VKYC_CALL_SCREEN AnalyticsScreenName = 1161
	// https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=305-13142&t=S9XAH2X60XUfyYdb-0
	AnalyticsScreenName_IMAGE_CAPTURE_INFO AnalyticsScreenName = 1162
	// https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=605-9795&t=S9XAH2X60XUfyYdb-0
	AnalyticsScreenName_SUBMIT_DOC AnalyticsScreenName = 1163
	// https://www.figma.com/design/ai7gHuFQud7VOsPN0z8wh5/D2H-%E2%80%A2-FFF?node-id=4020-15440&t=K2db9MekjF2YclS9-4
	AnalyticsScreenName_JOURNEY_LANDING_SCREEN AnalyticsScreenName = 1164
	// https://www.figma.com/design/ai7gHuFQud7VOsPN0z8wh5/D2H-%E2%80%A2-FFF?node-id=4076-18249&t=K2db9MekjF2YclS9-4
	AnalyticsScreenName_JOURNEY_BOTTOMSHEET_SCREEN AnalyticsScreenName = 1165
	// https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-10088&t=tyjHNPf1L614K26I-0
	AnalyticsScreenName_CONTACT_US_LANDING_SCREEN AnalyticsScreenName = 1166
	// https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?t=tyjHNPf1L614K26I-0
	AnalyticsScreenName_CONTACT_US_TERMINAL_SCREEN AnalyticsScreenName = 1167
	// https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=22964-120344&t=zusV9HPSk9xcBnFS-0
	AnalyticsScreenName_SALARY_PROGRAM_HEALTH_INS_ONSURITY_INPUT_FORM AnalyticsScreenName = 1168
	// https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=878-10032&t=jA3RH7SpOUor7O4p-4
	AnalyticsScreenName_COUNTRY_SELECTION_BOTTOM_SHEET AnalyticsScreenName = 1169
	// Figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16171-23989&t=51LZx7Z3lXC14GZP-4
	AnalyticsScreenName_SECRET_ANALYSER_LIBRARY_SCREEN AnalyticsScreenName = 1170
	// Below 3 screens allow user to re-select and submit the loan parameters (amount, tenure etc) during an application journey when the offer is revised
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48296&t=ineZGXSL5c6MuaPK-0
	// screen options: api.typesv2.deeplink_screen_option.preapprovedloans.RevisedLoanOfferDetailsScreenOptions
	AnalyticsScreenName_REVISED_LOAN_OFFER_DETAILS_SCREEN AnalyticsScreenName = 1171
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48596&t=ineZGXSL5c6MuaPK-0
	// screen options: api.typesv2.deeplink_screen_option.preapprovedloans.RevisedLoanOfferCustomPlanSelectionBottomSheet
	AnalyticsScreenName_REVISED_LOAN_OFFER_CUSTOM_PLAN_SELECTION_BOTTOM_SHEET AnalyticsScreenName = 1172
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48955&t=ineZGXSL5c6MuaPK-0
	// screen options: api.typesv2.deeplink_screen_option.preapprovedloans.RevisedLoanOfferApplicationDetailsScreen
	AnalyticsScreenName_REVISED_LOAN_OFFER_APPLICATION_DETAIL_SCREEN AnalyticsScreenName = 1173
	// Figma : https://www.figma.com/design/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?node-id=5199-46670&t=6mKN7q19PSHVnJUk-4
	// api.typesv2.deeplink_screen_option.connectedaccount.BankSelectionScreenOptions
	AnalyticsScreenName_CA_BANK_SELECTION                  AnalyticsScreenName = 1774
	AnalyticsScreenName_LOANS_AA_CONSENT_COLLECTION_SCREEN AnalyticsScreenName = 1775
	// Figma : https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=27821-27806&t=tdmegSLjAwLHV3Zw-4
	// api.typesv2.deeplink_screen_option.consent.ConsentV2ScreenOptions
	AnalyticsScreenName_CONSENT_V2 AnalyticsScreenName = 1776
	// Figma : https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31238-5895&t=2rlztOighhcK6foN-4
	// api.typesv2.deeplink_screen_option.usstocks.screen_options_v2.USStocksCollectProofOfIdentitywww3wwawaawwwwwwwwwwwawaawaaaaaaaaaaaaaaAddressScreenOptions
	AnalyticsScreenName_US_STOCKS_COLLECT_PROOF_OF_IDENTITY_ADDRESS_SCREEN AnalyticsScreenName = 1777
	// Figma : https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31240-6611&t=YGf4IUuCjOEqo688-4
	// api.typesv2.deeplink_screen_option.usstocks.screen_options_v2.USStocksSubmitAdhaarScreenOptions
	AnalyticsScreenName_US_STOCKS_SUBMIT_DIGILOCKER_AADHAAR_SCREEN AnalyticsScreenName = 1778
	// Figma : https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31238-5966&t=YGf4IUuCjOEqo688-4
	// api.typesv2.deeplink_screen_option.usstocks.screen_options_v2.USStocksUploadPANBottomSheetOptions
	AnalyticsScreenName_US_STOCKS_UPLOAD_PAN_BOTTOM_SHEET AnalyticsScreenName = 1779
	// Figma: https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=27666-246065&t=yeyAUagvnOX7NYvB-0
	// api.typesv2.deeplink_screen_option.consent.ConsentV2ScreenOptions
	AnalyticsScreenName_PERMISSIONS_BOTTOM_SHEET AnalyticsScreenName = 1780
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7870-14676&t=9qzkLma7WCLdgcSg-0
	// screen options: api.typesv2.deeplink_screen_option.contactus.ContactUsCategorySelectionScreenOptions
	AnalyticsScreenName_CONTACT_US_CATEGORY_SELECTION_SCREEN AnalyticsScreenName = 1781
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7946-7650&t=FsqgsDlfNkLSN4ch-0
	// screen options: api.typesv2.deeplink_screen_option.contactus.ContactUsCategorySelectionViewMoreScreenOptions
	AnalyticsScreenName_CONTACT_US_CATEGORY_SELECTION_VIEW_MORE_BOTTOM_SHEET AnalyticsScreenName = 1782
	// Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=21099-46723&t=QnDwNQavsfwU7LOI-4
	AnalyticsScreenName_COUNTRY_SELECTION_BOTTOMSHEET AnalyticsScreenName = 1783
	// Figma: https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=17094-20268&t=LKWOZBgpob0yTlNr-0
	AnalyticsScreenName_SHORTCUT_OPTIONS_SCREEN AnalyticsScreenName = 1784
	// Figma: https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=7451-23207&t=RyW3GGVkv8IGHTIc-0
	AnalyticsScreenName_REWARDS_PROCESSING_SCREEN AnalyticsScreenName = 1785
	// Figma: https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=13774-111183&t=V5y55zxOx6Ot6oq4-0?
	AnalyticsScreenName_HOME_PROMPT_SCREEN AnalyticsScreenName = 1786
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=2127-98327&t=qB410igv5ujGQoDY-1
	AnalyticsScreenName_ADD_FUNDS_PROCESSING_SCREEN AnalyticsScreenName = 1787
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=2127-98260&t=XzMdyGTd5njOutBA-1
	AnalyticsScreenName_ADD_FUNDS_SUCCESS_SCREEN AnalyticsScreenName = 1788
	// https://www.figma.com/design/3wbd6UmYwSqEpcER4lPvpf/AutoPay-%E2%80%A2-FFF?node-id=3820-15256&t=yZKTnDpVMrF5xVQh-1
	AnalyticsScreenName_AUTOPAY_ARCHIVED_REQUESTS_SCREEN AnalyticsScreenName = 1789
	// https://www.figma.com/design/pSoQn3AI6dC380ze9dzVHt/FFF-%E2%80%A2-Onboarding?node-id=16465-65326&t=v5nBcrcnwKtTKgnw-1
	AnalyticsScreenName_ACCOUNT_CLOSURE_TRANSFER_INITIATED_SCREEN AnalyticsScreenName = 1790
	// https://www.figma.com/design/mOAgJBEZ0dXhwJBR9HydJu/Account-Balances-%7C-Workfile?node-id=2797-47198&t=HTwkbbSIGUHviNgK-1
	AnalyticsScreenName_BANK_TRANSFER_RECENT_PAYEES_SCREEN AnalyticsScreenName = 1791
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=21952%3A22113&mode=dev
	AnalyticsScreenName_LINK_CONNECTED_ACCOUNTS_VIA_TPAP_SCREEN AnalyticsScreenName = 1792
	// https://www.figma.com/design/3wbd6UmYwSqEpcER4lPvpf/AutoPay-%E2%80%A2-FFF?node-id=3820-23753&t=6GZHVnzdb8GMXYtG-1
	AnalyticsScreenName_CREATE_AUTOPAY_RULE_SCREEN AnalyticsScreenName = 1793
	// https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=22007%3A22096&mode=design&t=gRVVeqGRzWUA69jj-1
	AnalyticsScreenName_PAY_LANDING_POPUP                 AnalyticsScreenName = 1794
	AnalyticsScreenName_PAYMENT_GATEWAY_CHECKOUT_WEB_VIEW AnalyticsScreenName = 1795
	// Figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=9484-44493&t=etEy0nrXd3fU5Rts-4
	AnalyticsScreenName_DC_USAGE_AND_LIMIT_SETTINGS_SCREEN AnalyticsScreenName = 1796
	// Onboarding screens names.....
	AnalyticsScreenName_ONBOARDING_ERROR_SCREEN                         AnalyticsScreenName = 1797
	AnalyticsScreenName_CLOSE_ACCOUNT_REOPENING_PAN_VERIFICATION_SCREEN AnalyticsScreenName = 1798
	AnalyticsScreenName_ENTER_FINITE_CODE_V1                            AnalyticsScreenName = 1799
	AnalyticsScreenName_PERMISSION_DENIED_SCREEN                        AnalyticsScreenName = 1800
	AnalyticsScreenName_SKIP_VKYC_BOTTOM_SHEET                          AnalyticsScreenName = 1801
	AnalyticsScreenName_VKYC_PRE_REQUISITE_SCREEN                       AnalyticsScreenName = 1802
	AnalyticsScreenName_SKIP_NOMINEE_BOTTOM_SHEET                       AnalyticsScreenName = 1803
	AnalyticsScreenName_ADD_GUARDIAN_SCREEN                             AnalyticsScreenName = 1804
	AnalyticsScreenName_INFO_ACKNOWLEDGEMENT_SCREEN                     AnalyticsScreenName = 1805
	AnalyticsScreenName_SHOW_CAMERA_MIC_PERMISSION_DENIED_SCREEN        AnalyticsScreenName = 1806
	AnalyticsScreenName_HELPER_BOTTOM_SHEET                             AnalyticsScreenName = 1807
	AnalyticsScreenName_OFFERS_LANDING_V2                               AnalyticsScreenName = 1808
	// https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=31028-255234&node-type=frame&t=l8pqchLHIyfjpXCs-0
	AnalyticsScreenName_PAN_VERIFICATION AnalyticsScreenName = 1809
	// https://www.figma.com/design/xzcr5E8lsH1ihsh3RmqQTU/Pay-%E2%80%A2-FFF-%E2%80%A2--v1.3?node-id=13537-28514&t=YIexGKJoL4ItJhSP-1
	AnalyticsScreenName_PAY_SEARCH_V2_SCREEN AnalyticsScreenName = 1810
	// https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=10031-18074&t=1PdZC2oPrSdvoU11-4
	AnalyticsScreenName_DC_ONBOARDING_INTRO AnalyticsScreenName = 1811
	// https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9073&node-type=frame&t=3mz9KqdYwv0sjXia-0
	AnalyticsScreenName_CATALOG_OFFER_REDEMPTION_BOTTOM_SHEET AnalyticsScreenName = 1812
	// https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9225&t=RyOPmHuEw3RYqWZy-4
	AnalyticsScreenName_REWARDS_ADDRESS_SELECTION_BOTTOM_SHEET AnalyticsScreenName = 1813
	// https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9239&node-type=frame&t=ocYBnZsxF4kdCkhM-0
	// V2 for OFFERS_DETAILS
	AnalyticsScreenName_OFFER_DETAILS_V2 AnalyticsScreenName = 1814
	// Figma: https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34022-17853&node-type=frame&t=mwzhgZXcWAcKun4o-0
	AnalyticsScreenName_US_STOCKS_TRADE_DETAILS_SCREEN AnalyticsScreenName = 1815
	// Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=63454-34056&t=WXHzMq429kA2DLtx-4
	AnalyticsScreenName_LOANS_CONSENT_SCREEN AnalyticsScreenName = 1816
	// Figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=11330-21495&t=eNzRYvO8zMq10N9E-4
	AnalyticsScreenName_DC_TOGGLE_TRAVEL_MODE_CONFIRMATION_BOTTOM_SHEET AnalyticsScreenName = 1817
	// https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=32155-36937&node-type=frame&t=xVRgzaBGcyrY2noY-0
	AnalyticsScreenName_ONBOARDING_INTRO_V2_SCREEN AnalyticsScreenName = 1818
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=54513-23747&t=FX4qqjMEPyFyF9IU-4
	// This is Loans bottom sheet for single option selection(radio button list)
	AnalyticsScreenName_LOANS_OPTION_SELECTION_BOTTOM_SHEET AnalyticsScreenName = 1819
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=54466-46642&t=cfiBff7SU9PUgvS7-4
	// This is Loans Full screen for single option selection(radio button list)
	AnalyticsScreenName_LOANS_OPTION_SELECTION_FULL_SCREEN AnalyticsScreenName = 1820
	// Figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=11906-21003&t=0bFEokg4ICMG3rUW-4
	AnalyticsScreenName_DC_ORDER_PHYSICAL_CARD_V2_SCREEN AnalyticsScreenName = 1821
	// figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=11949-25101&t=0bFEokg4ICMG3rUW-4
	AnalyticsScreenName_DELIVERY_ADDRESS_SELECTION_BOTTOM_SHEET AnalyticsScreenName = 1822
	// figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=11938-21917&t=0bFEokg4ICMG3rUW-4
	AnalyticsScreenName_DC_BENEFITS_DETAILS_BOTTOM_SHEET AnalyticsScreenName = 1823
	// https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=33532-6586&node-type=frame&t=MOcE2hEkD00unWnW-0
	AnalyticsScreenName_SMS_CONSENT AnalyticsScreenName = 1824
	// figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=10031-19148&t=gd97SMDFHPNq2tmK-4
	AnalyticsScreenName_DC_PHYSICAL_CARD_ORDER_SUCCESS_SCREEN_V2 AnalyticsScreenName = 1825
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=56140-24472&t=xlHnPFgaiXPDzKbb-4
	AnalyticsScreenName_LOANS_OFFER_INTRO_SCREEN AnalyticsScreenName = 1826
	// Figma: - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=56140-24674&t=iKj4ZPnj2ACVlVfs-4
	AnalyticsScreenName_LOANS_SINGLE_VENDOR_MULTI_OFFER_SCREEN AnalyticsScreenName = 1827
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-39739&t=v83a5Lp6TOWOy1bL-1
	AnalyticsScreenName_WEALTH_ANALYSER_REPORT_SCREEN AnalyticsScreenName = 1828
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=15220-20323&t=sl6llgU7Pd4RHsYY-4
	AnalyticsScreenName_PL_INCOME_SELECTION_SCREEN AnalyticsScreenName = 1829
	AnalyticsScreenName_PAN_UPDATE_POLLING         AnalyticsScreenName = 1830
	// https://www.figma.com/design/5TZH6I0awKw8DMtj3aMzrC/Referrals-%E2%80%A2-FFF?node-id=12101-5707&node-type=frame&t=GnvcHDCXBvU0jawC-0
	AnalyticsScreenName_CONTEXTUAL_CARD_BOTTOM_SHEET         AnalyticsScreenName = 1831
	AnalyticsScreenName_ADD_FUNDS_ACK_BOTTOM_SHEET           AnalyticsScreenName = 1832
	AnalyticsScreenName_PAN_DROP_OFF_BOTTOM_SHEET            AnalyticsScreenName = 1833
	AnalyticsScreenName_INFO_BOTTOM_SHEET                    AnalyticsScreenName = 1834
	AnalyticsScreenName_DEFAULT_REFERRAL_CODE_APPLIED_DIALOG AnalyticsScreenName = 1835
	AnalyticsScreenName_EMPLOYER_SEARCH                      AnalyticsScreenName = 1836
	AnalyticsScreenName_CLEAR_EXISTING_DETAILS               AnalyticsScreenName = 1837
	AnalyticsScreenName_DEFAULT_REFERRAL_CODE_DIALOG         AnalyticsScreenName = 1838
	AnalyticsScreenName_KYC_CONSENT_INFO                     AnalyticsScreenName = 1839
	AnalyticsScreenName_PHONE_EMAIL_UPDATE_BOTTOM_SHEET      AnalyticsScreenName = 1840
	AnalyticsScreenName_VIRTUAL_CARD_INFO_DIALOG             AnalyticsScreenName = 1841
	AnalyticsScreenName_COMPANY_SEARCH_BOTTOM_SHEET          AnalyticsScreenName = 1842
	AnalyticsScreenName_SPLASH                               AnalyticsScreenName = 1843
	AnalyticsScreenName_PERMISSION_RATIONALE                 AnalyticsScreenName = 1844
	AnalyticsScreenName_EKYC_EXPIRED_INFO_BOTTOM_SHEET       AnalyticsScreenName = 1845
	AnalyticsScreenName_VKYC_DETAILED_BOTTOM_SHEET           AnalyticsScreenName = 1846
	AnalyticsScreenName_SELECT_ADDRESS                       AnalyticsScreenName = 1847
	AnalyticsScreenName_GENERIC_BOTTOM_SHEET_INFO            AnalyticsScreenName = 1848
	AnalyticsScreenName_EDIT_EMP_BOTTOM_SHEET_V2             AnalyticsScreenName = 1849
	AnalyticsScreenName_EDIT_ABSOLUTE_INCOME_V2              AnalyticsScreenName = 1850
	AnalyticsScreenName_EDIT_EMP_TYPE_V2                     AnalyticsScreenName = 1851
	AnalyticsScreenName_EDIT_INCOME_V2                       AnalyticsScreenName = 1852
	AnalyticsScreenName_EDIT_OCCUPATION_V2                   AnalyticsScreenName = 1853
	AnalyticsScreenName_INFO_BOTTOM_SHEET_V2                 AnalyticsScreenName = 1854
	AnalyticsScreenName_EMPLOYER_SEARCH_v2                   AnalyticsScreenName = 1855
	AnalyticsScreenName_KYC_INFO                             AnalyticsScreenName = 1856
	AnalyticsScreenName_FI_LITE_INTRO_BOTTOM_SHEET           AnalyticsScreenName = 1857
	AnalyticsScreenName_KYC_DETAIL_DIALOG                    AnalyticsScreenName = 1858
	AnalyticsScreenName_MANUAL_KYC                           AnalyticsScreenName = 1859
	AnalyticsScreenName_VKYC_CALL_SLOTS_BOTTOM_SHEET         AnalyticsScreenName = 1860
	AnalyticsScreenName_VKYC_FEEDBACK_BOTTOM_SHEET           AnalyticsScreenName = 1861
	AnalyticsScreenName_VKYC_LIMIT_WARNING                   AnalyticsScreenName = 1862
	AnalyticsScreenName_NOMINEE_ADDRESS_SELECTION            AnalyticsScreenName = 1863
	AnalyticsScreenName_NOMINEE_ADDRESS                      AnalyticsScreenName = 1864
	AnalyticsScreenName_VKYC_ONB_STATUS                      AnalyticsScreenName = 1865
	AnalyticsScreenName_VKYC_NEXT_ACTION_API                 AnalyticsScreenName = 1866
	// figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=14463-22745&t=xBd4KGpMQqgvzsJz-4
	AnalyticsScreenName_ATM_LOCATOR_SCREEN AnalyticsScreenName = 1867
	// This screen can be used for AA data Sharing.
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=55659-32858&t=IGqvyv0YRKv3kis1-4
	AnalyticsScreenName_AA_DATA_SHARE_SCREEN AnalyticsScreenName = 1868
	// Figma:- https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=12584-6517&t=pZ5GzsPEOILv4z4z-4
	AnalyticsScreenName_LAMF_REPAYMENT_METHODS_SCREEN AnalyticsScreenName = 1869
	// Bottom sheet for country selection in DC international flow
	// https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=15011-47348&t=KgKRJvloBXHRHI4B-4
	AnalyticsScreenName_DC_COUNTRY_SELECTION_BOTTOM_SHEET AnalyticsScreenName = 1870
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=1735-50761&t=UADT6C7pHwXIlSEM-4
	AnalyticsScreenName_ASSET_DASHBOARD_SCREEN AnalyticsScreenName = 1871
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61258-10949&t=vbmOBUVASYRI3ZIN-4
	AnalyticsScreenName_VERIFY_INCOME_HOME_SCREEN AnalyticsScreenName = 1872
	// Tier loader screen
	// A lottie animation gets displayed when user tries to add fund or add funds to us stocks wallet or create FD/SD
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10193-125794&m=dev
	AnalyticsScreenName_TIER_LOADER_SCREEN AnalyticsScreenName = 1873
	// Tier all plans screen v2
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10002-54939&t=e3eOtSONnhAQGR5B-4
	AnalyticsScreenName_TIER_ALL_PLANS_SCREEN_V2 AnalyticsScreenName = 1874
	// Tier upgrade success screen v2
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10193-125818&m=dev
	AnalyticsScreenName_TIER_UPGRADE_SUCCESS_SCREEN_V2 AnalyticsScreenName = 1875
	// Loans know more screen - https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=12584-6620&t=tA6ko9TFqnCRRkpg-4
	AnalyticsScreenName_LOANS_KNOW_MORE_BOTTOM_SHEET AnalyticsScreenName = 1876
	// Figma : https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61258-12038&t=0DJRrOF2NKL08GtL-0
	// Figma : https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61258-10853&t=0DJRrOF2NKL08GtL-0
	// Figma : https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61258-10889&t=0DJRrOF2NKL08GtL-0
	AnalyticsScreenName_INCOME_ANALYSIS_STATUS_SCREEN AnalyticsScreenName = 1877
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61577-41955&t=I2PzLlAbmpATOjGU-4
	AnalyticsScreenName_SALARY_ACCOUNT_SELECTION_SCREEN AnalyticsScreenName = 1878
	// Generic screen to be used for any flow completion, success or failure screen
	// e.g. Money secrets next screen after story completion
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=5580-46284&t=ZEWUctFYgsnUYce5-4
	AnalyticsScreenName_GENERIC_FLOW_COMPLETION_SCREEN AnalyticsScreenName = 1879
	// Screen that collect old passport file number or ARN number of the passports that is issued outside india for NR onboarding
	// https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=3183-10195&t=Fa7AeskhVRGFWPRI-4
	AnalyticsScreenName_GLOBAL_ISSUED_PASSPORT_DATA_VERIFICATION AnalyticsScreenName = 1880
	// Tier drop off bottom sheet
	// Bottom sheet which gets displayed when user clicks back from add funds or us stocks or create FD/SD
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10277-72545&t=mXS9zsieZAJj8r2h-4
	AnalyticsScreenName_TIER_DROP_OFF_BOTTOM_SHEET AnalyticsScreenName = 1881
	// VKYC generic bottom sheet driven by SDUI, this is use as vkyc drop off bottom sheet and showing some generic info
	// https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1037-37053&t=lTeXolgSjyKy1n0S-4
	// https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1212-33521&t=lTeXolgSjyKy1n0S-4
	AnalyticsScreenName_VKYC_GENERIC_SDUI_BOTTOMSHEET AnalyticsScreenName = 1882
	// Version 2 of the Vkyc intro screen, driven by SDUI:
	// https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1037-34489&t=EreivkjdPffOwwix-4
	AnalyticsScreenName_VKYC_INTRO_V2_SCREEN AnalyticsScreenName = 1883
	// Vkyc steps info screen, driven by SDUI:
	// https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1037-34617&t=EreivkjdPffOwwix-4
	AnalyticsScreenName_VKYC_STEPS_INFO_SCREEN AnalyticsScreenName = 1884
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10175-83805&t=f2oABQDtEgTdOtwz-4
	// deeplink for tiering landing screen - rpc will be called to get the screen options to construct the screen
	// screen options: TieringLandingScreenOptions
	// rpc called: GetTierFlowScreen
	AnalyticsScreenName_TIERING_LANDING_SCREEN AnalyticsScreenName = 1885
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10175-83805&t=f2oABQDtEgTdOtwz-4
	// deeplink for tiering drop off full screen - screen options will be passed to construct the screen
	// screen options: DropOffFullScreenOptions
	AnalyticsScreenName_TIERING_DROP_OFF_FULL_SCREEN AnalyticsScreenName = 1886
	// Tier detailed benefits bottom sheet
	// Bottom sheet which gets displayed when user clicks on view details of any tier
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10043-11580&t=t76RUnvyrif5rq8v-4
	AnalyticsScreenName_TIER_DETAILED_BENEFITS_BOTTOM_SHEET AnalyticsScreenName = 1887
	// deeplink for wealth builder landing screen
	AnalyticsScreenName_WEALTH_BUILDER_LANDING_SCREEN AnalyticsScreenName = 1888
	// Figma - https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=9255-54117&t=t2K4QJevtLdS8EZ1-4
	// Screen for selecting a bank account Auto Verification
	AnalyticsScreenName_LOANS_AUTO_PAY_AUTH_METHOD_SELECTION_BOTTOM_SHEET AnalyticsScreenName = 1889
	// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=121-9682&t=TMDdzjxiprl4u9IS-4
	AnalyticsScreenName_ASSET_IMPORT_STATUS_POLLING_SCREEN AnalyticsScreenName = 1890
	// Polling screen to get the next action after the physical card dispatch.
	AnalyticsScreenName_GET_PHYSICAL_CARD_DISPATCH_NEXT_ACTION_SCREEN AnalyticsScreenName = 1891
	// Figma - https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=9295-39322&t=cpkfGPIy5hMHmKhH-4
	AnalyticsScreenName_LOAN_APPLICATION_ERROR_STATUS_SCREEN AnalyticsScreenName = 1892
	// PAN prefill and mutual fund import consent screen for the wealth builder 2.0 flow
	// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=328-16776&t=ftAnK2nwPrGYwISh-0
	AnalyticsScreenName_MF_HOLDINGS_IMPORT_PAN_CONSENT_SCREEN AnalyticsScreenName = 1893
	// Success Screen for updating nominee details for savings account
	// Figma: https://www.figma.com/design/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=14542-41244&t=UabtNsT83emYz239-4
	AnalyticsScreenName_SA_NOMINEE_DETAILS_UPDATE_SUCCESS_SCREEN AnalyticsScreenName = 1895
	// Screen to show home walkthrough to user
	// https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=27488-60286&p=f&t=UbesNP58UgvnGejK-0
	AnalyticsScreenName_WALKTHROUGH_SCREEN AnalyticsScreenName = 1896
	// Screen for calling SendSmsData rpc while showing a loading screen
	// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=413-14023&t=QnqXdSa3Zalejzao-4
	AnalyticsScreenName_SEND_SMS_DATA AnalyticsScreenName = 1897
	// Screen to show connect more assets on wealth builder landing page
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7351-45880&t=hw262jvGNUlVPeSO-4
	AnalyticsScreenName_WEALTH_BUILDER_LANDING_CONNECT_MORE_SCREEN AnalyticsScreenName = 1898
	// Screen to show Portfolio tracker's landing page
	// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1566&t=Q9eQjZjJg10nxoIA-4
	AnalyticsScreenName_PORTFOLIO_TRACKER_LANDING_SCREEN AnalyticsScreenName = 1899
	// Screen to show Portfolio tracker's Asset details page
	// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3721-20157&t=Q9eQjZjJg10nxoIA-4
	AnalyticsScreenName_PORTFOLIO_TRACKER_ASSET_DETAILS_SCREEN AnalyticsScreenName = 1900
	// Figma : https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-64946&t=IGmJANLtILqYrUaN-4
	AnalyticsScreenName_LOANS_FORM_ENTRY_SCREEN AnalyticsScreenName = 1901
	// represents credit card dashboard screen v2
	// https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-5716&t=NpQCwd13A6FBIgnw-4
	AnalyticsScreenName_CREDIT_CARD_DASHBOARD_SCREEN_V2 AnalyticsScreenName = 1902
	// In Loans, whenever we need to take the user's confirmation for any action, we can use this bottom sheet
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=54513-23027&t=kg9M6hgq9KNlUf7x-0
	AnalyticsScreenName_LOANS_CONFIRMATION_BOTTOM_SHEET AnalyticsScreenName = 1903
	// Success Screen for reward claim flow
	// Currently will only be used for Fi-Coins flow.
	// https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=8073-19980&t=MY1jWYlSz8xJsL1Z-4
	AnalyticsScreenName_REWARD_CLAIM_SUCCESS_SCREEN AnalyticsScreenName = 1904
	// Screen to render the Credit Cards SDK.
	// https://www.figma.com/design/tbtICUWo4zqseZBUUzPZOl/CC-ONB-flow-for-Saven?node-id=192-35987&t=Q1WqNlTRVHd7taxn-4
	AnalyticsScreenName_CREDIT_CARD_SDK_SCREEN AnalyticsScreenName = 1905
	// Figma - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=69553-38807&t=xKsnIo792toAzLeX-4
	AnalyticsScreenName_LOANS_CONSENT_V2_SCREEN AnalyticsScreenName = 1906
	// Figma - https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay---Workfile?node-id=36676-31931&t=DHrVlfHubQnxWK4A-0
	// Screen to show the Average Monthly Balance details for the user
	AnalyticsScreenName_AMB_DETAILS_SCREEN                           AnalyticsScreenName = 1907
	AnalyticsScreenName_CREDIT_REPORT_POLL_STATUS                    AnalyticsScreenName = 1908
	AnalyticsScreenName_CREDIT_REPORT_CONSENT_V2                     AnalyticsScreenName = 1909
	AnalyticsScreenName_RECORD_AUTH_FLOW_COMPLETION                  AnalyticsScreenName = 1910
	AnalyticsScreenName_INITIATE_CREDIT_REPORT_DOWNLOAD_FOR_ANALYSER AnalyticsScreenName = 1911
	// Figma - https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35748&t=4SzjTwXgOKZ9sTE9-4
	// New Contact Us Landing screen for the user to resolve any issues faced in the app.
	// This is the V2 of CONTACT_US_LANDING_SCREEN.
	// Example: User clicks on "Contact Us" and lands on this screen which shows options like "Recent Issues", "FAQs", etc.
	AnalyticsScreenName_CONTACT_US_LANDING_SCREEN_V2 AnalyticsScreenName = 1912
	// Screen to display a list of open/active incidents or service disruptions.
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35931&t=4SzjTwXgOKZ9sTE9-4
	AnalyticsScreenName_OPEN_INCIDENTS_SCREEN AnalyticsScreenName = 1913
	// Deeplink to navigate to the Search Query Screen
	// Figma: https://www.figma.com/design/HqMQEG4wBLPvhjdq1VEj3j/%E2%98%8E%EF%B8%8F-CX-workfile?node-id=70-187&t=Qrg5D7UsFKIkXMc9-0
	AnalyticsScreenName_CONTACT_US_QUERY_SCREEN AnalyticsScreenName = 1914
	// Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=74513-37832&t=JHfSdzSbmlwPJwbn-0
	AnalyticsScreenName_LOANS_PERMISSION_BOTTOM_SHEET_SCREEN AnalyticsScreenName = 1915
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=74513-37325&t=KW7tl7yDgNqbY4lQ-0
	AnalyticsScreenName_LOANS_APPLICATION_CONFIRMATION_BOTTOM_SHEET AnalyticsScreenName = 1916
	// Figma - https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=2391-259&t=B4JCaNn35YGRKPdq-4
	AnalyticsScreenName_CC_INTRO_V2_SCREEN       AnalyticsScreenName = 1917
	AnalyticsScreenName_GENERIC_PRELAUNCH_SCREEN AnalyticsScreenName = 1918
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=75523-46825&t=nxmxSGLBM1kmeBal-4
	AnalyticsScreenName_LOANS_KEY_VALUE_ROWS_BOTTOM_SHEET AnalyticsScreenName = 1919
	// Screen to show the magic import screen for networth which displays scan and review screens
	// Figma : https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15699&t=Iy3TPAyrAFPn1jPi-4
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15728&t=Iy3TPAyrAFPn1jPi-4
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15732&t=Iy3TPAyrAFPn1jPi-4
	AnalyticsScreenName_NETWORTH_MAGIC_IMPORT_SCREEN AnalyticsScreenName = 1920
	// Figma : https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=3107-12167&t=tpdZmbOYu5cVusPo-0
	// api.typesv2.deeplink_screen_option.consent.SaDeclarationScreenOptions
	AnalyticsScreenName_SA_DECLARATION AnalyticsScreenName = 1921
	// Figma : https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=13604-13207&t=Gyk1mAcTx6b3G9sr-4
	// Screen options: api.typesv2/deeplink_screen_options.assetandanalysis.WealthGenericSduiScreenOptions
	AnalyticsScreenName_WEALTH_SDUI_BOTTOM_SHEET AnalyticsScreenName = 1922
	// Figma - https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=3007-12881&t=y7Jv1lCWlM1R2xOV-0
	AnalyticsScreenName_CONFIRM_CARD_MAILING_ADDRESS_SCREEN_V2 AnalyticsScreenName = 1923
	// Digilocker Intro Screen
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=64895-29596&t=FEPmn6AdR7Urh3qU-1
	AnalyticsScreenName_WEB_DIGILOCKER_INTRO_SCREEN AnalyticsScreenName = 1924
	// SG KYC Multi option screen
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=64895-29655&t=e072nNSo04USyk3D-4
	AnalyticsScreenName_WEB_STOCKGUARDIAN_KYC_CHOICE_SCREEN AnalyticsScreenName = 1925
	// SG KYC Retry screen
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=66085-9215&m=dev
	AnalyticsScreenName_WEB_KYC_RETRY_SCREEN AnalyticsScreenName = 1926
	// used to confirm KYC application using OTP
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=76543-95399&m=dev
	AnalyticsScreenName_WEB_KYC_CONFIRMATION_VIA_OTP_SCREEN AnalyticsScreenName = 1927
	// https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=4131-2696&t=CQ4BU3IWZxj822Ih-4
	AnalyticsScreenName_EARNED_REWARDS_HISTORY_SCREEN AnalyticsScreenName = 1928
	// https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=2262-4544&t=CQ4BU3IWZxj822Ih-4
	AnalyticsScreenName_CC_DETAILS_AND_BENEFITS_SCREEN AnalyticsScreenName = 1929
	// https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-96670&t=XhP8BlFamoV8q77J-4
	AnalyticsScreenName_RECHARGE_INTRO_SCREEN AnalyticsScreenName = 1930
	// https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-97597&t=XhP8BlFamoV8q77J-4
	AnalyticsScreenName_RECHARGE_PLANS_SCREEN AnalyticsScreenName = 1931
	// https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-99237&t=XhP8BlFamoV8q77J-4
	AnalyticsScreenName_BILL_DETAILS_CONFIRMATION_SCREEN AnalyticsScreenName = 1932
	// screen for cancellation option bottom sheet to be used in salary estimation service
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=77530-89679&t=QYfqQ7fleiJx5xaF-4
	AnalyticsScreenName_SALARY_EST_CANCELLATION_BOTTOM_SHEET AnalyticsScreenName = 1933
	// Collect csat for latest resolved ticket (if any)
	AnalyticsScreenName_COLLECT_CSAT_SURVEY AnalyticsScreenName = 1934
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=16048-23163&t=qmTzSq6nX4YOgW1k-0
	AnalyticsScreenName_NETWORTH_MAGIC_IMPORT_IDEAS_SCREEN AnalyticsScreenName = 1944
	// screen to display list of consents, this will be scrollable page
	// https://www.figma.com/design/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=21053-3867&t=sIV9m0wWxgcuqPsq-4
	// https://drive.google.com/file/d/13VeL_lDoKH8BcyB4oNXu9uzoIJjvusOV/view?usp=sharing
	AnalyticsScreenName_WEALTH_GENERIC_RECORD_CONSENT AnalyticsScreenName = 1945
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=77728-356593&p=f&t=BHedV8jy6vEfO0hS-0
	AnalyticsScreenName_LOANS_DASHBOARD_V4_SCREEN AnalyticsScreenName = 1946
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=78102-98537&t=ABpAbjLBZ1hncmaO-4
	AnalyticsScreenName_LOANS_SUMMARY_SCREEN AnalyticsScreenName = 1947
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-FFF--Wealth-Builder?node-id=17842-22659&t=uuMtslO6scQPROQo-0
	AnalyticsScreenName_INTERACTIVE_TALK_TO_AI_SCREEN AnalyticsScreenName = 1948
)

// Enum value maps for AnalyticsScreenName.
var (
	AnalyticsScreenName_name = map[int32]string{
		0:    "ANALYTICS_SCREEN_NAME_UNSPECIFIED",
		1:    "ANALYZER",
		2:    "ASKFI_SEARCH_RESULTS",
		3:    "HOME_LANDING",
		4:    "HOME_FI_ACCOUNT_TAB",
		5:    "HOME_CONNECTED_ACCOUNT_TAB",
		6:    "HOME_SMART_DEPOSIT_TAB",
		7:    "HOME_FIXED_DEPOSIT_TAB",
		8:    "HOME_P2P_JUMP_TAB",
		9:    "HOME_FI_ACCOUNT_DETAILS",
		10:   "PAY_LANDING",
		11:   "HELP_LANDING",
		12:   "FITTT_LANDING",
		13:   "PROFILE_LANDING",
		14:   "PROFILE_SETTINGS",
		15:   "PAY_ENTER_AMOUNT",
		16:   "CATEGORY_ANALYSER",
		17:   "DEBIT_CARD_LANDING",
		18:   "DEBIT_CARD_LIMITS_HOME",
		19:   "DEBIT_CARD_UPDATE_LIMIT",
		20:   "DEBIT_CARD_ACTIVATE_QR_CODE",
		21:   "DEBIT_CARD_REQUEST_CUSTOM_REASONS",
		22:   "DEBIT_CARD_NEW_CARD_REQUEST",
		23:   "DEBIT_CARD_REQUEST_REASONS_LIST",
		24:   "DEBIT_CARD_REQUEST_SUCCESS",
		25:   "DEBIT_CARD_OFFERS_HOME",
		26:   "DEBIT_CARD_OFFER_DETAILS",
		27:   "DEBIT_CARD_SETTINGS",
		28:   "DEBIT_CARD_ATM_PIN_OPTIONS",
		29:   "DEBIT_CARD_RESET_ATM_PIN_OPTIONS",
		30:   "DEPOSITS_TEMPLATES_LIST",
		31:   "DEPOSIT_ADD_MONEY",
		32:   "DEPOSIT_ADD_NAME",
		33:   "DEPOSIT_CLOSURE_NUDGES",
		34:   "DEPOSIT_CLOSURE_CONFIRMATION",
		35:   "DEPOSIT_PAYOUT_MODE_SELECTION",
		36:   "DEPOSIT_SELECT_PAYMENT_METHOD",
		37:   "FITTT_COLLECTION_INFO",
		38:   "FITTT_EXPLORE_COLLECTIONS",
		39:   "FITTT_INTRO_SCREEN",
		40:   "FITTT_ALL_SUBSCRIPTIONS",
		41:   "FITTT_EXPLORE_MY_RULES",
		42:   "FITTT_RULE_SUBSCRIPTION_PREVIEW",
		43:   "FITTT_ALL_RULES",
		44:   "FITTT_SPORTS_INTRO",
		45:   "FITTT_SPORTS_CHALLENGE_LANDING",
		46:   "FITTT_SPORTS_CHALLENGE_REWARD",
		47:   "FITTT_SPORTS_CHALLENGE_MATCH_DETAILS",
		48:   "FITTT_RULE_EXECUTION_HISTORY",
		49:   "FITTT_SUBSCRIPTION_DETAILS",
		50:   "HELP_ARTICLE",
		51:   "HELP_CATEGORY",
		52:   "HELP_STORIES_TOPIC",
		53:   "DEPOSIT_ACCOUNT_DETAILS",
		54:   "NOTIFICATION_CENTER",
		55:   "REWARDS_PROMO_BANNER_SCREEN",
		56:   "ONBOARDING_INTRO_SCREEN",
		57:   "ONBOARDING_ACCOUNT_CLOSURE_TRANSFER_INIT",
		58:   "ONBOARDING_ADD_MONEY",
		59:   "ONBOARDING_CONFIRM_ADDRESS",
		60:   "ONBOARDING_APP_FEATURES_SCREEN",
		61:   "ONBOARDING_ACCOUNT_DUPLICATE",
		62:   "ONBOARDING_INFO_ACK",
		63:   "ONBOARDING_KYC_PARENT_NAME",
		64:   "ONBOARDING_EMAIL_ACCOUNT_VERIFICATION",
		65:   "ONBOARDING_ALL_SET",
		66:   "ONBOARDING_ENTER_FINITE_CODE",
		67:   "ONBOARDING_KYC_ENTER_PAN_DOB",
		68:   "ONBOARDING_TNC_SCREEN",
		69:   "ONBOARDING_WHATSAPP_CONSENT_SCREEN",
		70:   "ONBOARDING_CREATE_ACCOUNT_CONSENT",
		71:   "ONBOARDING_DEBIT_CARD_SET_PIN",
		72:   "ONBOARDING_SCREENER_CREDIT_REPORT_CONSENT",
		73:   "ONBOARDING_SCREENER_EMPLOYMENT_DECLARATION",
		74:   "ONBOARDING_SCREENER_EXPERIAN_TNC",
		75:   "ONBOARDING_SCREENER_MANDATE_CREDIT_REPORT_CONSENT",
		76:   "ONBOARDING_SCREENER_VERIFY_WITH_GMAIL",
		77:   "ONBOARDING_SCREENER_LINKEDIN_VERIFICATION",
		78:   "ONBOARDING_SCREENER_ENTER_WORK_EMAIL",
		79:   "ONBOARDING_SCREENER_WORK_EMAIL_OTP",
		80:   "PAY_ALL_TRANSACTIONS",
		81:   "PAY_SEARCH_TRANSACTIONS",
		82:   "PAY_AUTO_PAY_AMOUNT_SCREEN",
		83:   "PAY_CREATE_RECURRING_PAYMENT",
		84:   "PAY_RECURRING_PAY_DETAILS",
		85:   "PAY_RECURRING_PAY_EXECUTION_INFO",
		86:   "PAY_FILTER_RECURRING_PAYMENTS",
		87:   "PAY_AUTO_PAY_HOME",
		88:   "PAY_AUTO_PAY_INTRO",
		89:   "PAY_BANK_TRANSFER",
		90:   "PAY_DISPUTE_INPUT",
		91:   "PAY_INTENT_SCREEN",
		92:   "PAY_PHONE_PAYMENT",
		93:   "PAY_QR_SCAN",
		94:   "PAY_SEARCH",
		95:   "PAY_UPDATE_NPCI_PIN",
		96:   "PAY_TIMELINE",
		97:   "PAY_TRANSACTION_CATEGORY",
		98:   "PAY_TRANSACTION_CATEGORY_SIMILAR_TXN",
		99:   "PAY_TRANSACTION_RECEIPT",
		100:  "PAY_UPI_PAYMENT",
		101:  "PROFILE_ACCOUNTS_LIST",
		102:  "PROFILE_ACCOUNT_SETTINGS",
		103:  "PROFILE_CONNECTED_ACCOUNT_SETTINGS",
		104:  "PROFILE_PRIVACY_AND_SECURITY",
		105:  "PROFILE_SHOW_QR",
		106:  "PROFILE_SETTINGS_LEGAL",
		107:  "PROFILE_APP_LOGOUT_DIALOG",
		108:  "PROFILE_NOTIFICATION_SETTINGS",
		109:  "PROFILE_EDIT",
		110:  "PROFILE_PERSONAL_DETAILS",
		111:  "REFERRALS_HISTORY",
		112:  "REFERRALS_INVITE_FRIENDS",
		113:  "REFERRALS_QUALIFYING_ACTION_REQUIRED",
		114:  "REFERRALS_TNC",
		115:  "REFERRALS_REWARD_CAP_REACHED",
		116:  "REFERRALS_REWARD_RESET_CAP",
		117:  "REWARDS_CLAIM",
		118:  "REWARDS_DETAIL",
		119:  "REWARDS_WAYS_TO_EARN",
		120:  "REWARDS_INFO",
		121:  "REWARDS_LANDING",
		122:  "OFFERS_COLLECTED",
		123:  "OFFERS_DETAILS",
		124:  "OFFERS_EXCHANGED_OFFER_DETAILS",
		125:  "OFFERS_REDEEMED_GIFT_VOUCHER",
		126:  "OFFERS_COIN_EXCHANGE_CLAIM",
		127:  "OFFERS_COIN_EXCHANGE_SUCCESS",
		128:  "OFFERS_LANDING",
		129:  "OFFERS_DETAIL_TNC",
		130:  "REWARDS_SD",
		131:  "SALARY_INTRO",
		132:  "SALARY_LANDING",
		133:  "SALARY_ALL_BENEFITS",
		134:  "SALARY_ACTIVE_BENEFITS",
		135:  "ASKFI_LANDING",
		136:  "ASKFI_ALL_FINANCIAL_ACTIVITY",
		137:  "ASKFI_SUGGESTIONS",
		138:  "VKYC_INFO",
		139:  "VKYC_LANDING",
		140:  "VKYC_SCHEDULED",
		141:  "VKYC_DEVICE_INCOMPATIBLE",
		142:  "VKYC_STATUS",
		143:  "WEALTH_ADHAAR_ESIGN",
		144:  "WEALTH_COLLECT_PAN",
		145:  "WEALTH_COLLECT_SIGNATURE",
		146:  "WEALTH_CONSENT",
		147:  "WEALTH_DIGILOCKER_LANDING",
		148:  "WEALTH_DIGILOCKER_WEBVIEW",
		149:  "WEALTH_OTI_FUND_SELECTION",
		150:  "WEALTH_ONBOARDING_LANDING",
		151:  "WEALTH_MF_ACTIVITY",
		152:  "WEALTH_MF_LIST",
		153:  "WEALTH_MF_ALL_COLLECTIONS",
		154:  "WEALTH_MF_COLLECTIONS_LANDING",
		155:  "WEALTH_MF_DETAILS",
		156:  "WEALTH_MF_INVESTED_DETAILS",
		157:  "WEALTH_P2P_LANDING",
		158:  "WEALTH_ORDER_DETAILS",
		159:  "WEALTH_ONBOARDING_STATUS",
		160:  "WEALTH_ONBOARDING_MISSING_KYC_DATA",
		161:  "WEALTH_ONBOARDING_MISSING_KYC_INTRO",
		162:  "LIVENESS_CONSENT",
		163:  "LIVENESS_RECORDING",
		164:  "AA_BENEFITS",
		165:  "AA_ACCOUNT_INIT",
		166:  "ONBOARDING_EKYC_CONSENT",
		167:  "ONBOARDING_EKYC_MISMATCH",
		168:  "FITTT_RULE_CUSTOMISATION",
		169:  "DEBIT_CARD_USAGE_SETTING",
		170:  "DEBIT_CARD_TRACKING",
		171:  "HOME_MUTUAL_FUNDS_LANDING_TAB",
		172:  "P2P_LANDING_CONSENT",
		173:  "P2P_ACTIVITY_DETAILS",
		174:  "P2P_ALL_ACTIVITIES",
		175:  "P2P_ELIGIBILITY_CHECK",
		176:  "P2P_KNOW_MORE",
		177:  "ONBOARDING_DEDUPE_ERROR",
		178:  "ONBOARDING_DETAILED_ERROR",
		180:  "ONBOARDING_PHONE_VERIFICATION",
		181:  "ONBOARDING_SAFETY_NET_CONSENT",
		182:  "SCREENER_MANUAL_INTERVENTION",
		183:  "OFFERS_EXCHANGE_COIN_DETAIL",
		184:  "REWARDS_WAYS_TO_EARN_DETAIL",
		185:  "SALARY_TRANSACTION_SELECTOR",
		186:  "VKYC_EKYC_EXPIRED",
		187:  "VKYC_STATUS_POST_ONBOARDING",
		188:  "WEALTH_COLLECT_SINGLE_CHOICE",
		189:  "WEALTH_ONBOARDING_ERROR",
		190:  "P2P_LANDING_TAB",
		191:  "ONBOARDING_INSTRUCTIONS",
		192:  "PAY_QR_SCAN_ML_KIT",
		193:  "DEEPLINK_URL",
		194:  "SYSTEM_TRAY_NOTIFICATION",
		195:  "EDIT_PROFILE_INFO",
		196:  "SALARY_ALL_QUICK_LINKS",
		197:  "INSIGHTS_HUB",
		198:  "SCREENER_CONNECTED_ACCOUNTS_INFO",
		199:  "TIER_INTRODUCTION_SCREEN",
		200:  "TIER_OVERVIEW_SCREEN",
		201:  "TIER_UPGRADE_SUCCESS_SCREEN",
		202:  "HOME_LANDING_V2",
		203:  "HOME_EXPLORE_SCREEN",
		204:  "INVEST_LANDING",
		205:  "HOME_US_STOCKS_TAB",
		206:  "US_STOCKS_LANDING_EXPLORE",
		207:  "US_STOCKS_LANDING_YOUR_STOCKS",
		208:  "US_STOCKS_LANDING_WATCHLIST",
		209:  "US_STOCKS_COLLECTION",
		210:  "US_STOCKS_DETAILS",
		211:  "US_STOCKS_SEARCH",
		381:  "P2P_INVESTMENT_AVAILABLE_PLANS_INFO",
		382:  "P2P_INVESTMENT_CHOOSE_PLAN",
		383:  "P2P_INVESTMENT_UNLOCK_PLAN",
		385:  "P2P_WITHDRAW_MONEY_ENTER_AMOUNT",
		386:  "P2P_WITHDRAW_MONEY_SUMMARY",
		409:  "P2P_INVESTMENT_VIEW_BREAKUP_SCREEN",
		460:  "P2P_INVESTMENT_OTP_SCREEN",
		461:  "P2P_CONFIRM_INVESTMENT_RENEWAL_SCREEN",
		464:  "P2P_INVESTMENT_CURRENT_STATUS_SCREEN",
		465:  "P2P_INVESTMENT_ACTIVITY_DETAILS_SCREEN",
		466:  "P2P_INVESTMENT_ALL_UPCOMING_RENEWALS_SCREEN",
		467:  "P2P_INVESTMENT_RENEWAL_CANCELLATION_NUDGE_SCREEN",
		468:  "US_STOCK_BUY_ENTER_AMOUNT",
		469:  "US_STOCK_BUY_SUMMARY",
		470:  "US_STOCK_SELL_ENTER_AMOUNT",
		471:  "US_STOCK_SELL_SUMMARY",
		472:  "US_STOCK_ORDER_DETAILS",
		473:  "US_STOCK_ONB_STMNTS_PAN_FETCH",
		474:  "US_STOCK_ONB_DECLARATION",
		475:  "US_STOCK_ONB_DISCLAIMER",
		476:  "US_STOCK_ONB_EMPLOYER_DETAILS",
		478:  "US_STOCK_ONB_INVESTMENT_RANGE",
		479:  "US_STOCK_ONB_PAN_UPLOAD_OPTIONS",
		480:  "US_STOCK_ONB_PAN__UPLOAD_CAMERA",
		481:  "US_STOCK_ONB_PAN__UPLOAD_GALLERY",
		482:  "US_STOCK_EXISTING_CONNECTED_ACCOUNT",
		483:  "US_STOCK_NEW_CONNECTED_ACCOUNT",
		484:  "US_STOCK_SOF_POLLING",
		485:  "US_STOCK_ONB_FUTURE_SCOPE",
		486:  "US_STOCK_SOF_FUTURE_SCOPE",
		487:  "US_STOCK_SET_UP_ACCOUNT_DIALOG",
		488:  "US_STOCK_SET_UP_SOF_DIALOG",
		489:  "US_STOCK_PREREQUISITE_DIALOG",
		490:  "INVESTMENT_RISK_PROFILE_QUESTIONNAIRE",
		491:  "INVESTMENT_RISK_PROFILE_DASHBOARD",
		492:  "VKYC_INTRO",
		493:  "VKYC_PAN_TYPE_SELECTION",
		494:  "VKYC_INSTRUCTIONS",
		495:  "US_STOCKS_SEARCH_LANDING",
		496:  "US_STOCK_LANDING_PAGE",
		497:  "US_STOCKS_SEARCH_RESULT",
		498:  "REMINDERS_ENTRY",
		499:  "REMINDERS_LANDING",
		500:  "REMINDERS_SET",
		501:  "DEPOSIT_AUTO_SAVE_SUGGESTIONS",
		502:  "VKYC_ERROR_SCREEN",
		503:  "REMINDER_OVER_SPEND",
		504:  "REMINDER_DATE_CHANGE_PAGE",
		505:  "REMINDER_NO_CC_PAGE",
		506:  "CX_LANGUAGE_SELECTION_SCREEN",
		507:  "CX_LANGUAGE_SELECTION_SUCCESS_SCREEN",
		508:  "CX_LANGUAGE_OPTION_SUGGESTION_SCREEN",
		509:  "STORY_SCREEN",
		510:  "CX_TICKET_DETAIL_SCREEN",
		511:  "CX_TICKET_LIST_SCREEN",
		512:  "CX_TICKET_TAB_SCREEN",
		513:  "CX_CHAT_SCREEN",
		514:  "APP_SHORTCUTS",
		515:  "DISCONNECT_ACCOUNT_CONFIRM_VIEW_CONTROLLER",
		516:  "DISCONNECT_ACCOUNT_BOTTOM_SHEET_VIEW_CONTROLLER",
		517:  "CA_LANDING_PAGE_FI_TO_FI_FLOW_SCREEN",
		518:  "CA_INIT_SDK_FI_TO_FI_FLOW_SCREEN",
		519:  "DEBIT_CARD_CONSOLIDATED_USAGE_SETTING",
		621:  "DEBIT_CARD_PIN_ACTIVATION_SCREEN",
		622:  "DEBIT_CARD_SUCCESSFUL_ACTIVATION_SCREEN",
		623:  "DEBIT_CARD_QR_CODE_SCAN_SCREEN",
		624:  "PAY_PROCESSING",
		625:  "POST_PAYMENT",
		626:  "US_STOCKS_ONB_SUCCESS",
		627:  "US_STOCKS_ONB_POLLING",
		628:  "US_STOCKS_ONB_ADDRESS_ID_PROOF",
		629:  "US_STOCKS_ONB_ERROR",
		630:  "US_STOCKS_PN_ONB_LANDING",
		631:  "P2P_UPDATE_MATURITY_CONSENT_DIALOG",
		632:  "DEPOSIT_ADD_MONEY_FREQUENCY",
		633:  "DEPOSIT_CLOSURE_PROCESSING",
		634:  "DEPOSIT_CREATION_PROCESSING",
		635:  "DEPOSIT_CREATION_AUTO_SAVE_PROCESSING",
		636:  "DEPOSIT_GENERATE_STATEMENT",
		637:  "DEPOSIT_TERM_SELECTION",
		638:  "DEPOSIT_EXPLAINER_DIALOG",
		639:  "DEPOSIT_EXPLAINER_COMPARISON_DIALOG",
		640:  "ONBOARDING_SCREENER_CHOICE",
		641:  "CC_INTRO_SCREEN",
		642:  "CC_ADDRESS_SELECTION_SCREEN",
		643:  "CC_BILL_GENERATION_SCREEN",
		644:  "CC_LIVENESS_AND_FACE_MATCH_SCREEN",
		645:  "CC_VKYC_FLOW_SCREEN",
		646:  "CC_CARD_CREATION_SCREEN",
		647:  "CC_WELCOME_OFFER_SELECTION_SCREEN",
		648:  "CC_VALUE_BACK_SCREEN",
		649:  "CC_ACCELERATED_REWARDS_SCREEN",
		650:  "CC_WELCOME_VOUCHERS_SCREEN",
		651:  "CC_WELCOME_OFFER_SELECTION_SUCCESS_SCREEN",
		652:  "CC_LANDING_SCREEN",
		653:  "CC_CONTROLS",
		654:  "ES_ELIGIBILITY_LANDING",
		655:  "ES_APPLICATION_LANDING",
		656:  "ES_CUSTOM_OFFER_SELECTION_DIALOG",
		657:  "ES_APPLICATION_DETAILS",
		658:  "ES_AUTO_REPAYMENT",
		659:  "ES_ADDRESS_CONFIRMATION",
		660:  "ES_INITIATE_ESIGN",
		661:  "ES_ESIGN_VIEW_DOCUMENT",
		662:  "ES_APPLICATION_STATUS",
		663:  "ES_DASHBOARD",
		664:  "ES_LOAN_DETAILS",
		665:  "ES_APPLICATION_STATUS_POLL",
		666:  "PL_LANDING",
		667:  "PL_OFFER_DETAILS",
		668:  "PL_APPLICATION_DETAILS",
		669:  "PL_NOT_QUALIFIED_USERS",
		670:  "PL_INITIATE_MANDATE",
		671:  "PL_INITIATE_ESIGN",
		672:  "PL_ESIGN_VIEW_DOCUMENT",
		673:  "PL_APPLICATION_CONFIRMATION_VIA_OTP",
		674:  "PL_ADDRESS_CONFIRMATION",
		675:  "PL_EMPLOYMENT_DETAILS",
		676:  "PL_POLLING_SCREEN",
		677:  "PL_APPLICATION_STATUS",
		678:  "PL_LOAN_DASHBOARD",
		679:  "PL_PRE_PAY",
		680:  "PL_ACTIVITY_STATUS",
		681:  "PL_DOWNTIME",
		682:  "PL_ENTER_DETAILS",
		683:  "PL_LOAN_DETAILS",
		684:  "PL_REVIEW_DETAILS",
		685:  "PL_OCCUPATION_DETAILS",
		686:  "PL_UPDATED_RATE",
		687:  "PL_TRANSACTION_RECEIPT",
		688:  "PL_ALL_TRANSACTIONS",
		689:  "CC_USAGE_SCREEN",
		690:  "CC_LIMITS_SCREEN",
		691:  "CC_ALL_TRANSACTIONS_SCREEN",
		692:  "CC_TRANSACTION_RECEIPT_SCREEN",
		693:  "CC_BILL_REPAYMENT_SELECTION_SCREEN",
		694:  "CC_CUSTOM_AMOUNT_BILL_PAY_SCREEN",
		695:  "CC_PAYMENT_STATUS_SCREEN",
		696:  "CC_SET_CARD_PREFERENCES_SCREEN",
		697:  "CC_SET_CARD_PIN",
		698:  "CC_PHYSICAL_CARD_TRACKING_SCREEN",
		699:  "CC_VIEW_STATEMENT_SCREEN",
		700:  "CC_EXPORT_STATEMENT_SCREEN",
		701:  "CC_FREEZE_UNFREEZE_SCREEN",
		702:  "CC_NEW_CARD_REQUEST_ADDRESS_SELECTION_SCREEN",
		703:  "CC_NEW_CARD_REQUEST",
		704:  "CC_EMI_DASH_BOARD_SCREEN",
		705:  "CC_VIEW_ALL_EMI_TRANSACTIONS_SCREEN",
		706:  "CC_EMI_TRANSACTION_LOAN_OFFERS_SCREEN",
		707:  "CC_EMI_PREVIEW_PRE_CLOSE_LOAN_SCREEN",
		708:  "CC_EMI_LOAN_ACCOUNT_DETAILS_SCREEN",
		709:  "CC_EMI_PRE_CLOSE_LOAN_SCREEN",
		710:  "CC_SECURED_OPEN_FD_SCREEN",
		711:  "CC_SECURED_FD_DETAILS_SCREEN",
		712:  "CC_REAL_TIME_ELIGIBILITY_CHECK_INTRO_SCREEN",
		713:  "DEPOSIT_CREATION_SCREEN",
		714:  "CARD_TABS_SCREEN",
		715:  "CC_BILL_GENERATION_DATE_SELECTION_SCREEN",
		716:  "CC_EDIT_LIMITS_SCREEN",
		717:  "CC_SECURED_TENURE_SELECTION_BOTTOM_SHEET_SCREEN",
		718:  "CC_SECURED_FD_INFO_SCREEN",
		719:  "DEPOSIT_DECLARATION",
		720:  "CC_AUTH_OPTIONS_SCREEN",
		721:  "CC_BENEFITS_SCREEN",
		722:  "CC_CUG_SET_PIN_SCREEN",
		723:  "CC_EMI_VIEW_ACTIVE_SCREEN",
		724:  "CC_SUCCESS_INFO_DIALOG_SCREEN",
		725:  "CC_INITIATE_SCREEN",
		726:  "CC_LOUNGE_ACCESS_SCREEN",
		727:  "CC_OTP_SCREEN",
		728:  "CC_QR_ACTIVATION_SCREEN",
		729:  "CC_SELECT_STATEMENT_DURATION_DIALOG_SCREEN",
		730:  "CC_REQUEST_CUSTOM_REASONS_SCREEN",
		731:  "CC_STATEMENT_DETAILS_SCREEN",
		732:  "CC_NEW_REQUEST_SCREEN",
		733:  "CC_REQUEST_REASONS_SCREEN",
		734:  "CC_THREE_TOP_REWARDS_SCREEN",
		735:  "CC_EXTRA_REWARDS_DETAILS_DIALOG_SCREEN",
		736:  "CC_AUTH_POLL_SCREEN",
		737:  "CC_GENERIC_HALT_SCREEN",
		738:  "CC_KNOW_MORE_SCREEN",
		739:  "CC_LIVENESS_SUMMARY_POLL_SCREEN",
		740:  "CC_PERMANENT_USER_FAILURE_SCREEN",
		741:  "CC_REWARDS_POLL_SCREEN",
		742:  "CC_DISPUTE_DETAILS_SCREEN",
		743:  "CC_DISPUTE_DIALOG_SCREEN",
		744:  "CC_DISPUTE_QUESTIONS_SCREEN",
		745:  "CC_DISPUTE_SUMMARY_SCREEN",
		746:  "CC_STATUS_POLL_SCREEN",
		747:  "CC_ENABLE_PAYMENTS_SCREEN",
		748:  "CC_CONFIRM_SELECTION_DIALOG_SCREEN",
		749:  "CC_WAITLISTED_SCREEN",
		750:  "CC_WAITLIST_SCREEN",
		751:  "ONBOARDING_INTENT_SELECTION",
		752:  "NET_WORTH_DEPOSITS_CA_NO_ACC_DISCOVERED_SCREEN",
		753:  "NET_WORTH_DEPOSITS_SCREEN",
		754:  "BKYC_CONSENT_SCREEN",
		755:  "BKYC_HANDSHAKE_SCREEN",
		756:  "BKYC_USER_DETAILS_SCREEN",
		757:  "BKYC_CHOICE_SCREEN",
		758:  "DEFAULT_ERROR_SCREEN",
		759:  "PL_NAME_GENDER_SCREEN",
		760:  "PL_PAN_DOB_SCREEN",
		761:  "PL_BANKING_DETAILS_SCREEN",
		762:  "PL_CREDIT_REPORT_FETCH_CONSENT_SCREEN",
		763:  "PL_LOAN_ELIGIBILITY_LANDING_SCREEN",
		764:  "PL_APPLICATION_REVIEW_DETAILS_SCREEN",
		765:  "PL_CHECK_ELIGIBILITY_LOADING_SCREEN",
		766:  "PL_ELIGIBILITY_OFFER_AVAILABLE_SCREEN",
		767:  "PL_ELIGIBILITY_NO_OFFER_AVAILABLE_SCREEN",
		768:  "PL_NON_ELIGIBLE_USER_LANDING_SCREEN",
		769:  "CC_HORIZONTAL_INTRO",
		770:  "CC_AMPLI_FI_SCREEN",
		771:  "CC_INELIGIBLE_USER_SCREEN",
		772:  "ONBOARDING_SAVINGS_ACCOUNT_INTRO",
		773:  "RESET_ONBOARDING_STAGE_RPC",
		774:  "NETWORTH_DASHBOARD_SCREEN",
		775:  "RPC_BASED_REDIRECTION",
		776:  "PL_ACQ_TO_LEND_LANDING_SCREEN",
		777:  "PL_EDIT_LOAN_BOTTOM_SHEET",
		778:  "LOANS_INFO_SCREEN",
		779:  "REWARD_UNLOCK_DETAILS_BOTTOMSHEET",
		780:  "OFFER_REDEEM_INFO_BOTTOMSHEET",
		781:  "OFFER_VISTARA_CONVERSION_BOTTOMSHEET",
		782:  "OFFER_VISTARA_ENTER_DETAILS_BOTTOMSHEET",
		783:  "OFFER_SELECT_ADDRESS_DIALOG",
		784:  "SALARY_CALCULATOR_SCREEN",
		785:  "ES_ENABLE_PERMISSIONS_SCREEN",
		786:  "SALARY_REFERRAL_LANDING_SCREEN",
		787:  "SALARY_POLICY_INFO_SCREEN",
		788:  "SALARY_ENACH_CANCEL_MADATE_SETUP_SCREEN",
		789:  "SALARY_ENACH_MANDATE_SETUP_SCREEN",
		790:  "SALARY_ENACH_SETUP_STATUS",
		791:  "SALARY_ENACH_INTRO_SCREEN",
		792:  "SALARY_ENACH_FULL_UPGRADE_SCREEN",
		793:  "CANCELLED_CHEQUE_IN_PREVIEW_SCREEN",
		794:  "SALARY_VERIFICATION_STATUS_SCREEN",
		795:  "REWARD_PENDING_DETAILS_SCREEN",
		796:  "REWARD_UNOPENED_LIST_SCREEN",
		797:  "CX_CATEGORY_SEARCH_SCREEN",
		798:  "DC_PHYSICAL_ORDER_SCREEN",
		799:  "CX_CATEGORY_DETAILS_SCREEN",
		800:  "LOANS_DASHBOARD_SCREEN",
		801:  "LOANS_OVERVIEW_SCREEN",
		802:  "LOANS_DETAILS_SCREEN",
		803:  "LOANS_PAYMENT_DETAILS_SCREEN",
		804:  "PL_EMPLOYER_SELECTION_SCREEN",
		805:  "PL_LOCATION_PERMISSION_SCREEN",
		806:  "HELP_LANDING_SCREEN_V2",
		807:  "MF_HOLDINGS_IMPORT_GENERATE_OTP_SCREEN",
		808:  "MF_HOLDINGS_IMPORT_OTP_SUBMISSION_SCREEN",
		809:  "MF_HOLDINGS_IMPORT_OTP_SUCCESS_LOADING_SCREEN",
		810:  "MF_HOLDINGS_IMPORT_PHONE_NUMBER_SUBMISSION_SCREEN",
		811:  "MF_HOLDINGS_IMPORT_EMAIL_SUBMISSION_SCREEN",
		812:  "MF_HOLDINGS_IMPORT_CONSENT_SCREEN_V2",
		813:  "CONNECT_BANK_ACCOUNTS_INTRO_SCREEN",
		814:  "UPLOAD_FILE",
		815:  "US_STOCKS_WALLET_LANDING_SCREEN",
		816:  "US_STOCKS_WALLET_ADD_FUNDS_SCREEN",
		817:  "US_STOCKS_WALLET_WITHDRAW_SCREEN",
		818:  "LAMF_LANDING_SCREEN",
		819:  "LAMF_JOURNEY_SCREEN",
		820:  "LAMF_ADD_ACCOUNT_DETAILS_USER_INPUT_SCREEN",
		821:  "LAMF_CAMS_PORTFOLIO_FETCH_OTP_VERIFICATION_SCREEN",
		822:  "LAMF_KARVY_PORTFOLIO_FETCH_OTP_VERIFICATION_SCREEN",
		823:  "LAMF_VIEW_OFFER_SCREEN",
		824:  "LAMF_OFFER_SELECTION_SCREEN",
		825:  "LAMF_APPLICATION_DETAIL_SCREEN",
		826:  "LAMF_APPLICATION_ADDITIONAL_DETAILS_USER_INPUT_SCREEN",
		827:  "LAMF_KYC_SCREEN",
		828:  "LAMF_ESIGN_SCREEN",
		829:  "LAMF_MANDATE_SCREEN",
		830:  "LAMF_CAMS_LIEN_MARK_OTP_VERIFICATION_SCREEN",
		831:  "LAMF_KARVY_LIEN_MARK_OTP_VERIFICATION_SCREEN",
		832:  "LAMF_LIEN_PROGRESS_UPDATE_SCREEN",
		833:  "LAMF_ESIGN_PROGRESS_UPDATE_SCREEN",
		834:  "LAMF_MANDATE_INTRO_SCREEN",
		835:  "LAMF_APPLICATION_SUCCESS_SCREEN",
		836:  "LAMF_OFFER_FUND_DETAILS_SCREEN",
		837:  "LAMF_INSUFFICIENT_FOLIO_ERROR_SCREEN",
		838:  "LAMF_PARTIAL_LIEN_MARK_ERROR_SCREEN",
		839:  "LAMF_PERMANENT_FAILURE_FULL_SCREEN",
		840:  "LAMF_HOLDING_SCREEN",
		841:  "LAMF_DASHBOARD_SCREEN",
		842:  "LAMF_LOAN_OVERVIEW_SCREEN",
		843:  "LAMF_PAYMENT_DETAILS_SCREEN",
		844:  "LAMF_LOAN_DETAILS_SCREEN",
		845:  "LAMF_INITIATE_SI_SETUP_SCREEN",
		846:  "LAMF_PRE_PAY_SCREEN",
		847:  "LAMF_FUND_ELIGIBILITY_SCREEN",
		848:  "LAMF_NO_FUNDS_FOUND_FAILURE_SCREEN",
		849:  "CC_LOUNGE_ACCESS_V2_SCREEN",
		850:  "CC_COLLECTED_LOUNGE_PASSES_SCREEN",
		851:  "LOANS_SELFIE_SCREEN",
		852:  "LOANS_FORM_DETAILS_SCREEN",
		853:  "PL_OFFER_DETAILS_V2",
		854:  "PL_APPLICATION_DETAILS_V2",
		855:  "LOANS_INCOME_VERIFICATION_INTRO_SCREEN",
		856:  "CA_FI_TO_FI_POLLING_SCREEN",
		857:  "CA_FI_TO_FI_TERMINAL_SCREEN",
		858:  "LOANS_INCOME_VERIFICATION_RESULT_SCREEN",
		859:  "CREDIT_CARD_BILLING_DETAILS_BOTTOM_VIEW_SCREEN",
		860:  "US_STOCKS_ONBOARDING_RISKY_PROILE_TERMINAL_SCREEN",
		861:  "US_STOCKS_ONBOARDING_CREATE_PROFILE_SCREEN",
		862:  "US_STOCKS_ONBOARDING_COLLECT_RISK_LEVEL_SCREEN",
		863:  "US_STOCKS_ONBOARDING_RISK_DISCLOSURE_SCREEN",
		864:  "CC_ALL_ELIGIBLE_CARDS_SCREEN",
		865:  "CC_NETWORK_SELECTION_SCREEN",
		866:  "LOANS_PWA_LANDING_SCREEN",
		867:  "PWA_REDIRECTION_SCREEN",
		868:  "LOANS_BOTTOM_SHEET_SCREEN",
		869:  "APPLY_FOR_LOAN_SCREEN",
		870:  "PL_INITIATE_MANDATE_V2_SCREEN",
		871:  "PL_ALTERNATE_ACCOUNTS_SCREEN",
		872:  "PL_MANDATE_SETUP_SCREEN",
		873:  "SA_CLOSURE_BENEFIT_SCREEN",
		874:  "SA_CLOSURE_FEEDBACK_SCREEN",
		875:  "SA_CLOSURE_CRITERIA_SCREEN",
		876:  "PAN_DOB_INPUT_SCREEN",
		877:  "SA_CLOSURE_SUBMIT_REQUEST_SWIPE_ACTION_SCREEN",
		878:  "FULL_SCREEN_INFO_VIEW_SCREEN",
		879:  "SA_CLOSURE_RESOLVE_ISSUE_SCREEN",
		880:  "SA_CLOSURE_REQUEST_SUBMITTED_SCREEN",
		881:  "MF_HOLDINGS_IMPORT_PROGRESS_SCREEN",
		882:  "LOANS_TIMED_LOADER_SCREEN",
		883:  "ONBOARDING_ADD_FUNDS_TIERING_SUCCESS_SCREEN",
		884:  "LAMF_KFS_SCREEN",
		885:  "LOANS_WEBVIEW_DIGILOCKER",
		886:  "LAMF_FUND_VERIFICATION_FAILURE_SCREEN",
		887:  "LAMF_VERIFY_MF_PROGRESS_UPDATE_SCREEN",
		888:  "LAMF_LOAN_DETAIL_VERIFICATION_SCREEN",
		889:  "DC_PHYSICAL_BENEFITS_SCREEN",
		890:  "DC_CHECK_STATUS_FLOW_SCREEN",
		891:  "DC_INITIATE_FLOW_SCREEN",
		892:  "ORDER_CARD_SUCCESS_SCREEN",
		893:  "NEW_CARD_REQUEST_SUCCESS_SCREEN",
		894:  "DC_RESET_ATM_PIN_OPTIONS_SCREEN",
		895:  "CC_SYNC_POLL_SCREEN",
		896:  "CC_STATION_HALT_SCREEN",
		897:  "LOANS_LANDING_SCREEN_V2",
		898:  "LOANS_AMOUNT_SELECTOR_SCREEN",
		899:  "LOANS_DETAILS_SELECTION_V2",
		900:  "RESET_PIN_CARD_DETAILS_SCREEN",
		901:  "PAY_TERMINAL_STATUS_SCREEN",
		902:  "RECURRING_PAYMENT_ACTION_STATUS_SCREEN",
		903:  "RECURRING_PAYMENT_WEB_VIEW_SCREEN",
		904:  "NPCI_PIN_SET_SCREEN_V2",
		905:  "VPA_MIGRATION_SUCCESS_SCREEN_V1",
		906:  "TPAP_BANK_SELECTION_SCREEN",
		907:  "TPAP_ACCOUNT_SELECTION_SCREEN",
		908:  "TPAP_SELF_TRANSFER_SCREEN",
		909:  "TPAP_LINKING_SUCCESS_SCREEN",
		910:  "VPA_MIGRATION_INTRO_SCREEN",
		911:  "VPA_MIGRATION_SUCCESS_SCREEN_V2",
		912:  "UPI_INTERNATIONAL_ACTIVATION_SCREEN",
		913:  "UPI_INTERNATION_ENTER_AMOUNT_SCREEN",
		914:  "UPI_TIPS_SCREEN",
		915:  "UPI_MAPPER_INTRO_SCREEN",
		916:  "UPI_MAPPER_DETAILS_SCREEN",
		917:  "UPI_LITE_ADD_MONEY_SCREEN",
		918:  "UPI_LITE_INTRO_SCREEN",
		919:  "PAY_ASK_FI_SEARCH_SCREEN",
		920:  "OFFER_FI_COINS_CONVERSION_BOTTOMSHEET",
		921:  "OFFER_FI_COINS_ENTER_DETAILS_BOTTOMSHEET",
		922:  "SALARY_EMPLOYER_DOC_SUBMISSION_BOTTOMSHEET",
		923:  "SALARY_EMPLOYER_SEARCH_BOTTOMSHEET",
		924:  "SALARY_EMPLOYER_CONFIRMATION_BOTTOMSHEET",
		925:  "SALARY_EMPLOYER_VERIFICATION_FAILED_BOTTOMSHEET",
		926:  "SALARY_ENACH_BENEFITS_SCREEN",
		927:  "LAMF_FUND_VERIFICATION_BOTTOM_SHEET",
		928:  "AA_CONSENT_RENEWAL_DIALOG",
		929:  "AA_CONSENT_RENEWAL_SDK_SCREEN",
		930:  "AA_SDK_SCREEN",
		931:  "AA_FI_TO_FI_SDK_SCREEN",
		932:  "AA_POLLING_SCREEN",
		933:  "AA_TERMINAL_SCREEN",
		934:  "AA_REOOBE_SCREEN",
		935:  "AA_TRANSACTION_COMING_SOON",
		936:  "PROFILE_REPORTS_DOWNLOADS",
		937:  "PROFILE_SERVICE_REQUESTS",
		938:  "CHEQUE_BOOK_ORDER_DETAILS",
		939:  "LEGALITY_SIGN_FLOW",
		940:  "ORDER_CHEQUEBOOK",
		941:  "SAVINGS_SIGN_PROMPT",
		942:  "CHEQUE_BOOK_POLLING_SCREEN",
		943:  "ALFRED_REQUEST_SUMMARY",
		944:  "EXPORT_HEALTH_LOG_SCREEN",
		945:  "PROFILE_OPEN_SOURCE_LICENSES",
		946:  "PROFILE_SETTINGS_NOTIFICATION",
		947:  "DIALOG_ENTER_VPA",
		948:  "PAYMENT_METHOD_SELECTION_BOTTOM_SHEET",
		949:  "PROFILE_PERSONAL_DETAILS_EDIT",
		950:  "PROFILE_EDIT_IMAGE_SCREEN",
		951:  "PROFILE_DIALOG_EDIT_OPTION",
		952:  "INFO_ACKNOWLEDGEMENT_V2",
		953:  "CC_CREDIT_REPORT_ADDRESS_SCREEN",
		954:  "CC_CONSENT_BOTTOM_SHEET",
		955:  "LOANS_ESIGN_WEBVIEW",
		956:  "LOANS_MANDATE_DIGIO_SDK",
		957:  "ADD_ADDRESS",
		958:  "EKYC_CONSENT",
		959:  "NAME_DOB_MISMATCH",
		960:  "EKYC_VERIFICATION",
		961:  "LIVENESS",
		962:  "LIVENESS_POLLING",
		963:  "ONB_ADD_FUNDS",
		964:  "ONB_ADD_MONEY",
		965:  "ONB_ADD_FUNDS_TIERING_SUCCESS",
		966:  "FI_BENEFITS",
		967:  "ATM_PIN_VERIFICATION",
		968:  "ACC_DELETION_ACK",
		969:  "LIVENESS_MANUAL_VERIFICATION",
		970:  "LOGIN_VERIFICATION_ERROR",
		971:  "FINITE_CODE_VERIFICATION",
		972:  "INCREASE_BONUS_FOR_AFFLUENT_USER",
		973:  "RE_KYC_SCREEN",
		974:  "PAN_AUTH_SCREEN",
		975:  "AFU_POLL",
		976:  "ACC_CREATION_POLL",
		977:  "ONB_NEXT_ACTION_POLL",
		978:  "CREDIT_REPORT_AVAILABILITY",
		979:  "CREDIT_REPORT_VERIFICATION",
		980:  "EMPLOYMENT_VERIFICATION",
		981:  "SCREENER_ERROR",
		982:  "SCREENER_REJECT_STATE",
		983:  "GMAIL_VERIFICATION_STATUS",
		984:  "LINKEDIN_VERIFICATION",
		985:  "SIGN_OUT_TRANSITION",
		986:  "VKYC_NEXT_ACTION",
		987:  "VKYC_REGISTER_NOTIFICATION",
		988:  "VKYC_HEADLESS",
		989:  "EPAN_INIT",
		990:  "VKYC_INSTRUCTION_OVERLAY",
		991:  "VKYC_SCHEDULED_ACK",
		992:  "LOANS_DEBIT_CARD_DETAILS_SCREEN",
		993:  "DC_CARD_RENEWAL_TYPE_SELECTION_SCREEN",
		994:  "EPF_PASSBOOK_IMPORT_CONFIRM_PHONE_NUMBER_SCREEN",
		995:  "DMF_GENERIC_LOADING_SCREEN",
		996:  "EPF_PASSBOOK_IMPORT_UAN_LIST_SCREEN",
		997:  "EPF_PASSBOOK_IMPORT_OTP_SCREEN",
		998:  "EPF_DASHBOARD_SCREEN",
		999:  "MANUAL_ASSET_FORM_SCREEN",
		1000: "MANUAL_ASSET_DASHBOARD_SCREEN",
		1001: "INDIAN_STOCKS_DASHBOARD_SCREEN",
		1002: "INDIAN_STOCKS_INSTRUMENT_DETAILS_SCREEN",
		1003: "INDIAN_STOCKS_ORDER_RECEIPT_SCREEN",
		1004: "ADD_FUND_UPGRADE_UPI_BOTTOM_SHEET",
		1005: "ADD_FUNDS_ONB_V1_SUCCESS_TRANSITION_SCREEN",
		1006: "AFFLUENT_TRANSITION_SCREEN",
		1007: "FEATURE_BENEFITS_SCREEN",
		1008: "GENERIC_BOTTOMSHEET_SCREEN",
		1009: "EPAN_ENTRYPOINT_SCREEN",
		1010: "GENERIC_TRANSITION_SCREEN",
		1011: "ACCOUNT_DELETION_SCREEN",
		1012: "SCREENER_BOTTOMSHEET_SCREEN",
		1013: "GENERIC_RECORD_CONSENT_SCREEN",
		1014: "DEVICE_SECURITY_LOCK_SCREEN",
		1015: "NEXT_ONBOARDING_ACTION_SCREEN",
		1016: "ONBOARDING_PERMISSIONS_SCREEN",
		1017: "CKYC_DROPOFF_BOTTOMSHEET_SCREEN",
		1018: "VKYC_REVIEW_SCREEN",
		1019: "VKYC_LOADING_SCREEN",
		1020: "VKYC_WAITING_SCREEN",
		1021: "VKYC_VERIFYING_DOCS_SCREEN",
		1022: "VKYC_REJECTED_SCREEN",
		1023: "VKYC_AGENTS_BUSY_SCREEN",
		1024: "VKYC_UPLOAD_DOCUMENT_SCREEN",
		1025: "VKYC_INSTRUCTIONS_V2_SCREEN",
		1026: "EKYC_STATUS_SCREEN",
		1027: "VKYC_RETRY_CALL_SCREEN",
		1028: "VKYC_INSTRUCTIONS_V1_SCREEN",
		1029: "VKYC_LANDING_V2_SCREEN",
		1030: "VKYC_PANCARD_TYPE_SELECTION_SCREEN",
		1031: "VKYC_NOTIFY_ME_SCREEN",
		1032: "CONFIRM_PHONE_NUMBER_BOTTOMSHEET",
		1033: "UPDATE_USER_DETAILS_SCREEN",
		1034: "PHONE_VERIFICATION_OTP_SCREEN",
		1035: "LIVENESS_SUCCESS_SCREEN",
		1036: "AUTH_FACTOR_UPDATE_STATUS_SCREEN",
		1037: "APPLE_SIGNIN_MISSING_EMAIL_SCREEN",
		1038: "DEVICE_LOCK_SCREEN",
		1039: "CIBIL_OTP_VERIFICATION",
		1040: "ENTER_MOBILE_NUMBER",
		1041: "US_STOCK_INTRO",
		1042: "US_STOCK_ONB_DROPDOWN",
		1043: "US_STOCK_ACTIVITY_SCREEN",
		1044: "US_STOCK_REMITTANCE_FORM_BOTTOM_SHEET",
		1045: "US_STOCK_CANCEL_ORDER_DIALOG",
		1046: "US_STOCK_ORDER_SUCCESS_SCREEN",
		1047: "US_STOCK_ONB_PAN_UPLOAD_SUCCESS",
		1048: "US_STOCK_PAN_RE_UPLOAD",
		1049: "ANALYSER_BENEFITS_SCREEN",
		1050: "CREDIT_ANALYSER_DETAILS_BOTTOM_SHEET",
		1051: "CREDIT_ANALYSER_CONSENT_DIALOG",
		1052: "CREDIT_ANALYSER_OTP_DIALOG",
		1053: "ANALYSER_FILTER_DIALOG",
		1054: "ANALYSER_SINGLE_SELECT_FILTER",
		1055: "ANALYSER_MULTI_SELECT_FILTER",
		1056: "ANALYSER_DURATION_FILTER",
		1057: "MF_ANALYSER_LANDING_SCREEN",
		1058: "SIM_SELECTION_BOTTOM_SHEET",
		1059: "DEVICE_REGISTRATION_BOTTOM_SHEET",
		1060: "DEVICE_REGISTRATION_ERROR_BOTTOM_SHEET",
		1061: "SECURED_CREDIT_CARD_DEPOSIT_SCREEN_V2",
		1062: "EPF_PASSBOOK_IMPORT_MANUAL_UAN_SCREEN",
		1063: "NETWORTH_INTRO_SCREEN",
		1064: "NETWORTH_WIDGETS_LIST",
		1065: "MF_AUTO_INVEST_SCREEN",
		1066: "FITTT_RULE_ACTIVATION_SUCCESS_SCREEN",
		1067: "MF_OTI_SCREEN",
		1068: "INFO_POP_UP_V2",
		1069: "GENERIC_OTP_SCREEN",
		1070: "DEPOSIT_SUMMARY_SCREEN",
		1071: "DEPOSIT_MATURITY_DATE_SELECTION",
		1072: "ADD_NOMINEE_SCREEN",
		1073: "ADD_NOMINEE_DETAILS_SCREEN",
		1074: "DEPOSIT_DETAILS",
		1075: "DEPOSIT_STATEMENTS_SCREEN",
		1076: "DEPOSIT_MATURITY_AND_PAYMENT_SELECTION",
		1077: "HELP_CATEGORY_LIST",
		1078: "US_STOCK_ORDER_STATUS_SCREEN",
		1079: "AUTH_LIVENESS_SUMMARY_STATUS_POLLSCREEN",
		1080: "IMAGE_CAPTURE_SCREEN",
		1081: "IMAGE_CAPTURED_PREVIEW_SCREEN",
		1082: "USER_DETAILS_FORM_SCREEN",
		1083: "TIERING_EARNED_BENEFIT_SCREEN",
		1084: "LAMF_LINK_MF_SCREEN",
		1085: "LAMF_SKIP_LINKING_MF_SCREEN",
		1086: "PENNY_DROP_ACCOUNT_DETAILS_SCREEN",
		1087: "CARD_STATUS_POLL_SCREEN",
		1088: "PENNY_DROP_STATUS_POLL_SCREEN",
		1089: "BANK_INFO_SEARCH_SCREEN",
		1090: "LOANS_CALCULATION_BOTTOMSHEET",
		1091: "NET_WORTH_REFRESH_GET_NEXT_ACTION",
		1092: "NET_WORTH_REFRESH_INIT_BOTTOM_SHEET",
		1093: "NET_WORTH_REFRESH_MANUAL_ASSETS_REFRESH",
		1094: "NET_WORTH_REFRESH_UPDATE_MANUAL_ASSETS",
		1095: "NET_WORTH_REFRESH_SUCCESS_SCREEN",
		1096: "CC_LIMIT_CONSENT_SCREEN",
		1097: "CC_ACCEPT_LIMIT_CHANGE_SCREEN",
		1098: "CELEBRATION_POPUP",
		1099: "TIERING_EARNED_BENEFIT_HISTORY_SCREEN",
		1100: "LOANS_DOCUMENTS_SCREEN",
		1101: "LOANS_PREPAY_V2_SCREEN",
		1102: "LOANS_REPAYMENT_METHODS_SCREEN",
		1103: "LOANS_PAY_VIA_CX_SCREEN",
		1104: "LAMF_EMAIL_UPDATE_NFT_USER_AUTH_OTP_SCREEN",
		1105: "LAMF_MOBILE_UPDATE_NFT_USER_AUTH_OTP_SCREEN",
		1106: "LAMF_EMAIL_UPDATE_NFT_NEW_EMAIL_VERIFICATION_OTP_SCREEN",
		1107: "LAMF_MOBILE_UPDATE_NFT_NEW_MOBILE_VERIFICATION_OTP_SCREEN",
		1108: "LAMF_MF_LINK_SCREEN",
		1109: "LAMF_MF_PARTIAL_LINK_FAILURE_SCREEN",
		1110: "LAMF_MF_LINK_FAILURE",
		1111: "TRANSACTION_REWARDS_SUMMARY_DIALOG",
		1112: "GET_INVESTMENT_SIPS_MODIFICATION_SCREEN",
		1113: "VKYC_CALL_QUALITY_CHECK",
		1114: "LOANS_GENERIC_INFO_BOTTOM_SHEET",
		1115: "CX_CONTACT_US_LANDING",
		1116: "CX_CONTACT_US_TERMINAL",
		1117: "CX_CONTACT_US_ISSUE_SELECTION_BOTTOM_SHEET",
		1118: "UPDATE_STANDING_INSTRUCTION_PIN_SCREEN",
		1119: "LOANS_MOBILE_NUMBER_INTRO_SCREEN",
		1120: "LOANS_GENERIC_INTRO_SCREEN",
		1121: "LOANS_STATUS_POLL_SCREEN",
		1122: "EPAN_INSTRUCTIONS_SCREEN",
		1123: "MAP_ADDRESS_POINTER_SCREEN",
		1124: "ADD_NEW_ADDRESS_DETAILS_SCREEN",
		1125: "ENACH_CANCELLATION_DISCLAIMER_BOTTOM_SHEET",
		1126: "LAMF_MF_INTERMEDIATE_LINK_SCREEN",
		1127: "VKYC_INSTRUCTIONS_WEB_VIEW_SCREEN",
		1128: "AA_SALARY_PROGRAM_FLOWS_TERMINAL_SCREEN",
		1129: "AA_SALARY_PROGRAM_FLOWS_AMOUNT_TRANSFER_SETUP_SCREEN",
		1130: "AA_SALARY_LANDING_SCREEN",
		1131: "AA_SALARY_ADD_FUNDS_VIA_OFF_APP_TRANSFER",
		1132: "USSTOCKS_WALLET_BOTTOM_SHEET",
		1133: "ALL_OPENED_REWARDS_LIST_SCREEN",
		1134: "LOANS_WEB_VIEW_WITH_POLL_SCREEN",
		1135: "LOANS_CUSTOM_PLAN_SELECTION_BOTTOM_SHEET",
		1136: "LOANS_OFFER_DETAILS_SCREEN",
		1137: "HELP_RECENT_ACTIVITIES",
		1138: "HELP_RECENT_ACTIVITY_DETAILS",
		1139: "LAMF_MF_LINK_SUCCESSFUL_SCREEN",
		1140: "LOANS_ALTERNATE_OFFER_SCREEN",
		1141: "REQUEST_CHOICE_SCROLLABLE_BOTTOM_SHEET",
		1142: "INFO_ACKNOWLEDGEMENT_BOTTOM_SHEET",
		1143: "LIVENESS_POLL_SCREEN",
		1144: "ASSET_LANDING_PAGE",
		1145: "US_STOCKS_ACCOUNT_CREATION_INITIATED_SCREEN",
		1146: "US_STOCKS_ACCOUNT_CREATION_SUCCESS_SCREEN",
		1147: "LOANS_CHECK_ELIGIBILITY_SCREEN",
		1148: "DC_DASHBOARD_V2_SCREEN",
		1149: "AA_SALARY_SOURCE_OF_FUND_SCREEN",
		1150: "AA_SALARY_DATA_PULL",
		1151: "SDUI_BOTTOM_SHEET",
		1152: "ONBOARDING_SOFT_INTENT_SELECTION",
		1153: "USS_FUNDS_TRANSFER_PROGRESS_SCREEN",
		1154: "LOAN_ADDRESS_VERIFICATION_INTRO_SCREEN",
		1155: "LOAN_ADD_NEW_ADDRESS_DETAILS_SCREEN",
		1156: "LOANS_MULTIPLE_OFFER_DETAILS_SCREEN",
		1157: "LOANS_MULTIPLE_OFFER_SELECTION_SCREEN",
		1158: "INITIATE_UQUDO_SDK",
		1159: "LOAN_ADDRESS_AUTOCOMPLETE_BOTTOMSHEET",
		1160: "SECRET_ANALYSER_SCREEN",
		1161: "VKYC_CALL_SCREEN",
		1162: "IMAGE_CAPTURE_INFO",
		1163: "SUBMIT_DOC",
		1164: "JOURNEY_LANDING_SCREEN",
		1165: "JOURNEY_BOTTOMSHEET_SCREEN",
		1166: "CONTACT_US_LANDING_SCREEN",
		1167: "CONTACT_US_TERMINAL_SCREEN",
		1168: "SALARY_PROGRAM_HEALTH_INS_ONSURITY_INPUT_FORM",
		1169: "COUNTRY_SELECTION_BOTTOM_SHEET",
		1170: "SECRET_ANALYSER_LIBRARY_SCREEN",
		1171: "REVISED_LOAN_OFFER_DETAILS_SCREEN",
		1172: "REVISED_LOAN_OFFER_CUSTOM_PLAN_SELECTION_BOTTOM_SHEET",
		1173: "REVISED_LOAN_OFFER_APPLICATION_DETAIL_SCREEN",
		1774: "CA_BANK_SELECTION",
		1775: "LOANS_AA_CONSENT_COLLECTION_SCREEN",
		1776: "CONSENT_V2",
		1777: "US_STOCKS_COLLECT_PROOF_OF_IDENTITY_ADDRESS_SCREEN",
		1778: "US_STOCKS_SUBMIT_DIGILOCKER_AADHAAR_SCREEN",
		1779: "US_STOCKS_UPLOAD_PAN_BOTTOM_SHEET",
		1780: "PERMISSIONS_BOTTOM_SHEET",
		1781: "CONTACT_US_CATEGORY_SELECTION_SCREEN",
		1782: "CONTACT_US_CATEGORY_SELECTION_VIEW_MORE_BOTTOM_SHEET",
		1783: "COUNTRY_SELECTION_BOTTOMSHEET",
		1784: "SHORTCUT_OPTIONS_SCREEN",
		1785: "REWARDS_PROCESSING_SCREEN",
		1786: "HOME_PROMPT_SCREEN",
		1787: "ADD_FUNDS_PROCESSING_SCREEN",
		1788: "ADD_FUNDS_SUCCESS_SCREEN",
		1789: "AUTOPAY_ARCHIVED_REQUESTS_SCREEN",
		1790: "ACCOUNT_CLOSURE_TRANSFER_INITIATED_SCREEN",
		1791: "BANK_TRANSFER_RECENT_PAYEES_SCREEN",
		1792: "LINK_CONNECTED_ACCOUNTS_VIA_TPAP_SCREEN",
		1793: "CREATE_AUTOPAY_RULE_SCREEN",
		1794: "PAY_LANDING_POPUP",
		1795: "PAYMENT_GATEWAY_CHECKOUT_WEB_VIEW",
		1796: "DC_USAGE_AND_LIMIT_SETTINGS_SCREEN",
		1797: "ONBOARDING_ERROR_SCREEN",
		1798: "CLOSE_ACCOUNT_REOPENING_PAN_VERIFICATION_SCREEN",
		1799: "ENTER_FINITE_CODE_V1",
		1800: "PERMISSION_DENIED_SCREEN",
		1801: "SKIP_VKYC_BOTTOM_SHEET",
		1802: "VKYC_PRE_REQUISITE_SCREEN",
		1803: "SKIP_NOMINEE_BOTTOM_SHEET",
		1804: "ADD_GUARDIAN_SCREEN",
		1805: "INFO_ACKNOWLEDGEMENT_SCREEN",
		1806: "SHOW_CAMERA_MIC_PERMISSION_DENIED_SCREEN",
		1807: "HELPER_BOTTOM_SHEET",
		1808: "OFFERS_LANDING_V2",
		1809: "PAN_VERIFICATION",
		1810: "PAY_SEARCH_V2_SCREEN",
		1811: "DC_ONBOARDING_INTRO",
		1812: "CATALOG_OFFER_REDEMPTION_BOTTOM_SHEET",
		1813: "REWARDS_ADDRESS_SELECTION_BOTTOM_SHEET",
		1814: "OFFER_DETAILS_V2",
		1815: "US_STOCKS_TRADE_DETAILS_SCREEN",
		1816: "LOANS_CONSENT_SCREEN",
		1817: "DC_TOGGLE_TRAVEL_MODE_CONFIRMATION_BOTTOM_SHEET",
		1818: "ONBOARDING_INTRO_V2_SCREEN",
		1819: "LOANS_OPTION_SELECTION_BOTTOM_SHEET",
		1820: "LOANS_OPTION_SELECTION_FULL_SCREEN",
		1821: "DC_ORDER_PHYSICAL_CARD_V2_SCREEN",
		1822: "DELIVERY_ADDRESS_SELECTION_BOTTOM_SHEET",
		1823: "DC_BENEFITS_DETAILS_BOTTOM_SHEET",
		1824: "SMS_CONSENT",
		1825: "DC_PHYSICAL_CARD_ORDER_SUCCESS_SCREEN_V2",
		1826: "LOANS_OFFER_INTRO_SCREEN",
		1827: "LOANS_SINGLE_VENDOR_MULTI_OFFER_SCREEN",
		1828: "WEALTH_ANALYSER_REPORT_SCREEN",
		1829: "PL_INCOME_SELECTION_SCREEN",
		1830: "PAN_UPDATE_POLLING",
		1831: "CONTEXTUAL_CARD_BOTTOM_SHEET",
		1832: "ADD_FUNDS_ACK_BOTTOM_SHEET",
		1833: "PAN_DROP_OFF_BOTTOM_SHEET",
		1834: "INFO_BOTTOM_SHEET",
		1835: "DEFAULT_REFERRAL_CODE_APPLIED_DIALOG",
		1836: "EMPLOYER_SEARCH",
		1837: "CLEAR_EXISTING_DETAILS",
		1838: "DEFAULT_REFERRAL_CODE_DIALOG",
		1839: "KYC_CONSENT_INFO",
		1840: "PHONE_EMAIL_UPDATE_BOTTOM_SHEET",
		1841: "VIRTUAL_CARD_INFO_DIALOG",
		1842: "COMPANY_SEARCH_BOTTOM_SHEET",
		1843: "SPLASH",
		1844: "PERMISSION_RATIONALE",
		1845: "EKYC_EXPIRED_INFO_BOTTOM_SHEET",
		1846: "VKYC_DETAILED_BOTTOM_SHEET",
		1847: "SELECT_ADDRESS",
		1848: "GENERIC_BOTTOM_SHEET_INFO",
		1849: "EDIT_EMP_BOTTOM_SHEET_V2",
		1850: "EDIT_ABSOLUTE_INCOME_V2",
		1851: "EDIT_EMP_TYPE_V2",
		1852: "EDIT_INCOME_V2",
		1853: "EDIT_OCCUPATION_V2",
		1854: "INFO_BOTTOM_SHEET_V2",
		1855: "EMPLOYER_SEARCH_v2",
		1856: "KYC_INFO",
		1857: "FI_LITE_INTRO_BOTTOM_SHEET",
		1858: "KYC_DETAIL_DIALOG",
		1859: "MANUAL_KYC",
		1860: "VKYC_CALL_SLOTS_BOTTOM_SHEET",
		1861: "VKYC_FEEDBACK_BOTTOM_SHEET",
		1862: "VKYC_LIMIT_WARNING",
		1863: "NOMINEE_ADDRESS_SELECTION",
		1864: "NOMINEE_ADDRESS",
		1865: "VKYC_ONB_STATUS",
		1866: "VKYC_NEXT_ACTION_API",
		1867: "ATM_LOCATOR_SCREEN",
		1868: "AA_DATA_SHARE_SCREEN",
		1869: "LAMF_REPAYMENT_METHODS_SCREEN",
		1870: "DC_COUNTRY_SELECTION_BOTTOM_SHEET",
		1871: "ASSET_DASHBOARD_SCREEN",
		1872: "VERIFY_INCOME_HOME_SCREEN",
		1873: "TIER_LOADER_SCREEN",
		1874: "TIER_ALL_PLANS_SCREEN_V2",
		1875: "TIER_UPGRADE_SUCCESS_SCREEN_V2",
		1876: "LOANS_KNOW_MORE_BOTTOM_SHEET",
		1877: "INCOME_ANALYSIS_STATUS_SCREEN",
		1878: "SALARY_ACCOUNT_SELECTION_SCREEN",
		1879: "GENERIC_FLOW_COMPLETION_SCREEN",
		1880: "GLOBAL_ISSUED_PASSPORT_DATA_VERIFICATION",
		1881: "TIER_DROP_OFF_BOTTOM_SHEET",
		1882: "VKYC_GENERIC_SDUI_BOTTOMSHEET",
		1883: "VKYC_INTRO_V2_SCREEN",
		1884: "VKYC_STEPS_INFO_SCREEN",
		1885: "TIERING_LANDING_SCREEN",
		1886: "TIERING_DROP_OFF_FULL_SCREEN",
		1887: "TIER_DETAILED_BENEFITS_BOTTOM_SHEET",
		1888: "WEALTH_BUILDER_LANDING_SCREEN",
		1889: "LOANS_AUTO_PAY_AUTH_METHOD_SELECTION_BOTTOM_SHEET",
		1890: "ASSET_IMPORT_STATUS_POLLING_SCREEN",
		1891: "GET_PHYSICAL_CARD_DISPATCH_NEXT_ACTION_SCREEN",
		1892: "LOAN_APPLICATION_ERROR_STATUS_SCREEN",
		1893: "MF_HOLDINGS_IMPORT_PAN_CONSENT_SCREEN",
		1895: "SA_NOMINEE_DETAILS_UPDATE_SUCCESS_SCREEN",
		1896: "WALKTHROUGH_SCREEN",
		1897: "SEND_SMS_DATA",
		1898: "WEALTH_BUILDER_LANDING_CONNECT_MORE_SCREEN",
		1899: "PORTFOLIO_TRACKER_LANDING_SCREEN",
		1900: "PORTFOLIO_TRACKER_ASSET_DETAILS_SCREEN",
		1901: "LOANS_FORM_ENTRY_SCREEN",
		1902: "CREDIT_CARD_DASHBOARD_SCREEN_V2",
		1903: "LOANS_CONFIRMATION_BOTTOM_SHEET",
		1904: "REWARD_CLAIM_SUCCESS_SCREEN",
		1905: "CREDIT_CARD_SDK_SCREEN",
		1906: "LOANS_CONSENT_V2_SCREEN",
		1907: "AMB_DETAILS_SCREEN",
		1908: "CREDIT_REPORT_POLL_STATUS",
		1909: "CREDIT_REPORT_CONSENT_V2",
		1910: "RECORD_AUTH_FLOW_COMPLETION",
		1911: "INITIATE_CREDIT_REPORT_DOWNLOAD_FOR_ANALYSER",
		1912: "CONTACT_US_LANDING_SCREEN_V2",
		1913: "OPEN_INCIDENTS_SCREEN",
		1914: "CONTACT_US_QUERY_SCREEN",
		1915: "LOANS_PERMISSION_BOTTOM_SHEET_SCREEN",
		1916: "LOANS_APPLICATION_CONFIRMATION_BOTTOM_SHEET",
		1917: "CC_INTRO_V2_SCREEN",
		1918: "GENERIC_PRELAUNCH_SCREEN",
		1919: "LOANS_KEY_VALUE_ROWS_BOTTOM_SHEET",
		1920: "NETWORTH_MAGIC_IMPORT_SCREEN",
		1921: "SA_DECLARATION",
		1922: "WEALTH_SDUI_BOTTOM_SHEET",
		1923: "CONFIRM_CARD_MAILING_ADDRESS_SCREEN_V2",
		1924: "WEB_DIGILOCKER_INTRO_SCREEN",
		1925: "WEB_STOCKGUARDIAN_KYC_CHOICE_SCREEN",
		1926: "WEB_KYC_RETRY_SCREEN",
		1927: "WEB_KYC_CONFIRMATION_VIA_OTP_SCREEN",
		1928: "EARNED_REWARDS_HISTORY_SCREEN",
		1929: "CC_DETAILS_AND_BENEFITS_SCREEN",
		1930: "RECHARGE_INTRO_SCREEN",
		1931: "RECHARGE_PLANS_SCREEN",
		1932: "BILL_DETAILS_CONFIRMATION_SCREEN",
		1933: "SALARY_EST_CANCELLATION_BOTTOM_SHEET",
		1934: "COLLECT_CSAT_SURVEY",
		1944: "NETWORTH_MAGIC_IMPORT_IDEAS_SCREEN",
		1945: "WEALTH_GENERIC_RECORD_CONSENT",
		1946: "LOANS_DASHBOARD_V4_SCREEN",
		1947: "LOANS_SUMMARY_SCREEN",
		1948: "INTERACTIVE_TALK_TO_AI_SCREEN",
	}
	AnalyticsScreenName_value = map[string]int32{
		"ANALYTICS_SCREEN_NAME_UNSPECIFIED":                         0,
		"ANALYZER":                                                  1,
		"ASKFI_SEARCH_RESULTS":                                      2,
		"HOME_LANDING":                                              3,
		"HOME_FI_ACCOUNT_TAB":                                       4,
		"HOME_CONNECTED_ACCOUNT_TAB":                                5,
		"HOME_SMART_DEPOSIT_TAB":                                    6,
		"HOME_FIXED_DEPOSIT_TAB":                                    7,
		"HOME_P2P_JUMP_TAB":                                         8,
		"HOME_FI_ACCOUNT_DETAILS":                                   9,
		"PAY_LANDING":                                               10,
		"HELP_LANDING":                                              11,
		"FITTT_LANDING":                                             12,
		"PROFILE_LANDING":                                           13,
		"PROFILE_SETTINGS":                                          14,
		"PAY_ENTER_AMOUNT":                                          15,
		"CATEGORY_ANALYSER":                                         16,
		"DEBIT_CARD_LANDING":                                        17,
		"DEBIT_CARD_LIMITS_HOME":                                    18,
		"DEBIT_CARD_UPDATE_LIMIT":                                   19,
		"DEBIT_CARD_ACTIVATE_QR_CODE":                               20,
		"DEBIT_CARD_REQUEST_CUSTOM_REASONS":                         21,
		"DEBIT_CARD_NEW_CARD_REQUEST":                               22,
		"DEBIT_CARD_REQUEST_REASONS_LIST":                           23,
		"DEBIT_CARD_REQUEST_SUCCESS":                                24,
		"DEBIT_CARD_OFFERS_HOME":                                    25,
		"DEBIT_CARD_OFFER_DETAILS":                                  26,
		"DEBIT_CARD_SETTINGS":                                       27,
		"DEBIT_CARD_ATM_PIN_OPTIONS":                                28,
		"DEBIT_CARD_RESET_ATM_PIN_OPTIONS":                          29,
		"DEPOSITS_TEMPLATES_LIST":                                   30,
		"DEPOSIT_ADD_MONEY":                                         31,
		"DEPOSIT_ADD_NAME":                                          32,
		"DEPOSIT_CLOSURE_NUDGES":                                    33,
		"DEPOSIT_CLOSURE_CONFIRMATION":                              34,
		"DEPOSIT_PAYOUT_MODE_SELECTION":                             35,
		"DEPOSIT_SELECT_PAYMENT_METHOD":                             36,
		"FITTT_COLLECTION_INFO":                                     37,
		"FITTT_EXPLORE_COLLECTIONS":                                 38,
		"FITTT_INTRO_SCREEN":                                        39,
		"FITTT_ALL_SUBSCRIPTIONS":                                   40,
		"FITTT_EXPLORE_MY_RULES":                                    41,
		"FITTT_RULE_SUBSCRIPTION_PREVIEW":                           42,
		"FITTT_ALL_RULES":                                           43,
		"FITTT_SPORTS_INTRO":                                        44,
		"FITTT_SPORTS_CHALLENGE_LANDING":                            45,
		"FITTT_SPORTS_CHALLENGE_REWARD":                             46,
		"FITTT_SPORTS_CHALLENGE_MATCH_DETAILS":                      47,
		"FITTT_RULE_EXECUTION_HISTORY":                              48,
		"FITTT_SUBSCRIPTION_DETAILS":                                49,
		"HELP_ARTICLE":                                              50,
		"HELP_CATEGORY":                                             51,
		"HELP_STORIES_TOPIC":                                        52,
		"DEPOSIT_ACCOUNT_DETAILS":                                   53,
		"NOTIFICATION_CENTER":                                       54,
		"REWARDS_PROMO_BANNER_SCREEN":                               55,
		"ONBOARDING_INTRO_SCREEN":                                   56,
		"ONBOARDING_ACCOUNT_CLOSURE_TRANSFER_INIT":                  57,
		"ONBOARDING_ADD_MONEY":                                      58,
		"ONBOARDING_CONFIRM_ADDRESS":                                59,
		"ONBOARDING_APP_FEATURES_SCREEN":                            60,
		"ONBOARDING_ACCOUNT_DUPLICATE":                              61,
		"ONBOARDING_INFO_ACK":                                       62,
		"ONBOARDING_KYC_PARENT_NAME":                                63,
		"ONBOARDING_EMAIL_ACCOUNT_VERIFICATION":                     64,
		"ONBOARDING_ALL_SET":                                        65,
		"ONBOARDING_ENTER_FINITE_CODE":                              66,
		"ONBOARDING_KYC_ENTER_PAN_DOB":                              67,
		"ONBOARDING_TNC_SCREEN":                                     68,
		"ONBOARDING_WHATSAPP_CONSENT_SCREEN":                        69,
		"ONBOARDING_CREATE_ACCOUNT_CONSENT":                         70,
		"ONBOARDING_DEBIT_CARD_SET_PIN":                             71,
		"ONBOARDING_SCREENER_CREDIT_REPORT_CONSENT":                 72,
		"ONBOARDING_SCREENER_EMPLOYMENT_DECLARATION":                73,
		"ONBOARDING_SCREENER_EXPERIAN_TNC":                          74,
		"ONBOARDING_SCREENER_MANDATE_CREDIT_REPORT_CONSENT":         75,
		"ONBOARDING_SCREENER_VERIFY_WITH_GMAIL":                     76,
		"ONBOARDING_SCREENER_LINKEDIN_VERIFICATION":                 77,
		"ONBOARDING_SCREENER_ENTER_WORK_EMAIL":                      78,
		"ONBOARDING_SCREENER_WORK_EMAIL_OTP":                        79,
		"PAY_ALL_TRANSACTIONS":                                      80,
		"PAY_SEARCH_TRANSACTIONS":                                   81,
		"PAY_AUTO_PAY_AMOUNT_SCREEN":                                82,
		"PAY_CREATE_RECURRING_PAYMENT":                              83,
		"PAY_RECURRING_PAY_DETAILS":                                 84,
		"PAY_RECURRING_PAY_EXECUTION_INFO":                          85,
		"PAY_FILTER_RECURRING_PAYMENTS":                             86,
		"PAY_AUTO_PAY_HOME":                                         87,
		"PAY_AUTO_PAY_INTRO":                                        88,
		"PAY_BANK_TRANSFER":                                         89,
		"PAY_DISPUTE_INPUT":                                         90,
		"PAY_INTENT_SCREEN":                                         91,
		"PAY_PHONE_PAYMENT":                                         92,
		"PAY_QR_SCAN":                                               93,
		"PAY_SEARCH":                                                94,
		"PAY_UPDATE_NPCI_PIN":                                       95,
		"PAY_TIMELINE":                                              96,
		"PAY_TRANSACTION_CATEGORY":                                  97,
		"PAY_TRANSACTION_CATEGORY_SIMILAR_TXN":                      98,
		"PAY_TRANSACTION_RECEIPT":                                   99,
		"PAY_UPI_PAYMENT":                                           100,
		"PROFILE_ACCOUNTS_LIST":                                     101,
		"PROFILE_ACCOUNT_SETTINGS":                                  102,
		"PROFILE_CONNECTED_ACCOUNT_SETTINGS":                        103,
		"PROFILE_PRIVACY_AND_SECURITY":                              104,
		"PROFILE_SHOW_QR":                                           105,
		"PROFILE_SETTINGS_LEGAL":                                    106,
		"PROFILE_APP_LOGOUT_DIALOG":                                 107,
		"PROFILE_NOTIFICATION_SETTINGS":                             108,
		"PROFILE_EDIT":                                              109,
		"PROFILE_PERSONAL_DETAILS":                                  110,
		"REFERRALS_HISTORY":                                         111,
		"REFERRALS_INVITE_FRIENDS":                                  112,
		"REFERRALS_QUALIFYING_ACTION_REQUIRED":                      113,
		"REFERRALS_TNC":                                             114,
		"REFERRALS_REWARD_CAP_REACHED":                              115,
		"REFERRALS_REWARD_RESET_CAP":                                116,
		"REWARDS_CLAIM":                                             117,
		"REWARDS_DETAIL":                                            118,
		"REWARDS_WAYS_TO_EARN":                                      119,
		"REWARDS_INFO":                                              120,
		"REWARDS_LANDING":                                           121,
		"OFFERS_COLLECTED":                                          122,
		"OFFERS_DETAILS":                                            123,
		"OFFERS_EXCHANGED_OFFER_DETAILS":                            124,
		"OFFERS_REDEEMED_GIFT_VOUCHER":                              125,
		"OFFERS_COIN_EXCHANGE_CLAIM":                                126,
		"OFFERS_COIN_EXCHANGE_SUCCESS":                              127,
		"OFFERS_LANDING":                                            128,
		"OFFERS_DETAIL_TNC":                                         129,
		"REWARDS_SD":                                                130,
		"SALARY_INTRO":                                              131,
		"SALARY_LANDING":                                            132,
		"SALARY_ALL_BENEFITS":                                       133,
		"SALARY_ACTIVE_BENEFITS":                                    134,
		"ASKFI_LANDING":                                             135,
		"ASKFI_ALL_FINANCIAL_ACTIVITY":                              136,
		"ASKFI_SUGGESTIONS":                                         137,
		"VKYC_INFO":                                                 138,
		"VKYC_LANDING":                                              139,
		"VKYC_SCHEDULED":                                            140,
		"VKYC_DEVICE_INCOMPATIBLE":                                  141,
		"VKYC_STATUS":                                               142,
		"WEALTH_ADHAAR_ESIGN":                                       143,
		"WEALTH_COLLECT_PAN":                                        144,
		"WEALTH_COLLECT_SIGNATURE":                                  145,
		"WEALTH_CONSENT":                                            146,
		"WEALTH_DIGILOCKER_LANDING":                                 147,
		"WEALTH_DIGILOCKER_WEBVIEW":                                 148,
		"WEALTH_OTI_FUND_SELECTION":                                 149,
		"WEALTH_ONBOARDING_LANDING":                                 150,
		"WEALTH_MF_ACTIVITY":                                        151,
		"WEALTH_MF_LIST":                                            152,
		"WEALTH_MF_ALL_COLLECTIONS":                                 153,
		"WEALTH_MF_COLLECTIONS_LANDING":                             154,
		"WEALTH_MF_DETAILS":                                         155,
		"WEALTH_MF_INVESTED_DETAILS":                                156,
		"WEALTH_P2P_LANDING":                                        157,
		"WEALTH_ORDER_DETAILS":                                      158,
		"WEALTH_ONBOARDING_STATUS":                                  159,
		"WEALTH_ONBOARDING_MISSING_KYC_DATA":                        160,
		"WEALTH_ONBOARDING_MISSING_KYC_INTRO":                       161,
		"LIVENESS_CONSENT":                                          162,
		"LIVENESS_RECORDING":                                        163,
		"AA_BENEFITS":                                               164,
		"AA_ACCOUNT_INIT":                                           165,
		"ONBOARDING_EKYC_CONSENT":                                   166,
		"ONBOARDING_EKYC_MISMATCH":                                  167,
		"FITTT_RULE_CUSTOMISATION":                                  168,
		"DEBIT_CARD_USAGE_SETTING":                                  169,
		"DEBIT_CARD_TRACKING":                                       170,
		"HOME_MUTUAL_FUNDS_LANDING_TAB":                             171,
		"P2P_LANDING_CONSENT":                                       172,
		"P2P_ACTIVITY_DETAILS":                                      173,
		"P2P_ALL_ACTIVITIES":                                        174,
		"P2P_ELIGIBILITY_CHECK":                                     175,
		"P2P_KNOW_MORE":                                             176,
		"ONBOARDING_DEDUPE_ERROR":                                   177,
		"ONBOARDING_DETAILED_ERROR":                                 178,
		"ONBOARDING_PHONE_VERIFICATION":                             180,
		"ONBOARDING_SAFETY_NET_CONSENT":                             181,
		"SCREENER_MANUAL_INTERVENTION":                              182,
		"OFFERS_EXCHANGE_COIN_DETAIL":                               183,
		"REWARDS_WAYS_TO_EARN_DETAIL":                               184,
		"SALARY_TRANSACTION_SELECTOR":                               185,
		"VKYC_EKYC_EXPIRED":                                         186,
		"VKYC_STATUS_POST_ONBOARDING":                               187,
		"WEALTH_COLLECT_SINGLE_CHOICE":                              188,
		"WEALTH_ONBOARDING_ERROR":                                   189,
		"P2P_LANDING_TAB":                                           190,
		"ONBOARDING_INSTRUCTIONS":                                   191,
		"PAY_QR_SCAN_ML_KIT":                                        192,
		"DEEPLINK_URL":                                              193,
		"SYSTEM_TRAY_NOTIFICATION":                                  194,
		"EDIT_PROFILE_INFO":                                         195,
		"SALARY_ALL_QUICK_LINKS":                                    196,
		"INSIGHTS_HUB":                                              197,
		"SCREENER_CONNECTED_ACCOUNTS_INFO":                          198,
		"TIER_INTRODUCTION_SCREEN":                                  199,
		"TIER_OVERVIEW_SCREEN":                                      200,
		"TIER_UPGRADE_SUCCESS_SCREEN":                               201,
		"HOME_LANDING_V2":                                           202,
		"HOME_EXPLORE_SCREEN":                                       203,
		"INVEST_LANDING":                                            204,
		"HOME_US_STOCKS_TAB":                                        205,
		"US_STOCKS_LANDING_EXPLORE":                                 206,
		"US_STOCKS_LANDING_YOUR_STOCKS":                             207,
		"US_STOCKS_LANDING_WATCHLIST":                               208,
		"US_STOCKS_COLLECTION":                                      209,
		"US_STOCKS_DETAILS":                                         210,
		"US_STOCKS_SEARCH":                                          211,
		"P2P_INVESTMENT_AVAILABLE_PLANS_INFO":                       381,
		"P2P_INVESTMENT_CHOOSE_PLAN":                                382,
		"P2P_INVESTMENT_UNLOCK_PLAN":                                383,
		"P2P_WITHDRAW_MONEY_ENTER_AMOUNT":                           385,
		"P2P_WITHDRAW_MONEY_SUMMARY":                                386,
		"P2P_INVESTMENT_VIEW_BREAKUP_SCREEN":                        409,
		"P2P_INVESTMENT_OTP_SCREEN":                                 460,
		"P2P_CONFIRM_INVESTMENT_RENEWAL_SCREEN":                     461,
		"P2P_INVESTMENT_CURRENT_STATUS_SCREEN":                      464,
		"P2P_INVESTMENT_ACTIVITY_DETAILS_SCREEN":                    465,
		"P2P_INVESTMENT_ALL_UPCOMING_RENEWALS_SCREEN":               466,
		"P2P_INVESTMENT_RENEWAL_CANCELLATION_NUDGE_SCREEN":          467,
		"US_STOCK_BUY_ENTER_AMOUNT":                                 468,
		"US_STOCK_BUY_SUMMARY":                                      469,
		"US_STOCK_SELL_ENTER_AMOUNT":                                470,
		"US_STOCK_SELL_SUMMARY":                                     471,
		"US_STOCK_ORDER_DETAILS":                                    472,
		"US_STOCK_ONB_STMNTS_PAN_FETCH":                             473,
		"US_STOCK_ONB_DECLARATION":                                  474,
		"US_STOCK_ONB_DISCLAIMER":                                   475,
		"US_STOCK_ONB_EMPLOYER_DETAILS":                             476,
		"US_STOCK_ONB_INVESTMENT_RANGE":                             478,
		"US_STOCK_ONB_PAN_UPLOAD_OPTIONS":                           479,
		"US_STOCK_ONB_PAN__UPLOAD_CAMERA":                           480,
		"US_STOCK_ONB_PAN__UPLOAD_GALLERY":                          481,
		"US_STOCK_EXISTING_CONNECTED_ACCOUNT":                       482,
		"US_STOCK_NEW_CONNECTED_ACCOUNT":                            483,
		"US_STOCK_SOF_POLLING":                                      484,
		"US_STOCK_ONB_FUTURE_SCOPE":                                 485,
		"US_STOCK_SOF_FUTURE_SCOPE":                                 486,
		"US_STOCK_SET_UP_ACCOUNT_DIALOG":                            487,
		"US_STOCK_SET_UP_SOF_DIALOG":                                488,
		"US_STOCK_PREREQUISITE_DIALOG":                              489,
		"INVESTMENT_RISK_PROFILE_QUESTIONNAIRE":                     490,
		"INVESTMENT_RISK_PROFILE_DASHBOARD":                         491,
		"VKYC_INTRO":                                                492,
		"VKYC_PAN_TYPE_SELECTION":                                   493,
		"VKYC_INSTRUCTIONS":                                         494,
		"US_STOCKS_SEARCH_LANDING":                                  495,
		"US_STOCK_LANDING_PAGE":                                     496,
		"US_STOCKS_SEARCH_RESULT":                                   497,
		"REMINDERS_ENTRY":                                           498,
		"REMINDERS_LANDING":                                         499,
		"REMINDERS_SET":                                             500,
		"DEPOSIT_AUTO_SAVE_SUGGESTIONS":                             501,
		"VKYC_ERROR_SCREEN":                                         502,
		"REMINDER_OVER_SPEND":                                       503,
		"REMINDER_DATE_CHANGE_PAGE":                                 504,
		"REMINDER_NO_CC_PAGE":                                       505,
		"CX_LANGUAGE_SELECTION_SCREEN":                              506,
		"CX_LANGUAGE_SELECTION_SUCCESS_SCREEN":                      507,
		"CX_LANGUAGE_OPTION_SUGGESTION_SCREEN":                      508,
		"STORY_SCREEN":                                              509,
		"CX_TICKET_DETAIL_SCREEN":                                   510,
		"CX_TICKET_LIST_SCREEN":                                     511,
		"CX_TICKET_TAB_SCREEN":                                      512,
		"CX_CHAT_SCREEN":                                            513,
		"APP_SHORTCUTS":                                             514,
		"DISCONNECT_ACCOUNT_CONFIRM_VIEW_CONTROLLER":                515,
		"DISCONNECT_ACCOUNT_BOTTOM_SHEET_VIEW_CONTROLLER":           516,
		"CA_LANDING_PAGE_FI_TO_FI_FLOW_SCREEN":                      517,
		"CA_INIT_SDK_FI_TO_FI_FLOW_SCREEN":                          518,
		"DEBIT_CARD_CONSOLIDATED_USAGE_SETTING":                     519,
		"DEBIT_CARD_PIN_ACTIVATION_SCREEN":                          621,
		"DEBIT_CARD_SUCCESSFUL_ACTIVATION_SCREEN":                   622,
		"DEBIT_CARD_QR_CODE_SCAN_SCREEN":                            623,
		"PAY_PROCESSING":                                            624,
		"POST_PAYMENT":                                              625,
		"US_STOCKS_ONB_SUCCESS":                                     626,
		"US_STOCKS_ONB_POLLING":                                     627,
		"US_STOCKS_ONB_ADDRESS_ID_PROOF":                            628,
		"US_STOCKS_ONB_ERROR":                                       629,
		"US_STOCKS_PN_ONB_LANDING":                                  630,
		"P2P_UPDATE_MATURITY_CONSENT_DIALOG":                        631,
		"DEPOSIT_ADD_MONEY_FREQUENCY":                               632,
		"DEPOSIT_CLOSURE_PROCESSING":                                633,
		"DEPOSIT_CREATION_PROCESSING":                               634,
		"DEPOSIT_CREATION_AUTO_SAVE_PROCESSING":                     635,
		"DEPOSIT_GENERATE_STATEMENT":                                636,
		"DEPOSIT_TERM_SELECTION":                                    637,
		"DEPOSIT_EXPLAINER_DIALOG":                                  638,
		"DEPOSIT_EXPLAINER_COMPARISON_DIALOG":                       639,
		"ONBOARDING_SCREENER_CHOICE":                                640,
		"CC_INTRO_SCREEN":                                           641,
		"CC_ADDRESS_SELECTION_SCREEN":                               642,
		"CC_BILL_GENERATION_SCREEN":                                 643,
		"CC_LIVENESS_AND_FACE_MATCH_SCREEN":                         644,
		"CC_VKYC_FLOW_SCREEN":                                       645,
		"CC_CARD_CREATION_SCREEN":                                   646,
		"CC_WELCOME_OFFER_SELECTION_SCREEN":                         647,
		"CC_VALUE_BACK_SCREEN":                                      648,
		"CC_ACCELERATED_REWARDS_SCREEN":                             649,
		"CC_WELCOME_VOUCHERS_SCREEN":                                650,
		"CC_WELCOME_OFFER_SELECTION_SUCCESS_SCREEN":                 651,
		"CC_LANDING_SCREEN":                                         652,
		"CC_CONTROLS":                                               653,
		"ES_ELIGIBILITY_LANDING":                                    654,
		"ES_APPLICATION_LANDING":                                    655,
		"ES_CUSTOM_OFFER_SELECTION_DIALOG":                          656,
		"ES_APPLICATION_DETAILS":                                    657,
		"ES_AUTO_REPAYMENT":                                         658,
		"ES_ADDRESS_CONFIRMATION":                                   659,
		"ES_INITIATE_ESIGN":                                         660,
		"ES_ESIGN_VIEW_DOCUMENT":                                    661,
		"ES_APPLICATION_STATUS":                                     662,
		"ES_DASHBOARD":                                              663,
		"ES_LOAN_DETAILS":                                           664,
		"ES_APPLICATION_STATUS_POLL":                                665,
		"PL_LANDING":                                                666,
		"PL_OFFER_DETAILS":                                          667,
		"PL_APPLICATION_DETAILS":                                    668,
		"PL_NOT_QUALIFIED_USERS":                                    669,
		"PL_INITIATE_MANDATE":                                       670,
		"PL_INITIATE_ESIGN":                                         671,
		"PL_ESIGN_VIEW_DOCUMENT":                                    672,
		"PL_APPLICATION_CONFIRMATION_VIA_OTP":                       673,
		"PL_ADDRESS_CONFIRMATION":                                   674,
		"PL_EMPLOYMENT_DETAILS":                                     675,
		"PL_POLLING_SCREEN":                                         676,
		"PL_APPLICATION_STATUS":                                     677,
		"PL_LOAN_DASHBOARD":                                         678,
		"PL_PRE_PAY":                                                679,
		"PL_ACTIVITY_STATUS":                                        680,
		"PL_DOWNTIME":                                               681,
		"PL_ENTER_DETAILS":                                          682,
		"PL_LOAN_DETAILS":                                           683,
		"PL_REVIEW_DETAILS":                                         684,
		"PL_OCCUPATION_DETAILS":                                     685,
		"PL_UPDATED_RATE":                                           686,
		"PL_TRANSACTION_RECEIPT":                                    687,
		"PL_ALL_TRANSACTIONS":                                       688,
		"CC_USAGE_SCREEN":                                           689,
		"CC_LIMITS_SCREEN":                                          690,
		"CC_ALL_TRANSACTIONS_SCREEN":                                691,
		"CC_TRANSACTION_RECEIPT_SCREEN":                             692,
		"CC_BILL_REPAYMENT_SELECTION_SCREEN":                        693,
		"CC_CUSTOM_AMOUNT_BILL_PAY_SCREEN":                          694,
		"CC_PAYMENT_STATUS_SCREEN":                                  695,
		"CC_SET_CARD_PREFERENCES_SCREEN":                            696,
		"CC_SET_CARD_PIN":                                           697,
		"CC_PHYSICAL_CARD_TRACKING_SCREEN":                          698,
		"CC_VIEW_STATEMENT_SCREEN":                                  699,
		"CC_EXPORT_STATEMENT_SCREEN":                                700,
		"CC_FREEZE_UNFREEZE_SCREEN":                                 701,
		"CC_NEW_CARD_REQUEST_ADDRESS_SELECTION_SCREEN":              702,
		"CC_NEW_CARD_REQUEST":                                       703,
		"CC_EMI_DASH_BOARD_SCREEN":                                  704,
		"CC_VIEW_ALL_EMI_TRANSACTIONS_SCREEN":                       705,
		"CC_EMI_TRANSACTION_LOAN_OFFERS_SCREEN":                     706,
		"CC_EMI_PREVIEW_PRE_CLOSE_LOAN_SCREEN":                      707,
		"CC_EMI_LOAN_ACCOUNT_DETAILS_SCREEN":                        708,
		"CC_EMI_PRE_CLOSE_LOAN_SCREEN":                              709,
		"CC_SECURED_OPEN_FD_SCREEN":                                 710,
		"CC_SECURED_FD_DETAILS_SCREEN":                              711,
		"CC_REAL_TIME_ELIGIBILITY_CHECK_INTRO_SCREEN":               712,
		"DEPOSIT_CREATION_SCREEN":                                   713,
		"CARD_TABS_SCREEN":                                          714,
		"CC_BILL_GENERATION_DATE_SELECTION_SCREEN":                  715,
		"CC_EDIT_LIMITS_SCREEN":                                     716,
		"CC_SECURED_TENURE_SELECTION_BOTTOM_SHEET_SCREEN":           717,
		"CC_SECURED_FD_INFO_SCREEN":                                 718,
		"DEPOSIT_DECLARATION":                                       719,
		"CC_AUTH_OPTIONS_SCREEN":                                    720,
		"CC_BENEFITS_SCREEN":                                        721,
		"CC_CUG_SET_PIN_SCREEN":                                     722,
		"CC_EMI_VIEW_ACTIVE_SCREEN":                                 723,
		"CC_SUCCESS_INFO_DIALOG_SCREEN":                             724,
		"CC_INITIATE_SCREEN":                                        725,
		"CC_LOUNGE_ACCESS_SCREEN":                                   726,
		"CC_OTP_SCREEN":                                             727,
		"CC_QR_ACTIVATION_SCREEN":                                   728,
		"CC_SELECT_STATEMENT_DURATION_DIALOG_SCREEN":                729,
		"CC_REQUEST_CUSTOM_REASONS_SCREEN":                          730,
		"CC_STATEMENT_DETAILS_SCREEN":                               731,
		"CC_NEW_REQUEST_SCREEN":                                     732,
		"CC_REQUEST_REASONS_SCREEN":                                 733,
		"CC_THREE_TOP_REWARDS_SCREEN":                               734,
		"CC_EXTRA_REWARDS_DETAILS_DIALOG_SCREEN":                    735,
		"CC_AUTH_POLL_SCREEN":                                       736,
		"CC_GENERIC_HALT_SCREEN":                                    737,
		"CC_KNOW_MORE_SCREEN":                                       738,
		"CC_LIVENESS_SUMMARY_POLL_SCREEN":                           739,
		"CC_PERMANENT_USER_FAILURE_SCREEN":                          740,
		"CC_REWARDS_POLL_SCREEN":                                    741,
		"CC_DISPUTE_DETAILS_SCREEN":                                 742,
		"CC_DISPUTE_DIALOG_SCREEN":                                  743,
		"CC_DISPUTE_QUESTIONS_SCREEN":                               744,
		"CC_DISPUTE_SUMMARY_SCREEN":                                 745,
		"CC_STATUS_POLL_SCREEN":                                     746,
		"CC_ENABLE_PAYMENTS_SCREEN":                                 747,
		"CC_CONFIRM_SELECTION_DIALOG_SCREEN":                        748,
		"CC_WAITLISTED_SCREEN":                                      749,
		"CC_WAITLIST_SCREEN":                                        750,
		"ONBOARDING_INTENT_SELECTION":                               751,
		"NET_WORTH_DEPOSITS_CA_NO_ACC_DISCOVERED_SCREEN":            752,
		"NET_WORTH_DEPOSITS_SCREEN":                                 753,
		"BKYC_CONSENT_SCREEN":                                       754,
		"BKYC_HANDSHAKE_SCREEN":                                     755,
		"BKYC_USER_DETAILS_SCREEN":                                  756,
		"BKYC_CHOICE_SCREEN":                                        757,
		"DEFAULT_ERROR_SCREEN":                                      758,
		"PL_NAME_GENDER_SCREEN":                                     759,
		"PL_PAN_DOB_SCREEN":                                         760,
		"PL_BANKING_DETAILS_SCREEN":                                 761,
		"PL_CREDIT_REPORT_FETCH_CONSENT_SCREEN":                     762,
		"PL_LOAN_ELIGIBILITY_LANDING_SCREEN":                        763,
		"PL_APPLICATION_REVIEW_DETAILS_SCREEN":                      764,
		"PL_CHECK_ELIGIBILITY_LOADING_SCREEN":                       765,
		"PL_ELIGIBILITY_OFFER_AVAILABLE_SCREEN":                     766,
		"PL_ELIGIBILITY_NO_OFFER_AVAILABLE_SCREEN":                  767,
		"PL_NON_ELIGIBLE_USER_LANDING_SCREEN":                       768,
		"CC_HORIZONTAL_INTRO":                                       769,
		"CC_AMPLI_FI_SCREEN":                                        770,
		"CC_INELIGIBLE_USER_SCREEN":                                 771,
		"ONBOARDING_SAVINGS_ACCOUNT_INTRO":                          772,
		"RESET_ONBOARDING_STAGE_RPC":                                773,
		"NETWORTH_DASHBOARD_SCREEN":                                 774,
		"RPC_BASED_REDIRECTION":                                     775,
		"PL_ACQ_TO_LEND_LANDING_SCREEN":                             776,
		"PL_EDIT_LOAN_BOTTOM_SHEET":                                 777,
		"LOANS_INFO_SCREEN":                                         778,
		"REWARD_UNLOCK_DETAILS_BOTTOMSHEET":                         779,
		"OFFER_REDEEM_INFO_BOTTOMSHEET":                             780,
		"OFFER_VISTARA_CONVERSION_BOTTOMSHEET":                      781,
		"OFFER_VISTARA_ENTER_DETAILS_BOTTOMSHEET":                   782,
		"OFFER_SELECT_ADDRESS_DIALOG":                               783,
		"SALARY_CALCULATOR_SCREEN":                                  784,
		"ES_ENABLE_PERMISSIONS_SCREEN":                              785,
		"SALARY_REFERRAL_LANDING_SCREEN":                            786,
		"SALARY_POLICY_INFO_SCREEN":                                 787,
		"SALARY_ENACH_CANCEL_MADATE_SETUP_SCREEN":                   788,
		"SALARY_ENACH_MANDATE_SETUP_SCREEN":                         789,
		"SALARY_ENACH_SETUP_STATUS":                                 790,
		"SALARY_ENACH_INTRO_SCREEN":                                 791,
		"SALARY_ENACH_FULL_UPGRADE_SCREEN":                          792,
		"CANCELLED_CHEQUE_IN_PREVIEW_SCREEN":                        793,
		"SALARY_VERIFICATION_STATUS_SCREEN":                         794,
		"REWARD_PENDING_DETAILS_SCREEN":                             795,
		"REWARD_UNOPENED_LIST_SCREEN":                               796,
		"CX_CATEGORY_SEARCH_SCREEN":                                 797,
		"DC_PHYSICAL_ORDER_SCREEN":                                  798,
		"CX_CATEGORY_DETAILS_SCREEN":                                799,
		"LOANS_DASHBOARD_SCREEN":                                    800,
		"LOANS_OVERVIEW_SCREEN":                                     801,
		"LOANS_DETAILS_SCREEN":                                      802,
		"LOANS_PAYMENT_DETAILS_SCREEN":                              803,
		"PL_EMPLOYER_SELECTION_SCREEN":                              804,
		"PL_LOCATION_PERMISSION_SCREEN":                             805,
		"HELP_LANDING_SCREEN_V2":                                    806,
		"MF_HOLDINGS_IMPORT_GENERATE_OTP_SCREEN":                    807,
		"MF_HOLDINGS_IMPORT_OTP_SUBMISSION_SCREEN":                  808,
		"MF_HOLDINGS_IMPORT_OTP_SUCCESS_LOADING_SCREEN":             809,
		"MF_HOLDINGS_IMPORT_PHONE_NUMBER_SUBMISSION_SCREEN":         810,
		"MF_HOLDINGS_IMPORT_EMAIL_SUBMISSION_SCREEN":                811,
		"MF_HOLDINGS_IMPORT_CONSENT_SCREEN_V2":                      812,
		"CONNECT_BANK_ACCOUNTS_INTRO_SCREEN":                        813,
		"UPLOAD_FILE":                                               814,
		"US_STOCKS_WALLET_LANDING_SCREEN":                           815,
		"US_STOCKS_WALLET_ADD_FUNDS_SCREEN":                         816,
		"US_STOCKS_WALLET_WITHDRAW_SCREEN":                          817,
		"LAMF_LANDING_SCREEN":                                       818,
		"LAMF_JOURNEY_SCREEN":                                       819,
		"LAMF_ADD_ACCOUNT_DETAILS_USER_INPUT_SCREEN":                820,
		"LAMF_CAMS_PORTFOLIO_FETCH_OTP_VERIFICATION_SCREEN":         821,
		"LAMF_KARVY_PORTFOLIO_FETCH_OTP_VERIFICATION_SCREEN":        822,
		"LAMF_VIEW_OFFER_SCREEN":                                    823,
		"LAMF_OFFER_SELECTION_SCREEN":                               824,
		"LAMF_APPLICATION_DETAIL_SCREEN":                            825,
		"LAMF_APPLICATION_ADDITIONAL_DETAILS_USER_INPUT_SCREEN":     826,
		"LAMF_KYC_SCREEN":                                           827,
		"LAMF_ESIGN_SCREEN":                                         828,
		"LAMF_MANDATE_SCREEN":                                       829,
		"LAMF_CAMS_LIEN_MARK_OTP_VERIFICATION_SCREEN":               830,
		"LAMF_KARVY_LIEN_MARK_OTP_VERIFICATION_SCREEN":              831,
		"LAMF_LIEN_PROGRESS_UPDATE_SCREEN":                          832,
		"LAMF_ESIGN_PROGRESS_UPDATE_SCREEN":                         833,
		"LAMF_MANDATE_INTRO_SCREEN":                                 834,
		"LAMF_APPLICATION_SUCCESS_SCREEN":                           835,
		"LAMF_OFFER_FUND_DETAILS_SCREEN":                            836,
		"LAMF_INSUFFICIENT_FOLIO_ERROR_SCREEN":                      837,
		"LAMF_PARTIAL_LIEN_MARK_ERROR_SCREEN":                       838,
		"LAMF_PERMANENT_FAILURE_FULL_SCREEN":                        839,
		"LAMF_HOLDING_SCREEN":                                       840,
		"LAMF_DASHBOARD_SCREEN":                                     841,
		"LAMF_LOAN_OVERVIEW_SCREEN":                                 842,
		"LAMF_PAYMENT_DETAILS_SCREEN":                               843,
		"LAMF_LOAN_DETAILS_SCREEN":                                  844,
		"LAMF_INITIATE_SI_SETUP_SCREEN":                             845,
		"LAMF_PRE_PAY_SCREEN":                                       846,
		"LAMF_FUND_ELIGIBILITY_SCREEN":                              847,
		"LAMF_NO_FUNDS_FOUND_FAILURE_SCREEN":                        848,
		"CC_LOUNGE_ACCESS_V2_SCREEN":                                849,
		"CC_COLLECTED_LOUNGE_PASSES_SCREEN":                         850,
		"LOANS_SELFIE_SCREEN":                                       851,
		"LOANS_FORM_DETAILS_SCREEN":                                 852,
		"PL_OFFER_DETAILS_V2":                                       853,
		"PL_APPLICATION_DETAILS_V2":                                 854,
		"LOANS_INCOME_VERIFICATION_INTRO_SCREEN":                    855,
		"CA_FI_TO_FI_POLLING_SCREEN":                                856,
		"CA_FI_TO_FI_TERMINAL_SCREEN":                               857,
		"LOANS_INCOME_VERIFICATION_RESULT_SCREEN":                   858,
		"CREDIT_CARD_BILLING_DETAILS_BOTTOM_VIEW_SCREEN":            859,
		"US_STOCKS_ONBOARDING_RISKY_PROILE_TERMINAL_SCREEN":         860,
		"US_STOCKS_ONBOARDING_CREATE_PROFILE_SCREEN":                861,
		"US_STOCKS_ONBOARDING_COLLECT_RISK_LEVEL_SCREEN":            862,
		"US_STOCKS_ONBOARDING_RISK_DISCLOSURE_SCREEN":               863,
		"CC_ALL_ELIGIBLE_CARDS_SCREEN":                              864,
		"CC_NETWORK_SELECTION_SCREEN":                               865,
		"LOANS_PWA_LANDING_SCREEN":                                  866,
		"PWA_REDIRECTION_SCREEN":                                    867,
		"LOANS_BOTTOM_SHEET_SCREEN":                                 868,
		"APPLY_FOR_LOAN_SCREEN":                                     869,
		"PL_INITIATE_MANDATE_V2_SCREEN":                             870,
		"PL_ALTERNATE_ACCOUNTS_SCREEN":                              871,
		"PL_MANDATE_SETUP_SCREEN":                                   872,
		"SA_CLOSURE_BENEFIT_SCREEN":                                 873,
		"SA_CLOSURE_FEEDBACK_SCREEN":                                874,
		"SA_CLOSURE_CRITERIA_SCREEN":                                875,
		"PAN_DOB_INPUT_SCREEN":                                      876,
		"SA_CLOSURE_SUBMIT_REQUEST_SWIPE_ACTION_SCREEN":             877,
		"FULL_SCREEN_INFO_VIEW_SCREEN":                              878,
		"SA_CLOSURE_RESOLVE_ISSUE_SCREEN":                           879,
		"SA_CLOSURE_REQUEST_SUBMITTED_SCREEN":                       880,
		"MF_HOLDINGS_IMPORT_PROGRESS_SCREEN":                        881,
		"LOANS_TIMED_LOADER_SCREEN":                                 882,
		"ONBOARDING_ADD_FUNDS_TIERING_SUCCESS_SCREEN":               883,
		"LAMF_KFS_SCREEN":                                           884,
		"LOANS_WEBVIEW_DIGILOCKER":                                  885,
		"LAMF_FUND_VERIFICATION_FAILURE_SCREEN":                     886,
		"LAMF_VERIFY_MF_PROGRESS_UPDATE_SCREEN":                     887,
		"LAMF_LOAN_DETAIL_VERIFICATION_SCREEN":                      888,
		"DC_PHYSICAL_BENEFITS_SCREEN":                               889,
		"DC_CHECK_STATUS_FLOW_SCREEN":                               890,
		"DC_INITIATE_FLOW_SCREEN":                                   891,
		"ORDER_CARD_SUCCESS_SCREEN":                                 892,
		"NEW_CARD_REQUEST_SUCCESS_SCREEN":                           893,
		"DC_RESET_ATM_PIN_OPTIONS_SCREEN":                           894,
		"CC_SYNC_POLL_SCREEN":                                       895,
		"CC_STATION_HALT_SCREEN":                                    896,
		"LOANS_LANDING_SCREEN_V2":                                   897,
		"LOANS_AMOUNT_SELECTOR_SCREEN":                              898,
		"LOANS_DETAILS_SELECTION_V2":                                899,
		"RESET_PIN_CARD_DETAILS_SCREEN":                             900,
		"PAY_TERMINAL_STATUS_SCREEN":                                901,
		"RECURRING_PAYMENT_ACTION_STATUS_SCREEN":                    902,
		"RECURRING_PAYMENT_WEB_VIEW_SCREEN":                         903,
		"NPCI_PIN_SET_SCREEN_V2":                                    904,
		"VPA_MIGRATION_SUCCESS_SCREEN_V1":                           905,
		"TPAP_BANK_SELECTION_SCREEN":                                906,
		"TPAP_ACCOUNT_SELECTION_SCREEN":                             907,
		"TPAP_SELF_TRANSFER_SCREEN":                                 908,
		"TPAP_LINKING_SUCCESS_SCREEN":                               909,
		"VPA_MIGRATION_INTRO_SCREEN":                                910,
		"VPA_MIGRATION_SUCCESS_SCREEN_V2":                           911,
		"UPI_INTERNATIONAL_ACTIVATION_SCREEN":                       912,
		"UPI_INTERNATION_ENTER_AMOUNT_SCREEN":                       913,
		"UPI_TIPS_SCREEN":                                           914,
		"UPI_MAPPER_INTRO_SCREEN":                                   915,
		"UPI_MAPPER_DETAILS_SCREEN":                                 916,
		"UPI_LITE_ADD_MONEY_SCREEN":                                 917,
		"UPI_LITE_INTRO_SCREEN":                                     918,
		"PAY_ASK_FI_SEARCH_SCREEN":                                  919,
		"OFFER_FI_COINS_CONVERSION_BOTTOMSHEET":                     920,
		"OFFER_FI_COINS_ENTER_DETAILS_BOTTOMSHEET":                  921,
		"SALARY_EMPLOYER_DOC_SUBMISSION_BOTTOMSHEET":                922,
		"SALARY_EMPLOYER_SEARCH_BOTTOMSHEET":                        923,
		"SALARY_EMPLOYER_CONFIRMATION_BOTTOMSHEET":                  924,
		"SALARY_EMPLOYER_VERIFICATION_FAILED_BOTTOMSHEET":           925,
		"SALARY_ENACH_BENEFITS_SCREEN":                              926,
		"LAMF_FUND_VERIFICATION_BOTTOM_SHEET":                       927,
		"AA_CONSENT_RENEWAL_DIALOG":                                 928,
		"AA_CONSENT_RENEWAL_SDK_SCREEN":                             929,
		"AA_SDK_SCREEN":                                             930,
		"AA_FI_TO_FI_SDK_SCREEN":                                    931,
		"AA_POLLING_SCREEN":                                         932,
		"AA_TERMINAL_SCREEN":                                        933,
		"AA_REOOBE_SCREEN":                                          934,
		"AA_TRANSACTION_COMING_SOON":                                935,
		"PROFILE_REPORTS_DOWNLOADS":                                 936,
		"PROFILE_SERVICE_REQUESTS":                                  937,
		"CHEQUE_BOOK_ORDER_DETAILS":                                 938,
		"LEGALITY_SIGN_FLOW":                                        939,
		"ORDER_CHEQUEBOOK":                                          940,
		"SAVINGS_SIGN_PROMPT":                                       941,
		"CHEQUE_BOOK_POLLING_SCREEN":                                942,
		"ALFRED_REQUEST_SUMMARY":                                    943,
		"EXPORT_HEALTH_LOG_SCREEN":                                  944,
		"PROFILE_OPEN_SOURCE_LICENSES":                              945,
		"PROFILE_SETTINGS_NOTIFICATION":                             946,
		"DIALOG_ENTER_VPA":                                          947,
		"PAYMENT_METHOD_SELECTION_BOTTOM_SHEET":                     948,
		"PROFILE_PERSONAL_DETAILS_EDIT":                             949,
		"PROFILE_EDIT_IMAGE_SCREEN":                                 950,
		"PROFILE_DIALOG_EDIT_OPTION":                                951,
		"INFO_ACKNOWLEDGEMENT_V2":                                   952,
		"CC_CREDIT_REPORT_ADDRESS_SCREEN":                           953,
		"CC_CONSENT_BOTTOM_SHEET":                                   954,
		"LOANS_ESIGN_WEBVIEW":                                       955,
		"LOANS_MANDATE_DIGIO_SDK":                                   956,
		"ADD_ADDRESS":                                               957,
		"EKYC_CONSENT":                                              958,
		"NAME_DOB_MISMATCH":                                         959,
		"EKYC_VERIFICATION":                                         960,
		"LIVENESS":                                                  961,
		"LIVENESS_POLLING":                                          962,
		"ONB_ADD_FUNDS":                                             963,
		"ONB_ADD_MONEY":                                             964,
		"ONB_ADD_FUNDS_TIERING_SUCCESS":                             965,
		"FI_BENEFITS":                                               966,
		"ATM_PIN_VERIFICATION":                                      967,
		"ACC_DELETION_ACK":                                          968,
		"LIVENESS_MANUAL_VERIFICATION":                              969,
		"LOGIN_VERIFICATION_ERROR":                                  970,
		"FINITE_CODE_VERIFICATION":                                  971,
		"INCREASE_BONUS_FOR_AFFLUENT_USER":                          972,
		"RE_KYC_SCREEN":                                             973,
		"PAN_AUTH_SCREEN":                                           974,
		"AFU_POLL":                                                  975,
		"ACC_CREATION_POLL":                                         976,
		"ONB_NEXT_ACTION_POLL":                                      977,
		"CREDIT_REPORT_AVAILABILITY":                                978,
		"CREDIT_REPORT_VERIFICATION":                                979,
		"EMPLOYMENT_VERIFICATION":                                   980,
		"SCREENER_ERROR":                                            981,
		"SCREENER_REJECT_STATE":                                     982,
		"GMAIL_VERIFICATION_STATUS":                                 983,
		"LINKEDIN_VERIFICATION":                                     984,
		"SIGN_OUT_TRANSITION":                                       985,
		"VKYC_NEXT_ACTION":                                          986,
		"VKYC_REGISTER_NOTIFICATION":                                987,
		"VKYC_HEADLESS":                                             988,
		"EPAN_INIT":                                                 989,
		"VKYC_INSTRUCTION_OVERLAY":                                  990,
		"VKYC_SCHEDULED_ACK":                                        991,
		"LOANS_DEBIT_CARD_DETAILS_SCREEN":                           992,
		"DC_CARD_RENEWAL_TYPE_SELECTION_SCREEN":                     993,
		"EPF_PASSBOOK_IMPORT_CONFIRM_PHONE_NUMBER_SCREEN":           994,
		"DMF_GENERIC_LOADING_SCREEN":                                995,
		"EPF_PASSBOOK_IMPORT_UAN_LIST_SCREEN":                       996,
		"EPF_PASSBOOK_IMPORT_OTP_SCREEN":                            997,
		"EPF_DASHBOARD_SCREEN":                                      998,
		"MANUAL_ASSET_FORM_SCREEN":                                  999,
		"MANUAL_ASSET_DASHBOARD_SCREEN":                             1000,
		"INDIAN_STOCKS_DASHBOARD_SCREEN":                            1001,
		"INDIAN_STOCKS_INSTRUMENT_DETAILS_SCREEN":                   1002,
		"INDIAN_STOCKS_ORDER_RECEIPT_SCREEN":                        1003,
		"ADD_FUND_UPGRADE_UPI_BOTTOM_SHEET":                         1004,
		"ADD_FUNDS_ONB_V1_SUCCESS_TRANSITION_SCREEN":                1005,
		"AFFLUENT_TRANSITION_SCREEN":                                1006,
		"FEATURE_BENEFITS_SCREEN":                                   1007,
		"GENERIC_BOTTOMSHEET_SCREEN":                                1008,
		"EPAN_ENTRYPOINT_SCREEN":                                    1009,
		"GENERIC_TRANSITION_SCREEN":                                 1010,
		"ACCOUNT_DELETION_SCREEN":                                   1011,
		"SCREENER_BOTTOMSHEET_SCREEN":                               1012,
		"GENERIC_RECORD_CONSENT_SCREEN":                             1013,
		"DEVICE_SECURITY_LOCK_SCREEN":                               1014,
		"NEXT_ONBOARDING_ACTION_SCREEN":                             1015,
		"ONBOARDING_PERMISSIONS_SCREEN":                             1016,
		"CKYC_DROPOFF_BOTTOMSHEET_SCREEN":                           1017,
		"VKYC_REVIEW_SCREEN":                                        1018,
		"VKYC_LOADING_SCREEN":                                       1019,
		"VKYC_WAITING_SCREEN":                                       1020,
		"VKYC_VERIFYING_DOCS_SCREEN":                                1021,
		"VKYC_REJECTED_SCREEN":                                      1022,
		"VKYC_AGENTS_BUSY_SCREEN":                                   1023,
		"VKYC_UPLOAD_DOCUMENT_SCREEN":                               1024,
		"VKYC_INSTRUCTIONS_V2_SCREEN":                               1025,
		"EKYC_STATUS_SCREEN":                                        1026,
		"VKYC_RETRY_CALL_SCREEN":                                    1027,
		"VKYC_INSTRUCTIONS_V1_SCREEN":                               1028,
		"VKYC_LANDING_V2_SCREEN":                                    1029,
		"VKYC_PANCARD_TYPE_SELECTION_SCREEN":                        1030,
		"VKYC_NOTIFY_ME_SCREEN":                                     1031,
		"CONFIRM_PHONE_NUMBER_BOTTOMSHEET":                          1032,
		"UPDATE_USER_DETAILS_SCREEN":                                1033,
		"PHONE_VERIFICATION_OTP_SCREEN":                             1034,
		"LIVENESS_SUCCESS_SCREEN":                                   1035,
		"AUTH_FACTOR_UPDATE_STATUS_SCREEN":                          1036,
		"APPLE_SIGNIN_MISSING_EMAIL_SCREEN":                         1037,
		"DEVICE_LOCK_SCREEN":                                        1038,
		"CIBIL_OTP_VERIFICATION":                                    1039,
		"ENTER_MOBILE_NUMBER":                                       1040,
		"US_STOCK_INTRO":                                            1041,
		"US_STOCK_ONB_DROPDOWN":                                     1042,
		"US_STOCK_ACTIVITY_SCREEN":                                  1043,
		"US_STOCK_REMITTANCE_FORM_BOTTOM_SHEET":                     1044,
		"US_STOCK_CANCEL_ORDER_DIALOG":                              1045,
		"US_STOCK_ORDER_SUCCESS_SCREEN":                             1046,
		"US_STOCK_ONB_PAN_UPLOAD_SUCCESS":                           1047,
		"US_STOCK_PAN_RE_UPLOAD":                                    1048,
		"ANALYSER_BENEFITS_SCREEN":                                  1049,
		"CREDIT_ANALYSER_DETAILS_BOTTOM_SHEET":                      1050,
		"CREDIT_ANALYSER_CONSENT_DIALOG":                            1051,
		"CREDIT_ANALYSER_OTP_DIALOG":                                1052,
		"ANALYSER_FILTER_DIALOG":                                    1053,
		"ANALYSER_SINGLE_SELECT_FILTER":                             1054,
		"ANALYSER_MULTI_SELECT_FILTER":                              1055,
		"ANALYSER_DURATION_FILTER":                                  1056,
		"MF_ANALYSER_LANDING_SCREEN":                                1057,
		"SIM_SELECTION_BOTTOM_SHEET":                                1058,
		"DEVICE_REGISTRATION_BOTTOM_SHEET":                          1059,
		"DEVICE_REGISTRATION_ERROR_BOTTOM_SHEET":                    1060,
		"SECURED_CREDIT_CARD_DEPOSIT_SCREEN_V2":                     1061,
		"EPF_PASSBOOK_IMPORT_MANUAL_UAN_SCREEN":                     1062,
		"NETWORTH_INTRO_SCREEN":                                     1063,
		"NETWORTH_WIDGETS_LIST":                                     1064,
		"MF_AUTO_INVEST_SCREEN":                                     1065,
		"FITTT_RULE_ACTIVATION_SUCCESS_SCREEN":                      1066,
		"MF_OTI_SCREEN":                                             1067,
		"INFO_POP_UP_V2":                                            1068,
		"GENERIC_OTP_SCREEN":                                        1069,
		"DEPOSIT_SUMMARY_SCREEN":                                    1070,
		"DEPOSIT_MATURITY_DATE_SELECTION":                           1071,
		"ADD_NOMINEE_SCREEN":                                        1072,
		"ADD_NOMINEE_DETAILS_SCREEN":                                1073,
		"DEPOSIT_DETAILS":                                           1074,
		"DEPOSIT_STATEMENTS_SCREEN":                                 1075,
		"DEPOSIT_MATURITY_AND_PAYMENT_SELECTION":                    1076,
		"HELP_CATEGORY_LIST":                                        1077,
		"US_STOCK_ORDER_STATUS_SCREEN":                              1078,
		"AUTH_LIVENESS_SUMMARY_STATUS_POLLSCREEN":                   1079,
		"IMAGE_CAPTURE_SCREEN":                                      1080,
		"IMAGE_CAPTURED_PREVIEW_SCREEN":                             1081,
		"USER_DETAILS_FORM_SCREEN":                                  1082,
		"TIERING_EARNED_BENEFIT_SCREEN":                             1083,
		"LAMF_LINK_MF_SCREEN":                                       1084,
		"LAMF_SKIP_LINKING_MF_SCREEN":                               1085,
		"PENNY_DROP_ACCOUNT_DETAILS_SCREEN":                         1086,
		"CARD_STATUS_POLL_SCREEN":                                   1087,
		"PENNY_DROP_STATUS_POLL_SCREEN":                             1088,
		"BANK_INFO_SEARCH_SCREEN":                                   1089,
		"LOANS_CALCULATION_BOTTOMSHEET":                             1090,
		"NET_WORTH_REFRESH_GET_NEXT_ACTION":                         1091,
		"NET_WORTH_REFRESH_INIT_BOTTOM_SHEET":                       1092,
		"NET_WORTH_REFRESH_MANUAL_ASSETS_REFRESH":                   1093,
		"NET_WORTH_REFRESH_UPDATE_MANUAL_ASSETS":                    1094,
		"NET_WORTH_REFRESH_SUCCESS_SCREEN":                          1095,
		"CC_LIMIT_CONSENT_SCREEN":                                   1096,
		"CC_ACCEPT_LIMIT_CHANGE_SCREEN":                             1097,
		"CELEBRATION_POPUP":                                         1098,
		"TIERING_EARNED_BENEFIT_HISTORY_SCREEN":                     1099,
		"LOANS_DOCUMENTS_SCREEN":                                    1100,
		"LOANS_PREPAY_V2_SCREEN":                                    1101,
		"LOANS_REPAYMENT_METHODS_SCREEN":                            1102,
		"LOANS_PAY_VIA_CX_SCREEN":                                   1103,
		"LAMF_EMAIL_UPDATE_NFT_USER_AUTH_OTP_SCREEN":                1104,
		"LAMF_MOBILE_UPDATE_NFT_USER_AUTH_OTP_SCREEN":               1105,
		"LAMF_EMAIL_UPDATE_NFT_NEW_EMAIL_VERIFICATION_OTP_SCREEN":   1106,
		"LAMF_MOBILE_UPDATE_NFT_NEW_MOBILE_VERIFICATION_OTP_SCREEN": 1107,
		"LAMF_MF_LINK_SCREEN":                                       1108,
		"LAMF_MF_PARTIAL_LINK_FAILURE_SCREEN":                       1109,
		"LAMF_MF_LINK_FAILURE":                                      1110,
		"TRANSACTION_REWARDS_SUMMARY_DIALOG":                        1111,
		"GET_INVESTMENT_SIPS_MODIFICATION_SCREEN":                   1112,
		"VKYC_CALL_QUALITY_CHECK":                                   1113,
		"LOANS_GENERIC_INFO_BOTTOM_SHEET":                           1114,
		"CX_CONTACT_US_LANDING":                                     1115,
		"CX_CONTACT_US_TERMINAL":                                    1116,
		"CX_CONTACT_US_ISSUE_SELECTION_BOTTOM_SHEET":                1117,
		"UPDATE_STANDING_INSTRUCTION_PIN_SCREEN":                    1118,
		"LOANS_MOBILE_NUMBER_INTRO_SCREEN":                          1119,
		"LOANS_GENERIC_INTRO_SCREEN":                                1120,
		"LOANS_STATUS_POLL_SCREEN":                                  1121,
		"EPAN_INSTRUCTIONS_SCREEN":                                  1122,
		"MAP_ADDRESS_POINTER_SCREEN":                                1123,
		"ADD_NEW_ADDRESS_DETAILS_SCREEN":                            1124,
		"ENACH_CANCELLATION_DISCLAIMER_BOTTOM_SHEET":                1125,
		"LAMF_MF_INTERMEDIATE_LINK_SCREEN":                          1126,
		"VKYC_INSTRUCTIONS_WEB_VIEW_SCREEN":                         1127,
		"AA_SALARY_PROGRAM_FLOWS_TERMINAL_SCREEN":                   1128,
		"AA_SALARY_PROGRAM_FLOWS_AMOUNT_TRANSFER_SETUP_SCREEN":      1129,
		"AA_SALARY_LANDING_SCREEN":                                  1130,
		"AA_SALARY_ADD_FUNDS_VIA_OFF_APP_TRANSFER":                  1131,
		"USSTOCKS_WALLET_BOTTOM_SHEET":                              1132,
		"ALL_OPENED_REWARDS_LIST_SCREEN":                            1133,
		"LOANS_WEB_VIEW_WITH_POLL_SCREEN":                           1134,
		"LOANS_CUSTOM_PLAN_SELECTION_BOTTOM_SHEET":                  1135,
		"LOANS_OFFER_DETAILS_SCREEN":                                1136,
		"HELP_RECENT_ACTIVITIES":                                    1137,
		"HELP_RECENT_ACTIVITY_DETAILS":                              1138,
		"LAMF_MF_LINK_SUCCESSFUL_SCREEN":                            1139,
		"LOANS_ALTERNATE_OFFER_SCREEN":                              1140,
		"REQUEST_CHOICE_SCROLLABLE_BOTTOM_SHEET":                    1141,
		"INFO_ACKNOWLEDGEMENT_BOTTOM_SHEET":                         1142,
		"LIVENESS_POLL_SCREEN":                                      1143,
		"ASSET_LANDING_PAGE":                                        1144,
		"US_STOCKS_ACCOUNT_CREATION_INITIATED_SCREEN":               1145,
		"US_STOCKS_ACCOUNT_CREATION_SUCCESS_SCREEN":                 1146,
		"LOANS_CHECK_ELIGIBILITY_SCREEN":                            1147,
		"DC_DASHBOARD_V2_SCREEN":                                    1148,
		"AA_SALARY_SOURCE_OF_FUND_SCREEN":                           1149,
		"AA_SALARY_DATA_PULL":                                       1150,
		"SDUI_BOTTOM_SHEET":                                         1151,
		"ONBOARDING_SOFT_INTENT_SELECTION":                          1152,
		"USS_FUNDS_TRANSFER_PROGRESS_SCREEN":                        1153,
		"LOAN_ADDRESS_VERIFICATION_INTRO_SCREEN":                    1154,
		"LOAN_ADD_NEW_ADDRESS_DETAILS_SCREEN":                       1155,
		"LOANS_MULTIPLE_OFFER_DETAILS_SCREEN":                       1156,
		"LOANS_MULTIPLE_OFFER_SELECTION_SCREEN":                     1157,
		"INITIATE_UQUDO_SDK":                                        1158,
		"LOAN_ADDRESS_AUTOCOMPLETE_BOTTOMSHEET":                     1159,
		"SECRET_ANALYSER_SCREEN":                                    1160,
		"VKYC_CALL_SCREEN":                                          1161,
		"IMAGE_CAPTURE_INFO":                                        1162,
		"SUBMIT_DOC":                                                1163,
		"JOURNEY_LANDING_SCREEN":                                    1164,
		"JOURNEY_BOTTOMSHEET_SCREEN":                                1165,
		"CONTACT_US_LANDING_SCREEN":                                 1166,
		"CONTACT_US_TERMINAL_SCREEN":                                1167,
		"SALARY_PROGRAM_HEALTH_INS_ONSURITY_INPUT_FORM":             1168,
		"COUNTRY_SELECTION_BOTTOM_SHEET":                            1169,
		"SECRET_ANALYSER_LIBRARY_SCREEN":                            1170,
		"REVISED_LOAN_OFFER_DETAILS_SCREEN":                         1171,
		"REVISED_LOAN_OFFER_CUSTOM_PLAN_SELECTION_BOTTOM_SHEET":     1172,
		"REVISED_LOAN_OFFER_APPLICATION_DETAIL_SCREEN":              1173,
		"CA_BANK_SELECTION":                                         1774,
		"LOANS_AA_CONSENT_COLLECTION_SCREEN":                        1775,
		"CONSENT_V2":                                                1776,
		"US_STOCKS_COLLECT_PROOF_OF_IDENTITY_ADDRESS_SCREEN":        1777,
		"US_STOCKS_SUBMIT_DIGILOCKER_AADHAAR_SCREEN":                1778,
		"US_STOCKS_UPLOAD_PAN_BOTTOM_SHEET":                         1779,
		"PERMISSIONS_BOTTOM_SHEET":                                  1780,
		"CONTACT_US_CATEGORY_SELECTION_SCREEN":                      1781,
		"CONTACT_US_CATEGORY_SELECTION_VIEW_MORE_BOTTOM_SHEET":      1782,
		"COUNTRY_SELECTION_BOTTOMSHEET":                             1783,
		"SHORTCUT_OPTIONS_SCREEN":                                   1784,
		"REWARDS_PROCESSING_SCREEN":                                 1785,
		"HOME_PROMPT_SCREEN":                                        1786,
		"ADD_FUNDS_PROCESSING_SCREEN":                               1787,
		"ADD_FUNDS_SUCCESS_SCREEN":                                  1788,
		"AUTOPAY_ARCHIVED_REQUESTS_SCREEN":                          1789,
		"ACCOUNT_CLOSURE_TRANSFER_INITIATED_SCREEN":                 1790,
		"BANK_TRANSFER_RECENT_PAYEES_SCREEN":                        1791,
		"LINK_CONNECTED_ACCOUNTS_VIA_TPAP_SCREEN":                   1792,
		"CREATE_AUTOPAY_RULE_SCREEN":                                1793,
		"PAY_LANDING_POPUP":                                         1794,
		"PAYMENT_GATEWAY_CHECKOUT_WEB_VIEW":                         1795,
		"DC_USAGE_AND_LIMIT_SETTINGS_SCREEN":                        1796,
		"ONBOARDING_ERROR_SCREEN":                                   1797,
		"CLOSE_ACCOUNT_REOPENING_PAN_VERIFICATION_SCREEN":           1798,
		"ENTER_FINITE_CODE_V1":                                      1799,
		"PERMISSION_DENIED_SCREEN":                                  1800,
		"SKIP_VKYC_BOTTOM_SHEET":                                    1801,
		"VKYC_PRE_REQUISITE_SCREEN":                                 1802,
		"SKIP_NOMINEE_BOTTOM_SHEET":                                 1803,
		"ADD_GUARDIAN_SCREEN":                                       1804,
		"INFO_ACKNOWLEDGEMENT_SCREEN":                               1805,
		"SHOW_CAMERA_MIC_PERMISSION_DENIED_SCREEN":                  1806,
		"HELPER_BOTTOM_SHEET":                                       1807,
		"OFFERS_LANDING_V2":                                         1808,
		"PAN_VERIFICATION":                                          1809,
		"PAY_SEARCH_V2_SCREEN":                                      1810,
		"DC_ONBOARDING_INTRO":                                       1811,
		"CATALOG_OFFER_REDEMPTION_BOTTOM_SHEET":                     1812,
		"REWARDS_ADDRESS_SELECTION_BOTTOM_SHEET":                    1813,
		"OFFER_DETAILS_V2":                                          1814,
		"US_STOCKS_TRADE_DETAILS_SCREEN":                            1815,
		"LOANS_CONSENT_SCREEN":                                      1816,
		"DC_TOGGLE_TRAVEL_MODE_CONFIRMATION_BOTTOM_SHEET":           1817,
		"ONBOARDING_INTRO_V2_SCREEN":                                1818,
		"LOANS_OPTION_SELECTION_BOTTOM_SHEET":                       1819,
		"LOANS_OPTION_SELECTION_FULL_SCREEN":                        1820,
		"DC_ORDER_PHYSICAL_CARD_V2_SCREEN":                          1821,
		"DELIVERY_ADDRESS_SELECTION_BOTTOM_SHEET":                   1822,
		"DC_BENEFITS_DETAILS_BOTTOM_SHEET":                          1823,
		"SMS_CONSENT":                                               1824,
		"DC_PHYSICAL_CARD_ORDER_SUCCESS_SCREEN_V2":                  1825,
		"LOANS_OFFER_INTRO_SCREEN":                                  1826,
		"LOANS_SINGLE_VENDOR_MULTI_OFFER_SCREEN":                    1827,
		"WEALTH_ANALYSER_REPORT_SCREEN":                             1828,
		"PL_INCOME_SELECTION_SCREEN":                                1829,
		"PAN_UPDATE_POLLING":                                        1830,
		"CONTEXTUAL_CARD_BOTTOM_SHEET":                              1831,
		"ADD_FUNDS_ACK_BOTTOM_SHEET":                                1832,
		"PAN_DROP_OFF_BOTTOM_SHEET":                                 1833,
		"INFO_BOTTOM_SHEET":                                         1834,
		"DEFAULT_REFERRAL_CODE_APPLIED_DIALOG":                      1835,
		"EMPLOYER_SEARCH":                                           1836,
		"CLEAR_EXISTING_DETAILS":                                    1837,
		"DEFAULT_REFERRAL_CODE_DIALOG":                              1838,
		"KYC_CONSENT_INFO":                                          1839,
		"PHONE_EMAIL_UPDATE_BOTTOM_SHEET":                           1840,
		"VIRTUAL_CARD_INFO_DIALOG":                                  1841,
		"COMPANY_SEARCH_BOTTOM_SHEET":                               1842,
		"SPLASH":                                                    1843,
		"PERMISSION_RATIONALE":                                      1844,
		"EKYC_EXPIRED_INFO_BOTTOM_SHEET":                            1845,
		"VKYC_DETAILED_BOTTOM_SHEET":                                1846,
		"SELECT_ADDRESS":                                            1847,
		"GENERIC_BOTTOM_SHEET_INFO":                                 1848,
		"EDIT_EMP_BOTTOM_SHEET_V2":                                  1849,
		"EDIT_ABSOLUTE_INCOME_V2":                                   1850,
		"EDIT_EMP_TYPE_V2":                                          1851,
		"EDIT_INCOME_V2":                                            1852,
		"EDIT_OCCUPATION_V2":                                        1853,
		"INFO_BOTTOM_SHEET_V2":                                      1854,
		"EMPLOYER_SEARCH_v2":                                        1855,
		"KYC_INFO":                                                  1856,
		"FI_LITE_INTRO_BOTTOM_SHEET":                                1857,
		"KYC_DETAIL_DIALOG":                                         1858,
		"MANUAL_KYC":                                                1859,
		"VKYC_CALL_SLOTS_BOTTOM_SHEET":                              1860,
		"VKYC_FEEDBACK_BOTTOM_SHEET":                                1861,
		"VKYC_LIMIT_WARNING":                                        1862,
		"NOMINEE_ADDRESS_SELECTION":                                 1863,
		"NOMINEE_ADDRESS":                                           1864,
		"VKYC_ONB_STATUS":                                           1865,
		"VKYC_NEXT_ACTION_API":                                      1866,
		"ATM_LOCATOR_SCREEN":                                        1867,
		"AA_DATA_SHARE_SCREEN":                                      1868,
		"LAMF_REPAYMENT_METHODS_SCREEN":                             1869,
		"DC_COUNTRY_SELECTION_BOTTOM_SHEET":                         1870,
		"ASSET_DASHBOARD_SCREEN":                                    1871,
		"VERIFY_INCOME_HOME_SCREEN":                                 1872,
		"TIER_LOADER_SCREEN":                                        1873,
		"TIER_ALL_PLANS_SCREEN_V2":                                  1874,
		"TIER_UPGRADE_SUCCESS_SCREEN_V2":                            1875,
		"LOANS_KNOW_MORE_BOTTOM_SHEET":                              1876,
		"INCOME_ANALYSIS_STATUS_SCREEN":                             1877,
		"SALARY_ACCOUNT_SELECTION_SCREEN":                           1878,
		"GENERIC_FLOW_COMPLETION_SCREEN":                            1879,
		"GLOBAL_ISSUED_PASSPORT_DATA_VERIFICATION":                  1880,
		"TIER_DROP_OFF_BOTTOM_SHEET":                                1881,
		"VKYC_GENERIC_SDUI_BOTTOMSHEET":                             1882,
		"VKYC_INTRO_V2_SCREEN":                                      1883,
		"VKYC_STEPS_INFO_SCREEN":                                    1884,
		"TIERING_LANDING_SCREEN":                                    1885,
		"TIERING_DROP_OFF_FULL_SCREEN":                              1886,
		"TIER_DETAILED_BENEFITS_BOTTOM_SHEET":                       1887,
		"WEALTH_BUILDER_LANDING_SCREEN":                             1888,
		"LOANS_AUTO_PAY_AUTH_METHOD_SELECTION_BOTTOM_SHEET":         1889,
		"ASSET_IMPORT_STATUS_POLLING_SCREEN":                        1890,
		"GET_PHYSICAL_CARD_DISPATCH_NEXT_ACTION_SCREEN":             1891,
		"LOAN_APPLICATION_ERROR_STATUS_SCREEN":                      1892,
		"MF_HOLDINGS_IMPORT_PAN_CONSENT_SCREEN":                     1893,
		"SA_NOMINEE_DETAILS_UPDATE_SUCCESS_SCREEN":                  1895,
		"WALKTHROUGH_SCREEN":                                        1896,
		"SEND_SMS_DATA":                                             1897,
		"WEALTH_BUILDER_LANDING_CONNECT_MORE_SCREEN":                1898,
		"PORTFOLIO_TRACKER_LANDING_SCREEN":                          1899,
		"PORTFOLIO_TRACKER_ASSET_DETAILS_SCREEN":                    1900,
		"LOANS_FORM_ENTRY_SCREEN":                                   1901,
		"CREDIT_CARD_DASHBOARD_SCREEN_V2":                           1902,
		"LOANS_CONFIRMATION_BOTTOM_SHEET":                           1903,
		"REWARD_CLAIM_SUCCESS_SCREEN":                               1904,
		"CREDIT_CARD_SDK_SCREEN":                                    1905,
		"LOANS_CONSENT_V2_SCREEN":                                   1906,
		"AMB_DETAILS_SCREEN":                                        1907,
		"CREDIT_REPORT_POLL_STATUS":                                 1908,
		"CREDIT_REPORT_CONSENT_V2":                                  1909,
		"RECORD_AUTH_FLOW_COMPLETION":                               1910,
		"INITIATE_CREDIT_REPORT_DOWNLOAD_FOR_ANALYSER":              1911,
		"CONTACT_US_LANDING_SCREEN_V2":                              1912,
		"OPEN_INCIDENTS_SCREEN":                                     1913,
		"CONTACT_US_QUERY_SCREEN":                                   1914,
		"LOANS_PERMISSION_BOTTOM_SHEET_SCREEN":                      1915,
		"LOANS_APPLICATION_CONFIRMATION_BOTTOM_SHEET":               1916,
		"CC_INTRO_V2_SCREEN":                                        1917,
		"GENERIC_PRELAUNCH_SCREEN":                                  1918,
		"LOANS_KEY_VALUE_ROWS_BOTTOM_SHEET":                         1919,
		"NETWORTH_MAGIC_IMPORT_SCREEN":                              1920,
		"SA_DECLARATION":                                            1921,
		"WEALTH_SDUI_BOTTOM_SHEET":                                  1922,
		"CONFIRM_CARD_MAILING_ADDRESS_SCREEN_V2":                    1923,
		"WEB_DIGILOCKER_INTRO_SCREEN":                               1924,
		"WEB_STOCKGUARDIAN_KYC_CHOICE_SCREEN":                       1925,
		"WEB_KYC_RETRY_SCREEN":                                      1926,
		"WEB_KYC_CONFIRMATION_VIA_OTP_SCREEN":                       1927,
		"EARNED_REWARDS_HISTORY_SCREEN":                             1928,
		"CC_DETAILS_AND_BENEFITS_SCREEN":                            1929,
		"RECHARGE_INTRO_SCREEN":                                     1930,
		"RECHARGE_PLANS_SCREEN":                                     1931,
		"BILL_DETAILS_CONFIRMATION_SCREEN":                          1932,
		"SALARY_EST_CANCELLATION_BOTTOM_SHEET":                      1933,
		"COLLECT_CSAT_SURVEY":                                       1934,
		"NETWORTH_MAGIC_IMPORT_IDEAS_SCREEN":                        1944,
		"WEALTH_GENERIC_RECORD_CONSENT":                             1945,
		"LOANS_DASHBOARD_V4_SCREEN":                                 1946,
		"LOANS_SUMMARY_SCREEN":                                      1947,
		"INTERACTIVE_TALK_TO_AI_SCREEN":                             1948,
	}
)

func (x AnalyticsScreenName) Enum() *AnalyticsScreenName {
	p := new(AnalyticsScreenName)
	*p = x
	return p
}

func (x AnalyticsScreenName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnalyticsScreenName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_analytics_analytics_screen_name_proto_enumTypes[0].Descriptor()
}

func (AnalyticsScreenName) Type() protoreflect.EnumType {
	return &file_api_frontend_analytics_analytics_screen_name_proto_enumTypes[0]
}

func (x AnalyticsScreenName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnalyticsScreenName.Descriptor instead.
func (AnalyticsScreenName) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_analytics_analytics_screen_name_proto_rawDescGZIP(), []int{0}
}

var File_api_frontend_analytics_analytics_screen_name_proto protoreflect.FileDescriptor

var file_api_frontend_analytics_analytics_screen_name_proto_rawDesc = []byte{
	0x0a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2a, 0xe7, 0xfe, 0x01, 0x0a, 0x13, 0x41, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x54, 0x49, 0x43, 0x53, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x4e, 0x41, 0x4c,
	0x59, 0x5a, 0x45, 0x52, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x53, 0x4b, 0x46, 0x49, 0x5f,
	0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x53, 0x10, 0x02,
	0x12, 0x10, 0x0a, 0x0c, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x46, 0x49, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x41, 0x42, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x48,
	0x4f, 0x4d, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x41, 0x42, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x48,
	0x4f, 0x4d, 0x45, 0x5f, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49,
	0x54, 0x5f, 0x54, 0x41, 0x42, 0x10, 0x06, 0x12, 0x1a, 0x0a, 0x16, 0x48, 0x4f, 0x4d, 0x45, 0x5f,
	0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x54, 0x41,
	0x42, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x50, 0x32, 0x50, 0x5f,
	0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x54, 0x41, 0x42, 0x10, 0x08, 0x12, 0x1b, 0x0a, 0x17, 0x48, 0x4f,
	0x4d, 0x45, 0x5f, 0x46, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x09, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x41, 0x59, 0x5f, 0x4c,
	0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x0a, 0x12, 0x10, 0x0a, 0x0c, 0x48, 0x45, 0x4c, 0x50,
	0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x0b, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49,
	0x54, 0x54, 0x54, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x0c, 0x12, 0x13, 0x0a,
	0x0f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x45,
	0x54, 0x54, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x0e, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x41, 0x59, 0x5f,
	0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x0f, 0x12, 0x15,
	0x0a, 0x11, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59,
	0x53, 0x45, 0x52, 0x10, 0x10, 0x12, 0x16, 0x0a, 0x12, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x11, 0x12, 0x1a, 0x0a,
	0x16, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49,
	0x54, 0x53, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x10, 0x12, 0x12, 0x1b, 0x0a, 0x17, 0x44, 0x45, 0x42,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4c,
	0x49, 0x4d, 0x49, 0x54, 0x10, 0x13, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x51, 0x52,
	0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x14, 0x12, 0x25, 0x0a, 0x21, 0x44, 0x45, 0x42, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x53, 0x10, 0x15, 0x12, 0x1f,
	0x0a, 0x1b, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x45, 0x57,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x16, 0x12,
	0x23, 0x0a, 0x1f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x53, 0x5f, 0x4c, 0x49,
	0x53, 0x54, 0x10, 0x17, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x10, 0x18, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x10, 0x19,
	0x12, 0x1c, 0x0a, 0x18, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x1a, 0x12, 0x17,
	0x0a, 0x13, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x45, 0x54,
	0x54, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x1b, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x45, 0x42, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x54, 0x4d, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x4f, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x1c, 0x12, 0x24, 0x0a, 0x20, 0x44, 0x45, 0x42, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x54, 0x4d, 0x5f,
	0x50, 0x49, 0x4e, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x1d, 0x12, 0x1b, 0x0a,
	0x17, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41,
	0x54, 0x45, 0x53, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x1e, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x45,
	0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x10,
	0x1f, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x41, 0x44, 0x44,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x20, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x45, 0x50, 0x4f, 0x53,
	0x49, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x4e, 0x55, 0x44, 0x47, 0x45,
	0x53, 0x10, 0x21, 0x12, 0x20, 0x0a, 0x1c, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x43,
	0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x22, 0x12, 0x21, 0x0a, 0x1d, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54,
	0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x4c,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x23, 0x12, 0x21, 0x0a, 0x1d, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x10, 0x24, 0x12, 0x19, 0x0a, 0x15, 0x46,
	0x49, 0x54, 0x54, 0x54, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x10, 0x25, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f,
	0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x53, 0x10, 0x26, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x49,
	0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x27, 0x12, 0x1b, 0x0a,
	0x17, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x28, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x49,
	0x54, 0x54, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x4d, 0x59, 0x5f, 0x52,
	0x55, 0x4c, 0x45, 0x53, 0x10, 0x29, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f,
	0x52, 0x55, 0x4c, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x50, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x2a, 0x12, 0x13, 0x0a, 0x0f, 0x46,
	0x49, 0x54, 0x54, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x53, 0x10, 0x2b,
	0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x53,
	0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x10, 0x2c, 0x12, 0x22, 0x0a, 0x1e, 0x46, 0x49, 0x54, 0x54,
	0x54, 0x5f, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x53, 0x5f, 0x43, 0x48, 0x41, 0x4c, 0x4c, 0x45, 0x4e,
	0x47, 0x45, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x2d, 0x12, 0x21, 0x0a, 0x1d,
	0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x53, 0x5f, 0x43, 0x48, 0x41,
	0x4c, 0x4c, 0x45, 0x4e, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x2e, 0x12,
	0x28, 0x0a, 0x24, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x53, 0x5f,
	0x43, 0x48, 0x41, 0x4c, 0x4c, 0x45, 0x4e, 0x47, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x2f, 0x12, 0x20, 0x0a, 0x1c, 0x46, 0x49, 0x54,
	0x54, 0x54, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x48, 0x49, 0x53, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x30, 0x12, 0x1e, 0x0a, 0x1a, 0x46,
	0x49, 0x54, 0x54, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x31, 0x12, 0x10, 0x0a, 0x0c, 0x48,
	0x45, 0x4c, 0x50, 0x5f, 0x41, 0x52, 0x54, 0x49, 0x43, 0x4c, 0x45, 0x10, 0x32, 0x12, 0x11, 0x0a,
	0x0d, 0x48, 0x45, 0x4c, 0x50, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0x33,
	0x12, 0x16, 0x0a, 0x12, 0x48, 0x45, 0x4c, 0x50, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x49, 0x45, 0x53,
	0x5f, 0x54, 0x4f, 0x50, 0x49, 0x43, 0x10, 0x34, 0x12, 0x1b, 0x0a, 0x17, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x53, 0x10, 0x35, 0x12, 0x17, 0x0a, 0x13, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x10, 0x36, 0x12, 0x1f,
	0x0a, 0x1b, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x5f,
	0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x37, 0x12,
	0x1b, 0x0a, 0x17, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e,
	0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x38, 0x12, 0x2c, 0x0a, 0x28,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x46, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x39, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x4d, 0x4f, 0x4e,
	0x45, 0x59, 0x10, 0x3a, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45,
	0x53, 0x53, 0x10, 0x3b, 0x12, 0x22, 0x0a, 0x1e, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x53, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x3c, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x4e, 0x42, 0x4f,
	0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44,
	0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x10, 0x3d, 0x12, 0x17, 0x0a, 0x13, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x41, 0x43,
	0x4b, 0x10, 0x3e, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x41, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x10, 0x3f, 0x12, 0x29, 0x0a, 0x25, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x40, 0x12, 0x16,
	0x0a, 0x12, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x4c, 0x4c,
	0x5f, 0x53, 0x45, 0x54, 0x10, 0x41, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x54,
	0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x42, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x4e, 0x42, 0x4f,
	0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52,
	0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x44, 0x4f, 0x42, 0x10, 0x43, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x4e, 0x43, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0x44, 0x12, 0x26, 0x0a, 0x22, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x57, 0x48, 0x41, 0x54, 0x53, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x4f, 0x4e,
	0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x45, 0x12, 0x25, 0x0a,
	0x21, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45,
	0x4e, 0x54, 0x10, 0x46, 0x12, 0x21, 0x0a, 0x1d, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x45,
	0x54, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0x47, 0x12, 0x2d, 0x0a, 0x29, 0x4f, 0x4e, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x4f, 0x4e,
	0x53, 0x45, 0x4e, 0x54, 0x10, 0x48, 0x12, 0x2e, 0x0a, 0x2a, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x45, 0x4d,
	0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x49, 0x12, 0x24, 0x0a, 0x20, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x45, 0x58,
	0x50, 0x45, 0x52, 0x49, 0x41, 0x4e, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x4a, 0x12, 0x35, 0x0a, 0x31,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e,
	0x54, 0x10, 0x4b, 0x12, 0x29, 0x0a, 0x25, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x59, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x47, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x4c, 0x12, 0x2d,
	0x0a, 0x29, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x49, 0x4e, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x4d, 0x12, 0x28, 0x0a,
	0x24, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x45, 0x52, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x5f,
	0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x4e, 0x12, 0x26, 0x0a, 0x22, 0x4f, 0x4e, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x57,
	0x4f, 0x52, 0x4b, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x4f, 0x12,
	0x18, 0x0a, 0x14, 0x50, 0x41, 0x59, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x50, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x41, 0x59,
	0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x53, 0x10, 0x51, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x41, 0x59, 0x5f, 0x41, 0x55,
	0x54, 0x4f, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x52, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x41, 0x59, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x53, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x41, 0x59, 0x5f,
	0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x54, 0x12, 0x24, 0x0a, 0x20, 0x50, 0x41, 0x59, 0x5f, 0x52,
	0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x55, 0x12, 0x21, 0x0a,
	0x1d, 0x50, 0x41, 0x59, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x43, 0x55,
	0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x56,
	0x12, 0x15, 0x0a, 0x11, 0x50, 0x41, 0x59, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x41, 0x59,
	0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x10, 0x57, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x41, 0x59, 0x5f, 0x41,
	0x55, 0x54, 0x4f, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x10, 0x58, 0x12,
	0x15, 0x0a, 0x11, 0x50, 0x41, 0x59, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x46, 0x45, 0x52, 0x10, 0x59, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x41, 0x59, 0x5f, 0x44, 0x49,
	0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x10, 0x5a, 0x12, 0x15, 0x0a,
	0x11, 0x50, 0x41, 0x59, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x5b, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x41, 0x59, 0x5f, 0x50, 0x48, 0x4f, 0x4e,
	0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x5c, 0x12, 0x0f, 0x0a, 0x0b, 0x50,
	0x41, 0x59, 0x5f, 0x51, 0x52, 0x5f, 0x53, 0x43, 0x41, 0x4e, 0x10, 0x5d, 0x12, 0x0e, 0x0a, 0x0a,
	0x50, 0x41, 0x59, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x10, 0x5e, 0x12, 0x17, 0x0a, 0x13,
	0x50, 0x41, 0x59, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x50, 0x43, 0x49, 0x5f,
	0x50, 0x49, 0x4e, 0x10, 0x5f, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x41, 0x59, 0x5f, 0x54, 0x49, 0x4d,
	0x45, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x60, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x41, 0x59, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x10, 0x61, 0x12, 0x28, 0x0a, 0x24, 0x50, 0x41, 0x59, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x53, 0x49, 0x4d, 0x49, 0x4c, 0x41, 0x52, 0x5f, 0x54, 0x58, 0x4e, 0x10, 0x62, 0x12,
	0x1b, 0x0a, 0x17, 0x50, 0x41, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x50, 0x54, 0x10, 0x63, 0x12, 0x13, 0x0a, 0x0f,
	0x50, 0x41, 0x59, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x64, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x65, 0x12, 0x1c, 0x0a, 0x18,
	0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x66, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x52,
	0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x53,
	0x10, 0x67, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x50, 0x52,
	0x49, 0x56, 0x41, 0x43, 0x59, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49,
	0x54, 0x59, 0x10, 0x68, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x53, 0x48, 0x4f, 0x57, 0x5f, 0x51, 0x52, 0x10, 0x69, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x4c, 0x45,
	0x47, 0x41, 0x4c, 0x10, 0x6a, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x41, 0x50, 0x50, 0x5f, 0x4c, 0x4f, 0x47, 0x4f, 0x55, 0x54, 0x5f, 0x44, 0x49, 0x41, 0x4c,
	0x4f, 0x47, 0x10, 0x6b, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x54,
	0x54, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x6c, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x52, 0x4f, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x45, 0x44, 0x49, 0x54, 0x10, 0x6d, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x52, 0x4f,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x6e, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x46, 0x45, 0x52,
	0x52, 0x41, 0x4c, 0x53, 0x5f, 0x48, 0x49, 0x53, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x6f, 0x12, 0x1c,
	0x0a, 0x18, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x49,
	0x54, 0x45, 0x5f, 0x46, 0x52, 0x49, 0x45, 0x4e, 0x44, 0x53, 0x10, 0x70, 0x12, 0x28, 0x0a, 0x24,
	0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x5f, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x46,
	0x59, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x49, 0x52, 0x45, 0x44, 0x10, 0x71, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52,
	0x41, 0x4c, 0x53, 0x5f, 0x54, 0x4e, 0x43, 0x10, 0x72, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x46,
	0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x41,
	0x50, 0x5f, 0x52, 0x45, 0x41, 0x43, 0x48, 0x45, 0x44, 0x10, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x52,
	0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f,
	0x52, 0x45, 0x53, 0x45, 0x54, 0x5f, 0x43, 0x41, 0x50, 0x10, 0x74, 0x12, 0x11, 0x0a, 0x0d, 0x52,
	0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x10, 0x75, 0x12, 0x12,
	0x0a, 0x0e, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x10, 0x76, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x57, 0x41,
	0x59, 0x53, 0x5f, 0x54, 0x4f, 0x5f, 0x45, 0x41, 0x52, 0x4e, 0x10, 0x77, 0x12, 0x10, 0x0a, 0x0c,
	0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x78, 0x12, 0x13,
	0x0a, 0x0f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x79, 0x12, 0x14, 0x0a, 0x10, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x43, 0x4f,
	0x4c, 0x4c, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x7a, 0x12, 0x12, 0x0a, 0x0e, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x7b, 0x12, 0x22, 0x0a,
	0x1e, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45,
	0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x7c, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x52, 0x45, 0x44, 0x45,
	0x45, 0x4d, 0x45, 0x44, 0x5f, 0x47, 0x49, 0x46, 0x54, 0x5f, 0x56, 0x4f, 0x55, 0x43, 0x48, 0x45,
	0x52, 0x10, 0x7d, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x43, 0x4f,
	0x49, 0x4e, 0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x49,
	0x4d, 0x10, 0x7e, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x43, 0x4f,
	0x49, 0x4e, 0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x10, 0x7f, 0x12, 0x13, 0x0a, 0x0e, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f,
	0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x80, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x4e, 0x43, 0x10,
	0x81, 0x01, 0x12, 0x0f, 0x0a, 0x0a, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x53, 0x44,
	0x10, 0x82, 0x01, 0x12, 0x11, 0x0a, 0x0c, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x49, 0x4e,
	0x54, 0x52, 0x4f, 0x10, 0x83, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59,
	0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x84, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x53,
	0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49,
	0x54, 0x53, 0x10, 0x85, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x10,
	0x86, 0x01, 0x12, 0x12, 0x0a, 0x0d, 0x41, 0x53, 0x4b, 0x46, 0x49, 0x5f, 0x4c, 0x41, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x87, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x41, 0x53, 0x4b, 0x46, 0x49, 0x5f,
	0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x49, 0x41, 0x4c, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x10, 0x88, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x41, 0x53, 0x4b,
	0x46, 0x49, 0x5f, 0x53, 0x55, 0x47, 0x47, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x89,
	0x01, 0x12, 0x0e, 0x0a, 0x09, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x8a,
	0x01, 0x12, 0x11, 0x0a, 0x0c, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x8b, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x43, 0x48,
	0x45, 0x44, 0x55, 0x4c, 0x45, 0x44, 0x10, 0x8c, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x56, 0x4b, 0x59,
	0x43, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x50, 0x41,
	0x54, 0x49, 0x42, 0x4c, 0x45, 0x10, 0x8d, 0x01, 0x12, 0x10, 0x0a, 0x0b, 0x56, 0x4b, 0x59, 0x43,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x8e, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x57, 0x45,
	0x41, 0x4c, 0x54, 0x48, 0x5f, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x45, 0x53, 0x49, 0x47,
	0x4e, 0x10, 0x8f, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x43,
	0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x50, 0x41, 0x4e, 0x10, 0x90, 0x01, 0x12, 0x1d, 0x0a,
	0x18, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x5f,
	0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x55, 0x52, 0x45, 0x10, 0x91, 0x01, 0x12, 0x13, 0x0a, 0x0e,
	0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x92,
	0x01, 0x12, 0x1e, 0x0a, 0x19, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x44, 0x49, 0x47, 0x49,
	0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x93,
	0x01, 0x12, 0x1e, 0x0a, 0x19, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x44, 0x49, 0x47, 0x49,
	0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x52, 0x5f, 0x57, 0x45, 0x42, 0x56, 0x49, 0x45, 0x57, 0x10, 0x94,
	0x01, 0x12, 0x1e, 0x0a, 0x19, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x54, 0x49, 0x5f,
	0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x95,
	0x01, 0x12, 0x1e, 0x0a, 0x19, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f,
	0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x96,
	0x01, 0x12, 0x17, 0x0a, 0x12, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4d, 0x46, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x10, 0x97, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x57, 0x45,
	0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4d, 0x46, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x98, 0x01, 0x12,
	0x1e, 0x0a, 0x19, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4d, 0x46, 0x5f, 0x41, 0x4c, 0x4c,
	0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x99, 0x01, 0x12,
	0x22, 0x0a, 0x1d, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4d, 0x46, 0x5f, 0x43, 0x4f, 0x4c,
	0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x9a, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4d, 0x46,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x9b, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x57,
	0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4d, 0x46, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x45,
	0x44, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x9c, 0x01, 0x12, 0x17, 0x0a, 0x12,
	0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x50, 0x32, 0x50, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x9d, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x9e, 0x01,
	0x12, 0x1d, 0x0a, 0x18, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x9f, 0x01, 0x12,
	0x27, 0x0a, 0x22, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x4b, 0x59, 0x43,
	0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0xa0, 0x01, 0x12, 0x28, 0x0a, 0x23, 0x57, 0x45, 0x41, 0x4c,
	0x54, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x49,
	0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x10,
	0xa1, 0x01, 0x12, 0x15, 0x0a, 0x10, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43,
	0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0xa2, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0xa3, 0x01, 0x12, 0x10, 0x0a, 0x0b, 0x41, 0x41, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54,
	0x53, 0x10, 0xa4, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x41, 0x41, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0xa5, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0xa6, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x4f, 0x4e, 0x42, 0x4f,
	0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x4d, 0x49, 0x53, 0x4d,
	0x41, 0x54, 0x43, 0x48, 0x10, 0xa7, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x46, 0x49, 0x54, 0x54, 0x54,
	0x5f, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x53, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0xa8, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x55, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0xa9, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0xaa, 0x01, 0x12,
	0x22, 0x0a, 0x1d, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46,
	0x55, 0x4e, 0x44, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x41, 0x42,
	0x10, 0xab, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x50, 0x32, 0x50, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0xac, 0x01, 0x12, 0x19, 0x0a,
	0x14, 0x50, 0x32, 0x50, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0xad, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x50, 0x32, 0x50, 0x5f,
	0x41, 0x4c, 0x4c, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0xae,
	0x01, 0x12, 0x1a, 0x0a, 0x15, 0x50, 0x32, 0x50, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0xaf, 0x01, 0x12, 0x12, 0x0a,
	0x0d, 0x50, 0x32, 0x50, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x5f, 0x4d, 0x4f, 0x52, 0x45, 0x10, 0xb0,
	0x01, 0x12, 0x1c, 0x0a, 0x17, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xb1, 0x01, 0x12,
	0x1e, 0x0a, 0x19, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xb2, 0x01, 0x12,
	0x22, 0x0a, 0x1d, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x48,
	0x4f, 0x4e, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0xb4, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x41, 0x46, 0x45, 0x54, 0x59, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x4e,
	0x53, 0x45, 0x4e, 0x54, 0x10, 0xb5, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xb6, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x53, 0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x43, 0x4f,
	0x49, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x10, 0xb7, 0x01, 0x12, 0x20, 0x0a, 0x1b,
	0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x57, 0x41, 0x59, 0x53, 0x5f, 0x54, 0x4f, 0x5f,
	0x45, 0x41, 0x52, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x10, 0xb8, 0x01, 0x12, 0x20,
	0x0a, 0x1b, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x10, 0xb9, 0x01,
	0x12, 0x16, 0x0a, 0x11, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x45, 0x58,
	0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0xba, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x56, 0x4b, 0x59, 0x43,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x4f, 0x53, 0x54, 0x5f, 0x4f, 0x4e, 0x42,
	0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0xbb, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x57, 0x45,
	0x41, 0x4c, 0x54, 0x48, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x53, 0x49, 0x4e,
	0x47, 0x4c, 0x45, 0x5f, 0x43, 0x48, 0x4f, 0x49, 0x43, 0x45, 0x10, 0xbc, 0x01, 0x12, 0x1c, 0x0a,
	0x17, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xbd, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x50,
	0x32, 0x50, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x41, 0x42, 0x10, 0xbe,
	0x01, 0x12, 0x1c, 0x0a, 0x17, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0xbf, 0x01, 0x12,
	0x17, 0x0a, 0x12, 0x50, 0x41, 0x59, 0x5f, 0x51, 0x52, 0x5f, 0x53, 0x43, 0x41, 0x4e, 0x5f, 0x4d,
	0x4c, 0x5f, 0x4b, 0x49, 0x54, 0x10, 0xc0, 0x01, 0x12, 0x11, 0x0a, 0x0c, 0x44, 0x45, 0x45, 0x50,
	0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x55, 0x52, 0x4c, 0x10, 0xc1, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x53,
	0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x54, 0x52, 0x41, 0x59, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xc2, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10,
	0xc3, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x41, 0x4c, 0x4c,
	0x5f, 0x51, 0x55, 0x49, 0x43, 0x4b, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x53, 0x10, 0xc4, 0x01, 0x12,
	0x11, 0x0a, 0x0c, 0x49, 0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x53, 0x5f, 0x48, 0x55, 0x42, 0x10,
	0xc5, 0x01, 0x12, 0x25, 0x0a, 0x20, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x43,
	0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x53, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0xc6, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x54, 0x49, 0x45,
	0x52, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc7, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x54, 0x49, 0x45, 0x52,
	0x5f, 0x4f, 0x56, 0x45, 0x52, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xc8, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x50, 0x47, 0x52,
	0x41, 0x44, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xc9, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x41,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x56, 0x32, 0x10, 0xca, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x48,
	0x4f, 0x4d, 0x45, 0x5f, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xcb, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x5f,
	0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0xcc, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x48, 0x4f,
	0x4d, 0x45, 0x5f, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x54, 0x41, 0x42,
	0x10, 0xcd, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53,
	0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52, 0x45,
	0x10, 0xce, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53,
	0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x59, 0x4f, 0x55, 0x52, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x53, 0x10, 0xcf, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x41, 0x54,
	0x43, 0x48, 0x4c, 0x49, 0x53, 0x54, 0x10, 0xd0, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x55, 0x53, 0x5f,
	0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0xd1, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b,
	0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0xd2, 0x01, 0x12, 0x15, 0x0a, 0x10,
	0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48,
	0x10, 0xd3, 0x01, 0x12, 0x28, 0x0a, 0x23, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53,
	0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f,
	0x50, 0x4c, 0x41, 0x4e, 0x53, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0xfd, 0x02, 0x12, 0x1f, 0x0a,
	0x1a, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x43, 0x48, 0x4f, 0x4f, 0x53, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0xfe, 0x02, 0x12, 0x1f,
	0x0a, 0x1a, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x55, 0x4e, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0xff, 0x02, 0x12,
	0x24, 0x0a, 0x1f, 0x50, 0x32, 0x50, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x5f,
	0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x41, 0x4d, 0x4f, 0x55,
	0x4e, 0x54, 0x10, 0x81, 0x03, 0x12, 0x1f, 0x0a, 0x1a, 0x50, 0x32, 0x50, 0x5f, 0x57, 0x49, 0x54,
	0x48, 0x44, 0x52, 0x41, 0x57, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x53, 0x55, 0x4d, 0x4d,
	0x41, 0x52, 0x59, 0x10, 0x82, 0x03, 0x12, 0x27, 0x0a, 0x22, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e,
	0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x42, 0x52,
	0x45, 0x41, 0x4b, 0x55, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x99, 0x03, 0x12,
	0x1e, 0x0a, 0x19, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcc, 0x03, 0x12,
	0x2a, 0x0a, 0x25, 0x50, 0x32, 0x50, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x5f, 0x49,
	0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x41,
	0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcd, 0x03, 0x12, 0x29, 0x0a, 0x24, 0x50,
	0x32, 0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x55,
	0x52, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xd0, 0x03, 0x12, 0x2b, 0x0a, 0x26, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e,
	0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54,
	0x59, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xd1, 0x03, 0x12, 0x30, 0x0a, 0x2b, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53,
	0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x55, 0x50, 0x43, 0x4f, 0x4d, 0x49,
	0x4e, 0x47, 0x5f, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x41, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xd2, 0x03, 0x12, 0x35, 0x0a, 0x30, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e, 0x56,
	0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x41, 0x4c, 0x5f,
	0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x55, 0x44,
	0x47, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd3, 0x03, 0x12, 0x1e, 0x0a, 0x19,
	0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x45, 0x4e, 0x54,
	0x45, 0x52, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0xd4, 0x03, 0x12, 0x19, 0x0a, 0x14,
	0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x53, 0x55, 0x4d,
	0x4d, 0x41, 0x52, 0x59, 0x10, 0xd5, 0x03, 0x12, 0x1f, 0x0a, 0x1a, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x41,
	0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0xd6, 0x03, 0x12, 0x1a, 0x0a, 0x15, 0x55, 0x53, 0x5f, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52,
	0x59, 0x10, 0xd7, 0x03, 0x12, 0x1b, 0x0a, 0x16, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0xd8,
	0x03, 0x12, 0x22, 0x0a, 0x1d, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x4e,
	0x42, 0x5f, 0x53, 0x54, 0x4d, 0x4e, 0x54, 0x53, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x46, 0x45, 0x54,
	0x43, 0x48, 0x10, 0xd9, 0x03, 0x12, 0x1d, 0x0a, 0x18, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0xda, 0x03, 0x12, 0x1c, 0x0a, 0x17, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b,
	0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x45, 0x52, 0x10,
	0xdb, 0x03, 0x12, 0x22, 0x0a, 0x1d, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4f,
	0x4e, 0x42, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x53, 0x10, 0xdc, 0x03, 0x12, 0x22, 0x0a, 0x1d, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x10, 0xde, 0x03, 0x12, 0x24, 0x0a, 0x1f, 0x55, 0x53,
	0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x55,
	0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0xdf, 0x03,
	0x12, 0x24, 0x0a, 0x1f, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x4e, 0x42,
	0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x43, 0x41, 0x4d,
	0x45, 0x52, 0x41, 0x10, 0xe0, 0x03, 0x12, 0x25, 0x0a, 0x20, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x5f, 0x55, 0x50, 0x4c, 0x4f,
	0x41, 0x44, 0x5f, 0x47, 0x41, 0x4c, 0x4c, 0x45, 0x52, 0x59, 0x10, 0xe1, 0x03, 0x12, 0x28, 0x0a,
	0x23, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49,
	0x4e, 0x47, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x10, 0xe2, 0x03, 0x12, 0x23, 0x0a, 0x1e, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0xe3, 0x03, 0x12, 0x19, 0x0a, 0x14,
	0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x53, 0x4f, 0x46, 0x5f, 0x50, 0x4f, 0x4c,
	0x4c, 0x49, 0x4e, 0x47, 0x10, 0xe4, 0x03, 0x12, 0x1e, 0x0a, 0x19, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x46, 0x55, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53,
	0x43, 0x4f, 0x50, 0x45, 0x10, 0xe5, 0x03, 0x12, 0x1e, 0x0a, 0x19, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x5f, 0x53, 0x4f, 0x46, 0x5f, 0x46, 0x55, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53,
	0x43, 0x4f, 0x50, 0x45, 0x10, 0xe6, 0x03, 0x12, 0x23, 0x0a, 0x1e, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x55, 0x50, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0xe7, 0x03, 0x12, 0x1f, 0x0a, 0x1a,
	0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x55, 0x50, 0x5f,
	0x53, 0x4f, 0x46, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0xe8, 0x03, 0x12, 0x21, 0x0a,
	0x1c, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x50, 0x52, 0x45, 0x52, 0x45, 0x51,
	0x55, 0x49, 0x53, 0x49, 0x54, 0x45, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0xe9, 0x03,
	0x12, 0x2a, 0x0a, 0x25, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52,
	0x49, 0x53, 0x4b, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45, 0x10, 0xea, 0x03, 0x12, 0x26, 0x0a, 0x21,
	0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f,
	0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x10, 0xeb, 0x03, 0x12, 0x0f, 0x0a, 0x0a, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x4e, 0x54,
	0x52, 0x4f, 0x10, 0xec, 0x03, 0x12, 0x1c, 0x0a, 0x17, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x41,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0xed, 0x03, 0x12, 0x16, 0x0a, 0x11, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x4e, 0x53, 0x54,
	0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0xee, 0x03, 0x12, 0x1d, 0x0a, 0x18, 0x55,
	0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f,
	0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0xef, 0x03, 0x12, 0x1a, 0x0a, 0x15, 0x55, 0x53,
	0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50,
	0x41, 0x47, 0x45, 0x10, 0xf0, 0x03, 0x12, 0x1c, 0x0a, 0x17, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x53, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c,
	0x54, 0x10, 0xf1, 0x03, 0x12, 0x14, 0x0a, 0x0f, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52,
	0x53, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x10, 0xf2, 0x03, 0x12, 0x16, 0x0a, 0x11, 0x52, 0x45,
	0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0xf3, 0x03, 0x12, 0x12, 0x0a, 0x0d, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x53, 0x5f,
	0x53, 0x45, 0x54, 0x10, 0xf4, 0x03, 0x12, 0x22, 0x0a, 0x1d, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49,
	0x54, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x53, 0x41, 0x56, 0x45, 0x5f, 0x53, 0x55, 0x47, 0x47,
	0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0xf5, 0x03, 0x12, 0x16, 0x0a, 0x11, 0x56, 0x4b,
	0x59, 0x43, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xf6, 0x03, 0x12, 0x18, 0x0a, 0x13, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x4f,
	0x56, 0x45, 0x52, 0x5f, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x10, 0xf7, 0x03, 0x12, 0x1e, 0x0a, 0x19,
	0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x48,
	0x41, 0x4e, 0x47, 0x45, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0xf8, 0x03, 0x12, 0x18, 0x0a, 0x13,
	0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x5f, 0x43, 0x43, 0x5f, 0x50,
	0x41, 0x47, 0x45, 0x10, 0xf9, 0x03, 0x12, 0x21, 0x0a, 0x1c, 0x43, 0x58, 0x5f, 0x4c, 0x41, 0x4e,
	0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfa, 0x03, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x58, 0x5f,
	0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xfb, 0x03, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x58, 0x5f, 0x4c, 0x41, 0x4e, 0x47, 0x55,
	0x41, 0x47, 0x45, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x47, 0x47, 0x45,
	0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfc, 0x03, 0x12,
	0x11, 0x0a, 0x0c, 0x53, 0x54, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xfd, 0x03, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x58, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfe, 0x03,
	0x12, 0x1a, 0x0a, 0x15, 0x43, 0x58, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x4c, 0x49,
	0x53, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xff, 0x03, 0x12, 0x19, 0x0a, 0x14,
	0x43, 0x58, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x54, 0x41, 0x42, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x80, 0x04, 0x12, 0x13, 0x0a, 0x0e, 0x43, 0x58, 0x5f, 0x43, 0x48,
	0x41, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x81, 0x04, 0x12, 0x12, 0x0a, 0x0d,
	0x41, 0x50, 0x50, 0x5f, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x53, 0x10, 0x82, 0x04,
	0x12, 0x2f, 0x0a, 0x2a, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x5f, 0x56,
	0x49, 0x45, 0x57, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x4c, 0x45, 0x52, 0x10, 0x83,
	0x04, 0x12, 0x34, 0x0a, 0x2f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53,
	0x48, 0x45, 0x45, 0x54, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f,
	0x4c, 0x4c, 0x45, 0x52, 0x10, 0x84, 0x04, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x41, 0x5f, 0x4c, 0x41,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x5f, 0x54, 0x4f,
	0x5f, 0x46, 0x49, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0x85, 0x04, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x41, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x5f, 0x53, 0x44,
	0x4b, 0x5f, 0x46, 0x49, 0x5f, 0x54, 0x4f, 0x5f, 0x46, 0x49, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x86, 0x04, 0x12, 0x2a, 0x0a, 0x25, 0x44, 0x45, 0x42,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x4f, 0x4c, 0x49, 0x44,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x55, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x87, 0x04, 0x12, 0x25, 0x0a, 0x20, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xed, 0x04, 0x12, 0x2c, 0x0a, 0x27,
	0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x46, 0x55, 0x4c, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xee, 0x04, 0x12, 0x23, 0x0a, 0x1e, 0x44, 0x45,
	0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x51, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x53, 0x43, 0x41, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xef, 0x04, 0x12,
	0x13, 0x0a, 0x0e, 0x50, 0x41, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e,
	0x47, 0x10, 0xf0, 0x04, 0x12, 0x11, 0x0a, 0x0c, 0x50, 0x4f, 0x53, 0x54, 0x5f, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0xf1, 0x04, 0x12, 0x1a, 0x0a, 0x15, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x10, 0xf2, 0x04, 0x12, 0x1a, 0x0a, 0x15, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53,
	0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0xf3, 0x04, 0x12,
	0x23, 0x0a, 0x1e, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4f, 0x4e, 0x42,
	0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x49, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x4f,
	0x46, 0x10, 0xf4, 0x04, 0x12, 0x18, 0x0a, 0x13, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b,
	0x53, 0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xf5, 0x04, 0x12, 0x1d,
	0x0a, 0x18, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x50, 0x4e, 0x5f, 0x4f,
	0x4e, 0x42, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0xf6, 0x04, 0x12, 0x27, 0x0a,
	0x22, 0x50, 0x32, 0x50, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x55,
	0x52, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x49, 0x41,
	0x4c, 0x4f, 0x47, 0x10, 0xf7, 0x04, 0x12, 0x20, 0x0a, 0x1b, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49,
	0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x46, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x4e, 0x43, 0x59, 0x10, 0xf8, 0x04, 0x12, 0x1f, 0x0a, 0x1a, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43,
	0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0xf9, 0x04, 0x12, 0x20, 0x0a, 0x1b, 0x44, 0x45, 0x50,
	0x4f, 0x53, 0x49, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52,
	0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0xfa, 0x04, 0x12, 0x2a, 0x0a, 0x25, 0x44,
	0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x41, 0x55, 0x54, 0x4f, 0x5f, 0x53, 0x41, 0x56, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x10, 0xfb, 0x04, 0x12, 0x1f, 0x0a, 0x1a, 0x44, 0x45, 0x50, 0x4f, 0x53,
	0x49, 0x54, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0xfc, 0x04, 0x12, 0x1b, 0x0a, 0x16, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0xfd, 0x04, 0x12, 0x1d, 0x0a, 0x18, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54,
	0x5f, 0x45, 0x58, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f,
	0x47, 0x10, 0xfe, 0x04, 0x12, 0x28, 0x0a, 0x23, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f,
	0x45, 0x58, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x52,
	0x49, 0x53, 0x4f, 0x4e, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0xff, 0x04, 0x12, 0x1f,
	0x0a, 0x1a, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x4f, 0x49, 0x43, 0x45, 0x10, 0x80, 0x05, 0x12,
	0x14, 0x0a, 0x0f, 0x43, 0x43, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x81, 0x05, 0x12, 0x20, 0x0a, 0x1b, 0x43, 0x43, 0x5f, 0x41, 0x44, 0x44, 0x52,
	0x45, 0x53, 0x53, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x82, 0x05, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x43, 0x5f, 0x42, 0x49,
	0x4c, 0x4c, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x83, 0x05, 0x12, 0x26, 0x0a, 0x21, 0x43, 0x43, 0x5f, 0x4c, 0x49,
	0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f,
	0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x84, 0x05, 0x12,
	0x18, 0x0a, 0x13, 0x43, 0x43, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x85, 0x05, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x43, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x86, 0x05, 0x12, 0x26, 0x0a, 0x21, 0x43, 0x43, 0x5f, 0x57, 0x45,
	0x4c, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x4c, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x87, 0x05, 0x12,
	0x19, 0x0a, 0x14, 0x43, 0x43, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x42, 0x41, 0x43, 0x4b,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x88, 0x05, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x43,
	0x5f, 0x41, 0x43, 0x43, 0x45, 0x4c, 0x45, 0x52, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x57,
	0x41, 0x52, 0x44, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x89, 0x05, 0x12, 0x1f,
	0x0a, 0x1a, 0x43, 0x43, 0x5f, 0x57, 0x45, 0x4c, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x56, 0x4f, 0x55,
	0x43, 0x48, 0x45, 0x52, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x8a, 0x05, 0x12,
	0x2e, 0x0a, 0x29, 0x43, 0x43, 0x5f, 0x57, 0x45, 0x4c, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x8b, 0x05, 0x12,
	0x16, 0x0a, 0x11, 0x43, 0x43, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x8c, 0x05, 0x12, 0x10, 0x0a, 0x0b, 0x43, 0x43, 0x5f, 0x43, 0x4f,
	0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x53, 0x10, 0x8d, 0x05, 0x12, 0x1b, 0x0a, 0x16, 0x45, 0x53, 0x5f,
	0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x41, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x8e, 0x05, 0x12, 0x1b, 0x0a, 0x16, 0x45, 0x53, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x8f, 0x05, 0x12, 0x25, 0x0a, 0x20, 0x45, 0x53, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0x90, 0x05, 0x12, 0x1b, 0x0a, 0x16, 0x45, 0x53,
	0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x10, 0x91, 0x05, 0x12, 0x16, 0x0a, 0x11, 0x45, 0x53, 0x5f, 0x41, 0x55,
	0x54, 0x4f, 0x5f, 0x52, 0x45, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x92, 0x05, 0x12,
	0x1c, 0x0a, 0x17, 0x45, 0x53, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x4f,
	0x4e, 0x46, 0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x93, 0x05, 0x12, 0x16, 0x0a,
	0x11, 0x45, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x53, 0x49,
	0x47, 0x4e, 0x10, 0x94, 0x05, 0x12, 0x1b, 0x0a, 0x16, 0x45, 0x53, 0x5f, 0x45, 0x53, 0x49, 0x47,
	0x4e, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x95, 0x05, 0x12, 0x1a, 0x0a, 0x15, 0x45, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x96, 0x05, 0x12, 0x11,
	0x0a, 0x0c, 0x45, 0x53, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x10, 0x97,
	0x05, 0x12, 0x14, 0x0a, 0x0f, 0x45, 0x53, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x10, 0x98, 0x05, 0x12, 0x1f, 0x0a, 0x1a, 0x45, 0x53, 0x5f, 0x41, 0x50,
	0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x10, 0x99, 0x05, 0x12, 0x0f, 0x0a, 0x0a, 0x50, 0x4c, 0x5f, 0x4c,
	0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x9a, 0x05, 0x12, 0x15, 0x0a, 0x10, 0x50, 0x4c, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x9b, 0x05,
	0x12, 0x1b, 0x0a, 0x16, 0x50, 0x4c, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x9c, 0x05, 0x12, 0x1b, 0x0a,
	0x16, 0x50, 0x4c, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x10, 0x9d, 0x05, 0x12, 0x18, 0x0a, 0x13, 0x50, 0x4c,
	0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x9e, 0x05, 0x12, 0x16, 0x0a, 0x11, 0x50, 0x4c, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49,
	0x41, 0x54, 0x45, 0x5f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x10, 0x9f, 0x05, 0x12, 0x1b, 0x0a, 0x16,
	0x50, 0x4c, 0x5f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x44, 0x4f,
	0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0xa0, 0x05, 0x12, 0x28, 0x0a, 0x23, 0x50, 0x4c, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x49, 0x41, 0x5f, 0x4f, 0x54, 0x50,
	0x10, 0xa1, 0x05, 0x12, 0x1c, 0x0a, 0x17, 0x50, 0x4c, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53,
	0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xa2,
	0x05, 0x12, 0x1a, 0x0a, 0x15, 0x50, 0x4c, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0xa3, 0x05, 0x12, 0x16, 0x0a,
	0x11, 0x50, 0x4c, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xa4, 0x05, 0x12, 0x1a, 0x0a, 0x15, 0x50, 0x4c, 0x5f, 0x41, 0x50, 0x50, 0x4c,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xa5,
	0x05, 0x12, 0x16, 0x0a, 0x11, 0x50, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x41, 0x53,
	0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x10, 0xa6, 0x05, 0x12, 0x0f, 0x0a, 0x0a, 0x50, 0x4c, 0x5f,
	0x50, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x10, 0xa7, 0x05, 0x12, 0x17, 0x0a, 0x12, 0x50, 0x4c,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x10, 0xa8, 0x05, 0x12, 0x10, 0x0a, 0x0b, 0x50, 0x4c, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x54, 0x49,
	0x4d, 0x45, 0x10, 0xa9, 0x05, 0x12, 0x15, 0x0a, 0x10, 0x50, 0x4c, 0x5f, 0x45, 0x4e, 0x54, 0x45,
	0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0xaa, 0x05, 0x12, 0x14, 0x0a, 0x0f,
	0x50, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0xab, 0x05, 0x12, 0x16, 0x0a, 0x11, 0x50, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0xac, 0x05, 0x12, 0x1a, 0x0a, 0x15, 0x50, 0x4c,
	0x5f, 0x4f, 0x43, 0x43, 0x55, 0x50, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x53, 0x10, 0xad, 0x05, 0x12, 0x14, 0x0a, 0x0f, 0x50, 0x4c, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x10, 0xae, 0x05, 0x12, 0x1b, 0x0a, 0x16,
	0x50, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x43, 0x45, 0x49, 0x50, 0x54, 0x10, 0xaf, 0x05, 0x12, 0x18, 0x0a, 0x13, 0x50, 0x4c, 0x5f,
	0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53,
	0x10, 0xb0, 0x05, 0x12, 0x14, 0x0a, 0x0f, 0x43, 0x43, 0x5f, 0x55, 0x53, 0x41, 0x47, 0x45, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb1, 0x05, 0x12, 0x15, 0x0a, 0x10, 0x43, 0x43, 0x5f,
	0x4c, 0x49, 0x4d, 0x49, 0x54, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb2, 0x05,
	0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x43, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb3,
	0x05, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x43, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x50, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xb4, 0x05, 0x12, 0x27, 0x0a, 0x22, 0x43, 0x43, 0x5f, 0x42, 0x49, 0x4c, 0x4c,
	0x5f, 0x52, 0x45, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb5, 0x05, 0x12, 0x25,
	0x0a, 0x20, 0x43, 0x43, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x41, 0x4d, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x42, 0x49, 0x4c, 0x4c, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xb6, 0x05, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x43, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xb7, 0x05, 0x12, 0x23, 0x0a, 0x1e, 0x43, 0x43, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x53, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb8, 0x05, 0x12, 0x14, 0x0a, 0x0f, 0x43, 0x43, 0x5f,
	0x53, 0x45, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0xb9, 0x05, 0x12,
	0x25, 0x0a, 0x20, 0x43, 0x43, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xba, 0x05, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x43, 0x5f, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xbb, 0x05, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x43, 0x5f, 0x45, 0x58, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xbc, 0x05, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x43, 0x5f, 0x46, 0x52, 0x45,
	0x45, 0x5a, 0x45, 0x5f, 0x55, 0x4e, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xbd, 0x05, 0x12, 0x31, 0x0a, 0x2c, 0x43, 0x43, 0x5f, 0x4e, 0x45, 0x57,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x41, 0x44,
	0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xbe, 0x05, 0x12, 0x18, 0x0a, 0x13, 0x43, 0x43, 0x5f,
	0x4e, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x10, 0xbf, 0x05, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x43, 0x5f, 0x45, 0x4d, 0x49, 0x5f, 0x44, 0x41,
	0x53, 0x48, 0x5f, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xc0, 0x05, 0x12, 0x28, 0x0a, 0x23, 0x43, 0x43, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x41, 0x4c,
	0x4c, 0x5f, 0x45, 0x4d, 0x49, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc1, 0x05, 0x12, 0x2a, 0x0a, 0x25,
	0x43, 0x43, 0x5f, 0x45, 0x4d, 0x49, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc2, 0x05, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x43, 0x5f, 0x45,
	0x4d, 0x49, 0x5f, 0x50, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x43,
	0x4c, 0x4f, 0x53, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xc3, 0x05, 0x12, 0x27, 0x0a, 0x22, 0x43, 0x43, 0x5f, 0x45, 0x4d, 0x49, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49,
	0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc4, 0x05, 0x12, 0x21, 0x0a, 0x1c,
	0x43, 0x43, 0x5f, 0x45, 0x4d, 0x49, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45,
	0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc5, 0x05, 0x12,
	0x1e, 0x0a, 0x19, 0x43, 0x43, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x4f, 0x50,
	0x45, 0x4e, 0x5f, 0x46, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc6, 0x05, 0x12,
	0x21, 0x0a, 0x1c, 0x43, 0x43, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x46, 0x44,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xc7, 0x05, 0x12, 0x30, 0x0a, 0x2b, 0x43, 0x43, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x48, 0x45, 0x43, 0x4b, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xc8, 0x05, 0x12, 0x1c, 0x0a, 0x17, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xc9, 0x05, 0x12, 0x15, 0x0a, 0x10, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x41, 0x42, 0x53, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xca, 0x05, 0x12, 0x2d, 0x0a, 0x28, 0x43, 0x43, 0x5f,
	0x42, 0x49, 0x4c, 0x4c, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcb, 0x05, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x43, 0x5f, 0x45,
	0x44, 0x49, 0x54, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xcc, 0x05, 0x12, 0x34, 0x0a, 0x2f, 0x43, 0x43, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x45, 0x44, 0x5f, 0x54, 0x45, 0x4e, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcd, 0x05, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x43,
	0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x46, 0x44, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xce, 0x05, 0x12, 0x18, 0x0a, 0x13, 0x44, 0x45,
	0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0xcf, 0x05, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x43, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd0,
	0x05, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x43, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd1, 0x05, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x43,
	0x5f, 0x43, 0x55, 0x47, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xd2, 0x05, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x43, 0x5f, 0x45, 0x4d, 0x49,
	0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xd3, 0x05, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x43, 0x5f, 0x53, 0x55, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd4, 0x05, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x43,
	0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xd5, 0x05, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x43, 0x5f, 0x4c, 0x4f, 0x55, 0x4e, 0x47, 0x45,
	0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd6,
	0x05, 0x12, 0x12, 0x0a, 0x0d, 0x43, 0x43, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xd7, 0x05, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x43, 0x5f, 0x51, 0x52, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xd8, 0x05, 0x12, 0x2f, 0x0a, 0x2a, 0x43, 0x43, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xd9, 0x05, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x43, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xda, 0x05, 0x12, 0x20, 0x0a, 0x1b, 0x43,
	0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xdb, 0x05, 0x12, 0x1a, 0x0a,
	0x15, 0x43, 0x43, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xdc, 0x05, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x43, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x53, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xdd, 0x05, 0x12, 0x20, 0x0a, 0x1b, 0x43, 0x43, 0x5f,
	0x54, 0x48, 0x52, 0x45, 0x45, 0x5f, 0x54, 0x4f, 0x50, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44,
	0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xde, 0x05, 0x12, 0x2b, 0x0a, 0x26, 0x43,
	0x43, 0x5f, 0x45, 0x58, 0x54, 0x52, 0x41, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xdf, 0x05, 0x12, 0x18, 0x0a, 0x13, 0x43, 0x43, 0x5f, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xe0, 0x05, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x43, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x49, 0x43,
	0x5f, 0x48, 0x41, 0x4c, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe1, 0x05, 0x12,
	0x18, 0x0a, 0x13, 0x43, 0x43, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x5f, 0x4d, 0x4f, 0x52, 0x45, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe2, 0x05, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x43, 0x5f,
	0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59,
	0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe3, 0x05, 0x12,
	0x25, 0x0a, 0x20, 0x43, 0x43, 0x5f, 0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e, 0x54, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xe4, 0x05, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x43, 0x5f, 0x52, 0x45, 0x57,
	0x41, 0x52, 0x44, 0x53, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xe5, 0x05, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x43, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54,
	0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xe6, 0x05, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x43, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54,
	0x45, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xe7, 0x05, 0x12, 0x20, 0x0a, 0x1b, 0x43, 0x43, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45,
	0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xe8, 0x05, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x43, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55,
	0x54, 0x45, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xe9, 0x05, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xea, 0x05,
	0x12, 0x1e, 0x0a, 0x19, 0x43, 0x43, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xeb, 0x05,
	0x12, 0x27, 0x0a, 0x22, 0x43, 0x43, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x5f, 0x53,
	0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xec, 0x05, 0x12, 0x19, 0x0a, 0x14, 0x43, 0x43, 0x5f,
	0x57, 0x41, 0x49, 0x54, 0x4c, 0x49, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xed, 0x05, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x43, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x4c,
	0x49, 0x53, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xee, 0x05, 0x12, 0x20, 0x0a,
	0x1b, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xef, 0x05, 0x12,
	0x33, 0x0a, 0x2e, 0x4e, 0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x44, 0x45, 0x50,
	0x4f, 0x53, 0x49, 0x54, 0x53, 0x5f, 0x43, 0x41, 0x5f, 0x4e, 0x4f, 0x5f, 0x41, 0x43, 0x43, 0x5f,
	0x44, 0x49, 0x53, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xf0, 0x05, 0x12, 0x1e, 0x0a, 0x19, 0x4e, 0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54,
	0x48, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xf1, 0x05, 0x12, 0x18, 0x0a, 0x13, 0x42, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x4f, 0x4e,
	0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf2, 0x05, 0x12, 0x1a,
	0x0a, 0x15, 0x42, 0x4b, 0x59, 0x43, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x53, 0x48, 0x41, 0x4b, 0x45,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf3, 0x05, 0x12, 0x1d, 0x0a, 0x18, 0x42, 0x4b,
	0x59, 0x43, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf4, 0x05, 0x12, 0x17, 0x0a, 0x12, 0x42, 0x4b, 0x59,
	0x43, 0x5f, 0x43, 0x48, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xf5, 0x05, 0x12, 0x19, 0x0a, 0x14, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf6, 0x05, 0x12, 0x1a, 0x0a,
	0x15, 0x50, 0x4c, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x47, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf7, 0x05, 0x12, 0x16, 0x0a, 0x11, 0x50, 0x4c, 0x5f,
	0x50, 0x41, 0x4e, 0x5f, 0x44, 0x4f, 0x42, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf8,
	0x05, 0x12, 0x1e, 0x0a, 0x19, 0x50, 0x4c, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf9,
	0x05, 0x12, 0x2a, 0x0a, 0x25, 0x50, 0x4c, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52,
	0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x43, 0x4f, 0x4e, 0x53,
	0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfa, 0x05, 0x12, 0x27, 0x0a,
	0x22, 0x50, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xfb, 0x05, 0x12, 0x29, 0x0a, 0x24, 0x50, 0x4c, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfc,
	0x05, 0x12, 0x28, 0x0a, 0x23, 0x50, 0x4c, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x45, 0x4c,
	0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfd, 0x05, 0x12, 0x2a, 0x0a, 0x25, 0x50,
	0x4c, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xfe, 0x05, 0x12, 0x2d, 0x0a, 0x28, 0x50, 0x4c, 0x5f, 0x45, 0x4c,
	0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x4e, 0x4f, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xff, 0x05, 0x12, 0x28, 0x0a, 0x23, 0x50, 0x4c, 0x5f, 0x4e, 0x4f, 0x4e,
	0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c,
	0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x80, 0x06,
	0x12, 0x18, 0x0a, 0x13, 0x43, 0x43, 0x5f, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x4f, 0x4e, 0x54, 0x41,
	0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x10, 0x81, 0x06, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x43,
	0x5f, 0x41, 0x4d, 0x50, 0x4c, 0x49, 0x5f, 0x46, 0x49, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x82, 0x06, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x43, 0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49, 0x47,
	0x49, 0x42, 0x4c, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x83, 0x06, 0x12, 0x25, 0x0a, 0x20, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x10, 0x84, 0x06, 0x12, 0x1f, 0x0a, 0x1a, 0x52, 0x45,
	0x53, 0x45, 0x54, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x5f, 0x52, 0x50, 0x43, 0x10, 0x85, 0x06, 0x12, 0x1e, 0x0a, 0x19, 0x4e,
	0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x86, 0x06, 0x12, 0x1a, 0x0a, 0x15, 0x52,
	0x50, 0x43, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x44, 0x49, 0x52, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x87, 0x06, 0x12, 0x22, 0x0a, 0x1d, 0x50, 0x4c, 0x5f, 0x41, 0x43,
	0x51, 0x5f, 0x54, 0x4f, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x88, 0x06, 0x12, 0x1e, 0x0a, 0x19, 0x50,
	0x4c, 0x5f, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54,
	0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x89, 0x06, 0x12, 0x16, 0x0a, 0x11, 0x4c,
	0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x8a, 0x06, 0x12, 0x26, 0x0a, 0x21, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x55, 0x4e,
	0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x42, 0x4f, 0x54,
	0x54, 0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x8b, 0x06, 0x12, 0x22, 0x0a, 0x1d, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x44, 0x45, 0x45, 0x4d, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x8c, 0x06, 0x12,
	0x29, 0x0a, 0x24, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x56, 0x49, 0x53, 0x54, 0x41, 0x52, 0x41,
	0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54,
	0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x8d, 0x06, 0x12, 0x2c, 0x0a, 0x27, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x56, 0x49, 0x53, 0x54, 0x41, 0x52, 0x41, 0x5f, 0x45, 0x4e, 0x54, 0x45,
	0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d,
	0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x8e, 0x06, 0x12, 0x20, 0x0a, 0x1b, 0x4f, 0x46, 0x46, 0x45,
	0x52, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53,
	0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0x8f, 0x06, 0x12, 0x1d, 0x0a, 0x18, 0x53, 0x41,
	0x4c, 0x41, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x4c, 0x43, 0x55, 0x4c, 0x41, 0x54, 0x4f, 0x52, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x90, 0x06, 0x12, 0x21, 0x0a, 0x1c, 0x45, 0x53, 0x5f,
	0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f,
	0x4e, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x91, 0x06, 0x12, 0x23, 0x0a, 0x1e,
	0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f,
	0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x92,
	0x06, 0x12, 0x1e, 0x0a, 0x19, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x4c, 0x49,
	0x43, 0x59, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x93,
	0x06, 0x12, 0x2c, 0x0a, 0x27, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x4e, 0x41, 0x43,
	0x48, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x5f, 0x4d, 0x41, 0x44, 0x41, 0x54, 0x45, 0x5f,
	0x53, 0x45, 0x54, 0x55, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x94, 0x06, 0x12,
	0x26, 0x0a, 0x21, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f,
	0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x95, 0x06, 0x12, 0x1e, 0x0a, 0x19, 0x53, 0x41, 0x4c, 0x41, 0x52,
	0x59, 0x5f, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0x96, 0x06, 0x12, 0x1e, 0x0a, 0x19, 0x53, 0x41, 0x4c, 0x41, 0x52,
	0x59, 0x5f, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x97, 0x06, 0x12, 0x25, 0x0a, 0x20, 0x53, 0x41, 0x4c, 0x41, 0x52,
	0x59, 0x5f, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x55, 0x50, 0x47,
	0x52, 0x41, 0x44, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x98, 0x06, 0x12, 0x27,
	0x0a, 0x22, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x45, 0x51,
	0x55, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x99, 0x06, 0x12, 0x26, 0x0a, 0x21, 0x53, 0x41, 0x4c, 0x41, 0x52,
	0x59, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x9a, 0x06, 0x12,
	0x22, 0x0a, 0x1d, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x9b, 0x06, 0x12, 0x20, 0x0a, 0x1b, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x55, 0x4e,
	0x4f, 0x50, 0x45, 0x4e, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x9c, 0x06, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x58, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x9d, 0x06, 0x12, 0x1d, 0x0a, 0x18, 0x44, 0x43, 0x5f, 0x50, 0x48, 0x59, 0x53,
	0x49, 0x43, 0x41, 0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x9e, 0x06, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x58, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x9f, 0x06, 0x12, 0x1b, 0x0a, 0x16, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x44,
	0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xa0, 0x06, 0x12, 0x1a, 0x0a, 0x15, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4f, 0x56, 0x45, 0x52,
	0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa1, 0x06, 0x12, 0x19,
	0x0a, 0x14, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa2, 0x06, 0x12, 0x21, 0x0a, 0x1c, 0x4c, 0x4f, 0x41,
	0x4e, 0x53, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49,
	0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa3, 0x06, 0x12, 0x21, 0x0a, 0x1c,
	0x50, 0x4c, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x4c, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa4, 0x06, 0x12,
	0x22, 0x0a, 0x1d, 0x50, 0x4c, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50,
	0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xa5, 0x06, 0x12, 0x1b, 0x0a, 0x16, 0x48, 0x45, 0x4c, 0x50, 0x5f, 0x4c, 0x41, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0xa6, 0x06,
	0x12, 0x2b, 0x0a, 0x26, 0x4d, 0x46, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x53, 0x5f,
	0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f,
	0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa7, 0x06, 0x12, 0x2d, 0x0a,
	0x28, 0x4d, 0x46, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x49, 0x4d, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x53, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa8, 0x06, 0x12, 0x32, 0x0a, 0x2d,
	0x4d, 0x46, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x49, 0x4d, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x4c,
	0x4f, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa9, 0x06,
	0x12, 0x36, 0x0a, 0x31, 0x4d, 0x46, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x53, 0x5f,
	0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d,
	0x42, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xaa, 0x06, 0x12, 0x2f, 0x0a, 0x2a, 0x4d, 0x46, 0x5f, 0x48,
	0x4f, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x45,
	0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xab, 0x06, 0x12, 0x29, 0x0a, 0x24, 0x4d, 0x46, 0x5f,
	0x48, 0x4f, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f,
	0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56,
	0x32, 0x10, 0xac, 0x06, 0x12, 0x27, 0x0a, 0x22, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x5f,
	0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x49, 0x4e,
	0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xad, 0x06, 0x12, 0x10, 0x0a,
	0x0b, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0xae, 0x06, 0x12,
	0x24, 0x0a, 0x1f, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x57, 0x41, 0x4c,
	0x4c, 0x45, 0x54, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xaf, 0x06, 0x12, 0x26, 0x0a, 0x21, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x53, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55,
	0x4e, 0x44, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb0, 0x06, 0x12, 0x25, 0x0a,
	0x20, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45,
	0x54, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xb1, 0x06, 0x12, 0x18, 0x0a, 0x13, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4c, 0x41, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb2, 0x06, 0x12, 0x18,
	0x0a, 0x13, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4a, 0x4f, 0x55, 0x52, 0x4e, 0x45, 0x59, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb3, 0x06, 0x12, 0x2f, 0x0a, 0x2a, 0x4c, 0x41, 0x4d, 0x46,
	0x5f, 0x41, 0x44, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb4, 0x06, 0x12, 0x36, 0x0a, 0x31, 0x4c, 0x41, 0x4d,
	0x46, 0x5f, 0x43, 0x41, 0x4d, 0x53, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f,
	0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb5,
	0x06, 0x12, 0x37, 0x0a, 0x32, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4b, 0x41, 0x52, 0x56, 0x59, 0x5f,
	0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f,
	0x4f, 0x54, 0x50, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb6, 0x06, 0x12, 0x1b, 0x0a, 0x16, 0x4c, 0x41,
	0x4d, 0x46, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xb7, 0x06, 0x12, 0x20, 0x0a, 0x1b, 0x4c, 0x41, 0x4d, 0x46, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb8, 0x06, 0x12, 0x23, 0x0a, 0x1e, 0x4c, 0x41, 0x4d,
	0x46, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb9, 0x06, 0x12, 0x3a,
	0x0a, 0x35, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xba, 0x06, 0x12, 0x14, 0x0a, 0x0f, 0x4c, 0x41,
	0x4d, 0x46, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xbb, 0x06,
	0x12, 0x16, 0x0a, 0x11, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xbc, 0x06, 0x12, 0x18, 0x0a, 0x13, 0x4c, 0x41, 0x4d, 0x46,
	0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xbd, 0x06, 0x12, 0x30, 0x0a, 0x2b, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x43, 0x41, 0x4d, 0x53, 0x5f,
	0x4c, 0x49, 0x45, 0x4e, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xbe, 0x06, 0x12, 0x31, 0x0a, 0x2c, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4b, 0x41, 0x52,
	0x56, 0x59, 0x5f, 0x4c, 0x49, 0x45, 0x4e, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x4f, 0x54, 0x50,
	0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xbf, 0x06, 0x12, 0x25, 0x0a, 0x20, 0x4c, 0x41, 0x4d, 0x46, 0x5f,
	0x4c, 0x49, 0x45, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc0, 0x06, 0x12, 0x26,
	0x0a, 0x21, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xc1, 0x06, 0x12, 0x1e, 0x0a, 0x19, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4d,
	0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xc2, 0x06, 0x12, 0x24, 0x0a, 0x1f, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x41,
	0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc3, 0x06, 0x12, 0x23, 0x0a, 0x1e,
	0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc4,
	0x06, 0x12, 0x29, 0x0a, 0x24, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x46, 0x46,
	0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc5, 0x06, 0x12, 0x28, 0x0a, 0x23,
	0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x4c, 0x49, 0x45,
	0x4e, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xc6, 0x06, 0x12, 0x27, 0x0a, 0x22, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x50,
	0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc7, 0x06, 0x12,
	0x18, 0x0a, 0x13, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc8, 0x06, 0x12, 0x1a, 0x0a, 0x15, 0x4c, 0x41, 0x4d,
	0x46, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xc9, 0x06, 0x12, 0x1e, 0x0a, 0x19, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xca, 0x06, 0x12, 0x20, 0x0a, 0x1b, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xcb, 0x06, 0x12, 0x1d, 0x0a, 0x18, 0x4c, 0x41, 0x4d, 0x46, 0x5f,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xcc, 0x06, 0x12, 0x22, 0x0a, 0x1d, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x49,
	0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x49, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcd, 0x06, 0x12, 0x18, 0x0a, 0x13, 0x4c, 0x41,
	0x4d, 0x46, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xce, 0x06, 0x12, 0x21, 0x0a, 0x1c, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x46, 0x55, 0x4e,
	0x44, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xcf, 0x06, 0x12, 0x27, 0x0a, 0x22, 0x4c, 0x41, 0x4d, 0x46, 0x5f,
	0x4e, 0x4f, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd0, 0x06,
	0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x43, 0x5f, 0x4c, 0x4f, 0x55, 0x4e, 0x47, 0x45, 0x5f, 0x41, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x5f, 0x56, 0x32, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd1,
	0x06, 0x12, 0x26, 0x0a, 0x21, 0x43, 0x43, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x5f, 0x4c, 0x4f, 0x55, 0x4e, 0x47, 0x45, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x45, 0x53, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd2, 0x06, 0x12, 0x18, 0x0a, 0x13, 0x4c, 0x4f, 0x41,
	0x4e, 0x53, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x49, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xd3, 0x06, 0x12, 0x1e, 0x0a, 0x19, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x46, 0x4f, 0x52,
	0x4d, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xd4, 0x06, 0x12, 0x18, 0x0a, 0x13, 0x50, 0x4c, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x56, 0x32, 0x10, 0xd5, 0x06, 0x12, 0x1e, 0x0a,
	0x19, 0x50, 0x4c, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x56, 0x32, 0x10, 0xd6, 0x06, 0x12, 0x2b, 0x0a,
	0x26, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd7, 0x06, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x41,
	0x5f, 0x46, 0x49, 0x5f, 0x54, 0x4f, 0x5f, 0x46, 0x49, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd8, 0x06, 0x12, 0x20, 0x0a, 0x1b, 0x43,
	0x41, 0x5f, 0x46, 0x49, 0x5f, 0x54, 0x4f, 0x5f, 0x46, 0x49, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49,
	0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd9, 0x06, 0x12, 0x2c, 0x0a,
	0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c,
	0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xda, 0x06, 0x12, 0x33, 0x0a, 0x2e, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x42, 0x49, 0x4c, 0x4c, 0x49,
	0x4e, 0x47, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f,
	0x4d, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xdb, 0x06,
	0x12, 0x36, 0x0a, 0x31, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x59, 0x5f, 0x50,
	0x52, 0x4f, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xdc, 0x06, 0x12, 0x2f, 0x0a, 0x2a, 0x55, 0x53, 0x5f, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xdd, 0x06, 0x12, 0x33, 0x0a, 0x2e, 0x55, 0x53, 0x5f,
	0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x4c,
	0x45, 0x56, 0x45, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xde, 0x06, 0x12, 0x30,
	0x0a, 0x2b, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4f, 0x4e, 0x42, 0x4f,
	0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x44, 0x49, 0x53, 0x43,
	0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xdf, 0x06,
	0x12, 0x21, 0x0a, 0x1c, 0x43, 0x43, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49,
	0x42, 0x4c, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xe0, 0x06, 0x12, 0x20, 0x0a, 0x1b, 0x43, 0x43, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52,
	0x4b, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xe1, 0x06, 0x12, 0x1d, 0x0a, 0x18, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x50,
	0x57, 0x41, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xe2, 0x06, 0x12, 0x1b, 0x0a, 0x16, 0x50, 0x57, 0x41, 0x5f, 0x52, 0x45, 0x44, 0x49,
	0x52, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe3,
	0x06, 0x12, 0x1e, 0x0a, 0x19, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f,
	0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe4,
	0x06, 0x12, 0x1a, 0x0a, 0x15, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe5, 0x06, 0x12, 0x22, 0x0a,
	0x1d, 0x50, 0x4c, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x41, 0x4e,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x56, 0x32, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe6,
	0x06, 0x12, 0x21, 0x0a, 0x1c, 0x50, 0x4c, 0x5f, 0x41, 0x4c, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54,
	0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xe7, 0x06, 0x12, 0x1c, 0x0a, 0x17, 0x50, 0x4c, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xe8, 0x06, 0x12, 0x1e, 0x0a, 0x19, 0x53, 0x41, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45,
	0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xe9, 0x06, 0x12, 0x1f, 0x0a, 0x1a, 0x53, 0x41, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45,
	0x5f, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xea, 0x06, 0x12, 0x1f, 0x0a, 0x1a, 0x53, 0x41, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52,
	0x45, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xeb, 0x06, 0x12, 0x19, 0x0a, 0x14, 0x50, 0x41, 0x4e, 0x5f, 0x44, 0x4f, 0x42, 0x5f,
	0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xec, 0x06, 0x12,
	0x32, 0x0a, 0x2d, 0x53, 0x41, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x55,
	0x42, 0x4d, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x57, 0x49,
	0x50, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xed, 0x06, 0x12, 0x21, 0x0a, 0x1c, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xee, 0x06, 0x12, 0x24, 0x0a, 0x1f, 0x53, 0x41, 0x5f, 0x43, 0x4c, 0x4f,
	0x53, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x4c, 0x56, 0x45, 0x5f, 0x49, 0x53, 0x53,
	0x55, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xef, 0x06, 0x12, 0x28, 0x0a, 0x23,
	0x53, 0x41, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x44, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xf0, 0x06, 0x12, 0x27, 0x0a, 0x22, 0x4d, 0x46, 0x5f, 0x48, 0x4f, 0x4c,
	0x44, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf1, 0x06, 0x12,
	0x1e, 0x0a, 0x19, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x44, 0x5f, 0x4c,
	0x4f, 0x41, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf2, 0x06, 0x12,
	0x30, 0x0a, 0x2b, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x44,
	0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x5f,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf3,
	0x06, 0x12, 0x14, 0x0a, 0x0f, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4b, 0x46, 0x53, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xf4, 0x06, 0x12, 0x1d, 0x0a, 0x18, 0x4c, 0x4f, 0x41, 0x4e, 0x53,
	0x5f, 0x57, 0x45, 0x42, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x44, 0x49, 0x47, 0x49, 0x4c, 0x4f, 0x43,
	0x4b, 0x45, 0x52, 0x10, 0xf5, 0x06, 0x12, 0x2a, 0x0a, 0x25, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x46,
	0x55, 0x4e, 0x44, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xf6, 0x06, 0x12, 0x2a, 0x0a, 0x25, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x59, 0x5f, 0x4d, 0x46, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf7, 0x06, 0x12, 0x29,
	0x0a, 0x24, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf8, 0x06, 0x12, 0x20, 0x0a, 0x1b, 0x44, 0x43, 0x5f,
	0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54,
	0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf9, 0x06, 0x12, 0x20, 0x0a, 0x1b, 0x44,
	0x43, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfa, 0x06, 0x12, 0x1c, 0x0a,
	0x17, 0x44, 0x43, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfb, 0x06, 0x12, 0x1e, 0x0a, 0x19, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfc, 0x06, 0x12, 0x24, 0x0a, 0x1f, 0x4e,
	0x45, 0x57, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfd,
	0x06, 0x12, 0x24, 0x0a, 0x1f, 0x44, 0x43, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x54,
	0x4d, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xfe, 0x06, 0x12, 0x18, 0x0a, 0x13, 0x43, 0x43, 0x5f, 0x53, 0x59,
	0x4e, 0x43, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xff,
	0x06, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x48, 0x41, 0x4c, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x80, 0x07, 0x12, 0x1c,
	0x0a, 0x17, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0x81, 0x07, 0x12, 0x21, 0x0a, 0x1c,
	0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4c,
	0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x82, 0x07, 0x12,
	0x1f, 0x0a, 0x1a, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0x83, 0x07,
	0x12, 0x22, 0x0a, 0x1d, 0x52, 0x45, 0x53, 0x45, 0x54, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x84, 0x07, 0x12, 0x1f, 0x0a, 0x1a, 0x50, 0x41, 0x59, 0x5f, 0x54, 0x45, 0x52, 0x4d,
	0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x85, 0x07, 0x12, 0x2b, 0x0a, 0x26, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49,
	0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0x86, 0x07, 0x12, 0x26, 0x0a, 0x21, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x57, 0x45, 0x42, 0x5f, 0x56, 0x49, 0x45, 0x57,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x87, 0x07, 0x12, 0x1b, 0x0a, 0x16, 0x4e, 0x50,
	0x43, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x5f, 0x56, 0x32, 0x10, 0x88, 0x07, 0x12, 0x24, 0x0a, 0x1f, 0x56, 0x50, 0x41, 0x5f, 0x4d,
	0x49, 0x47, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x31, 0x10, 0x89, 0x07, 0x12, 0x1f, 0x0a,
	0x1a, 0x54, 0x50, 0x41, 0x50, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x8a, 0x07, 0x12, 0x22,
	0x0a, 0x1d, 0x54, 0x50, 0x41, 0x50, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53,
	0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0x8b, 0x07, 0x12, 0x1e, 0x0a, 0x19, 0x54, 0x50, 0x41, 0x50, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0x8c, 0x07, 0x12, 0x20, 0x0a, 0x1b, 0x54, 0x50, 0x41, 0x50, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x49,
	0x4e, 0x47, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x8d, 0x07, 0x12, 0x1f, 0x0a, 0x1a, 0x56, 0x50, 0x41, 0x5f, 0x4d, 0x49, 0x47, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x8e, 0x07, 0x12, 0x24, 0x0a, 0x1f, 0x56, 0x50, 0x41, 0x5f, 0x4d, 0x49, 0x47,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0x8f, 0x07, 0x12, 0x28, 0x0a, 0x23, 0x55,
	0x50, 0x49, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x90, 0x07, 0x12, 0x28, 0x0a, 0x23, 0x55, 0x50, 0x49, 0x5f, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x41,
	0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x91, 0x07, 0x12,
	0x14, 0x0a, 0x0f, 0x55, 0x50, 0x49, 0x5f, 0x54, 0x49, 0x50, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x92, 0x07, 0x12, 0x1c, 0x0a, 0x17, 0x55, 0x50, 0x49, 0x5f, 0x4d, 0x41, 0x50,
	0x50, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x93, 0x07, 0x12, 0x1e, 0x0a, 0x19, 0x55, 0x50, 0x49, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x45,
	0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x94, 0x07, 0x12, 0x1e, 0x0a, 0x19, 0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f,
	0x41, 0x44, 0x44, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x95, 0x07, 0x12, 0x1a, 0x0a, 0x15, 0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f,
	0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x96, 0x07, 0x12,
	0x1d, 0x0a, 0x18, 0x50, 0x41, 0x59, 0x5f, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x49, 0x5f, 0x53, 0x45,
	0x41, 0x52, 0x43, 0x48, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x97, 0x07, 0x12, 0x2a,
	0x0a, 0x25, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x43, 0x4f, 0x49, 0x4e, 0x53,
	0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54,
	0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x98, 0x07, 0x12, 0x2d, 0x0a, 0x28, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x5f, 0x43, 0x4f, 0x49, 0x4e, 0x53, 0x5f, 0x45, 0x4e, 0x54,
	0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f,
	0x4d, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x99, 0x07, 0x12, 0x2f, 0x0a, 0x2a, 0x53, 0x41, 0x4c,
	0x41, 0x52, 0x59, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x45, 0x52, 0x5f, 0x44, 0x4f, 0x43,
	0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54,
	0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x9a, 0x07, 0x12, 0x27, 0x0a, 0x22, 0x53, 0x41,
	0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x45, 0x52, 0x5f, 0x53, 0x45,
	0x41, 0x52, 0x43, 0x48, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45, 0x54,
	0x10, 0x9b, 0x07, 0x12, 0x2d, 0x0a, 0x28, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x4d,
	0x50, 0x4c, 0x4f, 0x59, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10,
	0x9c, 0x07, 0x12, 0x34, 0x0a, 0x2f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x4d, 0x50,
	0x4c, 0x4f, 0x59, 0x45, 0x52, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d,
	0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x9d, 0x07, 0x12, 0x21, 0x0a, 0x1c, 0x53, 0x41, 0x4c, 0x41,
	0x52, 0x59, 0x5f, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54,
	0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x9e, 0x07, 0x12, 0x28, 0x0a, 0x23, 0x4c,
	0x41, 0x4d, 0x46, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45,
	0x45, 0x54, 0x10, 0x9f, 0x07, 0x12, 0x1e, 0x0a, 0x19, 0x41, 0x41, 0x5f, 0x43, 0x4f, 0x4e, 0x53,
	0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x41, 0x4c, 0x5f, 0x44, 0x49, 0x41, 0x4c,
	0x4f, 0x47, 0x10, 0xa0, 0x07, 0x12, 0x22, 0x0a, 0x1d, 0x41, 0x41, 0x5f, 0x43, 0x4f, 0x4e, 0x53,
	0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x41, 0x4c, 0x5f, 0x53, 0x44, 0x4b, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa1, 0x07, 0x12, 0x12, 0x0a, 0x0d, 0x41, 0x41, 0x5f,
	0x53, 0x44, 0x4b, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa2, 0x07, 0x12, 0x1b, 0x0a,
	0x16, 0x41, 0x41, 0x5f, 0x46, 0x49, 0x5f, 0x54, 0x4f, 0x5f, 0x46, 0x49, 0x5f, 0x53, 0x44, 0x4b,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa3, 0x07, 0x12, 0x16, 0x0a, 0x11, 0x41, 0x41,
	0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xa4, 0x07, 0x12, 0x17, 0x0a, 0x12, 0x41, 0x41, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41,
	0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa5, 0x07, 0x12, 0x15, 0x0a, 0x10, 0x41,
	0x41, 0x5f, 0x52, 0x45, 0x4f, 0x4f, 0x42, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xa6, 0x07, 0x12, 0x1f, 0x0a, 0x1a, 0x41, 0x41, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x4f, 0x4f, 0x4e,
	0x10, 0xa7, 0x07, 0x12, 0x1e, 0x0a, 0x19, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x52,
	0x45, 0x50, 0x4f, 0x52, 0x54, 0x53, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x53,
	0x10, 0xa8, 0x07, 0x12, 0x1d, 0x0a, 0x18, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53,
	0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x53, 0x10,
	0xa9, 0x07, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x48, 0x45, 0x51, 0x55, 0x45, 0x5f, 0x42, 0x4f, 0x4f,
	0x4b, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0xaa, 0x07, 0x12, 0x17, 0x0a, 0x12, 0x4c, 0x45, 0x47, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x53,
	0x49, 0x47, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0xab, 0x07, 0x12, 0x15, 0x0a, 0x10, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x45, 0x51, 0x55, 0x45, 0x42, 0x4f, 0x4f, 0x4b, 0x10,
	0xac, 0x07, 0x12, 0x18, 0x0a, 0x13, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x53, 0x49,
	0x47, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x50, 0x54, 0x10, 0xad, 0x07, 0x12, 0x1f, 0x0a, 0x1a,
	0x43, 0x48, 0x45, 0x51, 0x55, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x50, 0x4f, 0x4c, 0x4c,
	0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xae, 0x07, 0x12, 0x1b, 0x0a,
	0x16, 0x41, 0x4c, 0x46, 0x52, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x10, 0xaf, 0x07, 0x12, 0x1d, 0x0a, 0x18, 0x45, 0x58,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4c, 0x4f, 0x47, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb0, 0x07, 0x12, 0x21, 0x0a, 0x1c, 0x50, 0x52, 0x4f,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x4c, 0x49, 0x43, 0x45, 0x4e, 0x53, 0x45, 0x53, 0x10, 0xb1, 0x07, 0x12, 0x22, 0x0a, 0x1d,
	0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x53,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xb2, 0x07,
	0x12, 0x15, 0x0a, 0x10, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52,
	0x5f, 0x56, 0x50, 0x41, 0x10, 0xb3, 0x07, 0x12, 0x2a, 0x0a, 0x25, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54,
	0x10, 0xb4, 0x07, 0x12, 0x22, 0x0a, 0x1d, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x50,
	0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f,
	0x45, 0x44, 0x49, 0x54, 0x10, 0xb5, 0x07, 0x12, 0x1e, 0x0a, 0x19, 0x50, 0x52, 0x4f, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xb6, 0x07, 0x12, 0x1f, 0x0a, 0x1a, 0x50, 0x52, 0x4f, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x5f, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x4f,
	0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xb7, 0x07, 0x12, 0x1c, 0x0a, 0x17, 0x49, 0x4e, 0x46, 0x4f,
	0x5f, 0x41, 0x43, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x56, 0x32, 0x10, 0xb8, 0x07, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x43, 0x5f, 0x43, 0x52, 0x45,
	0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45,
	0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb9, 0x07, 0x12, 0x1c, 0x0a, 0x17,
	0x43, 0x43, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f,
	0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xba, 0x07, 0x12, 0x18, 0x0a, 0x13, 0x4c, 0x4f,
	0x41, 0x4e, 0x53, 0x5f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x57, 0x45, 0x42, 0x56, 0x49, 0x45,
	0x57, 0x10, 0xbb, 0x07, 0x12, 0x1c, 0x0a, 0x17, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4d, 0x41,
	0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x49, 0x47, 0x49, 0x4f, 0x5f, 0x53, 0x44, 0x4b, 0x10,
	0xbc, 0x07, 0x12, 0x10, 0x0a, 0x0b, 0x41, 0x44, 0x44, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53,
	0x53, 0x10, 0xbd, 0x07, 0x12, 0x11, 0x0a, 0x0c, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x4f, 0x4e,
	0x53, 0x45, 0x4e, 0x54, 0x10, 0xbe, 0x07, 0x12, 0x16, 0x0a, 0x11, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x44, 0x4f, 0x42, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0xbf, 0x07, 0x12,
	0x16, 0x0a, 0x11, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0xc0, 0x07, 0x12, 0x0d, 0x0a, 0x08, 0x4c, 0x49, 0x56, 0x45, 0x4e,
	0x45, 0x53, 0x53, 0x10, 0xc1, 0x07, 0x12, 0x15, 0x0a, 0x10, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45,
	0x53, 0x53, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0xc2, 0x07, 0x12, 0x12, 0x0a,
	0x0d, 0x4f, 0x4e, 0x42, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0xc3,
	0x07, 0x12, 0x12, 0x0a, 0x0d, 0x4f, 0x4e, 0x42, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x4d, 0x4f, 0x4e,
	0x45, 0x59, 0x10, 0xc4, 0x07, 0x12, 0x22, 0x0a, 0x1d, 0x4f, 0x4e, 0x42, 0x5f, 0x41, 0x44, 0x44,
	0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0xc5, 0x07, 0x12, 0x10, 0x0a, 0x0b, 0x46, 0x49, 0x5f,
	0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x10, 0xc6, 0x07, 0x12, 0x19, 0x0a, 0x14, 0x41,
	0x54, 0x4d, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0xc7, 0x07, 0x12, 0x15, 0x0a, 0x10, 0x41, 0x43, 0x43, 0x5f, 0x44, 0x45,
	0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x4b, 0x10, 0xc8, 0x07, 0x12, 0x21, 0x0a,
	0x1c, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c,
	0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xc9, 0x07,
	0x12, 0x1d, 0x0a, 0x18, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xca, 0x07, 0x12,
	0x1d, 0x0a, 0x18, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xcb, 0x07, 0x12, 0x25,
	0x0a, 0x20, 0x49, 0x4e, 0x43, 0x52, 0x45, 0x41, 0x53, 0x45, 0x5f, 0x42, 0x4f, 0x4e, 0x55, 0x53,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x41, 0x46, 0x46, 0x4c, 0x55, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x10, 0xcc, 0x07, 0x12, 0x12, 0x0a, 0x0d, 0x52, 0x45, 0x5f, 0x4b, 0x59, 0x43, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcd, 0x07, 0x12, 0x14, 0x0a, 0x0f, 0x50, 0x41, 0x4e,
	0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xce, 0x07, 0x12,
	0x0d, 0x0a, 0x08, 0x41, 0x46, 0x55, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x10, 0xcf, 0x07, 0x12, 0x16,
	0x0a, 0x11, 0x41, 0x43, 0x43, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50,
	0x4f, 0x4c, 0x4c, 0x10, 0xd0, 0x07, 0x12, 0x19, 0x0a, 0x14, 0x4f, 0x4e, 0x42, 0x5f, 0x4e, 0x45,
	0x58, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x10, 0xd1,
	0x07, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10,
	0xd2, 0x07, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0xd3, 0x07, 0x12, 0x1c, 0x0a, 0x17, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xd4,
	0x07, 0x12, 0x13, 0x0a, 0x0e, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0xd5, 0x07, 0x12, 0x1a, 0x0a, 0x15, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x45, 0x52, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10,
	0xd6, 0x07, 0x12, 0x1e, 0x0a, 0x19, 0x47, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x56, 0x45, 0x52, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10,
	0xd7, 0x07, 0x12, 0x1a, 0x0a, 0x15, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x49, 0x4e, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xd8, 0x07, 0x12, 0x18,
	0x0a, 0x13, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xd9, 0x07, 0x12, 0x15, 0x0a, 0x10, 0x56, 0x4b, 0x59, 0x43,
	0x5f, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xda, 0x07, 0x12,
	0x1f, 0x0a, 0x1a, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xdb, 0x07,
	0x12, 0x12, 0x0a, 0x0d, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x48, 0x45, 0x41, 0x44, 0x4c, 0x45, 0x53,
	0x53, 0x10, 0xdc, 0x07, 0x12, 0x0e, 0x0a, 0x09, 0x45, 0x50, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x49,
	0x54, 0x10, 0xdd, 0x07, 0x12, 0x1d, 0x0a, 0x18, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x4e, 0x53,
	0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x4c, 0x41, 0x59,
	0x10, 0xde, 0x07, 0x12, 0x17, 0x0a, 0x12, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x43, 0x48, 0x45,
	0x44, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x4b, 0x10, 0xdf, 0x07, 0x12, 0x24, 0x0a, 0x1f,
	0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xe0, 0x07, 0x12, 0x2a, 0x0a, 0x25, 0x44, 0x43, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x4e, 0x45, 0x57, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe1, 0x07, 0x12, 0x34,
	0x0a, 0x2f, 0x45, 0x50, 0x46, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x49,
	0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x5f, 0x50, 0x48,
	0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xe2, 0x07, 0x12, 0x1f, 0x0a, 0x1a, 0x44, 0x4d, 0x46, 0x5f, 0x47, 0x45, 0x4e, 0x45,
	0x52, 0x49, 0x43, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xe3, 0x07, 0x12, 0x28, 0x0a, 0x23, 0x45, 0x50, 0x46, 0x5f, 0x50, 0x41, 0x53,
	0x53, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x55, 0x41, 0x4e,
	0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe4, 0x07, 0x12,
	0x23, 0x0a, 0x1e, 0x45, 0x50, 0x46, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x42, 0x4f, 0x4f, 0x4b, 0x5f,
	0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xe5, 0x07, 0x12, 0x19, 0x0a, 0x14, 0x45, 0x50, 0x46, 0x5f, 0x44, 0x41, 0x53, 0x48,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe6, 0x07, 0x12,
	0x1d, 0x0a, 0x18, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f,
	0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe7, 0x07, 0x12, 0x22,
	0x0a, 0x1d, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x44,
	0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xe8, 0x07, 0x12, 0x23, 0x0a, 0x1e, 0x49, 0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x53, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xe9, 0x07, 0x12, 0x2c, 0x0a, 0x27, 0x49, 0x4e, 0x44, 0x49, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xea, 0x07, 0x12, 0x27, 0x0a, 0x22, 0x49, 0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x43,
	0x45, 0x49, 0x50, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xeb, 0x07, 0x12, 0x26,
	0x0a, 0x21, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41,
	0x44, 0x45, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48,
	0x45, 0x45, 0x54, 0x10, 0xec, 0x07, 0x12, 0x2f, 0x0a, 0x2a, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55,
	0x4e, 0x44, 0x53, 0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x56, 0x31, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xed, 0x07, 0x12, 0x1f, 0x0a, 0x1a, 0x41, 0x46, 0x46, 0x4c, 0x55,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xee, 0x07, 0x12, 0x1c, 0x0a, 0x17, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xef, 0x07, 0x12, 0x1f, 0x0a, 0x1a, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x49,
	0x43, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45, 0x54, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xf0, 0x07, 0x12, 0x1b, 0x0a, 0x16, 0x45, 0x50, 0x41, 0x4e, 0x5f,
	0x45, 0x4e, 0x54, 0x52, 0x59, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xf1, 0x07, 0x12, 0x1e, 0x0a, 0x19, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x49, 0x43, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xf2, 0x07, 0x12, 0x1c, 0x0a, 0x17, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xf3, 0x07, 0x12, 0x20, 0x0a, 0x1b, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x42,
	0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xf4, 0x07, 0x12, 0x22, 0x0a, 0x1d, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x49, 0x43, 0x5f,
	0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf5, 0x07, 0x12, 0x20, 0x0a, 0x1b, 0x44, 0x45, 0x56, 0x49,
	0x43, 0x45, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x4f, 0x43, 0x4b,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf6, 0x07, 0x12, 0x22, 0x0a, 0x1d, 0x4e, 0x45,
	0x58, 0x54, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf7, 0x07, 0x12, 0x22,
	0x0a, 0x1d, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x45, 0x52,
	0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xf8, 0x07, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x4f,
	0x46, 0x46, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45, 0x54, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf9, 0x07, 0x12, 0x17, 0x0a, 0x12, 0x56, 0x4b, 0x59, 0x43,
	0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfa,
	0x07, 0x12, 0x18, 0x0a, 0x13, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfb, 0x07, 0x12, 0x18, 0x0a, 0x13, 0x56,
	0x4b, 0x59, 0x43, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xfc, 0x07, 0x12, 0x1f, 0x0a, 0x1a, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x59, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x4f, 0x43, 0x53, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xfd, 0x07, 0x12, 0x19, 0x0a, 0x14, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x52,
	0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfe,
	0x07, 0x12, 0x1c, 0x0a, 0x17, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x53,
	0x5f, 0x42, 0x55, 0x53, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xff, 0x07, 0x12,
	0x20, 0x0a, 0x1b, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x44,
	0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x80,
	0x08, 0x12, 0x20, 0x0a, 0x1b, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x56, 0x32, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x81, 0x08, 0x12, 0x17, 0x0a, 0x12, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x82, 0x08, 0x12, 0x1b, 0x0a, 0x16,
	0x56, 0x4b, 0x59, 0x43, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x83, 0x08, 0x12, 0x20, 0x0a, 0x1b, 0x56, 0x4b, 0x59,
	0x43, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x56,
	0x31, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x84, 0x08, 0x12, 0x1b, 0x0a, 0x16, 0x56,
	0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x56, 0x32, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x85, 0x08, 0x12, 0x27, 0x0a, 0x22, 0x56, 0x4b, 0x59, 0x43,
	0x5f, 0x50, 0x41, 0x4e, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45,
	0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x86,
	0x08, 0x12, 0x1a, 0x0a, 0x15, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59,
	0x5f, 0x4d, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x87, 0x08, 0x12, 0x25, 0x0a,
	0x20, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e,
	0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45,
	0x54, 0x10, 0x88, 0x08, 0x12, 0x1f, 0x0a, 0x1a, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x89, 0x08, 0x12, 0x22, 0x0a, 0x1d, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x54, 0x50, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x8a, 0x08, 0x12, 0x1c, 0x0a, 0x17, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x8b, 0x08, 0x12, 0x25, 0x0a, 0x20, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x8c, 0x08, 0x12, 0x26,
	0x0a, 0x21, 0x41, 0x50, 0x50, 0x4c, 0x45, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x49, 0x4e, 0x5f, 0x4d,
	0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0x8d, 0x08, 0x12, 0x17, 0x0a, 0x12, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45,
	0x5f, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x8e, 0x08, 0x12,
	0x1b, 0x0a, 0x16, 0x43, 0x49, 0x42, 0x49, 0x4c, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x56, 0x45, 0x52,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x8f, 0x08, 0x12, 0x18, 0x0a, 0x13,
	0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x55, 0x4d,
	0x42, 0x45, 0x52, 0x10, 0x90, 0x08, 0x12, 0x13, 0x0a, 0x0e, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x10, 0x91, 0x08, 0x12, 0x1a, 0x0a, 0x15, 0x55,
	0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x44, 0x52, 0x4f, 0x50,
	0x44, 0x4f, 0x57, 0x4e, 0x10, 0x92, 0x08, 0x12, 0x1d, 0x0a, 0x18, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0x93, 0x08, 0x12, 0x2a, 0x0a, 0x25, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x4f,
	0x52, 0x4d, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10,
	0x94, 0x08, 0x12, 0x21, 0x0a, 0x1c, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x43,
	0x41, 0x4e, 0x43, 0x45, 0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x44, 0x49, 0x41, 0x4c,
	0x4f, 0x47, 0x10, 0x95, 0x08, 0x12, 0x26, 0x0a, 0x1d, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x96, 0x08, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x24, 0x0a,
	0x1f, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x50, 0x41,
	0x4e, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x10, 0x97, 0x08, 0x12, 0x1b, 0x0a, 0x16, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f,
	0x50, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x98, 0x08,
	0x12, 0x1d, 0x0a, 0x18, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x42, 0x45, 0x4e,
	0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x99, 0x08, 0x12,
	0x29, 0x0a, 0x24, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53,
	0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f,
	0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x9a, 0x08, 0x12, 0x23, 0x0a, 0x1e, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0x9b, 0x08, 0x12,
	0x1f, 0x0a, 0x1a, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53,
	0x45, 0x52, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0x9c, 0x08,
	0x12, 0x1b, 0x0a, 0x16, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x4c,
	0x54, 0x45, 0x52, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0x9d, 0x08, 0x12, 0x22, 0x0a,
	0x1d, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45,
	0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52, 0x10, 0x9e,
	0x08, 0x12, 0x21, 0x0a, 0x1c, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x4d, 0x55,
	0x4c, 0x54, 0x49, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45,
	0x52, 0x10, 0x9f, 0x08, 0x12, 0x1d, 0x0a, 0x18, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52,
	0x5f, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52,
	0x10, 0xa0, 0x08, 0x12, 0x1f, 0x0a, 0x1a, 0x4d, 0x46, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53,
	0x45, 0x52, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xa1, 0x08, 0x12, 0x1f, 0x0a, 0x1a, 0x53, 0x49, 0x4d, 0x5f, 0x53, 0x45, 0x4c, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45,
	0x45, 0x54, 0x10, 0xa2, 0x08, 0x12, 0x25, 0x0a, 0x20, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f,
	0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54,
	0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xa3, 0x08, 0x12, 0x2b, 0x0a, 0x26,
	0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d,
	0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xa4, 0x08, 0x12, 0x2a, 0x0a, 0x25, 0x53, 0x45, 0x43,
	0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f,
	0x56, 0x32, 0x10, 0xa5, 0x08, 0x12, 0x2a, 0x0a, 0x25, 0x45, 0x50, 0x46, 0x5f, 0x50, 0x41, 0x53,
	0x53, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4d, 0x41, 0x4e,
	0x55, 0x41, 0x4c, 0x5f, 0x55, 0x41, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa6,
	0x08, 0x12, 0x1a, 0x0a, 0x15, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x49, 0x4e,
	0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa7, 0x08, 0x12, 0x1a, 0x0a,
	0x15, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54,
	0x53, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0xa8, 0x08, 0x12, 0x1a, 0x0a, 0x15, 0x4d, 0x46, 0x5f,
	0x41, 0x55, 0x54, 0x4f, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xa9, 0x08, 0x12, 0x29, 0x0a, 0x24, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x52,
	0x55, 0x4c, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xaa, 0x08,
	0x12, 0x12, 0x0a, 0x0d, 0x4d, 0x46, 0x5f, 0x4f, 0x54, 0x49, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xab, 0x08, 0x12, 0x13, 0x0a, 0x0e, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x50, 0x4f, 0x50,
	0x5f, 0x55, 0x50, 0x5f, 0x56, 0x32, 0x10, 0xac, 0x08, 0x12, 0x17, 0x0a, 0x12, 0x47, 0x45, 0x4e,
	0x45, 0x52, 0x49, 0x43, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xad, 0x08, 0x12, 0x1b, 0x0a, 0x16, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x53, 0x55,
	0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xae, 0x08, 0x12,
	0x24, 0x0a, 0x1f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x4d, 0x41, 0x54, 0x55, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0xaf, 0x08, 0x12, 0x17, 0x0a, 0x12, 0x41, 0x44, 0x44, 0x5f, 0x4e, 0x4f, 0x4d,
	0x49, 0x4e, 0x45, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb0, 0x08, 0x12, 0x1f,
	0x0a, 0x1a, 0x41, 0x44, 0x44, 0x5f, 0x4e, 0x4f, 0x4d, 0x49, 0x4e, 0x45, 0x45, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb1, 0x08, 0x12,
	0x14, 0x0a, 0x0f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49,
	0x4c, 0x53, 0x10, 0xb2, 0x08, 0x12, 0x1e, 0x0a, 0x19, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xb3, 0x08, 0x12, 0x2b, 0x0a, 0x26, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54,
	0x5f, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0xb4, 0x08, 0x12, 0x17, 0x0a, 0x12, 0x48, 0x45, 0x4c, 0x50, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0xb5, 0x08, 0x12, 0x21, 0x0a, 0x1c, 0x55,
	0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb6, 0x08, 0x12, 0x2c,
	0x0a, 0x27, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f,
	0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50,
	0x4f, 0x4c, 0x4c, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb7, 0x08, 0x12, 0x19, 0x0a, 0x14,
	0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xb8, 0x08, 0x12, 0x22, 0x0a, 0x1d, 0x49, 0x4d, 0x41, 0x47, 0x45,
	0x5f, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb9, 0x08, 0x12, 0x1d, 0x0a, 0x18, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x4d,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xba, 0x08, 0x12, 0x22, 0x0a, 0x1d, 0x54, 0x49,
	0x45, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x41, 0x52, 0x4e, 0x45, 0x44, 0x5f, 0x42, 0x45, 0x4e,
	0x45, 0x46, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xbb, 0x08, 0x12, 0x18,
	0x0a, 0x13, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x4d, 0x46, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xbc, 0x08, 0x12, 0x20, 0x0a, 0x1b, 0x4c, 0x41, 0x4d, 0x46,
	0x5f, 0x53, 0x4b, 0x49, 0x50, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x46,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xbd, 0x08, 0x12, 0x26, 0x0a, 0x21, 0x50, 0x45,
	0x4e, 0x4e, 0x59, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xbe, 0x08, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xbf, 0x08,
	0x12, 0x22, 0x0a, 0x1d, 0x50, 0x45, 0x4e, 0x4e, 0x59, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xc0, 0x08, 0x12, 0x1c, 0x0a, 0x17, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x49, 0x4e, 0x46,
	0x4f, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xc1, 0x08, 0x12, 0x22, 0x0a, 0x1d, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x43, 0x41, 0x4c, 0x43,
	0x55, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x53, 0x48,
	0x45, 0x45, 0x54, 0x10, 0xc2, 0x08, 0x12, 0x26, 0x0a, 0x21, 0x4e, 0x45, 0x54, 0x5f, 0x57, 0x4f,
	0x52, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x47, 0x45, 0x54, 0x5f,
	0x4e, 0x45, 0x58, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xc3, 0x08, 0x12, 0x28,
	0x0a, 0x23, 0x4e, 0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x46, 0x52,
	0x45, 0x53, 0x48, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f,
	0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xc4, 0x08, 0x12, 0x2c, 0x0a, 0x27, 0x4e, 0x45, 0x54, 0x5f,
	0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x4d, 0x41,
	0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x53, 0x5f, 0x52, 0x45, 0x46, 0x52,
	0x45, 0x53, 0x48, 0x10, 0xc5, 0x08, 0x12, 0x2b, 0x0a, 0x26, 0x4e, 0x45, 0x54, 0x5f, 0x57, 0x4f,
	0x52, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x53,
	0x10, 0xc6, 0x08, 0x12, 0x25, 0x0a, 0x20, 0x4e, 0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48,
	0x5f, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc7, 0x08, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x43,
	0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc8, 0x08, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x43, 0x5f, 0x41,
	0x43, 0x43, 0x45, 0x50, 0x54, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x4e,
	0x47, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc9, 0x08, 0x12, 0x16, 0x0a, 0x11,
	0x43, 0x45, 0x4c, 0x45, 0x42, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x4f, 0x50, 0x55,
	0x50, 0x10, 0xca, 0x08, 0x12, 0x2a, 0x0a, 0x25, 0x54, 0x49, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x5f,
	0x45, 0x41, 0x52, 0x4e, 0x45, 0x44, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x5f, 0x48,
	0x49, 0x53, 0x54, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcb, 0x08,
	0x12, 0x1b, 0x0a, 0x16, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45,
	0x4e, 0x54, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcc, 0x08, 0x12, 0x1b, 0x0a,
	0x16, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x50, 0x41, 0x59, 0x5f, 0x56, 0x32,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcd, 0x08, 0x12, 0x23, 0x0a, 0x1e, 0x4c, 0x4f,
	0x41, 0x4e, 0x53, 0x5f, 0x52, 0x45, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45,
	0x54, 0x48, 0x4f, 0x44, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xce, 0x08, 0x12,
	0x1c, 0x0a, 0x17, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x56, 0x49, 0x41,
	0x5f, 0x43, 0x58, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcf, 0x08, 0x12, 0x2f, 0x0a,
	0x2a, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x4e, 0x46, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x55, 0x54, 0x48,
	0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd0, 0x08, 0x12, 0x30,
	0x0a, 0x2b, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x46, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd1, 0x08,
	0x12, 0x3c, 0x0a, 0x37, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x46, 0x54, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd2, 0x08, 0x12, 0x3e,
	0x0a, 0x39, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x46, 0x54, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x4d, 0x4f, 0x42,
	0x49, 0x4c, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd3, 0x08, 0x12, 0x18,
	0x0a, 0x13, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4d, 0x46, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd4, 0x08, 0x12, 0x28, 0x0a, 0x23, 0x4c, 0x41, 0x4d, 0x46,
	0x5f, 0x4d, 0x46, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x4c, 0x49, 0x4e, 0x4b,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xd5, 0x08, 0x12, 0x19, 0x0a, 0x14, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4d, 0x46, 0x5f, 0x4c, 0x49,
	0x4e, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0xd6, 0x08, 0x12, 0x27, 0x0a,
	0x22, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x57,
	0x41, 0x52, 0x44, 0x53, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x44, 0x49, 0x41,
	0x4c, 0x4f, 0x47, 0x10, 0xd7, 0x08, 0x12, 0x2c, 0x0a, 0x27, 0x47, 0x45, 0x54, 0x5f, 0x49, 0x4e,
	0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x49, 0x50, 0x53, 0x5f, 0x4d, 0x4f,
	0x44, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xd8, 0x08, 0x12, 0x1c, 0x0a, 0x17, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x41, 0x4c,
	0x4c, 0x5f, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10,
	0xd9, 0x08, 0x12, 0x24, 0x0a, 0x1f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x47, 0x45, 0x4e, 0x45,
	0x52, 0x49, 0x43, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f,
	0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xda, 0x08, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x58, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x55, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0xdb, 0x08, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x58, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41,
	0x43, 0x54, 0x5f, 0x55, 0x53, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x10, 0xdc,
	0x08, 0x12, 0x2f, 0x0a, 0x2a, 0x43, 0x58, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f,
	0x55, 0x53, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10,
	0xdd, 0x08, 0x12, 0x2b, 0x0a, 0x26, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xde, 0x08, 0x12,
	0x25, 0x0a, 0x20, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f,
	0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xdf, 0x08, 0x12, 0x1f, 0x0a, 0x1a, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f,
	0x47, 0x45, 0x4e, 0x45, 0x52, 0x49, 0x43, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xe0, 0x08, 0x12, 0x1d, 0x0a, 0x18, 0x4c, 0x4f, 0x41, 0x4e, 0x53,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xe1, 0x08, 0x12, 0x1d, 0x0a, 0x18, 0x45, 0x50, 0x41, 0x4e, 0x5f, 0x49,
	0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xe2, 0x08, 0x12, 0x1f, 0x0a, 0x1a, 0x4d, 0x41, 0x50, 0x5f, 0x41, 0x44, 0x44,
	0x52, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xe3, 0x08, 0x12, 0x23, 0x0a, 0x1e, 0x41, 0x44, 0x44, 0x5f, 0x4e, 0x45,
	0x57, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe4, 0x08, 0x12, 0x2f, 0x0a, 0x2a, 0x45,
	0x4e, 0x41, 0x43, 0x48, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x45, 0x52, 0x5f, 0x42, 0x4f, 0x54,
	0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xe5, 0x08, 0x12, 0x25, 0x0a, 0x20,
	0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4d, 0x46, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4d, 0x45, 0x44,
	0x49, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xe6, 0x08, 0x12, 0x26, 0x0a, 0x21, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x4e, 0x53, 0x54,
	0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x57, 0x45, 0x42, 0x5f, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe7, 0x08, 0x12, 0x2c, 0x0a, 0x27, 0x41,
	0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x53, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe8, 0x08, 0x12, 0x39, 0x0a, 0x34, 0x41, 0x41, 0x5f,
	0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x53, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xe9, 0x08, 0x12, 0x1d, 0x0a, 0x18, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52,
	0x59, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xea, 0x08, 0x12, 0x2d, 0x0a, 0x28, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59,
	0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x56, 0x49, 0x41, 0x5f, 0x4f,
	0x46, 0x46, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x10,
	0xeb, 0x08, 0x12, 0x21, 0x0a, 0x1c, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x57,
	0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45,
	0x45, 0x54, 0x10, 0xec, 0x08, 0x12, 0x23, 0x0a, 0x1e, 0x41, 0x4c, 0x4c, 0x5f, 0x4f, 0x50, 0x45,
	0x4e, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x4c, 0x49, 0x53, 0x54,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xed, 0x08, 0x12, 0x24, 0x0a, 0x1f, 0x4c, 0x4f,
	0x41, 0x4e, 0x53, 0x5f, 0x57, 0x45, 0x42, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x57, 0x49, 0x54,
	0x48, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xee, 0x08,
	0x12, 0x2d, 0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xef, 0x08, 0x12,
	0x1f, 0x0a, 0x1a, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf0, 0x08,
	0x12, 0x1b, 0x0a, 0x16, 0x48, 0x45, 0x4c, 0x50, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x4e, 0x54, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0xf1, 0x08, 0x12, 0x21, 0x0a,
	0x1c, 0x48, 0x45, 0x4c, 0x50, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0xf2, 0x08,
	0x12, 0x23, 0x0a, 0x1e, 0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x4d, 0x46, 0x5f, 0x4c, 0x49, 0x4e, 0x4b,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xf3, 0x08, 0x12, 0x21, 0x0a, 0x1c, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x41,
	0x4c, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf4, 0x08, 0x12, 0x2b, 0x0a, 0x26, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x43, 0x48, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x4f, 0x4c,
	0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45,
	0x45, 0x54, 0x10, 0xf5, 0x08, 0x12, 0x26, 0x0a, 0x21, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x41, 0x43,
	0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x42, 0x4f,
	0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xf6, 0x08, 0x12, 0x19, 0x0a,
	0x14, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf7, 0x08, 0x12, 0x17, 0x0a, 0x12, 0x41, 0x53, 0x53, 0x45,
	0x54, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0xf8,
	0x08, 0x12, 0x30, 0x0a, 0x2b, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xf9, 0x08, 0x12, 0x2e, 0x0a, 0x29, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xfa, 0x08, 0x12, 0x23, 0x0a, 0x1e, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x43, 0x48, 0x45,
	0x43, 0x4b, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfb, 0x08, 0x12, 0x1b, 0x0a, 0x16, 0x44, 0x43, 0x5f, 0x44,
	0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x56, 0x32, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xfc, 0x08, 0x12, 0x24, 0x0a, 0x1f, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41,
	0x52, 0x59, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x4f, 0x46, 0x5f, 0x46, 0x55, 0x4e,
	0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfd, 0x08, 0x12, 0x18, 0x0a, 0x13, 0x41,
	0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x50, 0x55,
	0x4c, 0x4c, 0x10, 0xfe, 0x08, 0x12, 0x16, 0x0a, 0x11, 0x53, 0x44, 0x55, 0x49, 0x5f, 0x42, 0x4f,
	0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xff, 0x08, 0x12, 0x25, 0x0a,
	0x20, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x4f, 0x46, 0x54,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x80, 0x09, 0x12, 0x27, 0x0a, 0x22, 0x55, 0x53, 0x53, 0x5f, 0x46, 0x55, 0x4e, 0x44,
	0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52,
	0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x81, 0x09, 0x12, 0x2b, 0x0a,
	0x26, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x82, 0x09, 0x12, 0x28, 0x0a, 0x23, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45,
	0x53, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x83, 0x09, 0x12, 0x28, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4d, 0x55,
	0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x84, 0x09, 0x12, 0x2a,
	0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45,
	0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x85, 0x09, 0x12, 0x17, 0x0a, 0x12, 0x49, 0x4e,
	0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x51, 0x55, 0x44, 0x4f, 0x5f, 0x53, 0x44, 0x4b,
	0x10, 0x86, 0x09, 0x12, 0x2a, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x52,
	0x45, 0x53, 0x53, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45,
	0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x87, 0x09, 0x12,
	0x1b, 0x0a, 0x16, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53,
	0x45, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x88, 0x09, 0x12, 0x15, 0x0a, 0x10,
	0x56, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x89, 0x09, 0x12, 0x17, 0x0a, 0x12, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x41, 0x50,
	0x54, 0x55, 0x52, 0x45, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x8a, 0x09, 0x12, 0x0f, 0x0a, 0x0a,
	0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x44, 0x4f, 0x43, 0x10, 0x8b, 0x09, 0x12, 0x1b, 0x0a,
	0x16, 0x4a, 0x4f, 0x55, 0x52, 0x4e, 0x45, 0x59, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x8c, 0x09, 0x12, 0x1f, 0x0a, 0x1a, 0x4a, 0x4f,
	0x55, 0x52, 0x4e, 0x45, 0x59, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45,
	0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x8d, 0x09, 0x12, 0x1e, 0x0a, 0x19, 0x43,
	0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x55, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x8e, 0x09, 0x12, 0x1f, 0x0a, 0x1a, 0x43,
	0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x55, 0x53, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e,
	0x41, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x8f, 0x09, 0x12, 0x32, 0x0a, 0x2d,
	0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x48,
	0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x5f, 0x4f, 0x4e, 0x53, 0x55, 0x52, 0x49,
	0x54, 0x59, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x90, 0x09,
	0x12, 0x23, 0x0a, 0x1e, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x4c, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45,
	0x45, 0x54, 0x10, 0x91, 0x09, 0x12, 0x23, 0x0a, 0x1e, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f,
	0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x42, 0x52, 0x41, 0x52, 0x59,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x92, 0x09, 0x12, 0x26, 0x0a, 0x21, 0x52, 0x45,
	0x56, 0x49, 0x53, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0x93, 0x09, 0x12, 0x3a, 0x0a, 0x35, 0x52, 0x45, 0x56, 0x49, 0x53, 0x45, 0x44, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f,
	0x50, 0x4c, 0x41, 0x4e, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42,
	0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x94, 0x09, 0x12, 0x31,
	0x0a, 0x2c, 0x52, 0x45, 0x56, 0x49, 0x53, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x95,
	0x09, 0x12, 0x16, 0x0a, 0x11, 0x43, 0x41, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x53, 0x45, 0x4c,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xee, 0x0d, 0x12, 0x27, 0x0a, 0x22, 0x4c, 0x4f, 0x41,
	0x4e, 0x53, 0x5f, 0x41, 0x41, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f,
	0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0xef, 0x0d, 0x12, 0x0f, 0x0a, 0x0a, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x56, 0x32,
	0x10, 0xf0, 0x0d, 0x12, 0x37, 0x0a, 0x32, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53,
	0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x4f, 0x46, 0x5f, 0x4f,
	0x46, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45,
	0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf1, 0x0d, 0x12, 0x2f, 0x0a, 0x2a,
	0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54,
	0x5f, 0x44, 0x49, 0x47, 0x49, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x52, 0x5f, 0x41, 0x41, 0x44, 0x48,
	0x41, 0x41, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf2, 0x0d, 0x12, 0x26, 0x0a,
	0x21, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41,
	0x44, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45,
	0x45, 0x54, 0x10, 0xf3, 0x0d, 0x12, 0x1d, 0x0a, 0x18, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53,
	0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45,
	0x54, 0x10, 0xf4, 0x0d, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f,
	0x55, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x4c, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf5, 0x0d, 0x12,
	0x39, 0x0a, 0x34, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x55, 0x53, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x4d, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f,
	0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xf6, 0x0d, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xf7, 0x0d, 0x12, 0x1c,
	0x0a, 0x17, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf8, 0x0d, 0x12, 0x1e, 0x0a, 0x19,
	0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49,
	0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf9, 0x0d, 0x12, 0x17, 0x0a, 0x12,
	0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xfa, 0x0d, 0x12, 0x20, 0x0a, 0x1b, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e,
	0x44, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xfb, 0x0d, 0x12, 0x1d, 0x0a, 0x18, 0x41, 0x44, 0x44, 0x5f, 0x46,
	0x55, 0x4e, 0x44, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xfc, 0x0d, 0x12, 0x25, 0x0a, 0x20, 0x41, 0x55, 0x54, 0x4f, 0x50, 0x41,
	0x59, 0x5f, 0x41, 0x52, 0x43, 0x48, 0x49, 0x56, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfd, 0x0d, 0x12, 0x2e, 0x0a,
	0x29, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41,
	0x54, 0x45, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfe, 0x0d, 0x12, 0x27, 0x0a,
	0x22, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x52,
	0x45, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x45, 0x45, 0x53, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xff, 0x0d, 0x12, 0x2c, 0x0a, 0x27, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x43,
	0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x53, 0x5f, 0x56, 0x49, 0x41, 0x5f, 0x54, 0x50, 0x41, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x80, 0x0e, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x41,
	0x55, 0x54, 0x4f, 0x50, 0x41, 0x59, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x81, 0x0e, 0x12, 0x16, 0x0a, 0x11, 0x50, 0x41, 0x59, 0x5f, 0x4c, 0x41, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x4f, 0x50, 0x55, 0x50, 0x10, 0x82, 0x0e, 0x12, 0x26, 0x0a,
	0x21, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x47, 0x41, 0x54, 0x45, 0x57, 0x41, 0x59,
	0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x4f, 0x55, 0x54, 0x5f, 0x57, 0x45, 0x42, 0x5f, 0x56, 0x49,
	0x45, 0x57, 0x10, 0x83, 0x0e, 0x12, 0x27, 0x0a, 0x22, 0x44, 0x43, 0x5f, 0x55, 0x53, 0x41, 0x47,
	0x45, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x54,
	0x49, 0x4e, 0x47, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x84, 0x0e, 0x12, 0x1c,
	0x0a, 0x17, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x85, 0x0e, 0x12, 0x34, 0x0a, 0x2f,
	0x43, 0x4c, 0x4f, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x52, 0x45,
	0x4f, 0x50, 0x45, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x56, 0x45, 0x52, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0x86, 0x0e, 0x12, 0x19, 0x0a, 0x14, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x4e, 0x49,
	0x54, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x56, 0x31, 0x10, 0x87, 0x0e, 0x12, 0x1d, 0x0a,
	0x18, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49,
	0x45, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x88, 0x0e, 0x12, 0x1b, 0x0a, 0x16,
	0x53, 0x4b, 0x49, 0x50, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d,
	0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x89, 0x0e, 0x12, 0x1e, 0x0a, 0x19, 0x56, 0x4b, 0x59,
	0x43, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x45, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x8a, 0x0e, 0x12, 0x1e, 0x0a, 0x19, 0x53, 0x4b, 0x49,
	0x50, 0x5f, 0x4e, 0x4f, 0x4d, 0x49, 0x4e, 0x45, 0x45, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d,
	0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x8b, 0x0e, 0x12, 0x18, 0x0a, 0x13, 0x41, 0x44, 0x44,
	0x5f, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x8c, 0x0e, 0x12, 0x20, 0x0a, 0x1b, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x41, 0x43, 0x4b, 0x4e,
	0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x8d, 0x0e, 0x12, 0x2d, 0x0a, 0x28, 0x53, 0x48, 0x4f, 0x57, 0x5f, 0x43, 0x41,
	0x4d, 0x45, 0x52, 0x41, 0x5f, 0x4d, 0x49, 0x43, 0x5f, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53,
	0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x8e, 0x0e, 0x12, 0x18, 0x0a, 0x13, 0x48, 0x45, 0x4c, 0x50, 0x45, 0x52, 0x5f, 0x42,
	0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x8f, 0x0e, 0x12, 0x16,
	0x0a, 0x11, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x56, 0x32, 0x10, 0x90, 0x0e, 0x12, 0x15, 0x0a, 0x10, 0x50, 0x41, 0x4e, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x91, 0x0e, 0x12, 0x19, 0x0a,
	0x14, 0x50, 0x41, 0x59, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x56, 0x32, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x92, 0x0e, 0x12, 0x18, 0x0a, 0x13, 0x44, 0x43, 0x5f, 0x4f,
	0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x10,
	0x93, 0x0e, 0x12, 0x2a, 0x0a, 0x25, 0x43, 0x41, 0x54, 0x41, 0x4c, 0x4f, 0x47, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x44, 0x45, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42,
	0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x94, 0x0e, 0x12, 0x2b,
	0x0a, 0x26, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53,
	0x53, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54,
	0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x95, 0x0e, 0x12, 0x15, 0x0a, 0x10, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x56, 0x32, 0x10,
	0x96, 0x0e, 0x12, 0x23, 0x0a, 0x1e, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f,
	0x54, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x97, 0x0e, 0x12, 0x19, 0x0a, 0x14, 0x4c, 0x4f, 0x41, 0x4e, 0x53,
	0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0x98, 0x0e, 0x12, 0x34, 0x0a, 0x2f, 0x44, 0x43, 0x5f, 0x54, 0x4f, 0x47, 0x47, 0x4c, 0x45, 0x5f,
	0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f,
	0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x99, 0x0e, 0x12, 0x1f, 0x0a, 0x1a, 0x4f, 0x4e, 0x42, 0x4f,
	0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x56, 0x32, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x9a, 0x0e, 0x12, 0x28, 0x0a, 0x23, 0x4c, 0x4f, 0x41,
	0x4e, 0x53, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54,
	0x10, 0x9b, 0x0e, 0x12, 0x27, 0x0a, 0x22, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4f, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x55,
	0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x9c, 0x0e, 0x12, 0x25, 0x0a, 0x20,
	0x44, 0x43, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41,
	0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x56, 0x32, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x9d, 0x0e, 0x12, 0x2c, 0x0a, 0x27, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f,
	0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x9e,
	0x0e, 0x12, 0x25, 0x0a, 0x20, 0x44, 0x43, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f,
	0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x9f, 0x0e, 0x12, 0x10, 0x0a, 0x0b, 0x53, 0x4d, 0x53, 0x5f,
	0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0xa0, 0x0e, 0x12, 0x2d, 0x0a, 0x28, 0x44, 0x43,
	0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0xa1, 0x0e, 0x12, 0x1d, 0x0a, 0x18, 0x4c, 0x4f, 0x41,
	0x4e, 0x53, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa2, 0x0e, 0x12, 0x2b, 0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e,
	0x53, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f,
	0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xa3, 0x0e, 0x12, 0x22, 0x0a, 0x1d, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f,
	0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa4, 0x0e, 0x12, 0x1f, 0x0a, 0x1a, 0x50, 0x4c, 0x5f,
	0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa5, 0x0e, 0x12, 0x17, 0x0a, 0x12, 0x50, 0x41,
	0x4e, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x49, 0x4e, 0x47,
	0x10, 0xa6, 0x0e, 0x12, 0x21, 0x0a, 0x1c, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x55, 0x41,
	0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48,
	0x45, 0x45, 0x54, 0x10, 0xa7, 0x0e, 0x12, 0x1f, 0x0a, 0x1a, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55,
	0x4e, 0x44, 0x53, 0x5f, 0x41, 0x43, 0x4b, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53,
	0x48, 0x45, 0x45, 0x54, 0x10, 0xa8, 0x0e, 0x12, 0x1e, 0x0a, 0x19, 0x50, 0x41, 0x4e, 0x5f, 0x44,
	0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53,
	0x48, 0x45, 0x45, 0x54, 0x10, 0xa9, 0x0e, 0x12, 0x16, 0x0a, 0x11, 0x49, 0x4e, 0x46, 0x4f, 0x5f,
	0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xaa, 0x0e, 0x12,
	0x29, 0x0a, 0x24, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52,
	0x52, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x44,
	0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0xab, 0x0e, 0x12, 0x14, 0x0a, 0x0f, 0x45, 0x4d,
	0x50, 0x4c, 0x4f, 0x59, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x10, 0xac, 0x0e,
	0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4c, 0x45, 0x41, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49,
	0x4e, 0x47, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0xad, 0x0e, 0x12, 0x21, 0x0a,
	0x1c, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41,
	0x4c, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0xae, 0x0e,
	0x12, 0x15, 0x0a, 0x10, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x10, 0xaf, 0x0e, 0x12, 0x24, 0x0a, 0x1f, 0x50, 0x48, 0x4f, 0x4e, 0x45,
	0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x42, 0x4f,
	0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xb0, 0x0e, 0x12, 0x1d, 0x0a,
	0x18, 0x56, 0x49, 0x52, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x4e,
	0x46, 0x4f, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0xb1, 0x0e, 0x12, 0x20, 0x0a, 0x1b,
	0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x42,
	0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xb2, 0x0e, 0x12, 0x0b,
	0x0a, 0x06, 0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x10, 0xb3, 0x0e, 0x12, 0x19, 0x0a, 0x14, 0x50,
	0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x41, 0x4c, 0x45, 0x10, 0xb4, 0x0e, 0x12, 0x23, 0x0a, 0x1e, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x45,
	0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x42, 0x4f, 0x54, 0x54,
	0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xb5, 0x0e, 0x12, 0x1f, 0x0a, 0x1a, 0x56,
	0x4b, 0x59, 0x43, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x4f, 0x54,
	0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xb6, 0x0e, 0x12, 0x13, 0x0a, 0x0e,
	0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0xb7,
	0x0e, 0x12, 0x1e, 0x0a, 0x19, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x49, 0x43, 0x5f, 0x42, 0x4f, 0x54,
	0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0xb8,
	0x0e, 0x12, 0x1d, 0x0a, 0x18, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x45, 0x4d, 0x50, 0x5f, 0x42, 0x4f,
	0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x5f, 0x56, 0x32, 0x10, 0xb9, 0x0e,
	0x12, 0x1c, 0x0a, 0x17, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x41, 0x42, 0x53, 0x4f, 0x4c, 0x55, 0x54,
	0x45, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x56, 0x32, 0x10, 0xba, 0x0e, 0x12, 0x15,
	0x0a, 0x10, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x45, 0x4d, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x56, 0x32, 0x10, 0xbb, 0x0e, 0x12, 0x13, 0x0a, 0x0e, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x49, 0x4e,
	0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x56, 0x32, 0x10, 0xbc, 0x0e, 0x12, 0x17, 0x0a, 0x12, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x4f, 0x43, 0x43, 0x55, 0x50, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x32,
	0x10, 0xbd, 0x0e, 0x12, 0x19, 0x0a, 0x14, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x42, 0x4f, 0x54, 0x54,
	0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x5f, 0x56, 0x32, 0x10, 0xbe, 0x0e, 0x12, 0x17,
	0x0a, 0x12, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43,
	0x48, 0x5f, 0x76, 0x32, 0x10, 0xbf, 0x0e, 0x12, 0x0d, 0x0a, 0x08, 0x4b, 0x59, 0x43, 0x5f, 0x49,
	0x4e, 0x46, 0x4f, 0x10, 0xc0, 0x0e, 0x12, 0x1f, 0x0a, 0x1a, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54,
	0x45, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53,
	0x48, 0x45, 0x45, 0x54, 0x10, 0xc1, 0x0e, 0x12, 0x16, 0x0a, 0x11, 0x4b, 0x59, 0x43, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0xc2, 0x0e, 0x12,
	0x0f, 0x0a, 0x0a, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0xc3, 0x0e,
	0x12, 0x21, 0x0a, 0x1c, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x4c,
	0x4f, 0x54, 0x53, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54,
	0x10, 0xc4, 0x0e, 0x12, 0x1f, 0x0a, 0x1a, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x46, 0x45, 0x45, 0x44,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45,
	0x54, 0x10, 0xc5, 0x0e, 0x12, 0x17, 0x0a, 0x12, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x49, 0x4d,
	0x49, 0x54, 0x5f, 0x57, 0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0xc6, 0x0e, 0x12, 0x1e, 0x0a,
	0x19, 0x4e, 0x4f, 0x4d, 0x49, 0x4e, 0x45, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53,
	0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xc7, 0x0e, 0x12, 0x14, 0x0a,
	0x0f, 0x4e, 0x4f, 0x4d, 0x49, 0x4e, 0x45, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53,
	0x10, 0xc8, 0x0e, 0x12, 0x14, 0x0a, 0x0f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x4e, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xc9, 0x0e, 0x12, 0x19, 0x0a, 0x14, 0x56, 0x4b, 0x59,
	0x43, 0x5f, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x50,
	0x49, 0x10, 0xca, 0x0e, 0x12, 0x17, 0x0a, 0x12, 0x41, 0x54, 0x4d, 0x5f, 0x4c, 0x4f, 0x43, 0x41,
	0x54, 0x4f, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcb, 0x0e, 0x12, 0x19, 0x0a,
	0x14, 0x41, 0x41, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x45, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcc, 0x0e, 0x12, 0x22, 0x0a, 0x1d, 0x4c, 0x41, 0x4d, 0x46,
	0x5f, 0x52, 0x45, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f,
	0x44, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcd, 0x0e, 0x12, 0x26, 0x0a, 0x21,
	0x44, 0x43, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45,
	0x54, 0x10, 0xce, 0x0e, 0x12, 0x1b, 0x0a, 0x16, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x44, 0x41,
	0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xcf,
	0x0e, 0x12, 0x1e, 0x0a, 0x19, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x49, 0x4e, 0x43, 0x4f,
	0x4d, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd0,
	0x0e, 0x12, 0x17, 0x0a, 0x12, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x45, 0x52,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd1, 0x0e, 0x12, 0x1d, 0x0a, 0x18, 0x54, 0x49,
	0x45, 0x52, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x53, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0xd2, 0x0e, 0x12, 0x23, 0x0a, 0x1e, 0x54, 0x49, 0x45,
	0x52, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0xd3, 0x0e, 0x12, 0x21,
	0x0a, 0x1c, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x5f, 0x4d, 0x4f, 0x52,
	0x45, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xd4,
	0x0e, 0x12, 0x22, 0x0a, 0x1d, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x41, 0x4e, 0x41, 0x4c,
	0x59, 0x53, 0x49, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xd5, 0x0e, 0x12, 0x24, 0x0a, 0x1f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd6, 0x0e, 0x12, 0x23, 0x0a, 0x1e, 0x47,
	0x45, 0x4e, 0x45, 0x52, 0x49, 0x43, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xd7, 0x0e,
	0x12, 0x2d, 0x0a, 0x28, 0x47, 0x4c, 0x4f, 0x42, 0x41, 0x4c, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45,
	0x44, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xd8, 0x0e, 0x12,
	0x1f, 0x0a, 0x1a, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46,
	0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xd9, 0x0e,
	0x12, 0x22, 0x0a, 0x1d, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x49, 0x43,
	0x5f, 0x53, 0x44, 0x55, 0x49, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x53, 0x48, 0x45, 0x45,
	0x54, 0x10, 0xda, 0x0e, 0x12, 0x19, 0x0a, 0x14, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x4e, 0x54,
	0x52, 0x4f, 0x5f, 0x56, 0x32, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xdb, 0x0e, 0x12,
	0x1b, 0x0a, 0x16, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x53, 0x5f, 0x49, 0x4e,
	0x46, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xdc, 0x0e, 0x12, 0x1b, 0x0a, 0x16,
	0x54, 0x49, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xdd, 0x0e, 0x12, 0x21, 0x0a, 0x1c, 0x54, 0x49, 0x45,
	0x52, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x55,
	0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xde, 0x0e, 0x12, 0x28, 0x0a, 0x23,
	0x54, 0x49, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x45,
	0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48,
	0x45, 0x45, 0x54, 0x10, 0xdf, 0x0e, 0x12, 0x22, 0x0a, 0x1d, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48,
	0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe0, 0x0e, 0x12, 0x36, 0x0a, 0x31, 0x4c, 0x4f,
	0x41, 0x4e, 0x53, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x41, 0x55, 0x54,
	0x48, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10,
	0xe1, 0x0e, 0x12, 0x27, 0x0a, 0x22, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x49, 0x4d, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe2, 0x0e, 0x12, 0x32, 0x0a, 0x2d, 0x47,
	0x45, 0x54, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe3, 0x0e, 0x12,
	0x29, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe4, 0x0e, 0x12, 0x2a, 0x0a, 0x25, 0x4d, 0x46,
	0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xe5, 0x0e, 0x12, 0x2d, 0x0a, 0x28, 0x53, 0x41, 0x5f, 0x4e, 0x4f, 0x4d,
	0x49, 0x4e, 0x45, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xe7, 0x0e, 0x12, 0x17, 0x0a, 0x12, 0x57, 0x41, 0x4c, 0x4b, 0x54, 0x48, 0x52,
	0x4f, 0x55, 0x47, 0x48, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xe8, 0x0e, 0x12, 0x12,
	0x0a, 0x0d, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x53, 0x4d, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10,
	0xe9, 0x0e, 0x12, 0x2f, 0x0a, 0x2a, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x42, 0x55, 0x49,
	0x4c, 0x44, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x4f, 0x4e,
	0x4e, 0x45, 0x43, 0x54, 0x5f, 0x4d, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xea, 0x0e, 0x12, 0x25, 0x0a, 0x20, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f,
	0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xeb, 0x0e, 0x12, 0x2b, 0x0a, 0x26, 0x50, 0x4f,
	0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x45, 0x52, 0x5f,
	0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xec, 0x0e, 0x12, 0x1c, 0x0a, 0x17, 0x4c, 0x4f, 0x41, 0x4e, 0x53,
	0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0xed, 0x0e, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0xee, 0x0e, 0x12, 0x24, 0x0a, 0x1f, 0x4c,
	0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0xef,
	0x0e, 0x12, 0x20, 0x0a, 0x1b, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x49,
	0x4d, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xf0, 0x0e, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x53, 0x44, 0x4b, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf1, 0x0e,
	0x12, 0x1c, 0x0a, 0x17, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e,
	0x54, 0x5f, 0x56, 0x32, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf2, 0x0e, 0x12, 0x17,
	0x0a, 0x12, 0x41, 0x4d, 0x42, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xf3, 0x0e, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0xf4, 0x0e, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54,
	0x5f, 0x56, 0x32, 0x10, 0xf5, 0x0e, 0x12, 0x20, 0x0a, 0x1b, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44,
	0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x45, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xf6, 0x0e, 0x12, 0x31, 0x0a, 0x2c, 0x49, 0x4e, 0x49, 0x54,
	0x49, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f,
	0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x10, 0xf7, 0x0e, 0x12, 0x21, 0x0a, 0x1c, 0x43,
	0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x55, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0xf8, 0x0e, 0x12, 0x1a,
	0x0a, 0x15, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x53,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xf9, 0x0e, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4f,
	0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x55, 0x53, 0x5f, 0x51, 0x55, 0x45, 0x52, 0x59, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfa, 0x0e, 0x12, 0x29, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e,
	0x53, 0x5f, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54,
	0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0xfb, 0x0e, 0x12, 0x30, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45,
	0x45, 0x54, 0x10, 0xfc, 0x0e, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x43, 0x5f, 0x49, 0x4e, 0x54, 0x52,
	0x4f, 0x5f, 0x56, 0x32, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfd, 0x0e, 0x12, 0x1d,
	0x0a, 0x18, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x45, 0x4c, 0x41, 0x55,
	0x4e, 0x43, 0x48, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xfe, 0x0e, 0x12, 0x26, 0x0a,
	0x21, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4b, 0x45, 0x59, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45,
	0x5f, 0x52, 0x4f, 0x57, 0x53, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45,
	0x45, 0x54, 0x10, 0xff, 0x0e, 0x12, 0x21, 0x0a, 0x1c, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54,
	0x48, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x80, 0x0f, 0x12, 0x13, 0x0a, 0x0e, 0x53, 0x41, 0x5f, 0x44,
	0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x81, 0x0f, 0x12, 0x1d, 0x0a,
	0x18, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x53, 0x44, 0x55, 0x49, 0x5f, 0x42, 0x4f, 0x54,
	0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x82, 0x0f, 0x12, 0x2b, 0x0a, 0x26,
	0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4d, 0x41, 0x49,
	0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0x83, 0x0f, 0x12, 0x20, 0x0a, 0x1b, 0x57, 0x45, 0x42,
	0x5f, 0x44, 0x49, 0x47, 0x49, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x52,
	0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x84, 0x0f, 0x12, 0x28, 0x0a, 0x23, 0x57,
	0x45, 0x42, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e,
	0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x48, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x85, 0x0f, 0x12, 0x19, 0x0a, 0x14, 0x57, 0x45, 0x42, 0x5f, 0x4b, 0x59, 0x43,
	0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x86, 0x0f,
	0x12, 0x28, 0x0a, 0x23, 0x57, 0x45, 0x42, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x49, 0x41, 0x5f, 0x4f, 0x54, 0x50,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x87, 0x0f, 0x12, 0x22, 0x0a, 0x1d, 0x45, 0x41,
	0x52, 0x4e, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x48, 0x49, 0x53,
	0x54, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x88, 0x0f, 0x12, 0x23,
	0x0a, 0x1e, 0x43, 0x43, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x89, 0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f,
	0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x8a, 0x0f, 0x12,
	0x1a, 0x0a, 0x15, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x4e,
	0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x8b, 0x0f, 0x12, 0x25, 0x0a, 0x20, 0x42,
	0x49, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10,
	0x8c, 0x0f, 0x12, 0x29, 0x0a, 0x24, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54,
	0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f,
	0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x8d, 0x0f, 0x12, 0x18, 0x0a,
	0x13, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x43, 0x53, 0x41, 0x54, 0x5f, 0x53, 0x55,
	0x52, 0x56, 0x45, 0x59, 0x10, 0x8e, 0x0f, 0x12, 0x27, 0x0a, 0x22, 0x4e, 0x45, 0x54, 0x57, 0x4f,
	0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x49, 0x44, 0x45, 0x41, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x98, 0x0f,
	0x12, 0x22, 0x0a, 0x1d, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52,
	0x49, 0x43, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e,
	0x54, 0x10, 0x99, 0x0f, 0x12, 0x1e, 0x0a, 0x19, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x44, 0x41,
	0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x56, 0x34, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x9a, 0x0f, 0x12, 0x19, 0x0a, 0x14, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x53, 0x55,
	0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x9b, 0x0f, 0x12,
	0x22, 0x0a, 0x1d, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x54,
	0x41, 0x4c, 0x4b, 0x5f, 0x54, 0x4f, 0x5f, 0x41, 0x49, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x9c, 0x0f, 0x42, 0x5e, 0x0a, 0x2d, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x74, 0x69, 0x63, 0x73, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_analytics_analytics_screen_name_proto_rawDescOnce sync.Once
	file_api_frontend_analytics_analytics_screen_name_proto_rawDescData = file_api_frontend_analytics_analytics_screen_name_proto_rawDesc
)

func file_api_frontend_analytics_analytics_screen_name_proto_rawDescGZIP() []byte {
	file_api_frontend_analytics_analytics_screen_name_proto_rawDescOnce.Do(func() {
		file_api_frontend_analytics_analytics_screen_name_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_analytics_analytics_screen_name_proto_rawDescData)
	})
	return file_api_frontend_analytics_analytics_screen_name_proto_rawDescData
}

var file_api_frontend_analytics_analytics_screen_name_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_frontend_analytics_analytics_screen_name_proto_goTypes = []interface{}{
	(AnalyticsScreenName)(0), // 0: frontend.analytics.AnalyticsScreenName
}
var file_api_frontend_analytics_analytics_screen_name_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_frontend_analytics_analytics_screen_name_proto_init() }
func file_api_frontend_analytics_analytics_screen_name_proto_init() {
	if File_api_frontend_analytics_analytics_screen_name_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_analytics_analytics_screen_name_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_analytics_analytics_screen_name_proto_goTypes,
		DependencyIndexes: file_api_frontend_analytics_analytics_screen_name_proto_depIdxs,
		EnumInfos:         file_api_frontend_analytics_analytics_screen_name_proto_enumTypes,
	}.Build()
	File_api_frontend_analytics_analytics_screen_name_proto = out.File
	file_api_frontend_analytics_analytics_screen_name_proto_rawDesc = nil
	file_api_frontend_analytics_analytics_screen_name_proto_goTypes = nil
	file_api_frontend_analytics_analytics_screen_name_proto_depIdxs = nil
}
