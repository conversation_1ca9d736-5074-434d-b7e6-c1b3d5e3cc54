// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/pay/transaction/service.proto

package transaction

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	account "github.com/epifi/gamma/api/typesv2/account"

	accounts "github.com/epifi/gamma/api/accounts"

	add_funds_v2 "github.com/epifi/gamma/api/frontend/pay/add_funds_v2"

	common "github.com/epifi/be-common/api/typesv2/common"

	pay "github.com/epifi/gamma/api/frontend/pay"

	timeline "github.com/epifi/gamma/api/frontend/deeplink/timeline"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = account.AccountProductOffering(0)

	_ = accounts.Type(0)

	_ = add_funds_v2.UpiApp(0)

	_ = common.BooleanEnum(0)

	_ = pay.PaymentProtocol(0)

	_ = timeline.TransactionUIEntryPoint(0)
)

// Validate checks the field values on GetPaymentOptionsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPaymentOptionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaymentOptionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPaymentOptionsRequestMultiError, or nil if none found.
func (m *GetPaymentOptionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentOptionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionUiEntryPoint

	if len(m.GetOrchestrationMetadata()) > 1000 {
		err := GetPaymentOptionsRequestValidationError{
			field:  "OrchestrationMetadata",
			reason: "value length must be at most 1000 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetPaymentOptionsRequestMultiError(errors)
	}

	return nil
}

// GetPaymentOptionsRequestMultiError is an error wrapping multiple validation
// errors returned by GetPaymentOptionsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPaymentOptionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentOptionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentOptionsRequestMultiError) AllErrors() []error { return m }

// GetPaymentOptionsRequestValidationError is the validation error returned by
// GetPaymentOptionsRequest.Validate if the designated constraints aren't met.
type GetPaymentOptionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentOptionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentOptionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentOptionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentOptionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentOptionsRequestValidationError) ErrorName() string {
	return "GetPaymentOptionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentOptionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentOptionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentOptionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentOptionsRequestValidationError{}

// Validate checks the field values on GetPaymentOptionsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPaymentOptionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaymentOptionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPaymentOptionsResponseMultiError, or nil if none found.
func (m *GetPaymentOptionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentOptionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponseValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPaymentOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPaymentOptionsResponseValidationError{
						field:  fmt.Sprintf("PaymentOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPaymentOptionsResponseValidationError{
						field:  fmt.Sprintf("PaymentOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPaymentOptionsResponseValidationError{
					field:  fmt.Sprintf("PaymentOptions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFooter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponseValidationError{
				field:  "Footer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponseValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetOrchestrationMetadata()) > 1000 {
		err := GetPaymentOptionsResponseValidationError{
			field:  "OrchestrationMetadata",
			reason: "value length must be at most 1000 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPaymentOptionsScreenExpiryTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "PaymentOptionsScreenExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "PaymentOptionsScreenExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentOptionsScreenExpiryTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponseValidationError{
				field:  "PaymentOptionsScreenExpiryTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTopSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "TopSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "TopSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponseValidationError{
				field:  "TopSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "BottomSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "BottomSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponseValidationError{
				field:  "BottomSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDropOffScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "DropOffScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "DropOffScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDropOffScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponseValidationError{
				field:  "DropOffScreen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAdditionalTextBelowCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "AdditionalTextBelowCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponseValidationError{
					field:  "AdditionalTextBelowCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalTextBelowCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponseValidationError{
				field:  "AdditionalTextBelowCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPaymentOptionsResponseMultiError(errors)
	}

	return nil
}

// GetPaymentOptionsResponseMultiError is an error wrapping multiple validation
// errors returned by GetPaymentOptionsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetPaymentOptionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentOptionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentOptionsResponseMultiError) AllErrors() []error { return m }

// GetPaymentOptionsResponseValidationError is the validation error returned by
// GetPaymentOptionsResponse.Validate if the designated constraints aren't met.
type GetPaymentOptionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentOptionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentOptionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentOptionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentOptionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentOptionsResponseValidationError) ErrorName() string {
	return "GetPaymentOptionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentOptionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentOptionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentOptionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentOptionsResponseValidationError{}

// Validate checks the field values on GetAddFundsScreenDetailsV2Request with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAddFundsScreenDetailsV2Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAddFundsScreenDetailsV2Request
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAddFundsScreenDetailsV2RequestMultiError, or nil if none found.
func (m *GetAddFundsScreenDetailsV2Request) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAddFundsScreenDetailsV2Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundsScreenDetailsV2RequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundsScreenDetailsV2RequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundsScreenDetailsV2RequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UiEntryPoint

	// no validation rules for MinorVersion

	if len(errors) > 0 {
		return GetAddFundsScreenDetailsV2RequestMultiError(errors)
	}

	return nil
}

// GetAddFundsScreenDetailsV2RequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAddFundsScreenDetailsV2Request.ValidateAll() if the designated
// constraints aren't met.
type GetAddFundsScreenDetailsV2RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAddFundsScreenDetailsV2RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAddFundsScreenDetailsV2RequestMultiError) AllErrors() []error { return m }

// GetAddFundsScreenDetailsV2RequestValidationError is the validation error
// returned by GetAddFundsScreenDetailsV2Request.Validate if the designated
// constraints aren't met.
type GetAddFundsScreenDetailsV2RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAddFundsScreenDetailsV2RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAddFundsScreenDetailsV2RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAddFundsScreenDetailsV2RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAddFundsScreenDetailsV2RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAddFundsScreenDetailsV2RequestValidationError) ErrorName() string {
	return "GetAddFundsScreenDetailsV2RequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAddFundsScreenDetailsV2RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAddFundsScreenDetailsV2Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAddFundsScreenDetailsV2RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAddFundsScreenDetailsV2RequestValidationError{}

// Validate checks the field values on GetAddFundsScreenDetailsV2Response with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAddFundsScreenDetailsV2Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAddFundsScreenDetailsV2Response
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAddFundsScreenDetailsV2ResponseMultiError, or nil if none found.
func (m *GetAddFundsScreenDetailsV2Response) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAddFundsScreenDetailsV2Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundsScreenDetailsV2ResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundsScreenDetailsV2ResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundsScreenDetailsV2ResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundsScreenDetailsV2ResponseValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundsScreenDetailsV2ResponseValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundsScreenDetailsV2ResponseValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundsScreenDetailsV2ResponseValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundsScreenDetailsV2ResponseValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundsScreenDetailsV2ResponseValidationError{
				field:  "MaxAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDefaultAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundsScreenDetailsV2ResponseValidationError{
					field:  "DefaultAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundsScreenDetailsV2ResponseValidationError{
					field:  "DefaultAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundsScreenDetailsV2ResponseValidationError{
				field:  "DefaultAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IntentNavigateToPayStatus

	// no validation rules for CollectNavigateToPayStatus

	switch v := m.Details.(type) {
	case *GetAddFundsScreenDetailsV2Response_OnboardingAddFundsDetails:
		if v == nil {
			err := GetAddFundsScreenDetailsV2ResponseValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOnboardingAddFundsDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAddFundsScreenDetailsV2ResponseValidationError{
						field:  "OnboardingAddFundsDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAddFundsScreenDetailsV2ResponseValidationError{
						field:  "OnboardingAddFundsDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOnboardingAddFundsDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAddFundsScreenDetailsV2ResponseValidationError{
					field:  "OnboardingAddFundsDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetAddFundsScreenDetailsV2ResponseMultiError(errors)
	}

	return nil
}

// GetAddFundsScreenDetailsV2ResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAddFundsScreenDetailsV2Response.ValidateAll() if the designated
// constraints aren't met.
type GetAddFundsScreenDetailsV2ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAddFundsScreenDetailsV2ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAddFundsScreenDetailsV2ResponseMultiError) AllErrors() []error { return m }

// GetAddFundsScreenDetailsV2ResponseValidationError is the validation error
// returned by GetAddFundsScreenDetailsV2Response.Validate if the designated
// constraints aren't met.
type GetAddFundsScreenDetailsV2ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAddFundsScreenDetailsV2ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAddFundsScreenDetailsV2ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAddFundsScreenDetailsV2ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAddFundsScreenDetailsV2ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAddFundsScreenDetailsV2ResponseValidationError) ErrorName() string {
	return "GetAddFundsScreenDetailsV2ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAddFundsScreenDetailsV2ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAddFundsScreenDetailsV2Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAddFundsScreenDetailsV2ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAddFundsScreenDetailsV2ResponseValidationError{}

// Validate checks the field values on
// ManualBalanceRefreshForOnbAddFundsV2Request with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ManualBalanceRefreshForOnbAddFundsV2Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ManualBalanceRefreshForOnbAddFundsV2Request with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ManualBalanceRefreshForOnbAddFundsV2RequestMultiError, or nil if none found.
func (m *ManualBalanceRefreshForOnbAddFundsV2Request) ValidateAll() error {
	return m.validate(true)
}

func (m *ManualBalanceRefreshForOnbAddFundsV2Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ManualBalanceRefreshForOnbAddFundsV2RequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ManualBalanceRefreshForOnbAddFundsV2RequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ManualBalanceRefreshForOnbAddFundsV2RequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ManualBalanceRefreshForOnbAddFundsV2RequestMultiError(errors)
	}

	return nil
}

// ManualBalanceRefreshForOnbAddFundsV2RequestMultiError is an error wrapping
// multiple validation errors returned by
// ManualBalanceRefreshForOnbAddFundsV2Request.ValidateAll() if the designated
// constraints aren't met.
type ManualBalanceRefreshForOnbAddFundsV2RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManualBalanceRefreshForOnbAddFundsV2RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManualBalanceRefreshForOnbAddFundsV2RequestMultiError) AllErrors() []error { return m }

// ManualBalanceRefreshForOnbAddFundsV2RequestValidationError is the validation
// error returned by ManualBalanceRefreshForOnbAddFundsV2Request.Validate if
// the designated constraints aren't met.
type ManualBalanceRefreshForOnbAddFundsV2RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManualBalanceRefreshForOnbAddFundsV2RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManualBalanceRefreshForOnbAddFundsV2RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManualBalanceRefreshForOnbAddFundsV2RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManualBalanceRefreshForOnbAddFundsV2RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManualBalanceRefreshForOnbAddFundsV2RequestValidationError) ErrorName() string {
	return "ManualBalanceRefreshForOnbAddFundsV2RequestValidationError"
}

// Error satisfies the builtin error interface
func (e ManualBalanceRefreshForOnbAddFundsV2RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManualBalanceRefreshForOnbAddFundsV2Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManualBalanceRefreshForOnbAddFundsV2RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManualBalanceRefreshForOnbAddFundsV2RequestValidationError{}

// Validate checks the field values on
// ManualBalanceRefreshForOnbAddFundsV2Response with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ManualBalanceRefreshForOnbAddFundsV2Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ManualBalanceRefreshForOnbAddFundsV2Response with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ManualBalanceRefreshForOnbAddFundsV2ResponseMultiError, or nil if none found.
func (m *ManualBalanceRefreshForOnbAddFundsV2Response) ValidateAll() error {
	return m.validate(true)
}

func (m *ManualBalanceRefreshForOnbAddFundsV2Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBalanceBelowMinRequiredBottomSheet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError{
					field:  "BalanceBelowMinRequiredBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError{
					field:  "BalanceBelowMinRequiredBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBalanceBelowMinRequiredBottomSheet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError{
				field:  "BalanceBelowMinRequiredBottomSheet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ManualBalanceRefreshForOnbAddFundsV2ResponseMultiError(errors)
	}

	return nil
}

// ManualBalanceRefreshForOnbAddFundsV2ResponseMultiError is an error wrapping
// multiple validation errors returned by
// ManualBalanceRefreshForOnbAddFundsV2Response.ValidateAll() if the
// designated constraints aren't met.
type ManualBalanceRefreshForOnbAddFundsV2ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManualBalanceRefreshForOnbAddFundsV2ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManualBalanceRefreshForOnbAddFundsV2ResponseMultiError) AllErrors() []error { return m }

// ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError is the
// validation error returned by
// ManualBalanceRefreshForOnbAddFundsV2Response.Validate if the designated
// constraints aren't met.
type ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError) ErrorName() string {
	return "ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManualBalanceRefreshForOnbAddFundsV2Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManualBalanceRefreshForOnbAddFundsV2ResponseValidationError{}

// Validate checks the field values on TransactionAttribute with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TransactionAttribute) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactionAttribute with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactionAttributeMultiError, or nil if none found.
func (m *TransactionAttribute) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactionAttribute) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PayerAccountId

	// no validation rules for TransactionId

	// no validation rules for MerchantRefId

	// no validation rules for PaymentProtocol

	// no validation rules for ReferenceUrl

	// no validation rules for PayeeActorName

	// no validation rules for PayerMaskedAccountNumber

	if m.GetAmount() == nil {
		err := TransactionAttributeValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionAttributeValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionAttributeValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionAttributeValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Remarks

	// no validation rules for PayerPaymentInstrument

	// no validation rules for PayeePaymentInstrument

	// no validation rules for DisplayPayeePaymentInstrument

	// no validation rules for RequestId

	// no validation rules for DerivedAccountId

	// no validation rules for PayerUpiNumber

	// no validation rules for PayeeUpiNumber

	if len(errors) > 0 {
		return TransactionAttributeMultiError(errors)
	}

	return nil
}

// TransactionAttributeMultiError is an error wrapping multiple validation
// errors returned by TransactionAttribute.ValidateAll() if the designated
// constraints aren't met.
type TransactionAttributeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactionAttributeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactionAttributeMultiError) AllErrors() []error { return m }

// TransactionAttributeValidationError is the validation error returned by
// TransactionAttribute.Validate if the designated constraints aren't met.
type TransactionAttributeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactionAttributeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactionAttributeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactionAttributeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactionAttributeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactionAttributeValidationError) ErrorName() string {
	return "TransactionAttributeValidationError"
}

// Error satisfies the builtin error interface
func (e TransactionAttributeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactionAttribute.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactionAttributeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactionAttributeValidationError{}

// Validate checks the field values on CreateFundTransferOrderRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateFundTransferOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFundTransferOrderRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateFundTransferOrderRequestMultiError, or nil if none found.
func (m *CreateFundTransferOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFundTransferOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PayerAccountId

	// no validation rules for PayeeActorId

	if all {
		switch v := interface{}(m.GetExecuteAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "ExecuteAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "ExecuteAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecuteAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "ExecuteAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetAmount() == nil {
		err := CreateFundTransferOrderRequestValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if !_CreateFundTransferOrderRequest_Remarks_Pattern.MatchString(m.GetRemarks()) {
		err := CreateFundTransferOrderRequestValidationError{
			field:  "Remarks",
			reason: "value does not match regex pattern \"^[a-zA-Z0-9 ]*$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UpiUrn

	// no validation rules for UiEntryPoint

	// no validation rules for QrData

	// no validation rules for TransactionUiEntryPoint

	// no validation rules for PayerAccountType

	if all {
		switch v := interface{}(m.GetPayerUserIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PayerUserIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PayerUserIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayerUserIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "PayerUserIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayeeUserIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PayeeUserIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PayeeUserIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayeeUserIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "PayeeUserIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBaseAmountQuoteCurrency()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "BaseAmountQuoteCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "BaseAmountQuoteCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseAmountQuoteCurrency()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "BaseAmountQuoteCurrency",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPostPaymentDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PostPaymentDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PostPaymentDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostPaymentDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "PostPaymentDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetOrchestrationMetadata()) > 1000 {
		err := CreateFundTransferOrderRequestValidationError{
			field:  "OrchestrationMetadata",
			reason: "value length must be at most 1000 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPayerUserIdentifierV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PayerUserIdentifierV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PayerUserIdentifierV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayerUserIdentifierV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "PayerUserIdentifierV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayeeUserIdentifierV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PayeeUserIdentifierV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PayeeUserIdentifierV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayeeUserIdentifierV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "PayeeUserIdentifierV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateFundTransferOrderRequestMultiError(errors)
	}

	return nil
}

// CreateFundTransferOrderRequestMultiError is an error wrapping multiple
// validation errors returned by CreateFundTransferOrderRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateFundTransferOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFundTransferOrderRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFundTransferOrderRequestMultiError) AllErrors() []error { return m }

// CreateFundTransferOrderRequestValidationError is the validation error
// returned by CreateFundTransferOrderRequest.Validate if the designated
// constraints aren't met.
type CreateFundTransferOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFundTransferOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFundTransferOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFundTransferOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFundTransferOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFundTransferOrderRequestValidationError) ErrorName() string {
	return "CreateFundTransferOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFundTransferOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFundTransferOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFundTransferOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFundTransferOrderRequestValidationError{}

var _CreateFundTransferOrderRequest_Remarks_Pattern = regexp.MustCompile("^[a-zA-Z0-9 ]*$")

// Validate checks the field values on CreateFundTransferOrderResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateFundTransferOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFundTransferOrderResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateFundTransferOrderResponseMultiError, or nil if none found.
func (m *CreateFundTransferOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFundTransferOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderResponseValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PinRequired

	// no validation rules for OrderId

	if all {
		switch v := interface{}(m.GetTransactionAttribute()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "TransactionAttribute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "TransactionAttribute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionAttribute()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderResponseValidationError{
				field:  "TransactionAttribute",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetErrorView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrorView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderResponseValidationError{
				field:  "ErrorView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCooldownBottomSheet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "CooldownBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "CooldownBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCooldownBottomSheet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderResponseValidationError{
				field:  "CooldownBottomSheet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionInfo

	if len(errors) > 0 {
		return CreateFundTransferOrderResponseMultiError(errors)
	}

	return nil
}

// CreateFundTransferOrderResponseMultiError is an error wrapping multiple
// validation errors returned by CreateFundTransferOrderResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateFundTransferOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFundTransferOrderResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFundTransferOrderResponseMultiError) AllErrors() []error { return m }

// CreateFundTransferOrderResponseValidationError is the validation error
// returned by CreateFundTransferOrderResponse.Validate if the designated
// constraints aren't met.
type CreateFundTransferOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFundTransferOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFundTransferOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFundTransferOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFundTransferOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFundTransferOrderResponseValidationError) ErrorName() string {
	return "CreateFundTransferOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFundTransferOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFundTransferOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFundTransferOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFundTransferOrderResponseValidationError{}

// Validate checks the field values on CreateP2PCollectOrderRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateP2PCollectOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateP2PCollectOrderRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateP2PCollectOrderRequestMultiError, or nil if none found.
func (m *CreateP2PCollectOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateP2PCollectOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateP2PCollectOrderRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateP2PCollectOrderRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PayeeAccountId

	// no validation rules for PayerActorId

	if m.GetAmount() == nil {
		err := CreateP2PCollectOrderRequestValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateP2PCollectOrderRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if !_CreateP2PCollectOrderRequest_Remarks_Pattern.MatchString(m.GetRemarks()) {
		err := CreateP2PCollectOrderRequestValidationError{
			field:  "Remarks",
			reason: "value does not match regex pattern \"^[a-zA-Z0-9 ]*$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UiEntryPoint

	// no validation rules for IsAddFunds

	// no validation rules for TransactionUiEntryPoint

	// no validation rules for PayeeAccountType

	if all {
		switch v := interface{}(m.GetPayerUserIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "PayerUserIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "PayerUserIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayerUserIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateP2PCollectOrderRequestValidationError{
				field:  "PayerUserIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayeeUserIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "PayeeUserIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "PayeeUserIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayeeUserIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateP2PCollectOrderRequestValidationError{
				field:  "PayeeUserIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayerUserIdentifierV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "PayerUserIdentifierV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "PayerUserIdentifierV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayerUserIdentifierV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateP2PCollectOrderRequestValidationError{
				field:  "PayerUserIdentifierV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayeeUserIdentifierV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "PayeeUserIdentifierV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateP2PCollectOrderRequestValidationError{
					field:  "PayeeUserIdentifierV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayeeUserIdentifierV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateP2PCollectOrderRequestValidationError{
				field:  "PayeeUserIdentifierV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateP2PCollectOrderRequestMultiError(errors)
	}

	return nil
}

// CreateP2PCollectOrderRequestMultiError is an error wrapping multiple
// validation errors returned by CreateP2PCollectOrderRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateP2PCollectOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateP2PCollectOrderRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateP2PCollectOrderRequestMultiError) AllErrors() []error { return m }

// CreateP2PCollectOrderRequestValidationError is the validation error returned
// by CreateP2PCollectOrderRequest.Validate if the designated constraints
// aren't met.
type CreateP2PCollectOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateP2PCollectOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateP2PCollectOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateP2PCollectOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateP2PCollectOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateP2PCollectOrderRequestValidationError) ErrorName() string {
	return "CreateP2PCollectOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateP2PCollectOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateP2PCollectOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateP2PCollectOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateP2PCollectOrderRequestValidationError{}

var _CreateP2PCollectOrderRequest_Remarks_Pattern = regexp.MustCompile("^[a-zA-Z0-9 ]*$")

// Validate checks the field values on CreateP2PCollectOrderResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateP2PCollectOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateP2PCollectOrderResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateP2PCollectOrderResponseMultiError, or nil if none found.
func (m *CreateP2PCollectOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateP2PCollectOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateP2PCollectOrderResponseValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateP2PCollectOrderResponseValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateP2PCollectOrderResponseValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateP2PCollectOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateP2PCollectOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateP2PCollectOrderResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	if all {
		switch v := interface{}(m.GetTransactionAttribute()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateP2PCollectOrderResponseValidationError{
					field:  "TransactionAttribute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateP2PCollectOrderResponseValidationError{
					field:  "TransactionAttribute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionAttribute()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateP2PCollectOrderResponseValidationError{
				field:  "TransactionAttribute",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetErrorView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateP2PCollectOrderResponseValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateP2PCollectOrderResponseValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrorView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateP2PCollectOrderResponseValidationError{
				field:  "ErrorView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateP2PCollectOrderResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateP2PCollectOrderResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateP2PCollectOrderResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateP2PCollectOrderResponseMultiError(errors)
	}

	return nil
}

// CreateP2PCollectOrderResponseMultiError is an error wrapping multiple
// validation errors returned by CreateP2PCollectOrderResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateP2PCollectOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateP2PCollectOrderResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateP2PCollectOrderResponseMultiError) AllErrors() []error { return m }

// CreateP2PCollectOrderResponseValidationError is the validation error
// returned by CreateP2PCollectOrderResponse.Validate if the designated
// constraints aren't met.
type CreateP2PCollectOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateP2PCollectOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateP2PCollectOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateP2PCollectOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateP2PCollectOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateP2PCollectOrderResponseValidationError) ErrorName() string {
	return "CreateP2PCollectOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateP2PCollectOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateP2PCollectOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateP2PCollectOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateP2PCollectOrderResponseValidationError{}

// Validate checks the field values on InitiatePaymentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiatePaymentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiatePaymentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiatePaymentRequestMultiError, or nil if none found.
func (m *InitiatePaymentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiatePaymentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePaymentRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePaymentRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePaymentRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePaymentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePaymentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePaymentRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := utf8.RuneCountInString(m.GetOrderId()); l < 4 || l > 35 {
		err := InitiatePaymentRequestValidationError{
			field:  "OrderId",
			reason: "value length must be between 4 and 35 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetTransactionId()); l < 1 || l > 35 {
		err := InitiatePaymentRequestValidationError{
			field:  "TransactionId",
			reason: "value length must be between 1 and 35 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCredBlock()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePaymentRequestValidationError{
					field:  "CredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePaymentRequestValidationError{
					field:  "CredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCredBlock()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePaymentRequestValidationError{
				field:  "CredBlock",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetMerchantRefId()) > 35 {
		err := InitiatePaymentRequestValidationError{
			field:  "MerchantRefId",
			reason: "value length must be at most 35 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _InitiatePaymentRequest_PreferredPaymentProtocol_NotInLookup[m.GetPreferredPaymentProtocol()]; ok {
		err := InitiatePaymentRequestValidationError{
			field:  "PreferredPaymentProtocol",
			reason: "value must not be in list [PAYMENT_PROTOCOL_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetReferenceUrl()) > 35 {
		err := InitiatePaymentRequestValidationError{
			field:  "ReferenceUrl",
			reason: "value length must be at most 35 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PayerAccountId

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePaymentRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePaymentRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePaymentRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBaseAmountQuoteCurrency()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePaymentRequestValidationError{
					field:  "BaseAmountQuoteCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePaymentRequestValidationError{
					field:  "BaseAmountQuoteCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseAmountQuoteCurrency()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePaymentRequestValidationError{
				field:  "BaseAmountQuoteCurrency",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Urn

	// no validation rules for PayerUpiNumber

	// no validation rules for PayeeUpiNumber

	// no validation rules for OrchestrationMetadata

	switch v := m.Credential.(type) {
	case *InitiatePaymentRequest_PartnerSdkCredBlock:
		if v == nil {
			err := InitiatePaymentRequestValidationError{
				field:  "Credential",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PartnerSdkCredBlock
	case *InitiatePaymentRequest_NpciCredBlock:
		if v == nil {
			err := InitiatePaymentRequestValidationError{
				field:  "Credential",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNpciCredBlock()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InitiatePaymentRequestValidationError{
						field:  "NpciCredBlock",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InitiatePaymentRequestValidationError{
						field:  "NpciCredBlock",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNpciCredBlock()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InitiatePaymentRequestValidationError{
					field:  "NpciCredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InitiatePaymentRequest_NpciCredBlocks:
		if v == nil {
			err := InitiatePaymentRequestValidationError{
				field:  "Credential",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNpciCredBlocks()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InitiatePaymentRequestValidationError{
						field:  "NpciCredBlocks",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InitiatePaymentRequestValidationError{
						field:  "NpciCredBlocks",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNpciCredBlocks()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InitiatePaymentRequestValidationError{
					field:  "NpciCredBlocks",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return InitiatePaymentRequestMultiError(errors)
	}

	return nil
}

// InitiatePaymentRequestMultiError is an error wrapping multiple validation
// errors returned by InitiatePaymentRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiatePaymentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiatePaymentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiatePaymentRequestMultiError) AllErrors() []error { return m }

// InitiatePaymentRequestValidationError is the validation error returned by
// InitiatePaymentRequest.Validate if the designated constraints aren't met.
type InitiatePaymentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiatePaymentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiatePaymentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiatePaymentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiatePaymentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiatePaymentRequestValidationError) ErrorName() string {
	return "InitiatePaymentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiatePaymentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiatePaymentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiatePaymentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiatePaymentRequestValidationError{}

var _InitiatePaymentRequest_PreferredPaymentProtocol_NotInLookup = map[pay.PaymentProtocol]struct{}{
	0: {},
}

// Validate checks the field values on InitiatePaymentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiatePaymentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiatePaymentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiatePaymentResponseMultiError, or nil if none found.
func (m *InitiatePaymentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiatePaymentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePaymentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatusTimer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePaymentResponseValidationError{
					field:  "StatusTimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePaymentResponseValidationError{
					field:  "StatusTimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusTimer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePaymentResponseValidationError{
				field:  "StatusTimer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetErrorView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePaymentResponseValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePaymentResponseValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrorView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePaymentResponseValidationError{
				field:  "ErrorView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePaymentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePaymentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePaymentResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiatePaymentResponseMultiError(errors)
	}

	return nil
}

// InitiatePaymentResponseMultiError is an error wrapping multiple validation
// errors returned by InitiatePaymentResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiatePaymentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiatePaymentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiatePaymentResponseMultiError) AllErrors() []error { return m }

// InitiatePaymentResponseValidationError is the validation error returned by
// InitiatePaymentResponse.Validate if the designated constraints aren't met.
type InitiatePaymentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiatePaymentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiatePaymentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiatePaymentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiatePaymentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiatePaymentResponseValidationError) ErrorName() string {
	return "InitiatePaymentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiatePaymentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiatePaymentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiatePaymentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiatePaymentResponseValidationError{}

// Validate checks the field values on GetOrderStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrderStatusRequestMultiError, or nil if none found.
func (m *GetOrderStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderStatusRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderStatusRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderStatusRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderStatusRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	// no validation rules for OrchestrationMetadata

	if len(errors) > 0 {
		return GetOrderStatusRequestMultiError(errors)
	}

	return nil
}

// GetOrderStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetOrderStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOrderStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderStatusRequestMultiError) AllErrors() []error { return m }

// GetOrderStatusRequestValidationError is the validation error returned by
// GetOrderStatusRequest.Validate if the designated constraints aren't met.
type GetOrderStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderStatusRequestValidationError) ErrorName() string {
	return "GetOrderStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderStatusRequestValidationError{}

// Validate checks the field values on GetOrderStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrderStatusResponseMultiError, or nil if none found.
func (m *GetOrderStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderStatusResponseValidationError{
				field:  "Order",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRetryTimer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "RetryTimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "RetryTimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRetryTimer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderStatusResponseValidationError{
				field:  "RetryTimer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetErrorView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrorView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderStatusResponseValidationError{
				field:  "ErrorView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIntentResponseParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "IntentResponseParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "IntentResponseParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIntentResponseParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderStatusResponseValidationError{
				field:  "IntentResponseParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderStatusResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTxnCategories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrderStatusResponseValidationError{
						field:  fmt.Sprintf("TxnCategories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrderStatusResponseValidationError{
						field:  fmt.Sprintf("TxnCategories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrderStatusResponseValidationError{
					field:  fmt.Sprintf("TxnCategories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsCategoryEditable

	if len(errors) > 0 {
		return GetOrderStatusResponseMultiError(errors)
	}

	return nil
}

// GetOrderStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetOrderStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetOrderStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderStatusResponseMultiError) AllErrors() []error { return m }

// GetOrderStatusResponseValidationError is the validation error returned by
// GetOrderStatusResponse.Validate if the designated constraints aren't met.
type GetOrderStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderStatusResponseValidationError) ErrorName() string {
	return "GetOrderStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderStatusResponseValidationError{}

// Validate checks the field values on GetCollectOrderTransactionDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCollectOrderTransactionDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCollectOrderTransactionDetailsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetCollectOrderTransactionDetailsRequestMultiError, or nil if none found.
func (m *GetCollectOrderTransactionDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCollectOrderTransactionDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCollectOrderTransactionDetailsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCollectOrderTransactionDetailsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCollectOrderTransactionDetailsRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCollectOrderTransactionDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCollectOrderTransactionDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCollectOrderTransactionDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := utf8.RuneCountInString(m.GetOrderId()); l < 1 || l > 35 {
		err := GetCollectOrderTransactionDetailsRequestValidationError{
			field:  "OrderId",
			reason: "value length must be between 1 and 35 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCollectOrderTransactionDetailsRequestMultiError(errors)
	}

	return nil
}

// GetCollectOrderTransactionDetailsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetCollectOrderTransactionDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCollectOrderTransactionDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCollectOrderTransactionDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCollectOrderTransactionDetailsRequestMultiError) AllErrors() []error { return m }

// GetCollectOrderTransactionDetailsRequestValidationError is the validation
// error returned by GetCollectOrderTransactionDetailsRequest.Validate if the
// designated constraints aren't met.
type GetCollectOrderTransactionDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCollectOrderTransactionDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCollectOrderTransactionDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCollectOrderTransactionDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCollectOrderTransactionDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCollectOrderTransactionDetailsRequestValidationError) ErrorName() string {
	return "GetCollectOrderTransactionDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCollectOrderTransactionDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCollectOrderTransactionDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCollectOrderTransactionDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCollectOrderTransactionDetailsRequestValidationError{}

// Validate checks the field values on
// GetCollectOrderTransactionDetailsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetCollectOrderTransactionDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCollectOrderTransactionDetailsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetCollectOrderTransactionDetailsResponseMultiError, or nil if none found.
func (m *GetCollectOrderTransactionDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCollectOrderTransactionDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCollectOrderTransactionDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCollectOrderTransactionDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCollectOrderTransactionDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PinRequired

	for idx, item := range m.GetTransactionAttributes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCollectOrderTransactionDetailsResponseValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCollectOrderTransactionDetailsResponseValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCollectOrderTransactionDetailsResponseValidationError{
					field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCollectOrderTransactionDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCollectOrderTransactionDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCollectOrderTransactionDetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCollectOrderTransactionDetailsResponseMultiError(errors)
	}

	return nil
}

// GetCollectOrderTransactionDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetCollectOrderTransactionDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCollectOrderTransactionDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCollectOrderTransactionDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCollectOrderTransactionDetailsResponseMultiError) AllErrors() []error { return m }

// GetCollectOrderTransactionDetailsResponseValidationError is the validation
// error returned by GetCollectOrderTransactionDetailsResponse.Validate if the
// designated constraints aren't met.
type GetCollectOrderTransactionDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCollectOrderTransactionDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCollectOrderTransactionDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCollectOrderTransactionDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCollectOrderTransactionDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCollectOrderTransactionDetailsResponseValidationError) ErrorName() string {
	return "GetCollectOrderTransactionDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCollectOrderTransactionDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCollectOrderTransactionDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCollectOrderTransactionDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCollectOrderTransactionDetailsResponseValidationError{}

// Validate checks the field values on GetEligibleAccountsForPaymentRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetEligibleAccountsForPaymentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEligibleAccountsForPaymentRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetEligibleAccountsForPaymentRequestMultiError, or nil if none found.
func (m *GetEligibleAccountsForPaymentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEligibleAccountsForPaymentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEligibleAccountsForPaymentRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEligibleAccountsForPaymentRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PaymentScope

	if len(errors) > 0 {
		return GetEligibleAccountsForPaymentRequestMultiError(errors)
	}

	return nil
}

// GetEligibleAccountsForPaymentRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetEligibleAccountsForPaymentRequest.ValidateAll() if the designated
// constraints aren't met.
type GetEligibleAccountsForPaymentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEligibleAccountsForPaymentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEligibleAccountsForPaymentRequestMultiError) AllErrors() []error { return m }

// GetEligibleAccountsForPaymentRequestValidationError is the validation error
// returned by GetEligibleAccountsForPaymentRequest.Validate if the designated
// constraints aren't met.
type GetEligibleAccountsForPaymentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEligibleAccountsForPaymentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEligibleAccountsForPaymentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEligibleAccountsForPaymentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEligibleAccountsForPaymentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEligibleAccountsForPaymentRequestValidationError) ErrorName() string {
	return "GetEligibleAccountsForPaymentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEligibleAccountsForPaymentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEligibleAccountsForPaymentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEligibleAccountsForPaymentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEligibleAccountsForPaymentRequestValidationError{}

// Validate checks the field values on GetEligibleAccountsForPaymentResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetEligibleAccountsForPaymentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEligibleAccountsForPaymentResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetEligibleAccountsForPaymentResponseMultiError, or nil if none found.
func (m *GetEligibleAccountsForPaymentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEligibleAccountsForPaymentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEligibleAccountsForPaymentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountAttributes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetEligibleAccountsForPaymentResponseValidationError{
						field:  fmt.Sprintf("AccountAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetEligibleAccountsForPaymentResponseValidationError{
						field:  fmt.Sprintf("AccountAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetEligibleAccountsForPaymentResponseValidationError{
					field:  fmt.Sprintf("AccountAttributes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEligibleAccountsForPaymentResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEligibleAccountsForPaymentResponseMultiError(errors)
	}

	return nil
}

// GetEligibleAccountsForPaymentResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetEligibleAccountsForPaymentResponse.ValidateAll() if the designated
// constraints aren't met.
type GetEligibleAccountsForPaymentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEligibleAccountsForPaymentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEligibleAccountsForPaymentResponseMultiError) AllErrors() []error { return m }

// GetEligibleAccountsForPaymentResponseValidationError is the validation error
// returned by GetEligibleAccountsForPaymentResponse.Validate if the
// designated constraints aren't met.
type GetEligibleAccountsForPaymentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEligibleAccountsForPaymentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEligibleAccountsForPaymentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEligibleAccountsForPaymentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEligibleAccountsForPaymentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEligibleAccountsForPaymentResponseValidationError) ErrorName() string {
	return "GetEligibleAccountsForPaymentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEligibleAccountsForPaymentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEligibleAccountsForPaymentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEligibleAccountsForPaymentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEligibleAccountsForPaymentResponseValidationError{}

// Validate checks the field values on InitiateCollectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateCollectRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateCollectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateCollectRequestMultiError, or nil if none found.
func (m *InitiateCollectRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateCollectRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCollectRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCollectRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCollectRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCollectRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCollectRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCollectRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := utf8.RuneCountInString(m.GetOrderId()); l < 4 || l > 35 {
		err := InitiateCollectRequestValidationError{
			field:  "OrderId",
			reason: "value length must be between 4 and 35 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return InitiateCollectRequestMultiError(errors)
	}

	return nil
}

// InitiateCollectRequestMultiError is an error wrapping multiple validation
// errors returned by InitiateCollectRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateCollectRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateCollectRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateCollectRequestMultiError) AllErrors() []error { return m }

// InitiateCollectRequestValidationError is the validation error returned by
// InitiateCollectRequest.Validate if the designated constraints aren't met.
type InitiateCollectRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateCollectRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateCollectRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateCollectRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateCollectRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateCollectRequestValidationError) ErrorName() string {
	return "InitiateCollectRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateCollectRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateCollectRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateCollectRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateCollectRequestValidationError{}

// Validate checks the field values on InitiateCollectResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateCollectResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateCollectResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateCollectResponseMultiError, or nil if none found.
func (m *InitiateCollectResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateCollectResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCollectResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCollectResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCollectResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatusTimer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCollectResponseValidationError{
					field:  "StatusTimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCollectResponseValidationError{
					field:  "StatusTimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusTimer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCollectResponseValidationError{
				field:  "StatusTimer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetErrorView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCollectResponseValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCollectResponseValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrorView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCollectResponseValidationError{
				field:  "ErrorView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCollectResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCollectResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCollectResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateCollectResponseMultiError(errors)
	}

	return nil
}

// InitiateCollectResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateCollectResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateCollectResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateCollectResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateCollectResponseMultiError) AllErrors() []error { return m }

// InitiateCollectResponseValidationError is the validation error returned by
// InitiateCollectResponse.Validate if the designated constraints aren't met.
type InitiateCollectResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateCollectResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateCollectResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateCollectResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateCollectResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateCollectResponseValidationError) ErrorName() string {
	return "InitiateCollectResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateCollectResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateCollectResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateCollectResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateCollectResponseValidationError{}

// Validate checks the field values on DismissCollectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DismissCollectRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DismissCollectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DismissCollectRequestMultiError, or nil if none found.
func (m *DismissCollectRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DismissCollectRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DismissCollectRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DismissCollectRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DismissCollectRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DismissCollectRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DismissCollectRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DismissCollectRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	if len(errors) > 0 {
		return DismissCollectRequestMultiError(errors)
	}

	return nil
}

// DismissCollectRequestMultiError is an error wrapping multiple validation
// errors returned by DismissCollectRequest.ValidateAll() if the designated
// constraints aren't met.
type DismissCollectRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DismissCollectRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DismissCollectRequestMultiError) AllErrors() []error { return m }

// DismissCollectRequestValidationError is the validation error returned by
// DismissCollectRequest.Validate if the designated constraints aren't met.
type DismissCollectRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DismissCollectRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DismissCollectRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DismissCollectRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DismissCollectRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DismissCollectRequestValidationError) ErrorName() string {
	return "DismissCollectRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DismissCollectRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDismissCollectRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DismissCollectRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DismissCollectRequestValidationError{}

// Validate checks the field values on DismissCollectResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DismissCollectResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DismissCollectResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DismissCollectResponseMultiError, or nil if none found.
func (m *DismissCollectResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DismissCollectResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DismissCollectResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DismissCollectResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DismissCollectResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DismissCollectResponseValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DismissCollectResponseValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DismissCollectResponseValidationError{
				field:  "Order",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DismissCollectResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DismissCollectResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DismissCollectResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DismissCollectResponseMultiError(errors)
	}

	return nil
}

// DismissCollectResponseMultiError is an error wrapping multiple validation
// errors returned by DismissCollectResponse.ValidateAll() if the designated
// constraints aren't met.
type DismissCollectResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DismissCollectResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DismissCollectResponseMultiError) AllErrors() []error { return m }

// DismissCollectResponseValidationError is the validation error returned by
// DismissCollectResponse.Validate if the designated constraints aren't met.
type DismissCollectResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DismissCollectResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DismissCollectResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DismissCollectResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DismissCollectResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DismissCollectResponseValidationError) ErrorName() string {
	return "DismissCollectResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DismissCollectResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDismissCollectResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DismissCollectResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DismissCollectResponseValidationError{}

// Validate checks the field values on CreateURNOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateURNOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateURNOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateURNOrderRequestMultiError, or nil if none found.
func (m *CreateURNOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateURNOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateURNOrderRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateURNOrderRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateURNOrderRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateURNOrderRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateURNOrderRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateURNOrderRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PayeeAccountId

	if m.GetAmount() == nil {
		err := CreateURNOrderRequestValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateURNOrderRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateURNOrderRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateURNOrderRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InitiationMode

	// no validation rules for UiEntryPoint

	// no validation rules for TransactionUiEntryPoint

	// no validation rules for UpiApp

	if len(errors) > 0 {
		return CreateURNOrderRequestMultiError(errors)
	}

	return nil
}

// CreateURNOrderRequestMultiError is an error wrapping multiple validation
// errors returned by CreateURNOrderRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateURNOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateURNOrderRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateURNOrderRequestMultiError) AllErrors() []error { return m }

// CreateURNOrderRequestValidationError is the validation error returned by
// CreateURNOrderRequest.Validate if the designated constraints aren't met.
type CreateURNOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateURNOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateURNOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateURNOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateURNOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateURNOrderRequestValidationError) ErrorName() string {
	return "CreateURNOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateURNOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateURNOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateURNOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateURNOrderRequestValidationError{}

// Validate checks the field values on CreateURNOrderResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateURNOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateURNOrderResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateURNOrderResponseMultiError, or nil if none found.
func (m *CreateURNOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateURNOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateURNOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateURNOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateURNOrderResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	// no validation rules for Urn

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateURNOrderResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateURNOrderResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateURNOrderResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateURNOrderResponseMultiError(errors)
	}

	return nil
}

// CreateURNOrderResponseMultiError is an error wrapping multiple validation
// errors returned by CreateURNOrderResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateURNOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateURNOrderResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateURNOrderResponseMultiError) AllErrors() []error { return m }

// CreateURNOrderResponseValidationError is the validation error returned by
// CreateURNOrderResponse.Validate if the designated constraints aren't met.
type CreateURNOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateURNOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateURNOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateURNOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateURNOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateURNOrderResponseValidationError) ErrorName() string {
	return "CreateURNOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateURNOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateURNOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateURNOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateURNOrderResponseValidationError{}

// Validate checks the field values on GetOrderReceiptRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderReceiptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderReceiptRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrderReceiptRequestMultiError, or nil if none found.
func (m *GetOrderReceiptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderReceiptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	switch v := m.Identifier.(type) {
	case *GetOrderReceiptRequest_OrdersId:
		if v == nil {
			err := GetOrderReceiptRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for OrdersId
	case *GetOrderReceiptRequest_AaTxnId:
		if v == nil {
			err := GetOrderReceiptRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for AaTxnId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetOrderReceiptRequestMultiError(errors)
	}

	return nil
}

// GetOrderReceiptRequestMultiError is an error wrapping multiple validation
// errors returned by GetOrderReceiptRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOrderReceiptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderReceiptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderReceiptRequestMultiError) AllErrors() []error { return m }

// GetOrderReceiptRequestValidationError is the validation error returned by
// GetOrderReceiptRequest.Validate if the designated constraints aren't met.
type GetOrderReceiptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderReceiptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderReceiptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderReceiptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderReceiptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderReceiptRequestValidationError) ErrorName() string {
	return "GetOrderReceiptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderReceiptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderReceiptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderReceiptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderReceiptRequestValidationError{}

// Validate checks the field values on GetOrderReceiptResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderReceiptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderReceiptResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrderReceiptResponseMultiError, or nil if none found.
func (m *GetOrderReceiptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderReceiptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Category

	if all {
		switch v := interface{}(m.GetOverallStage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "OverallStage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "OverallStage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOverallStage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "OverallStage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOrderStages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrderReceiptResponseValidationError{
						field:  fmt.Sprintf("OrderStages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrderReceiptResponseValidationError{
						field:  fmt.Sprintf("OrderStages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrderReceiptResponseValidationError{
					field:  fmt.Sprintf("OrderStages[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrderReceiptResponseValidationError{
						field:  fmt.Sprintf("Details[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrderReceiptResponseValidationError{
						field:  fmt.Sprintf("Details[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrderReceiptResponseValidationError{
					field:  fmt.Sprintf("Details[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRefundAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "RefundAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "RefundAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRefundAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "RefundAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsRaiseDisputeEnabled

	// no validation rules for DisputeStatusInfo

	if all {
		switch v := interface{}(m.GetReceiptHead()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "ReceiptHead",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "ReceiptHead",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReceiptHead()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "ReceiptHead",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionId

	if all {
		switch v := interface{}(m.GetErrorView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrorView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "ErrorView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderExternalId

	// no validation rules for PaymentProtocol

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReceiptInfo

	for idx, item := range m.GetTxnCategories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrderReceiptResponseValidationError{
						field:  fmt.Sprintf("TxnCategories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrderReceiptResponseValidationError{
						field:  fmt.Sprintf("TxnCategories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrderReceiptResponseValidationError{
					field:  fmt.Sprintf("TxnCategories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetInapphelpMediaParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "InapphelpMediaParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "InapphelpMediaParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInapphelpMediaParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "InapphelpMediaParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CanUpdateCategory

	for idx, item := range m.GetFooterCtas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrderReceiptResponseValidationError{
						field:  fmt.Sprintf("FooterCtas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrderReceiptResponseValidationError{
						field:  fmt.Sprintf("FooterCtas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrderReceiptResponseValidationError{
					field:  fmt.Sprintf("FooterCtas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFooterMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "FooterMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "FooterMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooterMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "FooterMessage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserCaution()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "UserCaution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "UserCaution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserCaution()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "UserCaution",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExpandOrderStage

	for idx, item := range m.GetSuggestedCategories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrderReceiptResponseValidationError{
						field:  fmt.Sprintf("SuggestedCategories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrderReceiptResponseValidationError{
						field:  fmt.Sprintf("SuggestedCategories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrderReceiptResponseValidationError{
					field:  fmt.Sprintf("SuggestedCategories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetTiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrderReceiptResponseValidationError{
						field:  fmt.Sprintf("Tiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrderReceiptResponseValidationError{
						field:  fmt.Sprintf("Tiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrderReceiptResponseValidationError{
					field:  fmt.Sprintf("Tiles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRewardDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "RewardDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "RewardDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "RewardDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPartnerTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "PartnerTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "PartnerTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPartnerTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "PartnerTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReceiptErrorInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "ReceiptErrorInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "ReceiptErrorInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReceiptErrorInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "ReceiptErrorInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAssociatedTransaction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "AssociatedTransaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "AssociatedTransaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAssociatedTransaction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "AssociatedTransaction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOrderReceiptResponseMultiError(errors)
	}

	return nil
}

// GetOrderReceiptResponseMultiError is an error wrapping multiple validation
// errors returned by GetOrderReceiptResponse.ValidateAll() if the designated
// constraints aren't met.
type GetOrderReceiptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderReceiptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderReceiptResponseMultiError) AllErrors() []error { return m }

// GetOrderReceiptResponseValidationError is the validation error returned by
// GetOrderReceiptResponse.Validate if the designated constraints aren't met.
type GetOrderReceiptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderReceiptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderReceiptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderReceiptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderReceiptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderReceiptResponseValidationError) ErrorName() string {
	return "GetOrderReceiptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderReceiptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderReceiptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderReceiptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderReceiptResponseValidationError{}

// Validate checks the field values on RewardDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RewardDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RewardDetailsMultiError, or
// nil if none found.
func (m *RewardDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardDetailsValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardDetailsValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardDetailsValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomSheet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardDetailsValidationError{
					field:  "BottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardDetailsValidationError{
					field:  "BottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomSheet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardDetailsValidationError{
				field:  "BottomSheet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardDetailsMultiError(errors)
	}

	return nil
}

// RewardDetailsMultiError is an error wrapping multiple validation errors
// returned by RewardDetails.ValidateAll() if the designated constraints
// aren't met.
type RewardDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardDetailsMultiError) AllErrors() []error { return m }

// RewardDetailsValidationError is the validation error returned by
// RewardDetails.Validate if the designated constraints aren't met.
type RewardDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardDetailsValidationError) ErrorName() string { return "RewardDetailsValidationError" }

// Error satisfies the builtin error interface
func (e RewardDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardDetailsValidationError{}

// Validate checks the field values on RewardSummarySection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardSummarySection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardSummarySection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardSummarySectionMultiError, or nil if none found.
func (m *RewardSummarySection) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardSummarySection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardSummarySectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardSummarySectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardSummarySectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardSummarySectionValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardSummarySectionValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardSummarySectionValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRewardValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardSummarySectionValidationError{
						field:  fmt.Sprintf("RewardValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardSummarySectionValidationError{
						field:  fmt.Sprintf("RewardValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardSummarySectionValidationError{
					field:  fmt.Sprintf("RewardValues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFooterInfoTextComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardSummarySectionValidationError{
					field:  "FooterInfoTextComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardSummarySectionValidationError{
					field:  "FooterInfoTextComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooterInfoTextComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardSummarySectionValidationError{
				field:  "FooterInfoTextComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardSummarySectionMultiError(errors)
	}

	return nil
}

// RewardSummarySectionMultiError is an error wrapping multiple validation
// errors returned by RewardSummarySection.ValidateAll() if the designated
// constraints aren't met.
type RewardSummarySectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardSummarySectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardSummarySectionMultiError) AllErrors() []error { return m }

// RewardSummarySectionValidationError is the validation error returned by
// RewardSummarySection.Validate if the designated constraints aren't met.
type RewardSummarySectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardSummarySectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardSummarySectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardSummarySectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardSummarySectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardSummarySectionValidationError) ErrorName() string {
	return "RewardSummarySectionValidationError"
}

// Error satisfies the builtin error interface
func (e RewardSummarySectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardSummarySection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardSummarySectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardSummarySectionValidationError{}

// Validate checks the field values on RewardDetailsBottomSheet with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardDetailsBottomSheet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardDetailsBottomSheet with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardDetailsBottomSheetMultiError, or nil if none found.
func (m *RewardDetailsBottomSheet) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardDetailsBottomSheet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardDetailsBottomSheetValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardDetailsBottomSheetValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardDetailsBottomSheetValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRewardDetailsCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardDetailsBottomSheetValidationError{
						field:  fmt.Sprintf("RewardDetailsCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardDetailsBottomSheetValidationError{
						field:  fmt.Sprintf("RewardDetailsCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardDetailsBottomSheetValidationError{
					field:  fmt.Sprintf("RewardDetailsCards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardDetailsBottomSheetValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardDetailsBottomSheetValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardDetailsBottomSheetValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomSheetInfoText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardDetailsBottomSheetValidationError{
					field:  "BottomSheetInfoText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardDetailsBottomSheetValidationError{
					field:  "BottomSheetInfoText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomSheetInfoText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardDetailsBottomSheetValidationError{
				field:  "BottomSheetInfoText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardDetailsBottomSheetValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardDetailsBottomSheetValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardDetailsBottomSheetValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardDetailsBottomSheetMultiError(errors)
	}

	return nil
}

// RewardDetailsBottomSheetMultiError is an error wrapping multiple validation
// errors returned by RewardDetailsBottomSheet.ValidateAll() if the designated
// constraints aren't met.
type RewardDetailsBottomSheetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardDetailsBottomSheetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardDetailsBottomSheetMultiError) AllErrors() []error { return m }

// RewardDetailsBottomSheetValidationError is the validation error returned by
// RewardDetailsBottomSheet.Validate if the designated constraints aren't met.
type RewardDetailsBottomSheetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardDetailsBottomSheetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardDetailsBottomSheetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardDetailsBottomSheetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardDetailsBottomSheetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardDetailsBottomSheetValidationError) ErrorName() string {
	return "RewardDetailsBottomSheetValidationError"
}

// Error satisfies the builtin error interface
func (e RewardDetailsBottomSheetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardDetailsBottomSheet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardDetailsBottomSheetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardDetailsBottomSheetValidationError{}

// Validate checks the field values on Tile with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Tile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Tile with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TileMultiError, or nil if none found.
func (m *Tile) ValidateAll() error {
	return m.validate(true)
}

func (m *Tile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.DisplayTile.(type) {
	case *Tile_ChargeDescriptionDetails:
		if v == nil {
			err := TileValidationError{
				field:  "DisplayTile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetChargeDescriptionDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TileValidationError{
						field:  "ChargeDescriptionDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TileValidationError{
						field:  "ChargeDescriptionDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetChargeDescriptionDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TileValidationError{
					field:  "ChargeDescriptionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Tile_SduiSection:
		if v == nil {
			err := TileValidationError{
				field:  "DisplayTile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSduiSection()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TileValidationError{
						field:  "SduiSection",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TileValidationError{
						field:  "SduiSection",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSduiSection()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TileValidationError{
					field:  "SduiSection",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return TileMultiError(errors)
	}

	return nil
}

// TileMultiError is an error wrapping multiple validation errors returned by
// Tile.ValidateAll() if the designated constraints aren't met.
type TileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TileMultiError) AllErrors() []error { return m }

// TileValidationError is the validation error returned by Tile.Validate if the
// designated constraints aren't met.
type TileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TileValidationError) ErrorName() string { return "TileValidationError" }

// Error satisfies the builtin error interface
func (e TileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TileValidationError{}

// Validate checks the field values on ChargeDescriptionDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChargeDescriptionDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChargeDescriptionDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChargeDescriptionDetailsMultiError, or nil if none found.
func (m *ChargeDescriptionDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ChargeDescriptionDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetChargeDescriptionIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChargeDescriptionDetailsValidationError{
					field:  "ChargeDescriptionIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChargeDescriptionDetailsValidationError{
					field:  "ChargeDescriptionIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChargeDescriptionIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChargeDescriptionDetailsValidationError{
				field:  "ChargeDescriptionIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetChargeTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChargeDescriptionDetailsValidationError{
					field:  "ChargeTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChargeDescriptionDetailsValidationError{
					field:  "ChargeTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChargeTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChargeDescriptionDetailsValidationError{
				field:  "ChargeTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetChargeDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChargeDescriptionDetailsValidationError{
					field:  "ChargeDescription",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChargeDescriptionDetailsValidationError{
					field:  "ChargeDescription",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChargeDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChargeDescriptionDetailsValidationError{
				field:  "ChargeDescription",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCtaList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ChargeDescriptionDetailsValidationError{
						field:  fmt.Sprintf("CtaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ChargeDescriptionDetailsValidationError{
						field:  fmt.Sprintf("CtaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ChargeDescriptionDetailsValidationError{
					field:  fmt.Sprintf("CtaList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ChargeDescriptionDetailsMultiError(errors)
	}

	return nil
}

// ChargeDescriptionDetailsMultiError is an error wrapping multiple validation
// errors returned by ChargeDescriptionDetails.ValidateAll() if the designated
// constraints aren't met.
type ChargeDescriptionDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChargeDescriptionDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChargeDescriptionDetailsMultiError) AllErrors() []error { return m }

// ChargeDescriptionDetailsValidationError is the validation error returned by
// ChargeDescriptionDetails.Validate if the designated constraints aren't met.
type ChargeDescriptionDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChargeDescriptionDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChargeDescriptionDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChargeDescriptionDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChargeDescriptionDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChargeDescriptionDetailsValidationError) ErrorName() string {
	return "ChargeDescriptionDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ChargeDescriptionDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChargeDescriptionDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChargeDescriptionDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChargeDescriptionDetailsValidationError{}

// Validate checks the field values on GenericSduiSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenericSduiSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenericSduiSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenericSduiSectionMultiError, or nil if none found.
func (m *GenericSduiSection) ValidateAll() error {
	return m.validate(true)
}

func (m *GenericSduiSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenericSduiSectionValidationError{
					field:  "Section",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenericSduiSectionValidationError{
					field:  "Section",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenericSduiSectionValidationError{
				field:  "Section",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenericSduiSectionMultiError(errors)
	}

	return nil
}

// GenericSduiSectionMultiError is an error wrapping multiple validation errors
// returned by GenericSduiSection.ValidateAll() if the designated constraints
// aren't met.
type GenericSduiSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenericSduiSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenericSduiSectionMultiError) AllErrors() []error { return m }

// GenericSduiSectionValidationError is the validation error returned by
// GenericSduiSection.Validate if the designated constraints aren't met.
type GenericSduiSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenericSduiSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenericSduiSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenericSduiSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenericSduiSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenericSduiSectionValidationError) ErrorName() string {
	return "GenericSduiSectionValidationError"
}

// Error satisfies the builtin error interface
func (e GenericSduiSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenericSduiSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenericSduiSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenericSduiSectionValidationError{}

// Validate checks the field values on ErrorView with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ErrorView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ErrorView with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ErrorViewMultiError, or nil
// if none found.
func (m *ErrorView) ValidateAll() error {
	return m.validate(true)
}

func (m *ErrorView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	if utf8.RuneCountInString(m.GetSubTitle()) > 50 {
		err := ErrorViewValidationError{
			field:  "SubTitle",
			reason: "value length must be at most 50 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetDescription()) > 100 {
		err := ErrorViewValidationError{
			field:  "Description",
			reason: "value length must be at most 100 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IconUrl

	for idx, item := range m.GetActions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ErrorViewValidationError{
						field:  fmt.Sprintf("Actions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ErrorViewValidationError{
						field:  fmt.Sprintf("Actions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ErrorViewValidationError{
					field:  fmt.Sprintf("Actions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ErrorViewMultiError(errors)
	}

	return nil
}

// ErrorViewMultiError is an error wrapping multiple validation errors returned
// by ErrorView.ValidateAll() if the designated constraints aren't met.
type ErrorViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ErrorViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ErrorViewMultiError) AllErrors() []error { return m }

// ErrorViewValidationError is the validation error returned by
// ErrorView.Validate if the designated constraints aren't met.
type ErrorViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ErrorViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ErrorViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ErrorViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ErrorViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ErrorViewValidationError) ErrorName() string { return "ErrorViewValidationError" }

// Error satisfies the builtin error interface
func (e ErrorViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sErrorView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ErrorViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ErrorViewValidationError{}

// Validate checks the field values on RaiseDisputeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RaiseDisputeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RaiseDisputeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RaiseDisputeRequestMultiError, or nil if none found.
func (m *RaiseDisputeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RaiseDisputeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RaiseDisputeRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RaiseDisputeRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RaiseDisputeRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RaiseDisputeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RaiseDisputeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RaiseDisputeRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := utf8.RuneCountInString(m.GetOrderId()); l < 1 || l > 35 {
		err := RaiseDisputeRequestValidationError{
			field:  "OrderId",
			reason: "value length must be between 1 and 35 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RaiseDisputeRequestMultiError(errors)
	}

	return nil
}

// RaiseDisputeRequestMultiError is an error wrapping multiple validation
// errors returned by RaiseDisputeRequest.ValidateAll() if the designated
// constraints aren't met.
type RaiseDisputeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RaiseDisputeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RaiseDisputeRequestMultiError) AllErrors() []error { return m }

// RaiseDisputeRequestValidationError is the validation error returned by
// RaiseDisputeRequest.Validate if the designated constraints aren't met.
type RaiseDisputeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RaiseDisputeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RaiseDisputeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RaiseDisputeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RaiseDisputeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RaiseDisputeRequestValidationError) ErrorName() string {
	return "RaiseDisputeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RaiseDisputeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRaiseDisputeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RaiseDisputeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RaiseDisputeRequestValidationError{}

// Validate checks the field values on RaiseDisputeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RaiseDisputeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RaiseDisputeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RaiseDisputeResponseMultiError, or nil if none found.
func (m *RaiseDisputeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RaiseDisputeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RaiseDisputeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RaiseDisputeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RaiseDisputeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RaiseDisputeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RaiseDisputeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RaiseDisputeResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RaiseDisputeResponseMultiError(errors)
	}

	return nil
}

// RaiseDisputeResponseMultiError is an error wrapping multiple validation
// errors returned by RaiseDisputeResponse.ValidateAll() if the designated
// constraints aren't met.
type RaiseDisputeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RaiseDisputeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RaiseDisputeResponseMultiError) AllErrors() []error { return m }

// RaiseDisputeResponseValidationError is the validation error returned by
// RaiseDisputeResponse.Validate if the designated constraints aren't met.
type RaiseDisputeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RaiseDisputeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RaiseDisputeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RaiseDisputeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RaiseDisputeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RaiseDisputeResponseValidationError) ErrorName() string {
	return "RaiseDisputeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RaiseDisputeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRaiseDisputeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RaiseDisputeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RaiseDisputeResponseValidationError{}

// Validate checks the field values on BottomSheetActions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BottomSheetActions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BottomSheetActions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BottomSheetActionsMultiError, or nil if none found.
func (m *BottomSheetActions) ValidateAll() error {
	return m.validate(true)
}

func (m *BottomSheetActions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Action

	// no validation rules for ActionPrecedence

	// no validation rules for DisplayValue

	if len(errors) > 0 {
		return BottomSheetActionsMultiError(errors)
	}

	return nil
}

// BottomSheetActionsMultiError is an error wrapping multiple validation errors
// returned by BottomSheetActions.ValidateAll() if the designated constraints
// aren't met.
type BottomSheetActionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BottomSheetActionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BottomSheetActionsMultiError) AllErrors() []error { return m }

// BottomSheetActionsValidationError is the validation error returned by
// BottomSheetActions.Validate if the designated constraints aren't met.
type BottomSheetActionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BottomSheetActionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BottomSheetActionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BottomSheetActionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BottomSheetActionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BottomSheetActionsValidationError) ErrorName() string {
	return "BottomSheetActionsValidationError"
}

// Error satisfies the builtin error interface
func (e BottomSheetActionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBottomSheetActions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BottomSheetActionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BottomSheetActionsValidationError{}

// Validate checks the field values on IntentResponseParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IntentResponseParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IntentResponseParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IntentResponseParamsMultiError, or nil if none found.
func (m *IntentResponseParams) ValidateAll() error {
	return m.validate(true)
}

func (m *IntentResponseParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnId

	// no validation rules for ResponseCode

	// no validation rules for TrTxnRef

	// no validation rules for ApprovalRefNumber

	if len(errors) > 0 {
		return IntentResponseParamsMultiError(errors)
	}

	return nil
}

// IntentResponseParamsMultiError is an error wrapping multiple validation
// errors returned by IntentResponseParams.ValidateAll() if the designated
// constraints aren't met.
type IntentResponseParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IntentResponseParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IntentResponseParamsMultiError) AllErrors() []error { return m }

// IntentResponseParamsValidationError is the validation error returned by
// IntentResponseParams.Validate if the designated constraints aren't met.
type IntentResponseParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IntentResponseParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IntentResponseParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IntentResponseParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IntentResponseParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IntentResponseParamsValidationError) ErrorName() string {
	return "IntentResponseParamsValidationError"
}

// Error satisfies the builtin error interface
func (e IntentResponseParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIntentResponseParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IntentResponseParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IntentResponseParamsValidationError{}

// Validate checks the field values on GetAddFundParamsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAddFundParamsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAddFundParamsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAddFundParamsRequestMultiError, or nil if none found.
func (m *GetAddFundParamsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAddFundParamsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundParamsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundParamsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundParamsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UiEntryPoint

	// no validation rules for TransactionUiEntryPoint

	if len(errors) > 0 {
		return GetAddFundParamsRequestMultiError(errors)
	}

	return nil
}

// GetAddFundParamsRequestMultiError is an error wrapping multiple validation
// errors returned by GetAddFundParamsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAddFundParamsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAddFundParamsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAddFundParamsRequestMultiError) AllErrors() []error { return m }

// GetAddFundParamsRequestValidationError is the validation error returned by
// GetAddFundParamsRequest.Validate if the designated constraints aren't met.
type GetAddFundParamsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAddFundParamsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAddFundParamsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAddFundParamsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAddFundParamsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAddFundParamsRequestValidationError) ErrorName() string {
	return "GetAddFundParamsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAddFundParamsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAddFundParamsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAddFundParamsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAddFundParamsRequestValidationError{}

// Validate checks the field values on PaymentCtaOperationDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PaymentCtaOperationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PaymentCtaOperationDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PaymentCtaOperationDetailsMultiError, or nil if none found.
func (m *PaymentCtaOperationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PaymentCtaOperationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetOperations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PaymentCtaOperationDetailsValidationError{
						field:  fmt.Sprintf("Operations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PaymentCtaOperationDetailsValidationError{
						field:  fmt.Sprintf("Operations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PaymentCtaOperationDetailsValidationError{
					field:  fmt.Sprintf("Operations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PaymentCtaOperationDetailsMultiError(errors)
	}

	return nil
}

// PaymentCtaOperationDetailsMultiError is an error wrapping multiple
// validation errors returned by PaymentCtaOperationDetails.ValidateAll() if
// the designated constraints aren't met.
type PaymentCtaOperationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaymentCtaOperationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaymentCtaOperationDetailsMultiError) AllErrors() []error { return m }

// PaymentCtaOperationDetailsValidationError is the validation error returned
// by PaymentCtaOperationDetails.Validate if the designated constraints aren't met.
type PaymentCtaOperationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaymentCtaOperationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaymentCtaOperationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaymentCtaOperationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaymentCtaOperationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaymentCtaOperationDetailsValidationError) ErrorName() string {
	return "PaymentCtaOperationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e PaymentCtaOperationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPaymentCtaOperationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaymentCtaOperationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaymentCtaOperationDetailsValidationError{}

// Validate checks the field values on GetAddFundParamsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAddFundParamsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAddFundParamsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAddFundParamsResponseMultiError, or nil if none found.
func (m *GetAddFundParamsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAddFundParamsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundParamsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundParamsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDefaultAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "DefaultAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "DefaultAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundParamsResponseValidationError{
				field:  "DefaultAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundParamsResponseValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundParamsResponseValidationError{
				field:  "MaxAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSuggestedAmounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAddFundParamsResponseValidationError{
						field:  fmt.Sprintf("SuggestedAmounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAddFundParamsResponseValidationError{
						field:  fmt.Sprintf("SuggestedAmounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAddFundParamsResponseValidationError{
					field:  fmt.Sprintf("SuggestedAmounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetRewardsBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "RewardsBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "RewardsBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardsBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundParamsResponseValidationError{
				field:  "RewardsBanner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsBlocking

	// no validation rules for IsTieringEnabled

	if all {
		switch v := interface{}(m.GetAddFundsV2TieringScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "AddFundsV2TieringScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "AddFundsV2TieringScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddFundsV2TieringScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundParamsResponseValidationError{
				field:  "AddFundsV2TieringScreen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AddFundsVersion

	if all {
		switch v := interface{}(m.GetAddFundsV3ScreenDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "AddFundsV3ScreenDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "AddFundsV3ScreenDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddFundsV3ScreenDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundParamsResponseValidationError{
				field:  "AddFundsV3ScreenDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddFundsCtaOperationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "AddFundsCtaOperationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "AddFundsCtaOperationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddFundsCtaOperationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundParamsResponseValidationError{
				field:  "AddFundsCtaOperationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IntentNavigateToPayStatus

	// no validation rules for CollectNavigateToPayStatus

	if all {
		switch v := interface{}(m.GetBackNavigationDropOffDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "BackNavigationDropOffDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundParamsResponseValidationError{
					field:  "BackNavigationDropOffDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackNavigationDropOffDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundParamsResponseValidationError{
				field:  "BackNavigationDropOffDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAddFundParamsResponseMultiError(errors)
	}

	return nil
}

// GetAddFundParamsResponseMultiError is an error wrapping multiple validation
// errors returned by GetAddFundParamsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAddFundParamsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAddFundParamsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAddFundParamsResponseMultiError) AllErrors() []error { return m }

// GetAddFundParamsResponseValidationError is the validation error returned by
// GetAddFundParamsResponse.Validate if the designated constraints aren't met.
type GetAddFundParamsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAddFundParamsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAddFundParamsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAddFundParamsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAddFundParamsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAddFundParamsResponseValidationError) ErrorName() string {
	return "GetAddFundParamsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAddFundParamsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAddFundParamsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAddFundParamsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAddFundParamsResponseValidationError{}

// Validate checks the field values on CheckIfAddFundsAllowedRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckIfAddFundsAllowedRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckIfAddFundsAllowedRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckIfAddFundsAllowedRequestMultiError, or nil if none found.
func (m *CheckIfAddFundsAllowedRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckIfAddFundsAllowedRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckIfAddFundsAllowedRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckIfAddFundsAllowedRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckIfAddFundsAllowedRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetAmount() == nil {
		err := CheckIfAddFundsAllowedRequestValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckIfAddFundsAllowedRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckIfAddFundsAllowedRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckIfAddFundsAllowedRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayeeUserIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckIfAddFundsAllowedRequestValidationError{
					field:  "PayeeUserIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckIfAddFundsAllowedRequestValidationError{
					field:  "PayeeUserIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayeeUserIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckIfAddFundsAllowedRequestValidationError{
				field:  "PayeeUserIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UiEntryPoint

	if len(errors) > 0 {
		return CheckIfAddFundsAllowedRequestMultiError(errors)
	}

	return nil
}

// CheckIfAddFundsAllowedRequestMultiError is an error wrapping multiple
// validation errors returned by CheckIfAddFundsAllowedRequest.ValidateAll()
// if the designated constraints aren't met.
type CheckIfAddFundsAllowedRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckIfAddFundsAllowedRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckIfAddFundsAllowedRequestMultiError) AllErrors() []error { return m }

// CheckIfAddFundsAllowedRequestValidationError is the validation error
// returned by CheckIfAddFundsAllowedRequest.Validate if the designated
// constraints aren't met.
type CheckIfAddFundsAllowedRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckIfAddFundsAllowedRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckIfAddFundsAllowedRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckIfAddFundsAllowedRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckIfAddFundsAllowedRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckIfAddFundsAllowedRequestValidationError) ErrorName() string {
	return "CheckIfAddFundsAllowedRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckIfAddFundsAllowedRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckIfAddFundsAllowedRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckIfAddFundsAllowedRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckIfAddFundsAllowedRequestValidationError{}

// Validate checks the field values on CheckIfAddFundsAllowedResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckIfAddFundsAllowedResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckIfAddFundsAllowedResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckIfAddFundsAllowedResponseMultiError, or nil if none found.
func (m *CheckIfAddFundsAllowedResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckIfAddFundsAllowedResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPopUpOption()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckIfAddFundsAllowedResponseValidationError{
					field:  "PopUpOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckIfAddFundsAllowedResponseValidationError{
					field:  "PopUpOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPopUpOption()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckIfAddFundsAllowedResponseValidationError{
				field:  "PopUpOption",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckIfAddFundsAllowedResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckIfAddFundsAllowedResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckIfAddFundsAllowedResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckIfAddFundsAllowedResponseMultiError(errors)
	}

	return nil
}

// CheckIfAddFundsAllowedResponseMultiError is an error wrapping multiple
// validation errors returned by CheckIfAddFundsAllowedResponse.ValidateAll()
// if the designated constraints aren't met.
type CheckIfAddFundsAllowedResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckIfAddFundsAllowedResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckIfAddFundsAllowedResponseMultiError) AllErrors() []error { return m }

// CheckIfAddFundsAllowedResponseValidationError is the validation error
// returned by CheckIfAddFundsAllowedResponse.Validate if the designated
// constraints aren't met.
type CheckIfAddFundsAllowedResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckIfAddFundsAllowedResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckIfAddFundsAllowedResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckIfAddFundsAllowedResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckIfAddFundsAllowedResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckIfAddFundsAllowedResponseValidationError) ErrorName() string {
	return "CheckIfAddFundsAllowedResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckIfAddFundsAllowedResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckIfAddFundsAllowedResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckIfAddFundsAllowedResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckIfAddFundsAllowedResponseValidationError{}

// Validate checks the field values on AuthoriseFundTransferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthoriseFundTransferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthoriseFundTransferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthoriseFundTransferRequestMultiError, or nil if none found.
func (m *AuthoriseFundTransferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseFundTransferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseFundTransferRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseFundTransferRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseFundTransferRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := utf8.RuneCountInString(m.GetClientRequestId()); l < 4 || l > 36 {
		err := AuthoriseFundTransferRequestValidationError{
			field:  "ClientRequestId",
			reason: "value length must be between 4 and 36 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPaymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseFundTransferRequestValidationError{
					field:  "PaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseFundTransferRequestValidationError{
					field:  "PaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseFundTransferRequestValidationError{
				field:  "PaymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Credential.(type) {
	case *AuthoriseFundTransferRequest_PartnerSdkCredBlock:
		if v == nil {
			err := AuthoriseFundTransferRequestValidationError{
				field:  "Credential",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PartnerSdkCredBlock
	case *AuthoriseFundTransferRequest_NpciCredBlock:
		if v == nil {
			err := AuthoriseFundTransferRequestValidationError{
				field:  "Credential",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNpciCredBlock()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AuthoriseFundTransferRequestValidationError{
						field:  "NpciCredBlock",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AuthoriseFundTransferRequestValidationError{
						field:  "NpciCredBlock",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNpciCredBlock()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AuthoriseFundTransferRequestValidationError{
					field:  "NpciCredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AuthoriseFundTransferRequestMultiError(errors)
	}

	return nil
}

// AuthoriseFundTransferRequestMultiError is an error wrapping multiple
// validation errors returned by AuthoriseFundTransferRequest.ValidateAll() if
// the designated constraints aren't met.
type AuthoriseFundTransferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseFundTransferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseFundTransferRequestMultiError) AllErrors() []error { return m }

// AuthoriseFundTransferRequestValidationError is the validation error returned
// by AuthoriseFundTransferRequest.Validate if the designated constraints
// aren't met.
type AuthoriseFundTransferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseFundTransferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseFundTransferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseFundTransferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseFundTransferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseFundTransferRequestValidationError) ErrorName() string {
	return "AuthoriseFundTransferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseFundTransferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseFundTransferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseFundTransferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseFundTransferRequestValidationError{}

// Validate checks the field values on AuthoriseFundTransferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthoriseFundTransferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthoriseFundTransferResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AuthoriseFundTransferResponseMultiError, or nil if none found.
func (m *AuthoriseFundTransferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseFundTransferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseFundTransferResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseFundTransferResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseFundTransferResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPostAuthoriseDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseFundTransferResponseValidationError{
					field:  "PostAuthoriseDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseFundTransferResponseValidationError{
					field:  "PostAuthoriseDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostAuthoriseDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseFundTransferResponseValidationError{
				field:  "PostAuthoriseDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthoriseFundTransferResponseMultiError(errors)
	}

	return nil
}

// AuthoriseFundTransferResponseMultiError is an error wrapping multiple
// validation errors returned by AuthoriseFundTransferResponse.ValidateAll()
// if the designated constraints aren't met.
type AuthoriseFundTransferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseFundTransferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseFundTransferResponseMultiError) AllErrors() []error { return m }

// AuthoriseFundTransferResponseValidationError is the validation error
// returned by AuthoriseFundTransferResponse.Validate if the designated
// constraints aren't met.
type AuthoriseFundTransferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseFundTransferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseFundTransferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseFundTransferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseFundTransferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseFundTransferResponseValidationError) ErrorName() string {
	return "AuthoriseFundTransferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseFundTransferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseFundTransferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseFundTransferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseFundTransferResponseValidationError{}

// Validate checks the field values on GetEligibleAccountsForPaymentV1Request
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetEligibleAccountsForPaymentV1Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetEligibleAccountsForPaymentV1Request with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetEligibleAccountsForPaymentV1RequestMultiError, or nil if none found.
func (m *GetEligibleAccountsForPaymentV1Request) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEligibleAccountsForPaymentV1Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentV1RequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentV1RequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEligibleAccountsForPaymentV1RequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PaymentScope

	// no validation rules for SecondActorId

	// no validation rules for EligibleAccountsUiEntryPoint

	// no validation rules for PayeePiId

	if len(errors) > 0 {
		return GetEligibleAccountsForPaymentV1RequestMultiError(errors)
	}

	return nil
}

// GetEligibleAccountsForPaymentV1RequestMultiError is an error wrapping
// multiple validation errors returned by
// GetEligibleAccountsForPaymentV1Request.ValidateAll() if the designated
// constraints aren't met.
type GetEligibleAccountsForPaymentV1RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEligibleAccountsForPaymentV1RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEligibleAccountsForPaymentV1RequestMultiError) AllErrors() []error { return m }

// GetEligibleAccountsForPaymentV1RequestValidationError is the validation
// error returned by GetEligibleAccountsForPaymentV1Request.Validate if the
// designated constraints aren't met.
type GetEligibleAccountsForPaymentV1RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEligibleAccountsForPaymentV1RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEligibleAccountsForPaymentV1RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEligibleAccountsForPaymentV1RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEligibleAccountsForPaymentV1RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEligibleAccountsForPaymentV1RequestValidationError) ErrorName() string {
	return "GetEligibleAccountsForPaymentV1RequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEligibleAccountsForPaymentV1RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEligibleAccountsForPaymentV1Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEligibleAccountsForPaymentV1RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEligibleAccountsForPaymentV1RequestValidationError{}

// Validate checks the field values on GetEligibleAccountsForPaymentV1Response
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetEligibleAccountsForPaymentV1Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetEligibleAccountsForPaymentV1Response with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetEligibleAccountsForPaymentV1ResponseMultiError, or nil if none found.
func (m *GetEligibleAccountsForPaymentV1Response) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEligibleAccountsForPaymentV1Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentV1ResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentV1ResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEligibleAccountsForPaymentV1ResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetEligibleAccountsForPaymentV1ResponseValidationError{
						field:  fmt.Sprintf("AccountInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetEligibleAccountsForPaymentV1ResponseValidationError{
						field:  fmt.Sprintf("AccountInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetEligibleAccountsForPaymentV1ResponseValidationError{
					field:  fmt.Sprintf("AccountInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEntrypoints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetEligibleAccountsForPaymentV1ResponseValidationError{
						field:  fmt.Sprintf("Entrypoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetEligibleAccountsForPaymentV1ResponseValidationError{
						field:  fmt.Sprintf("Entrypoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetEligibleAccountsForPaymentV1ResponseValidationError{
					field:  fmt.Sprintf("Entrypoints[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAccountSelectorCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentV1ResponseValidationError{
					field:  "AccountSelectorCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentV1ResponseValidationError{
					field:  "AccountSelectorCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountSelectorCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEligibleAccountsForPaymentV1ResponseValidationError{
				field:  "AccountSelectorCard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPartnerTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentV1ResponseValidationError{
					field:  "PartnerTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentV1ResponseValidationError{
					field:  "PartnerTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPartnerTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEligibleAccountsForPaymentV1ResponseValidationError{
				field:  "PartnerTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEligibleAccountsForPaymentV1ResponseMultiError(errors)
	}

	return nil
}

// GetEligibleAccountsForPaymentV1ResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetEligibleAccountsForPaymentV1Response.ValidateAll() if the designated
// constraints aren't met.
type GetEligibleAccountsForPaymentV1ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEligibleAccountsForPaymentV1ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEligibleAccountsForPaymentV1ResponseMultiError) AllErrors() []error { return m }

// GetEligibleAccountsForPaymentV1ResponseValidationError is the validation
// error returned by GetEligibleAccountsForPaymentV1Response.Validate if the
// designated constraints aren't met.
type GetEligibleAccountsForPaymentV1ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEligibleAccountsForPaymentV1ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEligibleAccountsForPaymentV1ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEligibleAccountsForPaymentV1ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEligibleAccountsForPaymentV1ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEligibleAccountsForPaymentV1ResponseValidationError) ErrorName() string {
	return "GetEligibleAccountsForPaymentV1ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEligibleAccountsForPaymentV1ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEligibleAccountsForPaymentV1Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEligibleAccountsForPaymentV1ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEligibleAccountsForPaymentV1ResponseValidationError{}

// Validate checks the field values on UserIdentifier with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserIdentifier with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserIdentifierMultiError,
// or nil if none found.
func (m *UserIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *UserIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for DerivedAccountId

	// no validation rules for AccountType

	// no validation rules for PiId

	// no validation rules for UpiNumber

	// no validation rules for AccountProductOffering

	if len(errors) > 0 {
		return UserIdentifierMultiError(errors)
	}

	return nil
}

// UserIdentifierMultiError is an error wrapping multiple validation errors
// returned by UserIdentifier.ValidateAll() if the designated constraints
// aren't met.
type UserIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserIdentifierMultiError) AllErrors() []error { return m }

// UserIdentifierValidationError is the validation error returned by
// UserIdentifier.Validate if the designated constraints aren't met.
type UserIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserIdentifierValidationError) ErrorName() string { return "UserIdentifierValidationError" }

// Error satisfies the builtin error interface
func (e UserIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserIdentifierValidationError{}

// Validate checks the field values on GetChatHeadsForPaymentViaNumberRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetChatHeadsForPaymentViaNumberRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetChatHeadsForPaymentViaNumberRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetChatHeadsForPaymentViaNumberRequestMultiError, or nil if none found.
func (m *GetChatHeadsForPaymentViaNumberRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatHeadsForPaymentViaNumberRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatHeadsForPaymentViaNumberRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatHeadsForPaymentViaNumberRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatHeadsForPaymentViaNumberRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Number

	// no validation rules for UiEntryPoint

	if len(errors) > 0 {
		return GetChatHeadsForPaymentViaNumberRequestMultiError(errors)
	}

	return nil
}

// GetChatHeadsForPaymentViaNumberRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetChatHeadsForPaymentViaNumberRequest.ValidateAll() if the designated
// constraints aren't met.
type GetChatHeadsForPaymentViaNumberRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatHeadsForPaymentViaNumberRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatHeadsForPaymentViaNumberRequestMultiError) AllErrors() []error { return m }

// GetChatHeadsForPaymentViaNumberRequestValidationError is the validation
// error returned by GetChatHeadsForPaymentViaNumberRequest.Validate if the
// designated constraints aren't met.
type GetChatHeadsForPaymentViaNumberRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatHeadsForPaymentViaNumberRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatHeadsForPaymentViaNumberRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatHeadsForPaymentViaNumberRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatHeadsForPaymentViaNumberRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatHeadsForPaymentViaNumberRequestValidationError) ErrorName() string {
	return "GetChatHeadsForPaymentViaNumberRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetChatHeadsForPaymentViaNumberRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatHeadsForPaymentViaNumberRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatHeadsForPaymentViaNumberRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatHeadsForPaymentViaNumberRequestValidationError{}

// Validate checks the field values on GetChatHeadsForPaymentViaNumberResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetChatHeadsForPaymentViaNumberResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetChatHeadsForPaymentViaNumberResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetChatHeadsForPaymentViaNumberResponseMultiError, or nil if none found.
func (m *GetChatHeadsForPaymentViaNumberResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatHeadsForPaymentViaNumberResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetChatHeads() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetChatHeadsForPaymentViaNumberResponseValidationError{
						field:  fmt.Sprintf("ChatHeads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetChatHeadsForPaymentViaNumberResponseValidationError{
						field:  fmt.Sprintf("ChatHeads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetChatHeadsForPaymentViaNumberResponseValidationError{
					field:  fmt.Sprintf("ChatHeads[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatHeadsForPaymentViaNumberResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatHeadsForPaymentViaNumberResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatHeadsForPaymentViaNumberResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetChatHeadsForPaymentViaNumberResponseMultiError(errors)
	}

	return nil
}

// GetChatHeadsForPaymentViaNumberResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetChatHeadsForPaymentViaNumberResponse.ValidateAll() if the designated
// constraints aren't met.
type GetChatHeadsForPaymentViaNumberResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatHeadsForPaymentViaNumberResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatHeadsForPaymentViaNumberResponseMultiError) AllErrors() []error { return m }

// GetChatHeadsForPaymentViaNumberResponseValidationError is the validation
// error returned by GetChatHeadsForPaymentViaNumberResponse.Validate if the
// designated constraints aren't met.
type GetChatHeadsForPaymentViaNumberResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatHeadsForPaymentViaNumberResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatHeadsForPaymentViaNumberResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatHeadsForPaymentViaNumberResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatHeadsForPaymentViaNumberResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatHeadsForPaymentViaNumberResponseValidationError) ErrorName() string {
	return "GetChatHeadsForPaymentViaNumberResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetChatHeadsForPaymentViaNumberResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatHeadsForPaymentViaNumberResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatHeadsForPaymentViaNumberResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatHeadsForPaymentViaNumberResponseValidationError{}

// Validate checks the field values on NpciCredBlocks with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NpciCredBlocks) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NpciCredBlocks with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NpciCredBlocksMultiError,
// or nil if none found.
func (m *NpciCredBlocks) ValidateAll() error {
	return m.validate(true)
}

func (m *NpciCredBlocks) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCredBlocks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NpciCredBlocksValidationError{
						field:  fmt.Sprintf("CredBlocks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NpciCredBlocksValidationError{
						field:  fmt.Sprintf("CredBlocks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NpciCredBlocksValidationError{
					field:  fmt.Sprintf("CredBlocks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NpciCredBlocksMultiError(errors)
	}

	return nil
}

// NpciCredBlocksMultiError is an error wrapping multiple validation errors
// returned by NpciCredBlocks.ValidateAll() if the designated constraints
// aren't met.
type NpciCredBlocksMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NpciCredBlocksMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NpciCredBlocksMultiError) AllErrors() []error { return m }

// NpciCredBlocksValidationError is the validation error returned by
// NpciCredBlocks.Validate if the designated constraints aren't met.
type NpciCredBlocksValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NpciCredBlocksValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NpciCredBlocksValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NpciCredBlocksValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NpciCredBlocksValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NpciCredBlocksValidationError) ErrorName() string { return "NpciCredBlocksValidationError" }

// Error satisfies the builtin error interface
func (e NpciCredBlocksValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNpciCredBlocks.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NpciCredBlocksValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NpciCredBlocksValidationError{}

// Validate checks the field values on CreateFundTransferOrderV1Request with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateFundTransferOrderV1Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFundTransferOrderV1Request with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateFundTransferOrderV1RequestMultiError, or nil if none found.
func (m *CreateFundTransferOrderV1Request) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFundTransferOrderV1Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1RequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1RequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderV1RequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetAmount() == nil {
		err := CreateFundTransferOrderV1RequestValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1RequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1RequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderV1RequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBaseAmountQuoteCurrency()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1RequestValidationError{
					field:  "BaseAmountQuoteCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1RequestValidationError{
					field:  "BaseAmountQuoteCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseAmountQuoteCurrency()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderV1RequestValidationError{
				field:  "BaseAmountQuoteCurrency",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionUiEntryPoint

	if all {
		switch v := interface{}(m.GetPayerUserIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1RequestValidationError{
					field:  "PayerUserIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1RequestValidationError{
					field:  "PayerUserIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayerUserIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderV1RequestValidationError{
				field:  "PayerUserIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetOrchestrationMetadata()) > 1000 {
		err := CreateFundTransferOrderV1RequestValidationError{
			field:  "OrchestrationMetadata",
			reason: "value length must be at most 1000 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetIntegrationSpecs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1RequestValidationError{
					field:  "IntegrationSpecs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1RequestValidationError{
					field:  "IntegrationSpecs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIntegrationSpecs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderV1RequestValidationError{
				field:  "IntegrationSpecs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecurringPaymentId

	if len(errors) > 0 {
		return CreateFundTransferOrderV1RequestMultiError(errors)
	}

	return nil
}

// CreateFundTransferOrderV1RequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateFundTransferOrderV1Request.ValidateAll() if the designated
// constraints aren't met.
type CreateFundTransferOrderV1RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFundTransferOrderV1RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFundTransferOrderV1RequestMultiError) AllErrors() []error { return m }

// CreateFundTransferOrderV1RequestValidationError is the validation error
// returned by CreateFundTransferOrderV1Request.Validate if the designated
// constraints aren't met.
type CreateFundTransferOrderV1RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFundTransferOrderV1RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFundTransferOrderV1RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFundTransferOrderV1RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFundTransferOrderV1RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFundTransferOrderV1RequestValidationError) ErrorName() string {
	return "CreateFundTransferOrderV1RequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFundTransferOrderV1RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFundTransferOrderV1Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFundTransferOrderV1RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFundTransferOrderV1RequestValidationError{}

// Validate checks the field values on BankPaymentRequestData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BankPaymentRequestData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankPaymentRequestData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BankPaymentRequestDataMultiError, or nil if none found.
func (m *BankPaymentRequestData) ValidateAll() error {
	return m.validate(true)
}

func (m *BankPaymentRequestData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PinRequiredType

	if all {
		switch v := interface{}(m.GetTransactionAttribute()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankPaymentRequestDataValidationError{
					field:  "TransactionAttribute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankPaymentRequestDataValidationError{
					field:  "TransactionAttribute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionAttribute()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankPaymentRequestDataValidationError{
				field:  "TransactionAttribute",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BankPaymentRequestDataMultiError(errors)
	}

	return nil
}

// BankPaymentRequestDataMultiError is an error wrapping multiple validation
// errors returned by BankPaymentRequestData.ValidateAll() if the designated
// constraints aren't met.
type BankPaymentRequestDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankPaymentRequestDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankPaymentRequestDataMultiError) AllErrors() []error { return m }

// BankPaymentRequestDataValidationError is the validation error returned by
// BankPaymentRequestData.Validate if the designated constraints aren't met.
type BankPaymentRequestDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankPaymentRequestDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankPaymentRequestDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankPaymentRequestDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankPaymentRequestDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankPaymentRequestDataValidationError) ErrorName() string {
	return "BankPaymentRequestDataValidationError"
}

// Error satisfies the builtin error interface
func (e BankPaymentRequestDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankPaymentRequestData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankPaymentRequestDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankPaymentRequestDataValidationError{}

// Validate checks the field values on PaymentGatewayPaymentDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PaymentGatewayPaymentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PaymentGatewayPaymentDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PaymentGatewayPaymentDetailsMultiError, or nil if none found.
func (m *PaymentGatewayPaymentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PaymentGatewayPaymentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VendorOrderId

	if all {
		switch v := interface{}(m.GetIntegrationSpecs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PaymentGatewayPaymentDetailsValidationError{
					field:  "IntegrationSpecs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PaymentGatewayPaymentDetailsValidationError{
					field:  "IntegrationSpecs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIntegrationSpecs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PaymentGatewayPaymentDetailsValidationError{
				field:  "IntegrationSpecs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetAmount() == nil {
		err := PaymentGatewayPaymentDetailsValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PaymentGatewayPaymentDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PaymentGatewayPaymentDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PaymentGatewayPaymentDetailsValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecurringPaymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PaymentGatewayPaymentDetailsValidationError{
					field:  "RecurringPaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PaymentGatewayPaymentDetailsValidationError{
					field:  "RecurringPaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringPaymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PaymentGatewayPaymentDetailsValidationError{
				field:  "RecurringPaymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PaymentGatewayPaymentDetailsMultiError(errors)
	}

	return nil
}

// PaymentGatewayPaymentDetailsMultiError is an error wrapping multiple
// validation errors returned by PaymentGatewayPaymentDetails.ValidateAll() if
// the designated constraints aren't met.
type PaymentGatewayPaymentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaymentGatewayPaymentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaymentGatewayPaymentDetailsMultiError) AllErrors() []error { return m }

// PaymentGatewayPaymentDetailsValidationError is the validation error returned
// by PaymentGatewayPaymentDetails.Validate if the designated constraints
// aren't met.
type PaymentGatewayPaymentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaymentGatewayPaymentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaymentGatewayPaymentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaymentGatewayPaymentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaymentGatewayPaymentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaymentGatewayPaymentDetailsValidationError) ErrorName() string {
	return "PaymentGatewayPaymentDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e PaymentGatewayPaymentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPaymentGatewayPaymentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaymentGatewayPaymentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaymentGatewayPaymentDetailsValidationError{}

// Validate checks the field values on RecurringPaymentDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecurringPaymentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecurringPaymentDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecurringPaymentDetailsMultiError, or nil if none found.
func (m *RecurringPaymentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RecurringPaymentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VendorCustomerId

	// no validation rules for RecurringPaymentMethod

	if len(errors) > 0 {
		return RecurringPaymentDetailsMultiError(errors)
	}

	return nil
}

// RecurringPaymentDetailsMultiError is an error wrapping multiple validation
// errors returned by RecurringPaymentDetails.ValidateAll() if the designated
// constraints aren't met.
type RecurringPaymentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecurringPaymentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecurringPaymentDetailsMultiError) AllErrors() []error { return m }

// RecurringPaymentDetailsValidationError is the validation error returned by
// RecurringPaymentDetails.Validate if the designated constraints aren't met.
type RecurringPaymentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecurringPaymentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecurringPaymentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecurringPaymentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecurringPaymentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecurringPaymentDetailsValidationError) ErrorName() string {
	return "RecurringPaymentDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e RecurringPaymentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecurringPaymentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecurringPaymentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecurringPaymentDetailsValidationError{}

// Validate checks the field values on CreateFundTransferOrderV1Response with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateFundTransferOrderV1Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFundTransferOrderV1Response
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateFundTransferOrderV1ResponseMultiError, or nil if none found.
func (m *CreateFundTransferOrderV1Response) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFundTransferOrderV1Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1ResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1ResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderV1ResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1ResponseValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderV1ResponseValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderV1ResponseValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	switch v := m.PaymentRequestData.(type) {
	case *CreateFundTransferOrderV1Response_BankPaymentRequestData:
		if v == nil {
			err := CreateFundTransferOrderV1ResponseValidationError{
				field:  "PaymentRequestData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBankPaymentRequestData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateFundTransferOrderV1ResponseValidationError{
						field:  "BankPaymentRequestData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateFundTransferOrderV1ResponseValidationError{
						field:  "BankPaymentRequestData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBankPaymentRequestData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateFundTransferOrderV1ResponseValidationError{
					field:  "BankPaymentRequestData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CreateFundTransferOrderV1Response_PaymentGatewayDetails:
		if v == nil {
			err := CreateFundTransferOrderV1ResponseValidationError{
				field:  "PaymentRequestData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPaymentGatewayDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateFundTransferOrderV1ResponseValidationError{
						field:  "PaymentGatewayDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateFundTransferOrderV1ResponseValidationError{
						field:  "PaymentGatewayDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPaymentGatewayDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateFundTransferOrderV1ResponseValidationError{
					field:  "PaymentGatewayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CreateFundTransferOrderV1ResponseMultiError(errors)
	}

	return nil
}

// CreateFundTransferOrderV1ResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateFundTransferOrderV1Response.ValidateAll() if the designated
// constraints aren't met.
type CreateFundTransferOrderV1ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFundTransferOrderV1ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFundTransferOrderV1ResponseMultiError) AllErrors() []error { return m }

// CreateFundTransferOrderV1ResponseValidationError is the validation error
// returned by CreateFundTransferOrderV1Response.Validate if the designated
// constraints aren't met.
type CreateFundTransferOrderV1ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFundTransferOrderV1ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFundTransferOrderV1ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFundTransferOrderV1ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFundTransferOrderV1ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFundTransferOrderV1ResponseValidationError) ErrorName() string {
	return "CreateFundTransferOrderV1ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFundTransferOrderV1ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFundTransferOrderV1Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFundTransferOrderV1ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFundTransferOrderV1ResponseValidationError{}

// Validate checks the field values on MarkPostPaymentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MarkPostPaymentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MarkPostPaymentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MarkPostPaymentRequestMultiError, or nil if none found.
func (m *MarkPostPaymentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MarkPostPaymentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarkPostPaymentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarkPostPaymentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarkPostPaymentRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetOrchestrationMetadata()) > 1000 {
		err := MarkPostPaymentRequestValidationError{
			field:  "OrchestrationMetadata",
			reason: "value length must be at most 1000 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetIntegrationSpecs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarkPostPaymentRequestValidationError{
					field:  "IntegrationSpecs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarkPostPaymentRequestValidationError{
					field:  "IntegrationSpecs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIntegrationSpecs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarkPostPaymentRequestValidationError{
				field:  "IntegrationSpecs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecurringPaymentId

	// no validation rules for OrderId

	// no validation rules for TransactionUiEntryPoint

	// no validation rules for ActionType

	switch v := m.PgClientSdkResponsePayload.(type) {
	case *MarkPostPaymentRequest_RazorpayClientSuccessPayload:
		if v == nil {
			err := MarkPostPaymentRequestValidationError{
				field:  "PgClientSdkResponsePayload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRazorpayClientSuccessPayload()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MarkPostPaymentRequestValidationError{
						field:  "RazorpayClientSuccessPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MarkPostPaymentRequestValidationError{
						field:  "RazorpayClientSuccessPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRazorpayClientSuccessPayload()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MarkPostPaymentRequestValidationError{
					field:  "RazorpayClientSuccessPayload",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MarkPostPaymentRequest_RazorpayClientFailurePayload:
		if v == nil {
			err := MarkPostPaymentRequestValidationError{
				field:  "PgClientSdkResponsePayload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRazorpayClientFailurePayload()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MarkPostPaymentRequestValidationError{
						field:  "RazorpayClientFailurePayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MarkPostPaymentRequestValidationError{
						field:  "RazorpayClientFailurePayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRazorpayClientFailurePayload()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MarkPostPaymentRequestValidationError{
					field:  "RazorpayClientFailurePayload",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return MarkPostPaymentRequestMultiError(errors)
	}

	return nil
}

// MarkPostPaymentRequestMultiError is an error wrapping multiple validation
// errors returned by MarkPostPaymentRequest.ValidateAll() if the designated
// constraints aren't met.
type MarkPostPaymentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MarkPostPaymentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MarkPostPaymentRequestMultiError) AllErrors() []error { return m }

// MarkPostPaymentRequestValidationError is the validation error returned by
// MarkPostPaymentRequest.Validate if the designated constraints aren't met.
type MarkPostPaymentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MarkPostPaymentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MarkPostPaymentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MarkPostPaymentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MarkPostPaymentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MarkPostPaymentRequestValidationError) ErrorName() string {
	return "MarkPostPaymentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MarkPostPaymentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMarkPostPaymentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MarkPostPaymentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MarkPostPaymentRequestValidationError{}

// Validate checks the field values on RazorpaySuccessPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RazorpaySuccessPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RazorpaySuccessPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RazorpaySuccessPayloadMultiError, or nil if none found.
func (m *RazorpaySuccessPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *RazorpaySuccessPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RazorpayPaymentId

	// no validation rules for RazorpayOrderId

	// no validation rules for RazorpaySignature

	if len(errors) > 0 {
		return RazorpaySuccessPayloadMultiError(errors)
	}

	return nil
}

// RazorpaySuccessPayloadMultiError is an error wrapping multiple validation
// errors returned by RazorpaySuccessPayload.ValidateAll() if the designated
// constraints aren't met.
type RazorpaySuccessPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RazorpaySuccessPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RazorpaySuccessPayloadMultiError) AllErrors() []error { return m }

// RazorpaySuccessPayloadValidationError is the validation error returned by
// RazorpaySuccessPayload.Validate if the designated constraints aren't met.
type RazorpaySuccessPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RazorpaySuccessPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RazorpaySuccessPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RazorpaySuccessPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RazorpaySuccessPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RazorpaySuccessPayloadValidationError) ErrorName() string {
	return "RazorpaySuccessPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e RazorpaySuccessPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRazorpaySuccessPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RazorpaySuccessPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RazorpaySuccessPayloadValidationError{}

// Validate checks the field values on RazorpayFailurePayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RazorpayFailurePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RazorpayFailurePayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RazorpayFailurePayloadMultiError, or nil if none found.
func (m *RazorpayFailurePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *RazorpayFailurePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HttpStatusCode

	if all {
		switch v := interface{}(m.GetError()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RazorpayFailurePayloadValidationError{
					field:  "Error",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RazorpayFailurePayloadValidationError{
					field:  "Error",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetError()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RazorpayFailurePayloadValidationError{
				field:  "Error",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RazorpayFailurePayloadMultiError(errors)
	}

	return nil
}

// RazorpayFailurePayloadMultiError is an error wrapping multiple validation
// errors returned by RazorpayFailurePayload.ValidateAll() if the designated
// constraints aren't met.
type RazorpayFailurePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RazorpayFailurePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RazorpayFailurePayloadMultiError) AllErrors() []error { return m }

// RazorpayFailurePayloadValidationError is the validation error returned by
// RazorpayFailurePayload.Validate if the designated constraints aren't met.
type RazorpayFailurePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RazorpayFailurePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RazorpayFailurePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RazorpayFailurePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RazorpayFailurePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RazorpayFailurePayloadValidationError) ErrorName() string {
	return "RazorpayFailurePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e RazorpayFailurePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRazorpayFailurePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RazorpayFailurePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RazorpayFailurePayloadValidationError{}

// Validate checks the field values on RazorpayClientSdkError with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RazorpayClientSdkError) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RazorpayClientSdkError with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RazorpayClientSdkErrorMultiError, or nil if none found.
func (m *RazorpayClientSdkError) ValidateAll() error {
	return m.validate(true)
}

func (m *RazorpayClientSdkError) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Description

	// no validation rules for Source

	// no validation rules for Step

	// no validation rules for Reason

	// no validation rules for Field

	if len(errors) > 0 {
		return RazorpayClientSdkErrorMultiError(errors)
	}

	return nil
}

// RazorpayClientSdkErrorMultiError is an error wrapping multiple validation
// errors returned by RazorpayClientSdkError.ValidateAll() if the designated
// constraints aren't met.
type RazorpayClientSdkErrorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RazorpayClientSdkErrorMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RazorpayClientSdkErrorMultiError) AllErrors() []error { return m }

// RazorpayClientSdkErrorValidationError is the validation error returned by
// RazorpayClientSdkError.Validate if the designated constraints aren't met.
type RazorpayClientSdkErrorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RazorpayClientSdkErrorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RazorpayClientSdkErrorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RazorpayClientSdkErrorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RazorpayClientSdkErrorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RazorpayClientSdkErrorValidationError) ErrorName() string {
	return "RazorpayClientSdkErrorValidationError"
}

// Error satisfies the builtin error interface
func (e RazorpayClientSdkErrorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRazorpayClientSdkError.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RazorpayClientSdkErrorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RazorpayClientSdkErrorValidationError{}

// Validate checks the field values on MarkPostPaymentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MarkPostPaymentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MarkPostPaymentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MarkPostPaymentResponseMultiError, or nil if none found.
func (m *MarkPostPaymentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MarkPostPaymentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarkPostPaymentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarkPostPaymentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarkPostPaymentResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarkPostPaymentResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarkPostPaymentResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarkPostPaymentResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MarkPostPaymentResponseMultiError(errors)
	}

	return nil
}

// MarkPostPaymentResponseMultiError is an error wrapping multiple validation
// errors returned by MarkPostPaymentResponse.ValidateAll() if the designated
// constraints aren't met.
type MarkPostPaymentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MarkPostPaymentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MarkPostPaymentResponseMultiError) AllErrors() []error { return m }

// MarkPostPaymentResponseValidationError is the validation error returned by
// MarkPostPaymentResponse.Validate if the designated constraints aren't met.
type MarkPostPaymentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MarkPostPaymentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MarkPostPaymentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MarkPostPaymentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MarkPostPaymentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MarkPostPaymentResponseValidationError) ErrorName() string {
	return "MarkPostPaymentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MarkPostPaymentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMarkPostPaymentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MarkPostPaymentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MarkPostPaymentResponseValidationError{}

// Validate checks the field values on GetPaymentOptionsResponse_PaymentOption
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPaymentOptionsResponse_PaymentOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetPaymentOptionsResponse_PaymentOption with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetPaymentOptionsResponse_PaymentOptionMultiError, or nil if none found.
func (m *GetPaymentOptionsResponse_PaymentOption) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentOptionsResponse_PaymentOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOptionTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "OptionTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "OptionTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOptionTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponse_PaymentOptionValidationError{
				field:  "OptionTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOptionSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "OptionSubtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "OptionSubtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOptionSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponse_PaymentOptionValidationError{
				field:  "OptionSubtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponse_PaymentOptionValidationError{
				field:  "Tag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExpandCollapseState

	// no validation rules for IsCollapsible

	if all {
		switch v := interface{}(m.GetDownArrow()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "DownArrow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "DownArrow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDownArrow()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponse_PaymentOptionValidationError{
				field:  "DownArrow",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PaymentOptionState

	// no validation rules for PaymentOptionType

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponse_PaymentOptionValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaymentOptionCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "PaymentOptionCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "PaymentOptionCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentOptionCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponse_PaymentOptionValidationError{
				field:  "PaymentOptionCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaymentOptionCheckbox()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "PaymentOptionCheckbox",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "PaymentOptionCheckbox",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentOptionCheckbox()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentOptionsResponse_PaymentOptionValidationError{
				field:  "PaymentOptionCheckbox",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Option.(type) {
	case *GetPaymentOptionsResponse_PaymentOption_UpiAccountsOption:
		if v == nil {
			err := GetPaymentOptionsResponse_PaymentOptionValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpiAccountsOption()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
						field:  "UpiAccountsOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
						field:  "UpiAccountsOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpiAccountsOption()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "UpiAccountsOption",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetPaymentOptionsResponse_PaymentOption_IntentOption:
		if v == nil {
			err := GetPaymentOptionsResponse_PaymentOptionValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIntentOption()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
						field:  "IntentOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
						field:  "IntentOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIntentOption()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "IntentOption",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetPaymentOptionsResponse_PaymentOption_UpiIdInputOption:
		if v == nil {
			err := GetPaymentOptionsResponse_PaymentOptionValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpiIdInputOption()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
						field:  "UpiIdInputOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
						field:  "UpiIdInputOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpiIdInputOption()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "UpiIdInputOption",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetPaymentOptionsResponse_PaymentOption_NeftImpsOption:
		if v == nil {
			err := GetPaymentOptionsResponse_PaymentOptionValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNeftImpsOption()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
						field:  "NeftImpsOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
						field:  "NeftImpsOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNeftImpsOption()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "NeftImpsOption",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetPaymentOptionsResponse_PaymentOption_CardPaymentOption:
		if v == nil {
			err := GetPaymentOptionsResponse_PaymentOptionValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCardPaymentOption()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
						field:  "CardPaymentOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
						field:  "CardPaymentOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCardPaymentOption()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "CardPaymentOption",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetPaymentOptionsResponse_PaymentOption_NetbankingOption:
		if v == nil {
			err := GetPaymentOptionsResponse_PaymentOptionValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetbankingOption()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
						field:  "NetbankingOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPaymentOptionsResponse_PaymentOptionValidationError{
						field:  "NetbankingOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetbankingOption()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPaymentOptionsResponse_PaymentOptionValidationError{
					field:  "NetbankingOption",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetPaymentOptionsResponse_PaymentOptionMultiError(errors)
	}

	return nil
}

// GetPaymentOptionsResponse_PaymentOptionMultiError is an error wrapping
// multiple validation errors returned by
// GetPaymentOptionsResponse_PaymentOption.ValidateAll() if the designated
// constraints aren't met.
type GetPaymentOptionsResponse_PaymentOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentOptionsResponse_PaymentOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentOptionsResponse_PaymentOptionMultiError) AllErrors() []error { return m }

// GetPaymentOptionsResponse_PaymentOptionValidationError is the validation
// error returned by GetPaymentOptionsResponse_PaymentOption.Validate if the
// designated constraints aren't met.
type GetPaymentOptionsResponse_PaymentOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentOptionsResponse_PaymentOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentOptionsResponse_PaymentOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentOptionsResponse_PaymentOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentOptionsResponse_PaymentOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentOptionsResponse_PaymentOptionValidationError) ErrorName() string {
	return "GetPaymentOptionsResponse_PaymentOptionValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentOptionsResponse_PaymentOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentOptionsResponse_PaymentOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentOptionsResponse_PaymentOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentOptionsResponse_PaymentOptionValidationError{}

// Validate checks the field values on
// GetEligibleAccountsForPaymentResponse_AccountAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEligibleAccountsForPaymentResponse_AccountAttributes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetEligibleAccountsForPaymentResponse_AccountAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEligibleAccountsForPaymentResponse_AccountAttributesMultiError, or nil
// if none found.
func (m *GetEligibleAccountsForPaymentResponse_AccountAttributes) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEligibleAccountsForPaymentResponse_AccountAttributes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	// no validation rules for MaskAccountNumber

	// no validation rules for IsPrimary

	// no validation rules for PartnerBankName

	// no validation rules for AccountType

	// no validation rules for BankIconUrl

	// no validation rules for AccountProductOffering

	if len(errors) > 0 {
		return GetEligibleAccountsForPaymentResponse_AccountAttributesMultiError(errors)
	}

	return nil
}

// GetEligibleAccountsForPaymentResponse_AccountAttributesMultiError is an
// error wrapping multiple validation errors returned by
// GetEligibleAccountsForPaymentResponse_AccountAttributes.ValidateAll() if
// the designated constraints aren't met.
type GetEligibleAccountsForPaymentResponse_AccountAttributesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEligibleAccountsForPaymentResponse_AccountAttributesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEligibleAccountsForPaymentResponse_AccountAttributesMultiError) AllErrors() []error {
	return m
}

// GetEligibleAccountsForPaymentResponse_AccountAttributesValidationError is
// the validation error returned by
// GetEligibleAccountsForPaymentResponse_AccountAttributes.Validate if the
// designated constraints aren't met.
type GetEligibleAccountsForPaymentResponse_AccountAttributesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEligibleAccountsForPaymentResponse_AccountAttributesValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetEligibleAccountsForPaymentResponse_AccountAttributesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetEligibleAccountsForPaymentResponse_AccountAttributesValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetEligibleAccountsForPaymentResponse_AccountAttributesValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetEligibleAccountsForPaymentResponse_AccountAttributesValidationError) ErrorName() string {
	return "GetEligibleAccountsForPaymentResponse_AccountAttributesValidationError"
}

// Error satisfies the builtin error interface
func (e GetEligibleAccountsForPaymentResponse_AccountAttributesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEligibleAccountsForPaymentResponse_AccountAttributes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEligibleAccountsForPaymentResponse_AccountAttributesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEligibleAccountsForPaymentResponse_AccountAttributesValidationError{}

// Validate checks the field values on GetOrderReceiptResponse_OrderStage with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetOrderReceiptResponse_OrderStage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderReceiptResponse_OrderStage
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetOrderReceiptResponse_OrderStageMultiError, or nil if none found.
func (m *GetOrderReceiptResponse_OrderStage) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderReceiptResponse_OrderStage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for LocalizedDescription

	// no validation rules for ExecutionTime

	// no validation rules for DetailedDescription

	if len(errors) > 0 {
		return GetOrderReceiptResponse_OrderStageMultiError(errors)
	}

	return nil
}

// GetOrderReceiptResponse_OrderStageMultiError is an error wrapping multiple
// validation errors returned by
// GetOrderReceiptResponse_OrderStage.ValidateAll() if the designated
// constraints aren't met.
type GetOrderReceiptResponse_OrderStageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderReceiptResponse_OrderStageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderReceiptResponse_OrderStageMultiError) AllErrors() []error { return m }

// GetOrderReceiptResponse_OrderStageValidationError is the validation error
// returned by GetOrderReceiptResponse_OrderStage.Validate if the designated
// constraints aren't met.
type GetOrderReceiptResponse_OrderStageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderReceiptResponse_OrderStageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderReceiptResponse_OrderStageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderReceiptResponse_OrderStageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderReceiptResponse_OrderStageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderReceiptResponse_OrderStageValidationError) ErrorName() string {
	return "GetOrderReceiptResponse_OrderStageValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderReceiptResponse_OrderStageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderReceiptResponse_OrderStage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderReceiptResponse_OrderStageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderReceiptResponse_OrderStageValidationError{}

// Validate checks the field values on GetOrderReceiptResponse_BankAccountInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetOrderReceiptResponse_BankAccountInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetOrderReceiptResponse_BankAccountInfo with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetOrderReceiptResponse_BankAccountInfoMultiError, or nil if none found.
func (m *GetOrderReceiptResponse_BankAccountInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderReceiptResponse_BankAccountInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BankIconUrl

	// no validation rules for DisplayInfo

	if len(errors) > 0 {
		return GetOrderReceiptResponse_BankAccountInfoMultiError(errors)
	}

	return nil
}

// GetOrderReceiptResponse_BankAccountInfoMultiError is an error wrapping
// multiple validation errors returned by
// GetOrderReceiptResponse_BankAccountInfo.ValidateAll() if the designated
// constraints aren't met.
type GetOrderReceiptResponse_BankAccountInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderReceiptResponse_BankAccountInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderReceiptResponse_BankAccountInfoMultiError) AllErrors() []error { return m }

// GetOrderReceiptResponse_BankAccountInfoValidationError is the validation
// error returned by GetOrderReceiptResponse_BankAccountInfo.Validate if the
// designated constraints aren't met.
type GetOrderReceiptResponse_BankAccountInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderReceiptResponse_BankAccountInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderReceiptResponse_BankAccountInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderReceiptResponse_BankAccountInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderReceiptResponse_BankAccountInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderReceiptResponse_BankAccountInfoValidationError) ErrorName() string {
	return "GetOrderReceiptResponse_BankAccountInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderReceiptResponse_BankAccountInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderReceiptResponse_BankAccountInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderReceiptResponse_BankAccountInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderReceiptResponse_BankAccountInfoValidationError{}

// Validate checks the field values on GetOrderReceiptResponse_PaymentDetail
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetOrderReceiptResponse_PaymentDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderReceiptResponse_PaymentDetail
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetOrderReceiptResponse_PaymentDetailMultiError, or nil if none found.
func (m *GetOrderReceiptResponse_PaymentDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderReceiptResponse_PaymentDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	// no validation rules for Value

	if all {
		switch v := interface{}(m.GetBankAccountInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_PaymentDetailValidationError{
					field:  "BankAccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_PaymentDetailValidationError{
					field:  "BankAccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankAccountInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponse_PaymentDetailValidationError{
				field:  "BankAccountInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOrderReceiptResponse_PaymentDetailMultiError(errors)
	}

	return nil
}

// GetOrderReceiptResponse_PaymentDetailMultiError is an error wrapping
// multiple validation errors returned by
// GetOrderReceiptResponse_PaymentDetail.ValidateAll() if the designated
// constraints aren't met.
type GetOrderReceiptResponse_PaymentDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderReceiptResponse_PaymentDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderReceiptResponse_PaymentDetailMultiError) AllErrors() []error { return m }

// GetOrderReceiptResponse_PaymentDetailValidationError is the validation error
// returned by GetOrderReceiptResponse_PaymentDetail.Validate if the
// designated constraints aren't met.
type GetOrderReceiptResponse_PaymentDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderReceiptResponse_PaymentDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderReceiptResponse_PaymentDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderReceiptResponse_PaymentDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderReceiptResponse_PaymentDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderReceiptResponse_PaymentDetailValidationError) ErrorName() string {
	return "GetOrderReceiptResponse_PaymentDetailValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderReceiptResponse_PaymentDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderReceiptResponse_PaymentDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderReceiptResponse_PaymentDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderReceiptResponse_PaymentDetailValidationError{}

// Validate checks the field values on GetOrderReceiptResponse_ReceiptHead with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetOrderReceiptResponse_ReceiptHead) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderReceiptResponse_ReceiptHead
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetOrderReceiptResponse_ReceiptHeadMultiError, or nil if none found.
func (m *GetOrderReceiptResponse_ReceiptHead) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderReceiptResponse_ReceiptHead) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for Subtitle

	// no validation rules for IconUrl

	// no validation rules for IconColourCode

	// no validation rules for BgColourCode

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponse_ReceiptHeadValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Remarks

	// no validation rules for IsReversal

	// no validation rules for ShortIconUrl

	if all {
		switch v := interface{}(m.GetIconDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "IconDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "IconDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIconDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponse_ReceiptHeadValidationError{
				field:  "IconDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StatusIcon

	if all {
		switch v := interface{}(m.GetBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "Banner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "Banner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponse_ReceiptHeadValidationError{
				field:  "Banner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBaseAmountQuoteCurrency()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "BaseAmountQuoteCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "BaseAmountQuoteCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseAmountQuoteCurrency()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponse_ReceiptHeadValidationError{
				field:  "BaseAmountQuoteCurrency",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplinkBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "DeeplinkBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "DeeplinkBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplinkBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponse_ReceiptHeadValidationError{
				field:  "DeeplinkBanner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTrustedMerchantIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "TrustedMerchantIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "TrustedMerchantIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTrustedMerchantIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponse_ReceiptHeadValidationError{
				field:  "TrustedMerchantIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTxnCategoriesData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "TxnCategoriesData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "TxnCategoriesData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnCategoriesData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponse_ReceiptHeadValidationError{
				field:  "TxnCategoriesData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderReceiptBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "OrderReceiptBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "OrderReceiptBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderReceiptBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponse_ReceiptHeadValidationError{
				field:  "OrderReceiptBanner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomInfoBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "BottomInfoBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptHeadValidationError{
					field:  "BottomInfoBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomInfoBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponse_ReceiptHeadValidationError{
				field:  "BottomInfoBanner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOrderReceiptResponse_ReceiptHeadMultiError(errors)
	}

	return nil
}

// GetOrderReceiptResponse_ReceiptHeadMultiError is an error wrapping multiple
// validation errors returned by
// GetOrderReceiptResponse_ReceiptHead.ValidateAll() if the designated
// constraints aren't met.
type GetOrderReceiptResponse_ReceiptHeadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderReceiptResponse_ReceiptHeadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderReceiptResponse_ReceiptHeadMultiError) AllErrors() []error { return m }

// GetOrderReceiptResponse_ReceiptHeadValidationError is the validation error
// returned by GetOrderReceiptResponse_ReceiptHead.Validate if the designated
// constraints aren't met.
type GetOrderReceiptResponse_ReceiptHeadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderReceiptResponse_ReceiptHeadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderReceiptResponse_ReceiptHeadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderReceiptResponse_ReceiptHeadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderReceiptResponse_ReceiptHeadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderReceiptResponse_ReceiptHeadValidationError) ErrorName() string {
	return "GetOrderReceiptResponse_ReceiptHeadValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderReceiptResponse_ReceiptHeadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderReceiptResponse_ReceiptHead.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderReceiptResponse_ReceiptHeadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderReceiptResponse_ReceiptHeadValidationError{}

// Validate checks the field values on GetOrderReceiptResponse_ReceiptErrorInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetOrderReceiptResponse_ReceiptErrorInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetOrderReceiptResponse_ReceiptErrorInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetOrderReceiptResponse_ReceiptErrorInfoMultiError, or nil if none found.
func (m *GetOrderReceiptResponse_ReceiptErrorInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderReceiptResponse_ReceiptErrorInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptErrorInfoValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptErrorInfoValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponse_ReceiptErrorInfoValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptErrorInfoValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptErrorInfoValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponse_ReceiptErrorInfoValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCtas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrderReceiptResponse_ReceiptErrorInfoValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrderReceiptResponse_ReceiptErrorInfoValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrderReceiptResponse_ReceiptErrorInfoValidationError{
					field:  fmt.Sprintf("Ctas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptErrorInfoValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponse_ReceiptErrorInfoValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponse_ReceiptErrorInfoValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOrderReceiptResponse_ReceiptErrorInfoMultiError(errors)
	}

	return nil
}

// GetOrderReceiptResponse_ReceiptErrorInfoMultiError is an error wrapping
// multiple validation errors returned by
// GetOrderReceiptResponse_ReceiptErrorInfo.ValidateAll() if the designated
// constraints aren't met.
type GetOrderReceiptResponse_ReceiptErrorInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderReceiptResponse_ReceiptErrorInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderReceiptResponse_ReceiptErrorInfoMultiError) AllErrors() []error { return m }

// GetOrderReceiptResponse_ReceiptErrorInfoValidationError is the validation
// error returned by GetOrderReceiptResponse_ReceiptErrorInfo.Validate if the
// designated constraints aren't met.
type GetOrderReceiptResponse_ReceiptErrorInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderReceiptResponse_ReceiptErrorInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderReceiptResponse_ReceiptErrorInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderReceiptResponse_ReceiptErrorInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderReceiptResponse_ReceiptErrorInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderReceiptResponse_ReceiptErrorInfoValidationError) ErrorName() string {
	return "GetOrderReceiptResponse_ReceiptErrorInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderReceiptResponse_ReceiptErrorInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderReceiptResponse_ReceiptErrorInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderReceiptResponse_ReceiptErrorInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderReceiptResponse_ReceiptErrorInfoValidationError{}

// Validate checks the field values on RewardSummarySection_EarnedValueChip
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RewardSummarySection_EarnedValueChip) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardSummarySection_EarnedValueChip
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RewardSummarySection_EarnedValueChipMultiError, or nil if none found.
func (m *RewardSummarySection_EarnedValueChip) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardSummarySection_EarnedValueChip) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardSummarySection_EarnedValueChipValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardSummarySection_EarnedValueChipValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardSummarySection_EarnedValueChipValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShowWashedOutChip

	if len(errors) > 0 {
		return RewardSummarySection_EarnedValueChipMultiError(errors)
	}

	return nil
}

// RewardSummarySection_EarnedValueChipMultiError is an error wrapping multiple
// validation errors returned by
// RewardSummarySection_EarnedValueChip.ValidateAll() if the designated
// constraints aren't met.
type RewardSummarySection_EarnedValueChipMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardSummarySection_EarnedValueChipMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardSummarySection_EarnedValueChipMultiError) AllErrors() []error { return m }

// RewardSummarySection_EarnedValueChipValidationError is the validation error
// returned by RewardSummarySection_EarnedValueChip.Validate if the designated
// constraints aren't met.
type RewardSummarySection_EarnedValueChipValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardSummarySection_EarnedValueChipValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardSummarySection_EarnedValueChipValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardSummarySection_EarnedValueChipValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardSummarySection_EarnedValueChipValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardSummarySection_EarnedValueChipValidationError) ErrorName() string {
	return "RewardSummarySection_EarnedValueChipValidationError"
}

// Error satisfies the builtin error interface
func (e RewardSummarySection_EarnedValueChipValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardSummarySection_EarnedValueChip.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardSummarySection_EarnedValueChipValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardSummarySection_EarnedValueChipValidationError{}

// Validate checks the field values on
// RewardDetailsBottomSheet_RewardDetailsRow with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RewardDetailsBottomSheet_RewardDetailsRow) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RewardDetailsBottomSheet_RewardDetailsRow with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// RewardDetailsBottomSheet_RewardDetailsRowMultiError, or nil if none found.
func (m *RewardDetailsBottomSheet_RewardDetailsRow) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardDetailsBottomSheet_RewardDetailsRow) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardDetailsBottomSheet_RewardDetailsRowValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardDetailsBottomSheet_RewardDetailsRowValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardDetailsBottomSheet_RewardDetailsRowValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardDetailsBottomSheet_RewardDetailsRowValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardDetailsBottomSheet_RewardDetailsRowValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardDetailsBottomSheet_RewardDetailsRowValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEarnedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardDetailsBottomSheet_RewardDetailsRowValidationError{
					field:  "EarnedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardDetailsBottomSheet_RewardDetailsRowValidationError{
					field:  "EarnedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEarnedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardDetailsBottomSheet_RewardDetailsRowValidationError{
				field:  "EarnedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardDetailsBottomSheet_RewardDetailsRowValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardDetailsBottomSheet_RewardDetailsRowValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardDetailsBottomSheet_RewardDetailsRowValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShowWashedOutCard

	if all {
		switch v := interface{}(m.GetDesc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardDetailsBottomSheet_RewardDetailsRowValidationError{
					field:  "Desc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardDetailsBottomSheet_RewardDetailsRowValidationError{
					field:  "Desc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDesc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardDetailsBottomSheet_RewardDetailsRowValidationError{
				field:  "Desc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardDetailsBottomSheet_RewardDetailsRowValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardDetailsBottomSheet_RewardDetailsRowValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardDetailsBottomSheet_RewardDetailsRowValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardDetailsBottomSheet_RewardDetailsRowMultiError(errors)
	}

	return nil
}

// RewardDetailsBottomSheet_RewardDetailsRowMultiError is an error wrapping
// multiple validation errors returned by
// RewardDetailsBottomSheet_RewardDetailsRow.ValidateAll() if the designated
// constraints aren't met.
type RewardDetailsBottomSheet_RewardDetailsRowMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardDetailsBottomSheet_RewardDetailsRowMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardDetailsBottomSheet_RewardDetailsRowMultiError) AllErrors() []error { return m }

// RewardDetailsBottomSheet_RewardDetailsRowValidationError is the validation
// error returned by RewardDetailsBottomSheet_RewardDetailsRow.Validate if the
// designated constraints aren't met.
type RewardDetailsBottomSheet_RewardDetailsRowValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardDetailsBottomSheet_RewardDetailsRowValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardDetailsBottomSheet_RewardDetailsRowValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardDetailsBottomSheet_RewardDetailsRowValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardDetailsBottomSheet_RewardDetailsRowValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardDetailsBottomSheet_RewardDetailsRowValidationError) ErrorName() string {
	return "RewardDetailsBottomSheet_RewardDetailsRowValidationError"
}

// Error satisfies the builtin error interface
func (e RewardDetailsBottomSheet_RewardDetailsRowValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardDetailsBottomSheet_RewardDetailsRow.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardDetailsBottomSheet_RewardDetailsRowValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardDetailsBottomSheet_RewardDetailsRowValidationError{}

// Validate checks the field values on PaymentCtaOperationDetails_Operation
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PaymentCtaOperationDetails_Operation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PaymentCtaOperationDetails_Operation
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PaymentCtaOperationDetails_OperationMultiError, or nil if none found.
func (m *PaymentCtaOperationDetails_Operation) ValidateAll() error {
	return m.validate(true)
}

func (m *PaymentCtaOperationDetails_Operation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PaymentCtaOperationType

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PaymentCtaOperationDetails_OperationValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PaymentCtaOperationDetails_OperationValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PaymentCtaOperationDetails_OperationValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PaymentCtaOperationDetails_OperationValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PaymentCtaOperationDetails_OperationValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PaymentCtaOperationDetails_OperationValidationError{
				field:  "MaxAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.OperationTypeDetails.(type) {
	case *PaymentCtaOperationDetails_Operation_BottomSheet:
		if v == nil {
			err := PaymentCtaOperationDetails_OperationValidationError{
				field:  "OperationTypeDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBottomSheet()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PaymentCtaOperationDetails_OperationValidationError{
						field:  "BottomSheet",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PaymentCtaOperationDetails_OperationValidationError{
						field:  "BottomSheet",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBottomSheet()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PaymentCtaOperationDetails_OperationValidationError{
					field:  "BottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PaymentCtaOperationDetails_Operation_Deeplink:
		if v == nil {
			err := PaymentCtaOperationDetails_OperationValidationError{
				field:  "OperationTypeDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDeeplink()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PaymentCtaOperationDetails_OperationValidationError{
						field:  "Deeplink",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PaymentCtaOperationDetails_OperationValidationError{
						field:  "Deeplink",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PaymentCtaOperationDetails_OperationValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return PaymentCtaOperationDetails_OperationMultiError(errors)
	}

	return nil
}

// PaymentCtaOperationDetails_OperationMultiError is an error wrapping multiple
// validation errors returned by
// PaymentCtaOperationDetails_Operation.ValidateAll() if the designated
// constraints aren't met.
type PaymentCtaOperationDetails_OperationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaymentCtaOperationDetails_OperationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaymentCtaOperationDetails_OperationMultiError) AllErrors() []error { return m }

// PaymentCtaOperationDetails_OperationValidationError is the validation error
// returned by PaymentCtaOperationDetails_Operation.Validate if the designated
// constraints aren't met.
type PaymentCtaOperationDetails_OperationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaymentCtaOperationDetails_OperationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaymentCtaOperationDetails_OperationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaymentCtaOperationDetails_OperationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaymentCtaOperationDetails_OperationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaymentCtaOperationDetails_OperationValidationError) ErrorName() string {
	return "PaymentCtaOperationDetails_OperationValidationError"
}

// Error satisfies the builtin error interface
func (e PaymentCtaOperationDetails_OperationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPaymentCtaOperationDetails_Operation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaymentCtaOperationDetails_OperationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaymentCtaOperationDetails_OperationValidationError{}

// Validate checks the field values on PaymentCtaOperationDetails_BottomSheet
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PaymentCtaOperationDetails_BottomSheet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PaymentCtaOperationDetails_BottomSheet with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// PaymentCtaOperationDetails_BottomSheetMultiError, or nil if none found.
func (m *PaymentCtaOperationDetails_BottomSheet) ValidateAll() error {
	return m.validate(true)
}

func (m *PaymentCtaOperationDetails_BottomSheet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PaymentCtaOperationDetails_BottomSheetValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PaymentCtaOperationDetails_BottomSheetValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PaymentCtaOperationDetails_BottomSheetValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PaymentCtaOperationDetails_BottomSheetValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PaymentCtaOperationDetails_BottomSheetValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PaymentCtaOperationDetails_BottomSheetValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PaymentCtaOperationDetails_BottomSheetValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PaymentCtaOperationDetails_BottomSheetValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PaymentCtaOperationDetails_BottomSheetValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCta() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PaymentCtaOperationDetails_BottomSheetValidationError{
						field:  fmt.Sprintf("Cta[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PaymentCtaOperationDetails_BottomSheetValidationError{
						field:  fmt.Sprintf("Cta[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PaymentCtaOperationDetails_BottomSheetValidationError{
					field:  fmt.Sprintf("Cta[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PaymentCtaOperationDetails_BottomSheetMultiError(errors)
	}

	return nil
}

// PaymentCtaOperationDetails_BottomSheetMultiError is an error wrapping
// multiple validation errors returned by
// PaymentCtaOperationDetails_BottomSheet.ValidateAll() if the designated
// constraints aren't met.
type PaymentCtaOperationDetails_BottomSheetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaymentCtaOperationDetails_BottomSheetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaymentCtaOperationDetails_BottomSheetMultiError) AllErrors() []error { return m }

// PaymentCtaOperationDetails_BottomSheetValidationError is the validation
// error returned by PaymentCtaOperationDetails_BottomSheet.Validate if the
// designated constraints aren't met.
type PaymentCtaOperationDetails_BottomSheetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaymentCtaOperationDetails_BottomSheetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaymentCtaOperationDetails_BottomSheetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaymentCtaOperationDetails_BottomSheetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaymentCtaOperationDetails_BottomSheetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaymentCtaOperationDetails_BottomSheetValidationError) ErrorName() string {
	return "PaymentCtaOperationDetails_BottomSheetValidationError"
}

// Error satisfies the builtin error interface
func (e PaymentCtaOperationDetails_BottomSheetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPaymentCtaOperationDetails_BottomSheet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaymentCtaOperationDetails_BottomSheetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaymentCtaOperationDetails_BottomSheetValidationError{}

// Validate checks the field values on GetAddFundParamsResponse_Tag with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAddFundParamsResponse_Tag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAddFundParamsResponse_Tag with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAddFundParamsResponse_TagMultiError, or nil if none found.
func (m *GetAddFundParamsResponse_Tag) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAddFundParamsResponse_Tag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for IconUrl

	if len(errors) > 0 {
		return GetAddFundParamsResponse_TagMultiError(errors)
	}

	return nil
}

// GetAddFundParamsResponse_TagMultiError is an error wrapping multiple
// validation errors returned by GetAddFundParamsResponse_Tag.ValidateAll() if
// the designated constraints aren't met.
type GetAddFundParamsResponse_TagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAddFundParamsResponse_TagMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAddFundParamsResponse_TagMultiError) AllErrors() []error { return m }

// GetAddFundParamsResponse_TagValidationError is the validation error returned
// by GetAddFundParamsResponse_Tag.Validate if the designated constraints
// aren't met.
type GetAddFundParamsResponse_TagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAddFundParamsResponse_TagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAddFundParamsResponse_TagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAddFundParamsResponse_TagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAddFundParamsResponse_TagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAddFundParamsResponse_TagValidationError) ErrorName() string {
	return "GetAddFundParamsResponse_TagValidationError"
}

// Error satisfies the builtin error interface
func (e GetAddFundParamsResponse_TagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAddFundParamsResponse_Tag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAddFundParamsResponse_TagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAddFundParamsResponse_TagValidationError{}

// Validate checks the field values on GetAddFundParamsResponse_SuggestedAmount
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetAddFundParamsResponse_SuggestedAmount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetAddFundParamsResponse_SuggestedAmount with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetAddFundParamsResponse_SuggestedAmountMultiError, or nil if none found.
func (m *GetAddFundParamsResponse_SuggestedAmount) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAddFundParamsResponse_SuggestedAmount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddFundParamsResponse_SuggestedAmountValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddFundParamsResponse_SuggestedAmountValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddFundParamsResponse_SuggestedAmountValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAddFundParamsResponse_SuggestedAmountValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAddFundParamsResponse_SuggestedAmountValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAddFundParamsResponse_SuggestedAmountValidationError{
					field:  fmt.Sprintf("Tags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAddFundParamsResponse_SuggestedAmountMultiError(errors)
	}

	return nil
}

// GetAddFundParamsResponse_SuggestedAmountMultiError is an error wrapping
// multiple validation errors returned by
// GetAddFundParamsResponse_SuggestedAmount.ValidateAll() if the designated
// constraints aren't met.
type GetAddFundParamsResponse_SuggestedAmountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAddFundParamsResponse_SuggestedAmountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAddFundParamsResponse_SuggestedAmountMultiError) AllErrors() []error { return m }

// GetAddFundParamsResponse_SuggestedAmountValidationError is the validation
// error returned by GetAddFundParamsResponse_SuggestedAmount.Validate if the
// designated constraints aren't met.
type GetAddFundParamsResponse_SuggestedAmountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAddFundParamsResponse_SuggestedAmountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAddFundParamsResponse_SuggestedAmountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAddFundParamsResponse_SuggestedAmountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAddFundParamsResponse_SuggestedAmountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAddFundParamsResponse_SuggestedAmountValidationError) ErrorName() string {
	return "GetAddFundParamsResponse_SuggestedAmountValidationError"
}

// Error satisfies the builtin error interface
func (e GetAddFundParamsResponse_SuggestedAmountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAddFundParamsResponse_SuggestedAmount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAddFundParamsResponse_SuggestedAmountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAddFundParamsResponse_SuggestedAmountValidationError{}

// Validate checks the field values on GetAddFundParamsResponse_RewardsBanner
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetAddFundParamsResponse_RewardsBanner) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetAddFundParamsResponse_RewardsBanner with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetAddFundParamsResponse_RewardsBannerMultiError, or nil if none found.
func (m *GetAddFundParamsResponse_RewardsBanner) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAddFundParamsResponse_RewardsBanner) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	// no validation rules for IconUrl

	if len(errors) > 0 {
		return GetAddFundParamsResponse_RewardsBannerMultiError(errors)
	}

	return nil
}

// GetAddFundParamsResponse_RewardsBannerMultiError is an error wrapping
// multiple validation errors returned by
// GetAddFundParamsResponse_RewardsBanner.ValidateAll() if the designated
// constraints aren't met.
type GetAddFundParamsResponse_RewardsBannerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAddFundParamsResponse_RewardsBannerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAddFundParamsResponse_RewardsBannerMultiError) AllErrors() []error { return m }

// GetAddFundParamsResponse_RewardsBannerValidationError is the validation
// error returned by GetAddFundParamsResponse_RewardsBanner.Validate if the
// designated constraints aren't met.
type GetAddFundParamsResponse_RewardsBannerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAddFundParamsResponse_RewardsBannerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAddFundParamsResponse_RewardsBannerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAddFundParamsResponse_RewardsBannerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAddFundParamsResponse_RewardsBannerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAddFundParamsResponse_RewardsBannerValidationError) ErrorName() string {
	return "GetAddFundParamsResponse_RewardsBannerValidationError"
}

// Error satisfies the builtin error interface
func (e GetAddFundParamsResponse_RewardsBannerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAddFundParamsResponse_RewardsBanner.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAddFundParamsResponse_RewardsBannerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAddFundParamsResponse_RewardsBannerValidationError{}
