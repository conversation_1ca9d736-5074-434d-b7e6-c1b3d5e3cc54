syntax = "proto3";

package frontend.document_upload.polling;

option go_package = "github.com/epifi/gamma/api/frontend/document_upload/polling";
option java_package = "com.github.epifi.gamma.api.frontend.document_upload.polling";

import "api/frontend/document_upload/client_states/enums.proto";

// Provides information for generating and polling for any document
// client needs to call document_upload.service.PollDocumentGeneration if 'DocumentPollingOption' if this is present
message DocumentPollingOption{
  document_upload.clientstate.DocumentType document_type = 1;
  // client consuming the document
  document_upload.clientstate.Client client = 2;
  // duration in seconds for which client should poll
  int32 polling_duration = 3;
  // duration in seconds after which client should retry
  int32 polling_frequency = 4;
  // contains required params for document generation for different document types
  DocumentParams document_params = 5;
}

// required params for document generation/polling
message DocumentParams {
  oneof params {
    ConnectedAccountStatementParams connected_account_statement_params = 1;
  }
}

// document params for connected account statement document_type
message ConnectedAccountStatementParams {
  // account_id of the connected account for which statement is to be generated
  string account_id = 1;
}
