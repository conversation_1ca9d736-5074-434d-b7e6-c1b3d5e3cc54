// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/connected_account/data_analytics/analysis_attempt.pb.go

package data_analytics

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"google.golang.org/protobuf/encoding/protojson"
)

// Valuer interface implementation for storing the AnalysisStatus in string format in DB
func (p AnalysisStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing AnalysisStatus while reading from DB
func (p *AnalysisStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := AnalysisStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected AnalysisStatus value: %s", val)
	}
	*p = AnalysisStatus(valInt)
	return nil
}

// Marshaler interface implementation for AnalysisStatus
func (x AnalysisStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for AnalysisStatus
func (x *AnalysisStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = AnalysisStatus(AnalysisStatus_value[val])
	return nil
}

// Scanner interface implementation for parsing AnalysisRequestParams while reading from DB
func (a *AnalysisRequestParams) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the AnalysisRequestParams in string format in DB
func (a *AnalysisRequestParams) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for AnalysisRequestParams
func (a *AnalysisRequestParams) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for AnalysisRequestParams
func (a *AnalysisRequestParams) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
