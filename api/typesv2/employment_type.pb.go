//go:generate gen_sql -types=EmploymentType

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/employment_type.proto

package typesv2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// possible employment typesv2 of a user
type EmploymentType int32

const (
	EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED                   EmploymentType = 0
	EmploymentType_EMPLOYMENT_TYPE_SALARIED                      EmploymentType = 1
	EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED                 EmploymentType = 2
	EmploymentType_EMPLOYMENT_TYPE_RETIRED                       EmploymentType = 3
	EmploymentType_EMPLOYMENT_TYPE_OTHERS                        EmploymentType = 4
	EmploymentType_EMPLOYMENT_TYPE_BUSINESS_OWNER                EmploymentType = 5
	EmploymentType_EMPLOYMENT_TYPE_FREELANCER                    EmploymentType = 6
	EmploymentType_EMPLOYMENT_TYPE_WORKING_PROFESSIONAL          EmploymentType = 7
	EmploymentType_EMPLOYMENT_TYPE_STUDENT                       EmploymentType = 8
	EmploymentType_EMPLOYMENT_TYPE_HOMEMAKER                     EmploymentType = 9
	EmploymentType_EMPLOYMENT_TYPE_UNEMPLOYED                    EmploymentType = 10
	EmploymentType_EMPLOYMENT_TYPE_PUBLIC_SECTOR                 EmploymentType = 11
	EmploymentType_EMPLOYMENT_TYPE_APPRENTICE                    EmploymentType = 12
	EmploymentType_EMPLOYMENT_TYPE_MILITARY_OR_COMMUNITY_SERVICE EmploymentType = 13
	EmploymentType_EMPLOYMENT_TYPE_EMPLOYED                      EmploymentType = 14
	EmploymentType_EMPLOYMENT_TYPE_ENTREPRENEUR                  EmploymentType = 15
	EmploymentType_EMPLOYMENT_TYPE_PUBLIC_SECTOR_EMPLOYEE        EmploymentType = 16
	EmploymentType_EMPLOYMENT_TYPE_HOUSEWORK                     EmploymentType = 17
	EmploymentType_EMPLOYMENT_TYPE_HOUSEWIFE                     EmploymentType = 18
	EmploymentType_EMPLOYMENT_TYPE_POLITICIAN_OR_STATESMAN       EmploymentType = 19
	EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED_PROFESSIONAL    EmploymentType = 20
)

// Enum value maps for EmploymentType.
var (
	EmploymentType_name = map[int32]string{
		0:  "EMPLOYMENT_TYPE_UNSPECIFIED",
		1:  "EMPLOYMENT_TYPE_SALARIED",
		2:  "EMPLOYMENT_TYPE_SELF_EMPLOYED",
		3:  "EMPLOYMENT_TYPE_RETIRED",
		4:  "EMPLOYMENT_TYPE_OTHERS",
		5:  "EMPLOYMENT_TYPE_BUSINESS_OWNER",
		6:  "EMPLOYMENT_TYPE_FREELANCER",
		7:  "EMPLOYMENT_TYPE_WORKING_PROFESSIONAL",
		8:  "EMPLOYMENT_TYPE_STUDENT",
		9:  "EMPLOYMENT_TYPE_HOMEMAKER",
		10: "EMPLOYMENT_TYPE_UNEMPLOYED",
		11: "EMPLOYMENT_TYPE_PUBLIC_SECTOR",
		12: "EMPLOYMENT_TYPE_APPRENTICE",
		13: "EMPLOYMENT_TYPE_MILITARY_OR_COMMUNITY_SERVICE",
		14: "EMPLOYMENT_TYPE_EMPLOYED",
		15: "EMPLOYMENT_TYPE_ENTREPRENEUR",
		16: "EMPLOYMENT_TYPE_PUBLIC_SECTOR_EMPLOYEE",
		17: "EMPLOYMENT_TYPE_HOUSEWORK",
		18: "EMPLOYMENT_TYPE_HOUSEWIFE",
		19: "EMPLOYMENT_TYPE_POLITICIAN_OR_STATESMAN",
		20: "EMPLOYMENT_TYPE_SELF_EMPLOYED_PROFESSIONAL",
	}
	EmploymentType_value = map[string]int32{
		"EMPLOYMENT_TYPE_UNSPECIFIED":                   0,
		"EMPLOYMENT_TYPE_SALARIED":                      1,
		"EMPLOYMENT_TYPE_SELF_EMPLOYED":                 2,
		"EMPLOYMENT_TYPE_RETIRED":                       3,
		"EMPLOYMENT_TYPE_OTHERS":                        4,
		"EMPLOYMENT_TYPE_BUSINESS_OWNER":                5,
		"EMPLOYMENT_TYPE_FREELANCER":                    6,
		"EMPLOYMENT_TYPE_WORKING_PROFESSIONAL":          7,
		"EMPLOYMENT_TYPE_STUDENT":                       8,
		"EMPLOYMENT_TYPE_HOMEMAKER":                     9,
		"EMPLOYMENT_TYPE_UNEMPLOYED":                    10,
		"EMPLOYMENT_TYPE_PUBLIC_SECTOR":                 11,
		"EMPLOYMENT_TYPE_APPRENTICE":                    12,
		"EMPLOYMENT_TYPE_MILITARY_OR_COMMUNITY_SERVICE": 13,
		"EMPLOYMENT_TYPE_EMPLOYED":                      14,
		"EMPLOYMENT_TYPE_ENTREPRENEUR":                  15,
		"EMPLOYMENT_TYPE_PUBLIC_SECTOR_EMPLOYEE":        16,
		"EMPLOYMENT_TYPE_HOUSEWORK":                     17,
		"EMPLOYMENT_TYPE_HOUSEWIFE":                     18,
		"EMPLOYMENT_TYPE_POLITICIAN_OR_STATESMAN":       19,
		"EMPLOYMENT_TYPE_SELF_EMPLOYED_PROFESSIONAL":    20,
	}
)

func (x EmploymentType) Enum() *EmploymentType {
	p := new(EmploymentType)
	*p = x
	return p
}

func (x EmploymentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmploymentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_employment_type_proto_enumTypes[0].Descriptor()
}

func (EmploymentType) Type() protoreflect.EnumType {
	return &file_api_typesv2_employment_type_proto_enumTypes[0]
}

func (x EmploymentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmploymentType.Descriptor instead.
func (EmploymentType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_employment_type_proto_rawDescGZIP(), []int{0}
}

var File_api_typesv2_employment_type_proto protoreflect.FileDescriptor

var file_api_typesv2_employment_type_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2a, 0xf2, 0x05, 0x0a, 0x0e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x49, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f,
	0x59, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x54, 0x49, 0x52, 0x45, 0x44,
	0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53, 0x10, 0x04, 0x12, 0x22,
	0x0a, 0x1e, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x52,
	0x10, 0x05, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x52,
	0x10, 0x06, 0x12, 0x28, 0x0a, 0x24, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x52,
	0x4f, 0x46, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x07, 0x12, 0x1b, 0x0a, 0x17,
	0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x53, 0x54, 0x55, 0x44, 0x45, 0x4e, 0x54, 0x10, 0x08, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x4d, 0x50,
	0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x4f, 0x4d,
	0x45, 0x4d, 0x41, 0x4b, 0x45, 0x52, 0x10, 0x09, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x4d, 0x50, 0x4c,
	0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x45, 0x4d,
	0x50, 0x4c, 0x4f, 0x59, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x4d, 0x50, 0x4c,
	0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x55, 0x42, 0x4c,
	0x49, 0x43, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x0b, 0x12, 0x1e, 0x0a, 0x1a, 0x45,
	0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41,
	0x50, 0x50, 0x52, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x45, 0x10, 0x0c, 0x12, 0x31, 0x0a, 0x2d, 0x45,
	0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d,
	0x49, 0x4c, 0x49, 0x54, 0x41, 0x52, 0x59, 0x5f, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x55,
	0x4e, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x0d, 0x12, 0x1c,
	0x0a, 0x18, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x45, 0x44, 0x10, 0x0e, 0x12, 0x20, 0x0a, 0x1c,
	0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x45, 0x4e, 0x54, 0x52, 0x45, 0x50, 0x52, 0x45, 0x4e, 0x45, 0x55, 0x52, 0x10, 0x0f, 0x12, 0x2a,
	0x0a, 0x26, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f,
	0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x45, 0x45, 0x10, 0x10, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x4d,
	0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x4f,
	0x55, 0x53, 0x45, 0x57, 0x4f, 0x52, 0x4b, 0x10, 0x11, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x4d, 0x50,
	0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x4f, 0x55,
	0x53, 0x45, 0x57, 0x49, 0x46, 0x45, 0x10, 0x12, 0x12, 0x2b, 0x0a, 0x27, 0x45, 0x4d, 0x50, 0x4c,
	0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49,
	0x54, 0x49, 0x43, 0x49, 0x41, 0x4e, 0x5f, 0x4f, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x53,
	0x4d, 0x41, 0x4e, 0x10, 0x13, 0x12, 0x2e, 0x0a, 0x2a, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x45, 0x4d,
	0x50, 0x4c, 0x4f, 0x59, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x45, 0x53, 0x53, 0x49, 0x4f,
	0x4e, 0x41, 0x4c, 0x10, 0x14, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x5a, 0x22, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_employment_type_proto_rawDescOnce sync.Once
	file_api_typesv2_employment_type_proto_rawDescData = file_api_typesv2_employment_type_proto_rawDesc
)

func file_api_typesv2_employment_type_proto_rawDescGZIP() []byte {
	file_api_typesv2_employment_type_proto_rawDescOnce.Do(func() {
		file_api_typesv2_employment_type_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_employment_type_proto_rawDescData)
	})
	return file_api_typesv2_employment_type_proto_rawDescData
}

var file_api_typesv2_employment_type_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_employment_type_proto_goTypes = []interface{}{
	(EmploymentType)(0), // 0: api.typesv2.EmploymentType
}
var file_api_typesv2_employment_type_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_typesv2_employment_type_proto_init() }
func file_api_typesv2_employment_type_proto_init() {
	if File_api_typesv2_employment_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_employment_type_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_employment_type_proto_goTypes,
		DependencyIndexes: file_api_typesv2_employment_type_proto_depIdxs,
		EnumInfos:         file_api_typesv2_employment_type_proto_enumTypes,
	}.Build()
	File_api_typesv2_employment_type_proto = out.File
	file_api_typesv2_employment_type_proto_rawDesc = nil
	file_api_typesv2_employment_type_proto_goTypes = nil
	file_api_typesv2_employment_type_proto_depIdxs = nil
}
