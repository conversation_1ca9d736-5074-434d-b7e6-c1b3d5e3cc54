syntax = "proto3";

package api.typesv2;

import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

message FormField {
  // key to uniquely identify each field
  // while sending response back client needs to pass this key along with field response
  string identifier = 1;
  // type of answer expected for the given question
  FieldType field_type = 2;
  // additional details require based on the field type
  // EX: could be list of choices for dropdown type or values
  FieldOptions field_options = 3;
  // to check if this field is compulsory and always needed to be filled
  bool is_mandatory = 4;
}

enum FieldType {
  FIELD_TYPE_UNSPECIFIED = 0;
  FIELD_TYPE_TEXT = 1;
  FIELD_TYPE_DROPDOWN = 2;
  FIELD_TYPE_CHECKBOX = 3;
  FIELD_TYPE_RADIO_BUTTON = 4;
}

message FieldOptions {
  oneof options {
    TextFieldOptions text_field_options = 1;
    DropdownFieldOptions dropdown_field_options = 2;
    CheckboxFieldOptions checkbox_field_options = 3;
  }
}

message TextFieldOptions {
  // client needs to fill this value which is entered by user
  // if entered in screen options, will be used as prefilled value
  typesv2.common.Text value = 1;
  // To be shown to user when value is empty as a suggestive text of the field
  typesv2.common.Text placeholder = 2;
  // edit icon, if empty string, no need to show any icon
  string edit_icon = 3;
  // type according to which client adds validation
  TextType text_type = 4;
  // if this true, user cannot edit this field
  // use in case when just to show this details to user and make it non editable
  bool is_not_editable = 5;
}

enum TextType {
  TEXT_TYPE_UNSPECIFIED = 0;
  TEXT_TYPE_PAN = 1;
  TEXT_TYPE_AADHAAR = 2;
  TEXT_TYPE_PHONE_NUMBER = 3;
  TEXT_TYPE_FULL_NAME = 4;
}

message DropdownFieldOptions {
  // list of options to be shown for the dropdown field type
  repeated string choices = 1 [deprecated = true];
  // client needs to fill this value which is entered by user from choices
  string value = 2;

  // Heading for dropdown
  typesv2.common.Text heading = 3;

  // Drop down choices
  repeated Option drop_down_choices = 4;

  typesv2.common.Text hint = 5;

  // Individual Option if is selected by default or not.
  message Option {
    typesv2.common.Text text = 1;
    typesv2.common.Image icon = 2;

    // Is the DropDown is Selected by Default or not
    bool is_selected = 3;
  }
}

message CheckboxFieldOptions {
  // multiple options for checkboxes
  repeated Option options = 1;

  message Option {
    // text to show user along with the tick box
    string value = 1;
    // client needs to update this value when clicked by user as toggle
    // if true in screen options, will be ticked by default
    bool is_selected = 2;
  }
}
