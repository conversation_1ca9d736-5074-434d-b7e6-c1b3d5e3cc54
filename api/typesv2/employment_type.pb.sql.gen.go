// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/typesv2/employment_type.pb.go

package typesv2

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the EmploymentType in string format in DB
func (p EmploymentType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing EmploymentType while reading from DB
func (p *EmploymentType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := EmploymentType_value[val]
	if !ok {
		return fmt.Errorf("unexpected EmploymentType value: %s", val)
	}
	*p = EmploymentType(valInt)
	return nil
}

// Marshaler interface implementation for EmploymentType
func (x EmploymentType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for EmploymentType
func (x *EmploymentType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = EmploymentType(EmploymentType_value[val])
	return nil
}
