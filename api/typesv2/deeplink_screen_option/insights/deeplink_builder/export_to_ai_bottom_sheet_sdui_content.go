//nolint:dupl,gocritic
package deeplink_builder

import (
	"context"
	"fmt"

	anyPb "google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/pkg/epificontext"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/assetandanalysis"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

const (
	title        = "Talk Money to AI"
	contentTitle = "How would you like to proceed?"
	beta         = "BETA"

	aiSectionHeader     = "Share your finances with\nChatGPT, Gemini & more"
	shareSpendsTitle    = "Share & ask AI to analyse your spends"
	shareWithYourAiApps = "Share with your AI app"

	download = "No AI app on your phone? Download & share "

	colorFog200 = "#E6E9ED"

	iconMcp    = "https://epifi-icons.pointz.in/networth/mcp/profile_icon.png"
	iconAiApps = "https://epifi-icons.pointz.in/networth/mcp/ai_apps.png"
	iconStar   = "https://epifi-icons.pointz.in/networth/mcp/star.png"

	iconArrowUpRight        = "https://epifi-icons.pointz.in/networth/mcp/arrow_upright_green.png"
	iconArrowUpRightPrimary = "https://epifi-icons.pointz.in/networth/mcp/arrow_upright_white.png"
	iconDownload            = "https://epifi-icons.pointz.in/networth/mcp/download_ash.png"
	iconArrowRight          = "https://epifi-icons.pointz.in/networth/mcp/chevron_right_ash.png"
	iconArrowRightPrimary   = "https://epifi-icons.pointz.in/networth/mcp/chevron_right_green.png"

	disclaimerText         = "*Disclaimer: Fi MCP enables secure, encrypted transmission of your selected financial data to an AI assistant of "
	disclaimerTextReadMore = "..read more"
	disclaimerFullText     = "*Disclaimer: Fi MCP enables secure, encrypted transmission of your selected financial data to an AI tool of your choice. Fi does not own, operate, or control these AI tools and is not responsible for their outputs or actions. All interactions with the AI tool are private and cannot be accessed or stored by Fi. Use of this service is entirely at your own discretion and risk. Fi assumes no liability once data is transmitted as per your instructions. Terms and Conditions apply."
)

// TODO(Shweta/Nigam): refactor this file to have common code for Android & IOS
// nolint: gosec
func (d *DeeplinkBuilder) exportToAiBottomSheetSduiContent(ctx context.Context) (*sections.Section, error) {
	bottomSheetComponents := make([]*components.Component, 0)
	// Title
	bottomSheetComponents = append(
		bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				commontypes.GetTextFromStringFontColourFontStyleFontAlignment(title, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_HEADLINE_L, commontypes.Text_ALIGNMENT_CENTER),
			),
		},
	)

	// Content spacer
	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&components.Spacer{
					SpacingValue: components.Spacing_SPACING_XL,
				},
			),
		},
	)

	// Content Title
	bottomSheetComponents = append(
		bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				commontypes.GetTextFromStringFontColourFontStyleFontAlignment(contentTitle, colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_SUBTITLE_M, commontypes.Text_ALIGNMENT_CENTER),
			),
		},
	)

	// Components
	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&components.Spacer{
					SpacingValue: components.Spacing_SPACING_M,
				},
			),
		},
	)

	var isSpendsShareEnabled bool
	platform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	switch platform {
	case commontypes.Platform_IOS:
		isSpendsShareEnabled = uint32(appVersion) >= d.config.NetworthConfig().NetworthMcp().AppVersionsForNetworthDataReqPayload().MinVersionIos()
		optionsSection := d.getCardsForiOS(isSpendsShareEnabled)
		bottomSheetComponents = append(bottomSheetComponents, optionsSection...)
	case commontypes.Platform_ANDROID:
		isSpendsShareEnabled = uint32(appVersion) >= d.config.NetworthConfig().NetworthMcp().AppVersionsForNetworthDataReqPayload().MinVersionAndroid()
		optionsSection := d.getCardsForAndroid(isSpendsShareEnabled)
		bottomSheetComponents = append(bottomSheetComponents, optionsSection...)
	default:
		return nil, fmt.Errorf("unsupported platform")
	}

	// Spacer for disclaimer
	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&components.Spacer{
					SpacingValue: components.Spacing_SPACING_M,
				},
			),
		},
	)

	bottomSheetComponents = append(
		bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				ui.NewITC().WithTexts(commontypes.GetTextFromHtmlStringWithStyleAndAlignment(disclaimerText+"<span style=\"color: #00B899\">"+disclaimerTextReadMore+"</span>", colors.ColorMonochromeAsh, commontypes.FontStyle_BODY_XS, commontypes.Text_ALIGNMENT_CENTER)),
			),
			InteractionBehaviors: []*behaviors.InteractionBehavior{
				{
					Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
						OnClickBehavior: &behaviors.OnClickBehavior{
							Action: ui.GetAnyWithoutError(
								&deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_WEB_PAGE,
									ScreenOptions: &deeplinkPb.Deeplink_WebPageScreenOptions{
										WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{
											WebpageUrl: d.config.NetworthConfig().NetworthMcp().MCPTermsConditions(),
										},
									},
								},
							),
						},
					},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName: "ClickedDisclaimer",
					},
				},
			},
		},
	)

	return &sections.Section{
		Content: &sections.Section_VerticalListSection{VerticalListSection: &sections.VerticalListSection{
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							Padding: &properties.PaddingProperty{
								Left:   24,
								Right:  24,
								Bottom: 56,
							},
						},
					},
				},
			},
			Components:          bottomSheetComponents,
			HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
			LoadBehavior: &behaviors.LifecycleBehavior{
				Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
				AnalyticsEvent: &analytics.AnalyticsEvent{
					EventName: "LoadedMCPBottomSheet",
				},
			},
		}},
	}, nil
}

func (d *DeeplinkBuilder) getCardsForAndroid(isSpendsShareEnabled bool) []*components.Component {
	cards := make([]*components.Component, 0)
	cards = append(cards,
		&components.Component{
			Content: shareToAiContentAndroid(
				d.config.NetworthConfig().NetworthMcp().AllowedMCPAndroidApps().ToStringArray(),
			),
		},
	)

	if isSpendsShareEnabled {
		// share spends to AI spacer
		cards = append(cards, &components.Component{
			Content: ui.GetAnyWithoutError(&components.Spacer{
				SpacingValue: components.Spacing_SPACING_M,
			}),
		})

		// share spends to AI content
		cards = append(cards, &components.Component{Content: shareSpendsToAiContentAndroid(d.config.NetworthConfig().NetworthMcp().AllowedMCPAndroidApps().ToStringArray())})

	}

	// Download to device content
	cards = append(cards,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&components.Spacer{
					SpacingValue: components.Spacing_SPACING_M,
				},
			),
		},
	)

	cards = append(cards,
		&components.Component{
			Content: downloadCard(),
		},
	)

	return cards
}

func (d *DeeplinkBuilder) getCardsForiOS(isSpendsShareEnabled bool) []*components.Component {
	cards := make([]*components.Component, 0)
	cards = append(cards,
		&components.Component{
			Content: shareToAiContentiOS(
				d.config.NetworthConfig().NetworthMcp().AllowedMCPAndroidApps().ToStringArray(),
			),
		},
	)

	if isSpendsShareEnabled {
		// share spends to AI spacer
		cards = append(cards, &components.Component{
			Content: ui.GetAnyWithoutError(&components.Spacer{
				SpacingValue: components.Spacing_SPACING_M,
			}),
		})

		// share spends to AI content
		cards = append(cards, &components.Component{Content: shareSpendsToAiContentIos()})

	}

	// Download to device content
	cards = append(cards,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&components.Spacer{
					SpacingValue: components.Spacing_SPACING_M,
				},
			),
		},
	)

	cards = append(cards,
		&components.Component{
			Content: downloadCardiOS(),
		},
	)
	return cards
}

// shareToAiContentAndroid returns share to AI card
// Figma : https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=13604-13371&t=ISEPfND5p0sjltDB-4
func shareToAiContentAndroid(allowedAndroidApps []string) *anyPb.Any {
	depthWiseComponents := make([]*components.Component, 0)
	// Content
	depthWiseComponents = append(depthWiseComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&sections.HorizontalListSection{
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Padding: &properties.PaddingProperty{
										Left:   12,
										Right:  12,
										Top:    16,
										Bottom: 16,
									},
								},
							},
						},
					},
					Components: []*components.Component{
						// Title & CTA
						{
							Content: ui.GetAnyWithoutError(
								&sections.VerticalListSection{
									VisualProperties: []*properties.VisualProperty{
										{
											Properties: &properties.VisualProperty_Size{
												Size: &properties.Size{
													Width: &properties.Size_Dimension{
														Type: properties.Size_Dimension_DIMENSION_TYPE_WEIGHT,
														Weight: &properties.Weight{
															Value: float32(1),
														},
													},
													Height: &properties.Size_Dimension{
														Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
													},
												},
											},
										},
									},
									Components: []*components.Component{
										// Title
										{
											Content: ui.GetAnyWithoutError(
												ui.NewITC().
													WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(iconStar, 24, 24)).
													WithLeftImagePadding(4).
													WithTexts(
														commontypes.GetTextFromStringFontColourFontStyleFontAlignment(aiSectionHeader, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_S, commontypes.Text_ALIGNMENT_LEFT),
													),
											),
										},
										// Spacer
										{
											Content: ui.GetAnyWithoutError(
												&components.Spacer{
													SpacingValue: components.Spacing_SPACING_M,
												},
											),
										},
										// Share with your AI App
										{
											Content: ui.GetAnyWithoutError(
												&sections.DepthWiseListSection{
													InteractionBehaviors: []*behaviors.InteractionBehavior{
														{
															Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
																OnClickBehavior: &behaviors.OnClickBehavior{
																	Action: ui.GetAnyWithoutError(
																		&deeplinkPb.Deeplink{
																			Screen: deeplinkPb.Screen_EXPORT_NETWORTH_DATA,
																			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(
																				&assetandanalysis.ExportNetworthDataScreenOptions{
																					AllowedAndroidApps:         allowedAndroidApps,
																					GetNetworthDataFilePayload: "{\"networthDataFilePurpose\":\"NETWORTH_DATA_FILE_PURPOSE_EXPORT\", \"networthDataFileType\":\"NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA\"}",
																				},
																			),
																		},
																	),
																},
															},
															AnalyticsEvent: &analytics.AnalyticsEvent{
																EventName: "ClickedShareWithAI",
															},
														},
													},
													Components: []*components.Component{
														{
															Content: ui.GetAnyWithoutError(
																ui.NewITC().
																	WithTexts(
																		commontypes.GetTextFromStringFontColourFontStyle(shareWithYourAiApps, colors.ColorSnow, commontypes.FontStyle_BUTTON_S),
																	).
																	WithContainerWidgetBackgroundColor(
																		widget.GetBlockBackgroundColour(colors.ColorLightPrimaryAction),
																	).
																	WithContainerCornerRadius(40).
																	WithContainerPaddingSymmetrical(16, 12).
																	WithRightImagePadding(8).
																	WithRightVisualElement(
																		commontypes.GetVisualElementFromUrlHeightAndWidth(iconArrowUpRightPrimary, 24, 24),
																	),
															),
														},
													},
												},
											),
										},
									},
									HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
								},
							),
						},
						// Spacer
						{
							Content: ui.GetAnyWithoutError(
								&components.Spacer{
									SpacingValue: components.Spacing_SPACING_XS,
								},
							),
						},
						// AI Apps icon
						{
							Content: ui.GetAnyWithoutError(
								commontypes.GetVisualElementFromUrlHeightAndWidth(iconAiApps, 80, 80),
							),
						},
					},
				},
			),
		},
	)
	// Beta
	depthWiseComponents = append(depthWiseComponents,
		&components.Component{
			Content: betaText(),
		},
	)
	return ui.GetAnyWithoutError(
		&sections.DepthWiseListSection{
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_Border{
						Border: &properties.BorderProperty{
							BorderThickness: 1,
							BorderColor:     colorFog200,
							CornerRadius:    16,
						},
					},
				},
				{
					Properties: &properties.VisualProperty_Size{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
							},
						},
					},
				},
			},
			Components: depthWiseComponents,
			Alignment:  sections.DepthWiseListSection_TOP_RIGHT,
		},
	)
}

func shareSpendsToAiContentAndroid(allowedAndroidApps []string) *anyPb.Any {
	depthWiseComponents := make([]*components.Component, 0)
	// Content
	depthWiseComponents = append(depthWiseComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&sections.VerticalListSection{
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Padding: &properties.PaddingProperty{
										Left:   12,
										Right:  12,
										Top:    16,
										Bottom: 16,
									},
								},
							},
						},
						{
							Properties: &properties.VisualProperty_Size{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
									Height: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
									},
								},
							},
						},
					},
					Components: []*components.Component{
						// Title
						{
							Content: ui.GetAnyWithoutError(ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(iconStar, 24, 24)).
								WithLeftImagePadding(4).WithTexts(commontypes.GetTextFromStringFontColourFontStyle(shareSpendsTitle, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_S))),
						},
					},
					HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
				},
			),
		},
	)
	// Beta
	depthWiseComponents = append(depthWiseComponents,
		&components.Component{
			Content: betaText(),
		},
	)
	return ui.GetAnyWithoutError(
		&sections.DepthWiseListSection{
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_Border{
						Border: &properties.BorderProperty{
							BorderThickness: 1,
							BorderColor:     colorFog200,
							CornerRadius:    16,
						},
					},
				},
				{
					Properties: &properties.VisualProperty_Size{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
							},
						},
					},
				},
			},
			Components: depthWiseComponents,
			Alignment:  sections.DepthWiseListSection_TOP_RIGHT,
			InteractionBehaviors: []*behaviors.InteractionBehavior{
				{
					Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
						OnClickBehavior: &behaviors.OnClickBehavior{
							Action: ui.GetAnyWithoutError(
								&deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_EXPORT_NETWORTH_DATA,
									ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&assetandanalysis.ExportNetworthDataScreenOptions{
										AllowedAndroidApps:         allowedAndroidApps,
										GetNetworthDataFilePayload: "{\"networthDataFilePurpose\":\"NETWORTH_DATA_FILE_PURPOSE_EXPORT\", \"networthDataFileType\":\"NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA\"}",
									}),
								},
							),
						},
					},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName: "ClickedShareWithAIForSpends",
					},
				},
			},
		},
	)
}

// betaText returns BETA tag show on cards at top right
func betaText() *anyPb.Any {
	return ui.GetAnyWithoutError(
		&sections.DepthWiseListSection{
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							Padding: &properties.PaddingProperty{
								Left:   8,
								Right:  8,
								Top:    6,
								Bottom: 6,
							},
							BgColor: widget.GetBlockBackgroundColour(colors.ColorLightLayer1),
							Corner: &properties.CornerProperty{
								TopRightCornerRadius: 12,
								BottomLeftCorner:     12,
							},
						},
					},
				},
			},
			Components: []*components.Component{
				{
					Content: ui.GetAnyWithoutError(
						commontypes.GetTextFromStringFontColourFontStyle(beta, colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_OVERLINE_2XS_CAPS),
					),
				},
			},
		},
	)
}

// downloadCard returns download to device card
// Figma : https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=13604-14043&t=ISEPfND5p0sjltDB-4
func downloadCard() *anyPb.Any {
	downloadCardComponents := []*components.Component{
		// Download icon
		{
			Content: ui.GetAnyWithoutError(
				commontypes.GetVisualElementFromUrlHeightAndWidth(iconDownload, 24, 24),
			),
		},
		// Text
		{
			Content: ui.GetAnyWithoutError(
				&sections.VerticalListSection{
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_Size{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_WEIGHT,
										Weight: &properties.Weight{
											Value: float32(1),
										},
									},
									Height: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
									},
								},
							},
						},
						{
							Properties: &properties.VisualProperty_Padding{
								Padding: &properties.PaddingProperty{
									Left:  4,
									Right: 4,
								},
							},
						},
					},
					Components: []*components.Component{
						{
							Content: ui.GetAnyWithoutError(
								commontypes.GetTextFromStringFontColourFontStyle(download, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_S),
							),
						},
					},
					HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
				},
			),
		},
	}
	return ui.GetAnyWithoutError(
		&sections.HorizontalListSection{
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_Border{
						Border: &properties.BorderProperty{
							BorderThickness: 1,
							BorderColor:     colorFog200,
							CornerRadius:    16,
						},
					},
				},
				{
					Properties: &properties.VisualProperty_Size{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
							},
						},
					},
				},
				{
					Properties: &properties.VisualProperty_Padding{
						Padding: &properties.PaddingProperty{
							Left:   12,
							Right:  12,
							Top:    12,
							Bottom: 12,
						},
					},
				},
			},
			Components:            downloadCardComponents,
			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
			VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
			InteractionBehaviors: []*behaviors.InteractionBehavior{
				{
					Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
						OnClickBehavior: &behaviors.OnClickBehavior{
							Action: ui.GetAnyWithoutError(
								&deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_DOWNLOAD_NETWORTH_DATA,
									ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(
										&assetandanalysis.DownloadNetworthDataScreenOptions{
											GetNetworthDataFilePayload: "{\"networthDataFilePurpose\":\"NETWORTH_DATA_FILE_PURPOSE_DOWNLOAD\", \"networthDataFileType\":\"NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA\"}",
										},
									),
								},
							),
						},
					},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName: "ClickedDownloadNetWorth",
					},
				},
			},
		},
	)
}

func shareToAiContentiOS(allowedAndroidApps []string) *anyPb.Any {
	depthWiseComponents := make([]*components.Component, 0)
	// Content
	depthWiseComponents = append(depthWiseComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&sections.HorizontalListSection{
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Padding: &properties.PaddingProperty{
										Left:   8,
										Right:  8,
										Top:    16,
										Bottom: 16,
									},
								},
							},
						},
						{
							Properties: &properties.VisualProperty_Size{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
									Height: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
									},
								},
							},
						},
					},
					Components: []*components.Component{
						// Title & CTA
						{
							Content: ui.GetAnyWithoutError(
								&sections.VerticalListSection{
									HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
									Components: []*components.Component{
										// Title
										{
											Content: ui.GetAnyWithoutError(
												ui.NewITC().
													WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(iconStar, 24, 24)).
													WithLeftImagePadding(4).
													WithTexts(
														commontypes.GetTextFromStringFontColourFontStyleFontAlignment(aiSectionHeader, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_S, commontypes.Text_ALIGNMENT_LEFT),
													),
											),
										},
										// Spacer
										{
											Content: ui.GetAnyWithoutError(
												&components.Spacer{
													SpacingValue: components.Spacing_SPACING_M,
												},
											),
										},
										// Share with your AI App
										{
											Content: ui.GetAnyWithoutError(
												ui.NewITC().
													WithTexts(
														commontypes.GetTextFromStringFontColourFontStyle(shareWithYourAiApps, colors.ColorSnow, commontypes.FontStyle_BUTTON_S),
													).
													WithContainerWidgetBackgroundColor(
														widget.GetBlockBackgroundColour(colors.ColorLightPrimaryAction),
													).
													WithContainerCornerRadius(40).
													WithContainerPaddingSymmetrical(16, 12).
													WithRightImagePadding(8).
													WithRightVisualElement(
														commontypes.GetVisualElementFromUrlHeightAndWidth(iconArrowUpRightPrimary, 24, 24),
													),
											),
											InteractionBehaviors: []*behaviors.InteractionBehavior{
												{
													Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
														OnClickBehavior: &behaviors.OnClickBehavior{
															Action: ui.GetAnyWithoutError(
																&deeplinkPb.Deeplink{
																	Screen: deeplinkPb.Screen_EXPORT_NETWORTH_DATA,
																	ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(
																		&assetandanalysis.ExportNetworthDataScreenOptions{
																			AllowedAndroidApps:         allowedAndroidApps,
																			GetNetworthDataFilePayload: "{\"networthDataFilePurpose\":\"NETWORTH_DATA_FILE_PURPOSE_EXPORT\", \"networthDataFileType\":\"NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA\"}",
																		},
																	),
																},
															),
														},
													},
													AnalyticsEvent: &analytics.AnalyticsEvent{
														EventName: "ClickedShareWithAI",
													},
												},
											},
										},
									},
								},
							),
						},
						// Spacer
						{
							Content: ui.GetAnyWithoutError(
								&components.Spacer{
									SpacingValue: components.Spacing_SPACING_XS,
								},
							),
						},
						// AI Apps icon
						{
							Content: ui.GetAnyWithoutError(
								commontypes.GetVisualElementFromUrlHeightAndWidth(iconAiApps, 80, 80),
							),
						},
					},
				},
			),
		},
	)
	// Beta
	depthWiseComponents = append(depthWiseComponents,
		&components.Component{
			Content: betaText(),
		},
	)
	return ui.GetAnyWithoutError(
		&sections.DepthWiseListSection{
			Alignment: sections.DepthWiseListSection_TOP_RIGHT,
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_Border{
						Border: &properties.BorderProperty{
							BorderThickness: 1,
							BorderColor:     colorFog200,
							CornerRadius:    16,
						},
					},
				},
				{
					Properties: &properties.VisualProperty_Size{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
							},
						},
					},
				},
			},
			Components: depthWiseComponents,
		},
	)
}

func shareSpendsToAiContentIos() *anyPb.Any {
	depthWiseComponents := make([]*components.Component, 0)
	// Content
	depthWiseComponents = append(depthWiseComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&sections.HorizontalListSection{
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Padding: &properties.PaddingProperty{
										Left:   8,
										Right:  8,
										Top:    16,
										Bottom: 16,
									},
								},
							},
						},
						{
							Properties: &properties.VisualProperty_Size{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
									Height: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
									},
								},
							},
						},
					},
					Components: []*components.Component{
						// Title
						{
							Content: ui.GetAnyWithoutError(
								&sections.VerticalListSection{
									HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
									Components: []*components.Component{
										// Title
										{
											Content: ui.GetAnyWithoutError(ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(iconStar, 24, 24)).
												WithLeftImagePadding(4).WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(shareSpendsTitle, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_S, commontypes.Text_ALIGNMENT_LEFT)),
											),
										},
									},
								},
							),
						},
					},
				},
			),
		},
	)
	// Beta
	depthWiseComponents = append(depthWiseComponents,
		&components.Component{
			Content: betaText(),
		},
	)
	return ui.GetAnyWithoutError(
		&sections.DepthWiseListSection{
			Alignment: sections.DepthWiseListSection_TOP_RIGHT,
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_Border{
						Border: &properties.BorderProperty{
							BorderThickness: 1,
							BorderColor:     colorFog200,
							CornerRadius:    16,
						},
					},
				},
				{
					Properties: &properties.VisualProperty_Size{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
							},
						},
					},
				},
			},
			Components: depthWiseComponents,
			InteractionBehaviors: []*behaviors.InteractionBehavior{
				{
					Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
						OnClickBehavior: &behaviors.OnClickBehavior{
							Action: ui.GetAnyWithoutError(
								&deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_EXPORT_NETWORTH_DATA,
									ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(
										&assetandanalysis.ExportNetworthDataScreenOptions{
											GetNetworthDataFilePayload: "{\"networthDataFilePurpose\":\"NETWORTH_DATA_FILE_PURPOSE_EXPORT\", \"networthDataFileType\":\"NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA\"}",
										},
									),
								},
							),
						},
					},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName: "ClickedShareWithAI",
					},
				},
			},
		},
	)
}

// downloadCard returns download to device card
// Figma : https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=13604-14043&t=ISEPfND5p0sjltDB-4
func downloadCardiOS() *anyPb.Any {
	downloadCardComponents := []*components.Component{
		// Download icon
		{
			Content: ui.GetAnyWithoutError(
				commontypes.GetVisualElementFromUrlHeightAndWidth(iconDownload, 24, 24),
			),
		},
		// Text
		{
			Content: ui.GetAnyWithoutError(
				commontypes.GetTextFromStringFontColourFontStyle(download, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_S),
			),
		},
	}
	return ui.GetAnyWithoutError(
		&sections.HorizontalListSection{
			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
			VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_Size{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
							},
						},
					},
				},
				{
					Properties: &properties.VisualProperty_Padding{
						Padding: &properties.PaddingProperty{
							Left:   12,
							Right:  12,
							Top:    12,
							Bottom: 12,
						},
					},
				},
				{
					Properties: &properties.VisualProperty_Border{
						Border: &properties.BorderProperty{
							BorderThickness: 1,
							BorderColor:     colorFog200,
							CornerRadius:    16,
						},
					},
				},
			},
			Components: downloadCardComponents,
			InteractionBehaviors: []*behaviors.InteractionBehavior{
				{
					Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
						OnClickBehavior: &behaviors.OnClickBehavior{
							Action: ui.GetAnyWithoutError(
								&deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_DOWNLOAD_NETWORTH_DATA,
									ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(
										&assetandanalysis.DownloadNetworthDataScreenOptions{
											GetNetworthDataFilePayload: "{\"networthDataFilePurpose\":\"NETWORTH_DATA_FILE_PURPOSE_DOWNLOAD\", \"networthDataFileType\":\"NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA\"}",
										},
									),
								},
							),
						},
					},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName: "ClickedDownloadNetWorth",
					},
				},
			},
		},
	)
}
