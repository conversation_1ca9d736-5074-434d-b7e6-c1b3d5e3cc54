// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/insights/networth/interactive_talk_to_ai.proto

package networth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on InteractiveTalkToAiScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InteractiveTalkToAiScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InteractiveTalkToAiScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// InteractiveTalkToAiScreenOptionsMultiError, or nil if none found.
func (m *InteractiveTalkToAiScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *InteractiveTalkToAiScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractiveTalkToAiScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractiveTalkToAiScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractiveTalkToAiScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Entrypoint

	if len(errors) > 0 {
		return InteractiveTalkToAiScreenOptionsMultiError(errors)
	}

	return nil
}

// InteractiveTalkToAiScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// InteractiveTalkToAiScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type InteractiveTalkToAiScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InteractiveTalkToAiScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InteractiveTalkToAiScreenOptionsMultiError) AllErrors() []error { return m }

// InteractiveTalkToAiScreenOptionsValidationError is the validation error
// returned by InteractiveTalkToAiScreenOptions.Validate if the designated
// constraints aren't met.
type InteractiveTalkToAiScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InteractiveTalkToAiScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InteractiveTalkToAiScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InteractiveTalkToAiScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InteractiveTalkToAiScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InteractiveTalkToAiScreenOptionsValidationError) ErrorName() string {
	return "InteractiveTalkToAiScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e InteractiveTalkToAiScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInteractiveTalkToAiScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InteractiveTalkToAiScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InteractiveTalkToAiScreenOptionsValidationError{}
