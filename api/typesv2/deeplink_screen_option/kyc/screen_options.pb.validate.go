// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/kyc/screen_options.proto

package kyc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on KycStatusPollingScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KycStatusPollingScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KycStatusPollingScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// KycStatusPollingScreenOptionsMultiError, or nil if none found.
func (m *KycStatusPollingScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *KycStatusPollingScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycStatusPollingScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycStatusPollingScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycStatusPollingScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	// no validation rules for RetryAttemptNumber

	// no validation rules for RetryDelay

	if all {
		switch v := interface{}(m.GetPollingText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycStatusPollingScreenOptionsValidationError{
					field:  "PollingText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycStatusPollingScreenOptionsValidationError{
					field:  "PollingText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPollingText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycStatusPollingScreenOptionsValidationError{
				field:  "PollingText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KycStatusPollingScreenOptionsMultiError(errors)
	}

	return nil
}

// KycStatusPollingScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by KycStatusPollingScreenOptions.ValidateAll()
// if the designated constraints aren't met.
type KycStatusPollingScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KycStatusPollingScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KycStatusPollingScreenOptionsMultiError) AllErrors() []error { return m }

// KycStatusPollingScreenOptionsValidationError is the validation error
// returned by KycStatusPollingScreenOptions.Validate if the designated
// constraints aren't met.
type KycStatusPollingScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KycStatusPollingScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KycStatusPollingScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KycStatusPollingScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KycStatusPollingScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KycStatusPollingScreenOptionsValidationError) ErrorName() string {
	return "KycStatusPollingScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e KycStatusPollingScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKycStatusPollingScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KycStatusPollingScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KycStatusPollingScreenOptionsValidationError{}
