// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/tiering/screen_options.proto

package tiering

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Default to LIGHT if unspecified
type LoaderScreenOptions_AnimationTheme int32

const (
	LoaderScreenOptions_PILLAR_THEME_UNSPECIFIED LoaderScreenOptions_AnimationTheme = 0
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=11130-8578&t=mXS9zsieZAJj8r2h-4
	LoaderScreenOptions_LIGHT LoaderScreenOptions_AnimationTheme = 1
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=11130-8580&t=mXS9zsieZAJj8r2h-4
	LoaderScreenOptions_DARK LoaderScreenOptions_AnimationTheme = 2
)

// Enum value maps for LoaderScreenOptions_AnimationTheme.
var (
	LoaderScreenOptions_AnimationTheme_name = map[int32]string{
		0: "PILLAR_THEME_UNSPECIFIED",
		1: "LIGHT",
		2: "DARK",
	}
	LoaderScreenOptions_AnimationTheme_value = map[string]int32{
		"PILLAR_THEME_UNSPECIFIED": 0,
		"LIGHT":                    1,
		"DARK":                     2,
	}
)

func (x LoaderScreenOptions_AnimationTheme) Enum() *LoaderScreenOptions_AnimationTheme {
	p := new(LoaderScreenOptions_AnimationTheme)
	*p = x
	return p
}

func (x LoaderScreenOptions_AnimationTheme) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoaderScreenOptions_AnimationTheme) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_enumTypes[0].Descriptor()
}

func (LoaderScreenOptions_AnimationTheme) Type() protoreflect.EnumType {
	return &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_enumTypes[0]
}

func (x LoaderScreenOptions_AnimationTheme) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoaderScreenOptions_AnimationTheme.Descriptor instead.
func (LoaderScreenOptions_AnimationTheme) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescGZIP(), []int{2, 0}
}

// Default to LIGHT if unspecified
type UpgradeSuccessV2ScreenOptions_PillarTheme int32

const (
	UpgradeSuccessV2ScreenOptions_PILLAR_THEME_UNSPECIFIED UpgradeSuccessV2ScreenOptions_PillarTheme = 0
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=11130-8578&t=mXS9zsieZAJj8r2h-4
	UpgradeSuccessV2ScreenOptions_LIGHT UpgradeSuccessV2ScreenOptions_PillarTheme = 1
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=11130-8580&t=mXS9zsieZAJj8r2h-4
	UpgradeSuccessV2ScreenOptions_DARK UpgradeSuccessV2ScreenOptions_PillarTheme = 2
)

// Enum value maps for UpgradeSuccessV2ScreenOptions_PillarTheme.
var (
	UpgradeSuccessV2ScreenOptions_PillarTheme_name = map[int32]string{
		0: "PILLAR_THEME_UNSPECIFIED",
		1: "LIGHT",
		2: "DARK",
	}
	UpgradeSuccessV2ScreenOptions_PillarTheme_value = map[string]int32{
		"PILLAR_THEME_UNSPECIFIED": 0,
		"LIGHT":                    1,
		"DARK":                     2,
	}
)

func (x UpgradeSuccessV2ScreenOptions_PillarTheme) Enum() *UpgradeSuccessV2ScreenOptions_PillarTheme {
	p := new(UpgradeSuccessV2ScreenOptions_PillarTheme)
	*p = x
	return p
}

func (x UpgradeSuccessV2ScreenOptions_PillarTheme) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpgradeSuccessV2ScreenOptions_PillarTheme) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_enumTypes[1].Descriptor()
}

func (UpgradeSuccessV2ScreenOptions_PillarTheme) Type() protoreflect.EnumType {
	return &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_enumTypes[1]
}

func (x UpgradeSuccessV2ScreenOptions_PillarTheme) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpgradeSuccessV2ScreenOptions_PillarTheme.Descriptor instead.
func (UpgradeSuccessV2ScreenOptions_PillarTheme) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescGZIP(), []int{5, 0}
}

type EarnedBenefitScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Background color for header
	HeaderBgColor *widget.BackgroundColour `protobuf:"bytes,2,opt,name=header_bg_color,json=headerBgColor,proto3" json:"header_bg_color,omitempty"`
	// [Icon]
	HeaderVisualElement *common.VisualElement `protobuf:"bytes,3,opt,name=header_visual_element,json=headerVisualElement,proto3" json:"header_visual_element,omitempty"`
	// navigation bar title.
	NavigationBarTitle *common.Text `protobuf:"bytes,4,opt,name=navigation_bar_title,json=navigationBarTitle,proto3" json:"navigation_bar_title,omitempty"`
	// Screen background color
	PageBackgroundColour *widget.BackgroundColour `protobuf:"bytes,5,opt,name=page_background_colour,json=pageBackgroundColour,proto3" json:"page_background_colour,omitempty"`
	// https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-6430&mode=design&t=eiArYdKdW8YeboHu-4
	ShimmerHeaderColor string `protobuf:"bytes,6,opt,name=shimmer_header_color,json=shimmerHeaderColor,proto3" json:"shimmer_header_color,omitempty"`
	// Users current tier for events purpose.
	TierIdentifier string `protobuf:"bytes,8,opt,name=tier_identifier,json=tierIdentifier,proto3" json:"tier_identifier,omitempty"`
}

func (x *EarnedBenefitScreenOptions) Reset() {
	*x = EarnedBenefitScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EarnedBenefitScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EarnedBenefitScreenOptions) ProtoMessage() {}

func (x *EarnedBenefitScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EarnedBenefitScreenOptions.ProtoReflect.Descriptor instead.
func (*EarnedBenefitScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *EarnedBenefitScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *EarnedBenefitScreenOptions) GetHeaderBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.HeaderBgColor
	}
	return nil
}

func (x *EarnedBenefitScreenOptions) GetHeaderVisualElement() *common.VisualElement {
	if x != nil {
		return x.HeaderVisualElement
	}
	return nil
}

func (x *EarnedBenefitScreenOptions) GetNavigationBarTitle() *common.Text {
	if x != nil {
		return x.NavigationBarTitle
	}
	return nil
}

func (x *EarnedBenefitScreenOptions) GetPageBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.PageBackgroundColour
	}
	return nil
}

func (x *EarnedBenefitScreenOptions) GetShimmerHeaderColor() string {
	if x != nil {
		return x.ShimmerHeaderColor
	}
	return ""
}

func (x *EarnedBenefitScreenOptions) GetTierIdentifier() string {
	if x != nil {
		return x.TierIdentifier
	}
	return ""
}

type EarnedBenefitsHistoryScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Screen background color
	PageBackgroundColour *widget.BackgroundColour `protobuf:"bytes,2,opt,name=page_background_colour,json=pageBackgroundColour,proto3" json:"page_background_colour,omitempty"`
	// Users current tier for events purpose.
	TierIdentifier string `protobuf:"bytes,8,opt,name=tier_identifier,json=tierIdentifier,proto3" json:"tier_identifier,omitempty"`
}

func (x *EarnedBenefitsHistoryScreenOptions) Reset() {
	*x = EarnedBenefitsHistoryScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EarnedBenefitsHistoryScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EarnedBenefitsHistoryScreenOptions) ProtoMessage() {}

func (x *EarnedBenefitsHistoryScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EarnedBenefitsHistoryScreenOptions.ProtoReflect.Descriptor instead.
func (*EarnedBenefitsHistoryScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescGZIP(), []int{1}
}

func (x *EarnedBenefitsHistoryScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *EarnedBenefitsHistoryScreenOptions) GetPageBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.PageBackgroundColour
	}
	return nil
}

func (x *EarnedBenefitsHistoryScreenOptions) GetTierIdentifier() string {
	if x != nil {
		return x.TierIdentifier
	}
	return ""
}

// Tier loader screen
// A lottie animation gets displayed when user tries to add fund or add funds to us stocks wallet or create FD/SD
// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10193-125794&m=dev
type LoaderScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Metadata for any purpose like, entry point or tier identifier etc which will be passed in RPC.
	// Keeping it string will help adding any type of data.
	MetaData string `protobuf:"bytes,2,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	// Page background color
	PageBackgroundColor *widget.BackgroundColour `protobuf:"bytes,3,opt,name=page_background_color,json=pageBackgroundColor,proto3" json:"page_background_color,omitempty"`
	// Plan image
	PlanImage *common.VisualElement `protobuf:"bytes,4,opt,name=plan_image,json=planImage,proto3" json:"plan_image,omitempty"`
	// Plan info
	PlanInfo *common.Text `protobuf:"bytes,5,opt,name=plan_info,json=planInfo,proto3" json:"plan_info,omitempty"`
	// After animation completes, how many seconds user should stay in screen before navigating to next screen
	ScreenDelaySec int32 `protobuf:"varint,6,opt,name=screen_delay_sec,json=screenDelaySec,proto3" json:"screen_delay_sec,omitempty"`
	// Deeplink to navigate to post loader complete
	Deeplink *deeplink.Deeplink `protobuf:"bytes,7,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// Theme of animation
	AnimationTheme LoaderScreenOptions_AnimationTheme `protobuf:"varint,8,opt,name=animation_theme,json=animationTheme,proto3,enum=api.typesv2.deeplink_screen_option.tiering.LoaderScreenOptions_AnimationTheme" json:"animation_theme,omitempty"`
}

func (x *LoaderScreenOptions) Reset() {
	*x = LoaderScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoaderScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoaderScreenOptions) ProtoMessage() {}

func (x *LoaderScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoaderScreenOptions.ProtoReflect.Descriptor instead.
func (*LoaderScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescGZIP(), []int{2}
}

func (x *LoaderScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *LoaderScreenOptions) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

func (x *LoaderScreenOptions) GetPageBackgroundColor() *widget.BackgroundColour {
	if x != nil {
		return x.PageBackgroundColor
	}
	return nil
}

func (x *LoaderScreenOptions) GetPlanImage() *common.VisualElement {
	if x != nil {
		return x.PlanImage
	}
	return nil
}

func (x *LoaderScreenOptions) GetPlanInfo() *common.Text {
	if x != nil {
		return x.PlanInfo
	}
	return nil
}

func (x *LoaderScreenOptions) GetScreenDelaySec() int32 {
	if x != nil {
		return x.ScreenDelaySec
	}
	return 0
}

func (x *LoaderScreenOptions) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *LoaderScreenOptions) GetAnimationTheme() LoaderScreenOptions_AnimationTheme {
	if x != nil {
		return x.AnimationTheme
	}
	return LoaderScreenOptions_PILLAR_THEME_UNSPECIFIED
}

// Tier Detailed Benefits Screen options
// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10043-11580&t=t76RUnvyrif5rq8v-4
type BenefitsBottomSheetScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Screen background color
	PageBackgroundColour *widget.BackgroundColour `protobuf:"bytes,2,opt,name=page_background_colour,json=pageBackgroundColour,proto3" json:"page_background_colour,omitempty"`
	// Metadata for any purpose like, entry point or tier identifier etc which will be passed in RPC.
	// Keeping it string will help adding any type of data. will have the DetailedBenefitsScreenMetaData as json string
	MetaData string `protobuf:"bytes,3,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	// Header Bar for Bottom sheet
	HeaderBar *ui.HeaderBar `protobuf:"bytes,4,opt,name=header_bar,json=headerBar,proto3" json:"header_bar,omitempty"`
}

func (x *BenefitsBottomSheetScreenOptions) Reset() {
	*x = BenefitsBottomSheetScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenefitsBottomSheetScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenefitsBottomSheetScreenOptions) ProtoMessage() {}

func (x *BenefitsBottomSheetScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenefitsBottomSheetScreenOptions.ProtoReflect.Descriptor instead.
func (*BenefitsBottomSheetScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescGZIP(), []int{3}
}

func (x *BenefitsBottomSheetScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *BenefitsBottomSheetScreenOptions) GetPageBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.PageBackgroundColour
	}
	return nil
}

func (x *BenefitsBottomSheetScreenOptions) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

func (x *BenefitsBottomSheetScreenOptions) GetHeaderBar() *ui.HeaderBar {
	if x != nil {
		return x.HeaderBar
	}
	return nil
}

// Tier all plans screen v2
// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10002-54939&t=e3eOtSONnhAQGR5B-4
type AllPlansV2ScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Metadata for any purpose like, entry point or tier identifier etc which will be passed in RPC.
	// Keeping it string will help adding any type of data.
	// typesv2/tiering/metadata.go -> TieringScreenMetaData as json string
	MetaData string `protobuf:"bytes,2,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	// Screen title. e.g, Plans
	Title *common.Text `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
}

func (x *AllPlansV2ScreenOptions) Reset() {
	*x = AllPlansV2ScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllPlansV2ScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllPlansV2ScreenOptions) ProtoMessage() {}

func (x *AllPlansV2ScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllPlansV2ScreenOptions.ProtoReflect.Descriptor instead.
func (*AllPlansV2ScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescGZIP(), []int{4}
}

func (x *AllPlansV2ScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AllPlansV2ScreenOptions) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

func (x *AllPlansV2ScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

// Tier upgrade success screen v2
// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10193-125818&m=dev
type UpgradeSuccessV2ScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Page background color
	PageBackgroundColor *widget.BackgroundColour `protobuf:"bytes,2,opt,name=page_background_color,json=pageBackgroundColor,proto3" json:"page_background_color,omitempty"`
	// Gradient background color
	HeaderBackgroundColor *widget.BackgroundColour `protobuf:"bytes,3,opt,name=header_background_color,json=headerBackgroundColor,proto3" json:"header_background_color,omitempty"`
	// Header overlay background color
	HeaderOverlayBackgroundColor *widget.BackgroundColour `protobuf:"bytes,4,opt,name=header_overlay_background_color,json=headerOverlayBackgroundColor,proto3" json:"header_overlay_background_color,omitempty"`
	// Title
	Title *common.Text `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	// Subtitle
	Subtitle *common.Text `protobuf:"bytes,6,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// Plan image
	PlanImage *common.VisualElement `protobuf:"bytes,7,opt,name=plan_image,json=planImage,proto3" json:"plan_image,omitempty"`
	// Pillar image
	PillarImage *common.VisualElement `protobuf:"bytes,8,opt,name=pillar_image,json=pillarImage,proto3" json:"pillar_image,omitempty"`
	// Pillar overlay gradient
	PillarOverlayGradient *widget.BackgroundColour `protobuf:"bytes,9,opt,name=pillar_overlay_gradient,json=pillarOverlayGradient,proto3" json:"pillar_overlay_gradient,omitempty"`
	// Theme of pillar image
	PillarTheme UpgradeSuccessV2ScreenOptions_PillarTheme `protobuf:"varint,10,opt,name=pillar_theme,json=pillarTheme,proto3,enum=api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions_PillarTheme" json:"pillar_theme,omitempty"`
	// The gap is in -ve for pillar image and plan image to render properly
	// Since for different plans the image can be different, we are getting from the backend
	PillarPlanImageGap int32 `protobuf:"varint,11,opt,name=pillar_plan_image_gap,json=pillarPlanImageGap,proto3" json:"pillar_plan_image_gap,omitempty"`
	// Cta shown at the bottom of the screen
	Cta *deeplink.Cta `protobuf:"bytes,12,opt,name=cta,proto3" json:"cta,omitempty"`
	// Metadata for any purpose like, entry point or tier identifier etc which will be passed in events.
	MetaData string `protobuf:"bytes,13,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
}

func (x *UpgradeSuccessV2ScreenOptions) Reset() {
	*x = UpgradeSuccessV2ScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeSuccessV2ScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeSuccessV2ScreenOptions) ProtoMessage() {}

func (x *UpgradeSuccessV2ScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeSuccessV2ScreenOptions.ProtoReflect.Descriptor instead.
func (*UpgradeSuccessV2ScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescGZIP(), []int{5}
}

func (x *UpgradeSuccessV2ScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpgradeSuccessV2ScreenOptions) GetPageBackgroundColor() *widget.BackgroundColour {
	if x != nil {
		return x.PageBackgroundColor
	}
	return nil
}

func (x *UpgradeSuccessV2ScreenOptions) GetHeaderBackgroundColor() *widget.BackgroundColour {
	if x != nil {
		return x.HeaderBackgroundColor
	}
	return nil
}

func (x *UpgradeSuccessV2ScreenOptions) GetHeaderOverlayBackgroundColor() *widget.BackgroundColour {
	if x != nil {
		return x.HeaderOverlayBackgroundColor
	}
	return nil
}

func (x *UpgradeSuccessV2ScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *UpgradeSuccessV2ScreenOptions) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *UpgradeSuccessV2ScreenOptions) GetPlanImage() *common.VisualElement {
	if x != nil {
		return x.PlanImage
	}
	return nil
}

func (x *UpgradeSuccessV2ScreenOptions) GetPillarImage() *common.VisualElement {
	if x != nil {
		return x.PillarImage
	}
	return nil
}

func (x *UpgradeSuccessV2ScreenOptions) GetPillarOverlayGradient() *widget.BackgroundColour {
	if x != nil {
		return x.PillarOverlayGradient
	}
	return nil
}

func (x *UpgradeSuccessV2ScreenOptions) GetPillarTheme() UpgradeSuccessV2ScreenOptions_PillarTheme {
	if x != nil {
		return x.PillarTheme
	}
	return UpgradeSuccessV2ScreenOptions_PILLAR_THEME_UNSPECIFIED
}

func (x *UpgradeSuccessV2ScreenOptions) GetPillarPlanImageGap() int32 {
	if x != nil {
		return x.PillarPlanImageGap
	}
	return 0
}

func (x *UpgradeSuccessV2ScreenOptions) GetCta() *deeplink.Cta {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *UpgradeSuccessV2ScreenOptions) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

// Drop off bottom sheet screen
// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10277-72545&t=mXS9zsieZAJj8r2h-4
type DropOffBottomSheetScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Background color of the page
	PageBackgroundColor *widget.BackgroundColour `protobuf:"bytes,2,opt,name=page_background_color,json=pageBackgroundColor,proto3" json:"page_background_color,omitempty"`
	// Limited time offer
	Offer *ui.IconTextComponent `protobuf:"bytes,3,opt,name=offer,proto3" json:"offer,omitempty"`
	Title *common.Text          `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// Alternate way to enter tier
	Components []*DropOffOptionComponent `protobuf:"bytes,5,rep,name=components,proto3" json:"components,omitempty"`
	// Explore cta
	ExploreCta *deeplink.Cta `protobuf:"bytes,6,opt,name=explore_cta,json=exploreCta,proto3" json:"explore_cta,omitempty"`
	// Go back cta
	GoBackCta *deeplink.Cta `protobuf:"bytes,7,opt,name=go_back_cta,json=goBackCta,proto3" json:"go_back_cta,omitempty"`
	// Metadata for any purpose like, entry point or tier identifier etc which will be passed in events.
	MetaData string `protobuf:"bytes,8,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
}

func (x *DropOffBottomSheetScreenOptions) Reset() {
	*x = DropOffBottomSheetScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DropOffBottomSheetScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DropOffBottomSheetScreenOptions) ProtoMessage() {}

func (x *DropOffBottomSheetScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DropOffBottomSheetScreenOptions.ProtoReflect.Descriptor instead.
func (*DropOffBottomSheetScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescGZIP(), []int{6}
}

func (x *DropOffBottomSheetScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *DropOffBottomSheetScreenOptions) GetPageBackgroundColor() *widget.BackgroundColour {
	if x != nil {
		return x.PageBackgroundColor
	}
	return nil
}

func (x *DropOffBottomSheetScreenOptions) GetOffer() *ui.IconTextComponent {
	if x != nil {
		return x.Offer
	}
	return nil
}

func (x *DropOffBottomSheetScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *DropOffBottomSheetScreenOptions) GetComponents() []*DropOffOptionComponent {
	if x != nil {
		return x.Components
	}
	return nil
}

func (x *DropOffBottomSheetScreenOptions) GetExploreCta() *deeplink.Cta {
	if x != nil {
		return x.ExploreCta
	}
	return nil
}

func (x *DropOffBottomSheetScreenOptions) GetGoBackCta() *deeplink.Cta {
	if x != nil {
		return x.GoBackCta
	}
	return nil
}

func (x *DropOffBottomSheetScreenOptions) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

type TieringLandingScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// json string to propagate the context.
	// will have the typesv2/tiering -> TieringScreenMetaData as json string
	MetaData string `protobuf:"bytes,2,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
}

func (x *TieringLandingScreenOptions) Reset() {
	*x = TieringLandingScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TieringLandingScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TieringLandingScreenOptions) ProtoMessage() {}

func (x *TieringLandingScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TieringLandingScreenOptions.ProtoReflect.Descriptor instead.
func (*TieringLandingScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescGZIP(), []int{7}
}

func (x *TieringLandingScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *TieringLandingScreenOptions) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

type DropOffFullScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Background color of the page
	PageBackgroundColor *widget.BackgroundColour `protobuf:"bytes,2,opt,name=page_background_color,json=pageBackgroundColor,proto3" json:"page_background_color,omitempty"`
	// Header bar
	HeaderBar *ui.HeaderBar `protobuf:"bytes,3,opt,name=header_bar,json=headerBar,proto3" json:"header_bar,omitempty"`
	// eg: Offer expires soon
	Banner      *ui.IconTextComponent     `protobuf:"bytes,4,opt,name=banner,proto3" json:"banner,omitempty"`
	CenterImage *common.VisualElement     `protobuf:"bytes,5,opt,name=center_image,json=centerImage,proto3" json:"center_image,omitempty"`
	Title       *common.Text              `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	Benefits    []*DropOffOptionComponent `protobuf:"bytes,7,rep,name=benefits,proto3" json:"benefits,omitempty"`
	// CTA (eg: Explore Prime)
	Cta *deeplink.Cta `protobuf:"bytes,8,opt,name=cta,proto3" json:"cta,omitempty"`
	// Metadata for any purpose like, entry point or tier identifier etc which will be passed in events.
	MetaData string `protobuf:"bytes,9,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
}

func (x *DropOffFullScreenOptions) Reset() {
	*x = DropOffFullScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DropOffFullScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DropOffFullScreenOptions) ProtoMessage() {}

func (x *DropOffFullScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DropOffFullScreenOptions.ProtoReflect.Descriptor instead.
func (*DropOffFullScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescGZIP(), []int{8}
}

func (x *DropOffFullScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *DropOffFullScreenOptions) GetPageBackgroundColor() *widget.BackgroundColour {
	if x != nil {
		return x.PageBackgroundColor
	}
	return nil
}

func (x *DropOffFullScreenOptions) GetHeaderBar() *ui.HeaderBar {
	if x != nil {
		return x.HeaderBar
	}
	return nil
}

func (x *DropOffFullScreenOptions) GetBanner() *ui.IconTextComponent {
	if x != nil {
		return x.Banner
	}
	return nil
}

func (x *DropOffFullScreenOptions) GetCenterImage() *common.VisualElement {
	if x != nil {
		return x.CenterImage
	}
	return nil
}

func (x *DropOffFullScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *DropOffFullScreenOptions) GetBenefits() []*DropOffOptionComponent {
	if x != nil {
		return x.Benefits
	}
	return nil
}

func (x *DropOffFullScreenOptions) GetCta() *deeplink.Cta {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *DropOffFullScreenOptions) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

var File_api_typesv2_deeplink_screen_option_tiering_screen_options_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x2a, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x1a, 0x24, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x5f, 0x62, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f,
	0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa8, 0x04, 0x0a, 0x1a, 0x45, 0x61, 0x72, 0x6e, 0x65,
	0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x56, 0x0a, 0x0f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f,
	0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0d,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x55, 0x0a,
	0x15, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x13, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x14, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x62, 0x61, 0x72, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x12, 0x6e, 0x61,
	0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x72, 0x54, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x64, 0x0a, 0x16, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e,
	0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x52, 0x14, 0x70, 0x61, 0x67, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x68, 0x69, 0x6d, 0x6d, 0x65,
	0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x68, 0x69, 0x6d, 0x6d, 0x65, 0x72, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x69, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x74, 0x69, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x22, 0x83, 0x02, 0x0a, 0x22, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x64, 0x0a, 0x16, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69,
	0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x14, 0x70, 0x61, 0x67, 0x65, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x27,
	0x0a, 0x0f, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x69, 0x65, 0x72, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x80, 0x05, 0x0a, 0x13, 0x4c, 0x6f, 0x61, 0x64,
	0x65, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x62, 0x0a, 0x15,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x13, 0x70, 0x61, 0x67,
	0x65, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x40, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x65, 0x6c, 0x61, 0x79,
	0x53, 0x65, 0x63, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x77, 0x0a, 0x0f,
	0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x68, 0x65, 0x6d, 0x65, 0x52, 0x0e, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x68, 0x65, 0x6d, 0x65, 0x22, 0x43, 0x0a, 0x0e, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x49, 0x4c, 0x4c, 0x41,
	0x52, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x10, 0x01,
	0x12, 0x08, 0x0a, 0x04, 0x44, 0x41, 0x52, 0x4b, 0x10, 0x02, 0x22, 0xac, 0x02, 0x0a, 0x20, 0x42,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65,
	0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x64, 0x0a, 0x16, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42,
	0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52,
	0x14, 0x70, 0x61, 0x67, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x35, 0x0a, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x52, 0x09,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x22, 0xb6, 0x01, 0x0a, 0x17, 0x41, 0x6c,
	0x6c, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x56, 0x32, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x22, 0xbe, 0x08, 0x0a, 0x1d, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x56, 0x32, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x62, 0x0a, 0x15, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c,
	0x6f, 0x75, 0x72, 0x52, 0x13, 0x70, 0x61, 0x67, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x66, 0x0a, 0x17, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75,
	0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x15, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x75, 0x0a, 0x1f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x6c,
	0x61, 0x79, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75,
	0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x1c, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x4f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x79, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x40, 0x0a,
	0x0a, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x44, 0x0a, 0x0c, 0x70, 0x69, 0x6c, 0x6c, 0x61, 0x72, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x70, 0x69, 0x6c, 0x6c, 0x61, 0x72,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x66, 0x0a, 0x17, 0x70, 0x69, 0x6c, 0x6c, 0x61, 0x72, 0x5f,
	0x6f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x79, 0x5f, 0x67, 0x72, 0x61, 0x64, 0x69, 0x65, 0x6e, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x15, 0x70, 0x69, 0x6c, 0x6c, 0x61, 0x72, 0x4f, 0x76,
	0x65, 0x72, 0x6c, 0x61, 0x79, 0x47, 0x72, 0x61, 0x64, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x78, 0x0a,
	0x0c, 0x70, 0x69, 0x6c, 0x6c, 0x61, 0x72, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x55, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x56,
	0x32, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x50,
	0x69, 0x6c, 0x6c, 0x61, 0x72, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x52, 0x0b, 0x70, 0x69, 0x6c, 0x6c,
	0x61, 0x72, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x70, 0x69, 0x6c, 0x6c, 0x61,
	0x72, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x67, 0x61, 0x70,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x70, 0x69, 0x6c, 0x6c, 0x61, 0x72, 0x50, 0x6c,
	0x61, 0x6e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x47, 0x61, 0x70, 0x12, 0x28, 0x0a, 0x03, 0x63, 0x74,
	0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52,
	0x03, 0x63, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x22, 0x40, 0x0a, 0x0b, 0x50, 0x69, 0x6c, 0x6c, 0x61, 0x72, 0x54, 0x68, 0x65, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x18, 0x50, 0x49, 0x4c, 0x4c, 0x41, 0x52, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09,
	0x0a, 0x05, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x41, 0x52,
	0x4b, 0x10, 0x02, 0x22, 0xb0, 0x04, 0x0a, 0x1f, 0x44, 0x72, 0x6f, 0x70, 0x4f, 0x66, 0x66, 0x42,
	0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x62, 0x0a, 0x15, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x13, 0x70, 0x61, 0x67, 0x65, 0x42, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x37, 0x0a, 0x05, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x62, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x72, 0x6f, 0x70, 0x4f, 0x66, 0x66, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6c,
	0x6f, 0x72, 0x65, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x43, 0x74,
	0x61, 0x12, 0x36, 0x0a, 0x0b, 0x67, 0x6f, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x63, 0x74, 0x61,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x09,
	0x67, 0x6f, 0x42, 0x61, 0x63, 0x6b, 0x43, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74,
	0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x22, 0x8a, 0x01, 0x0a, 0x1b, 0x54, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44,
	0x61, 0x74, 0x61, 0x22, 0xdd, 0x04, 0x0a, 0x18, 0x44, 0x72, 0x6f, 0x70, 0x4f, 0x66, 0x66, 0x46,
	0x75, 0x6c, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x62, 0x0a, 0x15, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42,
	0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52,
	0x13, 0x70, 0x61, 0x67, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x35, 0x0a, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x62,
	0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72,
	0x52, 0x09, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x12, 0x39, 0x0a, 0x06, 0x62,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x06,
	0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x0c, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x5e, 0x0a, 0x08,
	0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x72, 0x6f, 0x70,
	0x4f, 0x66, 0x66, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x08, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x28, 0x0a, 0x03,
	0x63, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74,
	0x61, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44,
	0x61, 0x74, 0x61, 0x42, 0x88, 0x01, 0x0a, 0x41, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x01, 0x5a, 0x41, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescData = file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_goTypes = []interface{}{
	(LoaderScreenOptions_AnimationTheme)(0),           // 0: api.typesv2.deeplink_screen_option.tiering.LoaderScreenOptions.AnimationTheme
	(UpgradeSuccessV2ScreenOptions_PillarTheme)(0),    // 1: api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions.PillarTheme
	(*EarnedBenefitScreenOptions)(nil),                // 2: api.typesv2.deeplink_screen_option.tiering.EarnedBenefitScreenOptions
	(*EarnedBenefitsHistoryScreenOptions)(nil),        // 3: api.typesv2.deeplink_screen_option.tiering.EarnedBenefitsHistoryScreenOptions
	(*LoaderScreenOptions)(nil),                       // 4: api.typesv2.deeplink_screen_option.tiering.LoaderScreenOptions
	(*BenefitsBottomSheetScreenOptions)(nil),          // 5: api.typesv2.deeplink_screen_option.tiering.BenefitsBottomSheetScreenOptions
	(*AllPlansV2ScreenOptions)(nil),                   // 6: api.typesv2.deeplink_screen_option.tiering.AllPlansV2ScreenOptions
	(*UpgradeSuccessV2ScreenOptions)(nil),             // 7: api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions
	(*DropOffBottomSheetScreenOptions)(nil),           // 8: api.typesv2.deeplink_screen_option.tiering.DropOffBottomSheetScreenOptions
	(*TieringLandingScreenOptions)(nil),               // 9: api.typesv2.deeplink_screen_option.tiering.TieringLandingScreenOptions
	(*DropOffFullScreenOptions)(nil),                  // 10: api.typesv2.deeplink_screen_option.tiering.DropOffFullScreenOptions
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 11: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*widget.BackgroundColour)(nil),                   // 12: api.typesv2.common.ui.widget.BackgroundColour
	(*common.VisualElement)(nil),                      // 13: api.typesv2.common.VisualElement
	(*common.Text)(nil),                               // 14: api.typesv2.common.Text
	(*deeplink.Deeplink)(nil),                         // 15: frontend.deeplink.Deeplink
	(*ui.HeaderBar)(nil),                              // 16: api.typesv2.HeaderBar
	(*deeplink.Cta)(nil),                              // 17: frontend.deeplink.Cta
	(*ui.IconTextComponent)(nil),                      // 18: api.typesv2.ui.IconTextComponent
	(*DropOffOptionComponent)(nil),                    // 19: api.typesv2.deeplink_screen_option.tiering.DropOffOptionComponent
}
var file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_depIdxs = []int32{
	11, // 0: api.typesv2.deeplink_screen_option.tiering.EarnedBenefitScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	12, // 1: api.typesv2.deeplink_screen_option.tiering.EarnedBenefitScreenOptions.header_bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	13, // 2: api.typesv2.deeplink_screen_option.tiering.EarnedBenefitScreenOptions.header_visual_element:type_name -> api.typesv2.common.VisualElement
	14, // 3: api.typesv2.deeplink_screen_option.tiering.EarnedBenefitScreenOptions.navigation_bar_title:type_name -> api.typesv2.common.Text
	12, // 4: api.typesv2.deeplink_screen_option.tiering.EarnedBenefitScreenOptions.page_background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	11, // 5: api.typesv2.deeplink_screen_option.tiering.EarnedBenefitsHistoryScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	12, // 6: api.typesv2.deeplink_screen_option.tiering.EarnedBenefitsHistoryScreenOptions.page_background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	11, // 7: api.typesv2.deeplink_screen_option.tiering.LoaderScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	12, // 8: api.typesv2.deeplink_screen_option.tiering.LoaderScreenOptions.page_background_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	13, // 9: api.typesv2.deeplink_screen_option.tiering.LoaderScreenOptions.plan_image:type_name -> api.typesv2.common.VisualElement
	14, // 10: api.typesv2.deeplink_screen_option.tiering.LoaderScreenOptions.plan_info:type_name -> api.typesv2.common.Text
	15, // 11: api.typesv2.deeplink_screen_option.tiering.LoaderScreenOptions.deeplink:type_name -> frontend.deeplink.Deeplink
	0,  // 12: api.typesv2.deeplink_screen_option.tiering.LoaderScreenOptions.animation_theme:type_name -> api.typesv2.deeplink_screen_option.tiering.LoaderScreenOptions.AnimationTheme
	11, // 13: api.typesv2.deeplink_screen_option.tiering.BenefitsBottomSheetScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	12, // 14: api.typesv2.deeplink_screen_option.tiering.BenefitsBottomSheetScreenOptions.page_background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	16, // 15: api.typesv2.deeplink_screen_option.tiering.BenefitsBottomSheetScreenOptions.header_bar:type_name -> api.typesv2.HeaderBar
	11, // 16: api.typesv2.deeplink_screen_option.tiering.AllPlansV2ScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	14, // 17: api.typesv2.deeplink_screen_option.tiering.AllPlansV2ScreenOptions.title:type_name -> api.typesv2.common.Text
	11, // 18: api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	12, // 19: api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions.page_background_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	12, // 20: api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions.header_background_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	12, // 21: api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions.header_overlay_background_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	14, // 22: api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions.title:type_name -> api.typesv2.common.Text
	14, // 23: api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions.subtitle:type_name -> api.typesv2.common.Text
	13, // 24: api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions.plan_image:type_name -> api.typesv2.common.VisualElement
	13, // 25: api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions.pillar_image:type_name -> api.typesv2.common.VisualElement
	12, // 26: api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions.pillar_overlay_gradient:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	1,  // 27: api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions.pillar_theme:type_name -> api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions.PillarTheme
	17, // 28: api.typesv2.deeplink_screen_option.tiering.UpgradeSuccessV2ScreenOptions.cta:type_name -> frontend.deeplink.Cta
	11, // 29: api.typesv2.deeplink_screen_option.tiering.DropOffBottomSheetScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	12, // 30: api.typesv2.deeplink_screen_option.tiering.DropOffBottomSheetScreenOptions.page_background_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	18, // 31: api.typesv2.deeplink_screen_option.tiering.DropOffBottomSheetScreenOptions.offer:type_name -> api.typesv2.ui.IconTextComponent
	14, // 32: api.typesv2.deeplink_screen_option.tiering.DropOffBottomSheetScreenOptions.title:type_name -> api.typesv2.common.Text
	19, // 33: api.typesv2.deeplink_screen_option.tiering.DropOffBottomSheetScreenOptions.components:type_name -> api.typesv2.deeplink_screen_option.tiering.DropOffOptionComponent
	17, // 34: api.typesv2.deeplink_screen_option.tiering.DropOffBottomSheetScreenOptions.explore_cta:type_name -> frontend.deeplink.Cta
	17, // 35: api.typesv2.deeplink_screen_option.tiering.DropOffBottomSheetScreenOptions.go_back_cta:type_name -> frontend.deeplink.Cta
	11, // 36: api.typesv2.deeplink_screen_option.tiering.TieringLandingScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	11, // 37: api.typesv2.deeplink_screen_option.tiering.DropOffFullScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	12, // 38: api.typesv2.deeplink_screen_option.tiering.DropOffFullScreenOptions.page_background_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	16, // 39: api.typesv2.deeplink_screen_option.tiering.DropOffFullScreenOptions.header_bar:type_name -> api.typesv2.HeaderBar
	18, // 40: api.typesv2.deeplink_screen_option.tiering.DropOffFullScreenOptions.banner:type_name -> api.typesv2.ui.IconTextComponent
	13, // 41: api.typesv2.deeplink_screen_option.tiering.DropOffFullScreenOptions.center_image:type_name -> api.typesv2.common.VisualElement
	14, // 42: api.typesv2.deeplink_screen_option.tiering.DropOffFullScreenOptions.title:type_name -> api.typesv2.common.Text
	19, // 43: api.typesv2.deeplink_screen_option.tiering.DropOffFullScreenOptions.benefits:type_name -> api.typesv2.deeplink_screen_option.tiering.DropOffOptionComponent
	17, // 44: api.typesv2.deeplink_screen_option.tiering.DropOffFullScreenOptions.cta:type_name -> frontend.deeplink.Cta
	45, // [45:45] is the sub-list for method output_type
	45, // [45:45] is the sub-list for method input_type
	45, // [45:45] is the sub-list for extension type_name
	45, // [45:45] is the sub-list for extension extendee
	0,  // [0:45] is the sub-list for field type_name
}

func init() { file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_init() }
func file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_init() {
	if File_api_typesv2_deeplink_screen_option_tiering_screen_options_proto != nil {
		return
	}
	file_api_typesv2_deeplink_screen_option_tiering_components_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EarnedBenefitScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EarnedBenefitsHistoryScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoaderScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenefitsBottomSheetScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllPlansV2ScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeSuccessV2ScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DropOffBottomSheetScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TieringLandingScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DropOffFullScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_depIdxs,
		EnumInfos:         file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_enumTypes,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_tiering_screen_options_proto = out.File
	file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_tiering_screen_options_proto_depIdxs = nil
}
