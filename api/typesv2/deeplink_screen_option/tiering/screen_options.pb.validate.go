// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/tiering/screen_options.proto

package tiering

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EarnedBenefitScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EarnedBenefitScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EarnedBenefitScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EarnedBenefitScreenOptionsMultiError, or nil if none found.
func (m *EarnedBenefitScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *EarnedBenefitScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EarnedBenefitScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EarnedBenefitScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EarnedBenefitScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EarnedBenefitScreenOptionsValidationError{
					field:  "HeaderBgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EarnedBenefitScreenOptionsValidationError{
					field:  "HeaderBgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EarnedBenefitScreenOptionsValidationError{
				field:  "HeaderBgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EarnedBenefitScreenOptionsValidationError{
					field:  "HeaderVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EarnedBenefitScreenOptionsValidationError{
					field:  "HeaderVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EarnedBenefitScreenOptionsValidationError{
				field:  "HeaderVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNavigationBarTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EarnedBenefitScreenOptionsValidationError{
					field:  "NavigationBarTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EarnedBenefitScreenOptionsValidationError{
					field:  "NavigationBarTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNavigationBarTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EarnedBenefitScreenOptionsValidationError{
				field:  "NavigationBarTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EarnedBenefitScreenOptionsValidationError{
					field:  "PageBackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EarnedBenefitScreenOptionsValidationError{
					field:  "PageBackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EarnedBenefitScreenOptionsValidationError{
				field:  "PageBackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShimmerHeaderColor

	// no validation rules for TierIdentifier

	if len(errors) > 0 {
		return EarnedBenefitScreenOptionsMultiError(errors)
	}

	return nil
}

// EarnedBenefitScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by EarnedBenefitScreenOptions.ValidateAll() if
// the designated constraints aren't met.
type EarnedBenefitScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EarnedBenefitScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EarnedBenefitScreenOptionsMultiError) AllErrors() []error { return m }

// EarnedBenefitScreenOptionsValidationError is the validation error returned
// by EarnedBenefitScreenOptions.Validate if the designated constraints aren't met.
type EarnedBenefitScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EarnedBenefitScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EarnedBenefitScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EarnedBenefitScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EarnedBenefitScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EarnedBenefitScreenOptionsValidationError) ErrorName() string {
	return "EarnedBenefitScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e EarnedBenefitScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEarnedBenefitScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EarnedBenefitScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EarnedBenefitScreenOptionsValidationError{}

// Validate checks the field values on EarnedBenefitsHistoryScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *EarnedBenefitsHistoryScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EarnedBenefitsHistoryScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// EarnedBenefitsHistoryScreenOptionsMultiError, or nil if none found.
func (m *EarnedBenefitsHistoryScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *EarnedBenefitsHistoryScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EarnedBenefitsHistoryScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EarnedBenefitsHistoryScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EarnedBenefitsHistoryScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EarnedBenefitsHistoryScreenOptionsValidationError{
					field:  "PageBackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EarnedBenefitsHistoryScreenOptionsValidationError{
					field:  "PageBackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EarnedBenefitsHistoryScreenOptionsValidationError{
				field:  "PageBackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TierIdentifier

	if len(errors) > 0 {
		return EarnedBenefitsHistoryScreenOptionsMultiError(errors)
	}

	return nil
}

// EarnedBenefitsHistoryScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// EarnedBenefitsHistoryScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type EarnedBenefitsHistoryScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EarnedBenefitsHistoryScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EarnedBenefitsHistoryScreenOptionsMultiError) AllErrors() []error { return m }

// EarnedBenefitsHistoryScreenOptionsValidationError is the validation error
// returned by EarnedBenefitsHistoryScreenOptions.Validate if the designated
// constraints aren't met.
type EarnedBenefitsHistoryScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EarnedBenefitsHistoryScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EarnedBenefitsHistoryScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EarnedBenefitsHistoryScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EarnedBenefitsHistoryScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EarnedBenefitsHistoryScreenOptionsValidationError) ErrorName() string {
	return "EarnedBenefitsHistoryScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e EarnedBenefitsHistoryScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEarnedBenefitsHistoryScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EarnedBenefitsHistoryScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EarnedBenefitsHistoryScreenOptionsValidationError{}

// Validate checks the field values on LoaderScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoaderScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoaderScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoaderScreenOptionsMultiError, or nil if none found.
func (m *LoaderScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *LoaderScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoaderScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoaderScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoaderScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MetaData

	if all {
		switch v := interface{}(m.GetPageBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoaderScreenOptionsValidationError{
					field:  "PageBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoaderScreenOptionsValidationError{
					field:  "PageBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoaderScreenOptionsValidationError{
				field:  "PageBackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPlanImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoaderScreenOptionsValidationError{
					field:  "PlanImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoaderScreenOptionsValidationError{
					field:  "PlanImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlanImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoaderScreenOptionsValidationError{
				field:  "PlanImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPlanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoaderScreenOptionsValidationError{
					field:  "PlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoaderScreenOptionsValidationError{
					field:  "PlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoaderScreenOptionsValidationError{
				field:  "PlanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ScreenDelaySec

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoaderScreenOptionsValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoaderScreenOptionsValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoaderScreenOptionsValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AnimationTheme

	if len(errors) > 0 {
		return LoaderScreenOptionsMultiError(errors)
	}

	return nil
}

// LoaderScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by LoaderScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type LoaderScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoaderScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoaderScreenOptionsMultiError) AllErrors() []error { return m }

// LoaderScreenOptionsValidationError is the validation error returned by
// LoaderScreenOptions.Validate if the designated constraints aren't met.
type LoaderScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoaderScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoaderScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoaderScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoaderScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoaderScreenOptionsValidationError) ErrorName() string {
	return "LoaderScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e LoaderScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoaderScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoaderScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoaderScreenOptionsValidationError{}

// Validate checks the field values on BenefitsBottomSheetScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BenefitsBottomSheetScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BenefitsBottomSheetScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BenefitsBottomSheetScreenOptionsMultiError, or nil if none found.
func (m *BenefitsBottomSheetScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *BenefitsBottomSheetScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BenefitsBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BenefitsBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BenefitsBottomSheetScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BenefitsBottomSheetScreenOptionsValidationError{
					field:  "PageBackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BenefitsBottomSheetScreenOptionsValidationError{
					field:  "PageBackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BenefitsBottomSheetScreenOptionsValidationError{
				field:  "PageBackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MetaData

	if all {
		switch v := interface{}(m.GetHeaderBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BenefitsBottomSheetScreenOptionsValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BenefitsBottomSheetScreenOptionsValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BenefitsBottomSheetScreenOptionsValidationError{
				field:  "HeaderBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BenefitsBottomSheetScreenOptionsMultiError(errors)
	}

	return nil
}

// BenefitsBottomSheetScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// BenefitsBottomSheetScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type BenefitsBottomSheetScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BenefitsBottomSheetScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BenefitsBottomSheetScreenOptionsMultiError) AllErrors() []error { return m }

// BenefitsBottomSheetScreenOptionsValidationError is the validation error
// returned by BenefitsBottomSheetScreenOptions.Validate if the designated
// constraints aren't met.
type BenefitsBottomSheetScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BenefitsBottomSheetScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BenefitsBottomSheetScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BenefitsBottomSheetScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BenefitsBottomSheetScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BenefitsBottomSheetScreenOptionsValidationError) ErrorName() string {
	return "BenefitsBottomSheetScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e BenefitsBottomSheetScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBenefitsBottomSheetScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BenefitsBottomSheetScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BenefitsBottomSheetScreenOptionsValidationError{}

// Validate checks the field values on AllPlansV2ScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AllPlansV2ScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AllPlansV2ScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AllPlansV2ScreenOptionsMultiError, or nil if none found.
func (m *AllPlansV2ScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *AllPlansV2ScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AllPlansV2ScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AllPlansV2ScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AllPlansV2ScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MetaData

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AllPlansV2ScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AllPlansV2ScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AllPlansV2ScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AllPlansV2ScreenOptionsMultiError(errors)
	}

	return nil
}

// AllPlansV2ScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by AllPlansV2ScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type AllPlansV2ScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AllPlansV2ScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AllPlansV2ScreenOptionsMultiError) AllErrors() []error { return m }

// AllPlansV2ScreenOptionsValidationError is the validation error returned by
// AllPlansV2ScreenOptions.Validate if the designated constraints aren't met.
type AllPlansV2ScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AllPlansV2ScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AllPlansV2ScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AllPlansV2ScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AllPlansV2ScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AllPlansV2ScreenOptionsValidationError) ErrorName() string {
	return "AllPlansV2ScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e AllPlansV2ScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAllPlansV2ScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AllPlansV2ScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AllPlansV2ScreenOptionsValidationError{}

// Validate checks the field values on UpgradeSuccessV2ScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpgradeSuccessV2ScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpgradeSuccessV2ScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpgradeSuccessV2ScreenOptionsMultiError, or nil if none found.
func (m *UpgradeSuccessV2ScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *UpgradeSuccessV2ScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeSuccessV2ScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "PageBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "PageBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeSuccessV2ScreenOptionsValidationError{
				field:  "PageBackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "HeaderBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "HeaderBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeSuccessV2ScreenOptionsValidationError{
				field:  "HeaderBackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderOverlayBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "HeaderOverlayBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "HeaderOverlayBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderOverlayBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeSuccessV2ScreenOptionsValidationError{
				field:  "HeaderOverlayBackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeSuccessV2ScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeSuccessV2ScreenOptionsValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPlanImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "PlanImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "PlanImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlanImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeSuccessV2ScreenOptionsValidationError{
				field:  "PlanImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPillarImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "PillarImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "PillarImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPillarImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeSuccessV2ScreenOptionsValidationError{
				field:  "PillarImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPillarOverlayGradient()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "PillarOverlayGradient",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "PillarOverlayGradient",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPillarOverlayGradient()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeSuccessV2ScreenOptionsValidationError{
				field:  "PillarOverlayGradient",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PillarTheme

	// no validation rules for PillarPlanImageGap

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeSuccessV2ScreenOptionsValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeSuccessV2ScreenOptionsValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MetaData

	if len(errors) > 0 {
		return UpgradeSuccessV2ScreenOptionsMultiError(errors)
	}

	return nil
}

// UpgradeSuccessV2ScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by UpgradeSuccessV2ScreenOptions.ValidateAll()
// if the designated constraints aren't met.
type UpgradeSuccessV2ScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpgradeSuccessV2ScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpgradeSuccessV2ScreenOptionsMultiError) AllErrors() []error { return m }

// UpgradeSuccessV2ScreenOptionsValidationError is the validation error
// returned by UpgradeSuccessV2ScreenOptions.Validate if the designated
// constraints aren't met.
type UpgradeSuccessV2ScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpgradeSuccessV2ScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpgradeSuccessV2ScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpgradeSuccessV2ScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpgradeSuccessV2ScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpgradeSuccessV2ScreenOptionsValidationError) ErrorName() string {
	return "UpgradeSuccessV2ScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e UpgradeSuccessV2ScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpgradeSuccessV2ScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpgradeSuccessV2ScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpgradeSuccessV2ScreenOptionsValidationError{}

// Validate checks the field values on DropOffBottomSheetScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DropOffBottomSheetScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DropOffBottomSheetScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DropOffBottomSheetScreenOptionsMultiError, or nil if none found.
func (m *DropOffBottomSheetScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *DropOffBottomSheetScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DropOffBottomSheetScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
					field:  "PageBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
					field:  "PageBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DropOffBottomSheetScreenOptionsValidationError{
				field:  "PageBackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOffer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
					field:  "Offer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
					field:  "Offer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOffer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DropOffBottomSheetScreenOptionsValidationError{
				field:  "Offer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DropOffBottomSheetScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetComponents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("Components[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("Components[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DropOffBottomSheetScreenOptionsValidationError{
					field:  fmt.Sprintf("Components[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetExploreCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
					field:  "ExploreCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
					field:  "ExploreCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExploreCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DropOffBottomSheetScreenOptionsValidationError{
				field:  "ExploreCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGoBackCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
					field:  "GoBackCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DropOffBottomSheetScreenOptionsValidationError{
					field:  "GoBackCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoBackCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DropOffBottomSheetScreenOptionsValidationError{
				field:  "GoBackCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MetaData

	if len(errors) > 0 {
		return DropOffBottomSheetScreenOptionsMultiError(errors)
	}

	return nil
}

// DropOffBottomSheetScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by DropOffBottomSheetScreenOptions.ValidateAll()
// if the designated constraints aren't met.
type DropOffBottomSheetScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DropOffBottomSheetScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DropOffBottomSheetScreenOptionsMultiError) AllErrors() []error { return m }

// DropOffBottomSheetScreenOptionsValidationError is the validation error
// returned by DropOffBottomSheetScreenOptions.Validate if the designated
// constraints aren't met.
type DropOffBottomSheetScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DropOffBottomSheetScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DropOffBottomSheetScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DropOffBottomSheetScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DropOffBottomSheetScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DropOffBottomSheetScreenOptionsValidationError) ErrorName() string {
	return "DropOffBottomSheetScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e DropOffBottomSheetScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDropOffBottomSheetScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DropOffBottomSheetScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DropOffBottomSheetScreenOptionsValidationError{}

// Validate checks the field values on TieringLandingScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TieringLandingScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TieringLandingScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TieringLandingScreenOptionsMultiError, or nil if none found.
func (m *TieringLandingScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *TieringLandingScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringLandingScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringLandingScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringLandingScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MetaData

	if len(errors) > 0 {
		return TieringLandingScreenOptionsMultiError(errors)
	}

	return nil
}

// TieringLandingScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by TieringLandingScreenOptions.ValidateAll() if
// the designated constraints aren't met.
type TieringLandingScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TieringLandingScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TieringLandingScreenOptionsMultiError) AllErrors() []error { return m }

// TieringLandingScreenOptionsValidationError is the validation error returned
// by TieringLandingScreenOptions.Validate if the designated constraints
// aren't met.
type TieringLandingScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TieringLandingScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TieringLandingScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TieringLandingScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TieringLandingScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TieringLandingScreenOptionsValidationError) ErrorName() string {
	return "TieringLandingScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e TieringLandingScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTieringLandingScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TieringLandingScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TieringLandingScreenOptionsValidationError{}

// Validate checks the field values on DropOffFullScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DropOffFullScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DropOffFullScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DropOffFullScreenOptionsMultiError, or nil if none found.
func (m *DropOffFullScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *DropOffFullScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DropOffFullScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "PageBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "PageBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DropOffFullScreenOptionsValidationError{
				field:  "PageBackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DropOffFullScreenOptionsValidationError{
				field:  "HeaderBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "Banner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "Banner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DropOffFullScreenOptionsValidationError{
				field:  "Banner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCenterImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "CenterImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "CenterImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCenterImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DropOffFullScreenOptionsValidationError{
				field:  "CenterImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DropOffFullScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBenefits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DropOffFullScreenOptionsValidationError{
						field:  fmt.Sprintf("Benefits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DropOffFullScreenOptionsValidationError{
						field:  fmt.Sprintf("Benefits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DropOffFullScreenOptionsValidationError{
					field:  fmt.Sprintf("Benefits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DropOffFullScreenOptionsValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DropOffFullScreenOptionsValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MetaData

	if len(errors) > 0 {
		return DropOffFullScreenOptionsMultiError(errors)
	}

	return nil
}

// DropOffFullScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by DropOffFullScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type DropOffFullScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DropOffFullScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DropOffFullScreenOptionsMultiError) AllErrors() []error { return m }

// DropOffFullScreenOptionsValidationError is the validation error returned by
// DropOffFullScreenOptions.Validate if the designated constraints aren't met.
type DropOffFullScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DropOffFullScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DropOffFullScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DropOffFullScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DropOffFullScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DropOffFullScreenOptionsValidationError) ErrorName() string {
	return "DropOffFullScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e DropOffFullScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDropOffFullScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DropOffFullScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DropOffFullScreenOptionsValidationError{}
