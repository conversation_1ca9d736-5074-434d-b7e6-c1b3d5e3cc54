// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/assetandanalysis/screen_options.proto

package assetandanalysis

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	ui "github.com/epifi/gamma/api/frontend/investment/ui"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	ui1 "github.com/epifi/gamma/api/typesv2/ui"
	sections "github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AssetImportStatusPolingScreenOptions_FlowType int32

const (
	AssetImportStatusPolingScreenOptions_FLOW_TYPE_UNSPECIFIED   AssetImportStatusPolingScreenOptions_FlowType = 0
	AssetImportStatusPolingScreenOptions_FLOW_TYPE_WB_ONBOARDING AssetImportStatusPolingScreenOptions_FlowType = 1
	AssetImportStatusPolingScreenOptions_FLOW_TYPE_MF_REFRESH    AssetImportStatusPolingScreenOptions_FlowType = 2
	AssetImportStatusPolingScreenOptions_FLOW_TYPE_MAGIC_IMPORT  AssetImportStatusPolingScreenOptions_FlowType = 3
)

// Enum value maps for AssetImportStatusPolingScreenOptions_FlowType.
var (
	AssetImportStatusPolingScreenOptions_FlowType_name = map[int32]string{
		0: "FLOW_TYPE_UNSPECIFIED",
		1: "FLOW_TYPE_WB_ONBOARDING",
		2: "FLOW_TYPE_MF_REFRESH",
		3: "FLOW_TYPE_MAGIC_IMPORT",
	}
	AssetImportStatusPolingScreenOptions_FlowType_value = map[string]int32{
		"FLOW_TYPE_UNSPECIFIED":   0,
		"FLOW_TYPE_WB_ONBOARDING": 1,
		"FLOW_TYPE_MF_REFRESH":    2,
		"FLOW_TYPE_MAGIC_IMPORT":  3,
	}
)

func (x AssetImportStatusPolingScreenOptions_FlowType) Enum() *AssetImportStatusPolingScreenOptions_FlowType {
	p := new(AssetImportStatusPolingScreenOptions_FlowType)
	*p = x
	return p
}

func (x AssetImportStatusPolingScreenOptions_FlowType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetImportStatusPolingScreenOptions_FlowType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_enumTypes[0].Descriptor()
}

func (AssetImportStatusPolingScreenOptions_FlowType) Type() protoreflect.EnumType {
	return &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_enumTypes[0]
}

func (x AssetImportStatusPolingScreenOptions_FlowType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetImportStatusPolingScreenOptions_FlowType.Descriptor instead.
func (AssetImportStatusPolingScreenOptions_FlowType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{1, 0}
}

type WealthSduiBottomSheetMetaData_Purpose int32

const (
	WealthSduiBottomSheetMetaData_PURPOSE_UNSPECIFIED WealthSduiBottomSheetMetaData_Purpose = 0
	WealthSduiBottomSheetMetaData_PURPOSE_TALK_TO_AI  WealthSduiBottomSheetMetaData_Purpose = 1
)

// Enum value maps for WealthSduiBottomSheetMetaData_Purpose.
var (
	WealthSduiBottomSheetMetaData_Purpose_name = map[int32]string{
		0: "PURPOSE_UNSPECIFIED",
		1: "PURPOSE_TALK_TO_AI",
	}
	WealthSduiBottomSheetMetaData_Purpose_value = map[string]int32{
		"PURPOSE_UNSPECIFIED": 0,
		"PURPOSE_TALK_TO_AI":  1,
	}
)

func (x WealthSduiBottomSheetMetaData_Purpose) Enum() *WealthSduiBottomSheetMetaData_Purpose {
	p := new(WealthSduiBottomSheetMetaData_Purpose)
	*p = x
	return p
}

func (x WealthSduiBottomSheetMetaData_Purpose) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WealthSduiBottomSheetMetaData_Purpose) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_enumTypes[1].Descriptor()
}

func (WealthSduiBottomSheetMetaData_Purpose) Type() protoreflect.EnumType {
	return &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_enumTypes[1]
}

func (x WealthSduiBottomSheetMetaData_Purpose) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WealthSduiBottomSheetMetaData_Purpose.Descriptor instead.
func (WealthSduiBottomSheetMetaData_Purpose) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{7, 0}
}

// screen options for ASSET_LANDING_PAGE
type AssetLandingPageScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// request params for asset landing page
	Params *ui.LandingPageRequestParams `protobuf:"bytes,2,opt,name=params,proto3" json:"params,omitempty"`
}

func (x *AssetLandingPageScreenOptions) Reset() {
	*x = AssetLandingPageScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetLandingPageScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetLandingPageScreenOptions) ProtoMessage() {}

func (x *AssetLandingPageScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetLandingPageScreenOptions.ProtoReflect.Descriptor instead.
func (*AssetLandingPageScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *AssetLandingPageScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AssetLandingPageScreenOptions) GetParams() *ui.LandingPageRequestParams {
	if x != nil {
		return x.Params
	}
	return nil
}

// Screen options for Asset_IMPORT_STATUS_POLLING_SCREEN
// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=121-9682&t=TMDdzjxiprl4u9IS-4
type AssetImportStatusPolingScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header              *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	AssetType           string                                     `protobuf:"bytes,2,opt,name=asset_type,json=assetType,proto3" json:"asset_type,omitempty"`
	FlowId              string                                     `protobuf:"bytes,3,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	BackgroundColor     string                                     `protobuf:"bytes,4,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	BackgroundLottieUrl string                                     `protobuf:"bytes,5,opt,name=background_lottie_url,json=backgroundLottieUrl,proto3" json:"background_lottie_url,omitempty"`
	// this defines the initial state of the loading animation
	ImportStatusDetails *AssetImportStatusDetails `protobuf:"bytes,6,opt,name=import_status_details,json=importStatusDetails,proto3" json:"import_status_details,omitempty"`
	// in case polling times out without a terminal state, then client will use this to show the in progess state
	ImportInProgressDetails *AssetImportTerminalInProgressDetails `protobuf:"bytes,7,opt,name=import_in_progress_details,json=importInProgressDetails,proto3" json:"import_in_progress_details,omitempty"`
	// in case of any failures, then client will use this to show the in error state
	ImportFailureDetails *AssetImportTerminalFailureDetails                `protobuf:"bytes,8,opt,name=import_failure_details,json=importFailureDetails,proto3" json:"import_failure_details,omitempty"`
	PollingDetails       *AssetImportStatusPolingScreenOptions_PollingData `protobuf:"bytes,9,opt,name=polling_details,json=pollingDetails,proto3" json:"polling_details,omitempty"`
	// current networth of user before importing mf
	CurrentNetworth *typesv2.Money `protobuf:"bytes,10,opt,name=current_networth,json=currentNetworth,proto3" json:"current_networth,omitempty"`
	// Maps to FlowType enum
	FlowType string `protobuf:"bytes,11,opt,name=flow_type,json=flowType,proto3" json:"flow_type,omitempty"`
	// Flow specific payload which is required to check asset import status or construct deeplink
	Payload []byte `protobuf:"bytes,12,opt,name=payload,proto3" json:"payload,omitempty"`
}

func (x *AssetImportStatusPolingScreenOptions) Reset() {
	*x = AssetImportStatusPolingScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetImportStatusPolingScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetImportStatusPolingScreenOptions) ProtoMessage() {}

func (x *AssetImportStatusPolingScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetImportStatusPolingScreenOptions.ProtoReflect.Descriptor instead.
func (*AssetImportStatusPolingScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{1}
}

func (x *AssetImportStatusPolingScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AssetImportStatusPolingScreenOptions) GetAssetType() string {
	if x != nil {
		return x.AssetType
	}
	return ""
}

func (x *AssetImportStatusPolingScreenOptions) GetFlowId() string {
	if x != nil {
		return x.FlowId
	}
	return ""
}

func (x *AssetImportStatusPolingScreenOptions) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

func (x *AssetImportStatusPolingScreenOptions) GetBackgroundLottieUrl() string {
	if x != nil {
		return x.BackgroundLottieUrl
	}
	return ""
}

func (x *AssetImportStatusPolingScreenOptions) GetImportStatusDetails() *AssetImportStatusDetails {
	if x != nil {
		return x.ImportStatusDetails
	}
	return nil
}

func (x *AssetImportStatusPolingScreenOptions) GetImportInProgressDetails() *AssetImportTerminalInProgressDetails {
	if x != nil {
		return x.ImportInProgressDetails
	}
	return nil
}

func (x *AssetImportStatusPolingScreenOptions) GetImportFailureDetails() *AssetImportTerminalFailureDetails {
	if x != nil {
		return x.ImportFailureDetails
	}
	return nil
}

func (x *AssetImportStatusPolingScreenOptions) GetPollingDetails() *AssetImportStatusPolingScreenOptions_PollingData {
	if x != nil {
		return x.PollingDetails
	}
	return nil
}

func (x *AssetImportStatusPolingScreenOptions) GetCurrentNetworth() *typesv2.Money {
	if x != nil {
		return x.CurrentNetworth
	}
	return nil
}

func (x *AssetImportStatusPolingScreenOptions) GetFlowType() string {
	if x != nil {
		return x.FlowType
	}
	return ""
}

func (x *AssetImportStatusPolingScreenOptions) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

type AssetImportStatusDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Title stating the current status of data fetch
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Subtitle stating details of data fetch status
	Subtitle      *common.Text                            `protobuf:"bytes,2,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	LottieDetails *DataFetchLottieDetails                 `protobuf:"bytes,3,opt,name=lottie_details,json=lottieDetails,proto3" json:"lottie_details,omitempty"`
	FooterDetails *AssetImportStatusDetails_FooterDetails `protobuf:"bytes,4,opt,name=footer_details,json=footerDetails,proto3" json:"footer_details,omitempty"`
}

func (x *AssetImportStatusDetails) Reset() {
	*x = AssetImportStatusDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetImportStatusDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetImportStatusDetails) ProtoMessage() {}

func (x *AssetImportStatusDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetImportStatusDetails.ProtoReflect.Descriptor instead.
func (*AssetImportStatusDetails) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{2}
}

func (x *AssetImportStatusDetails) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AssetImportStatusDetails) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *AssetImportStatusDetails) GetLottieDetails() *DataFetchLottieDetails {
	if x != nil {
		return x.LottieDetails
	}
	return nil
}

func (x *AssetImportStatusDetails) GetFooterDetails() *AssetImportStatusDetails_FooterDetails {
	if x != nil {
		return x.FooterDetails
	}
	return nil
}

type DataFetchLottieDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartFrame int32 `protobuf:"varint,2,opt,name=start_frame,json=startFrame,proto3" json:"start_frame,omitempty"`
	EndFrame   int32 `protobuf:"varint,3,opt,name=end_frame,json=endFrame,proto3" json:"end_frame,omitempty"`
	ShouldLoop bool  `protobuf:"varint,4,opt,name=should_loop,json=shouldLoop,proto3" json:"should_loop,omitempty"`
}

func (x *DataFetchLottieDetails) Reset() {
	*x = DataFetchLottieDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataFetchLottieDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataFetchLottieDetails) ProtoMessage() {}

func (x *DataFetchLottieDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataFetchLottieDetails.ProtoReflect.Descriptor instead.
func (*DataFetchLottieDetails) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{3}
}

func (x *DataFetchLottieDetails) GetStartFrame() int32 {
	if x != nil {
		return x.StartFrame
	}
	return 0
}

func (x *DataFetchLottieDetails) GetEndFrame() int32 {
	if x != nil {
		return x.EndFrame
	}
	return 0
}

func (x *DataFetchLottieDetails) GetShouldLoop() bool {
	if x != nil {
		return x.ShouldLoop
	}
	return false
}

// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=158-7623&t=66Wt92ulqSVdD9D2-4
type AssetImportTerminalInProgressDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// text to be shown above the animation eg Net Worth
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// used to update how many funds are connected
	AssetsUpdate *ui1.VerticalKeyValuePair `protobuf:"bytes,2,opt,name=assets_update,json=assetsUpdate,proto3" json:"assets_update,omitempty"`
	ExitCta      *deeplink.Cta             `protobuf:"bytes,3,opt,name=exit_cta,json=exitCta,proto3" json:"exit_cta,omitempty"`
	// based on the status this will pass frames to be loaded on the lottie
	LottieDetails *DataFetchLottieDetails `protobuf:"bytes,4,opt,name=lottie_details,json=lottieDetails,proto3" json:"lottie_details,omitempty"`
	// client will use this to show share cta in nav bar top right corner
	ShareCta *ui1.IconTextComponent `protobuf:"bytes,5,opt,name=share_cta,json=shareCta,proto3" json:"share_cta,omitempty"`
}

func (x *AssetImportTerminalInProgressDetails) Reset() {
	*x = AssetImportTerminalInProgressDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetImportTerminalInProgressDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetImportTerminalInProgressDetails) ProtoMessage() {}

func (x *AssetImportTerminalInProgressDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetImportTerminalInProgressDetails.ProtoReflect.Descriptor instead.
func (*AssetImportTerminalInProgressDetails) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{4}
}

func (x *AssetImportTerminalInProgressDetails) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AssetImportTerminalInProgressDetails) GetAssetsUpdate() *ui1.VerticalKeyValuePair {
	if x != nil {
		return x.AssetsUpdate
	}
	return nil
}

func (x *AssetImportTerminalInProgressDetails) GetExitCta() *deeplink.Cta {
	if x != nil {
		return x.ExitCta
	}
	return nil
}

func (x *AssetImportTerminalInProgressDetails) GetLottieDetails() *DataFetchLottieDetails {
	if x != nil {
		return x.LottieDetails
	}
	return nil
}

func (x *AssetImportTerminalInProgressDetails) GetShareCta() *ui1.IconTextComponent {
	if x != nil {
		return x.ShareCta
	}
	return nil
}

// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=137-7836&t=66Wt92ulqSVdD9D2-4
type AssetImportTerminalFailureDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorTitle    *common.Text            `protobuf:"bytes,1,opt,name=error_title,json=errorTitle,proto3" json:"error_title,omitempty"`
	ErrorMessage  *ui1.IconTextComponent  `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	ErrorImage    *common.VisualElement   `protobuf:"bytes,3,opt,name=error_image,json=errorImage,proto3" json:"error_image,omitempty"`
	Ctas          []*deeplink.Cta         `protobuf:"bytes,4,rep,name=ctas,proto3" json:"ctas,omitempty"`
	LottieDetails *DataFetchLottieDetails `protobuf:"bytes,5,opt,name=lottie_details,json=lottieDetails,proto3" json:"lottie_details,omitempty"`
}

func (x *AssetImportTerminalFailureDetails) Reset() {
	*x = AssetImportTerminalFailureDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetImportTerminalFailureDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetImportTerminalFailureDetails) ProtoMessage() {}

func (x *AssetImportTerminalFailureDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetImportTerminalFailureDetails.ProtoReflect.Descriptor instead.
func (*AssetImportTerminalFailureDetails) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{5}
}

func (x *AssetImportTerminalFailureDetails) GetErrorTitle() *common.Text {
	if x != nil {
		return x.ErrorTitle
	}
	return nil
}

func (x *AssetImportTerminalFailureDetails) GetErrorMessage() *ui1.IconTextComponent {
	if x != nil {
		return x.ErrorMessage
	}
	return nil
}

func (x *AssetImportTerminalFailureDetails) GetErrorImage() *common.VisualElement {
	if x != nil {
		return x.ErrorImage
	}
	return nil
}

func (x *AssetImportTerminalFailureDetails) GetCtas() []*deeplink.Cta {
	if x != nil {
		return x.Ctas
	}
	return nil
}

func (x *AssetImportTerminalFailureDetails) GetLottieDetails() *DataFetchLottieDetails {
	if x != nil {
		return x.LottieDetails
	}
	return nil
}

// Figma : https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=13604-13207&t=Gyk1mAcTx6b3G9sr-4
// Deeplink : WEALTH_SDUI_BOTTOM_SHEET
type WealthSduiBottomSheetScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header           *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	BackgroundColour *widget.BackgroundColour                   `protobuf:"bytes,2,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/assetandanalysis/screen_options.proto.
	CloseIcon          *common.VisualElement  `protobuf:"bytes,3,opt,name=close_icon,json=closeIcon,proto3" json:"close_icon,omitempty"`
	Content            *sections.Section      `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	CloseIconComponent *ui1.IconTextComponent `protobuf:"bytes,5,opt,name=close_icon_component,json=closeIconComponent,proto3" json:"close_icon_component,omitempty"`
	// Meta data for the bottom sheet [WealthSduiBottomSheetMetaData]
	MetaData string `protobuf:"bytes,6,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
}

func (x *WealthSduiBottomSheetScreenOptions) Reset() {
	*x = WealthSduiBottomSheetScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WealthSduiBottomSheetScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WealthSduiBottomSheetScreenOptions) ProtoMessage() {}

func (x *WealthSduiBottomSheetScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WealthSduiBottomSheetScreenOptions.ProtoReflect.Descriptor instead.
func (*WealthSduiBottomSheetScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{6}
}

func (x *WealthSduiBottomSheetScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *WealthSduiBottomSheetScreenOptions) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/assetandanalysis/screen_options.proto.
func (x *WealthSduiBottomSheetScreenOptions) GetCloseIcon() *common.VisualElement {
	if x != nil {
		return x.CloseIcon
	}
	return nil
}

func (x *WealthSduiBottomSheetScreenOptions) GetContent() *sections.Section {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *WealthSduiBottomSheetScreenOptions) GetCloseIconComponent() *ui1.IconTextComponent {
	if x != nil {
		return x.CloseIconComponent
	}
	return nil
}

func (x *WealthSduiBottomSheetScreenOptions) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

// Meta data which defines what is the use of bottom sheet
type WealthSduiBottomSheetMetaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Purpose WealthSduiBottomSheetMetaData_Purpose `protobuf:"varint,1,opt,name=purpose,proto3,enum=api.typesv2.deeplink_screen_option.assetandanalysis.WealthSduiBottomSheetMetaData_Purpose" json:"purpose,omitempty"`
}

func (x *WealthSduiBottomSheetMetaData) Reset() {
	*x = WealthSduiBottomSheetMetaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WealthSduiBottomSheetMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WealthSduiBottomSheetMetaData) ProtoMessage() {}

func (x *WealthSduiBottomSheetMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WealthSduiBottomSheetMetaData.ProtoReflect.Descriptor instead.
func (*WealthSduiBottomSheetMetaData) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{7}
}

func (x *WealthSduiBottomSheetMetaData) GetPurpose() WealthSduiBottomSheetMetaData_Purpose {
	if x != nil {
		return x.Purpose
	}
	return WealthSduiBottomSheetMetaData_PURPOSE_UNSPECIFIED
}

// Deeplink Screen: EXPORT_NETWORTH_DATA
type ExportNetworthDataScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header             *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	AllowedAndroidApps []string                                   `protobuf:"bytes,2,rep,name=allowed_android_apps,json=allowedAndroidApps,proto3" json:"allowed_android_apps,omitempty"`
	AllowedIosApps     []string                                   `protobuf:"bytes,3,rep,name=allowed_ios_apps,json=allowedIosApps,proto3" json:"allowed_ios_apps,omitempty"`
	// api request payload for GetNetworthDataFile
	GetNetworthDataFilePayload string `protobuf:"bytes,4,opt,name=get_networth_data_file_payload,json=getNetworthDataFilePayload,proto3" json:"get_networth_data_file_payload,omitempty"`
}

func (x *ExportNetworthDataScreenOptions) Reset() {
	*x = ExportNetworthDataScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportNetworthDataScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportNetworthDataScreenOptions) ProtoMessage() {}

func (x *ExportNetworthDataScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportNetworthDataScreenOptions.ProtoReflect.Descriptor instead.
func (*ExportNetworthDataScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{8}
}

func (x *ExportNetworthDataScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ExportNetworthDataScreenOptions) GetAllowedAndroidApps() []string {
	if x != nil {
		return x.AllowedAndroidApps
	}
	return nil
}

func (x *ExportNetworthDataScreenOptions) GetAllowedIosApps() []string {
	if x != nil {
		return x.AllowedIosApps
	}
	return nil
}

func (x *ExportNetworthDataScreenOptions) GetGetNetworthDataFilePayload() string {
	if x != nil {
		return x.GetNetworthDataFilePayload
	}
	return ""
}

// Deeplink screen: DOWNLOAD_NETWORTH_DATA
type DownloadNetworthDataScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// api request payload for GetNetworthDataFile
	GetNetworthDataFilePayload string `protobuf:"bytes,2,opt,name=get_networth_data_file_payload,json=getNetworthDataFilePayload,proto3" json:"get_networth_data_file_payload,omitempty"`
}

func (x *DownloadNetworthDataScreenOptions) Reset() {
	*x = DownloadNetworthDataScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadNetworthDataScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadNetworthDataScreenOptions) ProtoMessage() {}

func (x *DownloadNetworthDataScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadNetworthDataScreenOptions.ProtoReflect.Descriptor instead.
func (*DownloadNetworthDataScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{9}
}

func (x *DownloadNetworthDataScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *DownloadNetworthDataScreenOptions) GetGetNetworthDataFilePayload() string {
	if x != nil {
		return x.GetNetworthDataFilePayload
	}
	return ""
}

// Generic screen options to be used for scrollable consent page, can have multiple consent in this
// Screen: WEALTH_GENERIC_RECORD_CONSENT, api call on swipe cta: frontend.consent.RecordConsent
// https://www.figma.com/design/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=21053-3867&t=sIV9m0wWxgcuqPsq-4
// https://drive.google.com/file/d/13VeL_lDoKH8BcyB4oNXu9uzoIJjvusOV/view?usp=sharing
type GenericScrollableRecordConsentScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Icon to show on top of the page
	Icon *common.VisualElement `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	// heading  on screen
	Heading *common.Text `protobuf:"bytes,3,opt,name=heading,proto3" json:"heading,omitempty"`
	// title text on screen
	Title *common.Text `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// subtitle text on screen
	SubTitle *common.Text `protobuf:"bytes,5,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	// list of consent data to show
	// https://drive.google.com/file/d/1FdVK9jeaJq_bBcI0S0TmbubUVJgOs4X2/view?usp=sharing
	ContentData []*ui1.IconTextComponent `protobuf:"bytes,6,rep,name=content_data,json=contentData,proto3" json:"content_data,omitempty"`
	// list of consent ids to record
	ConsentIds []string `protobuf:"bytes,7,rep,name=consent_ids,json=consentIds,proto3" json:"consent_ids,omitempty"`
	// swipe button to show at footer of page
	SwipeButton *ui1.SwipeButton `protobuf:"bytes,8,opt,name=swipe_button,json=swipeButton,proto3" json:"swipe_button,omitempty"`
}

func (x *GenericScrollableRecordConsentScreenOptions) Reset() {
	*x = GenericScrollableRecordConsentScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenericScrollableRecordConsentScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenericScrollableRecordConsentScreenOptions) ProtoMessage() {}

func (x *GenericScrollableRecordConsentScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenericScrollableRecordConsentScreenOptions.ProtoReflect.Descriptor instead.
func (*GenericScrollableRecordConsentScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{10}
}

func (x *GenericScrollableRecordConsentScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GenericScrollableRecordConsentScreenOptions) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *GenericScrollableRecordConsentScreenOptions) GetHeading() *common.Text {
	if x != nil {
		return x.Heading
	}
	return nil
}

func (x *GenericScrollableRecordConsentScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *GenericScrollableRecordConsentScreenOptions) GetSubTitle() *common.Text {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *GenericScrollableRecordConsentScreenOptions) GetContentData() []*ui1.IconTextComponent {
	if x != nil {
		return x.ContentData
	}
	return nil
}

func (x *GenericScrollableRecordConsentScreenOptions) GetConsentIds() []string {
	if x != nil {
		return x.ConsentIds
	}
	return nil
}

func (x *GenericScrollableRecordConsentScreenOptions) GetSwipeButton() *ui1.SwipeButton {
	if x != nil {
		return x.SwipeButton
	}
	return nil
}

type AssetImportStatusPolingScreenOptions_PollingData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// polling should be done in this intervals
	PollingInterval int32 `protobuf:"varint,1,opt,name=polling_interval,json=pollingInterval,proto3" json:"polling_interval,omitempty"`
	// max duration for which the client should poll
	MaxPollingDuration int32 `protobuf:"varint,2,opt,name=max_polling_duration,json=maxPollingDuration,proto3" json:"max_polling_duration,omitempty"`
}

func (x *AssetImportStatusPolingScreenOptions_PollingData) Reset() {
	*x = AssetImportStatusPolingScreenOptions_PollingData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetImportStatusPolingScreenOptions_PollingData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetImportStatusPolingScreenOptions_PollingData) ProtoMessage() {}

func (x *AssetImportStatusPolingScreenOptions_PollingData) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetImportStatusPolingScreenOptions_PollingData.ProtoReflect.Descriptor instead.
func (*AssetImportStatusPolingScreenOptions_PollingData) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{1, 0}
}

func (x *AssetImportStatusPolingScreenOptions_PollingData) GetPollingInterval() int32 {
	if x != nil {
		return x.PollingInterval
	}
	return 0
}

func (x *AssetImportStatusPolingScreenOptions_PollingData) GetMaxPollingDuration() int32 {
	if x != nil {
		return x.MaxPollingDuration
	}
	return 0
}

type AssetImportStatusDetails_FooterDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content    *common.Text             `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	RightImage *common.VisualElement    `protobuf:"bytes,2,opt,name=right_image,json=rightImage,proto3" json:"right_image,omitempty"`
	BgColor    *widget.BackgroundColour `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *AssetImportStatusDetails_FooterDetails) Reset() {
	*x = AssetImportStatusDetails_FooterDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetImportStatusDetails_FooterDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetImportStatusDetails_FooterDetails) ProtoMessage() {}

func (x *AssetImportStatusDetails_FooterDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetImportStatusDetails_FooterDetails.ProtoReflect.Descriptor instead.
func (*AssetImportStatusDetails_FooterDetails) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP(), []int{2, 0}
}

func (x *AssetImportStatusDetails_FooterDetails) GetContent() *common.Text {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *AssetImportStatusDetails_FooterDetails) GetRightImage() *common.VisualElement {
	if x != nil {
		return x.RightImage
	}
	return nil
}

func (x *AssetImportStatusDetails_FooterDetails) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

var File_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDesc = []byte{
	0x0a, 0x48, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x69, 0x73, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x33, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x1a,
	0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x75,
	0x69, 0x2f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x6e,
	0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x75, 0x69, 0x2f, 0x73, 0x64, 0x75, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x73,
	0x77, 0x69, 0x70, 0x65, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75,
	0x69, 0x2f, 0x76, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xb9, 0x01, 0x0a, 0x1d, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x50, 0x61, 0x67, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x48, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x75, 0x69, 0x2e, 0x4c, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xa6, 0x09, 0x0a, 0x24,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x50, 0x6f, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x32, 0x0a, 0x15, 0x62, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x6c, 0x6f, 0x74, 0x74, 0x69, 0x65, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x69, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x81, 0x01, 0x0a, 0x15,
	0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69,
	0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x13, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x96, 0x01, 0x0a, 0x1a, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x6e, 0x5f, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x59, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x61,
	0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x17, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x8c, 0x01, 0x0a, 0x16, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x2e,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x14, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x8e, 0x01, 0x0a, 0x0f, 0x70, 0x6f, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x61, 0x6e, 0x64, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x6f, 0x6c, 0x69, 0x6e, 0x67, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x50, 0x6f, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0e, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3d, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x77,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x1a, 0x6a,
	0x0a, 0x0b, 0x50, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x29, 0x0a,
	0x10, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x30, 0x0a, 0x14, 0x6d, 0x61, 0x78, 0x5f,
	0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x6d, 0x61, 0x78, 0x50, 0x6f, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x78, 0x0a, 0x08, 0x46, 0x6c,
	0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57,
	0x42, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x18,
	0x0a, 0x14, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x52,
	0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x49, 0x4d, 0x50, 0x4f,
	0x52, 0x54, 0x10, 0x03, 0x22, 0xce, 0x04, 0x0a, 0x18, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73,
	0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x72, 0x0a, 0x0e, 0x6c, 0x6f, 0x74, 0x74, 0x69,
	0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x4b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x69, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c,
	0x6f, 0x74, 0x74, 0x69, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x6c, 0x6f,
	0x74, 0x74, 0x69, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x82, 0x01, 0x0a, 0x0e,
	0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x61,
	0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x2e, 0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x0d, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x1a, 0xd2, 0x01, 0x0a, 0x0d, 0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x32, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x0b, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a,
	0x72, 0x69, 0x67, 0x68, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x77, 0x0a, 0x16, 0x44, 0x61, 0x74, 0x61, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x69, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x64, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x6c, 0x6f, 0x6f, 0x70, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x4c, 0x6f, 0x6f, 0x70, 0x22, 0x88,
	0x03, 0x0a, 0x24, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x49, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x50, 0x61, 0x69, 0x72, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x31, 0x0a, 0x08, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x07, 0x65, 0x78,
	0x69, 0x74, 0x43, 0x74, 0x61, 0x12, 0x72, 0x0a, 0x0e, 0x6c, 0x6f, 0x74, 0x74, 0x69, 0x65, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4b, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x69, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x6f, 0x74,
	0x74, 0x69, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x6c, 0x6f, 0x74, 0x74,
	0x69, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x0a, 0x09, 0x73, 0x68, 0x61,
	0x72, 0x65, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x08, 0x73, 0x68, 0x61, 0x72, 0x65, 0x43, 0x74, 0x61, 0x22, 0x8a, 0x03, 0x0a, 0x21, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x39, 0x0a, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0a,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x46, 0x0a, 0x0d, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x04, 0x63, 0x74, 0x61, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x04, 0x63, 0x74,
	0x61, 0x73, 0x12, 0x72, 0x0a, 0x0e, 0x6c, 0x6f, 0x74, 0x74, 0x69, 0x65, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x69, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x6c, 0x6f, 0x74, 0x74, 0x69, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xca, 0x03, 0x0a, 0x22, 0x57, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x53, 0x64, 0x75, 0x69, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x5b, 0x0a,
	0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69,
	0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x44, 0x0a, 0x0a, 0x63, 0x6c,
	0x6f, 0x73, 0x65, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x49, 0x63, 0x6f, 0x6e,
	0x12, 0x3f, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x53, 0x0a, 0x14, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x12, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44,
	0x61, 0x74, 0x61, 0x22, 0xd1, 0x01, 0x0a, 0x1d, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x64,
	0x75, 0x69, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x74, 0x0a, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x5a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x2e, 0x57, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x53, 0x64, 0x75, 0x69, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65,
	0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f,
	0x73, 0x65, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x22, 0x3a, 0x0a, 0x07, 0x50,
	0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x16, 0x0a, 0x12, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x54, 0x41, 0x4c, 0x4b, 0x5f,
	0x54, 0x4f, 0x5f, 0x41, 0x49, 0x10, 0x01, 0x22, 0x91, 0x02, 0x0a, 0x1f, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x61, 0x74, 0x61, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x61,
	0x70, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x41, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x41, 0x70, 0x70, 0x73, 0x12, 0x28, 0x0a,
	0x10, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x69, 0x6f, 0x73, 0x5f, 0x61, 0x70, 0x70,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x49, 0x6f, 0x73, 0x41, 0x70, 0x70, 0x73, 0x12, 0x42, 0x0a, 0x1e, 0x67, 0x65, 0x74, 0x5f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x1a, 0x67, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x61, 0x74, 0x61,
	0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0xb7, 0x01, 0x0a, 0x21,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x44, 0x61, 0x74, 0x61, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x42, 0x0a, 0x1e, 0x67, 0x65, 0x74, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x67, 0x65, 0x74, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0xf6, 0x03, 0x0a, 0x2b, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x69,
	0x63, 0x53, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x07,
	0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x35, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73,
	0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x44, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x3e,
	0x0a, 0x0c, 0x73, 0x77, 0x69, 0x70, 0x65, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x77, 0x69, 0x70, 0x65, 0x42, 0x75, 0x74, 0x74, 0x6f,
	0x6e, 0x52, 0x0b, 0x73, 0x77, 0x69, 0x70, 0x65, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x42, 0x9a,
	0x01, 0x0a, 0x4a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x50, 0x01, 0x5a,
	0x4a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescData = file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_goTypes = []interface{}{
	(AssetImportStatusPolingScreenOptions_FlowType)(0),       // 0: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusPolingScreenOptions.FlowType
	(WealthSduiBottomSheetMetaData_Purpose)(0),               // 1: api.typesv2.deeplink_screen_option.assetandanalysis.WealthSduiBottomSheetMetaData.Purpose
	(*AssetLandingPageScreenOptions)(nil),                    // 2: api.typesv2.deeplink_screen_option.assetandanalysis.AssetLandingPageScreenOptions
	(*AssetImportStatusPolingScreenOptions)(nil),             // 3: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusPolingScreenOptions
	(*AssetImportStatusDetails)(nil),                         // 4: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails
	(*DataFetchLottieDetails)(nil),                           // 5: api.typesv2.deeplink_screen_option.assetandanalysis.DataFetchLottieDetails
	(*AssetImportTerminalInProgressDetails)(nil),             // 6: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalInProgressDetails
	(*AssetImportTerminalFailureDetails)(nil),                // 7: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalFailureDetails
	(*WealthSduiBottomSheetScreenOptions)(nil),               // 8: api.typesv2.deeplink_screen_option.assetandanalysis.WealthSduiBottomSheetScreenOptions
	(*WealthSduiBottomSheetMetaData)(nil),                    // 9: api.typesv2.deeplink_screen_option.assetandanalysis.WealthSduiBottomSheetMetaData
	(*ExportNetworthDataScreenOptions)(nil),                  // 10: api.typesv2.deeplink_screen_option.assetandanalysis.ExportNetworthDataScreenOptions
	(*DownloadNetworthDataScreenOptions)(nil),                // 11: api.typesv2.deeplink_screen_option.assetandanalysis.DownloadNetworthDataScreenOptions
	(*GenericScrollableRecordConsentScreenOptions)(nil),      // 12: api.typesv2.deeplink_screen_option.assetandanalysis.GenericScrollableRecordConsentScreenOptions
	(*AssetImportStatusPolingScreenOptions_PollingData)(nil), // 13: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusPolingScreenOptions.PollingData
	(*AssetImportStatusDetails_FooterDetails)(nil),           // 14: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails.FooterDetails
	(*deeplink_screen_option.ScreenOptionHeader)(nil),        // 15: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*ui.LandingPageRequestParams)(nil),                      // 16: frontend.investment.ui.LandingPageRequestParams
	(*typesv2.Money)(nil),                                    // 17: api.typesv2.Money
	(*common.Text)(nil),                                      // 18: api.typesv2.common.Text
	(*ui1.VerticalKeyValuePair)(nil),                         // 19: api.typesv2.ui.VerticalKeyValuePair
	(*deeplink.Cta)(nil),                                     // 20: frontend.deeplink.Cta
	(*ui1.IconTextComponent)(nil),                            // 21: api.typesv2.ui.IconTextComponent
	(*common.VisualElement)(nil),                             // 22: api.typesv2.common.VisualElement
	(*widget.BackgroundColour)(nil),                          // 23: api.typesv2.common.ui.widget.BackgroundColour
	(*sections.Section)(nil),                                 // 24: api.typesv2.ui.sdui.sections.Section
	(*ui1.SwipeButton)(nil),                                  // 25: api.typesv2.ui.SwipeButton
}
var file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_depIdxs = []int32{
	15, // 0: api.typesv2.deeplink_screen_option.assetandanalysis.AssetLandingPageScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	16, // 1: api.typesv2.deeplink_screen_option.assetandanalysis.AssetLandingPageScreenOptions.params:type_name -> frontend.investment.ui.LandingPageRequestParams
	15, // 2: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusPolingScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	4,  // 3: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusPolingScreenOptions.import_status_details:type_name -> api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails
	6,  // 4: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusPolingScreenOptions.import_in_progress_details:type_name -> api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalInProgressDetails
	7,  // 5: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusPolingScreenOptions.import_failure_details:type_name -> api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalFailureDetails
	13, // 6: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusPolingScreenOptions.polling_details:type_name -> api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusPolingScreenOptions.PollingData
	17, // 7: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusPolingScreenOptions.current_networth:type_name -> api.typesv2.Money
	18, // 8: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails.title:type_name -> api.typesv2.common.Text
	18, // 9: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails.subtitle:type_name -> api.typesv2.common.Text
	5,  // 10: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails.lottie_details:type_name -> api.typesv2.deeplink_screen_option.assetandanalysis.DataFetchLottieDetails
	14, // 11: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails.footer_details:type_name -> api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails.FooterDetails
	18, // 12: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalInProgressDetails.title:type_name -> api.typesv2.common.Text
	19, // 13: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalInProgressDetails.assets_update:type_name -> api.typesv2.ui.VerticalKeyValuePair
	20, // 14: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalInProgressDetails.exit_cta:type_name -> frontend.deeplink.Cta
	5,  // 15: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalInProgressDetails.lottie_details:type_name -> api.typesv2.deeplink_screen_option.assetandanalysis.DataFetchLottieDetails
	21, // 16: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalInProgressDetails.share_cta:type_name -> api.typesv2.ui.IconTextComponent
	18, // 17: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalFailureDetails.error_title:type_name -> api.typesv2.common.Text
	21, // 18: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalFailureDetails.error_message:type_name -> api.typesv2.ui.IconTextComponent
	22, // 19: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalFailureDetails.error_image:type_name -> api.typesv2.common.VisualElement
	20, // 20: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalFailureDetails.ctas:type_name -> frontend.deeplink.Cta
	5,  // 21: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalFailureDetails.lottie_details:type_name -> api.typesv2.deeplink_screen_option.assetandanalysis.DataFetchLottieDetails
	15, // 22: api.typesv2.deeplink_screen_option.assetandanalysis.WealthSduiBottomSheetScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	23, // 23: api.typesv2.deeplink_screen_option.assetandanalysis.WealthSduiBottomSheetScreenOptions.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	22, // 24: api.typesv2.deeplink_screen_option.assetandanalysis.WealthSduiBottomSheetScreenOptions.close_icon:type_name -> api.typesv2.common.VisualElement
	24, // 25: api.typesv2.deeplink_screen_option.assetandanalysis.WealthSduiBottomSheetScreenOptions.content:type_name -> api.typesv2.ui.sdui.sections.Section
	21, // 26: api.typesv2.deeplink_screen_option.assetandanalysis.WealthSduiBottomSheetScreenOptions.close_icon_component:type_name -> api.typesv2.ui.IconTextComponent
	1,  // 27: api.typesv2.deeplink_screen_option.assetandanalysis.WealthSduiBottomSheetMetaData.purpose:type_name -> api.typesv2.deeplink_screen_option.assetandanalysis.WealthSduiBottomSheetMetaData.Purpose
	15, // 28: api.typesv2.deeplink_screen_option.assetandanalysis.ExportNetworthDataScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	15, // 29: api.typesv2.deeplink_screen_option.assetandanalysis.DownloadNetworthDataScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	15, // 30: api.typesv2.deeplink_screen_option.assetandanalysis.GenericScrollableRecordConsentScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	22, // 31: api.typesv2.deeplink_screen_option.assetandanalysis.GenericScrollableRecordConsentScreenOptions.icon:type_name -> api.typesv2.common.VisualElement
	18, // 32: api.typesv2.deeplink_screen_option.assetandanalysis.GenericScrollableRecordConsentScreenOptions.heading:type_name -> api.typesv2.common.Text
	18, // 33: api.typesv2.deeplink_screen_option.assetandanalysis.GenericScrollableRecordConsentScreenOptions.title:type_name -> api.typesv2.common.Text
	18, // 34: api.typesv2.deeplink_screen_option.assetandanalysis.GenericScrollableRecordConsentScreenOptions.sub_title:type_name -> api.typesv2.common.Text
	21, // 35: api.typesv2.deeplink_screen_option.assetandanalysis.GenericScrollableRecordConsentScreenOptions.content_data:type_name -> api.typesv2.ui.IconTextComponent
	25, // 36: api.typesv2.deeplink_screen_option.assetandanalysis.GenericScrollableRecordConsentScreenOptions.swipe_button:type_name -> api.typesv2.ui.SwipeButton
	18, // 37: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails.FooterDetails.content:type_name -> api.typesv2.common.Text
	22, // 38: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails.FooterDetails.right_image:type_name -> api.typesv2.common.VisualElement
	23, // 39: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails.FooterDetails.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	40, // [40:40] is the sub-list for method output_type
	40, // [40:40] is the sub-list for method input_type
	40, // [40:40] is the sub-list for extension type_name
	40, // [40:40] is the sub-list for extension extendee
	0,  // [0:40] is the sub-list for field type_name
}

func init() { file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_init() }
func file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_init() {
	if File_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetLandingPageScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetImportStatusPolingScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetImportStatusDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataFetchLottieDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetImportTerminalInProgressDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetImportTerminalFailureDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WealthSduiBottomSheetScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WealthSduiBottomSheetMetaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportNetworthDataScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadNetworthDataScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenericScrollableRecordConsentScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetImportStatusPolingScreenOptions_PollingData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetImportStatusDetails_FooterDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_depIdxs,
		EnumInfos:         file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_enumTypes,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto = out.File
	file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_assetandanalysis_screen_options_proto_depIdxs = nil
}
