syntax = "proto3";

package api.typesv2.ui;

import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui";
option java_package = "com.github.epifi.gamma.api.typesv2.ui";

// Component to render broadly two texts, one at top and one at bottom
// [Optional] Images can be added on left, right side of the those texts
// width of the component is max(width of title, width of value)
// eg) https://drive.google.com/file/d/1Rq54x9z4qtP-GbNc82HaURDbqhcbhUqB/view?usp=share_link
message VerticalKeyValuePair {
  // title will be placed top center aligned horizontally
  // title is tappable. eg) info icon can be added and tapping on it can direct to InfoPopUp screen
  typesv2.ui.IconTextComponent title = 1;
  // value will be placed bottom center aligned horizontally
  // value is tappable. eg) info icon can be added and tapping on it can direct to InfoPopUp screen
  typesv2.ui.IconTextComponent value = 2;

  // Padding for the gap between the title and value
  // Can be picked directly from the figma
  int32 vertical_padding_btw_title_value = 3;

  // Specifies the Horizontal alignment of the VerticalKeyValuePair's child items(title, value)'s alignment.
  VerticalKeyValuePairHAlignment h_alignment = 4;
  // Note: properties like bgColor, corner radius, border, shadows, alignment of the title, value(default it is HCenter&VCenter aligned)
  // can be added based on the need.
}

// Specifies the Horizontal alignment of the VerticalKeyValuePair's child items(title, value)'s alignment.
enum VerticalKeyValuePairHAlignment {
  VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_CENTER = 0;
  VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_LEFT = 1;
  VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_RIGHT = 2;
}


