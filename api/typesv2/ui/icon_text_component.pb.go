// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/ui/icon_text_component.proto

package ui

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Component for showing icon+text component on the screen, explicitly for horizontal components
type IconTextComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Icon to be shown to the left of the text
	// Deprecated in favour of left_visual_element
	//
	// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
	LeftIcon *common.Image `protobuf:"bytes,1,opt,name=left_icon,json=leftIcon,proto3" json:"left_icon,omitempty"`
	// Text to be shown
	// Impl. Caveats
	// - Client to not put any spacing when rendering this list of texts. Its BE's onus to provide space/separator as part of the text itself etc.
	// - If using HTML, Pass only one element in the array. Array is only meant for simple multi-formatted text.
	// Client would take this array to create a single label/span with texts having multiple formats.
	// Combination of texts which cannot confine to this would break.
	Texts []*common.Text `protobuf:"bytes,2,rep,name=texts,proto3" json:"texts,omitempty"`
	// Icon to be shown to the right of the text
	// Deprecated in favour of right_visual_element
	//
	// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
	RightIcon *common.Image `protobuf:"bytes,3,opt,name=right_icon,json=rightIcon,proto3" json:"right_icon,omitempty"`
	// Padding for the gap between the left image and text
	// Can be picked directly from the figma
	// Eg: https://drive.google.com/file/d/1g7vptUsEXrLM8ES4tnWAkBSGDV40IICI/view?usp=sharing
	LeftImgTxtPadding int32 `protobuf:"varint,4,opt,name=left_img_txt_padding,json=leftImgTxtPadding,proto3" json:"left_img_txt_padding,omitempty"`
	// Padding for the gap between the right image and text
	// Can be picked directly from the figma
	// Eg: https://drive.google.com/file/d/1WaGANP9bFxGMysmwSjGRQe8e9opxXM-I/view?usp=sharing
	RightImgTxtPadding int32 `protobuf:"varint,5,opt,name=right_img_txt_padding,json=rightImgTxtPadding,proto3" json:"right_img_txt_padding,omitempty"`
	// Deeplink for redirection if the component is clicked
	Deeplink            *deeplink.Deeplink                     `protobuf:"bytes,6,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	ContainerProperties *IconTextComponent_ContainerProperties `protobuf:"bytes,7,opt,name=container_properties,json=containerProperties,proto3" json:"container_properties,omitempty"`
	// visual element to be rendered to the left side of component
	LeftVisualElement *common.VisualElement `protobuf:"bytes,8,opt,name=left_visual_element,json=leftVisualElement,proto3" json:"left_visual_element,omitempty"`
	// visual element to be rendered to the right side of component
	RightVisualElement *common.VisualElement `protobuf:"bytes,9,opt,name=right_visual_element,json=rightVisualElement,proto3" json:"right_visual_element,omitempty"`
}

func (x *IconTextComponent) Reset() {
	*x = IconTextComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_icon_text_component_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IconTextComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IconTextComponent) ProtoMessage() {}

func (x *IconTextComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_icon_text_component_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IconTextComponent.ProtoReflect.Descriptor instead.
func (*IconTextComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_icon_text_component_proto_rawDescGZIP(), []int{0}
}

// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
func (x *IconTextComponent) GetLeftIcon() *common.Image {
	if x != nil {
		return x.LeftIcon
	}
	return nil
}

func (x *IconTextComponent) GetTexts() []*common.Text {
	if x != nil {
		return x.Texts
	}
	return nil
}

// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
func (x *IconTextComponent) GetRightIcon() *common.Image {
	if x != nil {
		return x.RightIcon
	}
	return nil
}

func (x *IconTextComponent) GetLeftImgTxtPadding() int32 {
	if x != nil {
		return x.LeftImgTxtPadding
	}
	return 0
}

func (x *IconTextComponent) GetRightImgTxtPadding() int32 {
	if x != nil {
		return x.RightImgTxtPadding
	}
	return 0
}

func (x *IconTextComponent) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *IconTextComponent) GetContainerProperties() *IconTextComponent_ContainerProperties {
	if x != nil {
		return x.ContainerProperties
	}
	return nil
}

func (x *IconTextComponent) GetLeftVisualElement() *common.VisualElement {
	if x != nil {
		return x.LeftVisualElement
	}
	return nil
}

func (x *IconTextComponent) GetRightVisualElement() *common.VisualElement {
	if x != nil {
		return x.RightVisualElement
	}
	return nil
}

// Component for showing icon+text component on the screen, explicitly for vertical components
type VerticalIconTextComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Icon to be shown to the top of the text
	// Figma SS: https://drive.google.com/file/d/14NNB0U5YJE2dWSIwXHdmIrpUdFcWi1zy/view?usp=sharing
	// Deprecated in favour of top_visual_element
	//
	// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
	TopIcon *common.Image `protobuf:"bytes,1,opt,name=top_icon,json=topIcon,proto3" json:"top_icon,omitempty"`
	// Padding for the gap between the image and text at the bottom of it
	// Can be picked directly from the figma
	TopImgTxtPadding int32 `protobuf:"varint,2,opt,name=top_img_txt_padding,json=topImgTxtPadding,proto3" json:"top_img_txt_padding,omitempty"`
	// Icon to be shown to the bottom of the text
	// Deprecated in favour of bottom_visual_element
	//
	// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
	BottomIcon *common.Image `protobuf:"bytes,3,opt,name=bottom_icon,json=bottomIcon,proto3" json:"bottom_icon,omitempty"`
	// Padding for the gap between the image and text on top of it
	// Can be picked directly from the figma
	BottomImgTxtPadding int32 `protobuf:"varint,4,opt,name=bottom_img_txt_padding,json=bottomImgTxtPadding,proto3" json:"bottom_img_txt_padding,omitempty"`
	// Text to be shown
	// Impl. Caveats
	// - Client to not put any spacing when rendering this list of texts. Its BE's onus to provide space/separator as part of the text itself etc.
	// /n needs to be used in case the text needs to be broken down into multiple lines
	// - If using HTML, Pass only one element in the array. Array is only meant for simple multi-formatted text.
	// Client would take this array to create a single label/span with texts having multiple formats.
	// Combination of texts which cannot confine to this would break.
	// Stack would be vertical
	Texts []*common.Text `protobuf:"bytes,5,rep,name=texts,proto3" json:"texts,omitempty"`
	// Deeplink for redirection if the component is clicked
	Deeplink            *deeplink.Deeplink                             `protobuf:"bytes,6,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	ContainerProperties *VerticalIconTextComponent_ContainerProperties `protobuf:"bytes,7,opt,name=container_properties,json=containerProperties,proto3" json:"container_properties,omitempty"`
	// visual element to be rendered to the top side of component
	TopVisualElement *common.VisualElement `protobuf:"bytes,8,opt,name=top_visual_element,json=topVisualElement,proto3" json:"top_visual_element,omitempty"`
	// visual element to be rendered to the bottom side of component
	BottomVisualElement *common.VisualElement `protobuf:"bytes,9,opt,name=bottom_visual_element,json=bottomVisualElement,proto3" json:"bottom_visual_element,omitempty"`
}

func (x *VerticalIconTextComponent) Reset() {
	*x = VerticalIconTextComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_icon_text_component_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerticalIconTextComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerticalIconTextComponent) ProtoMessage() {}

func (x *VerticalIconTextComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_icon_text_component_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerticalIconTextComponent.ProtoReflect.Descriptor instead.
func (*VerticalIconTextComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_icon_text_component_proto_rawDescGZIP(), []int{1}
}

// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
func (x *VerticalIconTextComponent) GetTopIcon() *common.Image {
	if x != nil {
		return x.TopIcon
	}
	return nil
}

func (x *VerticalIconTextComponent) GetTopImgTxtPadding() int32 {
	if x != nil {
		return x.TopImgTxtPadding
	}
	return 0
}

// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
func (x *VerticalIconTextComponent) GetBottomIcon() *common.Image {
	if x != nil {
		return x.BottomIcon
	}
	return nil
}

func (x *VerticalIconTextComponent) GetBottomImgTxtPadding() int32 {
	if x != nil {
		return x.BottomImgTxtPadding
	}
	return 0
}

func (x *VerticalIconTextComponent) GetTexts() []*common.Text {
	if x != nil {
		return x.Texts
	}
	return nil
}

func (x *VerticalIconTextComponent) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *VerticalIconTextComponent) GetContainerProperties() *VerticalIconTextComponent_ContainerProperties {
	if x != nil {
		return x.ContainerProperties
	}
	return nil
}

func (x *VerticalIconTextComponent) GetTopVisualElement() *common.VisualElement {
	if x != nil {
		return x.TopVisualElement
	}
	return nil
}

func (x *VerticalIconTextComponent) GetBottomVisualElement() *common.VisualElement {
	if x != nil {
		return x.BottomVisualElement
	}
	return nil
}

// Needed if the IconTextComponent is within a 'container'
// If not specified, the icon text would be rendered with no container.
// Thus, it would be part of the component this is part of
type IconTextComponent_ContainerProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If unspecified, default handling would be to use the same background color as the place
	// where the content is being rendered
	// Deprecated in favour of background_colour to add extended support for different types of background colours
	//
	// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
	BgColor string `protobuf:"bytes,1,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// corner radius for the container
	CornerRadius int32 `protobuf:"varint,2,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	// Optional: Height of the container
	// If not specified, container height would be resized according to the content
	Height int32 `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	// Optional: Width of the container
	// If not specified, container width would be resized according to the content
	Width int32 `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`
	// Eg: Figma SS: https://drive.google.com/file/d/1xVYsSMWqQiypYcymcLk0TxJbkl7wYR4I/view?usp=sharing
	LeftPadding int32 `protobuf:"varint,5,opt,name=left_padding,json=leftPadding,proto3" json:"left_padding,omitempty"`
	// Eg: Figma SS: https://drive.google.com/file/d/1-PlWgcwRSwqCCPDgYkp1J4qKDxgF738A/view?usp=sharing
	RightPadding int32 `protobuf:"varint,6,opt,name=right_padding,json=rightPadding,proto3" json:"right_padding,omitempty"`
	// Eg: Figma SS: https://drive.google.com/file/d/1N850dgvI74kDYiJXdf4MPa33KeLcJ_G3/view?usp=sharing
	TopPadding int32 `protobuf:"varint,7,opt,name=top_padding,json=topPadding,proto3" json:"top_padding,omitempty"`
	// Eg: Figma SS: https://drive.google.com/file/d/1N850dgvI74kDYiJXdf4MPa33KeLcJ_G3/view?usp=sharing
	BottomPadding int32 `protobuf:"varint,8,opt,name=bottom_padding,json=bottomPadding,proto3" json:"bottom_padding,omitempty"`
	// border color of the container: Eg Figma SS: https://drive.google.com/file/d/1eO9KqLzdlg6bXvgIinyBFC8KOH9uo-3M/view?usp=share_link
	// Deprecated in favour of bg_border_colour to add extended support for different types of borders colours like gradients
	//
	// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
	BorderColor string `protobuf:"bytes,9,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
	// border width of the container: Eg Figma SS: https://drive.google.com/file/d/1eO9KqLzdlg6bXvgIinyBFC8KOH9uo-3M/view?usp=share_link
	BorderWidth int32 `protobuf:"varint,10,opt,name=border_width,json=borderWidth,proto3" json:"border_width,omitempty"`
	// Optional: shadow height of the container
	ShadowHeight string `protobuf:"bytes,11,opt,name=shadow_height,json=shadowHeight,proto3" json:"shadow_height,omitempty"`
	// Optional: shadow color of the container
	ShadowColor string `protobuf:"bytes,12,opt,name=shadow_color,json=shadowColor,proto3" json:"shadow_color,omitempty"`
	// Optional : Background color for the itc widget
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,13,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// Optional : Border color for the itc widget
	BgBorderColour *widget.BackgroundColour `protobuf:"bytes,14,opt,name=bg_border_colour,json=bgBorderColour,proto3" json:"bg_border_colour,omitempty"`
}

func (x *IconTextComponent_ContainerProperties) Reset() {
	*x = IconTextComponent_ContainerProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_icon_text_component_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IconTextComponent_ContainerProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IconTextComponent_ContainerProperties) ProtoMessage() {}

func (x *IconTextComponent_ContainerProperties) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_icon_text_component_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IconTextComponent_ContainerProperties.ProtoReflect.Descriptor instead.
func (*IconTextComponent_ContainerProperties) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_icon_text_component_proto_rawDescGZIP(), []int{0, 0}
}

// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
func (x *IconTextComponent_ContainerProperties) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *IconTextComponent_ContainerProperties) GetCornerRadius() int32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *IconTextComponent_ContainerProperties) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *IconTextComponent_ContainerProperties) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *IconTextComponent_ContainerProperties) GetLeftPadding() int32 {
	if x != nil {
		return x.LeftPadding
	}
	return 0
}

func (x *IconTextComponent_ContainerProperties) GetRightPadding() int32 {
	if x != nil {
		return x.RightPadding
	}
	return 0
}

func (x *IconTextComponent_ContainerProperties) GetTopPadding() int32 {
	if x != nil {
		return x.TopPadding
	}
	return 0
}

func (x *IconTextComponent_ContainerProperties) GetBottomPadding() int32 {
	if x != nil {
		return x.BottomPadding
	}
	return 0
}

// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
func (x *IconTextComponent_ContainerProperties) GetBorderColor() string {
	if x != nil {
		return x.BorderColor
	}
	return ""
}

func (x *IconTextComponent_ContainerProperties) GetBorderWidth() int32 {
	if x != nil {
		return x.BorderWidth
	}
	return 0
}

func (x *IconTextComponent_ContainerProperties) GetShadowHeight() string {
	if x != nil {
		return x.ShadowHeight
	}
	return ""
}

func (x *IconTextComponent_ContainerProperties) GetShadowColor() string {
	if x != nil {
		return x.ShadowColor
	}
	return ""
}

func (x *IconTextComponent_ContainerProperties) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *IconTextComponent_ContainerProperties) GetBgBorderColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgBorderColour
	}
	return nil
}

// Needed if the IconTextComponent is within a 'container'
// If not specified, the icon text would be rendered with no container.
// Thus, it would be part of the component this is part of
type VerticalIconTextComponent_ContainerProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If unspecified, default handling would be to use the same background color as the place
	// where the content is being rendered
	//
	// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
	BgColor string `protobuf:"bytes,1,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// corner radius for the container
	CornerRadius int32 `protobuf:"varint,2,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	// Optional: Height of the container
	// If not specified, container height would be resized according to the content
	Height int32 `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	// Optional: Width of the container
	// If not specified, container width would be resized according to the content
	Width int32 `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`
	// Eg: Figma SS: https://drive.google.com/file/d/1xVYsSMWqQiypYcymcLk0TxJbkl7wYR4I/view?usp=sharing
	LeftPadding int32 `protobuf:"varint,5,opt,name=left_padding,json=leftPadding,proto3" json:"left_padding,omitempty"`
	// Eg: Figma SS: https://drive.google.com/file/d/1-PlWgcwRSwqCCPDgYkp1J4qKDxgF738A/view?usp=sharing
	RightPadding int32 `protobuf:"varint,6,opt,name=right_padding,json=rightPadding,proto3" json:"right_padding,omitempty"`
	// Eg: Figma SS: https://drive.google.com/file/d/1N850dgvI74kDYiJXdf4MPa33KeLcJ_G3/view?usp=sharing
	TopPadding int32 `protobuf:"varint,7,opt,name=top_padding,json=topPadding,proto3" json:"top_padding,omitempty"`
	// Eg: Figma SS: https://drive.google.com/file/d/1N850dgvI74kDYiJXdf4MPa33KeLcJ_G3/view?usp=sharing
	BottomPadding int32 `protobuf:"varint,8,opt,name=bottom_padding,json=bottomPadding,proto3" json:"bottom_padding,omitempty"`
	// border color of the container: Eg Figma SS: https://drive.google.com/file/d/1eO9KqLzdlg6bXvgIinyBFC8KOH9uo-3M/view?usp=share_link
	//
	// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
	BorderColor string `protobuf:"bytes,9,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
	// border width of the container: Eg Figma SS: https://drive.google.com/file/d/1eO9KqLzdlg6bXvgIinyBFC8KOH9uo-3M/view?usp=share_link
	BorderWidth int32 `protobuf:"varint,10,opt,name=border_width,json=borderWidth,proto3" json:"border_width,omitempty"`
	// Background color for the itc widget
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,11,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// Border color for the itc widget
	BorderColour *widget.BackgroundColour `protobuf:"bytes,12,opt,name=border_colour,json=borderColour,proto3" json:"border_colour,omitempty"`
}

func (x *VerticalIconTextComponent_ContainerProperties) Reset() {
	*x = VerticalIconTextComponent_ContainerProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_icon_text_component_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerticalIconTextComponent_ContainerProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerticalIconTextComponent_ContainerProperties) ProtoMessage() {}

func (x *VerticalIconTextComponent_ContainerProperties) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_icon_text_component_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerticalIconTextComponent_ContainerProperties.ProtoReflect.Descriptor instead.
func (*VerticalIconTextComponent_ContainerProperties) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_icon_text_component_proto_rawDescGZIP(), []int{1, 0}
}

// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
func (x *VerticalIconTextComponent_ContainerProperties) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *VerticalIconTextComponent_ContainerProperties) GetCornerRadius() int32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *VerticalIconTextComponent_ContainerProperties) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *VerticalIconTextComponent_ContainerProperties) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *VerticalIconTextComponent_ContainerProperties) GetLeftPadding() int32 {
	if x != nil {
		return x.LeftPadding
	}
	return 0
}

func (x *VerticalIconTextComponent_ContainerProperties) GetRightPadding() int32 {
	if x != nil {
		return x.RightPadding
	}
	return 0
}

func (x *VerticalIconTextComponent_ContainerProperties) GetTopPadding() int32 {
	if x != nil {
		return x.TopPadding
	}
	return 0
}

func (x *VerticalIconTextComponent_ContainerProperties) GetBottomPadding() int32 {
	if x != nil {
		return x.BottomPadding
	}
	return 0
}

// Deprecated: Marked as deprecated in api/typesv2/ui/icon_text_component.proto.
func (x *VerticalIconTextComponent_ContainerProperties) GetBorderColor() string {
	if x != nil {
		return x.BorderColor
	}
	return ""
}

func (x *VerticalIconTextComponent_ContainerProperties) GetBorderWidth() int32 {
	if x != nil {
		return x.BorderWidth
	}
	return 0
}

func (x *VerticalIconTextComponent_ContainerProperties) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *VerticalIconTextComponent_ContainerProperties) GetBorderColour() *widget.BackgroundColour {
	if x != nil {
		return x.BorderColour
	}
	return nil
}

var File_api_typesv2_ui_icon_text_component_proto protoreflect.FileDescriptor

var file_api_typesv2_ui_icon_text_component_proto_rawDesc = []byte{
	0x0a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcf, 0x09, 0x0a, 0x11, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x12, 0x3a, 0x0a, 0x09, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x05,
	0x74, 0x65, 0x78, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x65, 0x78, 0x74, 0x73, 0x12, 0x3c, 0x0a, 0x0a,
	0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x09, 0x72, 0x69, 0x67, 0x68, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x14, 0x6c, 0x65,
	0x66, 0x74, 0x5f, 0x69, 0x6d, 0x67, 0x5f, 0x74, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6c, 0x65, 0x66, 0x74, 0x49, 0x6d,
	0x67, 0x54, 0x78, 0x74, 0x50, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x31, 0x0a, 0x15, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x6d, 0x67, 0x5f, 0x74, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x72, 0x69, 0x67, 0x68,
	0x74, 0x49, 0x6d, 0x67, 0x54, 0x78, 0x74, 0x50, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x37,
	0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x68, 0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x13, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x12, 0x51, 0x0a, 0x13, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x11, 0x6c, 0x65, 0x66, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x53, 0x0a, 0x14, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x76, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x12, 0x72, 0x69, 0x67, 0x68, 0x74, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0xe0, 0x04, 0x0a, 0x13, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x12, 0x1d, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52,
	0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x70, 0x61, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6c, 0x65, 0x66, 0x74, 0x50,
	0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f,
	0x70, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x50, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x6f, 0x70, 0x5f, 0x70, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x74, 0x6f, 0x70, 0x50, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e,
	0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x70, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x50, 0x61, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x62,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x48, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e,
	0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x12, 0x58, 0x0a, 0x10, 0x62, 0x67, 0x5f, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0e, 0x62, 0x67,
	0x42, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x22, 0x92, 0x09, 0x0a,
	0x19, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x08, 0x74, 0x6f,
	0x70, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x74, 0x6f, 0x70,
	0x49, 0x63, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x13, 0x74, 0x6f, 0x70, 0x5f, 0x69, 0x6d, 0x67, 0x5f,
	0x74, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x10, 0x74, 0x6f, 0x70, 0x49, 0x6d, 0x67, 0x54, 0x78, 0x74, 0x50, 0x61, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x3e, 0x0a, 0x0b, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x69, 0x63,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x49,
	0x63, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x16, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x69, 0x6d,
	0x67, 0x5f, 0x74, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x13, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x49, 0x6d, 0x67, 0x54, 0x78,
	0x74, 0x50, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x65, 0x78, 0x74,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x05, 0x74, 0x65, 0x78, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x12, 0x70, 0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x13,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x4f, 0x0a, 0x12, 0x74, 0x6f, 0x70, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x10, 0x74, 0x6f, 0x70, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x55, 0x0a, 0x15, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x13, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x93, 0x04, 0x0a, 0x13,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64,
	0x69, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65,
	0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x70, 0x61,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6c, 0x65, 0x66,
	0x74, 0x50, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x69, 0x67, 0x68,
	0x74, 0x5f, 0x70, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x72, 0x69, 0x67, 0x68, 0x74, 0x50, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x6f, 0x70, 0x5f, 0x70, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x70, 0x50, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x25,
	0x0a, 0x0e, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x70, 0x61, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x50, 0x61,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x21, 0x0a, 0x0c,
	0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12,
	0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x53, 0x0a, 0x0d,
	0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c,
	0x6f, 0x75, 0x72, 0x52, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75,
	0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_ui_icon_text_component_proto_rawDescOnce sync.Once
	file_api_typesv2_ui_icon_text_component_proto_rawDescData = file_api_typesv2_ui_icon_text_component_proto_rawDesc
)

func file_api_typesv2_ui_icon_text_component_proto_rawDescGZIP() []byte {
	file_api_typesv2_ui_icon_text_component_proto_rawDescOnce.Do(func() {
		file_api_typesv2_ui_icon_text_component_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_ui_icon_text_component_proto_rawDescData)
	})
	return file_api_typesv2_ui_icon_text_component_proto_rawDescData
}

var file_api_typesv2_ui_icon_text_component_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_typesv2_ui_icon_text_component_proto_goTypes = []interface{}{
	(*IconTextComponent)(nil),                             // 0: api.typesv2.ui.IconTextComponent
	(*VerticalIconTextComponent)(nil),                     // 1: api.typesv2.ui.VerticalIconTextComponent
	(*IconTextComponent_ContainerProperties)(nil),         // 2: api.typesv2.ui.IconTextComponent.ContainerProperties
	(*VerticalIconTextComponent_ContainerProperties)(nil), // 3: api.typesv2.ui.VerticalIconTextComponent.ContainerProperties
	(*common.Image)(nil),                                  // 4: api.typesv2.common.Image
	(*common.Text)(nil),                                   // 5: api.typesv2.common.Text
	(*deeplink.Deeplink)(nil),                             // 6: frontend.deeplink.Deeplink
	(*common.VisualElement)(nil),                          // 7: api.typesv2.common.VisualElement
	(*widget.BackgroundColour)(nil),                       // 8: api.typesv2.common.ui.widget.BackgroundColour
}
var file_api_typesv2_ui_icon_text_component_proto_depIdxs = []int32{
	4,  // 0: api.typesv2.ui.IconTextComponent.left_icon:type_name -> api.typesv2.common.Image
	5,  // 1: api.typesv2.ui.IconTextComponent.texts:type_name -> api.typesv2.common.Text
	4,  // 2: api.typesv2.ui.IconTextComponent.right_icon:type_name -> api.typesv2.common.Image
	6,  // 3: api.typesv2.ui.IconTextComponent.deeplink:type_name -> frontend.deeplink.Deeplink
	2,  // 4: api.typesv2.ui.IconTextComponent.container_properties:type_name -> api.typesv2.ui.IconTextComponent.ContainerProperties
	7,  // 5: api.typesv2.ui.IconTextComponent.left_visual_element:type_name -> api.typesv2.common.VisualElement
	7,  // 6: api.typesv2.ui.IconTextComponent.right_visual_element:type_name -> api.typesv2.common.VisualElement
	4,  // 7: api.typesv2.ui.VerticalIconTextComponent.top_icon:type_name -> api.typesv2.common.Image
	4,  // 8: api.typesv2.ui.VerticalIconTextComponent.bottom_icon:type_name -> api.typesv2.common.Image
	5,  // 9: api.typesv2.ui.VerticalIconTextComponent.texts:type_name -> api.typesv2.common.Text
	6,  // 10: api.typesv2.ui.VerticalIconTextComponent.deeplink:type_name -> frontend.deeplink.Deeplink
	3,  // 11: api.typesv2.ui.VerticalIconTextComponent.container_properties:type_name -> api.typesv2.ui.VerticalIconTextComponent.ContainerProperties
	7,  // 12: api.typesv2.ui.VerticalIconTextComponent.top_visual_element:type_name -> api.typesv2.common.VisualElement
	7,  // 13: api.typesv2.ui.VerticalIconTextComponent.bottom_visual_element:type_name -> api.typesv2.common.VisualElement
	8,  // 14: api.typesv2.ui.IconTextComponent.ContainerProperties.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	8,  // 15: api.typesv2.ui.IconTextComponent.ContainerProperties.bg_border_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	8,  // 16: api.typesv2.ui.VerticalIconTextComponent.ContainerProperties.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	8,  // 17: api.typesv2.ui.VerticalIconTextComponent.ContainerProperties.border_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_api_typesv2_ui_icon_text_component_proto_init() }
func file_api_typesv2_ui_icon_text_component_proto_init() {
	if File_api_typesv2_ui_icon_text_component_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_ui_icon_text_component_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IconTextComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_icon_text_component_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerticalIconTextComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_icon_text_component_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IconTextComponent_ContainerProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_icon_text_component_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerticalIconTextComponent_ContainerProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_ui_icon_text_component_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_ui_icon_text_component_proto_goTypes,
		DependencyIndexes: file_api_typesv2_ui_icon_text_component_proto_depIdxs,
		MessageInfos:      file_api_typesv2_ui_icon_text_component_proto_msgTypes,
	}.Build()
	File_api_typesv2_ui_icon_text_component_proto = out.File
	file_api_typesv2_ui_icon_text_component_proto_rawDesc = nil
	file_api_typesv2_ui_icon_text_component_proto_goTypes = nil
	file_api_typesv2_ui_icon_text_component_proto_depIdxs = nil
}
