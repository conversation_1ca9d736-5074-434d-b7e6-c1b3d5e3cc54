// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/ui/icon_text_component.proto

package ui

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on IconTextComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *IconTextComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IconTextComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IconTextComponentMultiError, or nil if none found.
func (m *IconTextComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *IconTextComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeftIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IconTextComponentValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IconTextComponentValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IconTextComponentValidationError{
				field:  "LeftIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, IconTextComponentValidationError{
						field:  fmt.Sprintf("Texts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, IconTextComponentValidationError{
						field:  fmt.Sprintf("Texts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return IconTextComponentValidationError{
					field:  fmt.Sprintf("Texts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRightIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IconTextComponentValidationError{
					field:  "RightIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IconTextComponentValidationError{
					field:  "RightIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IconTextComponentValidationError{
				field:  "RightIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LeftImgTxtPadding

	// no validation rules for RightImgTxtPadding

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IconTextComponentValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IconTextComponentValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IconTextComponentValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContainerProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IconTextComponentValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IconTextComponentValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContainerProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IconTextComponentValidationError{
				field:  "ContainerProperties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLeftVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IconTextComponentValidationError{
					field:  "LeftVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IconTextComponentValidationError{
					field:  "LeftVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IconTextComponentValidationError{
				field:  "LeftVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IconTextComponentValidationError{
					field:  "RightVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IconTextComponentValidationError{
					field:  "RightVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IconTextComponentValidationError{
				field:  "RightVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IconTextComponentMultiError(errors)
	}

	return nil
}

// IconTextComponentMultiError is an error wrapping multiple validation errors
// returned by IconTextComponent.ValidateAll() if the designated constraints
// aren't met.
type IconTextComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IconTextComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IconTextComponentMultiError) AllErrors() []error { return m }

// IconTextComponentValidationError is the validation error returned by
// IconTextComponent.Validate if the designated constraints aren't met.
type IconTextComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IconTextComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IconTextComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IconTextComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IconTextComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IconTextComponentValidationError) ErrorName() string {
	return "IconTextComponentValidationError"
}

// Error satisfies the builtin error interface
func (e IconTextComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIconTextComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IconTextComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IconTextComponentValidationError{}

// Validate checks the field values on VerticalIconTextComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerticalIconTextComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerticalIconTextComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerticalIconTextComponentMultiError, or nil if none found.
func (m *VerticalIconTextComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *VerticalIconTextComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTopIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerticalIconTextComponentValidationError{
					field:  "TopIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerticalIconTextComponentValidationError{
					field:  "TopIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerticalIconTextComponentValidationError{
				field:  "TopIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TopImgTxtPadding

	if all {
		switch v := interface{}(m.GetBottomIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerticalIconTextComponentValidationError{
					field:  "BottomIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerticalIconTextComponentValidationError{
					field:  "BottomIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerticalIconTextComponentValidationError{
				field:  "BottomIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BottomImgTxtPadding

	for idx, item := range m.GetTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VerticalIconTextComponentValidationError{
						field:  fmt.Sprintf("Texts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VerticalIconTextComponentValidationError{
						field:  fmt.Sprintf("Texts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VerticalIconTextComponentValidationError{
					field:  fmt.Sprintf("Texts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerticalIconTextComponentValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerticalIconTextComponentValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerticalIconTextComponentValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContainerProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerticalIconTextComponentValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerticalIconTextComponentValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContainerProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerticalIconTextComponentValidationError{
				field:  "ContainerProperties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTopVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerticalIconTextComponentValidationError{
					field:  "TopVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerticalIconTextComponentValidationError{
					field:  "TopVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerticalIconTextComponentValidationError{
				field:  "TopVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerticalIconTextComponentValidationError{
					field:  "BottomVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerticalIconTextComponentValidationError{
					field:  "BottomVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerticalIconTextComponentValidationError{
				field:  "BottomVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerticalIconTextComponentMultiError(errors)
	}

	return nil
}

// VerticalIconTextComponentMultiError is an error wrapping multiple validation
// errors returned by VerticalIconTextComponent.ValidateAll() if the
// designated constraints aren't met.
type VerticalIconTextComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerticalIconTextComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerticalIconTextComponentMultiError) AllErrors() []error { return m }

// VerticalIconTextComponentValidationError is the validation error returned by
// VerticalIconTextComponent.Validate if the designated constraints aren't met.
type VerticalIconTextComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerticalIconTextComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerticalIconTextComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerticalIconTextComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerticalIconTextComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerticalIconTextComponentValidationError) ErrorName() string {
	return "VerticalIconTextComponentValidationError"
}

// Error satisfies the builtin error interface
func (e VerticalIconTextComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerticalIconTextComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerticalIconTextComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerticalIconTextComponentValidationError{}

// Validate checks the field values on IconTextComponent_ContainerProperties
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *IconTextComponent_ContainerProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IconTextComponent_ContainerProperties
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// IconTextComponent_ContainerPropertiesMultiError, or nil if none found.
func (m *IconTextComponent_ContainerProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *IconTextComponent_ContainerProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BgColor

	// no validation rules for CornerRadius

	// no validation rules for Height

	// no validation rules for Width

	// no validation rules for LeftPadding

	// no validation rules for RightPadding

	// no validation rules for TopPadding

	// no validation rules for BottomPadding

	// no validation rules for BorderColor

	// no validation rules for BorderWidth

	// no validation rules for ShadowHeight

	// no validation rules for ShadowColor

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IconTextComponent_ContainerPropertiesValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IconTextComponent_ContainerPropertiesValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IconTextComponent_ContainerPropertiesValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgBorderColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IconTextComponent_ContainerPropertiesValidationError{
					field:  "BgBorderColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IconTextComponent_ContainerPropertiesValidationError{
					field:  "BgBorderColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgBorderColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IconTextComponent_ContainerPropertiesValidationError{
				field:  "BgBorderColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IconTextComponent_ContainerPropertiesMultiError(errors)
	}

	return nil
}

// IconTextComponent_ContainerPropertiesMultiError is an error wrapping
// multiple validation errors returned by
// IconTextComponent_ContainerProperties.ValidateAll() if the designated
// constraints aren't met.
type IconTextComponent_ContainerPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IconTextComponent_ContainerPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IconTextComponent_ContainerPropertiesMultiError) AllErrors() []error { return m }

// IconTextComponent_ContainerPropertiesValidationError is the validation error
// returned by IconTextComponent_ContainerProperties.Validate if the
// designated constraints aren't met.
type IconTextComponent_ContainerPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IconTextComponent_ContainerPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IconTextComponent_ContainerPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IconTextComponent_ContainerPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IconTextComponent_ContainerPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IconTextComponent_ContainerPropertiesValidationError) ErrorName() string {
	return "IconTextComponent_ContainerPropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e IconTextComponent_ContainerPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIconTextComponent_ContainerProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IconTextComponent_ContainerPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IconTextComponent_ContainerPropertiesValidationError{}

// Validate checks the field values on
// VerticalIconTextComponent_ContainerProperties with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VerticalIconTextComponent_ContainerProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// VerticalIconTextComponent_ContainerProperties with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// VerticalIconTextComponent_ContainerPropertiesMultiError, or nil if none found.
func (m *VerticalIconTextComponent_ContainerProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *VerticalIconTextComponent_ContainerProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BgColor

	// no validation rules for CornerRadius

	// no validation rules for Height

	// no validation rules for Width

	// no validation rules for LeftPadding

	// no validation rules for RightPadding

	// no validation rules for TopPadding

	// no validation rules for BottomPadding

	// no validation rules for BorderColor

	// no validation rules for BorderWidth

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerticalIconTextComponent_ContainerPropertiesValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerticalIconTextComponent_ContainerPropertiesValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerticalIconTextComponent_ContainerPropertiesValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBorderColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerticalIconTextComponent_ContainerPropertiesValidationError{
					field:  "BorderColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerticalIconTextComponent_ContainerPropertiesValidationError{
					field:  "BorderColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerticalIconTextComponent_ContainerPropertiesValidationError{
				field:  "BorderColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerticalIconTextComponent_ContainerPropertiesMultiError(errors)
	}

	return nil
}

// VerticalIconTextComponent_ContainerPropertiesMultiError is an error wrapping
// multiple validation errors returned by
// VerticalIconTextComponent_ContainerProperties.ValidateAll() if the
// designated constraints aren't met.
type VerticalIconTextComponent_ContainerPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerticalIconTextComponent_ContainerPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerticalIconTextComponent_ContainerPropertiesMultiError) AllErrors() []error { return m }

// VerticalIconTextComponent_ContainerPropertiesValidationError is the
// validation error returned by
// VerticalIconTextComponent_ContainerProperties.Validate if the designated
// constraints aren't met.
type VerticalIconTextComponent_ContainerPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerticalIconTextComponent_ContainerPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerticalIconTextComponent_ContainerPropertiesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e VerticalIconTextComponent_ContainerPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerticalIconTextComponent_ContainerPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerticalIconTextComponent_ContainerPropertiesValidationError) ErrorName() string {
	return "VerticalIconTextComponent_ContainerPropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e VerticalIconTextComponent_ContainerPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerticalIconTextComponent_ContainerProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerticalIconTextComponent_ContainerPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerticalIconTextComponent_ContainerPropertiesValidationError{}
