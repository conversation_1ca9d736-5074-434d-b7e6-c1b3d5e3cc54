// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/salaryestimation/developer/service.proto

package developer

import (
	context "context"
	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DevSalaryEstimation_GetEntityList_FullMethodName    = "/salaryestimation.developer.DevSalaryEstimation/GetEntityList"
	DevSalaryEstimation_GetParameterList_FullMethodName = "/salaryestimation.developer.DevSalaryEstimation/GetParameterList"
	DevSalaryEstimation_GetData_FullMethodName          = "/salaryestimation.developer.DevSalaryEstimation/GetData"
)

// DevSalaryEstimationClient is the client API for DevSalaryEstimation service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DevSalaryEstimationClient interface {
	// service to fetch list of entities for which salary estimation can return the data
	GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error)
	// For each entity as defined above, the parameter required to fetch that data will be different
	// This service will return appropriate params based on entity passed in request
	GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error)
	// The actual get data API call where we will make a DB call to get the required data
	GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error)
}

type devSalaryEstimationClient struct {
	cc grpc.ClientConnInterface
}

func NewDevSalaryEstimationClient(cc grpc.ClientConnInterface) DevSalaryEstimationClient {
	return &devSalaryEstimationClient{cc}
}

func (c *devSalaryEstimationClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	out := new(db_state.GetEntityListResponse)
	err := c.cc.Invoke(ctx, DevSalaryEstimation_GetEntityList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devSalaryEstimationClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	out := new(db_state.GetParameterListResponse)
	err := c.cc.Invoke(ctx, DevSalaryEstimation_GetParameterList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devSalaryEstimationClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	out := new(db_state.GetDataResponse)
	err := c.cc.Invoke(ctx, DevSalaryEstimation_GetData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DevSalaryEstimationServer is the server API for DevSalaryEstimation service.
// All implementations should embed UnimplementedDevSalaryEstimationServer
// for forward compatibility
type DevSalaryEstimationServer interface {
	// service to fetch list of entities for which salary estimation can return the data
	GetEntityList(context.Context, *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error)
	// For each entity as defined above, the parameter required to fetch that data will be different
	// This service will return appropriate params based on entity passed in request
	GetParameterList(context.Context, *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error)
	// The actual get data API call where we will make a DB call to get the required data
	GetData(context.Context, *db_state.GetDataRequest) (*db_state.GetDataResponse, error)
}

// UnimplementedDevSalaryEstimationServer should be embedded to have forward compatible implementations.
type UnimplementedDevSalaryEstimationServer struct {
}

func (UnimplementedDevSalaryEstimationServer) GetEntityList(context.Context, *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntityList not implemented")
}
func (UnimplementedDevSalaryEstimationServer) GetParameterList(context.Context, *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetParameterList not implemented")
}
func (UnimplementedDevSalaryEstimationServer) GetData(context.Context, *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetData not implemented")
}

// UnsafeDevSalaryEstimationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DevSalaryEstimationServer will
// result in compilation errors.
type UnsafeDevSalaryEstimationServer interface {
	mustEmbedUnimplementedDevSalaryEstimationServer()
}

func RegisterDevSalaryEstimationServer(s grpc.ServiceRegistrar, srv DevSalaryEstimationServer) {
	s.RegisterService(&DevSalaryEstimation_ServiceDesc, srv)
}

func _DevSalaryEstimation_GetEntityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetEntityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevSalaryEstimationServer).GetEntityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevSalaryEstimation_GetEntityList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevSalaryEstimationServer).GetEntityList(ctx, req.(*db_state.GetEntityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DevSalaryEstimation_GetParameterList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetParameterListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevSalaryEstimationServer).GetParameterList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevSalaryEstimation_GetParameterList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevSalaryEstimationServer).GetParameterList(ctx, req.(*db_state.GetParameterListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DevSalaryEstimation_GetData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevSalaryEstimationServer).GetData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DevSalaryEstimation_GetData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevSalaryEstimationServer).GetData(ctx, req.(*db_state.GetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DevSalaryEstimation_ServiceDesc is the grpc.ServiceDesc for DevSalaryEstimation service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DevSalaryEstimation_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "salaryestimation.developer.DevSalaryEstimation",
	HandlerType: (*DevSalaryEstimationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEntityList",
			Handler:    _DevSalaryEstimation_GetEntityList_Handler,
		},
		{
			MethodName: "GetParameterList",
			Handler:    _DevSalaryEstimation_GetParameterList_Handler,
		},
		{
			MethodName: "GetData",
			Handler:    _DevSalaryEstimation_GetData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/salaryestimation/developer/service.proto",
}
