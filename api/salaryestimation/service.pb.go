// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/salaryestimation/service.proto

package salaryestimation

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	salaryestimation "github.com/epifi/gamma/api/typesv2/salaryestimation"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ComputeSalaryRequest_ExecutionType int32

const (
	ComputeSalaryRequest_UNSPECIFIED ComputeSalaryRequest_ExecutionType = 0
	ComputeSalaryRequest_STATELESS   ComputeSalaryRequest_ExecutionType = 1
	ComputeSalaryRequest_STATEFUL    ComputeSalaryRequest_ExecutionType = 2
)

// Enum value maps for ComputeSalaryRequest_ExecutionType.
var (
	ComputeSalaryRequest_ExecutionType_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "STATELESS",
		2: "STATEFUL",
	}
	ComputeSalaryRequest_ExecutionType_value = map[string]int32{
		"UNSPECIFIED": 0,
		"STATELESS":   1,
		"STATEFUL":    2,
	}
)

func (x ComputeSalaryRequest_ExecutionType) Enum() *ComputeSalaryRequest_ExecutionType {
	p := new(ComputeSalaryRequest_ExecutionType)
	*p = x
	return p
}

func (x ComputeSalaryRequest_ExecutionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ComputeSalaryRequest_ExecutionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryestimation_service_proto_enumTypes[0].Descriptor()
}

func (ComputeSalaryRequest_ExecutionType) Type() protoreflect.EnumType {
	return &file_api_salaryestimation_service_proto_enumTypes[0]
}

func (x ComputeSalaryRequest_ExecutionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ComputeSalaryRequest_ExecutionType.Descriptor instead.
func (ComputeSalaryRequest_ExecutionType) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryestimation_service_proto_rawDescGZIP(), []int{4, 0}
}

type GetSalaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetSalaryRequest) Reset() {
	*x = GetSalaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSalaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSalaryRequest) ProtoMessage() {}

func (x *GetSalaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSalaryRequest.ProtoReflect.Descriptor instead.
func (*GetSalaryRequest) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetSalaryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetSalaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status              *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Salary              *Salary     `protobuf:"bytes,2,opt,name=salary,proto3" json:"salary,omitempty"`
	L1AnalysisSignedUrl string      `protobuf:"bytes,3,opt,name=l1_analysis_signed_url,json=l1AnalysisSignedUrl,proto3" json:"l1_analysis_signed_url,omitempty"`
}

func (x *GetSalaryResponse) Reset() {
	*x = GetSalaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSalaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSalaryResponse) ProtoMessage() {}

func (x *GetSalaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSalaryResponse.ProtoReflect.Descriptor instead.
func (*GetSalaryResponse) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetSalaryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSalaryResponse) GetSalary() *Salary {
	if x != nil {
		return x.Salary
	}
	return nil
}

func (x *GetSalaryResponse) GetL1AnalysisSignedUrl() string {
	if x != nil {
		return x.L1AnalysisSignedUrl
	}
	return ""
}

type Salary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source        salaryestimation.Source `protobuf:"varint,1,opt,name=source,proto3,enum=api.typesv2.salaryestimation.Source" json:"source,omitempty"`
	SalaryAccount *SalaryAccount          `protobuf:"bytes,2,opt,name=salary_account,json=salaryAccount,proto3" json:"salary_account,omitempty"`
	ComputedAt    *timestamppb.Timestamp  `protobuf:"bytes,3,opt,name=computed_at,json=computedAt,proto3" json:"computed_at,omitempty"`
}

func (x *Salary) Reset() {
	*x = Salary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Salary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Salary) ProtoMessage() {}

func (x *Salary) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Salary.ProtoReflect.Descriptor instead.
func (*Salary) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_service_proto_rawDescGZIP(), []int{2}
}

func (x *Salary) GetSource() salaryestimation.Source {
	if x != nil {
		return x.Source
	}
	return salaryestimation.Source(0)
}

func (x *Salary) GetSalaryAccount() *SalaryAccount {
	if x != nil {
		return x.SalaryAccount
	}
	return nil
}

func (x *Salary) GetComputedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ComputedAt
	}
	return nil
}

type SalaryAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *SalaryAccount) Reset() {
	*x = SalaryAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SalaryAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SalaryAccount) ProtoMessage() {}

func (x *SalaryAccount) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SalaryAccount.ProtoReflect.Descriptor instead.
func (*SalaryAccount) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_service_proto_rawDescGZIP(), []int{3}
}

func (x *SalaryAccount) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type ComputeSalaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Client is used to determine the service to notify or callback on exit after salary estimation
	Client Client `protobuf:"varint,2,opt,name=client,proto3,enum=api.salaryestimation.Client" json:"client,omitempty"`
	// Client request ID is a unique identifier provided by the client for a unique request.
	// It is used to identify the data shared and the analysis attempt made against the client's request.
	ClientReqId          string                             `protobuf:"bytes,3,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	Source               salaryestimation.Source            `protobuf:"varint,4,opt,name=source,proto3,enum=api.typesv2.salaryestimation.Source" json:"source,omitempty"`
	SourceFlowParams     *salaryestimation.SourceFlowParams `protobuf:"bytes,5,opt,name=source_flow_params,json=sourceFlowParams,proto3" json:"source_flow_params,omitempty"`
	RequireHoldingScreen bool                               `protobuf:"varint,6,opt,name=require_holding_screen,json=requireHoldingScreen,proto3" json:"require_holding_screen,omitempty"`
	// user's employment type details as sent by client
	EmploymentType typesv2.EmploymentType `protobuf:"varint,7,opt,name=employment_type,json=employmentType,proto3,enum=api.typesv2.EmploymentType" json:"employment_type,omitempty"` // optional field
	// Name of user's organisation / employer
	OrganisationName string `protobuf:"bytes,8,opt,name=organisation_name,json=organisationName,proto3" json:"organisation_name,omitempty"` // optional field
	// (optional param) If sent as STATEFUL, will use salary estimation ComputeSalary method with stateful execution, else will be stateless.
	// if unspecified, use whatever is default at service
	ExecutionType ComputeSalaryRequest_ExecutionType `protobuf:"varint,9,opt,name=execution_type,json=executionType,proto3,enum=api.salaryestimation.ComputeSalaryRequest_ExecutionType" json:"execution_type,omitempty"`
}

func (x *ComputeSalaryRequest) Reset() {
	*x = ComputeSalaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComputeSalaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComputeSalaryRequest) ProtoMessage() {}

func (x *ComputeSalaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComputeSalaryRequest.ProtoReflect.Descriptor instead.
func (*ComputeSalaryRequest) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_service_proto_rawDescGZIP(), []int{4}
}

func (x *ComputeSalaryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ComputeSalaryRequest) GetClient() Client {
	if x != nil {
		return x.Client
	}
	return Client_CLIENT_UNSPECIFIED
}

func (x *ComputeSalaryRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *ComputeSalaryRequest) GetSource() salaryestimation.Source {
	if x != nil {
		return x.Source
	}
	return salaryestimation.Source(0)
}

func (x *ComputeSalaryRequest) GetSourceFlowParams() *salaryestimation.SourceFlowParams {
	if x != nil {
		return x.SourceFlowParams
	}
	return nil
}

func (x *ComputeSalaryRequest) GetRequireHoldingScreen() bool {
	if x != nil {
		return x.RequireHoldingScreen
	}
	return false
}

func (x *ComputeSalaryRequest) GetEmploymentType() typesv2.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return typesv2.EmploymentType(0)
}

func (x *ComputeSalaryRequest) GetOrganisationName() string {
	if x != nil {
		return x.OrganisationName
	}
	return ""
}

func (x *ComputeSalaryRequest) GetExecutionType() ComputeSalaryRequest_ExecutionType {
	if x != nil {
		return x.ExecutionType
	}
	return ComputeSalaryRequest_UNSPECIFIED
}

type ComputeSalaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction    *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	AttemptStatus AttemptStatus      `protobuf:"varint,3,opt,name=attempt_status,json=attemptStatus,proto3,enum=api.salaryestimation.AttemptStatus" json:"attempt_status,omitempty"`
}

func (x *ComputeSalaryResponse) Reset() {
	*x = ComputeSalaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComputeSalaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComputeSalaryResponse) ProtoMessage() {}

func (x *ComputeSalaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComputeSalaryResponse.ProtoReflect.Descriptor instead.
func (*ComputeSalaryResponse) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_service_proto_rawDescGZIP(), []int{5}
}

func (x *ComputeSalaryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ComputeSalaryResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *ComputeSalaryResponse) GetAttemptStatus() AttemptStatus {
	if x != nil {
		return x.AttemptStatus
	}
	return AttemptStatus_ATTEMPT_STATUS_UNSPECIFIED
}

type CancelAttemptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId     string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *CancelAttemptRequest) Reset() {
	*x = CancelAttemptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelAttemptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelAttemptRequest) ProtoMessage() {}

func (x *CancelAttemptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelAttemptRequest.ProtoReflect.Descriptor instead.
func (*CancelAttemptRequest) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_service_proto_rawDescGZIP(), []int{6}
}

func (x *CancelAttemptRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CancelAttemptRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type CancelAttemptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *CancelAttemptResponse) Reset() {
	*x = CancelAttemptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelAttemptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelAttemptResponse) ProtoMessage() {}

func (x *CancelAttemptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelAttemptResponse.ProtoReflect.Descriptor instead.
func (*CancelAttemptResponse) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_service_proto_rawDescGZIP(), []int{7}
}

func (x *CancelAttemptResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CancelAttemptResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

var File_api_salaryestimation_service_proto protoreflect.FileDescriptor

var file_api_salaryestimation_service_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79,
	0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65,
	0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x36, 0x0a, 0x10,
	0x47, 0x65, 0x74, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x22, 0xa3, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x34, 0x0a, 0x06, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x06, 0x73,
	0x61, 0x6c, 0x61, 0x72, 0x79, 0x12, 0x33, 0x0a, 0x16, 0x6c, 0x31, 0x5f, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x69, 0x73, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6c, 0x31, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69,
	0x73, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x22, 0xcf, 0x01, 0x0a, 0x06, 0x53,
	0x61, 0x6c, 0x61, 0x72, 0x79, 0x12, 0x3c, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x4a, 0x0a, 0x0e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x0d, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x3b, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x2e, 0x0a, 0x0d,
	0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x82, 0x05, 0x0a,
	0x14, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x06, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12,
	0x2b, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72,
	0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x5c, 0x0a, 0x12, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x6c, 0x6f, 0x77,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x10, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x6c,
	0x6f, 0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x44,
	0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x5f, 0x0a, 0x0e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0d, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x3d, 0x0a, 0x0d, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4c, 0x45, 0x53,
	0x53, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x54, 0x41, 0x54, 0x45, 0x46, 0x55, 0x4c, 0x10,
	0x02, 0x22, 0xc6, 0x01, 0x0a, 0x15, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x53, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a,
	0x0a, 0x0e, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x61, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x67, 0x0a, 0x14, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x49, 0x64, 0x22, 0x7a, 0x0a, 0x15, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x32,
	0xc4, 0x02, 0x0a, 0x10, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5c, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x53, 0x61, 0x6c, 0x61, 0x72,
	0x79, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x68, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x53, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79,
	0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x75,
	0x74, 0x65, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x53, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x0d,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_salaryestimation_service_proto_rawDescOnce sync.Once
	file_api_salaryestimation_service_proto_rawDescData = file_api_salaryestimation_service_proto_rawDesc
)

func file_api_salaryestimation_service_proto_rawDescGZIP() []byte {
	file_api_salaryestimation_service_proto_rawDescOnce.Do(func() {
		file_api_salaryestimation_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_salaryestimation_service_proto_rawDescData)
	})
	return file_api_salaryestimation_service_proto_rawDescData
}

var file_api_salaryestimation_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_salaryestimation_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_salaryestimation_service_proto_goTypes = []interface{}{
	(ComputeSalaryRequest_ExecutionType)(0),   // 0: api.salaryestimation.ComputeSalaryRequest.ExecutionType
	(*GetSalaryRequest)(nil),                  // 1: api.salaryestimation.GetSalaryRequest
	(*GetSalaryResponse)(nil),                 // 2: api.salaryestimation.GetSalaryResponse
	(*Salary)(nil),                            // 3: api.salaryestimation.Salary
	(*SalaryAccount)(nil),                     // 4: api.salaryestimation.SalaryAccount
	(*ComputeSalaryRequest)(nil),              // 5: api.salaryestimation.ComputeSalaryRequest
	(*ComputeSalaryResponse)(nil),             // 6: api.salaryestimation.ComputeSalaryResponse
	(*CancelAttemptRequest)(nil),              // 7: api.salaryestimation.CancelAttemptRequest
	(*CancelAttemptResponse)(nil),             // 8: api.salaryestimation.CancelAttemptResponse
	(*rpc.Status)(nil),                        // 9: rpc.Status
	(salaryestimation.Source)(0),              // 10: api.typesv2.salaryestimation.Source
	(*timestamppb.Timestamp)(nil),             // 11: google.protobuf.Timestamp
	(Client)(0),                               // 12: api.salaryestimation.Client
	(*salaryestimation.SourceFlowParams)(nil), // 13: api.typesv2.salaryestimation.SourceFlowParams
	(typesv2.EmploymentType)(0),               // 14: api.typesv2.EmploymentType
	(*deeplink.Deeplink)(nil),                 // 15: frontend.deeplink.Deeplink
	(AttemptStatus)(0),                        // 16: api.salaryestimation.AttemptStatus
}
var file_api_salaryestimation_service_proto_depIdxs = []int32{
	9,  // 0: api.salaryestimation.GetSalaryResponse.status:type_name -> rpc.Status
	3,  // 1: api.salaryestimation.GetSalaryResponse.salary:type_name -> api.salaryestimation.Salary
	10, // 2: api.salaryestimation.Salary.source:type_name -> api.typesv2.salaryestimation.Source
	4,  // 3: api.salaryestimation.Salary.salary_account:type_name -> api.salaryestimation.SalaryAccount
	11, // 4: api.salaryestimation.Salary.computed_at:type_name -> google.protobuf.Timestamp
	12, // 5: api.salaryestimation.ComputeSalaryRequest.client:type_name -> api.salaryestimation.Client
	10, // 6: api.salaryestimation.ComputeSalaryRequest.source:type_name -> api.typesv2.salaryestimation.Source
	13, // 7: api.salaryestimation.ComputeSalaryRequest.source_flow_params:type_name -> api.typesv2.salaryestimation.SourceFlowParams
	14, // 8: api.salaryestimation.ComputeSalaryRequest.employment_type:type_name -> api.typesv2.EmploymentType
	0,  // 9: api.salaryestimation.ComputeSalaryRequest.execution_type:type_name -> api.salaryestimation.ComputeSalaryRequest.ExecutionType
	9,  // 10: api.salaryestimation.ComputeSalaryResponse.status:type_name -> rpc.Status
	15, // 11: api.salaryestimation.ComputeSalaryResponse.next_action:type_name -> frontend.deeplink.Deeplink
	16, // 12: api.salaryestimation.ComputeSalaryResponse.attempt_status:type_name -> api.salaryestimation.AttemptStatus
	9,  // 13: api.salaryestimation.CancelAttemptResponse.status:type_name -> rpc.Status
	15, // 14: api.salaryestimation.CancelAttemptResponse.next_action:type_name -> frontend.deeplink.Deeplink
	1,  // 15: api.salaryestimation.SalaryEstimation.GetSalary:input_type -> api.salaryestimation.GetSalaryRequest
	5,  // 16: api.salaryestimation.SalaryEstimation.ComputeSalary:input_type -> api.salaryestimation.ComputeSalaryRequest
	7,  // 17: api.salaryestimation.SalaryEstimation.CancelAttempt:input_type -> api.salaryestimation.CancelAttemptRequest
	2,  // 18: api.salaryestimation.SalaryEstimation.GetSalary:output_type -> api.salaryestimation.GetSalaryResponse
	6,  // 19: api.salaryestimation.SalaryEstimation.ComputeSalary:output_type -> api.salaryestimation.ComputeSalaryResponse
	8,  // 20: api.salaryestimation.SalaryEstimation.CancelAttempt:output_type -> api.salaryestimation.CancelAttemptResponse
	18, // [18:21] is the sub-list for method output_type
	15, // [15:18] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_api_salaryestimation_service_proto_init() }
func file_api_salaryestimation_service_proto_init() {
	if File_api_salaryestimation_service_proto != nil {
		return
	}
	file_api_salaryestimation_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_salaryestimation_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSalaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_salaryestimation_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSalaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_salaryestimation_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Salary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_salaryestimation_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SalaryAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_salaryestimation_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComputeSalaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_salaryestimation_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComputeSalaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_salaryestimation_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelAttemptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_salaryestimation_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelAttemptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_salaryestimation_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_salaryestimation_service_proto_goTypes,
		DependencyIndexes: file_api_salaryestimation_service_proto_depIdxs,
		EnumInfos:         file_api_salaryestimation_service_proto_enumTypes,
		MessageInfos:      file_api_salaryestimation_service_proto_msgTypes,
	}.Build()
	File_api_salaryestimation_service_proto = out.File
	file_api_salaryestimation_service_proto_rawDesc = nil
	file_api_salaryestimation_service_proto_goTypes = nil
	file_api_salaryestimation_service_proto_depIdxs = nil
}
