// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/inhousebre/service.proto

package inhousebre

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetFeaturesDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// features list for which data needs to be fetched from feature store
	// NOTE : feature store currently only supports fetching data for features which have same set of request identifiers
	// Example : Say a feature F1 has actorId and model name as request identifier, F2 has actorId as request identifier and F3 has actorId and
	// model name as request identifier, then in a single api call we can either support [F2] or [F1,F3].
	FeatureNameList []string `protobuf:"bytes,1,rep,name=feature_name_list,json=FeatureNameList,proto3" json:"feature_name_list,omitempty"`
	// request identifiers containing list of Identifiers for each request
	// we will evaluate the above feature list for each request identifier
	// Request Identifiers should have same set of list of identifiers for each request as mentioned above
	RequestIdentifiersList []*RequestIdentifiers `protobuf:"bytes,2,rep,name=request_identifiers_list,json=RequestIdentifiersList,proto3" json:"request_identifiers_list,omitempty"`
}

func (x *GetFeaturesDataRequest) Reset() {
	*x = GetFeaturesDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_inhousebre_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeaturesDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeaturesDataRequest) ProtoMessage() {}

func (x *GetFeaturesDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_inhousebre_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeaturesDataRequest.ProtoReflect.Descriptor instead.
func (*GetFeaturesDataRequest) Descriptor() ([]byte, []int) {
	return file_api_inhousebre_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetFeaturesDataRequest) GetFeatureNameList() []string {
	if x != nil {
		return x.FeatureNameList
	}
	return nil
}

func (x *GetFeaturesDataRequest) GetRequestIdentifiersList() []*RequestIdentifiers {
	if x != nil {
		return x.RequestIdentifiersList
	}
	return nil
}

type RequestIdentifiers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifiers for which data needs to be fetched
	// Currently the supported identifiers are actorId, accountId or Model Name and combination of the same
	Identifiers *Identifiers `protobuf:"bytes,2,opt,name=identifiers,json=Identifiers,proto3" json:"identifiers,omitempty"`
}

func (x *RequestIdentifiers) Reset() {
	*x = RequestIdentifiers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_inhousebre_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestIdentifiers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestIdentifiers) ProtoMessage() {}

func (x *RequestIdentifiers) ProtoReflect() protoreflect.Message {
	mi := &file_api_inhousebre_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestIdentifiers.ProtoReflect.Descriptor instead.
func (*RequestIdentifiers) Descriptor() ([]byte, []int) {
	return file_api_inhousebre_service_proto_rawDescGZIP(), []int{1}
}

func (x *RequestIdentifiers) GetIdentifiers() *Identifiers {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

type GetFeaturesDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of features data for identifiers sent in the request
	FeaturesResponseDataList []*FeaturesResponseData `protobuf:"bytes,1,rep,name=features_response_data_list,json=FeaturesResponseDataList,proto3" json:"features_response_data_list,omitempty"`
	// this status will define whether the experian report data is available(With history or Without history) or not
	ExperianReportDataAvailabilityStatus string `protobuf:"bytes,2,opt,name=experian_report_data_availability_status,json=experianReportDataAvailabilityStatus,proto3" json:"experian_report_data_availability_status,omitempty"`
}

func (x *GetFeaturesDataResponse) Reset() {
	*x = GetFeaturesDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_inhousebre_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeaturesDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeaturesDataResponse) ProtoMessage() {}

func (x *GetFeaturesDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_inhousebre_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeaturesDataResponse.ProtoReflect.Descriptor instead.
func (*GetFeaturesDataResponse) Descriptor() ([]byte, []int) {
	return file_api_inhousebre_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetFeaturesDataResponse) GetFeaturesResponseDataList() []*FeaturesResponseData {
	if x != nil {
		return x.FeaturesResponseDataList
	}
	return nil
}

func (x *GetFeaturesDataResponse) GetExperianReportDataAvailabilityStatus() string {
	if x != nil {
		return x.ExperianReportDataAvailabilityStatus
	}
	return ""
}

type FeaturesResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifiers sent in the request
	Identifiers *Identifiers `protobuf:"bytes,1,opt,name=identifiers,json=Identifiers,proto3" json:"identifiers,omitempty"`
	// List of features value for the identifier
	FeatureValueMap map[string]*structpb.Value `protobuf:"bytes,2,rep,name=feature_value_map,json=FeatureValueMap,proto3" json:"feature_value_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *FeaturesResponseData) Reset() {
	*x = FeaturesResponseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_inhousebre_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeaturesResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeaturesResponseData) ProtoMessage() {}

func (x *FeaturesResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_api_inhousebre_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeaturesResponseData.ProtoReflect.Descriptor instead.
func (*FeaturesResponseData) Descriptor() ([]byte, []int) {
	return file_api_inhousebre_service_proto_rawDescGZIP(), []int{3}
}

func (x *FeaturesResponseData) GetIdentifiers() *Identifiers {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

func (x *FeaturesResponseData) GetFeatureValueMap() map[string]*structpb.Value {
	if x != nil {
		return x.FeatureValueMap
	}
	return nil
}

// Identifiers for which data is fetched from feature store
// Based on the feature we can have any combination of these identifiers
type Identifiers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor Id
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=ActorId,proto3" json:"actor_id,omitempty"`
	// account Id
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=AccountId,proto3" json:"account_id,omitempty"`
	// Model name
	ModelName string `protobuf:"bytes,3,opt,name=model_name,json=ModelName,proto3" json:"model_name,omitempty"`
}

func (x *Identifiers) Reset() {
	*x = Identifiers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_inhousebre_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Identifiers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Identifiers) ProtoMessage() {}

func (x *Identifiers) ProtoReflect() protoreflect.Message {
	mi := &file_api_inhousebre_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Identifiers.ProtoReflect.Descriptor instead.
func (*Identifiers) Descriptor() ([]byte, []int) {
	return file_api_inhousebre_service_proto_rawDescGZIP(), []int{4}
}

func (x *Identifiers) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *Identifiers) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Identifiers) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

type GetEpfoDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=ActorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetEpfoDataRequest) Reset() {
	*x = GetEpfoDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_inhousebre_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEpfoDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEpfoDataRequest) ProtoMessage() {}

func (x *GetEpfoDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_inhousebre_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEpfoDataRequest.ProtoReflect.Descriptor instead.
func (*GetEpfoDataRequest) Descriptor() ([]byte, []int) {
	return file_api_inhousebre_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetEpfoDataRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetEpfoDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawEpfoData string `protobuf:"bytes,1,opt,name=raw_epfo_data,json=RawEpfoData,proto3" json:"raw_epfo_data,omitempty"`
}

func (x *GetEpfoDataResponse) Reset() {
	*x = GetEpfoDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_inhousebre_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEpfoDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEpfoDataResponse) ProtoMessage() {}

func (x *GetEpfoDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_inhousebre_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEpfoDataResponse.ProtoReflect.Descriptor instead.
func (*GetEpfoDataResponse) Descriptor() ([]byte, []int) {
	return file_api_inhousebre_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetEpfoDataResponse) GetRawEpfoData() string {
	if x != nil {
		return x.RawEpfoData
	}
	return ""
}

type GetCibilReportFeaturesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId       string   `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	FeaturesNames []string `protobuf:"bytes,2,rep,name=features_names,json=featureNames,proto3" json:"features_names,omitempty"`
}

func (x *GetCibilReportFeaturesRequest) Reset() {
	*x = GetCibilReportFeaturesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_inhousebre_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCibilReportFeaturesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCibilReportFeaturesRequest) ProtoMessage() {}

func (x *GetCibilReportFeaturesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_inhousebre_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCibilReportFeaturesRequest.ProtoReflect.Descriptor instead.
func (*GetCibilReportFeaturesRequest) Descriptor() ([]byte, []int) {
	return file_api_inhousebre_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetCibilReportFeaturesRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetCibilReportFeaturesRequest) GetFeaturesNames() []string {
	if x != nil {
		return x.FeaturesNames
	}
	return nil
}

type GetCibilReportFeaturesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FeatureValueMap map[string]*structpb.Value `protobuf:"bytes,1,rep,name=feature_value_map,json=featureValueMap,proto3" json:"feature_value_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// this status will define whether the cibil report data is available(With history or Without history) or not
	CibilReportDataAvailabilityStatus string `protobuf:"bytes,2,opt,name=cibil_report_data_availability_status,json=cibilReportDataAvailabilityStatus,proto3" json:"cibil_report_data_availability_status,omitempty"`
}

func (x *GetCibilReportFeaturesResponse) Reset() {
	*x = GetCibilReportFeaturesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_inhousebre_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCibilReportFeaturesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCibilReportFeaturesResponse) ProtoMessage() {}

func (x *GetCibilReportFeaturesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_inhousebre_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCibilReportFeaturesResponse.ProtoReflect.Descriptor instead.
func (*GetCibilReportFeaturesResponse) Descriptor() ([]byte, []int) {
	return file_api_inhousebre_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetCibilReportFeaturesResponse) GetFeatureValueMap() map[string]*structpb.Value {
	if x != nil {
		return x.FeatureValueMap
	}
	return nil
}

func (x *GetCibilReportFeaturesResponse) GetCibilReportDataAvailabilityStatus() string {
	if x != nil {
		return x.CibilReportDataAvailabilityStatus
	}
	return ""
}

type GetPdScoreRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Bureau  string `protobuf:"bytes,2,opt,name=bureau,proto3" json:"bureau,omitempty"` // accepts CIBIL and EXPERIAN strings
}

func (x *GetPdScoreRequest) Reset() {
	*x = GetPdScoreRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_inhousebre_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPdScoreRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPdScoreRequest) ProtoMessage() {}

func (x *GetPdScoreRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_inhousebre_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPdScoreRequest.ProtoReflect.Descriptor instead.
func (*GetPdScoreRequest) Descriptor() ([]byte, []int) {
	return file_api_inhousebre_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetPdScoreRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetPdScoreRequest) GetBureau() string {
	if x != nil {
		return x.Bureau
	}
	return ""
}

type GetPdScoreResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PdScore        float64 `protobuf:"fixed64,1,opt,name=pd_score,json=pdScore,proto3" json:"pd_score,omitempty"`
	PdScoreVersion string  `protobuf:"bytes,2,opt,name=pd_score_version,json=pdScoreVersion,proto3" json:"pd_score_version,omitempty"`
}

func (x *GetPdScoreResponse) Reset() {
	*x = GetPdScoreResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_inhousebre_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPdScoreResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPdScoreResponse) ProtoMessage() {}

func (x *GetPdScoreResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_inhousebre_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPdScoreResponse.ProtoReflect.Descriptor instead.
func (*GetPdScoreResponse) Descriptor() ([]byte, []int) {
	return file_api_inhousebre_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetPdScoreResponse) GetPdScore() float64 {
	if x != nil {
		return x.PdScore
	}
	return 0
}

func (x *GetPdScoreResponse) GetPdScoreVersion() string {
	if x != nil {
		return x.PdScoreVersion
	}
	return ""
}

var File_api_inhousebre_service_proto protoreflect.FileDescriptor

var file_api_inhousebre_service_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a,
	0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9e, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x58, 0x0a,
	0x18, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x52,
	0x16, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x4f, 0x0a, 0x12, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12, 0x39, 0x0a,
	0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x2e,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x52, 0x0b, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x22, 0xd2, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x1b, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x68, 0x6f,
	0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x18, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x56, 0x0a, 0x28, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x61,
	0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x24, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x61,
	0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x90, 0x02,
	0x0a, 0x14, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x6e,
	0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x73, 0x52, 0x0b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x73, 0x12, 0x61, 0x0a, 0x11, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x69,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0f, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x4d, 0x61, 0x70, 0x1a, 0x5a, 0x0a, 0x14, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x66, 0x0a, 0x0b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x2f, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x45,
	0x70, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x39, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x45, 0x70, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x22, 0x0a, 0x0d, 0x72, 0x61, 0x77, 0x5f, 0x65, 0x70, 0x66, 0x6f, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x52, 0x61, 0x77, 0x45, 0x70, 0x66, 0x6f,
	0x44, 0x61, 0x74, 0x61, 0x22, 0x60, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43, 0x69, 0x62, 0x69, 0x6c,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0xbb, 0x02, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x69,
	0x62, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6b, 0x0a, 0x11, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x69, 0x62, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70, 0x12, 0x50, 0x0a, 0x25, 0x63, 0x69, 0x62, 0x69, 0x6c, 0x5f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x21, 0x63, 0x69, 0x62, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x5a, 0x0a, 0x14, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x46, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x64, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x72, 0x65, 0x61, 0x75, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x72, 0x65, 0x61, 0x75, 0x22, 0x59, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x50, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x70, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x28, 0x0a,
	0x10, 0x70, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0x85, 0x04, 0x0a, 0x03, 0x42, 0x72, 0x65, 0x12,
	0x7f, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x22, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65,
	0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x23, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65,
	0x2f, 0x67, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x72, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x45, 0x70, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x1e, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x70, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1f, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x70, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x69, 0x6e,
	0x68, 0x6f, 0x75, 0x73, 0x65, 0x2f, 0x76, 0x30, 0x2f, 0x67, 0x65, 0x74, 0x45, 0x70, 0x66, 0x6f,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x9b, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x69, 0x62, 0x69,
	0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12,
	0x29, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x69, 0x62, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x69, 0x6e, 0x68,
	0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x69, 0x62, 0x69, 0x6c,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a, 0x01,
	0x2a, 0x22, 0x1f, 0x2f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2f, 0x67, 0x65, 0x74, 0x43,
	0x69, 0x62, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x73, 0x12, 0x6b, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x50, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x1d, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f, 0x69, 0x6e, 0x68,
	0x6f, 0x75, 0x73, 0x65, 0x2f, 0x67, 0x65, 0x74, 0x50, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x42,
	0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x72, 0x65, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_inhousebre_service_proto_rawDescOnce sync.Once
	file_api_inhousebre_service_proto_rawDescData = file_api_inhousebre_service_proto_rawDesc
)

func file_api_inhousebre_service_proto_rawDescGZIP() []byte {
	file_api_inhousebre_service_proto_rawDescOnce.Do(func() {
		file_api_inhousebre_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_inhousebre_service_proto_rawDescData)
	})
	return file_api_inhousebre_service_proto_rawDescData
}

var file_api_inhousebre_service_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_api_inhousebre_service_proto_goTypes = []interface{}{
	(*GetFeaturesDataRequest)(nil),         // 0: inhousebre.GetFeaturesDataRequest
	(*RequestIdentifiers)(nil),             // 1: inhousebre.RequestIdentifiers
	(*GetFeaturesDataResponse)(nil),        // 2: inhousebre.GetFeaturesDataResponse
	(*FeaturesResponseData)(nil),           // 3: inhousebre.FeaturesResponseData
	(*Identifiers)(nil),                    // 4: inhousebre.Identifiers
	(*GetEpfoDataRequest)(nil),             // 5: inhousebre.GetEpfoDataRequest
	(*GetEpfoDataResponse)(nil),            // 6: inhousebre.GetEpfoDataResponse
	(*GetCibilReportFeaturesRequest)(nil),  // 7: inhousebre.GetCibilReportFeaturesRequest
	(*GetCibilReportFeaturesResponse)(nil), // 8: inhousebre.GetCibilReportFeaturesResponse
	(*GetPdScoreRequest)(nil),              // 9: inhousebre.GetPdScoreRequest
	(*GetPdScoreResponse)(nil),             // 10: inhousebre.GetPdScoreResponse
	nil,                                    // 11: inhousebre.FeaturesResponseData.FeatureValueMapEntry
	nil,                                    // 12: inhousebre.GetCibilReportFeaturesResponse.FeatureValueMapEntry
	(*structpb.Value)(nil),                 // 13: google.protobuf.Value
}
var file_api_inhousebre_service_proto_depIdxs = []int32{
	1,  // 0: inhousebre.GetFeaturesDataRequest.request_identifiers_list:type_name -> inhousebre.RequestIdentifiers
	4,  // 1: inhousebre.RequestIdentifiers.identifiers:type_name -> inhousebre.Identifiers
	3,  // 2: inhousebre.GetFeaturesDataResponse.features_response_data_list:type_name -> inhousebre.FeaturesResponseData
	4,  // 3: inhousebre.FeaturesResponseData.identifiers:type_name -> inhousebre.Identifiers
	11, // 4: inhousebre.FeaturesResponseData.feature_value_map:type_name -> inhousebre.FeaturesResponseData.FeatureValueMapEntry
	12, // 5: inhousebre.GetCibilReportFeaturesResponse.feature_value_map:type_name -> inhousebre.GetCibilReportFeaturesResponse.FeatureValueMapEntry
	13, // 6: inhousebre.FeaturesResponseData.FeatureValueMapEntry.value:type_name -> google.protobuf.Value
	13, // 7: inhousebre.GetCibilReportFeaturesResponse.FeatureValueMapEntry.value:type_name -> google.protobuf.Value
	0,  // 8: inhousebre.Bre.GetFeaturesData:input_type -> inhousebre.GetFeaturesDataRequest
	5,  // 9: inhousebre.Bre.GetEpfoData:input_type -> inhousebre.GetEpfoDataRequest
	7,  // 10: inhousebre.Bre.GetCibilReportFeatures:input_type -> inhousebre.GetCibilReportFeaturesRequest
	9,  // 11: inhousebre.Bre.GetPdScore:input_type -> inhousebre.GetPdScoreRequest
	2,  // 12: inhousebre.Bre.GetFeaturesData:output_type -> inhousebre.GetFeaturesDataResponse
	6,  // 13: inhousebre.Bre.GetEpfoData:output_type -> inhousebre.GetEpfoDataResponse
	8,  // 14: inhousebre.Bre.GetCibilReportFeatures:output_type -> inhousebre.GetCibilReportFeaturesResponse
	10, // 15: inhousebre.Bre.GetPdScore:output_type -> inhousebre.GetPdScoreResponse
	12, // [12:16] is the sub-list for method output_type
	8,  // [8:12] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_inhousebre_service_proto_init() }
func file_api_inhousebre_service_proto_init() {
	if File_api_inhousebre_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_inhousebre_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFeaturesDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_inhousebre_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestIdentifiers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_inhousebre_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFeaturesDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_inhousebre_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeaturesResponseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_inhousebre_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Identifiers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_inhousebre_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEpfoDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_inhousebre_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEpfoDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_inhousebre_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCibilReportFeaturesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_inhousebre_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCibilReportFeaturesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_inhousebre_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPdScoreRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_inhousebre_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPdScoreResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_inhousebre_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_inhousebre_service_proto_goTypes,
		DependencyIndexes: file_api_inhousebre_service_proto_depIdxs,
		MessageInfos:      file_api_inhousebre_service_proto_msgTypes,
	}.Build()
	File_api_inhousebre_service_proto = out.File
	file_api_inhousebre_service_proto_rawDesc = nil
	file_api_inhousebre_service_proto_goTypes = nil
	file_api_inhousebre_service_proto_depIdxs = nil
}
