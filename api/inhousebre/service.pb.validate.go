// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/inhousebre/service.proto

package inhousebre

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetFeaturesDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFeaturesDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFeaturesDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFeaturesDataRequestMultiError, or nil if none found.
func (m *GetFeaturesDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFeaturesDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRequestIdentifiersList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFeaturesDataRequestValidationError{
						field:  fmt.Sprintf("RequestIdentifiersList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFeaturesDataRequestValidationError{
						field:  fmt.Sprintf("RequestIdentifiersList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFeaturesDataRequestValidationError{
					field:  fmt.Sprintf("RequestIdentifiersList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFeaturesDataRequestMultiError(errors)
	}

	return nil
}

// GetFeaturesDataRequestMultiError is an error wrapping multiple validation
// errors returned by GetFeaturesDataRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFeaturesDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFeaturesDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFeaturesDataRequestMultiError) AllErrors() []error { return m }

// GetFeaturesDataRequestValidationError is the validation error returned by
// GetFeaturesDataRequest.Validate if the designated constraints aren't met.
type GetFeaturesDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFeaturesDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFeaturesDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFeaturesDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFeaturesDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFeaturesDataRequestValidationError) ErrorName() string {
	return "GetFeaturesDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFeaturesDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFeaturesDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFeaturesDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFeaturesDataRequestValidationError{}

// Validate checks the field values on RequestIdentifiers with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RequestIdentifiers) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RequestIdentifiers with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RequestIdentifiersMultiError, or nil if none found.
func (m *RequestIdentifiers) ValidateAll() error {
	return m.validate(true)
}

func (m *RequestIdentifiers) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifiers()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RequestIdentifiersValidationError{
					field:  "Identifiers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RequestIdentifiersValidationError{
					field:  "Identifiers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifiers()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RequestIdentifiersValidationError{
				field:  "Identifiers",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RequestIdentifiersMultiError(errors)
	}

	return nil
}

// RequestIdentifiersMultiError is an error wrapping multiple validation errors
// returned by RequestIdentifiers.ValidateAll() if the designated constraints
// aren't met.
type RequestIdentifiersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RequestIdentifiersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RequestIdentifiersMultiError) AllErrors() []error { return m }

// RequestIdentifiersValidationError is the validation error returned by
// RequestIdentifiers.Validate if the designated constraints aren't met.
type RequestIdentifiersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RequestIdentifiersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RequestIdentifiersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RequestIdentifiersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RequestIdentifiersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RequestIdentifiersValidationError) ErrorName() string {
	return "RequestIdentifiersValidationError"
}

// Error satisfies the builtin error interface
func (e RequestIdentifiersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRequestIdentifiers.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RequestIdentifiersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RequestIdentifiersValidationError{}

// Validate checks the field values on GetFeaturesDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFeaturesDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFeaturesDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFeaturesDataResponseMultiError, or nil if none found.
func (m *GetFeaturesDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFeaturesDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFeaturesResponseDataList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFeaturesDataResponseValidationError{
						field:  fmt.Sprintf("FeaturesResponseDataList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFeaturesDataResponseValidationError{
						field:  fmt.Sprintf("FeaturesResponseDataList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFeaturesDataResponseValidationError{
					field:  fmt.Sprintf("FeaturesResponseDataList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ExperianReportDataAvailabilityStatus

	if len(errors) > 0 {
		return GetFeaturesDataResponseMultiError(errors)
	}

	return nil
}

// GetFeaturesDataResponseMultiError is an error wrapping multiple validation
// errors returned by GetFeaturesDataResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFeaturesDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFeaturesDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFeaturesDataResponseMultiError) AllErrors() []error { return m }

// GetFeaturesDataResponseValidationError is the validation error returned by
// GetFeaturesDataResponse.Validate if the designated constraints aren't met.
type GetFeaturesDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFeaturesDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFeaturesDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFeaturesDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFeaturesDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFeaturesDataResponseValidationError) ErrorName() string {
	return "GetFeaturesDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFeaturesDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFeaturesDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFeaturesDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFeaturesDataResponseValidationError{}

// Validate checks the field values on FeaturesResponseData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FeaturesResponseData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeaturesResponseData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FeaturesResponseDataMultiError, or nil if none found.
func (m *FeaturesResponseData) ValidateAll() error {
	return m.validate(true)
}

func (m *FeaturesResponseData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifiers()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeaturesResponseDataValidationError{
					field:  "Identifiers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeaturesResponseDataValidationError{
					field:  "Identifiers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifiers()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeaturesResponseDataValidationError{
				field:  "Identifiers",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetFeatureValueMap()))
		i := 0
		for key := range m.GetFeatureValueMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFeatureValueMap()[key]
			_ = val

			// no validation rules for FeatureValueMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, FeaturesResponseDataValidationError{
							field:  fmt.Sprintf("FeatureValueMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, FeaturesResponseDataValidationError{
							field:  fmt.Sprintf("FeatureValueMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return FeaturesResponseDataValidationError{
						field:  fmt.Sprintf("FeatureValueMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return FeaturesResponseDataMultiError(errors)
	}

	return nil
}

// FeaturesResponseDataMultiError is an error wrapping multiple validation
// errors returned by FeaturesResponseData.ValidateAll() if the designated
// constraints aren't met.
type FeaturesResponseDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeaturesResponseDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeaturesResponseDataMultiError) AllErrors() []error { return m }

// FeaturesResponseDataValidationError is the validation error returned by
// FeaturesResponseData.Validate if the designated constraints aren't met.
type FeaturesResponseDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeaturesResponseDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeaturesResponseDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeaturesResponseDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeaturesResponseDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeaturesResponseDataValidationError) ErrorName() string {
	return "FeaturesResponseDataValidationError"
}

// Error satisfies the builtin error interface
func (e FeaturesResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeaturesResponseData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeaturesResponseDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeaturesResponseDataValidationError{}

// Validate checks the field values on Identifiers with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Identifiers) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Identifiers with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IdentifiersMultiError, or
// nil if none found.
func (m *Identifiers) ValidateAll() error {
	return m.validate(true)
}

func (m *Identifiers) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AccountId

	// no validation rules for ModelName

	if len(errors) > 0 {
		return IdentifiersMultiError(errors)
	}

	return nil
}

// IdentifiersMultiError is an error wrapping multiple validation errors
// returned by Identifiers.ValidateAll() if the designated constraints aren't met.
type IdentifiersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IdentifiersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IdentifiersMultiError) AllErrors() []error { return m }

// IdentifiersValidationError is the validation error returned by
// Identifiers.Validate if the designated constraints aren't met.
type IdentifiersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IdentifiersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IdentifiersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IdentifiersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IdentifiersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IdentifiersValidationError) ErrorName() string { return "IdentifiersValidationError" }

// Error satisfies the builtin error interface
func (e IdentifiersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIdentifiers.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IdentifiersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IdentifiersValidationError{}

// Validate checks the field values on GetEpfoDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEpfoDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEpfoDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEpfoDataRequestMultiError, or nil if none found.
func (m *GetEpfoDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEpfoDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetEpfoDataRequestMultiError(errors)
	}

	return nil
}

// GetEpfoDataRequestMultiError is an error wrapping multiple validation errors
// returned by GetEpfoDataRequest.ValidateAll() if the designated constraints
// aren't met.
type GetEpfoDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEpfoDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEpfoDataRequestMultiError) AllErrors() []error { return m }

// GetEpfoDataRequestValidationError is the validation error returned by
// GetEpfoDataRequest.Validate if the designated constraints aren't met.
type GetEpfoDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEpfoDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEpfoDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEpfoDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEpfoDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEpfoDataRequestValidationError) ErrorName() string {
	return "GetEpfoDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEpfoDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEpfoDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEpfoDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEpfoDataRequestValidationError{}

// Validate checks the field values on GetEpfoDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEpfoDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEpfoDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEpfoDataResponseMultiError, or nil if none found.
func (m *GetEpfoDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEpfoDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RawEpfoData

	if len(errors) > 0 {
		return GetEpfoDataResponseMultiError(errors)
	}

	return nil
}

// GetEpfoDataResponseMultiError is an error wrapping multiple validation
// errors returned by GetEpfoDataResponse.ValidateAll() if the designated
// constraints aren't met.
type GetEpfoDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEpfoDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEpfoDataResponseMultiError) AllErrors() []error { return m }

// GetEpfoDataResponseValidationError is the validation error returned by
// GetEpfoDataResponse.Validate if the designated constraints aren't met.
type GetEpfoDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEpfoDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEpfoDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEpfoDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEpfoDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEpfoDataResponseValidationError) ErrorName() string {
	return "GetEpfoDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEpfoDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEpfoDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEpfoDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEpfoDataResponseValidationError{}

// Validate checks the field values on GetCibilReportFeaturesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCibilReportFeaturesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCibilReportFeaturesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCibilReportFeaturesRequestMultiError, or nil if none found.
func (m *GetCibilReportFeaturesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCibilReportFeaturesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetCibilReportFeaturesRequestMultiError(errors)
	}

	return nil
}

// GetCibilReportFeaturesRequestMultiError is an error wrapping multiple
// validation errors returned by GetCibilReportFeaturesRequest.ValidateAll()
// if the designated constraints aren't met.
type GetCibilReportFeaturesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCibilReportFeaturesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCibilReportFeaturesRequestMultiError) AllErrors() []error { return m }

// GetCibilReportFeaturesRequestValidationError is the validation error
// returned by GetCibilReportFeaturesRequest.Validate if the designated
// constraints aren't met.
type GetCibilReportFeaturesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCibilReportFeaturesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCibilReportFeaturesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCibilReportFeaturesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCibilReportFeaturesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCibilReportFeaturesRequestValidationError) ErrorName() string {
	return "GetCibilReportFeaturesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCibilReportFeaturesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCibilReportFeaturesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCibilReportFeaturesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCibilReportFeaturesRequestValidationError{}

// Validate checks the field values on GetCibilReportFeaturesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCibilReportFeaturesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCibilReportFeaturesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCibilReportFeaturesResponseMultiError, or nil if none found.
func (m *GetCibilReportFeaturesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCibilReportFeaturesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetFeatureValueMap()))
		i := 0
		for key := range m.GetFeatureValueMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFeatureValueMap()[key]
			_ = val

			// no validation rules for FeatureValueMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetCibilReportFeaturesResponseValidationError{
							field:  fmt.Sprintf("FeatureValueMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetCibilReportFeaturesResponseValidationError{
							field:  fmt.Sprintf("FeatureValueMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetCibilReportFeaturesResponseValidationError{
						field:  fmt.Sprintf("FeatureValueMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for CibilReportDataAvailabilityStatus

	if len(errors) > 0 {
		return GetCibilReportFeaturesResponseMultiError(errors)
	}

	return nil
}

// GetCibilReportFeaturesResponseMultiError is an error wrapping multiple
// validation errors returned by GetCibilReportFeaturesResponse.ValidateAll()
// if the designated constraints aren't met.
type GetCibilReportFeaturesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCibilReportFeaturesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCibilReportFeaturesResponseMultiError) AllErrors() []error { return m }

// GetCibilReportFeaturesResponseValidationError is the validation error
// returned by GetCibilReportFeaturesResponse.Validate if the designated
// constraints aren't met.
type GetCibilReportFeaturesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCibilReportFeaturesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCibilReportFeaturesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCibilReportFeaturesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCibilReportFeaturesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCibilReportFeaturesResponseValidationError) ErrorName() string {
	return "GetCibilReportFeaturesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCibilReportFeaturesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCibilReportFeaturesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCibilReportFeaturesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCibilReportFeaturesResponseValidationError{}

// Validate checks the field values on GetPdScoreRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetPdScoreRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPdScoreRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPdScoreRequestMultiError, or nil if none found.
func (m *GetPdScoreRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPdScoreRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Bureau

	if len(errors) > 0 {
		return GetPdScoreRequestMultiError(errors)
	}

	return nil
}

// GetPdScoreRequestMultiError is an error wrapping multiple validation errors
// returned by GetPdScoreRequest.ValidateAll() if the designated constraints
// aren't met.
type GetPdScoreRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPdScoreRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPdScoreRequestMultiError) AllErrors() []error { return m }

// GetPdScoreRequestValidationError is the validation error returned by
// GetPdScoreRequest.Validate if the designated constraints aren't met.
type GetPdScoreRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPdScoreRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPdScoreRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPdScoreRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPdScoreRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPdScoreRequestValidationError) ErrorName() string {
	return "GetPdScoreRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPdScoreRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPdScoreRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPdScoreRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPdScoreRequestValidationError{}

// Validate checks the field values on GetPdScoreResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPdScoreResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPdScoreResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPdScoreResponseMultiError, or nil if none found.
func (m *GetPdScoreResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPdScoreResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PdScore

	// no validation rules for PdScoreVersion

	if len(errors) > 0 {
		return GetPdScoreResponseMultiError(errors)
	}

	return nil
}

// GetPdScoreResponseMultiError is an error wrapping multiple validation errors
// returned by GetPdScoreResponse.ValidateAll() if the designated constraints
// aren't met.
type GetPdScoreResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPdScoreResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPdScoreResponseMultiError) AllErrors() []error { return m }

// GetPdScoreResponseValidationError is the validation error returned by
// GetPdScoreResponse.Validate if the designated constraints aren't met.
type GetPdScoreResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPdScoreResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPdScoreResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPdScoreResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPdScoreResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPdScoreResponseValidationError) ErrorName() string {
	return "GetPdScoreResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPdScoreResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPdScoreResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPdScoreResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPdScoreResponseValidationError{}
