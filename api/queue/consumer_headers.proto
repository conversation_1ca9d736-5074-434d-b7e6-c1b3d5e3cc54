// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package queue;

import "api/rpc/status.proto";


option go_package = "github.com/epifi/be-common/api/queue";
option java_package = "com.github.epifi.be-common.api.queue";

// Represents status of message consumption by a consumer method.
// The queue poller takes certain actions based on this status
enum MessageConsumptionStatus {
  STATUS_UNSPECIFIED = 0;
  // Marks successful processing of a message. Message is deleted from queue on SUCCESS.
  SUCCESS = 1;
  // For transient failures which can be retried. Optionally, message visibility is changed based on different retry strategies.
  TRANSIENT_FAILURE = 2;
  // For permanent failures which can't be retried. When this status code is returned the packet is deleted from the queue
  // e.g. errors like record not found, etc. come under this.
  PERMANENT_FAILURE = 3;
}

// A set of all the common attributes to be contained in a queue consumer response
message ConsumerResponseHeader {
  MessageConsumptionStatus status = 1;

  // next timeout to be set for the packet in case of a transient failure.
  // consumers are expected to populate this based on retry strategies they follow.
  // if set to default(0) visibility timeout is not changed.
  int64 next_timeout = 2;

  // Grpc status code to be populated by consumer (optional)
  // This field will determine what grpc code the consumer response will get mapped to in case retries are exhausted, or permanent failure occurs
  rpc.Status grpc_status_code = 3;
}

// A set of all the common attributes to be contained in a queue consumer response
message ConsumerRequestHeader {
  // This flag gives info of whether this call from subscriber to service method is the last call or not
  // Using this flag services method can put some extra logic on their side to handle last call ex: update state
  // to manual_intervention_needed
  bool is_last_attempt = 1;
}
