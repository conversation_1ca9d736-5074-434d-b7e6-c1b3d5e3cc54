// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/pay/signal/fund_transfer.proto

package signal

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	payment "github.com/epifi/gamma/api/order/payment"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = payment.TransactionStatus(0)
)

// Validate checks the field values on FundTransferStatusSignal with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FundTransferStatusSignal) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FundTransferStatusSignal with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FundTransferStatusSignalMultiError, or nil if none found.
func (m *FundTransferStatusSignal) ValidateAll() error {
	return m.validate(true)
}

func (m *FundTransferStatusSignal) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransactionStatus

	// no validation rules for TransactionId

	// no validation rules for OrderId

	if len(errors) > 0 {
		return FundTransferStatusSignalMultiError(errors)
	}

	return nil
}

// FundTransferStatusSignalMultiError is an error wrapping multiple validation
// errors returned by FundTransferStatusSignal.ValidateAll() if the designated
// constraints aren't met.
type FundTransferStatusSignalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FundTransferStatusSignalMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FundTransferStatusSignalMultiError) AllErrors() []error { return m }

// FundTransferStatusSignalValidationError is the validation error returned by
// FundTransferStatusSignal.Validate if the designated constraints aren't met.
type FundTransferStatusSignalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FundTransferStatusSignalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FundTransferStatusSignalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FundTransferStatusSignalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FundTransferStatusSignalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FundTransferStatusSignalValidationError) ErrorName() string {
	return "FundTransferStatusSignalValidationError"
}

// Error satisfies the builtin error interface
func (e FundTransferStatusSignalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFundTransferStatusSignal.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FundTransferStatusSignalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FundTransferStatusSignalValidationError{}
