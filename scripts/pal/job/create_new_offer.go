// nolint:funlen
package job

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/preapprovedloan/enums"

	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/scripts/pal/job/vendors"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
)

type CreateNewOfferJob struct {
	loanOffersDao       dao.LoanOffersDao
	loanApplicantDao    dao.LoanApplicantDao
	loecDao             dao.LoanOfferEligibilityCriteriaDao
	txnExecutor         storageV2.TxnExecutor
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]
	dbResourceProvider  *storageV2.DBResourceProvider[*gorm.DB]
}

func NewCreateNewOfferJob(loanOffersDao dao.LoanOffersDao, loanApplicantDao dao.LoanApplicantDao, txnExecutor storageV2.TxnExecutor, txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor], dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB], loecDao dao.LoanOfferEligibilityCriteriaDao) *CreateNewOfferJob {
	return &CreateNewOfferJob{
		loanOffersDao:       loanOffersDao,
		loanApplicantDao:    loanApplicantDao,
		txnExecutor:         txnExecutor,
		txnExecutorProvider: txnExecutorProvider,
		dbResourceProvider:  dbResourceProvider,
		loecDao:             loecDao,
	}
}

type CreateNewOfferJobArgs struct {
	ActorId              string `json:"actorId"`
	CreateOffer          bool   `json:"createOffer,omitempty"`
	LoanProgram          string `json:"loanProgram,omitempty"`
	Vendor               string `json:"vendor,omitempty"`
	DeleteAllOlderOffers bool   `json:"deleteAllOlderOffers,omitempty"`
}

// jobName: CREATE_NEW_OFFER
// jobArguments: {"actorId":"AC221114IV3898UjhY6FgtJMTAOlpQ==","createOffer":true,"loanProgram":"preapprovedloan","vendor":"liquiloans"}
// actorId : ActorId of the user
// createOffer : Flag to check if new offer needs to be created after deletion or not
// loanProgram : preapprovedloan/earlysalary/fldg
// vendor : liquiloans/idfc

func (p *CreateNewOfferJob) GetArgs() interface{} {
	return &CreateNewOfferJobArgs{}
}

func (p *CreateNewOfferJob) Run(ctx context.Context, args ...interface{}) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}
	if cfg.IsProdEnv(env) {
		logger.Error(ctx, "env is prod, This job cannot run in prod env")
		return nil
	}
	ctx = epificontext.WithOwnership(ctx, VendorToOwnershipMap[strings.ToLower(args[0].(*CreateNewOfferJobArgs).Vendor)])
	actorId := args[0].(*CreateNewOfferJobArgs).ActorId
	createOffer := args[0].(*CreateNewOfferJobArgs).CreateOffer
	vendor := inputArgsToVendor[strings.ToLower(args[0].(*CreateNewOfferJobArgs).Vendor)]
	lp, ok := inputArgsToLp[strings.ToLower(args[0].(*CreateNewOfferJobArgs).LoanProgram)]
	if !ok {
		return fmt.Errorf("the loan program inputed is invalid")
	}

	var deleteOffersForVendors []commontypes.Ownership
	if !args[0].(*CreateNewOfferJobArgs).DeleteAllOlderOffers {
		deleteOffersForVendors = append(deleteOffersForVendors, VendorToOwnershipMap[strings.ToLower(args[0].(*CreateNewOfferJobArgs).Vendor)])
	} else {
		for _, val := range VendorToOwnershipMap {
			deleteOffersForVendors = append(deleteOffersForVendors, val)
		}
	}
	for _, val := range deleteOffersForVendors {
		delCtx := context.Background()
		delCtx = epificontext.WithOwnership(delCtx, val)
		var loanAccountIds []string
		// delete loan data for the given actor
		db, connErr := vendors.GetConnFromContextOrProvider(delCtx, p.dbResourceProvider)
		if connErr != nil {
			return fmt.Errorf("error in getConnFromContextOrProvider")
		}

		if dbErr := db.Raw("SELECT loan_account_id FROM loan_accounts WHERE actor_id = ?", actorId).Scan(pq.Array(&loanAccountIds)); dbErr.Error != nil {
			return fmt.Errorf("failed to fetch loan account id with given actor id: %w", dbErr.Error)
		}

		if err = db.Exec("DELETE FROM loan_installment_info WHERE account_id IN (?)", loanAccountIds).Error; err != nil {
			return fmt.Errorf("error deleting loan_installment_info relation: %w", err)
		}

		if err = db.Exec("DELETE FROM loan_activities WHERE loan_account_id IN (?)", loanAccountIds).Error; err != nil {
			return fmt.Errorf("error deleting loan_activities relation: %w", err)
		}

		if err = db.Exec("DELETE FROM loan_requests WHERE actor_id = ?", actorId).Error; err != nil {
			return fmt.Errorf("error deleting loan_requests relation: %w", err)
		}

		if err = db.Exec("DELETE FROM loan_accounts WHERE actor_id = ?", actorId).Error; err != nil {
			return fmt.Errorf("error deleting loan_accounts relation: %w", err)
		}

		if err = db.Exec("DELETE FROM loan_step_executions WHERE actor_id = ?", actorId).Error; err != nil {
			return fmt.Errorf("error deleting loan_step_executions relation: %w", err)
		}

		if err = db.Exec("DELETE FROM loan_offers WHERE actor_id = ?", actorId).Error; err != nil {
			return fmt.Errorf("error deleting loan_offers relation: %w", err)
		}

		if err = db.Exec("DELETE FROM loan_offer_eligibility_criteria WHERE actor_id = ?", actorId).Error; err != nil {
			return fmt.Errorf("error deleting loan_offer_eligibility_criteria relation: %w", err)
		}

		if err = db.Exec("DELETE FROM loan_applicants WHERE actor_id = ?", actorId).Error; err != nil {
			return fmt.Errorf("error deleting loan_applicants relation: %w", err)
		}
	}

	// if createOffer is set to false, return from here and don't create offer for the user
	if !createOffer {
		return nil
	}

	loanOffer, loanApplicant := getLoanOfferFromLpAndVendor(lp, vendor, actorId)

	realtimeLpForVendor, ok := isRealTimeCheck[vendor]
	if ok && realtimeLpForVendor[lp] {
		loecRealTime, applicant := getLoecAndApplicantFromLpAndVendor(lp, vendor, actorId)
		// Create LOEC and Loan Applicant in transaction
		txnExec, txnExecErr := vendors.GetTxnExecutorByOwnership(ctx, p.txnExecutorProvider)
		if txnExecErr != nil {
			return fmt.Errorf("error while deriving txn executor : %w", txnExecErr)
		}
		txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
			_, err = p.loecDao.Create(txnCtx, loecRealTime)
			if err != nil && !errors.Is(err, epifierrors.ErrDuplicateEntry) {
				return fmt.Errorf("unable to create loan offer eligibility criteria")
			}
			if applicant != nil {
				_, err = p.loanApplicantDao.Create(txnCtx, applicant)
				if err != nil && !errors.Is(err, epifierrors.ErrDuplicateEntry) {
					return fmt.Errorf("unable to create loan applicant")
				}
			}
			return nil
		})
		if txnErr != nil {
			return fmt.Errorf("unable to create loan eligibility criteria and loan applicant after deactivating the loan")
		}
		return nil
	}

	txnExec, txnExecErr := vendors.GetTxnExecutorByOwnership(ctx, p.txnExecutorProvider)
	if txnExecErr != nil {
		return fmt.Errorf("error while deriving txn executor : %w", txnExecErr)
	}
	txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
		_, err = p.loanApplicantDao.Create(txnCtx, loanApplicant)
		if err != nil && err != epifierrors.ErrDuplicateEntry {
			return fmt.Errorf("unable to create loan applicant")
		}
		_, err2 := p.loanOffersDao.Create(txnCtx, loanOffer)
		if err2 != nil {
			return fmt.Errorf("unable to create loan offer")
		}
		return nil
	})
	if txnErr != nil {
		return fmt.Errorf("unable to create loan offer after deactivating the loan")
	}
	return nil
}

var VendorToOwnershipMap = map[string]commontypes.Ownership{
	"liquiloans":       commontypes.Ownership_LIQUILOANS_PL,
	"idfc":             commontypes.Ownership_IDFC_PL,
	"abfl":             commontypes.Ownership_LOANS_ABFL,
	"moneyview":        commontypes.Ownership_MONEYVIEW_PL,
	"epifitechv2":      commontypes.Ownership_EPIFI_TECH_V2,
	"stockguardianlsp": commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP,
	"lenden":           commontypes.Ownership_LOANS_LENDEN,
}

var inputArgsToLp = map[string]preapprovedloan.LoanProgram{
	"earlysalary":          preapprovedloan.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
	"earlysalaryv2":        preapprovedloan.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2,
	"preapprovedloan":      preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	"fldg":                 preapprovedloan.LoanProgram_LOAN_PROGRAM_FLDG,
	"stpl":                 preapprovedloan.LoanProgram_LOAN_PROGRAM_STPL,
	"realtimedistribution": preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
	"realtimesubvention":   preapprovedloan.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION,
	"realtimestpl":         preapprovedloan.LoanProgram_LOAN_PROGRAM_REALTIME_STPL,
}

var inputArgsToVendor = map[string]preapprovedloan.Vendor{
	"liquiloans":       preapprovedloan.Vendor_LIQUILOANS,
	"idfc":             preapprovedloan.Vendor_IDFC,
	"moneyview":        preapprovedloan.Vendor_MONEYVIEW,
	"abfl":             preapprovedloan.Vendor_ABFL,
	"stockguardianlsp": preapprovedloan.Vendor_STOCK_GUARDIAN_LSP,
	"federal":          preapprovedloan.Vendor_FEDERAL,
	"lenden":           preapprovedloan.Vendor_LENDEN,
}

var inputArgsToAccType = map[string]preapprovedloan.LoanType{
	"preapprovedloan": preapprovedloan.LoanType_LOAN_TYPE_PERSONAL,
	"earlysalary":     preapprovedloan.LoanType_LOAN_TYPE_EARLY_SALARY,
}

// isRealTimeCheck defines is loan program is of realtime eligibility check type. If that is the case, we create entry for loec and applicant
var isRealTimeCheck = map[preapprovedloan.Vendor]map[preapprovedloan.LoanProgram]bool{
	preapprovedloan.Vendor_FEDERAL: {preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION: true},
	preapprovedloan.Vendor_LIQUILOANS: {
		preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION: true,
		preapprovedloan.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:    true,
		preapprovedloan.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:          true,
	},
	preapprovedloan.Vendor_STOCK_GUARDIAN_LSP: {
		preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION: true,
		preapprovedloan.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:        true,
	},
	preapprovedloan.Vendor_LENDEN: {
		preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION: true,
	},
}

func getLoecAndApplicantFromLpAndVendor(lp preapprovedloan.LoanProgram, vendor preapprovedloan.Vendor, actorId string) (*preapprovedloan.LoanOfferEligibilityCriteria, *preapprovedloan.LoanApplicant) {
	var loec *preapprovedloan.LoanOfferEligibilityCriteria
	var la *preapprovedloan.LoanApplicant
	switch vendor {
	case preapprovedloan.Vendor_FEDERAL:
		switch lp {
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			loec = &preapprovedloan.LoanOfferEligibilityCriteria{
				ActorId:        actorId,
				Vendor:         vendor,
				Status:         preapprovedloan.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED,
				SubStatus:      preapprovedloan.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_PRE_QUALIFIED_BY_VENDOR,
				VendorResponse: &preapprovedloan.VendorResponse{},
				BatchId:        "BI-09-03-2023",
				LoanScheme:     "FED_AA",
				LoanProgram:    preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			}
			la = nil
		default:
			// do nothing
		}
	case preapprovedloan.Vendor_STOCK_GUARDIAN_LSP:
		vendorRequestId := uuid.NewString()
		loec = &preapprovedloan.LoanOfferEligibilityCriteria{
			ActorId:         actorId,
			Vendor:          vendor,
			Status:          preapprovedloan.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED,
			SubStatus:       preapprovedloan.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_PRE_QUALIFIED_BY_VENDOR,
			VendorResponse:  &preapprovedloan.VendorResponse{},
			BatchId:         "1",
			LoanScheme:      "RT_LL",
			LoanProgram:     lp,
			VendorRequestId: vendorRequestId,
		}
		la = &preapprovedloan.LoanApplicant{
			ActorId:            actorId,
			Vendor:             vendor,
			Status:             preapprovedloan.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
			SubStatus:          preapprovedloan.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI,
			LoanProgram:        lp,
			VendorRequestId:    vendorRequestId,
			VendorApplicantId:  uuid.NewString(),
			LoanProgramVersion: enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1,
		}
	case preapprovedloan.Vendor_LENDEN:
		switch lp {
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			loec = &preapprovedloan.LoanOfferEligibilityCriteria{
				ActorId:        actorId,
				Vendor:         vendor,
				Status:         preapprovedloan.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED,
				SubStatus:      preapprovedloan.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED,
				VendorResponse: &preapprovedloan.VendorResponse{},
				BatchId:        "",
				LoanScheme:     "",
				LoanProgram:    preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
				PolicyParams: &preapprovedloan.PolicyParams{
					ExecutionInfo: &preapprovedloan.ExecutionInfo{},
				},
				DataRequirementDetails: &preapprovedloan.DataRequirementDetails{},
				ExpiredAt:              timestampPb.New(timestampPb.Now().AsTime().AddDate(0, 1, 0)),
			}

			la = &preapprovedloan.LoanApplicant{
				ActorId:            actorId,
				Vendor:             vendor,
				VendorApplicantId:  "",
				VendorRequestId:    uuid.NewString(),
				LoanProgram:        preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
				Status:             preapprovedloan.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
				SubStatus:          preapprovedloan.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI,
				LoanProgramVersion: enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1,
			}
		default:
			// do nothing
		}
	case preapprovedloan.Vendor_LIQUILOANS:
		switch lp {
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			preapprovedloan.LoanProgram_LOAN_PROGRAM_REALTIME_STPL,
			preapprovedloan.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
			loec = &preapprovedloan.LoanOfferEligibilityCriteria{
				ActorId:        actorId,
				Vendor:         vendor,
				Status:         preapprovedloan.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED,
				SubStatus:      preapprovedloan.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_PRE_QUALIFIED_BY_VENDOR,
				VendorResponse: &preapprovedloan.VendorResponse{},
				BatchId:        "BI-09-03-2023",
				LoanScheme:     "RT_LL",
				LoanProgram:    lp,
			}
			la = &preapprovedloan.LoanApplicant{
				ActorId:     actorId,
				Vendor:      vendor,
				Status:      preapprovedloan.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
				SubStatus:   preapprovedloan.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_UNSPECIFIED,
				LoanProgram: lp,
			}
		default:
			// do nothing
		}
	}

	return loec, la
}

func getLoanOfferFromLpAndVendor(loanprogram preapprovedloan.LoanProgram, vendor preapprovedloan.Vendor, actorId string) (*preapprovedloan.LoanOffer, *preapprovedloan.LoanApplicant) {
	var maxTenure int32
	var maxAmount, maxEmi, minAmount *money.Money
	var interest float64
	minTenure := int32(12)
	switch loanprogram {
	case preapprovedloan.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		maxTenure = 1
		maxAmount = &money.Money{
			CurrencyCode: "INR",
			Units:        50000,
			Nanos:        0,
		}
		minAmount = &money.Money{
			CurrencyCode: "INR",
			Units:        1000,
			Nanos:        0,
		}
		maxEmi = maxAmount
		interest = 0
	case preapprovedloan.LoanProgram_LOAN_PROGRAM_STPL:
		switch vendor {
		case preapprovedloan.Vendor_LIQUILOANS:
			maxTenure = 6
			maxAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        50000,
				Nanos:        0,
			}
			minAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        10000,
				Nanos:        0,
			}
			maxEmi = &money.Money{
				CurrencyCode: "INR",
				Units:        10000,
				Nanos:        0,
			}
			interest = 18
		default:
			// do nothing
		}
	case preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		switch vendor {
		case preapprovedloan.Vendor_ABFL:
			maxTenure = 36
			maxAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        90000,
				Nanos:        0,
			}
			minAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        1000,
				Nanos:        0,
			}
			maxEmi = &money.Money{
				CurrencyCode: "INR",
				Units:        8000,
				Nanos:        0,
			}
			interest = 11.5
		default:
			// do nothing
		}
	case preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN, preapprovedloan.LoanProgram_LOAN_PROGRAM_FLDG:
		switch vendor {
		case preapprovedloan.Vendor_LIQUILOANS:
			maxTenure = 36
			maxAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        90000,
				Nanos:        0,
			}
			minAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        10000,
				Nanos:        0,
			}
			maxEmi = &money.Money{
				CurrencyCode: "INR",
				Units:        10000,
				Nanos:        0,
			}
			interest = 18
		case preapprovedloan.Vendor_IDFC:
			maxTenure = 60
			maxAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        420000,
				Nanos:        0,
			}
			minAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        10000,
				Nanos:        0,
			}
			maxEmi = &money.Money{
				CurrencyCode: "INR",
				Units:        75000,
				Nanos:        0,
			}
			interest = 15
		case preapprovedloan.Vendor_ABFL:
			maxTenure = 60
			maxAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        320000,
				Nanos:        0,
			}
			minAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        40000,
				Nanos:        0,
			}
			maxEmi = &money.Money{
				CurrencyCode: "INR",
				Units:        75000,
				Nanos:        0,
			}
			interest = 15
		case preapprovedloan.Vendor_STOCK_GUARDIAN_LSP:
			maxTenure = 60
			maxAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        500000,
				Nanos:        0,
			}
			minAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        30000,
				Nanos:        0,
			}
			maxEmi = &money.Money{
				CurrencyCode: "INR",
				Units:        75000,
				Nanos:        0,
			}
			interest = 15
		case preapprovedloan.Vendor_MONEYVIEW:
			maxTenure = 60
			maxAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        500000,
				Nanos:        0,
			}
			minAmount = &money.Money{
				CurrencyCode: "INR",
				Units:        30000,
				Nanos:        0,
			}
			maxEmi = &money.Money{
				CurrencyCode: "INR",
				Units:        75000,
				Nanos:        0,
			}
			interest = 15
		}

	default:
		// do nothing
	}
	validTill := timestampPb.Now().AsTime().AddDate(0, 1, 0)
	loanOffer := &preapprovedloan.LoanOffer{
		ActorId:       actorId,
		VendorOfferId: uuid.NewString(),
		Vendor:        vendor,
		OfferConstraints: &preapprovedloan.OfferConstraints{
			MaxTenureMonths: maxTenure,
			MaxLoanAmount:   maxAmount,
			MaxEmiAmount:    maxEmi,
			MinLoanAmount:   minAmount,
			MinTenureMonths: minTenure,
		},
		ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
			ProcessingFee: []*preapprovedloan.RangeData{
				{
					Value: &preapprovedloan.RangeData_Percentage{Percentage: 2},
					Start: minAmount.GetUnits(),
					End:   1000000,
				},
			},
			InterestRate: []*preapprovedloan.RangeData{
				{
					Value: &preapprovedloan.RangeData_Percentage{Percentage: interest},
					Start: minAmount.GetUnits(),
					End:   1000000,
				},
			},
			Gst: 18,
		},
		ValidSince:  timestampPb.Now(),
		ValidTill:   timestampPb.New(validTill),
		LoanProgram: loanprogram,
	}

	la := &preapprovedloan.LoanApplicant{
		ActorId:         actorId,
		Vendor:          vendor,
		VendorRequestId: strconv.Itoa(int(time.Now().Unix())),
		LoanProgram:     loanprogram,
		Status:          preapprovedloan.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
		SubStatus:       preapprovedloan.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI,
	}
	return loanOffer, la
}
