// nolint:all
package purge

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/dao/model"
	"github.com/epifi/gamma/scripts/pal/job/common"

	"gorm.io/gorm"
)

// LenderDataJob is used to delete the customer PII collected and stored in lender specific databases.
// These PIIs are usually collected during application journey and stored in Loan Request and Loan Step Execution.
// However, the scope can be extended to other entities like Loan Applicants and Loan Accounts
type LenderDataJob struct {
	dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB]
	lseDao             dao.LoanStepExecutionsDao
	lrDao              dao.LoanRequestsDao
}

func NewLenderDataJob(
	dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB],
	lseDao dao.LoanStepExecutionsDao,
	lrDao dao.LoanRequestsDao,
) *LenderDataJob {
	return &LenderDataJob{
		dbResourceProvider: dbResourceProvider,
		lseDao:             lseDao,
		lrDao:              lrDao,
	}
}

type LenderDataJobArgs struct {
	Vendor   palPb.Vendor `json:"vendor"`
	FromDays int64        `json:"fromDays"`
	ToDays   int64        `json:"toDays"`
}

func (b *LenderDataJob) GetArgs() interface{} {
	return &LenderDataJobArgs{}
}

// JobName : "PURGE_LENDER_DATA"
// purging data from (now - FromDays) to (now - ToDays)
// args: {"vendor":"FEDERAL", "fromDays": 25, "toDays": 20}

// nolint:funlen
func (b *LenderDataJob) Run(ctx context.Context, args ...interface{}) error {
	vendor := args[0].(*LenderDataJobArgs).Vendor
	fromDays := args[0].(*LenderDataJobArgs).FromDays
	toDays := args[0].(*LenderDataJobArgs).ToDays
	ctx = epificontext.WithOwnership(ctx, common.GetPalOwnership(vendor))

	if toDays < 20 || (vendor != palPb.Vendor_FEDERAL) {
		return errors.New("not allowing to run for less than 20 days, and vendor other than federal for now")
	}

	var totalUpdatedLrIds []string
	var totalUpdatedLseIds []string
	errMap := make(map[string]string)

	// get the required db
	db, err := common.GetConnFromContextOrProvider(ctx, b.dbResourceProvider)
	if err != nil {
		return fmt.Errorf("error in getConnFromContextOrProvider : %w", err)
	}

	currentTime := datetime.GetTimeAtStartOfTheDay(time.Now().In(datetime.IST))
	fromTimeStamp := currentTime.AddDate(0, 0, int(-fromDays))
	toTimeStamp := currentTime.AddDate(0, 0, int(-toDays))
	var loanRequests []*model.LoanRequest

	// get the purger
	purger, gpErr := GetPurger(vendor)
	if gpErr != nil {
		return fmt.Errorf("error in getting purger, %v", gpErr)
	}

	// purging data from (now - fromDays) to (now - toDays)
	// this will ideally require a index on created_at but given the size of the table is < 100000. We can still manage without index.
	if dbErr := db.Where("created_at >= ? and created_at < ?", fromTimeStamp, toTimeStamp).FindInBatches(&loanRequests, 100, func(tx *gorm.DB, batch int) error {
		fmt.Println("Running batch: ", batch)
		for i := range loanRequests {
			lr := loanRequests[i].GetProto()
			if lr.IsNonTerminal() {
				continue
			}

			// purge data from LR
			updatedLr, isUpdateNeeded := purger.GetUpdatedLoanRequest(lr)
			if isUpdateNeeded {
				updateLrErr := b.lrDao.Update(ctx, updatedLr, []palPb.LoanRequestFieldMask{
					palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS,
				})
				switch {
				case errors.Is(updateLrErr, epifierrors.ErrRowNotUpdated):
					// row not updated, continue
				case updateLrErr != nil:
					errMap[lr.GetActorId()] = fmt.Sprintf("error in updating lr. lrId:%v,  %v", updatedLr.GetId(), updateLrErr)
				default:
					totalUpdatedLrIds = append(totalUpdatedLrIds, lr.GetId())
				}
			} else {
				fmt.Println("Update not needed for LR: ", lr.GetId())
			}

			// get all LSEs
			lseList, glErr := b.lseDao.GetByRefIdAndStatuses(ctx, lr.GetId(), nil)
			if glErr != nil {
				errMap[lr.GetActorId()] = fmt.Sprintf("error GetByRefIdAndStatuses, lrId:%v, %v", lr.GetId(), glErr)
			}

			// purge data from LSEs
			for _, lse := range lseList {
				updatedLse, isLseUpdateNeeded := purger.GetUpdatedLoanStepExecution(lse)
				if isLseUpdateNeeded {
					uErr := b.lseDao.Update(ctx, updatedLse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
					switch {
					case errors.Is(uErr, epifierrors.ErrRowNotUpdated):
						// row not updated as it's already purged
						// continue
					case uErr != nil:
						errMap[lr.GetActorId()] = fmt.Sprintf("error in updating lse. lseId:%v,  %v", lse.GetId(), uErr)
					default:
						totalUpdatedLseIds = append(totalUpdatedLseIds, lse.GetId())
					}
				} else {
					fmt.Println("Update not needed for LSE: ", lse.GetId())
				}
			}
		}
		time.Sleep(1 * time.Second)
		return nil
	}).Error; dbErr != nil {
		return fmt.Errorf("error in fetching loan requests, error: %w", dbErr)
	}

	fmt.Println("TotalUpdatedLrIds: ", totalUpdatedLrIds)
	fmt.Println("TotalUpdatedLseIds: ", totalUpdatedLseIds)
	fmt.Println("Errors: ", errMap)
	return nil
}
