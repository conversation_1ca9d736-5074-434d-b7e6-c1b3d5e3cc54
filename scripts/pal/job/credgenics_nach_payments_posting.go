package job

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gocarina/gocsv"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/crypto"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	collectionPb "github.com/epifi/gamma/api/collection"
	collectionTypesPb "github.com/epifi/gamma/api/collection/types"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/scripts/pal/job/vendors"
	wireTypes "github.com/epifi/gamma/scripts/pal/wire/type"
)

type PostNachPaymentsToCredgenicsJob struct {
	palS3Client      wireTypes.PalS3Client
	collectionClient collectionPb.CollectionClient
	loanAccountsDao  dao.LoanAccountsDao
}

func NewPostNachPaymentsToCredgenicsJob(palS3Client wireTypes.PalS3Client, collectionsClient collectionPb.CollectionClient,
	loanAccountsDao dao.LoanAccountsDao) *PostNachPaymentsToCredgenicsJob {
	return &PostNachPaymentsToCredgenicsJob{
		palS3Client:      palS3Client,
		collectionClient: collectionsClient,
		loanAccountsDao:  loanAccountsDao,
	}
}

type PostNachPaymentsToCredgenicsJobArgs struct {
	NachFileName string `json:"nachFileName"`
	Vendor       string `json:"vendor"`
}

func (p *PostNachPaymentsToCredgenicsJob) GetArgs() interface{} {
	return &PostNachPaymentsToCredgenicsJobArgs{}
}

type PaymentRecord struct {
	LoanAccountNumber string  `csv:"LoanAccountNumber"`
	Amount            float64 `csv:"Amount"`
	PaymentDate       string  `csv:"PaymentDate"`
	PaymentStatus     string  `csv:"PaymentStatus"`
}

type PaymentPostingOutput struct {
	PaymentRecord *PaymentRecord
	PostingStatus string
	Remark        string
	PaymentRefNum string
}

//nolint:gosec
const (
	RawNachFileS3Path                    = "vendors/%s/payments/nach/raw/%s"
	ProcessedNachFileForCredgenicsS3Path = "vendors/%s/payments/nach/processed/credgenics/%s_result_%s.csv"
	// **IMPORTANT**: Do not change this paymentRef format else posted payments won't get deduped correctly.
	paymentRefNumFormat = "NACH_TXN_%s_%f_%s"
	// denotes if NACH execustion is successful
	successfulNachTxnStatus               = "success"
	liquiloansLoanAccNumberPrefixWithZero = "LCL0"
	liquiloansLoanAccNumberPrefix         = "LCL"
	// denotes if payment posting flow execution is failed
	failedPostingStatus             = "FAILED"
	fetchAccountNumberPostingStatus = "FETCHING ACCOUNT NUMBER FAILED"
	// denotes if payment posting flow execution is successful
	successfulPostingStatus    = "SUCCESSFUL"
	alreadyPostedPostingStatus = "ALREADY POSTED"
)

func (p *PostNachPaymentsToCredgenicsJob) Run(ctx context.Context, args ...interface{}) error {
	fileName := args[0].(*PostNachPaymentsToCredgenicsJobArgs).NachFileName
	vendorStr := args[0].(*PostNachPaymentsToCredgenicsJobArgs).Vendor
	if vendorStr == "" {
		return errors.New("vendor argument cannot be empty")
	}
	vendor := vendors.GetVendorFromString(vendorStr)
	ownership := vendors.GetPalOwnership(vendor)
	ctx = epificontext.WithOwnership(ctx, ownership)

	filePath := fmt.Sprintf(RawNachFileS3Path, strings.ToLower(vendorStr), fileName)
	logger.Info(ctx, "reading NACH file from S3", zap.String("filePath", filePath))
	records, err := p.palS3Client.Read(ctx, filePath)
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("error while reading NACH file from s3 with path %s", filePath))
	}

	var paymentRecords []*PaymentRecord
	err = gocsv.Unmarshal(bytes.NewReader(records), &paymentRecords)
	if err != nil {
		return errors.Wrap(err, "error while unmarshaling file data into payment record struct")
	}

	successfulPaymentRecords := getSuccessfulPaymentRecords(paymentRecords)
	result := p.postNachPaymentToCredgenics(ctx, successfulPaymentRecords, vendor)
	csvContent, err := convertPaymentPostingOutputToCsvContent(result)
	if err != nil {
		return errors.Wrap(err, "error while converting payment posting result to csv")
	}
	writeErr := p.writePaymentPostingRecordToS3(ctx, csvContent, fileName, vendor)
	if writeErr != nil {
		return errors.Wrap(writeErr, "error while writing payment posting result file to S3")
	}
	return nil
}

func getSuccessfulPaymentRecords(paymentRecords []*PaymentRecord) []*PaymentRecord {
	var successfulPaymentRecords []*PaymentRecord
	for _, record := range paymentRecords {
		if strings.EqualFold(record.PaymentStatus, successfulNachTxnStatus) {
			successfulPaymentRecords = append(successfulPaymentRecords, record)
		}
	}
	return successfulPaymentRecords
}

// nolint:funlen
func (p *PostNachPaymentsToCredgenicsJob) postNachPaymentToCredgenics(ctx context.Context, paymentRecords []*PaymentRecord, vendor palPb.Vendor) []*PaymentPostingOutput {
	var result []*PaymentPostingOutput
	for _, paymentRecord := range paymentRecords {
		amtReceived := moneyPkg.ParseFloat(paymentRecord.Amount, "INR")
		recoveryDate := datetimePkg.DateFromString(paymentRecord.PaymentDate)
		accountNumber, accountNumberErr := getLoanAccountNumber(paymentRecord.LoanAccountNumber)
		if accountNumberErr != nil {
			result = append(result, &PaymentPostingOutput{
				PaymentRecord: paymentRecord,
				PostingStatus: fetchAccountNumberPostingStatus,
				Remark:        fmt.Sprintf("error while fetch account number with error: %v", accountNumberErr),
			})
			continue
		}
		account, err := p.loanAccountsDao.GetByAccountNumberAndVendor(ctx, accountNumber, vendor)
		if err != nil {
			logger.Error(ctx, "error while fetching loan account using account number", zap.Error(err), zap.String("accountNumber", paymentRecord.LoanAccountNumber))
			result = append(result, &PaymentPostingOutput{
				PaymentRecord: paymentRecord,
				PostingStatus: failedPostingStatus,
				Remark:        fmt.Sprintf("error while fetching loan account using account number %s with error: %v", paymentRecord.LoanAccountNumber, err),
			})
			continue
		}
		paymentRefNumber := generatePaymentRefNumber(paymentRecord, account.GetId())
		resp, err := p.collectionClient.InitiateUpdatePayment(ctx, &collectionPb.InitiateUpdatePaymentRequest{
			CollectionHeader: &collectionTypesPb.Header{
				CollectionVendor: commonvgpb.Vendor_CREDGENICS,
				Product:          collectionTypesPb.Product_PRODUCT_LOAN,
				ProductVendor:    GetVendorFromLoanVendor(vendor),
			},
			PaymentDetails: &collectionPb.PaymentDetails{
				AccountId:     account.GetId(),
				MoneyReceived: amtReceived,
				RecoveryDate:  recoveryDate,
				ReferenceNo:   paymentRefNumber,
			},
		})

		if te := epifigrpc.RPCError(resp, err); te != nil {
			logger.Error(ctx, "error while calling Initiate update payment rpc", zap.Error(te), zap.String(logger.LOAN_ACCOUNT_ID, account.GetId()))
			if resp.GetStatus().IsAlreadyExists() {
				result = append(result, &PaymentPostingOutput{
					PaymentRecord: paymentRecord,
					PostingStatus: alreadyPostedPostingStatus,
				})
				continue
			}

			result = append(result, &PaymentPostingOutput{
				PaymentRecord: paymentRecord,
				PostingStatus: failedPostingStatus,
				Remark:        fmt.Sprintf("error while calling Initiate update payment rpc with error: %v", te),
			})
			continue
		}
		logger.Info(ctx, "successfully posted payment", zap.String(logger.LOAN_ACCOUNT_ID, account.GetId()))
		result = append(result, &PaymentPostingOutput{
			PaymentRecord: paymentRecord,
			PostingStatus: successfulPostingStatus,
			PaymentRefNum: paymentRefNumber,
		})
	}
	return result
}

func convertPaymentPostingOutputToCsvContent(data []*PaymentPostingOutput) (string, error) {
	// Create a buffer to store CSV content
	var buffer bytes.Buffer
	// Create a CSV writer
	writer := csv.NewWriter(&buffer)
	recordHeader := []string{"Account number", "Payment Date", "Amount", "Payment Status", "Remark", "Payment Posting Status", "Credgenics Payment Reference Number"}
	err := writer.Write(recordHeader)
	if err != nil {
		return "", errors.Wrap(err, "Error while writing csv header")
	}
	// Write records to CSV
	for _, pay := range data {
		record := []string{pay.PaymentRecord.LoanAccountNumber, pay.PaymentRecord.PaymentDate, strconv.FormatFloat(pay.PaymentRecord.Amount, 'f', -1, 64), pay.PaymentRecord.PaymentStatus, pay.Remark, pay.PostingStatus, pay.PaymentRefNum}
		err = writer.Write(record)
		if err != nil {
			return "", errors.Wrap(err, "Error while writing to csv")
		}
	}
	// Flush the writer
	writer.Flush()
	// Check for errors during flush
	if err = writer.Error(); err != nil {
		return "", errors.Wrap(err, "Error while flushing csv writer")
	}
	return buffer.String(), nil
}

//nolint:gocritic
func (p *PostNachPaymentsToCredgenicsJob) writePaymentPostingRecordToS3(ctx context.Context, csvContent string, inputFileName string, vendor palPb.Vendor) error {
	currentTime := time.Now()
	currentTimeString := currentTime.Format("2006-01-02 15:04:05")
	// Replace spaces with hyphens
	currentTimeString = strings.Replace(currentTimeString, " ", "-", -1)
	filePath := fmt.Sprintf(ProcessedNachFileForCredgenicsS3Path, strings.ToLower(vendor.String()), inputFileName, currentTimeString)
	logger.Info(ctx, "writing output csv to S3", zap.String("filePath", filePath))
	err := p.palS3Client.Write(ctx, filePath, []byte(csvContent), "")
	if err != nil {
		return errors.Wrap(err, "error writing csv to output path")
	}
	return nil
}

func generatePaymentRefNumber(record *PaymentRecord, loanAccountId string) string {
	hashedLoanAccountId := crypto.GetSHA256Hash("LoanAccountId:", loanAccountId)
	return fmt.Sprintf(paymentRefNumFormat, hashedLoanAccountId, record.Amount, record.PaymentDate)
}

func getLoanAccountNumber(loanAccNumber string) (string, error) {
	// this prefix cut is done for LL case, and will not impact other vendors if we dont use the same prefix
	accNum, found := strings.CutPrefix(loanAccNumber, liquiloansLoanAccNumberPrefixWithZero)
	if !found {
		accNum, found = strings.CutPrefix(loanAccNumber, liquiloansLoanAccNumberPrefix)
		if !found {
			return "", errors.New("unable to get the account number")
		}
	}
	return accNum, nil
}

// GetVendorFromLoanVendor converts palPb.Vendor to commonvgpb.Vendor
func GetVendorFromLoanVendor(loanVendor palPb.Vendor) commonvgpb.Vendor {
	switch loanVendor {
	case palPb.Vendor_LIQUILOANS:
		return commonvgpb.Vendor_LIQUILOANS
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		return commonvgpb.Vendor_STOCK_GUARDIAN_LSP
	default:
		return commonvgpb.Vendor_VENDOR_UNSPECIFIED
	}
}
