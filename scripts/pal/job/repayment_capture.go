//nolint:all
package job

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	collection "github.com/epifi/be-common/pkg/epifitemporal/namespace/collection"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	collectionPb "github.com/epifi/gamma/api/collection"
	collectionTypesPb "github.com/epifi/gamma/api/collection/types"
	"github.com/epifi/gamma/api/collection/workflow"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/dao/model"
	"github.com/epifi/gamma/scripts/pal/helper"
	"github.com/epifi/gamma/scripts/pal/job/vendors"
)

var (
	lenderToCollectionsEligibleLoanProgrammes = map[palPb.Vendor][]palPb.LoanProgram{
		palPb.Vendor_STOCK_GUARDIAN_LSP: {palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION},
		palPb.Vendor_LIQUILOANS: {palPb.LoanProgram_LOAN_PROGRAM_FLDG, palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY, palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND,
			palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL, palPb.LoanProgram_LOAN_PROGRAM_STPL, palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION, palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL},
	}
)

type RepaymentCaptureJob struct {
	dbResourceProvider    *storageV2.DBResourceProvider[*gorm.DB]
	helper                *helper.Helper
	loanInstallmentInfo   dao.LoanInstallmentInfoDao
	loanInstallmentPayout dao.LoanInstallmentPayoutDao
	loanActivity          dao.LoanActivityDao
	txnExecutorProvider   *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]
	palLlVgClient         palVgPb.LiquiloansClient
	palClient             palPb.PreApprovedLoanClient
	collectionClient      collectionPb.CollectionClient
	celestialClient       celestialPb.CelestialClient
}

func NewRepaymentCaptureJob(
	dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB],
	helper *helper.Helper,
	loanInstallmentInfo dao.LoanInstallmentInfoDao,
	loanInstallmentPayout dao.LoanInstallmentPayoutDao,
	loanActivity dao.LoanActivityDao,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	palLlVgClient palVgPb.LiquiloansClient,
	palClient palPb.PreApprovedLoanClient,
	collectionClient collectionPb.CollectionClient,
	celestialClient celestialPb.CelestialClient,
) *RepaymentCaptureJob {
	return &RepaymentCaptureJob{
		dbResourceProvider:    dbResourceProvider,
		helper:                helper,
		loanInstallmentInfo:   loanInstallmentInfo,
		loanInstallmentPayout: loanInstallmentPayout,
		loanActivity:          loanActivity,
		txnExecutorProvider:   txnExecutorProvider,
		palLlVgClient:         palLlVgClient,
		palClient:             palClient,
		collectionClient:      collectionClient,
		celestialClient:       celestialClient,
	}
}

// REPAYMENT_CAPTURE will call RefreshLMSSchedule Rpc to mirror repayment schedule from vendor's end
// jobArguments {"actorIds":["AC230307wQpyRyAJSJ+ii66ZKGsHcA==","AC230307wQpyRyAJSJ+ii3rZKGsHcA=="],"vendor":"FEDERAL","loanProgram":"LOAN_PROGRAM_PRE_APPROVED_LOAN"}
// jobArguments {"actorIds":[],"vendor":"LIQUILOANS","loanProgram":"LOAN_PROGRAM_EARLY_SALARY"}
// jobArguments {"actorIds":[],"vendor":"LIQUILOANS","loanProgram":"LOAN_PROGRAM_FLDG"}
// jobArguments {"actorIds":[],"vendor":"LIQUILOANS","loanProgram":"LOAN_PROGRAM_ACQ_TO_LEND","initiateCollection": true}
// jobArguments {"actorIds":[],"vendor":"LIQUILOANS","loanProgram":"LOAN_PROGRAM_FLDG", "initiateCollection": true }
// jobArguments {"actorIds":[],"vendor":"FIFTYFIN","loanProgram":"LOAN_PROGRAM_LAMF", "initiateCollection": false }

// empty actor id will fetch all loan accounts in active state
type RepaymentCaptureJobArgs struct {
	ActorIds           []string          `json:"actorIds"`
	Vendor             palPb.Vendor      `json:"vendor"`
	LoanProgram        palPb.LoanProgram `json:"loanProgram"`
	InitiateCollection bool              `json:"initiateCollection"`
}

func (p *RepaymentCaptureJob) GetArgs() interface{} {
	return &RepaymentCaptureJobArgs{}
}

const defaultNumGoroutines = 20

var (
	maxOfGoroutinesPerVendor = map[palPb.Vendor]int{
		palPb.Vendor_FIFTYFIN:   1,
		palPb.Vendor_IDFC:       1,
		palPb.Vendor_LIQUILOANS: 1,
		palPb.Vendor_FEDERAL:    1,
		palPb.Vendor_LENDEN:     1, // Only 1 goroutine to ensure no more than 1 call at a time
		palPb.Vendor_ABFL:       1,
	}
	requestInitWaitTimeVendorMap = map[palPb.Vendor]time.Duration{
		palPb.Vendor_FIFTYFIN: 5 * time.Second,
		// LL rate limit is 5k calls per 5 min across all APIs
		// 5 calls per sec * 60 sec * 5 min = 1500 calls in 5 min
		palPb.Vendor_LIQUILOANS: 200 * time.Millisecond,
		palPb.Vendor_IDFC:       5 * time.Second,
		palPb.Vendor_MONEYVIEW:  1 * time.Second,
		// Refreshing the LMS schedule for federal loan accounts depends on making a call to Federal's FetchLoanDetails endpoint
		// This endpoint has a rate limit of 10 calls per minute. Hence, we call it once every 6 seconds.
		palPb.Vendor_FEDERAL: 6 * time.Second,
		palPb.Vendor_LENDEN:  4 * time.Second, // 15 calls per minute = 1 call every 4 seconds
		palPb.Vendor_ABFL:    3 * time.Second, // 200 calls per 5 minute -> 1 call every 2 seconds -> restricting to 1 call every 3 seconds to allow organic load also
	}
)

// nolint:funlen
func (p *RepaymentCaptureJob) Run(ctx context.Context, args ...interface{}) error {
	var (
		actorIds           = args[0].(*RepaymentCaptureJobArgs).ActorIds
		vendor             = args[0].(*RepaymentCaptureJobArgs).Vendor
		loanProgram        = args[0].(*RepaymentCaptureJobArgs).LoanProgram
		initiateCollection = args[0].(*RepaymentCaptureJobArgs).InitiateCollection

		loanHeader = &palPb.LoanHeader{
			LoanProgram: loanProgram,
			Vendor:      vendor,
		}
	)
	var successActorsArr []string
	var failedActorsArr []string
	var successDelinquencyLoansArr []string
	var failedDelinquencyHandlingAccountIds []string
	var refreshLmsErrors []error

	ctx = epificontext.WithOwnership(ctx, vendors.GetPalOwnership(vendor))
	db, err := vendors.GetConnFromContextOrProvider(ctx, p.dbResourceProvider)
	if err != nil {
		return fmt.Errorf("error in getConnFromContextOrProvider")
	}
	var loanAccounts []*model.LoanAccount

	query := db.Where("status = ?", palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE).Where("loan_program = ?", loanProgram)
	if len(actorIds) > 0 {
		query = query.Where("actor_id IN (?)", actorIds)
	}

	dbResults := query.FindInBatches(&loanAccounts, 1000, func(tx *gorm.DB, batch int) error {
		var wg sync.WaitGroup
		wg.Add(len(loanAccounts))

		numOfGoroutines, exists := maxOfGoroutinesPerVendor[vendor]
		if !exists {
			numOfGoroutines = defaultNumGoroutines
		}
		ch := make(chan struct{}, numOfGoroutines)

		failedDelinquencyHandlingAccountIdsChannel := make(chan string, len(loanAccounts))
		successDeliquencyLoans := make(chan string, len(loanAccounts))
		successfulActorsChan := make(chan string, len(loanAccounts))
		failedActorsChan := make(chan string, len(loanAccounts))
		refreshLmsErrorsChannel := make(chan error, len(loanAccounts))
		for _, la := range loanAccounts {
			// Update context with actor id
			ch <- struct{}{}
			loanAccount := la
			gctx := epificontext.CtxWithActorId(ctx, loanAccount.ActorId)
			goroutine.Run(gctx, time.Minute, func(ctx context.Context) {
				defer func() {
					wg.Done()
					<-ch
				}()
				refreshErr := p.refreshLmsSchedule(ctx, loanHeader, loanAccount)
				if refreshErr != nil {
					if rpc.StatusFromError(refreshErr).GetCode() == uint32(palPb.RefreshLmsScheduleResponse_EXPECTED_DELAY_IN_LMS_UPDATE_AT_VENDOR) {
						logger.Info(ctx, "lms data not update at vendor yet", zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.Id))
					} else {
						logger.Error(ctx, "failed to refresh lms schedule for loan account id", zap.Error(refreshErr), zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.Id), zap.String(logger.ACTOR_ID_V2, loanAccount.ActorId))
						failedActorsChan <- loanAccount.ActorId
						refreshLmsErrorsChannel <- refreshErr
					}
				} else {
					successfulActorsChan <- loanAccount.ActorId

					if isLoanProgramEligibleForCollection(loanHeader.GetVendor(), loanHeader.GetLoanProgram()) && initiateCollection {
						delinquencyErr := p.handleLoanDelinquencyIfApplicable(ctx, loanAccount.GetProto())
						if delinquencyErr != nil {
							logger.Error(ctx, "failed to handle delinquency", zap.Error(delinquencyErr), zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.Id))
							failedDelinquencyHandlingAccountIdsChannel <- loanAccount.Id
						} else {
							successDeliquencyLoans <- loanAccount.Id
						}
					}
				}
			})

			if duration, ok := requestInitWaitTimeVendorMap[vendor]; ok {
				if initiateCollection {
					time.Sleep(2 * time.Second)
				} else {
					time.Sleep(duration)
				}
			}
		}
		timeout := waitgroup.SafeWait(&wg, 7*time.Hour)
		if !timeout {
			return fmt.Errorf("timeout while stopping services")
		}
		close(ch)
		close(failedDelinquencyHandlingAccountIdsChannel)
		close(successDeliquencyLoans)
		close(successfulActorsChan)
		close(refreshLmsErrorsChannel)
		close(failedActorsChan)

		for actorId := range successfulActorsChan {
			successActorsArr = append(successActorsArr, actorId)
		}
		for actorId := range failedActorsChan {
			failedActorsArr = append(failedActorsArr, actorId)
		}
		for loanId := range successDeliquencyLoans {
			successDelinquencyLoansArr = append(successDelinquencyLoansArr, loanId)
		}
		for loanId := range failedDelinquencyHandlingAccountIdsChannel {
			failedDelinquencyHandlingAccountIds = append(failedDelinquencyHandlingAccountIds, loanId)
		}
		for refreshLmsErr := range refreshLmsErrorsChannel {
			refreshLmsErrors = append(refreshLmsErrors, refreshLmsErr)
		}
		return nil
	})
	if dbResults.Error != nil {
		return fmt.Errorf("failed to find loan accounts in batches and recon LMS, %w", dbResults.Error)
	}

	logger.Info(ctx, fmt.Sprintf("successfulActors: %v", successActorsArr), zap.Int("success_count", len(successActorsArr)))
	logger.Info(ctx, fmt.Sprintf("failedActors: %v", failedActorsArr), zap.Int("failure_count", len(refreshLmsErrors)))

	logger.Info(ctx, fmt.Sprintf("failed delinquency loans: %v", failedDelinquencyHandlingAccountIds))
	logger.Info(ctx, fmt.Sprintf("success delinquency loans: %v", successDelinquencyLoansArr))

	// adding panic for failure cases so that scripts exits with a failed status code and we can add alerts on it.
	if len(refreshLmsErrors) > 0 {
		return fmt.Errorf("lms refresh failed for some loan accounts")
	}
	if len(failedDelinquencyHandlingAccountIds) > 0 {
		return fmt.Errorf("failed to handle some delinquent loan accounts")
	}

	return nil
}

func (p *RepaymentCaptureJob) refreshLmsSchedule(ctx context.Context, loanHeader *palPb.LoanHeader, loanAccount *model.LoanAccount) error {
	res, err := p.palClient.RefreshLMSSchedule(ctx, &palPb.RefreshLmsScheduleRequest{
		ActorId:    loanAccount.ActorId,
		LoanHeader: loanHeader,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return fmt.Errorf("failed to refresh LMS Schedule: %w", te)
	}
	return nil
}

func (p *RepaymentCaptureJob) handleLoanDelinquencyIfApplicable(ctx context.Context, loanAccount *palPb.LoanAccount) error {
	// check if any of the loan installment is overdue.
	loanScheduleRes, lsErr := p.palClient.GetLoanSchedule(ctx, &palPb.GetLoanScheduleRequest{
		LoanHeader: &palPb.LoanHeader{
			Vendor:      loanAccount.GetVendor(),
			LoanProgram: loanAccount.GetLoanProgram(),
		},
		LoanId: loanAccount.GetId(),
	})
	if lsErr = epifigrpc.RPCError(loanScheduleRes, lsErr); lsErr != nil {
		return fmt.Errorf("failed to get loan schedule, err : %w", lsErr)
	}

	isAnyOfTheInstallmentOverdue := false
	for _, installment := range loanScheduleRes.GetSchedule() {
		if isInstallmentOverdue(installment) {
			isAnyOfTheInstallmentOverdue = true
			break
		}
	}
	// if none of the installment is overdue, then the loan is not delinquent.
	if !isAnyOfTheInstallmentOverdue {
		return nil
	}

	// create allocation for the loan in collections system for delinquent loans.
	createAllocationErr := p.createLoanAllocationInCollectionSystem(ctx, loanAccount)
	if createAllocationErr != nil {
		return fmt.Errorf("failed to create allocation for the loan in collections system, err: %w", createAllocationErr)
	}
	return nil
}

func (p *RepaymentCaptureJob) createLoanAllocationInCollectionSystem(ctx context.Context, loanAccount *palPb.LoanAccount) error {
	marPayload, marPayloadErr := protojson.Marshal(&workflow.CreateAllocationPayload{
		CollectionHeader: &collectionTypesPb.Header{
			CollectionVendor: commonvgpb.Vendor_CREDGENICS,
			Product:          collectionTypesPb.Product_PRODUCT_LOAN,
			ProductVendor:    convertVendorEnumToVgEnum(loanAccount.GetVendor()),
		},
		AccountId: loanAccount.GetId(),
	})
	if marPayloadErr != nil {
		return fmt.Errorf("failed to marshal CreateAllocation workflow payload, err : %w", marPayloadErr)
	}
	// todo (utkarsh): verify if the allocation idempotency for the loan account is correctly handled in the workflow logic.
	initiateWorkflowRes, err := p.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: loanAccount.GetActorId(),
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(collection.CreateAllocation),
			Payload: marPayload,
			ClientReqId: &workflowPb.ClientReqId{
				Id:     uuid.NewString(),
				Client: workflowPb.Client_PRE_APPROVED_LOAN,
			},
			Ownership: vendors.GetPalOwnership(loanAccount.GetVendor()),
		},
	})
	if te := epifigrpc.RPCError(initiateWorkflowRes, err); te != nil {
		return fmt.Errorf("error while initiate CreateAllocation collections workflow, err: %w", te)
	}
	return nil
}

func isInstallmentOverdue(payout *palPb.LoanInstallmentPayout) bool {
	return datetime.IsDateBeforeTodayInLoc(payout.GetDueDate(), datetime.IST) &&
		(payout.GetStatus() == palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING || payout.GetStatus() == palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID)
}

func isLoanProgramEligibleForCollection(lender palPb.Vendor, loanProgram palPb.LoanProgram) bool {
	return lo.Contains(lenderToCollectionsEligibleLoanProgrammes[lender], loanProgram)
}

func convertVendorEnumToVgEnum(vendor palPb.Vendor) commonvgpb.Vendor {
	switch vendor {
	case palPb.Vendor_LIQUILOANS:
		return commonvgpb.Vendor_LIQUILOANS
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		return commonvgpb.Vendor_STOCK_GUARDIAN_LSP
	default:
		return commonvgpb.Vendor_VENDOR_UNSPECIFIED
	}
}
