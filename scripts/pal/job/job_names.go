package job

var (
	JobNames = map[string]Type{
		"PROCESS_FI_ELIGIBLE_BASE":                   ProcessFiEligibleBase,
		"PROCESS_ELIGIBLE_BASE_FROM_VENDOR":          ProcessEligibleBaseFromVendor,
		"DELETE_RISKY_USERS":                         DeleteRiskyUsers,
		"RETRY_FETCH_DETAILS_V2":                     RetryFetchDetailsV2,
		"GET_STATEMENT":                              GetStatement,
		"POPULATE_LEC_FROM_VENDOR_FILE":              PopulateLecFromVendorFile,
		"POPULATE_LEC_FROM_VENDOR":                   Populate<PERSON>ecFromVendor,
		"SEND_EMI_COMMS":                             SendE<PERSON>Comms,
		"CREATE_NEW_OFFER":                           <PERSON><PERSON><PERSON>ewOffer,
		"EXTEND_EXPIRY_FED_OFFERS":                   ExtendExpiryFedOffers,
		"PROCESS_LL_ELIGIBLE_BASE":                   ProcessLlEligibleBase,
		"CREDIT_REPORT_FLATTEN_ADHOC":                CreditReportFlattenAdhoc,
		"CREDIT_REPORT_FLATTEN":                      CreditReportFlatten,
		"UPDATE_INSTALLMENT_INFO":                    UpdateInstallmentInfo,
		"FETCH_LL_REJECTED_BASE":                     FetchLlRejectedBase,
		"TRIGGER_LL_ACTIVITY":                        TriggerLlActivity,
		"SET_LOAN_PROGRAM_IN_LOAN_OFFERS":            SetLoanProgramInLoanOffers,
		"BACKFILL_CKYC_VIDEO_PQ":                     BackfillCkycVideoPQ,
		"LOAN_ACCOUNT_RECON":                         LoanAccountRecon,
		"CRYPT_S3_DATA":                              CryptS3Data,
		"REPAYMENT_CAPTURE":                          RepaymentCapture,
		"LOAN_ACTIVITY_BACKFILL":                     LoanActivityBackFill,
		"GENERATE_VENDOR_BUREAU_FILE":                GenerateVendorBureauFile,
		"PROCESS_SCRUB_DATA":                         ProcessScrubData,
		"PROCESS_ENCRYPTED_SCRUB_DATA":               ProcessEncryptedScrubData,
		"DISABLE_OFFERS":                             DisableOffers,
		"LOAN_SCHEME_BACKFILL":                       LoanSchemeBackfill,
		"SYNC_LOAN_ACCOUNT":                          SyncLoanAccount,
		"DEACTIVATE_LL_OFFERS":                       DeactivateAllOffers,
		"DEACTIVATE_OFFER_IF_LOAN_TAKEN":             DeactivateOfferIfLoanTaken,
		"CLEANUP_PAYOUTS":                            CleanUpPayouts,
		"SI_COLLECTION":                              SiCollection,
		"RESOLVE_LOEC":                               ResolveLOEC,
		"BACKFILL_LOAN_ACCOUNT_ID":                   BackfillLoanAccountId,
		"RESOLVE_EMI_DEFAULTS":                       ResolveEMIDefaults,
		"ES_ESIGN_TRIGGER":                           EsEsignTrigger,
		"CREATE_IDFC_CUG_OFFERS":                     CreateIdfcCugOffers,
		"DELETE_LOAN_ACCOUNT":                        DeleteLoanAccount,
		"GET_ADDRESS":                                GetAddress,
		"FIX_LOAN_APPLICANT":                         FixLoanApplicant,
		"RESET_CHARGES_AMOUNT":                       ResetChargesAmount,
		"BACKFILL_UTR":                               BackfillUTR,
		"BUREAU_REPORTING_SUBVENTION":                BureauReportingSubvention,
		"UPDATE_VENDOR_REQ_ID_IDFC":                  UpdateVendorReqIdIdfc,
		"BRE_PIPING":                                 BrePiping,
		"SYNC_LOAN_ACCOUNT_LL":                       SyncLoanAccountLL,
		"DELETE_DUPLICATE_LOAN_LII":                  DeleteDuplicateLoanLii,
		"DEACTIVATE_FEDERAL_OFFERS":                  DeactivateFederalOffer,
		"MV_PAN_DEDUPE":                              MvPanDedupe,
		"PROCESS_PREPAYMENT_AT_VENDOR":               ProcessPrepaymentAtVendor,
		"FETCH_REPAYMENT_SCHEDULE":                   FetchRepaymentSchedule,
		"FETCH_LOAN_STATUS":                          FetchLoanStatus,
		"LAMF_RESET_FAILED_OFFER_GEN_LSE":            LamfResetFailedOfferGenLse,
		"BACKFILL_LOAN_ACTIVITIES_UTR":               BackfillLoanActivitiesUTR,
		"DELETE_USER_LOAN_DATA":                      DeleteUserLoanData,
		"NORMALIZE_CIBIL":                            NormalizeCibil,
		"COLLECTIONS_SYNC_LEAD":                      CollectionsSyncLead,
		"BACKFILL_CIBIL_CUST_ID_MISMATCH":            BfCibilCustIdMismatch,
		"BACKFILL_CIBIL_REPORTS":                     BackFillCibilReports,
		"RECON_LOAN_PRE_CLOSURE":                     ReconLoanPreClosure,
		"LAMF_MANUAL_RESOLUTION":                     LamfManualResolution,
		"COLLECTIONS_ALLOCATION_SYNC":                CollectionsAllocationSync,
		"PROCESS_BILLZY_PAYMENTS":                    ProcessBillzyPayments,
		"LMS_DATA_DIFFERENCE":                        LmsDataDifference,
		"FILL_FEDERAL_DPD_USER_DATA":                 FillFederalDpdUserData,
		"UPLOAD_ITR_FILE_TO_SFTP":                    UploadITRFileToSFTP,
		"LAMF_CREATE_INTEREST_ACCOUNT_PI":            LamfCreateInterestAccountPi,
		"CREDIT_REPORT_DATA_RECOVERY":                CreditReportDataRecovery,
		"AUTO_PAY":                                   AutoPay,
		"POST_NACH_PAYMENTS_TO_CREDGENICS":           PostNachPaymentsToCredgenics,
		"SOFT_DELETE_CREDIT_REPORT_DATA":             SoftDeleteCreditReportData,
		"SOFT_DELETE_OLD_CREDIT_REPORT_DATA":         SoftDeleteOldCreditReportData,
		"HARD_DELETE_FLATTENED_CREDIT_REPORT_DATA":   HardDeleteFlattenedCreditReportData,
		"HARD_DELETE_FLATTENED_CREDIT_REPORT_DATA_2": HardDeleteFlattenedCreditReportData2,
		"ONE_TIME":                                   OneTime,
		"UPDATE_CREDIT_REPORT_DOWNLOAD_DATA":         UpdateCreditReportDownloadData,
		"APPLY_OFFER_DISCOUNT_AFTER_USER_DROPOFF":    ApplyOfferDiscountAfterUserDropoff,
		"HARD_DELETE_FLATTENED_CIBIL_REPORT_DATA":    HardDeleteFlattenedCibilReportData,
		"SOFT_DELETE_OLD_CIBIL_REPORT_DATA":          SoftDeleteOldCibilReportData,
		"DEACTIVATE_ALL_LOAN_APPLICATION":            DeactivateAllLoanApplication,
		"PURGE_CKYC_DATA":                            PurgeCkycData,
		"ABFL_REGULATORY_DATA":                       AbflRegulatoryData,
		"LOAN_ACCOUNT_CREATION_LAMF_JOB":             LoanAccountCreationLAMF,
		"STORE_EXPERIAN_CONSENTS":                    StoreExperianConsents,
		"UPDATE_LR_LSE_STATUS_JOB":                   UpdateLrLseStatus,
		"SYNC_ALL_ACTIVE_LEADS_COLLECTIONS":          SyncAllActiveLeadsCollections,
		"LIQUILOANS_REPOST_HOLD_PAYMENTS":            LiquiloansRepostHoldPayments,
		"UPDATE_LOAN_OFFER_CONSTRAINTS":              UpdateLoanOfferConstraints,
		"INITIATE_ANALYSIS_JOB":                      InitiateAnalysis,
		"UPDATE_ABFL_PWA_LOAN_STATUS":                UploadAbflPwaLoanApplicationStatus,
		"BACKFILL_REJECTED_BRE_DATA":                 BackfillRejectedBreData,
		"RETRY_DEDUPE":                               RetryDedupe,
		"EPFO_DATA_FETCH":                            EpfoDataFetch,
		"FED_LENTRA_CUG_TESTING":                     FedLentraCugTesting,
		"LOAN_FUNNEL_TRACKING":                       LoanFunnelTracking,
		"UPDATE_FEDERAL_LOAN_REQUEST_VENDOR_ID":      UpdateFederalLoanRequestVendorId,
		"LOANS_ONBOARDING":                           LoansOnboarding,
		"LIQUILOANS_GET_SOA":                         LiquiloansGetSoa,
		"UPDATE_LOAN_REQUEST_SUB_STATUS_NON_PROD":    UpdateLoanRequestSubStatusNonProdJob,
		"LOEC_BACKFILLING":                           LOECBackfillingJob,
		"REFRESH_USER_LEAD":                          RefreshUserLead,
		"PURGE_LENDER_DATA_JOB":                      PurgeLenderDataJob,
	}
)
