//nolint:all
package job

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"google.golang.org/genproto/googleapis/type/money"
	"net/url"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"gorm.io/gorm"

	"github.com/google/uuid"
	"github.com/pkg/errors"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	moneyPkg "github.com/epifi/be-common/pkg/money"
	commsPb "github.com/epifi/gamma/api/comms"
	leadsPb "github.com/epifi/gamma/api/leads"
	preapprovedloanPb "github.com/epifi/gamma/api/preapprovedloan"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/dao/model"
	pal "github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/scripts/pal/config"
)

// Step names
const (
	StepUnknown                   = "UNKNOWN STEP"
	StepEligibilityFLOW           = "ELIGIBILITY_FLOW"
	StepWebEligibilityFLOW        = "WEB_ELIGIBILITY_FLOW"
	StepBRE                       = "BRE"
	StepApplicationStarted        = "APPLICATION_STARTED"
	StepKYC                       = "KYC"
	StepVendorOffer               = "VENDOR_OFFER"
	StepDisbursal                 = "DISBURSAL"
	StepMandate                   = "MANDATE"
	StepNoActorFound              = "NO_ACTOR_FOUND"
	StepUserFoundNotInLoansFunnel = "USER_FOUND_NOT_IN_LOANS_FUNNEL"
	StepOfferGeneration           = "OFFER_GENERATION(APP)"
	StepWebOfferGeneration        = "OFFER_GENERATION(WEB)"
)

// Status values
const (
	StatusUnknown    = "UNKNOWN STATUS"
	StatusSuccess    = "SUCCESS"
	StatusFailed     = "FAILED"
	StatusExpired    = "EXPIRED"
	StatusInProgress = "IN_PROGRESS"
	StatusNa         = "NA"
)

// UserData holds all necessary user information retrieved from the database.
type UserData struct {
	ActorID       string
	UserCreatedAt time.Time
	DsaName       string
	LeadCreatedAt time.Time
	UtmContent    string
}

// LoanFunnelTrackingJob is a job that tracks users' last steps in the loan funnel
type LoanFunnelTrackingJob struct {
	conf                            *config.Config
	usersClient                     userPb.UsersClient
	loanStepExecutionsDao           dao.LoanStepExecutionsDao
	loanOffersDao                   dao.LoanOffersDao
	loanOfferEligibilityCriteriaDao dao.LoanOfferEligibilityCriteriaDao
	dbResourceProvider              *storage.DBResourceProvider[*gorm.DB]
	s3Client                        s3.S3Client
	epifiDb                         *gorm.DB
	commsClient                     commsPb.CommsClient
}

// NewLoanFunnelTrackingJob creates a new LoanFunnelTrackingJob
func NewLoanFunnelTrackingJob(
	conf *config.Config,
	usersClient userPb.UsersClient,
	loanStepExecutionsDao dao.LoanStepExecutionsDao,
	loanOffersDao dao.LoanOffersDao,
	loanOfferEligibilityCriteriaDao dao.LoanOfferEligibilityCriteriaDao,
	dbResourceProvider *storage.DBResourceProvider[*gorm.DB],
	s3Client s3.S3Client,
	epifiDb types.EpifiCRDB,
	commsClient commsPb.CommsClient,
) *LoanFunnelTrackingJob {
	return &LoanFunnelTrackingJob{
		conf:                            conf,
		usersClient:                     usersClient,
		loanStepExecutionsDao:           loanStepExecutionsDao,
		loanOffersDao:                   loanOffersDao,
		loanOfferEligibilityCriteriaDao: loanOfferEligibilityCriteriaDao,
		dbResourceProvider:              dbResourceProvider,
		s3Client:                        s3Client,
		epifiDb:                         epifiDb,
		commsClient:                     commsClient,
	}
}

// UserInputItem represents a single user phone number from the input CSV
type UserInputItem struct {
	PhoneNumber string
}

// SkippedItem represents an item that was skipped during processing
type SkippedItem struct {
	PhoneNumber string
	Reason      string
	RowNumber   int
}

// LoanFunnelTrackingJobArgs contains args for the job itself
type LoanFunnelTrackingJobArgs struct {
	StartDate     string   `json:"start_date"`
	EndDate       string   `json:"end_date"`
	UseLeadsTable bool     `json:"use_leads_table"`
	SkipEmail     bool     `json:"skip_email"`
	DSAList       []string `json:"dsa_list,omitempty"`
}

// LoanFunnelResult represents data for the output CSV
type LoanFunnelResult struct {
	DSAName             string    `json:"dsa_name"`
	CustomerContactNo   string    `json:"customer_contact_no"`
	LastStageDate       time.Time `json:"last_stage_date,omitempty"`
	UserLastStep        string    `json:"user_last_step"`
	StatusOfLastStep    string    `json:"status_of_last_step"`
	RejectionReason     string    `json:"rejection_reason,omitempty"`
	UserAction          string    `json:"user_action,omitempty"`
	DSAAction           string    `json:"dsa_action,omitempty"`
	TroubleshootingTips string    `json:"troubleshooting_tips,omitempty"`
	FocusPriority       string    `json:"focus_priority,omitempty"`
	UserCreationDate    time.Time `json:"user_creation_date,omitempty"`
	LeadCreationDate    time.Time `json:"lead_creation_date,omitempty"`
	VendorOfferValue    float64   `json:"vendor_offer_value,omitempty"`
	DisbursedAmount     float64   `json:"disbursed_amount,omitempty"`
	Platform            string    `json:"platform,omitempty"`
	UtmContent          string    `json:"utm_content,omitempty"`
}

func (j *LoanFunnelTrackingJob) GetArgs() interface{} {
	return &LoanFunnelTrackingJobArgs{}
}

// doc link: https://docs.google.com/document/d/1TnHRhST-HbrNMcbKUBIuDngHW-aDRykJahK_LO13hsQ/edit?tab=t.0

func (j *LoanFunnelTrackingJob) Run(ctx context.Context, args ...interface{}) error {
	jobArgs := args[0].(*LoanFunnelTrackingJobArgs)

	// Case 1: CSV file input
	if len(args[1].([]string)) > 0 && args[1].([]string)[0] != "" {
		jobFilePath := args[1].([]string)[0]
		return j.runWithCSVInput(ctx, jobFilePath)
	}

	// Case 2: Date range input
	if jobArgs.StartDate != "" || jobArgs.EndDate != "" {
		return j.runWithDateRangeInput(ctx, jobArgs.StartDate, jobArgs.EndDate, false)
	}

	// Case 3: Fetch only from user_leads table
	if jobArgs.UseLeadsTable {
		return j.runWithLeadsTableInput(ctx, !jobArgs.SkipEmail, jobArgs.DSAList)
	}

	// Case 4: No input provided - default to last 15 days
	today := time.Now().In(datetime.IST)
	endTime := today
	startTime := today.AddDate(0, 0, -15)
	return j.runWithDateRangeInput(ctx, startTime.Format("2006-01-02"), endTime.Format("2006-01-02"), true)
}

func (j *LoanFunnelTrackingJob) runWithLeadsTableInput(ctx context.Context, doMail bool, dsaList []string) error {
	usersDataMap, err := j.getUsersDataMapFromLeads(dsaList)
	if err != nil {
		return errors.Wrap(err, "error getUsersDataMapFromLeads")
	}

	var phoneNumbers []string
	for phone := range usersDataMap {
		phoneNumbers = append(phoneNumbers, phone)
	}

	results, err := j.processUsersInBatches(ctx, usersDataMap, phoneNumbers, doMail)
	if err != nil {
		return errors.Wrap(err, "error processing users from lead table input")
	}

	logger.Info(ctx, "Completed Loan Funnel Tracking job for lead table input",
		zap.Int("processed_count", len(results)))
	return nil
}

func (j *LoanFunnelTrackingJob) getUsersDataMapFromLeads(dsaList []string) (map[string]*UserData, error) {
	// Internal struct to scan DB result.
	type leadModel struct {
		ID                string                     `gorm:"column:id;primaryKey"`
		ActorID           string                     `gorm:"column:actor_id"`
		MobileNumber      string                     `gorm:"column:mobile_number"`
		ClientRequestId   string                     `gorm:"column:client_request_id"`
		ClientId          string                     `gorm:"column:client_id"`
		CreatedAt         time.Time                  `gorm:"column:created_at"`
		AdditionalDetails *leadsPb.AdditionalDetails `gorm:"column:additional_details"`
		ExpiredAt       time.Time `gorm:"column:expired_at"`
		LeadStatus      string    `gorm:"column:lead_status"`
	}
	leadsDb, err := j.dbResourceProvider.GetResourceForOwnership(commontypes.Ownership_EPIFI_TECH_V2)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}

	query := leadsDb.Table("user_leads").
		Select("id,client_id,mobile_number,actor_id,expired_at,created_at,additional_details").
		Where("expired_at > ?", time.Now().AddDate(0, 0, -1))

	// Add DSA filter if DSAList is provided
	if len(dsaList) > 0 {
		query = query.Where("client_id IN (?)", dsaList)
	}

	usersDataMap := make(map[string]*UserData)
	batchSize := 1000

	var leads []*leadModel
	err = query.FindInBatches(&leads, batchSize, func(tx *gorm.DB, batch int) error {
		for _, u := range leads {
			// Filter out expired users and users with rejected lead status that are not converted
			if u.ExpiredAt.Before(time.Now()) && u.LeadStatus != "USER_LEAD_STATUS_CONVERTED" {
				continue
			}
			userData := &UserData{
				ActorID:       u.ActorID,
				DsaName:       u.ClientId,
				LeadCreatedAt: u.CreatedAt,
			}

			userData.UtmContent = u.AdditionalDetails.GetUtmParameters().GetUtmContent()
			usersDataMap[u.MobileNumber] = userData
		}

		// Add a small delay between batches to prevent overwhelming the database
		if batch > 0 {
			time.Sleep(100 * time.Millisecond)
		}
		return nil
	}).Error

	if err != nil {
		return nil, errors.Wrap(err, "error fetching leads in batches")
	}

	return usersDataMap, nil
}

func (j *LoanFunnelTrackingJob) runWithCSVInput(ctx context.Context, filePath string) error {
	inputItems, skippedItems, err := readCSV(filePath)
	if err != nil {
		return errors.Wrap(err, "error reading input CSV")
	}

	if len(skippedItems) > 0 {
		logger.Info(ctx, "Skipped items during CSV processing",
			zap.Int("skipped_count", len(skippedItems)),
			zap.Any("skipped_items", skippedItems))
	}

	if len(inputItems) == 0 {
		return errors.New("no valid phone numbers found after filtering")
	}

	var phoneNumbers []string
	for _, item := range inputItems {
		phoneNumbers = append(phoneNumbers, item.PhoneNumber)
	}

	usersDataMap, err := j.getUsersDataMapFromDB(ctx, phoneNumbers)
	if err != nil {
		return errors.Wrap(err, "failed to get user data from DB for CSV input")
	}

	results, err := j.processUsersInBatches(ctx, usersDataMap, phoneNumbers, false)
	if err != nil {
		return errors.Wrap(err, "error processing users from CSV input")
	}

	logger.Info(ctx, "Completed Loan Funnel Tracking job for CSV input",
		zap.Int("processed_count", len(results)),
		zap.Int("skipped_count", len(skippedItems)))

	return nil
}

func (j *LoanFunnelTrackingJob) runWithDateRangeInput(ctx context.Context, startDate, endDate string, doMail bool) error {
	// Parse dates for validation
	startTime, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return errors.Wrap(err, "invalid start date format. Expected format: YYYY-MM-DD")
	}

	endTime, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return errors.Wrap(err, "invalid end date format. Expected format: YYYY-MM-DD")
	}

	// Calculate the difference in days
	daysDiff := endTime.Sub(startTime).Hours() / 24
	if daysDiff > 30 {
		return errors.New("date range must be less than 31 days")
	}

	usersDataMap, err := j.getUsersDataByDateRange(ctx, startTime, endTime)
	if err != nil {
		return errors.Wrap(err, "failed to get user data from DB for date range input")
	}

	if len(usersDataMap) == 0 {
		logger.Info(ctx, "No users found for the given date range and criteria.")
		return nil
	}

	var phoneNumbers []string
	for phone := range usersDataMap {
		phoneNumbers = append(phoneNumbers, phone)
	}

	results, err := j.processUsersInBatches(ctx, usersDataMap, phoneNumbers, doMail)
	if err != nil {
		return errors.Wrap(err, "error processing users from date range input")
	}

	logger.Info(ctx, "Completed Loan Funnel Tracking job for date range input",
		zap.Int("processed_count", len(results)))
	return nil
}

// processUsersInBatches processes all users by breaking them into smaller batches.
func (j *LoanFunnelTrackingJob) processUsersInBatches(ctx context.Context, usersDataMap map[string]*UserData, allPhoneNumbers []string, doMail bool) ([]*LoanFunnelResult, error) {
	logger.Info(ctx, "Starting user processing in batches", zap.Int("total_users", len(allPhoneNumbers)))

	// Group results by utm_campaign
	dsaToResult := make(map[string][]*LoanFunnelResult)
	var allResults []*LoanFunnelResult

	for i := 0; i < len(allPhoneNumbers); i += 1000 {
		end := i + 1000
		if end > len(allPhoneNumbers) {
			end = len(allPhoneNumbers)
		}
		batchPhoneNumbers := allPhoneNumbers[i:end]

		// Create a user data map for just the current batch
		batchUsersDataMap := make(map[string]*UserData)
		for _, phone := range batchPhoneNumbers {
			if userData, ok := usersDataMap[phone]; ok {
				batchUsersDataMap[phone] = userData
			}
		}

		batchResults, err := j.processUserBatch(ctx, batchUsersDataMap, batchPhoneNumbers)
		if err != nil {
			logger.Error(ctx, "Error processing user batch", zap.Error(err), zap.Int("batch_start", i))
			// Continue processing other batches
		}

		for _, result := range batchResults {
			// ignore last step if step time is before lead creation time because what the user had done before the lead was created is not a concern to DSA
			if !result.LeadCreationDate.IsZero() && !result.LastStageDate.IsZero() && result.LeadCreationDate.After(result.LastStageDate) {
				result.UserLastStep = StepUserFoundNotInLoansFunnel
				result.StatusOfLastStep = StatusNa
				result.LastStageDate = time.Time{}
				result.RejectionReason = ""
				result.DisbursedAmount = 0
			}

			// group by dsa name only if mail needs to be sent to DSAs, otherwise club all results into one
			if !doMail {
				allResults = append(allResults, result)
				continue
			}
			if result.DSAName == "" {
				continue
			}
			dsaToResult[result.DSAName] = append(dsaToResult[result.DSAName], result)
		}

		if end < len(allPhoneNumbers) {
			time.Sleep(1 * time.Second)
		}
	}

	// For each dsa, get email from config and send report
	for dsa, results := range dsaToResult {
		emails := j.conf.DSAIdToEmailMap[dsa]
		if len(emails) == 0 {
			logger.Info(ctx, "No email mapped for dsa", zap.String("dsa", dsa))
			continue
		}
		err := j.writeResultsToCSV(ctx, results, emails, dsa, doMail)
		if err != nil {
			logger.Error(ctx, "Error writing results to CSV", zap.Error(err), zap.String("dsa", dsa))
		}
	}

	if !doMail {
		err := j.writeResultsToCSV(ctx, allResults, nil, "", doMail)
		if err != nil {
			return nil, errors.Wrap(err, "error processing results to CSV")
		}
	}

	return allResults, nil
}

func (j *LoanFunnelTrackingJob) getUsersDataByDateRange(ctx context.Context, startTime, endTime time.Time) (map[string]*UserData, error) {
	// Internal struct to scan DB result.
	type dbUser struct {
		ID                  string          `gorm:"column:id;primaryKey"`
		ActorID             string          `gorm:"column:actor_id"`
		ComputedPhoneNumber string          `gorm:"column:computed_phone_number"`
		AcquisitionInfo     json.RawMessage `gorm:"column:acquisition_info"`
		CreatedAt           time.Time       `gorm:"column:created_at"`
	}

	query := j.epifiDb.Table("users").
		Select("id, actor_id, computed_phone_number, acquisition_info, created_at").
		Where("acquisition_info->>'acquisitionIntent' = ?", "ACQUISITION_INTENT_PERSONAL_LOANS").
		Where("deleted_at_unix = ?", 0)

	// Set time to start of day in IST
	startTime = datetime.StartOfDay(startTime.In(datetime.IST))
	query = query.Where("updated_at >= ? AND created_at >= ?", startTime, startTime)

	// Set time to end of day in IST
	endTime = datetime.EndOfDay(endTime.In(datetime.IST))
	query = query.Where("updated_at <= ? AND created_at <= ?", endTime, endTime)

	usersDataMap := make(map[string]*UserData)
	batchSize := 1000

	// Declare the slice variable outside the callback
	var dbUsers []dbUser

	// Use FindInBatches to process users in batches
	err := query.FindInBatches(&dbUsers, batchSize, func(tx *gorm.DB, batch int) error {
		for _, u := range dbUsers {
			userData := &UserData{
				ActorID:       u.ActorID,
				UserCreatedAt: u.CreatedAt,
			}

			if len(u.AcquisitionInfo) > 0 {
				acqInfo := &userPb.AcquisitionInfo{}
				if err := protojson.Unmarshal(u.AcquisitionInfo, acqInfo); err == nil {
					if acqInfo.WebUrl != "" {
						campaign := extractUrlParameter(acqInfo.WebUrl, "utm_campaign")
						source := extractUrlParameter(acqInfo.WebUrl, "utm_source")
						if source == "dsa_individual" {
							userData.DsaName = campaign
						}
					}
				} else {
					logger.Error(ctx, "failed to unmarshal acquisition info", zap.String("actorId", u.ActorID), zap.Error(err))
				}
			}

			var nationalNumber string
			if len(u.ComputedPhoneNumber) >= 10 {
				nationalNumber = u.ComputedPhoneNumber[len(u.ComputedPhoneNumber)-10:]
			} else {
				logger.Warn("Invalid computed phone number found in DB", zap.String("phone_number", u.ComputedPhoneNumber))
				continue
			}

			dsaName, err := j.getDsaNameForActor(ctx, u.ActorID)
			if err == nil && dsaName != "" {
				userData.DsaName = dsaName
			}

			usersDataMap[nationalNumber] = userData
		}

		// Add a small delay between batches to prevent overwhelming the database
		if batch > 0 {
			time.Sleep(100 * time.Millisecond)
		}
		return nil
	}).Error

	if err != nil {
		return nil, errors.Wrap(err, "error processing users in batches")
	}

	return usersDataMap, nil
}

// getLoanStepExecutionsForDB fetches loan step executions from a specific database
func (j *LoanFunnelTrackingJob) getLoanStepExecutionsForDB(actorIds []string, ownership commontypes.Ownership) (map[string][]*preapprovedloanPb.LoanStepExecution, error) {
	db, err := j.dbResourceProvider.GetResourceForOwnership(ownership)
	if err != nil {
		return nil, err
	}

	results := make(map[string][]*preapprovedloanPb.LoanStepExecution)

	if len(actorIds) == 0 {
		return results, nil
	}

	var loanStepExecutions []*model.LoanStepExecution
	query := db.Table("loan_step_executions")

	// Add details to select based on ownership using switch case
	switch ownership {
	case commontypes.Ownership_MONEYVIEW_PL, commontypes.Ownership_LOANS_ABFL:
		query = query.Select("DISTINCT ON (actor_id) actor_id, step_name, group_stage, status, created_at, updated_at, completed_at, details")
	default:
		query = query.Select("DISTINCT ON (actor_id) actor_id, step_name, group_stage, status, created_at, updated_at, completed_at")
	}

	err = query.
		Where("actor_id IN (?)", actorIds).
		Order("actor_id, updated_at DESC").
		Find(&loanStepExecutions).Error

	if err != nil {
		return nil, err
	}

	for _, lse := range loanStepExecutions {
		pbLse := lse.GetProto()
		actorId := lse.ActorId
		results[actorId] = append(results[actorId], pbLse)
	}

	return results, nil
}

// getLatestLOECsForDB fetches the latest Loan Offer Eligibility Checks from a specific database
func (j *LoanFunnelTrackingJob) getLatestLOECsForDB(actorIds []string, ownership commontypes.Ownership) (map[string][]*preapprovedloanPb.LoanOfferEligibilityCriteria, error) {
	db, err := j.dbResourceProvider.GetResourceForOwnership(ownership)
	if err != nil {
		return nil, err
	}

	results := make(map[string][]*preapprovedloanPb.LoanOfferEligibilityCriteria)

	if len(actorIds) == 0 {
		return results, nil
	}

	var loecs []*model.LoanOfferEligibilityCriteria
	err = db.Table("loan_offer_eligibility_criteria").
		Select("DISTINCT ON (actor_id) actor_id, status, created_at, updated_at, expired_at, vendor_response").
		Where("actor_id IN (?) and updated_at > ?", actorIds, time.Now().Add(-90*24*time.Hour)).
		Order("actor_id, updated_at DESC").
		Find(&loecs).Error

	if err != nil {
		return nil, err
	}

	for _, loec := range loecs {
		pbLoec := loec.GetProto()
		actorId := loec.ActorId
		results[actorId] = append(results[actorId], pbLoec)
	}

	return results, nil
}

// getLoanRequestsForDB fetches loan requests from a specific database
func (j *LoanFunnelTrackingJob) getLoanRequestsForDB(actorIds []string, ownership commontypes.Ownership) ([]*model.LoanRequest, error) {
	db, err := j.dbResourceProvider.GetResourceForOwnership(ownership)
	if err != nil {
		return nil, err
	}

	if len(actorIds) == 0 {
		return nil, nil
	}

	var loanRequests []*model.LoanRequest
	err = db.Table("loan_requests").
		Select("DISTINCT ON (actor_id) actor_id, vendor, status, type, loan_program, completed_at, created_at, updated_at").
		Where("actor_id IN (?)", actorIds).
		Order("actor_id, updated_at DESC").
		Find(&loanRequests).Error

	if err != nil {
		return nil, err
	}

	return loanRequests, nil
}

// getUsersDataMapFromDB queries users from the database based on a list of phone numbers
// and returns a map of phone number to UserData.
func (j *LoanFunnelTrackingJob) getUsersDataMapFromDB(ctx context.Context, phoneNumbers []string) (map[string]*UserData, error) {
	if len(phoneNumbers) == 0 {
		return nil, nil
	}

	// Internal struct to scan DB result.
	type dbUser struct {
		ID                  string          `gorm:"column:id;primaryKey"`
		ActorID             string          `gorm:"column:actor_id"`
		ComputedPhoneNumber string          `gorm:"column:computed_phone_number"`
		AcquisitionInfo     json.RawMessage `gorm:"column:acquisition_info"`
		CreatedAt           time.Time       `gorm:"column:created_at"`
	}

	fullPhoneNumbers := make([]string, len(phoneNumbers))
	for i, p := range phoneNumbers {
		fullPhoneNumbers[i] = "91" + p
	}

	var dbUsers []*dbUser
	err := j.epifiDb.Table("users").
		Select("id, actor_id, computed_phone_number, acquisition_info, created_at").
		Where("computed_phone_number IN (?)", fullPhoneNumbers).
		Where("deleted_at_unix = ?", 0).
		Find(&dbUsers).Error
	if err != nil {
		return nil, errors.Wrap(err, "error querying users by phone numbers")
	}

	usersDataMap := make(map[string]*UserData)
	for _, u := range dbUsers {
		userData := &UserData{
			ActorID:       u.ActorID,
			UserCreatedAt: u.CreatedAt,
		}

		if len(u.AcquisitionInfo) > 0 {
			acqInfo := &userPb.AcquisitionInfo{}
			if err := protojson.Unmarshal(u.AcquisitionInfo, acqInfo); err == nil {
				if acqInfo.WebUrl != "" {
					campaign := extractUrlParameter(acqInfo.WebUrl, "utm_campaign")
					source := extractUrlParameter(acqInfo.WebUrl, "utm_source")
					if source == "dsa_individual" {
						userData.DsaName = campaign
					}
				}
			} else {
				logger.Warn("failed to unmarshal acquisition info", zap.String("actorId", u.ActorID), zap.Error(err))
			}
		}

		var nationalNumber string
		if len(u.ComputedPhoneNumber) >= 10 {
			nationalNumber = u.ComputedPhoneNumber[len(u.ComputedPhoneNumber)-10:]
		} else {
			logger.Warn("Invalid computed phone number found in DB", zap.String("phone_number", u.ComputedPhoneNumber))
			continue
		}
		DsaName, err := j.getDsaNameForActor(ctx, u.ActorID)
		if err == nil && DsaName != "" {
			userData.DsaName = DsaName
		}
		usersDataMap[nationalNumber] = userData
	}

	return usersDataMap, nil
}

// processUserBatch processes a single batch of users.
func (j *LoanFunnelTrackingJob) processUserBatch(ctx context.Context, usersDataMap map[string]*UserData, phoneNumbers []string) ([]*LoanFunnelResult, error) {
	results := make([]*LoanFunnelResult, 0, len(phoneNumbers))

	var actorIds []string
	for _, data := range usersDataMap {
		if data.ActorID == "" {
			continue
		}
		actorIds = append(actorIds, data.ActorID)
	}

	// If no valid users found in the batch, return early
	if len(actorIds) == 0 {
		for _, phoneNumber := range phoneNumbers {
			dsaName := ""
			if usersDataMap[phoneNumber] != nil {
				dsaName = usersDataMap[phoneNumber].DsaName
			}
			results = append(results, &LoanFunnelResult{
				CustomerContactNo: phoneNumber,
				UserLastStep:      StepNoActorFound,
				StatusOfLastStep:  StatusNa,
				DSAName:           dsaName,
			})
		}
		return results, nil
	}

	// Get loan requests from all ownerships
	var allLoanRequests []*model.LoanRequest

	ownerships := []commontypes.Ownership{
		commontypes.Ownership_EPIFI_TECH_V2,
		commontypes.Ownership_MONEYVIEW_PL,
		commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP,
		commontypes.Ownership_LOANS_ABFL,
		commontypes.Ownership_LOANS_LENDEN,
		commontypes.Ownership_FEDERAL_BANK,
	}

	for _, ownership := range ownerships {
		ownershipLoanRequests, err := j.getLoanRequestsForDB(actorIds, ownership)
		if err != nil {
			logger.Error(ctx, "Error fetching loan requests",
				zap.String("ownership", ownership.String()),
				zap.Error(err))
			return nil, errors.Wrap(err, fmt.Sprintf("Error fetching loan requests for ownership: %s", ownership.String()))
		}
		allLoanRequests = append(allLoanRequests, ownershipLoanRequests...)
	}

	// Sort all loan requests by updated_at to get the latest ones
	sort.Slice(allLoanRequests, func(i, j int) bool {
		if allLoanRequests[i].UpdatedAt == nil {
			return false
		}
		if allLoanRequests[j].UpdatedAt == nil {
			return true
		}
		return allLoanRequests[i].UpdatedAt.After(*allLoanRequests[j].UpdatedAt)
	})

	logger.Info(ctx, "Latest loan requests after sorting",
		zap.Int("total_loan_requests", len(allLoanRequests)))

	actorToLoanRequestMap := make(map[string]*model.LoanRequest)

	for i := 0; i < len(allLoanRequests); i++ {
		lr := allLoanRequests[i]
		if lr == nil {
			return nil, errors.New("Nil loan request found in results")
		}
		// Only add to map if we haven't seen this actor before
		if _, exists := actorToLoanRequestMap[lr.ActorId]; !exists {
			actorToLoanRequestMap[lr.ActorId] = lr
		}
	}

	lsesByOwnership := make(map[commontypes.Ownership]map[string][]*preapprovedloanPb.LoanStepExecution)
	loecsByOwnership := make(map[commontypes.Ownership]map[string][]*preapprovedloanPb.LoanOfferEligibilityCriteria)

	for _, ownership := range ownerships {
		lseResults, err := j.getLoanStepExecutionsForDB(actorIds, ownership)
		if err != nil {
			logger.Error(ctx, "Error getting LSEs from DB",
				zap.String("ownership", ownership.String()),
				zap.Error(err))
			return nil, errors.Wrap(err, fmt.Sprintf("Error getting LSEs from DB for ownership: %s", ownership.String()))
		}
		lsesByOwnership[ownership] = lseResults

		loecResults, err := j.getLatestLOECsForDB(actorIds, ownership)
		if err != nil {
			logger.Error(ctx, "Error getting LOECs from DB",
				zap.String("ownership", ownership.String()),
				zap.Error(err))
			return nil, errors.Wrap(err, fmt.Sprintf("Error getting LOECs from DB for ownership: %s", ownership.String()))
		}
		loecsByOwnership[ownership] = loecResults
	}

	// Process each item and update results
	for _, phoneNumber := range phoneNumbers {
		userData, found := usersDataMap[phoneNumber]
		if !found || userData.ActorID == "" {
			dsaName := ""
			if usersDataMap[phoneNumber] != nil {
				dsaName = usersDataMap[phoneNumber].DsaName
			}
			results = append(results, &LoanFunnelResult{
				CustomerContactNo: phoneNumber,
				UserLastStep:      StepNoActorFound,
				StatusOfLastStep:  StatusNa,
				DSAName:           dsaName,
			})
			continue
		}

		actorId := userData.ActorID
		result := &LoanFunnelResult{
			CustomerContactNo: phoneNumber,
			Platform:          "app",
			UserCreationDate:  userData.UserCreatedAt,
			DSAName:           userData.DsaName,
			LeadCreationDate:  userData.LeadCreatedAt,
			UtmContent:        userData.UtmContent,
		}

		var allLOECs []*preapprovedloanPb.LoanOfferEligibilityCriteria
		var allLSEs []*preapprovedloanPb.LoanStepExecution

		for _, ownership := range ownerships {
			if lseMap, ok := lsesByOwnership[ownership]; ok {
				if lses, exists := lseMap[actorId]; exists {
					allLSEs = append(allLSEs, lses...)
				}
			}

			if loecMap, ok := loecsByOwnership[ownership]; ok {
				if loecs, exists := loecMap[actorId]; exists {
					allLOECs = append(allLOECs, loecs...)
				}
			}
		}

		latestLR, hasLR := actorToLoanRequestMap[actorId]
		var isTerminal bool
		if latestLR != nil {
			isTerminal = latestLR.CompletedAt.Valid
		}

		if latestLR != nil && latestLR.LoanProgram == preapprovedloanPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB && latestLR.Vendor == preapprovedloanPb.Vendor_EPIFI_TECH {
			result.Platform = "web"
		}

		// Log LOEC information
		if len(allLOECs) > 0 {
			sortLOECsByUpdatedAt(allLOECs)
		}

		// If Latest LR is successful and it is not from epifi_tech, means user have a active loan
		if latestLR != nil && latestLR.Vendor != preapprovedloanPb.Vendor_EPIFI_TECH && latestLR.Status == preapprovedloanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {

			result.UserLastStep = StepDisbursal
			result.StatusOfLastStep = StatusSuccess
			result.LastStageDate = *latestLR.UpdatedAt

			// Fetch disbursed amount for successful loan
			var disbursedAmountStr string
			ownership := pal.GetPalOwnership(latestLR.Vendor)
			db, err := j.dbResourceProvider.GetResourceForOwnership(ownership)
			if err != nil {
				return nil, err
			}
			err = db.Table("loan_accounts").
				Select("disbursed_amount").
				Where("actor_id = ?", actorId).
				Order("created_at DESC").
				Limit(1).
				Pluck("disbursed_amount", &disbursedAmountStr).Error
			if err != nil {
				logger.Error(ctx, "Error fetching disbursal amount", zap.String("actor_id", actorId), zap.Error(err))
			} else {
				var disbursedAmount money.Money
				if err := json.Unmarshal([]byte(disbursedAmountStr), &disbursedAmount); err != nil {
					return nil, errors.Wrap(err, "error unmarshalling disbursed amount")
				}
				result.DisbursedAmount = moneyPkg.ToFloat(&disbursedAmount)
				logger.Info(ctx, "Set disbursed amount for user",
					zap.String("actor_id", actorId),
					zap.Float64("disbursed_amount", result.DisbursedAmount))
			}
			results = append(results, result)
			continue
		}

		// Set Priority of User
		result.FocusPriority = getPriority(latestLR, allLOECs)

		// If latest LR is from epifi tech and is non-terminal, return the status based on latest step
		if latestLR != nil && !isTerminal && latestLR.Vendor == preapprovedloanPb.Vendor_EPIFI_TECH {
			if lseMap, ok := lsesByOwnership[commontypes.Ownership_EPIFI_TECH_V2]; ok {
				if lses, exists := lseMap[actorId]; exists && len(lses) > 0 {
					sortLSEsByUpdatedAt(lses)
					latestLSE := lses[0]

					ownership := commontypes.Ownership_EPIFI_TECH_V2
					loanProgram := preapprovedloanPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED
					if latestLR.LoanProgram != preapprovedloanPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
						loanProgram = latestLR.LoanProgram
					}

					step, status, userAction, dsaAction, troubleshootingTip := mapLSEToFunnelStep(latestLSE, ownership, loanProgram)
					result.UserLastStep = step
					result.StatusOfLastStep = status
					result.UserAction = userAction
					result.DSAAction = dsaAction
					result.TroubleshootingTips = troubleshootingTip
					result.LastStageDate = latestLSE.UpdatedAt.AsTime()

				}
			}

			results = append(results, result)
			continue
		}
		// If latest LR is not terminal and we found any valid LSE(except epifi_tech),then check for the latest LSE and return the status based on that
		if len(allLSEs) > 0 && !isTerminal && latestLR.Vendor != preapprovedloanPb.Vendor_EPIFI_TECH {
			sortLSEsByUpdatedAt(allLSEs)

			ownership := commontypes.Ownership_EPIFI_TECH_V2
			loanProgram := preapprovedloanPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED

			if hasLR {
				ownership = pal.GetPalOwnership(latestLR.Vendor)
				if latestLR.LoanProgram != preapprovedloanPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
					loanProgram = latestLR.LoanProgram
				}
			}

			latestLSE := allLSEs[0]
			step, status, userAction, dsaAction, troubleshootingTip := mapLSEToFunnelStep(latestLSE, ownership, loanProgram)
			result.UserLastStep = step
			result.StatusOfLastStep = status
			result.UserAction = userAction
			result.DSAAction = dsaAction
			result.TroubleshootingTips = troubleshootingTip
			result.LastStageDate = latestLSE.UpdatedAt.AsTime()

			results = append(results, result)
			continue
		}

		// If we have terminal LR as well as we don't have any LSEs, then return the status based on latest LOEC(for loan request type ELIGIBILITY)
		if latestLR != nil && latestLR.LoanProgram == preapprovedloanPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY && latestLR.CompletedAt.Valid {
			if len(allLOECs) > 0 {
				sortLOECsByUpdatedAt(allLOECs)

				var hasApproved, hasRejected, hasCreated bool
				allExpired := true

				for _, loec := range allLOECs {
					status := loec.GetStatus()
					switch status {
					case preapprovedloanPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED:
						hasApproved = true
					case preapprovedloanPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED:
						hasRejected = true
					default:
						hasCreated = true
					}

					// If any LOEC is not expired, then not all are expired
					if loec.GetExpiredAt() == nil || loec.GetExpiredAt().AsTime().After(time.Now()) {
						allExpired = false
					}
				}

				// If all LOECs are expired, set step name to USER_FOUND_NOT_IN_LOANS_FUNNEL
				if allExpired {
					result.UserLastStep = StepUserFoundNotInLoansFunnel
					result.StatusOfLastStep = StatusNa
					result.LastStageDate = allLOECs[0].GetUpdatedAt().AsTime()
					result.DSAAction = "Ask the user to retry the eligibility flow."
					result.UserAction = "Go through the eligibility flow again."
					results = append(results, result)
					continue
				}

				// Handle LOEC status cases
				switch {
				case hasRejected && !hasApproved && !hasCreated:
					result.UserLastStep = StepBRE
					result.StatusOfLastStep = StatusFailed
					result.LastStageDate = allLOECs[0].GetUpdatedAt().AsTime()
					result.DSAAction = "User Rejected by all lenders"
					result.UserAction = "User Rejected by all lenders"
					results = append(results, result)
					continue
				case hasApproved:
					result.UserLastStep = StepBRE
					result.StatusOfLastStep = StatusSuccess
					result.LastStageDate = allLOECs[0].GetUpdatedAt().AsTime()
					result.DSAAction = "User has approved offers from lenders, Ask user to select an offer and complete the remaining flow."
					result.UserAction = "Select offer and fill required details to complete the flow."
					results = append(results, result)
					continue
				case hasCreated:
					result.UserLastStep = StepEligibilityFLOW
					result.StatusOfLastStep = StatusInProgress
					result.LastStageDate = allLOECs[0].GetUpdatedAt().AsTime()
					result.DSAAction = "User hasn't completed the eligibility flow yet."
					result.UserAction = "Fill all required details to complete the eligibility flow."
					results = append(results, result)
					continue
				}
			}
		}
		// If we have terminal LR as well as we don't have any LSEs except epifi_tech, (for loan request type PRE_ELIGIBILITY)
		if latestLR != nil && latestLR.Type == preapprovedloanPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY && latestLR.CompletedAt.Valid {
			step, status, userAction, dsaAction, troubleshootingTip := mapLSEToFunnelStep(allLSEs[0], commontypes.Ownership_EPIFI_TECH_V2, latestLR.LoanProgram)
			result.UserLastStep = step
			result.StatusOfLastStep = status
			result.UserAction = userAction
			result.DSAAction = dsaAction
			result.TroubleshootingTips = troubleshootingTip
			result.LastStageDate = allLSEs[0].UpdatedAt.AsTime()

			results = append(results, result)
			continue
		}

		result.UserLastStep = StepUserFoundNotInLoansFunnel
		result.StatusOfLastStep = StatusNa
		results = append(results, result)
	}

	return results, nil
}

// readCSV reads the input CSV file
func readCSV(filePath string) ([]*UserInputItem, []*SkippedItem, error) {
	if filePath == "" {
		return nil, nil, errors.New("input CSV file path is empty")
	}

	file, err := os.OpenFile(filepath.Clean(filePath), os.O_RDONLY, 0600)
	if err != nil {
		return nil, nil, errors.Wrap(err, "error opening input CSV file")
	}
	defer func() {
		closeErr := file.Close()
		if closeErr != nil {
			err = closeErr
		}
	}()

	reader := csv.NewReader(file)
	rows, err := reader.ReadAll()
	if err != nil {
		return nil, nil, errors.Wrap(err, "error reading CSV")
	}

	if len(rows) <= 1 {
		return nil, nil, errors.New("input CSV file is empty or has no data rows")
	}

	if len(rows) > 1001 {
		return nil, nil, errors.Errorf("too many input items provided (%d), max limit is 1000", len(rows)-1)
	}

	var inputItems []*UserInputItem
	var skippedItems []*SkippedItem

	// Start from first row since we're not looking for headers anymore
	for i := 0; i < len(rows); i++ {
		row := rows[i]
		if len(row) == 0 {
			skippedItems = append(skippedItems, &SkippedItem{
				RowNumber: i + 1,
				Reason:    "Empty row",
			})
			continue
		}

		// Assume first column is phone number
		phoneNumber := strings.TrimSpace(row[0])
		if phoneNumber == "" {
			skippedItems = append(skippedItems, &SkippedItem{
				RowNumber: i + 1,
				Reason:    "Empty phone number",
			})
			continue
		}

		// Validate phone number: should contain only digits and be 10 digits long (Indian numbers)
		isValid := true
		var reason string

		if len(phoneNumber) != 10 {
			isValid = false
			reason = fmt.Sprintf("Invalid length: %d digits (expected 10)", len(phoneNumber))
		} else {
			for _, ch := range phoneNumber {
				if ch < '0' || ch > '9' {
					isValid = false
					reason = "Contains non-digit characters"
					break
				}
			}
		}

		if !isValid {
			skippedItems = append(skippedItems, &SkippedItem{
				PhoneNumber: phoneNumber,
				RowNumber:   i + 1,
				Reason:      reason,
			})
			continue
		}

		item := &UserInputItem{
			PhoneNumber: phoneNumber,
		}
		inputItems = append(inputItems, item)
	}

	return inputItems, skippedItems, nil
}

// writeResultsToCSV writes the loan funnel tracking results directly to S3
func (j *LoanFunnelTrackingJob) writeResultsToCSV(ctx context.Context, results []*LoanFunnelResult, emails []string, dsaName string, doMail bool) error {
	var csvBuffer strings.Builder
	writer := csv.NewWriter(&csvBuffer)

	// Define headers in the specified order
	headers := []string{
		"DSA Name",
		"Customer's Contact No",
		"Last stage time",
		"User Last Step",
		"Status of Last Step",
		"Rejection reason",
		"User action",
		"DSA action",
		"Troubleshooting tips",
		"Focus priority",
		"User creation time",
		"Lead creation time",
		"Vendor offer value",
		"Disbursed amount",
		"Platform",
		"UTM Content",
	}

	var rows [][]string
	rows = append(rows, headers)

	// Add data rows
	for _, result := range results {
		lastStageDate := ""
		if !result.LastStageDate.IsZero() {
			lastStageDate = result.LastStageDate.In(datetime.IST).Format("2006-01-02 15:04:05")
		}
		userCreationDate := ""
		if !result.UserCreationDate.IsZero() {
			userCreationDate = result.UserCreationDate.In(datetime.IST).Format("2006-01-02 15:04:05")
		}
		leadCreationDate := ""
		if !result.LeadCreationDate.IsZero() {
			leadCreationDate = result.LeadCreationDate.In(datetime.IST).Format("2006-01-02 15:04:05")
		}

		row := []string{
			result.DSAName,
			result.CustomerContactNo,
			lastStageDate,
			result.UserLastStep,
			result.StatusOfLastStep,
			result.RejectionReason,
			result.UserAction,
			result.DSAAction,
			result.TroubleshootingTips,
			result.FocusPriority,
			userCreationDate,
			leadCreationDate,
			formatFloat(result.VendorOfferValue),
			formatFloat(result.DisbursedAmount),
			result.Platform,
			result.UtmContent,
		}

		rows = append(rows, row)
	}

	err := writer.WriteAll(rows)
	if err != nil {
		return errors.Wrap(err, "error writing CSV data")
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return errors.Wrap(err, "error flushing CSV writer")
	}

	// Get the CSV content as bytes
	csvContent := []byte(csvBuffer.String())

	// Upload to S3
	s3Path := fmt.Sprintf("loan-funnel-tracking/loan-funnel-tracking-results_%s.csv", uuid.New().String())

	preSignedUrl, err := j.s3Client.WriteAndGetPreSignedUrl(ctx, s3Path, csvContent, 3*60*60)
	if err != nil {
		return errors.Wrap(err, "error uploading CSV to S3")
	}

	fmt.Println("\n====================== Loan Funnel Tracking Results ======================")
	fmt.Println(preSignedUrl)
	fmt.Println("\n==========================================================================")

	if doMail {
		// Send email with presigned URL
		subject := fmt.Sprintf("Fi loans MIS report for %s", dsaName)
		err = j.sendResultsEmail(ctx, emails, subject, preSignedUrl)
		if err != nil {
			return errors.Wrap(err, "error sending results email")
		}

		logger.Info(ctx, "Sent email report", zap.String("dsa", dsaName))
	}
	return nil
}

// sendResultsEmail sends the email with a presigned URL for CSV download
func (j *LoanFunnelTrackingJob) sendResultsEmail(ctx context.Context, emails []string, subject string, preSignedUrl string) error {
	if len(emails) == 0 {
		return errors.New("no emails provided")
	}

	request := &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_GUARANTEED,
		Medium: commsPb.Medium_EMAIL,
		UserIdentifier: &commsPb.SendMessageRequest_EmailId{
			EmailId: emails[0],
		},
		Message: &commsPb.SendMessageRequest_Email{
			Email: &commsPb.EmailMessage{
				FromEmailId:   "<EMAIL>",
				FromEmailName: "Fi Money",
				Subject:       subject,
				Body: []*commsPb.Body{
					{
						Content: fmt.Sprintf(`
<p>Hi,</p>
<p>Click <a href="%s" style="color: #007bff; text-decoration: none;">here</a> to download the report</p>
<p>Note: This download link will expire in 3 hours.</p>
<p>Best regards,<br>Fi Money Team</p>`, preSignedUrl),
						ContentType: commsPb.ContentType_HTML,
					},
				},
			},
		},
	}
	if len(emails) > 1 {
		for i, email := range emails {
			if i == 0 {
				continue
			}
			if request.GetEmail().GetDestination() == nil {
				request.GetEmail().Destination = &commsPb.EmailMessage_Destination{}
			}
			request.GetEmail().GetDestination().ToAddresses = append(request.GetEmail().GetDestination().ToAddresses, &commsPb.EmailMessage_Destination_EmailAddress{EmailId: email})
		}
	}
	res, err := j.commsClient.SendMessage(ctx, request)
	if err != nil {
		return errors.Wrap(err, "error calling SendMessage")
	}
	logger.Info(ctx, "successfully sent loan funnel tracking report email", zap.String(logger.MESSAGE_ID, res.GetMessageId()))
	return nil
}

// sortLSEsByUpdatedAt sorts loan step executions by updated_at timestamp in descending order
func sortLSEsByUpdatedAt(lses []*preapprovedloanPb.LoanStepExecution) {
	for i := 0; i < len(lses)-1; i++ {
		for j := i + 1; j < len(lses); j++ {
			timeI := datetime.TimestampToTime(lses[i].GetUpdatedAt())
			timeJ := datetime.TimestampToTime(lses[j].GetUpdatedAt())
			if timeI.Before(timeJ) {
				lses[i], lses[j] = lses[j], lses[i]
			}
		}
	}
}

// sortLOECsByUpdatedAt sorts LOECs by updated_at timestamp in descending order
func sortLOECsByUpdatedAt(loecs []*preapprovedloanPb.LoanOfferEligibilityCriteria) {
	for i := 0; i < len(loecs)-1; i++ {
		for j := i + 1; j < len(loecs); j++ {
			timeI := datetime.TimestampToTime(loecs[i].GetUpdatedAt())
			timeJ := datetime.TimestampToTime(loecs[j].GetUpdatedAt())
			if timeI.Before(timeJ) {
				loecs[i], loecs[j] = loecs[j], loecs[i]
			}
		}
	}
}

func formatFloat(value float64) string {
	if value == 0 {
		return ""
	}
	return strconv.FormatFloat(value, 'f', 6, 64)
}

// extractUrlParameter extracts a utm_campaign parameter value from a URL
func extractUrlParameter(urlStr, paramName string) string {
	if urlStr == "" {
		return ""
	}
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return ""
	}
	queryParams := parsedURL.Query()
	return queryParams.Get(paramName)
}

// getPriority determines the focus priority based on loan request and eligibility checks
func getPriority(latestLR *model.LoanRequest, allLOECs []*preapprovedloanPb.LoanOfferEligibilityCriteria) string {
	// Check for High priority - at least one LOEC approved
	for _, loec := range allLOECs {
		if !(loec.GetExpiredAt() == nil || loec.GetExpiredAt().AsTime().After(time.Now())) {
			continue // Skip expired LOECs
		}
		if loec.GetStatus() == preapprovedloanPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED {
			return "High"
		}
	}

	// Check for Ignore priority - all LOEC rejected or web eligibility failed
	allLoecRejected := true
	for _, loec := range allLOECs {
		if loec.GetStatus() != preapprovedloanPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED {
			allLoecRejected = false
			break
		}
	}

	if len(allLOECs) > 0 && allLoecRejected {
		return "Ignore"
	}
	if latestLR != nil && latestLR.Type == preapprovedloanPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY &&
		latestLR.Status == preapprovedloanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED {
		return "Ignore"
	}
	// Check for Medium priority - app eligibility LR exists in any state
	if latestLR != nil && latestLR.Type == preapprovedloanPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY {
		return "Medium"
	}
	// Check for Low priority - web eligibility LR exists and not failed
	if latestLR != nil && latestLR.Type == preapprovedloanPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY &&
		latestLR.Status != preapprovedloanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED {
		return "Low"
	}

	return ""
}

// getDsaNameForActor retrieves the DSA name for a given actor ID.
func (j *LoanFunnelTrackingJob) getDsaNameForActor(ctx context.Context, actorID string) (string, error) {
	leadsDb, err := j.dbResourceProvider.GetResourceForOwnership(commontypes.Ownership_EPIFI_TECH_V2)
	if err != nil {
		return "", fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}

	var DsaName string
	err = leadsDb.Table("user_leads").
		Select("client_id").
		Where("actor_id = ?", actorID).
		Limit(1).
		Pluck("client_id", &DsaName).Error

	if err != nil {
		logger.Error(ctx, "Error fetching DSA name for actor", zap.String("actorID", actorID), zap.Error(err))
		return "", err
	}

	return DsaName, nil
}

// RESULTS OF EXPLAIN QUERIES:
//
// Query 1:
// EXPLAIN
// SELECT actor_id, computed_phone_number, acquisition_info, created_at
// FROM users
// WHERE computed_phone_number IN
//	  ('912452315158', '912117266041', '912387777963',
//    ......)
// AND deleted_at_unix = 0;
//
// Result:
// +-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
// |                                                                                           INFO                                                                                            |
// +-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
// | distribution: local                                                                                                                                                                       |
// | vectorized: true                                                                                                                                                                          |
// |                                                                                                                                                                                           |
// | • index join                                                                                                                                                                              |
// | │ estimated row count: 438                                                                                                                                                                |
// | │ table: users@primary                                                                                                                                                                    |
// | │                                                                                                                                                                                         |
// | └── • scan                                                                                                                                                                                |
// |       estimated row count: 8 (<0.01% of the table; stats collected 15 days ago; using stats forecast for 44 days in the future)                                                           |
// |       table: users@users_computed_phone_number_deleted_at_unix_unq_idx                                                                                                                    |
// |       spans: [/'910012410180'/0 - /'910012410180'/0] [/'910018533155'/0 - /'910018533155'/0] [/'910036244616'/0 - /'910036244616'/0] [/'910041801988'/0 - /'910041801988'/0] … (995 more) |
// +-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
//
// Explanation:The query retrieves user records from the users table where the phone number matches one of about 1000 specific values. It uses an index to efficiently scan for these phone numbers,
// and since only around 8 users are expected to match, the scan is minimal — less than 0.01% of the total table data. After finding the matches through the index, it performs an index join to fetch
// the complete user details from the main table. The execution is local and optimized using vectorized processing, ensuring fast performance.
//
// Query 2:
//
// EXPLAIN
// SELECT actor_id, computed_phone_number, acquisition_info, created_at
// FROM users
// WHERE acquisition_info->>'acquisitionIntent' = 'ACQUISITION_INTENT_PERSONAL_LOANS'
// AND deleted_at_unix = 0
// AND updated_at >= '2025-05-01 00:00:00+05:30'
// AND created_at >= '2025-05-01 00:00:00+05:30'
// AND updated_at <= '2025-06-01 23:59:59.999999999+05:30'
// AND created_at <= '2025-06-01 23:59:59.999999999+05:30';
//
// Result:
// +--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
// |                                                                                                           INFO                                                                                                           |
// +--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
// | distribution: local                                                                                                                                                                                                      |
// | vectorized: true                                                                                                                                                                                                         |
// |                                                                                                                                                                                                                          |
// | • filter                                                                                                                                                                                                                 |
// | │ estimated row count: 86,706                                                                                                                                                                                            |
// | │ filter: (((created_at >= '2025-05-01 00:00:00+05:30') AND (created_at <= '2025-06-02 00:00:00+05:30')) AND ((acquisition_info->>'acquisitionIntent') = 'ACQUISITION_INTENT_PERSONAL_LOANS')) AND (deleted_at_unix = 0) |
// | │                                                                                                                                                                                                                        |
// | └── • index join                                                                                                                                                                                                         |
// |     │ estimated row count: 288,442                                                                                                                                                                                       |
// |     │ table: users@primary                                                                                                                                                                                               |
// |     │                                                                                                                                                                                                                    |
// |     └── • scan                                                                                                                                                                                                           |
// |           estimated row count: 288,442 (1.6% of the table; stats collected 15 days ago; using stats forecast for 44 days in the future)                                                                                  |
// |           table: users@users_updated_at_idx                                                                                                                                                                              |
// |           spans: [/'2025-05-01 00:00:00+05:30' - /'2025-06-02 00:00:00+05:30']                                                                                                                                           |
// |                                                                                                                                                                                                                          |
// | index recommendations: 1                                                                                                                                                                                                 |
// | 1. type: index creation                                                                                                                                                                                                  |
// |    SQL command: CREATE INDEX ON epifi.public.users (deleted_at_unix, updated_at) STORING (computed_phone_number, created_at, acquisition_info, actor_id);                                                                |
// +--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
//
// Explanation:The database first scans the users_updated_at_idx index to filter records within the date range, which covers about 1.6% of the total table (around 288,442 rows). Then, it performs an index join to retrieve full
// user details from the primary table. A final filter is applied to narrow the results down to the 86,706 rows that match all the criteria. The query is executed locally using vectorized processing for speed. Also, the system
// suggests creating a new index on (deleted_at_unix, updated_at) to make this query even faster in the future.
//
// Query 3:
// EXPLAIN
// SELECT actor_id, computed_phone_number, acquisition_info, created_at
// FROM users
// WHERE acquisition_info->>'acquisitionIntent' = 'ACQUISITION_INTENT_PERSONAL_LOANS'
// AND deleted_at_unix = 0
// AND updated_at >= '2025-05-01 00:00:00+05:30'
// AND created_at >= '2025-05-01 00:00:00+05:30'
// AND updated_at <= '2025-05-07 23:59:59.999999999+05:30'
// AND created_at <= '2025-05-07 23:59:59.999999999+05:30';
//
// Result:
// +--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
// |                                                                                                           INFO                                                                                                           |
// +--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
// | distribution: local                                                                                                                                                                                                      |
// | vectorized: true                                                                                                                                                                                                         |
// |                                                                                                                                                                                                                          |
// | • filter                                                                                                                                                                                                                 |
// | │ estimated row count: 16,025                                                                                                                                                                                            |
// | │ filter: (((created_at >= '2025-05-01 00:00:00+05:30') AND (created_at <= '2025-05-08 00:00:00+05:30')) AND ((acquisition_info->>'acquisitionIntent') = 'ACQUISITION_INTENT_PERSONAL_LOANS')) AND (deleted_at_unix = 0) |
// | │                                                                                                                                                                                                                        |
// | └── • index join                                                                                                                                                                                                         |
// |     │ estimated row count: 53,393                                                                                                                                                                                        |
// |     │ table: users@primary                                                                                                                                                                                               |
// |     │                                                                                                                                                                                                                    |
// |     └── • scan                                                                                                                                                                                                           |
// |           estimated row count: 53,393 (0.29% of the table; stats collected 15 days ago; using stats forecast for 44 days in the future)                                                                                  |
// |           table: users@users_updated_at_idx                                                                                                                                                                              |
// |           spans: [/'2025-05-01 00:00:00+05:30' - /'2025-05-08 00:00:00+05:30']                                                                                                                                           |
// |                                                                                                                                                                                                                          |
// | index recommendations: 1                                                                                                                                                                                                 |
// | 1. type: index creation                                                                                                                                                                                                  |
// |    SQL command: CREATE INDEX ON epifi.public.users (deleted_at_unix, updated_at) STORING (computed_phone_number, created_at, acquisition_info, actor_id);                                                                |
// +--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+

// Explanation:
// This query fetches user records created between May 1 and June 2, 2025, where the user is not deleted (deleted_at_unix = 0) and the acquisitionIntent is 'ACQUISITION_INTENT_PERSONAL_LOANS'. It first scans the users_updated_at_idx
// index to get rows in the date range (about 288,442 rows), then performs an index join to retrieve full user data. After applying all filters, around 86,706 records are expected. The query runs locally with vectorized execution,
// and the system suggests creating a new index to improve performance.
