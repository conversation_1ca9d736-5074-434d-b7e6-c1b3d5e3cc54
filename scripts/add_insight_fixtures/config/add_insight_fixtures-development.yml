Application:
  Environment: "development"
  Name: "add_insight_fixtures"

ActorInsightsDb:
  Name: "actor_insights"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    UseInsecureLog: true
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms

AWS:
  Region: "ap-south-1"

Secrets:
  Ids:
    ActorInsightsDbUsernamePassword: "{\"username\": \"root\", \"password\": \"\"}"

