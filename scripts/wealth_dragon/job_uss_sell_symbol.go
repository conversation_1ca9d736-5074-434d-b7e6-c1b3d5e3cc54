package main

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/usstocks"
	catalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	usStocksOrderPb "github.com/epifi/gamma/api/usstocks/order"
)

type jobUssSellAllForSymbol struct {
	catalogManagerClient catalogPb.CatalogManagerClient
	orderManagerClient   usStocksOrderPb.OrderManagerClient
}

var allowedSellAllActorIds = []string{
	"AC210906KVfLE3JyTB6ooZ5O9VYmVw==",
	"AC9/TH3wjUQVSo97eMpHGVeg240129==",
}

func isAllowedActorId(actorId string) bool {
	for _, allowed := range allowedSellAllActorIds {
		if actorId == allowed {
			return true
		}
	}
	return false
}

func (j *jobUssSellAllForSymbol) PerformJob(ctx context.Context, req *JobRequest) error {
	symbol := req.Args1
	actorId := req.Args2
	if symbol == "" {
		return fmt.Errorf("symbol is required as Args1")
	}
	if actorId == "" {
		return fmt.Errorf("actorId is required as Args2")
	}
	if !isAllowedActorId(actorId) {
		return fmt.Errorf("actorId %s is not allowed to perform this operation", actorId)
	}

	// get stock id from symbol
	getStocksRes, err := j.catalogManagerClient.GetStocks(ctx, &catalogPb.GetStocksRequest{
		Identifiers: &catalogPb.GetStocksRequest_Symbols{Symbols: &catalogPb.RepeatedStrings{Ids: []string{symbol}}},
	})
	if err = epifigrpc.RPCError(getStocksRes, err); err != nil {
		if getStocksRes != nil && getStocksRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "stock id not found for symbol", zap.Error(err))
			return err
		}
		logger.Error(ctx, "error getting stock id for symbol", zap.Error(err))
		return err
	}
	stockDetails, found := getStocksRes.GetStocks()[symbol]
	if !found {
		logger.Error(ctx, "stock details not found for symbol")
		return fmt.Errorf("stock details not found for symbol")
	}

	// place sell all order
	createOrderResp, err := j.orderManagerClient.CreateOrder(ctx, &usStocksOrderPb.CreateOrderRequest{
		ClientOrderId: uuid.NewString(),
		ActorId:       actorId,
		CatalogRefId:  stockDetails.GetId(),
		Side:          usstocks.OrderSide_SELL,
		Type:          usstocks.OrderType_ORDER_TYPE_SELL_ALL,
		FundingType:   usstocks.OrderFundingType_ORDER_FUNDING_TYPE_WALLET,
		// this amount will not be used for placing sell order
		// sending this so that validation in backend does not fail
		AmountRequested: &money.Money{CurrencyCode: "USD", Units: 1},
		CalculatedTradeAmountAfterChargesDeduction: &money.Money{CurrencyCode: "USD"},
	})
	fmt.Println("create order response", createOrderResp)
	if err = epifigrpc.RPCError(createOrderResp, err); err != nil {
		logger.Error(ctx, "error placing sell order", zap.Error(err))
		return err
	}
	logger.Info(ctx, "sell order placed successfully", zap.Any("create order response", createOrderResp))
	return nil
}
