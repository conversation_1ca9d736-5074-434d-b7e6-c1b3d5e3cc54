package main

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsConfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/cenkalti/backoff/v4"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"

	"github.com/epifi/gamma/scripts/risk_sms_filter/config"
	"github.com/epifi/gamma/scripts/risk_sms_filter/internal"
	"github.com/epifi/gamma/scripts/risk_sms_filter/internal/checkpoint"
	"github.com/epifi/gamma/scripts/risk_sms_filter/internal/s3reader"
	"github.com/epifi/gamma/scripts/risk_sms_filter/internal/usecases"
)

var (
	chunkSize            = 5000
	breakingVal          int
	MaxWorkers           int
	MaxRetries           = 3               // Maximum number of retries for failed downloads
	GoroutineTimeout     = 5 * time.Minute // Timeout for goroutine.Run
	maxMessagesSoftLimit uint64
	messagesMatchedTotal uint64
)

// Retrieves the Slack Webhook URL from AWS Secrets Manager.
func getSlackWebhookURL(ctx context.Context, client *secretsmanager.Client, slackSecretName string) (string, error) {
	input := &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(slackSecretName),
	}
	result, err := client.GetSecretValue(ctx, input)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve Slack webhook URL: %v", err)
	}
	var secretData map[string]string
	err = json.Unmarshal([]byte(*result.SecretString), &secretData)
	if err != nil {
		return "", fmt.Errorf("failed to parse secret JSON: %v", err)
	}
	webhookURL, exists := secretData["slackWebhookURL"]
	if !exists {
		return "", fmt.Errorf("slackWebhookURL key not found in secret")
	}
	return webhookURL, nil
}

// Sends a message containing the Pre-signed URL and file name to Slack.
func sendToSlack(webhookURL, presignedURL, fileName string) error {
	payload := map[string]string{
		"text": fmt.Sprintf("*Click to download the file `%s`:* <%s|s3 signed url>", fileName, presignedURL),
	}
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal Slack payload: %v", err)
	}
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonPayload)) //nolint:gosec
	if err != nil {
		return fmt.Errorf("failed to send message to Slack: %v", err)
	}
	defer func() {
		if err := resp.Body.Close(); err != nil {
			log.Printf("Failed to close response body: %v", err)
		}
	}()
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("slack webhook returned non-OK status: %d", resp.StatusCode)
	}
	return nil
}

// Generates a Pre-signed URL for downloading a file from S3.
func generatePresignedURL(ctx context.Context, s3Client *s3.Client, bucket, key string) (string, error) {
	presigner := s3.NewPresignClient(s3Client)
	req, err := presigner.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	}, s3.WithPresignExpires(24*time.Hour))
	if err != nil {
		return "", fmt.Errorf("failed to generate pre-signed URL: %v", err)
	}
	log.Printf("URL expires at: %s", time.Now().Add(24*time.Hour).Format(time.RFC3339))
	return req.URL, nil
}

// loadS3JSON loads JSON data from S3, handling common logic for summary and combined output.
func loadS3JSON(ctx context.Context, s3Client *s3.Client, bucket, key, errPrefix string) (*internal.ProcessedJSON, error) {
	resp, err := s3Client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		if strings.Contains(err.Error(), "NoSuchKey") {
			return &internal.ProcessedJSON{}, nil
		}
		return nil, fmt.Errorf("%s: %v", errPrefix, err)
	}
	defer func() {
		if err := resp.Body.Close(); err != nil {
			log.Printf("Failed to close response body for %s: %v", key, err)
		}
	}()
	var data internal.ProcessedJSON
	if err := json.NewDecoder(resp.Body).Decode(&data); err != nil {
		return nil, fmt.Errorf("failed to decode %s: %v", errPrefix, err)
	}
	return &data, nil
}

// Loads the existing summary file from S3.
func loadSummary(ctx context.Context, s3Client *s3.Client, bucket, summaryFileKey string) (*internal.ProcessedJSON, error) {
	return loadS3JSON(ctx, s3Client, bucket, summaryFileKey, "failed to load summary")
}

// Loads the existing combined output from S3 for the current run.
func loadCombinedOutput(ctx context.Context, s3Client *s3.Client, bucket, key string) (*internal.ProcessedJSON, error) {
	return loadS3JSON(ctx, s3Client, bucket, key, "failed to load combined output")
}

// Saves the updated summary file to S3.
func saveSummary(ctx context.Context, s3Client *s3.Client, bucket, summaryFileKey string, summary *internal.ProcessedJSON) error {
	data, err := json.Marshal(summary)
	if err != nil {
		return fmt.Errorf("failed to marshal summary: %v", err)
	}
	_, err = s3Client.PutObject(ctx, &s3.PutObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(summaryFileKey),
		Body:   strings.NewReader(string(data)),
	})
	if err != nil {
		return fmt.Errorf("failed to upload summary: %v", err)
	}
	return nil
}

func processFile(ctx context.Context, reader *s3reader.S3Reader, key, useCase string) ([]internal.ProcessedMessage, error) {
	var msgs []internal.ProcessedMessage
	err := backoff.Retry(func() error {
		data, err := reader.DownloadJSONFile(ctx, key)
		if err != nil {
			return err
		}
		uc, exists := usecases.UseCases[useCase]
		if !exists {
			return fmt.Errorf("unknown use case: %s", useCase)
		}
		processedResult := uc.ProcessFunc(data)
		msgs = processedResult.Messages
		return nil
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), uint64(MaxRetries)))
	if err != nil {
		encodedKey := base64.StdEncoding.EncodeToString([]byte(key))
		return nil, fmt.Errorf("failed to download file %s after %d retries: %v", encodedKey, MaxRetries, err)
	}
	return msgs, nil
}

func processChunk(ctx context.Context, s3Client *s3.Client, reader *s3reader.S3Reader, bucket, outputKey, summaryFileKey string, files []string, messageSet map[int64]struct{}, setMutex *sync.Mutex, useCase string) bool {
	success := true

	if len(files) > 0 {
		lastKey := files[len(files)-1]
		log.Printf("Last key in chunk: %s", base64.StdEncoding.EncodeToString([]byte(lastKey)))
	}

	combinedOutput, err := loadCombinedOutput(ctx, s3Client, bucket, outputKey)

	if err != nil {
		log.Printf("Failed to load combined output before processing chunk: %v", err)
		success = false
		combinedOutput = &internal.ProcessedJSON{}
	}

	setMutex.Lock()
	for _, msg := range combinedOutput.Messages {
		messageSet[msg.ReceivedTimestamp] = struct{}{}
	}
	setMutex.Unlock()

	results := make(chan []internal.ProcessedMessage, len(files))
	errors := make(chan error, len(files))
	var wg sync.WaitGroup
	sem := make(chan struct{}, MaxWorkers)

	for _, key := range files {
		wg.Add(1)
		sem <- struct{}{}
		k := key
		goroutine.Run(ctx, GoroutineTimeout, func(ctx context.Context) {
			defer wg.Done()
			defer func() { <-sem }()
			msgs, err := processFile(ctx, reader, k, useCase)
			if err != nil {
				errors <- err
				return
			}
			results <- msgs
		})
	}

	goroutine.Run(ctx, GoroutineTimeout, func(ctx context.Context) {
		waitgroup.SafeWait(&wg, 5*time.Minute)
		close(results)
		close(errors)
	})

	for err := range errors {
		log.Printf("Error processing file: %v", err)
		success = false
	}

	for msgs := range results {
		setMutex.Lock()
		for _, msg := range msgs {
			if _, exists := messageSet[msg.ReceivedTimestamp]; !exists {
				combinedOutput.Messages = append(combinedOutput.Messages, msg)
				messageSet[msg.ReceivedTimestamp] = struct{}{}
			}
		}
		atomic.AddUint64(&messagesMatchedTotal, uint64(len(msgs)))
		setMutex.Unlock()
	}

	if success && len(combinedOutput.Messages) > 0 {
		data, err := json.Marshal(combinedOutput)
		if err != nil {
			log.Printf("Failed to marshal combined output: %v", err)
			success = false
		} else {
			_, err = s3Client.PutObject(ctx, &s3.PutObjectInput{
				Bucket: aws.String(bucket),
				Key:    aws.String(outputKey),
				Body:   strings.NewReader(string(data)),
			})
			if err != nil {
				log.Printf("Failed to upload combined output to %s: %v", outputKey, err)
				success = false
			} else {
				log.Printf("Successfully uploaded combined output to %s with %d messages", outputKey, len(combinedOutput.Messages))
				summary, err := loadSummary(ctx, s3Client, bucket, summaryFileKey)
				if err != nil {
					log.Printf("Failed to load summary file in chunk: %v", err)
					success = false
				} else {
					existingSet := make(map[int64]struct{})
					for _, msg := range summary.Messages {
						existingSet[msg.ReceivedTimestamp] = struct{}{}
					}
					for _, msg := range combinedOutput.Messages {
						if _, exists := existingSet[msg.ReceivedTimestamp]; !exists {
							summary.Messages = append(summary.Messages, msg)
							existingSet[msg.ReceivedTimestamp] = struct{}{}
						}
					}
					if err := saveSummary(ctx, s3Client, bucket, summaryFileKey, summary); err != nil {
						log.Printf("Failed to save summary file in chunk: %v", err)
						success = false
					} else {
						log.Printf("Successfully updated summary file %s with %d messages after chunk", summaryFileKey, len(summary.Messages))
					}
				}
			}
		}
	}
	return success
}

func generateURLOnDemand(ctx context.Context, s3Client *s3.Client, secretsClient *secretsmanager.Client, bucket, key, slackSecretName string) {
	presignedURL, err := generatePresignedURL(ctx, s3Client, bucket, key)
	if err != nil {
		log.Printf("Failed to generate Pre-signed URL for key %s: %v", key, err)
		os.Exit(1)
	}
	slackWebhookURL, err := getSlackWebhookURL(ctx, secretsClient, slackSecretName)
	if err != nil {
		log.Printf("Failed to retrieve Slack webhook URL: %v", err)
		os.Exit(1)
	}

	if err := sendToSlack(slackWebhookURL, presignedURL, filepath.Base(key)); err != nil {
		log.Printf("Failed to send Slack notification for key %s: %v", key, err)
		os.Exit(1)
	}
	log.Printf("Successfully sent Pre-signed URL for key %s to Slack!", key)
	os.Exit(0)
}

func main() {
	fmt.Println(os.Args)
	fmt.Println("starting the program")
	startDateStr := flag.String("strtdate", "", "Start datetime for processing (YYYY-MM-DDTHH:MM:SSZ)")
	startAfterStr := flag.String("startafter", "", "Key to start listing after")
	generateURL := flag.String("generate-url", "", "Generate a pre-signed URL for the specified key and send it to Slack")
	useCaseStr := flag.String("usecase", "account", "Use case for processing (only one allowed: account, salary)")
	flag.Uint64Var(&maxMessagesSoftLimit, "maxMessagesSoftLimit", 0, "If not zero, then the processing will be stopped when the messages exceed this soft limit. Final messages can be more this soft limit as processing is done concurrently.")
	flag.IntVar(&breakingVal, "breakingVal", 0, "Breaking value for file processing (0 or not provided means process all files)")
	flag.IntVar(&MaxWorkers, "maxWorkers", 30, "Maximum number of workers for parallel processing")
	flag.Parse()
	fmt.Printf("generateURL: %s\n", *generateURL)
	fmt.Printf("startDateStr: %s\n", *startDateStr)
	fmt.Printf("startAfterStr: %s\n", *startAfterStr)
	fmt.Printf("useCaseStr: %s\n", *useCaseStr)
	fmt.Printf("breakingVal: %d \n", breakingVal)
	fmt.Printf("maxWorkers: %d\n", MaxWorkers)
	// Validate use case (only one is processed per run)
	if _, exists := usecases.UseCases[*useCaseStr]; !exists {
		log.Fatalf("Invalid use case: %s. Available: %v", *useCaseStr, usecases.GetAvailableUseCases())
	}

	ctx := context.Background()

	// Load config with the specified use case
	cfgSettings, err := config.Load(*useCaseStr)
	if err != nil {
		log.Fatalf("Failed to load config for use case %s: %v", *useCaseStr, err)
	}
	log.Printf("Source Bucket: %s, Destination Bucket: %s, Slack Secret: %s, Summary File Key: %s",
		cfgSettings.S3.SourceBucketName,
		cfgSettings.S3.DestBucketName,
		cfgSettings.S3.SlackSecretName,
		cfgSettings.S3.SummaryFileKey)
	log.Printf("Starting processing with use case: %s", *useCaseStr)

	awsCfg, err := awsConfig.LoadDefaultConfig(ctx, awsConfig.WithRegion(cfgSettings.AWS.Region))
	if err != nil {
		log.Fatalf("Unable to load AWS config: %v", err)
	}

	s3Client := s3.NewFromConfig(awsCfg)

	if *generateURL != "" {
		secretsClient := secretsmanager.NewFromConfig(awsCfg)
		generateURLOnDemand(ctx, s3Client, secretsClient, cfgSettings.S3.DestBucketName, *generateURL, cfgSettings.S3.SlackSecretName)
	}

	reader := s3reader.NewS3Reader(s3Client, cfgSettings.S3.SourceBucketName)

	lastProcessed, err := checkpoint.LoadCheckpoint(ctx, s3Client, cfgSettings.S3.DestBucketName, cfgSettings.S3.CheckpointFileKey)
	if err != nil {
		log.Printf("Failed to load checkpoint: %v", err)
		lastProcessed = time.Time{}
	}

	var startTime time.Time
	if *startDateStr != "" {
		startTime, err = time.Parse(time.RFC3339, *startDateStr)
		if err != nil {
			log.Fatalf("Invalid start date format: %v", err)
		}
	} else {
		startTime = lastProcessed
	}

	outputKey := fmt.Sprintf("processed_output/%s_%s.json", *useCaseStr, time.Now().Format("2006-01-02"))
	messageSet := make(map[int64]struct{})
	var setMutex sync.Mutex
	totalFilesProcessed := 0

	listInput := &s3.ListObjectsV2Input{
		Bucket: aws.String(cfgSettings.S3.SourceBucketName),
		Prefix: aws.String("processed/"),
	}
	if *startAfterStr != "" {
		listInput.StartAfter = aws.String(*startAfterStr)
	}

	paginator := s3.NewListObjectsV2Paginator(s3Client, listInput)
	allSuccessful := true

	var chunkFiles []string

	for paginator.HasMorePages() {
		if breakingVal > 0 && totalFilesProcessed >= breakingVal {
			log.Printf("Reached breaking value of %d files, stopping processing", breakingVal)
			break
		}
		if maxMessagesSoftLimit > 0 && atomic.LoadUint64(&messagesMatchedTotal) >= maxMessagesSoftLimit {
			log.Printf("Reached messages soft limiit: %d, stopping processing", atomic.LoadUint64(&messagesMatchedTotal))
			break
		}

		page, err := paginator.NextPage(ctx)
		if err != nil {
			log.Printf("Failed to list files: %v", err)
			allSuccessful = false
			break
		}

		for _, obj := range page.Contents {
			if breakingVal > 0 && totalFilesProcessed >= breakingVal {
				log.Printf("Reached breaking value of %d files, stopping processing", breakingVal)
				break
			}

			key := aws.ToString(obj.Key)
			if strings.HasSuffix(key, "/") || !strings.HasSuffix(strings.ToLower(key), ".json") {
				continue
			}
			if startTime.IsZero() || obj.LastModified.After(startTime) {
				chunkFiles = append(chunkFiles, key)
				totalFilesProcessed++

				if len(chunkFiles) >= chunkSize {
					// summary file: summary/lea_summary.json
					// output file: processed_output/account_2025-05-12.json
					allSuccessful = processChunk(ctx, s3Client, reader, cfgSettings.S3.DestBucketName, outputKey, cfgSettings.S3.SummaryFileKey, chunkFiles, messageSet, &setMutex, *useCaseStr) && allSuccessful
					fmt.Printf("total files processed: %d\n", totalFilesProcessed)
					chunkFiles = nil
				}
			}
		}
	}

	if len(chunkFiles) > 0 {
		allSuccessful = processChunk(ctx, s3Client, reader, cfgSettings.S3.DestBucketName, outputKey, cfgSettings.S3.SummaryFileKey, chunkFiles, messageSet, &setMutex, *useCaseStr) && allSuccessful
	}

	log.Printf("Processed %d files in total for use case %s", totalFilesProcessed, *useCaseStr)

	if totalFilesProcessed > 0 {
		presignedURL, err := generatePresignedURL(ctx, s3Client, cfgSettings.S3.DestBucketName, cfgSettings.S3.SummaryFileKey)
		if err != nil {
			log.Printf("Failed to generate Pre-signed URL: %v", err)
			allSuccessful = false
		} else {
			secretsClient := secretsmanager.NewFromConfig(awsCfg)
			slackWebhookURL, err := getSlackWebhookURL(ctx, secretsClient, cfgSettings.S3.SlackSecretName)
			if err != nil {
				log.Printf("Failed to retrieve Slack webhook URL: %v", err)
				allSuccessful = false
			} else {
				if err := sendToSlack(slackWebhookURL, presignedURL, filepath.Base(cfgSettings.S3.SummaryFileKey)); err != nil {
					log.Printf("Failed to send Slack notification: %v", err)
					allSuccessful = false
				} else {
					log.Println("Sent Pre-signed URL to Slack!")
				}
			}
		}
	}

	if allSuccessful {
		currentTime := time.Now()
		if err := checkpoint.SaveCheckpoint(ctx, s3Client, cfgSettings.S3.DestBucketName, cfgSettings.S3.CheckpointFileKey, currentTime); err != nil {
			log.Printf("Failed to save checkpoint: %v", err)
		} else {
			log.Printf("Checkpoint updated with LastProcessed: %s", currentTime.Format(time.RFC3339))
		}
	}

	log.Println("Processing completed")
}
