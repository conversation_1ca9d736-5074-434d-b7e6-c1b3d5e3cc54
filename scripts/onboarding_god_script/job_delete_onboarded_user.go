package main

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	pkgErr "github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/user"
	authDao "github.com/epifi/gamma/auth/dao"
	bankCustDao "github.com/epifi/gamma/bankcust/dao"
	onbDao "github.com/epifi/gamma/user/onboarding/dao"
)

type DeleteOnboardedUser struct {
	authClient         auth.AuthClient
	userClient         user.UsersClient
	actorClient        actor.ActorClient
	bankCustomerClient bankCustomerPb.BankCustomerServiceClient
	employmentClient   employmentPb.EmploymentClient
	bcDao              bankCustDao.BankCustomerDao
	onbDao             onbDao.OnboardingDao
	devRegDao          authDao.DeviceRegistrationDao
}

// List of deleted users
// actorId = "AC210426EzpxCjinRQmKUCaPFetuWw==", userId  = "34536a0e-8791-497c-b024-929e614f5434"
// actorId = "AC230126tWUdNX5eRkiz8RZqm6BJMQ==", userId  = "af6b5302-f617-471b-8b6a-1b290b0ace8d", reason: user is stuck due to privaterelay.appleid
// actorId = "AClroOe5rGQO6t4kX1FbBHHQ230403==", userId  = "a9128e3a-2aca-4ea0-859a-32bc558fe6d0", reason: internal user is stuck due to privaterelay.appleid
// actorId = "AC210614R6TMh4emSVum2s85nzUaFA==", userId  = "862efad6-7214-4305-93c7-dd77deef52a2", reason: POC for salary program, requested by Ayushi Lamba
// actorId = "AC220317c2U+lK+mTUCRnIOuQeXp5w==", userId  = "efe54587-1d52-4db2-8dd7-3cb529fefb12", reason: Sujith's fnf min kyc expiry
// actorId = "AC220325ylWOKdKnS+2RvSZSVB8X+A==", userId  = "66457e20-6269-4497-bf54-749f93ebddb5", reason: Risk ops request
// actorId = "AC210908RJRd7obGR/awVb1b29N6AQ==", userId  = "c5d48715-5381-4985-9a97-1b6fbde466f0", reason: deleted upon user request
// actorId = "AC210718yinE65/DSWeBExwnC5G4SQ==", userId  = "462df172-f4f4-4eab-ab8b-0949af400521", reason: deleted upon user request
// actorId = "AC220128SE9PkupxTNCVxUnyV9qgfA==", userId  = "d217a41d-1180-4f65-a69b-b3c48855df6a", reason: deleted upon user request
// actorId = "ACgFxkEbHCQ+Shicskk37E5g230802==", userId  = "ac393e33-88be-4046-a335-df9995fd9963", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=68006
// actorId = "AC220429rqpvB9iWSuCiEmdoTWIwdw==", userId = "d8986f1b-52a4-4843-a96e-78c1f9cf98cc", reason: https://epifi.slack.com/archives/C05N2BY8AAU/p1704447318587699
// actorId = "ACJlofbtlKQQWDXnPlVvKp1w231212==", userId = "5aa8b4b8-3352-4db3-86f8-e6f1ed729525", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=73449
// actorId = "ACRtZfdBHzQ/Gksm8OHFKA3A240131==", userId = "0dddd18a-5478-4889-9023-d6150b558436", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=76740, deleted using scripts/onboarding_god_script/job_delete_userdetails.go
// actorId = "AC220720RBwvc1aASGaRwcy8ovn6mg==", userId = "b1a42089-aa81-4365-8e8b-5241f5389836", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=91946
// actorId = "AC2204259hdFhiFvTVG4bPWv6fbBLQ==", userId = "6cab82be-b5f4-4eac-ada6-b92879f89f9f", reason: https://epifi.slack.com/archives/C02N8JXBTRT/p1738572703210899
// actorId = "AC2203125C/008pQR2OmRI6RXzHDag==", userId = "4f6e6a28-2eb0-4cca-981f-ec184f610b37", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=95988
// actorId = "AC220307FfRWXO1MSnGLtckU+Oob4w==", userId = "11c4fe82-892b-4520-86a3-1949e29a18d6", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=99063
// actorId = "AC211023DRn8HM+3SQmoKKrRZgEzEQ==", userId = "23b3056d-4fe6-416a-951d-c45a9e66ea7e", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=99423
// actorId = "ACfYOT39YsQbGfu1UOEq869w230420==", userId = "eaf6e333-999d-4420-b418-45f9a9c8b155", reason: https://github.com/epiFi/tickets/issues/56457
// https://github.com/epiFi/tickets/issues/57344

// Params of user to be deleted

type UserToDelete struct {
	ActorID string
	UserID  string
}

var usersToDelete = []UserToDelete{

	{ActorID: "AC220819x7l8I9WHRyyxQCHk6m1d0w==", UserID: "8da8408d-fb0d-4c33-8b42-aa2455dc5137"},
	{ActorID: "AC220311DT66BNZ/RAmS7cVjnec8HA==", UserID: "43fc08e2-b943-4cfa-a2c0-5838e5888a7e"},
	{ActorID: "AC220116TzWDC9QSSlOBrbOjDfT0Ow==", UserID: "91dfee6a-7eb7-4949-9f76-18ad69fcffc6"},
	{ActorID: "AC210626xHj+QJ0oRY6Mri2OGK12Sw==", UserID: "a28629b9-9c57-4acb-8ec8-1e77f3a7e6c6"},
	{ActorID: "AC211001uEdGMm5DS4ag/lli+Q44QA==", UserID: "2abb4429-eba0-4c84-aee4-a27e97a429cb"},
	{ActorID: "AC220222//FhhvnSSQGavP6t3MG6eg==", UserID: "97405fe9-1d01-4eea-9ba0-d86ab482dad5"},
	{ActorID: "AC220614iUOw+ujrTe64EFRowNrMCw==", UserID: "a2e4b49c-019b-4a77-a420-3542dd40f5ab"},
	{ActorID: "AC211224B2XZzaI4Rhq1+0HWDaVx/A==", UserID: "34cacff8-b576-4547-b482-92cb6812b11d"},
	{ActorID: "AC220507m9nDzaE0R0uygPPdZH/LUQ==", UserID: "d6026f5c-c8a6-4ba0-8ee6-03b88fbd4506"},
	{ActorID: "AC211126444NHcA3SN6gDH+88lBVaQ==", UserID: "8b722f66-ec64-41c3-895e-bbd3baca15e5"},
	{ActorID: "AC230323wkwVxVukS/C7vsYRFq9vMA==", UserID: "411f906b-4776-4190-b786-b08081189e08"},
	{ActorID: "ACAGm1fteAbU250529", UserID: "f1437776-1773-4052-b669-1bca32a75109"},
	{ActorID: "AC220517taWkc5JDQ+Og2bhgOk569A==", UserID: "46b5df04-767c-4789-81a8-fd15b5361f56"},
	{ActorID: "AC220428hqLtp2xXRbiOqCYFtNFrfQ==", UserID: "789bbb64-a8e8-4e32-8ddd-8cd69164eab3"},
	{ActorID: "AC2202115PUw/Su7SjqMB0CuP4cJdw==", UserID: "a79d617c-9aed-4bce-8f8b-20e4a15489ab"},
	{ActorID: "AC3n6fSNhm1H250425", UserID: "5da29008-8a95-466e-9dee-944445d91cb0"},
	{ActorID: "AC211201KKkheEOHTGS/YeZp9FvNMQ==", UserID: "53a58e87-39b7-4bd4-a3f2-2c8e83d112bf"},
	{ActorID: "AC230116ZkTVLIgxRzmziiQvwgU2SQ==", UserID: "d519823f-d647-462a-adbf-27ef84dbc1fc"},
	{ActorID: "AC210808WIrnLFLSQEqlFTPdz+0N/g==", UserID: "bc6b289c-f224-44d2-b4fa-09ebf87e6e51"},
	{ActorID: "AC211020jsPLuKAVS3iHzb5bxsxWtA==", UserID: "583ca362-6061-442a-87b3-0616c562bae3"},
	{ActorID: "AC230125U/nX8cASRh6kcmih5oCJRA==", UserID: "22e5ba9a-b664-48ff-a4b3-a34dde7608b8"},
	{ActorID: "AC220201VYO4/EcBRneStaMdAGFmiQ==", UserID: "64e30ab5-2f8d-4203-aa0c-dd07a80e811f"},
	{ActorID: "AC210728DjL7+k04Sw+aftHOu6QT6Q==", UserID: "3cc15f0c-3143-4a0d-b01d-4715fda2877a"},
	{ActorID: "ACDELNx/d0QW+l+PMnrMDPjw230524==", UserID: "67deb63b-a99f-4ab6-93bb-f999d835253f"},
	{ActorID: "AC211012zFr9FN0USf+myU8eDNT7XA==", UserID: "f80a44f0-8f55-4f9a-a6bf-a348424ac4df"},
	{ActorID: "AC230222R7RaJq73QEC+PL2GhDbU/A==", UserID: "69d152ad-6453-4f55-ad1f-13e0e599a2da"},
	{ActorID: "AC230111MJE2qcCQTQiuZtUSgISclg==", UserID: "b147e3ba-c2a2-4bf1-9cfc-e22311edd6df"},
	{ActorID: "AC230125TCdiZfxOQLGSOLN+Ku8APg==", UserID: "4e7ca37b-69ef-4124-a7d0-1c23acd5ebcb"},
	{ActorID: "AC211020jsPLuKAVS3iHzb5bxsxWtA==", UserID: "583ca362-6061-442a-87b3-0616c562bae3"},
}

const vendor = commonvgpb.Vendor_FEDERAL_BANK

// nolint: funlen
func (d *DeleteOnboardedUser) DoJob(ctx context.Context, req *JobRequest) error {
	/*
		1. DeleteToken
		2. Delete Device Registration
		2. DeleteUser
		3. DeleteActor
		4. onb details Delete
	*/

	var errs []error

	for _, u := range usersToDelete {
		onbDetails, errOnb := d.onbDao.GetOnboardingDetailsByActor(ctx, u.ActorID)
		if errOnb != nil {
			logger.Error(ctx, "error in GetOnboardingDetailsByActor", zap.Error(errOnb), zap.String("actorId", u.ActorID), zap.String("userId", u.UserID))
			errs = append(errs, pkgErr.Wrapf(errOnb, "actorId: %s, userId: %s", u.ActorID, u.UserID))
			continue
		}

		userResp, errUser := d.userClient.GetUser(ctx, &user.GetUserRequest{
			Identifier: &user.GetUserRequest_Id{
				Id: u.UserID,
			},
		})
		if grpcErr := epifigrpc.RPCError(userResp, errUser); grpcErr != nil {
			logger.Error(ctx, "error in GetUser", zap.Error(grpcErr), zap.String("actorId", u.ActorID), zap.String("userId", u.UserID))
			errs = append(errs, pkgErr.Wrapf(grpcErr, "actorId: %s, userId: %s", u.ActorID, u.UserID))
			continue
		}

		if errDeReg := d.permanentDeregisterDevice(ctx, d.authClient, u.ActorID); errDeReg != nil {
			logger.Error(ctx, "error in DeactivateDevice Device", zap.Error(errDeReg), zap.String("actorId", u.ActorID), zap.String("userId", u.UserID))
			errs = append(errs, pkgErr.Wrapf(errDeReg, "actorId: %s, userId: %s", u.ActorID, u.UserID))
			continue
		}

		if err := DeleteToken(ctx, d.authClient, userResp.GetUser().GetProfile().GetPhoneNumber()); err != nil {
			logger.Error(ctx, "error in DeleteToken", zap.Error(err), zap.String("actorId", u.ActorID), zap.String("userId", u.UserID))
			errs = append(errs, pkgErr.Wrapf(err, "actorId: %s, userId: %s", u.ActorID, u.UserID))
			continue
		}

		if err := DeleteDeviceRegistration(ctx, d.devRegDao, u.ActorID); err != nil {
			logger.Error(ctx, "error in DeleteDeviceRegistration", zap.Error(err), zap.String("actorId", u.ActorID), zap.String("userId", u.UserID))
			errs = append(errs, pkgErr.Wrapf(err, "actorId: %s, userId: %s", u.ActorID, u.UserID))
			continue
		}

		if err := DeleteEmploymentData(ctx, d.employmentClient, u.ActorID); err != nil {
			logger.Error(ctx, "error in DeleteEmploymentData", zap.Error(err), zap.String("actorId", u.ActorID), zap.String("userId", u.UserID))
			errs = append(errs, pkgErr.Wrapf(err, "actorId: %s, userId: %s", u.ActorID, u.UserID))
			continue
		}

		if err := DeleteUser(ctx, d.userClient, u.UserID); err != nil {
			logger.Error(ctx, "error in DeleteUser", zap.Error(err), zap.String("actorId", u.ActorID), zap.String("userId", u.UserID))
			errs = append(errs, pkgErr.Wrapf(err, "actorId: %s, userId: %s", u.ActorID, u.UserID))
			continue
		}

		if err := DeleteBankCustomer(ctx, d.bankCustomerClient, u.ActorID, vendor); err != nil {
			logger.Error(ctx, "error in DeleteBankCustomer", zap.Error(err), zap.String("actorId", u.ActorID), zap.String("userId", u.UserID))
			errs = append(errs, pkgErr.Wrapf(err, "actorId: %s, userId: %s", u.ActorID, u.UserID))
			continue
		}

		if err := DeleteActor(ctx, d.actorClient, u.ActorID); err != nil {
			logger.Error(ctx, "error in DeleteActor", zap.Error(err), zap.String("actorId", u.ActorID), zap.String("userId", u.UserID))
			errs = append(errs, pkgErr.Wrapf(err, "actorId: %s, userId: %s", u.ActorID, u.UserID))
			continue
		}

		if err := DeleteOnbDetails(ctx, d.onbDao, onbDetails.GetOnboardingId()); err != nil {
			logger.Error(ctx, "error in DeleteOnbDetails", zap.Error(err), zap.String("actorId", u.ActorID), zap.String("userId", u.UserID))
			errs = append(errs, pkgErr.Wrapf(err, "actorId: %s, userId: %s", u.ActorID, u.UserID))
			continue
		}
	}

	if len(errs) > 0 {
		return pkgErr.Errorf("errors occurred during deletion: %v", errs)
	}
	return nil
}

func DeleteEmploymentData(ctx context.Context, employmentClient employmentPb.EmploymentClient, actorId string) error {
	res, err := employmentClient.DeleteEmploymentData(ctx, &employmentPb.DeleteEmploymentDataRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil && !res.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in deleting employment data", zap.Error(err))
		return err
	}
	return nil
}

func DeleteToken(ctx context.Context, authClient auth.AuthClient, phoneNumber *commontypes.PhoneNumber) error {
	res, err := authClient.UpdateToken(ctx, &auth.UpdateTokenRequest{
		Status: auth.UpdateTokenRequest_DELETE,
		Identifier: &auth.UpdateTokenRequest_PhoneNumber{
			PhoneNumber: phoneNumber,
		},
		TokenTypes: []auth.TokenType{
			auth.TokenType_ACCESS_TOKEN,
			auth.TokenType_REFRESH_TOKEN,
		},
		TokenUpdationReason: auth.TokenDeletionReason_TOKEN_DELETION_REASON_RESET_USER,
	})

	return epifigrpc.RPCError(res, err)
}

func DeleteUser(ctx context.Context, userClient user.UsersClient, userId string) error {
	res, err := userClient.DeleteUser(ctx, &user.DeleteUserRequest{
		UserId: userId,
		DeletionDetails: &user.DeletionDetails{
			DeletionReason: user.DeletionDetails_DELETION_REASON_SCRIPT,
		},
	})
	return epifigrpc.RPCError(res, err)
}

func DeleteActor(ctx context.Context, actorClient actor.ActorClient, actorId string) error {
	res, err := actorClient.DeleteActor(ctx, &actor.DeleteActorRequest{
		ActorId: actorId,
	})
	return epifigrpc.RPCError(res, err)
}

func DeleteOnbDetails(ctx context.Context, onboardingDao onbDao.OnboardingDao, onbId string) error {
	return onboardingDao.Delete(ctx, onbId)
}

func DeleteDeviceRegistration(ctx context.Context, dao authDao.DeviceRegistrationDao, actorId string) error {
	devReg, err := dao.GetDeviceRegistration(ctx, actorId, "")
	if err != nil {
		if pkgErr.Is(err, gorm.ErrRecordNotFound) {
			logger.Info(ctx, "no active device registration record found for actor")
			return nil
		}
		return pkgErr.Wrap(err, "error finding dev reg record from actor")
	}

	if err = dao.DeleteDeviceRegistration(ctx, actorId, devReg.GetDeviceId()); err != nil {
		if pkgErr.Is(err, gorm.ErrRecordNotFound) {
			logger.Info(ctx, "no active device registration record found for deletion")
			return nil
		}
		return pkgErr.Wrap(err, "error deleting dev reg record from actor and device")
	}

	return nil
}

func DeleteBankCustomer(ctx context.Context, bankCustomerClient bankCustomerPb.BankCustomerServiceClient, actorId string, vendor commonvgpb.Vendor) error {
	res, err := bankCustomerClient.DeleteBankCustomer(ctx, &bankCustomerPb.DeleteBankCustomerRequest{
		ActorId: actorId,
		Vendor:  vendor,
	})
	return epifigrpc.RPCError(res, err)
}

func (d *DeleteOnboardedUser) permanentDeregisterDevice(ctx context.Context, authClient auth.AuthClient, actorId string) error {
	deActResp, errDeAct := authClient.DeactivateDevice(ctx, &auth.DeactivateDeviceRequest{
		ActorId:          actorId,
		DeactivationType: auth.DeactivateDeviceRequest_DEACTIVATION_TYPE_PERMANENT,
	})

	return epifigrpc.RPCError(deActResp, errDeAct)
}
