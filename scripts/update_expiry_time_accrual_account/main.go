// nolint: goimports
// Script to update the expiry time of point buckets and resolve account balances.
package main

import (
	"context"
	std_errors "errors"
	"flag"
	"fmt"
	"strings"
	"time"

	"github.com/epifi/be-common/pkg/storage"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/jackc/pgconn"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/accrual"
	"github.com/epifi/gamma/accrual/dao"
	dm "github.com/epifi/gamma/accrual/dao/model"
	"github.com/epifi/gamma/scripts/update_expiry_time_accrual_account/config"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/idgen"
)

var (
	batchSize       = flag.Int("batchSize", 100, "Number of accounts to process in each batch")
	sleepPerAccount = flag.Int64("sleepPerAccount", 50, "Sleep duration in ms after processing each account (for rate limiting, if needed)")
	sleepPerPage    = flag.Int64("sleepPerPage", 200, "Sleep duration in ms after processing each page of accounts (for rate limiting, if needed)")
)

func main() {
	flag.Parse()
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
	}

	db, err := storagev2.NewPostgresDBWithConfig(conf.AccrualDb, false)
	if err != nil {
		logger.Panic("failed to establish DB conn", zap.Error(err))
	}
	sqlDB, err := db.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	defer func() { _ = sqlDB.Close() }()

	ctx := context.Background()

	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	accountDao := dao.NewAccountDaoImpl(db, domainIdGenerator)
	txnExecutor := storagev2.NewGormTxnExecutor(db)
	transactionDaoImpl := dao.NewTransactionDaoImpl(db)
	pointBucketDaoImpl := dao.NewPointBucketDaoImpl(db)
	transactionSplitDaoImpl := dao.NewTransactionSplitDaoImpl(db)
	accrualService := accrual.NewService(txnExecutor, domainIdGenerator, accountDao, transactionDaoImpl, pointBucketDaoImpl, transactionSplitDaoImpl, false)

	processAccounts(ctx, db, accrualService, txnExecutor)
}

func processAccounts(ctx context.Context, db *gorm.DB, accrualService *accrual.Service, txnExecutor storagev2.TxnExecutor) {
	logger.Info(ctx, "starting script to update expiry time for accrual accounts")

	// 1. Define constants for time ranges and new expiry
	// Since Script has been run already for the buckets having expiry_time 29th July 12PM, now sliding the window till 2nd Aug.
	startTime := time.Date(2025, 7, 29, 12, 00, 00, 0, datetime.IST)
	endTime := time.Date(2025, 8, 2, 00, 00, 00, 0, datetime.IST)
	newExpiryTime := time.Date(2025, 8, 5, 00, 00, 30, 0, datetime.IST)

	var (
		lastAccountID  string
		totalSucceeded int
		totalFailed    int
		totalFetched   int
	)

	for {
		// 2. Fetch distinct account IDs with buckets in the specified range
		var accountIDs []string
		query := db.Model(&dm.PointBucket{}).
			Where("expiry_time >= ? AND expiry_time <= ?", startTime.UTC(), endTime.UTC()).
			Where("current_balance > 0").
			Where("status = ?", "AVAILABLE")

		if lastAccountID != "" {
			query = query.Where("account_id > ?", lastAccountID)
		}

		if err := query.Group("account_id").Order("account_id ASC").Limit(*batchSize).Pluck("account_id", &accountIDs).Error; err != nil {
			logger.Error(ctx, "failed to fetch account IDs", zap.Error(err))
			return
		}

		if len(accountIDs) == 0 {
			logger.Info(ctx, "Got no accountIds, finishing up.")
			break
		}
		totalFetched += len(accountIDs)

		var (
			batchSucceeded int
			batchFailed    int
		)

		// 3. Process each account
		for _, accountID := range accountIDs {
			err := txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
				txDb := gormctxv2.FromContextOrDefault(txnCtx, db)

				// a. Initial resolveBalance to sync the account and expire any old buckets
				if _, err := accrualService.ResolveBalanceExported(txnCtx, accountID); err != nil {
					return errors.Wrap(err, "error in initial resolveBalance")
				}

				// b. Fetch all bucket IDs for the account that need updating.
				var bucketIDsToUpdate []string
				if err := txDb.Model(&dm.PointBucket{}).
					Where("account_id = ?", accountID).
					Where("expiry_time >= ? AND expiry_time <= ?", startTime.UTC(), endTime.UTC()).
					Where("current_balance > 0").
					Where("status = ?", "AVAILABLE").
					Pluck("id", &bucketIDsToUpdate).Error; err != nil {
					return errors.Wrap(err, "failed to fetch point bucket IDs for update")
				}

				if len(bucketIDsToUpdate) == 0 {
					return nil // No buckets to update
				}

				// c. Iterate and update each bucket with a unique expiry time
				var (
					updatedCount         int64
					currentNewExpiryTime = newExpiryTime.UTC()
				)

				for _, bucketID := range bucketIDsToUpdate {
					var updated bool
					for attempt := 0; attempt < 3; attempt++ {
						// Sanitize bucket ID for use in savepoint name (replace hyphens with underscores)
						sanitizedBucketID := strings.ReplaceAll(bucketID, "-", "_")
						savepointName := fmt.Sprintf("sp_%s_%d", sanitizedBucketID, attempt)
						if err := txDb.SavePoint(savepointName).Error; err != nil {
							return errors.Wrap(err, "failed to create savepoint")
						}
						updateResult := txDb.Model(&dm.PointBucket{}).
							Where("id = ?", bucketID).
							Update("expiry_time", currentNewExpiryTime)

						if updateResult.Error != nil {
							if err := txDb.RollbackTo(savepointName).Error; err != nil {
								return errors.Wrapf(err, "failed to rollback to savepoint %s after update error: %v", savepointName, updateResult.Error)
							}
							var pgErr *pgconn.PgError
							// Check for unique violation error (code 23505 for PostgreSQL)
							if std_errors.As(updateResult.Error, &pgErr) && pgErr.Code == storage.PostgresDuplicateKeyValueErrorCode {
								logger.Info(txnCtx, "unique constraint violation on acc_id_expiry_time, retrying with new time",
									zap.String("bucket_id", bucketID),
									zap.Int("attempt", attempt+1))
								currentNewExpiryTime = currentNewExpiryTime.Add(time.Second)
								continue // Try again with the new timestamp
							}
							// For any other error, fail the transaction for this account
							return errors.Wrap(updateResult.Error, "failed to update point_bucket with a non-unique-constraint error")
						}
						// Update successful, release the savepoint to free memory
						if err := txDb.Exec(fmt.Sprintf("RELEASE SAVEPOINT %s", savepointName)).Error; err != nil {
							// Log warning but don't fail the transaction as the update was successful
							logger.Warn("failed to release savepoint, continuing", zap.String("savepoint", savepointName), zap.Error(err))
						}
						updated = true
						updatedCount++
						currentNewExpiryTime = currentNewExpiryTime.Add(time.Second)
						break
					}
					if !updated {
						logger.Error(txnCtx, "failed to update bucket after 3 attempts, rolling back for account", zap.String("bucket_id", bucketID), zap.String("account_id", accountID))
						return errors.New("failed to update bucket after max retries")
					}
				}

				// d. Recalculate and update account balance if any bucket was updated
				if updatedCount > 0 {
					logger.Info(txnCtx, "updated buckets for account", zap.String("account_id", accountID), zap.Int64("rows_affected", updatedCount))
					if _, err := accrualService.CalculateAndUpdateAccountBalance(txnCtx, accountID); err != nil {
						return errors.Wrap(err, "error in final CalculateAndUpdateAccountBalance")
					}
				}

				return nil
			})

			if err != nil {
				logger.Error(ctx, "failed to process account", zap.String("account_id", accountID), zap.Error(err))
				batchFailed++
				continue // Skip to next account on error
			}

			batchSucceeded++
			time.Sleep(time.Duration(*sleepPerAccount) * time.Millisecond)
		}

		totalSucceeded += batchSucceeded
		totalFailed += batchFailed

		if len(accountIDs) > 0 {
			lastAccountID = accountIDs[len(accountIDs)-1]
		}
		logger.Info(ctx, "processed a batch",
			zap.Int("succeeded", batchSucceeded),
			zap.Int("failed", batchFailed),
		)
		time.Sleep(time.Duration(*sleepPerPage) * time.Millisecond)
	}

	logger.Info(ctx, "script finished successfully",
		zap.Int("total_accounts_fetched", totalFetched),
		zap.Int("total_accounts_succeeded", totalSucceeded),
		zap.Int("total_accounts_failed", totalFailed),
	)
}
