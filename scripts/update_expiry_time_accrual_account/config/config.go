package config

import (
	"fmt"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	once       sync.Once
	config     *Config
	err        error
	_, b, _, _ = runtime.Caller(0)
)

const (
	dbCredentials = "DbCredentials"
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})
	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, "update_expiry_time_accrual_account")
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	keyToIdMap := cfg.AddPgdbSslCertSecretIds(conf.AccrualDb, conf.Secrets.Ids)
	keyToSecret, err := cfg.LoadSecrets(keyToIdMap, conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		return nil, err
	}
	if err = updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}
	return conf, nil
}

func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointInConfig(c.AccrualDb, dbServerEndpoint)
	cfg.UpdatePGDBSecretValues(c.AccrualDb, c.Secrets, keyToSecret)
	if c.Application.Environment == cfg.TestEnv || c.Application.Environment == cfg.DevelopmentEnv {
		cfg.UpdateDbUsernamePasswordInConfig(c.AccrualDb, c.Secrets.Ids[dbCredentials])
		return nil
	}
	if _, ok := keyToSecret[dbCredentials]; !ok {
		return fmt.Errorf("db username password not fetched from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.AccrualDb, keyToSecret[dbCredentials])
	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..")
	return configPath
}

type Config struct {
	Application *Application
	AccrualDb   *cfg.DB
	Aws         *cfg.AWS
	Secrets     *cfg.Secrets
}

type Application struct {
	Name        string
	Environment string
}
