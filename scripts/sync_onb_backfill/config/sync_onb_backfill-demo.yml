Application:
  Environment: "demo"
  Name: "sync_onb_backfill"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "./crdb/demo/"
  SSLRootCert: "demo/cockroach/ca.crt"
  SSLClientCert: "demo/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "demo/cockroach/client.epifi_dev_user.key"

Aws:
  Region: "ap-south-1"

SyncOnboardingPublisher:
  QueueName: "demo-sync-onboarding-queue"
