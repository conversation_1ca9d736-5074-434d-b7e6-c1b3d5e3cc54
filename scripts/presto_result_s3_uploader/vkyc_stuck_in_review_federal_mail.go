// nolint: funlen
package main

import (
	"bytes"
	"context"
	"fmt"
	"time"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	beEvent "github.com/epifi/be-common/pkg/events"

	"github.com/samber/lo"

	"github.com/google/uuid"
	"github.com/jszwec/csvutil"
	"github.com/pkg/errors"
	"github.com/slack-go/slack"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/cx/watson"
	"github.com/epifi/gamma/api/kyc/vkyc"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	vkycPb "github.com/epifi/gamma/api/vendorgateway/vkyc"
	kycTypeChangePb "github.com/epifi/gamma/api/vendornotification/openbanking/kyctypechange/federal"
	vkycDao "github.com/epifi/gamma/kyc/vkyc/dao"
	"github.com/epifi/gamma/pkg/cx"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
	"github.com/epifi/gamma/scripts/presto_result_s3_uploader/config"
	"github.com/epifi/gamma/scripts/presto_result_s3_uploader/events"
	"github.com/epifi/gamma/user/onboarding/stageproc"
)

const (
	PollInterval            = 4 * time.Second
	inReviewIssueCategoryId = "e596e7ea-0d2f-5818-b54a-ef14724a7b62"
)

type VkycStuckInReviewStateFederalMail struct {
	vkycCustInfoDao         vkycDao.VKYCKarzaCustomerInfoDao
	vkycSummaryInfoDao      vkycDao.VKYCSummaryDao
	commsClient             comms.CommsClient
	conf                    *config.Config
	usersClient             user.UsersClient
	actorClient             actor.ActorClient
	customerVgClient        customer.CustomerClient
	fedVkycUpdPublisher     queue.Publisher
	vkycClient              vkyc.VKYCClient
	slack                   *slack.Client
	bcClient                bankcust.BankCustomerServiceClient
	savingsClient           savingsPb.SavingsClient
	vkycKarzaCallHistoryDao vkycDao.VKYCKarzaCallHistoryDao
	watsonClient            watson.WatsonClient
	eventBroker             beEvent.Broker
}

var (
	// path to store summary id of vkyc stuck in review state for more than 3 days
	inReviewS3OutputPath = "PRESTO/SUMMARY_ID/VKYC/REVIEW_STATE/%v.csv"
	// date format YYYY-MM-DD
	inReviewDateFormat = "2006-01-02"
	// min date till we have to fetch users stuck in review state
	inReviewDateFromFetch = "2025-04-01"
	// vkyc state of user
	inReviewVkycState        = "VKYC_SUMMARY_STATUS_IN_REVIEW"
	retryableRejectionReason = []vkyc.VKYCKarzaAuditorFailureReason{
		vkyc.VKYCKarzaAuditorFailureReason_VKYC_KARZA_AUDITOR_FAILURE_REASON_NO_LIVE_SIGNATURE,
		vkyc.VKYCKarzaAuditorFailureReason_VKYC_KARZA_AUDITOR_FAILURE_REASON_PAN_IS_NOT_FULLY_CAPTURED,
		vkyc.VKYCKarzaAuditorFailureReason_VKYC_KARZA_AUDITOR_FAILURE_REASON_PAN_OCR_ISSUE,
		vkyc.VKYCKarzaAuditorFailureReason_VKYC_KARZA_AUDITOR_FAILURE_REASON_PAN_PHOTO_NOT_CLEAR,
		vkyc.VKYCKarzaAuditorFailureReason_VKYC_KARZA_AUDITOR_FAILURE_REASON_VIDEO_NOT_AVAILABLE,
		vkyc.VKYCKarzaAuditorFailureReason_VKYC_KARZA_AUDITOR_FAILURE_REASON_NETWORK_ISSUES,
		vkyc.VKYCKarzaAuditorFailureReason_VKYC_KARZA_AUDITOR_FAILURE_REASON_VIDEO_NOT_CLEAR_OR_NOT_PRESENT,
		vkyc.VKYCKarzaAuditorFailureReason_VKYC_KARZA_AUDITOR_FAILURE_REASON_AUDIO_NOT_CLEAR_OR_NOT_PRESENT,
		vkyc.VKYCKarzaAuditorFailureReason_VKYC_KARZA_AUDITOR_FAILURE_REASON_OTHERS,
	}
)

func (v *VkycStuckInReviewStateFederalMail) GetQueryStorageInfo() []*QueryStorageInfo {
	dt := time.Now().AddDate(0, 0, -1)
	dateTillFetch := dt.Format(inReviewDateFormat)
	args, query := inReviewGetSnowflakeDetails(dateTillFetch)
	file := inReviewGetS3File()
	var queryStorageInfos []*QueryStorageInfo

	queryStorageInfos = append(queryStorageInfos, &QueryStorageInfo{
		queryArgs:   args,
		prestoQuery: query,
		s3File:      file,
	})
	return queryStorageInfos
}

// return snowflake query and args
func inReviewGetSnowflakeDetails(dateTillFetch string) ([]interface{}, string) {
	// since we have to fetch vkyc summary ids whose account will expire in t-2 days
	args := []interface{}{inReviewVkycState, dateTillFetch, inReviewDateFromFetch}
	query := "select id from VKYC_SUMMARIES where status = ? AND updated_at <= ? AND updated_at >= ?"
	return args, query
}

// return s3 file name
func inReviewGetS3File() string {
	s3OutputPathWithDate := fmt.Sprintf(inReviewS3OutputPath, time.Now().Format("01-02-2006"))
	return s3OutputPathWithDate
}

type fedDetail struct {
	ActorId       string              `csv:"Actor Id"`
	EkycRrnNo     string              `csv:"EKYCRRN No"`
	TransactionId string              `csv:"Transaction Id"`
	UpdatedAt     time.Time           `csv:"Updated At"`
	FedVkycStatus customer.VKYCStatus `csv:"Federal Vkyc status"`
	FedVkycRemark string              `csv:"Federal vkyc remark"`
	FedErr        error               `csv:"Federal vkyc error"`
	SummaryId     string              `csv:"Summary Id"`
}

// fetch user stuck in review state for t-2 date
// check their current vkyc state
// create a csv file and append ekycrrn and transcationid
// mail fed to inform about those users
// nolint:funlen
func (v *VkycStuckInReviewStateFederalMail) DoJob(summaryIds []string, index int) error {
	dt := time.Now().AddDate(0, 0, -1)
	dateTillFetch := dt.Format(inReviewDateFormat)
	ctx := context.Background()
	var fedDetails []*fedDetail
	var idNotInReviewState []string
	var idInBlockedState []string
	var failedSummaryCount = 0
	var actionablePendingInreview int64

	for _, summaryId := range summaryIds {
		vkycSummary, summaryErr := v.vkycSummaryInfoDao.GetById(ctx, summaryId)
		if summaryErr != nil {
			logger.Error(ctx, fmt.Sprintf("Error in fetch summary status for summary id: %v", summaryId))
			failedSummaryCount++
			continue
		}
		newCtx := epificontext.CtxWithActorId(ctx, vkycSummary.GetActorId())
		actorRes, actorErr := v.actorClient.GetActorById(newCtx, &actor.GetActorByIdRequest{Id: vkycSummary.GetActorId()})
		if te := epifigrpc.RPCError(actorRes, actorErr); te != nil {
			logger.Error(ctx, fmt.Sprintf("Error in fetch entity id for summary id: %v", summaryId), zap.Error(te))
			failedSummaryCount++
			continue
		}
		userRes, userErr := v.usersClient.GetUser(newCtx, &user.GetUserRequest{Identifier: &user.GetUserRequest_Id{Id: actorRes.GetActor().GetEntityId()}})
		if te := epifigrpc.RPCError(userRes, userErr); te != nil {
			logger.Error(ctx, fmt.Sprintf("Error in fetch user detail for summary id: %v", summaryId))
			failedSummaryCount++
			continue
		}
		// ensure account should not be in blocked state
		if userRes.GetUser().GetAccessRevokeDetails().GetAccessRevokeStatus() == user.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED {
			idInBlockedState = append(idInBlockedState, summaryId)
			failedSummaryCount++
			continue
		}

		if vkycSummary.GetStatus() != vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW {
			idNotInReviewState = append(idNotInReviewState, summaryId)
			continue
		}

		essentialsResp, err := v.savingsClient.GetSavingsAccountEssentials(newCtx, &savingsPb.GetSavingsAccountEssentialsRequest{
			Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
				ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
					ActorId:     vkycSummary.GetActorId(),
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		})
		if te := epifigrpc.RPCError(essentialsResp, err); te != nil && !essentialsResp.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, fmt.Sprintf("Error in fetch savings detail for summary id: %v", summaryId))
			failedSummaryCount++
			continue
		}
		if essentialsResp.GetAccount().GetState() == savingsPb.State_CLOSED {
			fmt.Printf("users account is closed %v", savingsPb.State_CLOSED.String())
			continue
		}

		// following doa return latest info for summary id
		custInfoDetail, custInfoErr := v.vkycCustInfoDao.GetByVkycSummaryId(newCtx, summaryId)
		if custInfoErr != nil {
			logger.Error(ctx, fmt.Sprintf("Error in fetch customer info status for summary id: %v", summaryId))
			failedSummaryCount++
			continue
		}

		vkycFedStatus, vkycFedMessege, err := v.getFedVkycStatus(newCtx, custInfoDetail.GetTransactionMetadata().GetEkycRrnNo())
		failedToUpdate := v.handleEnquiryApiResp(newCtx, vkycFedStatus, custInfoDetail.GetTransactionMetadata().GetEkycRrnNo(), custInfoDetail.GetTransactionId(), vkycSummary, userRes.GetUser())
		if custInfoDetail.GetTransactionId() != "" && custInfoDetail.GetTransactionMetadata().GetEkycRrnNo() != "" && failedToUpdate {
			stuckSince := time.Now().Add(-48 * time.Hour)
			if time.Unix(vkycSummary.GetUpdatedAt().GetSeconds(), 0).Before(stuckSince) {
				actionablePendingInreview++
				fedDetails = append(fedDetails, &fedDetail{
					ActorId:       vkycSummary.GetActorId(),
					EkycRrnNo:     custInfoDetail.GetTransactionMetadata().GetEkycRrnNo(),
					TransactionId: custInfoDetail.GetTransactionId(),
					UpdatedAt:     time.Unix(vkycSummary.GetUpdatedAt().GetSeconds(), 0),
					FedVkycStatus: vkycFedStatus,
					FedVkycRemark: vkycFedMessege,
					FedErr:        err,
					SummaryId:     summaryId,
				})
				if !cx.IsIncidentPresent(ctx, v.watsonClient, vkycSummary.GetActorId(), inReviewIssueCategoryId) {
					v.eventBroker.AddToBatch(newCtx, events.NewVkycInReviewAutoIdCreationEvent(vkycSummary.GetActorId(), stageproc.GenerateClientRequestId(vkycSummary.GetActorId()), inReviewIssueCategoryId, vkycSummary.GetStatus()))
				}
			}
		} else {
			failedSummaryCount++
			logger.Error(ctx, fmt.Sprintf("Either transcation or ekkycrrn not avaiable for summary id or failed to re-register user for vkyc: %v", summaryId))
		}
		time.Sleep(PollInterval)
	}
	logger.Info(ctx, fmt.Sprintf("Failed to send mail to %v summary id ", failedSummaryCount))
	logger.Info(ctx, fmt.Sprintf("Following summary id are not in review state: %v", idNotInReviewState))
	logger.Info(ctx, fmt.Sprintf("Following summary id are blocked state: %v", idInBlockedState))
	_ = v.sendSlackAlert(fedDetails, actionablePendingInreview)
	if len(fedDetails) == 0 {
		logger.Info(ctx, "No user stuck in review stage skipping sending mail to fed")
		return nil
	}
	fileName, bytes, err := createAndConvertToBytesCsvFile(ctx, fedDetails, "user_stuck"+dateTillFetch)
	if err != nil {
		return err
	}
	err = PopulateAndSendEmail(ctx, fileName, bytes, v.conf, v.commsClient)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Error in sending mail to fed to for till date: %v", dateTillFetch))
		return err
	}
	logger.Info(ctx, fmt.Sprintf("Sucessfully send mail to following ekyc and transactionID %v", fedDetails))
	return nil
}

func PopulateAndSendEmail(ctx context.Context, fileName string, fileData []byte, conf *config.Config, commsClient comms.CommsClient,
) error {
	attachments := []*comms.EmailMessage_Attachment{
		{
			FileContent:    fileData,
			FileName:       fileName,
			Disposition:    comms.Disposition_ATTACHMENT,
			AttachmentType: "text/comma-separated-values",
		},
	}
	resp, err := commsClient.SendMessage(ctx, &comms.SendMessageRequest{
		Type:   comms.QoS_GUARANTEED,
		Medium: comms.Medium_EMAIL,
		UserIdentifier: &comms.SendMessageRequest_EmailId{
			EmailId: conf.UserStuckInVkycReview.ToEmailId,
		},
		Message: &comms.SendMessageRequest_Email{
			Email: &comms.EmailMessage{
				FromEmailId:   conf.UserStuckInVkycReview.FromEmailId,
				FromEmailName: conf.UserStuckInVkycReview.FromEmailName,
				ToEmailName:   conf.UserStuckInVkycReview.ToEmailId,
				EmailOption: &comms.EmailOption{
					Option: &comms.EmailOption_UserStuckInVkycReviewStateFederalEmailOption{
						UserStuckInVkycReviewStateFederalEmailOption: &comms.UserStuckInVkycReviewStateFederalEmailOption{
							EmailType: comms.EmailType_USER_STUCK_IN_VKYC_REVIEW_STATE_FEDERAL_EMAIL,
							Option: &comms.UserStuckInVkycReviewStateFederalEmailOption_UserStuckInVkycReviewStateFederalEmailOptionV1{
								UserStuckInVkycReviewStateFederalEmailOptionV1: &comms.UserStuckInVkycReviewStateFederalEmailOptionV1{
									TemplateVersion: comms.TemplateVersion_VERSION_V1,
								},
							},
						},
					},
				},
				Attachment: attachments,
			},
		},
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		logger.Error(ctx, "error while emailing bulk user details CSV", zap.Error(grpcErr))
		return grpcErr
	}
	return nil
}

func (v *VkycStuckInReviewStateFederalMail) GetS3FileName() []string {
	var fileNames []string
	s3InputPathWithDate := fmt.Sprintf(inReviewS3OutputPath, time.Now().Format("01-02-2006"))
	fileNames = append(fileNames, s3InputPathWithDate)
	return fileNames
}

// check vkyc status on fed end
func (v *VkycStuckInReviewStateFederalMail) getFedVkycStatus(ctx context.Context, ekycRrn string) (customer.VKYCStatus, string, error) {
	vkycEnquireStatusResp, vkycEnquireErr := v.customerVgClient.EnquireVKYCStatus(ctx, &customer.EnquireVKYCStatusRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		EkycRrn: ekycRrn,
	})
	if te := epifigrpc.RPCError(vkycEnquireStatusResp, vkycEnquireErr); te != nil {
		logger.Error(ctx, "Error in fetching ekyc status from federal bank", zap.Error(te))
		return customer.VKYCStatus_VKYC_STATUS_UNSPECIFIED, "", te
	}
	return vkycEnquireStatusResp.GetVkycStatus(), vkycEnquireStatusResp.GetMessage(), nil
}

// return false if don't want to add in fedDetails
func (v *VkycStuckInReviewStateFederalMail) handleEnquiryApiResp(ctx context.Context, status customer.VKYCStatus, ekycRrn, transactionId string, vkycSummary *vkyc.VKYCSummary, user *user.User) bool {
	callbackMissingSince := time.Now().Add(-240 * time.Hour)
	switch status {
	case customer.VKYCStatus_VKYC_STATUS_APPROVED:
		bcCustResp, bcCustErr := v.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
			Identifier: &bankcust.GetBankCustomerRequest_ActorId{
				ActorId: vkycSummary.GetActorId(),
			},
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		})
		if te := epifigrpc.RPCError(bcCustResp, bcCustErr); te != nil && !bcCustResp.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "Error while fetching bank customer info", zap.Error(te))
			return true
		}
		if bcCustResp.GetBankCustomer().GetStatus() != bankcust.Status_STATUS_ACTIVE {
			logger.Info(ctx, fmt.Sprintf("updating the vkyc approved %v", vkycSummary.GetActorId()))
			_ = v.publishVkycUpdateEvent(ctx, ekycRrn, transactionId, "Y", "vkyc approved taking enquiry as proxy")
			return false
		}

		// ignoring dedupe call for vkyc lost users as due to some issue at bank end for these users we are not getting valid dedupe response
		// https://monorail.pointz.in/p/fi-app/issues/detail?id=90252
		if !lo.Contains(vkycPkg.ReAttemptVKYC, vkycSummary.GetActorId()) {
			dedupeReq := &customer.DedupeCheckRequest{
				Header: &commonvgpb.RequestHeader{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
				},
				PanNumber:       user.GetProfile().GetPAN(),
				MobileNum:       user.GetProfile().GetPhoneNumber().ToString(),
				PhoneNumber:     user.GetProfile().GetPhoneNumber(),
				UserId:          user.GetId(),
				UidReferenceKey: ekycRrn,
				DateOfBirth:     user.GetProfile().GetDateOfBirth(),
				RequestId:       idgen.FederalRandomDigitsSequence("DDPEVKJ", 5),
			}

			dedupeResp, dedupeErr := v.customerVgClient.DedupeCheck(ctx, dedupeReq)
			if te := epifigrpc.RPCError(dedupeResp, dedupeErr); te != nil {
				logger.Error(ctx, "error in getting dedupe response from vkyc", zap.Error(te))
				return true
			}

			if dedupeResp.GetDedupeStatus() != customer.DedupeStatus_CUSTOMER_EXISTS &&
				// ignoring DedupeStatus_CUSTOMER_EXISTS_KYC_NOT_VALID status as this is for kyc non compliance status
				dedupeResp.GetDedupeStatus() != customer.DedupeStatus_CUSTOMER_EXISTS_KYC_NOT_VALID {
				_ = v.sendSlackTrigger(ctx, "user's vkyc is approved taking enquire as proxy and dedupe doesn't return customer exist")
				return true
			}
		}
		logger.Info(ctx, fmt.Sprintf("updating the vkyc approved %v", vkycSummary.GetActorId()))
		_ = v.publishVkycUpdateEvent(ctx, ekycRrn, transactionId, "Y", "vkyc approved taking enquiry as proxy")
		v.eventBroker.AddToBatch(ctx, events.NewVkycInReviewAutoIdResolutionEvent(vkycSummary.GetActorId(), stageproc.GenerateClientRequestId(vkycSummary.GetActorId()), inReviewIssueCategoryId, vkycSummary.GetStatus()))
	case customer.VKYCStatus_VKYC_STATUS_REJECTED:
		isEligibleForRetry, err := v.isretryableRejection(ctx, vkycSummary.GetActorId())
		if err != nil {
			logger.Info(ctx, fmt.Sprintf("error while checking isretryableRejection  %v", vkycSummary.GetActorId()))
			return true
		}
		if isEligibleForRetry {
			logger.Info(ctx, fmt.Sprintf("re-registering user for vkyc as auditor failure reason is retryable %v", vkycSummary.GetActorId()))
			err = v.reRegisterForVKYC(ctx, vkycSummary.GetActorId())
			if err != nil {
				return true
			}
			return false
		}

		logger.Info(ctx, fmt.Sprintf("updating the vkyc rejected %v", vkycSummary.GetActorId()))
		_ = v.publishVkycUpdateEvent(ctx, ekycRrn, transactionId, "R2", "vkyc rejected taking enquiry as proxy")
		v.eventBroker.AddToBatch(ctx, events.NewVkycInReviewAutoIdResolutionEvent(vkycSummary.GetActorId(), stageproc.GenerateClientRequestId(vkycSummary.GetActorId()), inReviewIssueCategoryId, vkycSummary.GetStatus()))
	case customer.VKYCStatus_VKYC_STATUS_AUDITOR_RECAPTURE:
		logger.Info(ctx, fmt.Sprintf("re-registering user for vkyc %v", vkycSummary.GetActorId()))
		err := v.reRegisterForVKYC(ctx, vkycSummary.GetActorId())
		if err != nil {
			return true
		}
		return false
	case customer.VKYCStatus_VKYC_STATUS_AGENT_CALL_BACK_MISSING:
		if time.Unix(vkycSummary.GetUpdatedAt().GetSeconds(), 0).Before(callbackMissingSince) {
			logger.Info(ctx, fmt.Sprintf("re-registering user for vkyc as agent success callback missing for more than 10 days%v", vkycSummary.GetActorId()))
			err := v.reRegisterForVKYC(ctx, vkycSummary.GetActorId())
			if err != nil {
				return true
			}
			return false
		}
		logger.Info(ctx, fmt.Sprintf("Trigger agent callback %v", vkycSummary.GetActorId()))
		triggerCallBackResp, triggerCallBackErr := v.vkycClient.TriggerCallback(ctx, &vkyc.TriggerCallbackRequest{
			ActorIds:     []string{vkycSummary.GetActorId()},
			CallbackType: vkycPb.CallbackType_CALLBACK_TYPE_AGENT,
		})
		if err := epifigrpc.RPCError(triggerCallBackResp, triggerCallBackErr); err != nil {
			logger.Error(ctx, "Error while triggering vkyc callback agent", zap.Error(err))
			return true
		}
		return true
	case customer.VKYCStatus_VKYC_STATUS_AUDITOR_CALL_BACK_MISSING:
		if time.Unix(vkycSummary.GetUpdatedAt().GetSeconds(), 0).Before(callbackMissingSince) {
			logger.Info(ctx, fmt.Sprintf("re-registering user for vkyc as auditor success callback missing for more than 10 days%v", vkycSummary.GetActorId()))
			err := v.reRegisterForVKYC(ctx, vkycSummary.GetActorId())
			if err != nil {
				return true
			}
			return false
		}
		logger.Info(ctx, fmt.Sprintf("Trigger auditor callback %v", vkycSummary.GetActorId()))
		triggerCallBackResp, triggerCallBackErr := v.vkycClient.TriggerCallback(ctx, &vkyc.TriggerCallbackRequest{
			ActorIds:     []string{vkycSummary.GetActorId()},
			CallbackType: vkycPb.CallbackType_CALLBACK_TYPE_AUDITOR,
		})
		if err := epifigrpc.RPCError(triggerCallBackResp, triggerCallBackErr); err != nil {
			logger.Error(ctx, "Error while triggering vkyc callback for auditor", zap.Error(err))
			return true
		}
		return true
	default:
		return true
	}
	return false
}

func (v *VkycStuckInReviewStateFederalMail) publishVkycUpdateEvent(ctx context.Context, ekycRrn, transactionId, kycStatus, remarks string) error {
	beEvent := &kycTypeChangePb.KYCStateChangeEvent{
		UniqueUidaiRrn: ekycRrn,
		KycStatus:      kycStatus,
		Remarks:        remarks,
		EventTimestamp: timestamppb.Now(),
		EventId:        uuid.NewString(),
		TransactionId:  transactionId,
	}

	_, err := v.fedVkycUpdPublisher.Publish(ctx, beEvent)
	if err != nil {
		logger.Error(ctx, "error publishing vkyc update event to queue", zap.Error(err))
		return err
	}
	return nil
}

func (v *VkycStuckInReviewStateFederalMail) sendSlackTrigger(ctx context.Context, msg string) error {
	var (
		channelID = "C02Q2SZ7T26"
	)
	slackAttachment := slack.Attachment{
		Pretext: "<@D032WCQQ127> there is discrepancy in dedupe api response",
	}
	_, timestamp, errSlack := v.slack.PostMessage(channelID, slack.MsgOptionAttachments(slackAttachment))
	if errSlack != nil {
		logger.Error(ctx, "error posting message on slack", zap.Error(errSlack))
		return errSlack
	}
	logger.Info(ctx, "slack message posted", zap.String("timestamp", timestamp), zap.String("message", msg))
	return nil
}

func (v *VkycStuckInReviewStateFederalMail) sendSlackAlert(fedDetails []*fedDetail, actionablePendingInreview int64) error {
	var (
		channelID = "C01NTC58Z35"
	)
	dataBytes, err := csvutil.Marshal(fedDetails)
	if err != nil {
		return errors.Wrap(err, "error in csvUtil.Marshal")
	}
	dtString := datetime.TimestampToString(timestamppb.Now(), datetime.DATE_LAYOUT_YYYYMMDD, datetime.IST)
	title := fmt.Sprintf("VKYC in review for more 48 hours: %v", actionablePendingInreview)
	fileName := fmt.Sprintf("VKYC-in-review-%v.csv", dtString)
	csvReader := bytes.NewReader(dataBytes)
	params := slack.UploadFileV2Parameters{
		Reader:   csvReader,
		Filename: fileName,
		FileSize: csvReader.Len(),
		Title:    title,
		Channel:  channelID,
	}
	_, err = v.slack.UploadFileV2(params)
	if err != nil {
		logger.ErrorNoCtx("error sending slack alert", zap.Error(err))
		return errors.Wrap(err, "error in slackClient.UploadFile")
	}
	return nil
}

func (v *VkycStuckInReviewStateFederalMail) reRegisterForVKYC(ctx context.Context, actorId string) error {
	resp, err := v.vkycClient.ReRegisterVkycInfo(ctx, &vkyc.ReRegisterVkycInfoRequest{
		Identifier: &vkyc.ReRegisterVkycInfoRequest_ActorId{
			ActorId: actorId,
		},
		CallInfoSubStatus: vkyc.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_AUDITOR_RECAPTURE,
		AttemptSubStatus:  vkyc.VKYCAttemptSubStatus_VKYC_ATTEMPT_SUB_STATUS_CALL_FAILED_AUDITOR_RECAPTURE,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error while re-registering the user", zap.Error(te))
		return te
	}
	v.eventBroker.AddToBatch(ctx, events.NewVkycInReviewAutoIdResolutionEvent(actorId, stageproc.GenerateClientRequestId(actorId), inReviewIssueCategoryId, vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_RE_REGISTER))
	return nil
}

func (v *VkycStuckInReviewStateFederalMail) isretryableRejection(ctx context.Context, actorId string) (bool, error) {

	resp, errResp := v.vkycClient.GetVKYCSummary(ctx, &vkyc.GetVKYCSummaryRequest{
		ActorId: actorId,
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil {
		logger.Error(ctx, "error in fetching vkyc summary", zap.Error(err))
		return false, err
	}
	var inReviewCallInfos []*vkyc.VKYCKarzaCallInfo
	var vkycAttempts []*vkyc.VKYCAttempt
	var rejectionReason = vkyc.VKYCKarzaAuditorFailureReason_VKYC_KARZA_AUDITOR_FAILURE_REASON_UNSPECIFIED

	if len(resp.GetVkycRecord().GetVkycAttemptDataList()) == 0 {
		logger.Info(ctx, fmt.Sprintf("no vkycAttemptData found for actorId %v", actorId))
		return false, nil
	}
	for _, vkycAttemptData := range resp.GetVkycRecord().GetVkycAttemptDataList() {
		vkycAttempts = append(vkycAttempts, vkycAttemptData.GetVkycAttempt())
		for _, callInfoData := range vkycAttemptData.GetVkycKarzaCallInfoDataList() {
			logger.Info(ctx, fmt.Sprintf("checking for callInfoData id %v", callInfoData.GetVkycKarzaCallInfo().GetId()))
			logger.Info(ctx, fmt.Sprintf("logging GetVkycKarzaCallInfo().GetStatus() %v", callInfoData.GetVkycKarzaCallInfo().GetStatus()))
			if callInfoData.GetVkycKarzaCallInfo().GetStatus() == vkyc.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_IN_REVIEW {
				logger.Info(ctx, fmt.Sprintf("in review call info id %v", callInfoData.GetVkycKarzaCallInfo().GetId()))
				inReviewCallInfos = append(inReviewCallInfos, callInfoData.GetVkycKarzaCallInfo())
			}
		}
	}

	if len(inReviewCallInfos) > 0 {
		callHistoryList, err := v.vkycKarzaCallHistoryDao.GetByCallInfoId(ctx, inReviewCallInfos[0].GetId())
		if err != nil {
			logger.Error(ctx, "failed to fetch vkyc call history list by call info id", zap.Error(err))
			return false, err
		}

		for _, callHist := range callHistoryList {
			if callHist.GetMetadata().GetTransactionEvent().GetAuditorFailureReason() != vkyc.VKYCKarzaAuditorFailureReason_VKYC_KARZA_AUDITOR_FAILURE_REASON_UNSPECIFIED {
				rejectionReason = callHist.GetMetadata().GetTransactionEvent().GetAuditorFailureReason()
				logger.Info(ctx, fmt.Sprintf("auditor failure reason is %v for callInfoId %v", rejectionReason, callHist.GetId()))
			}
			if callHist.GetMetadata().GetCallEvent().GetAuditorFailureReasonV2() != vkyc.VKYCKarzaAuditorFailureReason_VKYC_KARZA_AUDITOR_FAILURE_REASON_UNSPECIFIED {
				rejectionReason = callHist.GetMetadata().GetCallEvent().GetAuditorFailureReasonV2()
				logger.Info(ctx, fmt.Sprintf("auditor failure reason is %v for callInfoId %v", rejectionReason, callHist.GetId()))
			}
		}
	}
	logger.Info(ctx, fmt.Sprintf("found rejectionReason %v", rejectionReason))
	return lo.Contains[vkyc.VKYCKarzaAuditorFailureReason](retryableRejectionReason, rejectionReason), nil
}
