// nolint:gosimple,staticcheck,gocritic,funlen,gosec,unparam,govet,dupl
package job

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/csv"
	json "encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	accountEnumsPb "github.com/epifi/gamma/api/accounts/enums"
	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	actorPb "github.com/epifi/gamma/api/actor"
	cardPb "github.com/epifi/gamma/api/card"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	savingsClientPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/card/dao"
	"github.com/epifi/gamma/card/dao/model"
	"github.com/epifi/gamma/scripts/cc_firefly/helper"
)

const (
	inputDateFormat               = "2006-01-02"
	dateDDMMYYYYFormat            = "02-01-2006"
	maxFileSizeForFed             = 2000
	refundEmailDataLayout         = "<tr><td>{#column1_val}</td> <td>{#column2_val}</td> <td>{#column3_val}</td> <td>{#column4_val}</td></tr>"
	txnAdditionalDetailsDelimiter = "/"
	externalTxnIdStringLength     = 12
)

var (
	targetEmail = []string{"<EMAIL>"}
	ccEmails    = []string{"<EMAIL>", "<EMAIL>", "<EMAIL>",
		"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
		"<EMAIL>", "<EMAIL>", "<EMAIL>",
		"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}
	// External txn id in trans particular for a forex refund would be an alphanumeric string
	// of length 12
	alphanumericForexExtTxnIdIdentifierRegex = regexp.MustCompile(`[a-zA-Z0-9]+`)
)

type ProcessCardForexTxnRefundJob struct {
	helper        *helper.Helper
	db            types.EpifiCRDB
	dcDb          dao.DebitCardPGDB
	profileClient profilePb.ProfileClient
}

func NewProcessCardForexTxnRefundJob(
	helper *helper.Helper,
	db types.EpifiCRDB,
	dcDb dao.DebitCardPGDB,
	profileClient profilePb.ProfileClient,
) *ProcessCardForexTxnRefundJob {
	return &ProcessCardForexTxnRefundJob{
		helper:        helper,
		db:            db,
		dcDb:          dcDb,
		profileClient: profileClient,
	}
}

type FileWithFileName struct {
	FileName   string
	FileMatrix [][]string
}

type ProcessDebitCardForexTxnRefundJobArgs struct {
	ExitAfterCount bool `json:"exitAfterCount"`
}

var (
	disallowedOperStatuses = []accountEnumsPb.OperationalStatus{
		accountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_CLOSED,
		accountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_DORMANT,
		accountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_INACTIVE,
	}
	disallowedFreezeStatuses = []accountEnumsPb.FreezeStatus{
		accountEnumsPb.FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE,
		accountEnumsPb.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE,
	}
)

const (
	// folderDateLayout format -> YYYYMMDD
	folderDateLayout       = "02-01-2006T15:04:05"
	folderName             = "forex_txn_refunds"
	recordLevelDateLayout  = "02/01/2006"
	gstPercentage          = 18.0
	txnRawDetailsDelimiter = "/"
	colonDelimiter         = ":"
	apiBatchLimitCount     = 100
)

func (p *ProcessCardForexTxnRefundJob) GetArgs() *ProcessDebitCardForexTxnRefundJobArgs {
	return &ProcessDebitCardForexTxnRefundJobArgs{}
}

func (p *ProcessCardForexTxnRefundJob) Run(ctx context.Context, argString string) error {
	args := &ProcessDebitCardForexTxnRefundJobArgs{}
	err := json.Unmarshal([]byte(argString), args)
	if err != nil {
		logger.Error(ctx, "error in unmarshalling job arguments", zap.Error(err))
		return err
	}
	rejectedFilePath := fmt.Sprintf("%s.csv", filepath.Join("forex-refund", "rejected", datetime.TimestampToString(timestamppb.Now(), "2006-01-02T15:04:05", datetime.IST)))
	dcForexRefundDao := dao.NewDcForexTxnRefundCrdb(p.dcDb, idgen.NewDomainIdGenerator(idgen.NewClock()))
	allRefunds, evaluatedRefunds, err := p.fetchRefundDataWithinTimeRange(ctx)
	if err != nil {
		logger.Error(ctx, "error in fetching forex refunds for the given time period", zap.Error(err))
		return err
	}
	logger.Info(ctx, fmt.Sprintf("%d evaluated refund records found", len(evaluatedRefunds)))
	if args.ExitAfterCount {
		return nil
	}
	erroredOutTxns := make(map[string]bool)
	inactiveAccountTxns := make(map[string]bool)
	orderIdentifiers := make([][]*orderPb.OrderIdentifier, apiBatchCount)
	for idx, ref := range evaluatedRefunds {
		orderIdRes, err := p.helper.PaymentClient.GetOrderId(ctx, &paymentPb.GetOrderIdRequest{TransactionId: ref.GetTxnId()})
		logger.Info(ctx, "order id fetched", zap.String(logger.TXN_ID, ref.GetTxnId()), zap.String(logger.ORDER_ID, orderIdRes.GetOrderId()))
		if te := epifigrpc.RPCError(orderIdRes, err); te != nil {
			logger.Error(ctx, "error in fetching order id for the txn", zap.Error(te), zap.String(logger.TXN_ID, ref.GetTxnId()))
			erroredOutTxns[ref.GetTxnId()] = true
			time.Sleep(100 * time.Millisecond)
			continue
		}
		orderIdentifiers[idx%apiBatchCount] = append(orderIdentifiers[idx%apiBatchCount], &orderPb.OrderIdentifier{
			Identifier: &orderPb.OrderIdentifier_OrderId{OrderId: orderIdRes.GetOrderId()},
		})
		time.Sleep(100 * time.Millisecond)
	}
	txnIdToOrderWithTxn, err := p.fetchOrderWithTxnsMap(ctx, orderIdentifiers)
	if err != nil {
		logger.Error(ctx, "error in fetching txn id to orderWithTxns map", zap.Error(err))
		return err
	}
	logger.Info(ctx, "ORDERS WITH TXNS FETCHED")

	actorIdToAccountMap, err := p.fetchAccountMap(ctx, erroredOutTxns, evaluatedRefunds)
	if err != nil {
		logger.Error(ctx, "error in fetching actor id to account map", zap.Error(err))
		return err
	}

	accountOperationalStatusActive := make(map[string]bool)
	logger.Info(ctx, fmt.Sprintf("FETCHING ACCOUNT OPERATIONAL STATUS FOR %d ACCOUNTS", len(actorIdToAccountMap)))
	for actorId, acc := range actorIdToAccountMap {
		isActive, err := p.isAccountActive(ctx, acc)
		if err != nil {
			logger.Error(ctx, "error in checking if the account is active",
				zap.Error(err),
				zap.String(logger.ACCOUNT_ID, acc.GetId()),
				zap.String(logger.ACTOR_ID_V2, actorId))
		}
		if !isActive {
			logger.Error(ctx, "account inoperational",
				zap.String(logger.ACCOUNT_ID, acc.GetId()),
				zap.String(logger.ACTOR_ID_V2, actorId))
		}
		accountOperationalStatusActive[acc.GetId()] = isActive
		time.Sleep(1 * time.Second)
	}

	logger.Info(ctx, "FETCHED ACCOUNT OPERATIONAL STATUSES")

	rejectedRecords := make([][]string, 0)
	rejectedRecords = append(rejectedRecords, []string{
		"Account Number",
		"Actual transaction amount (incl markup)",
		"Transaction Id",
		"Transaction Date",
		"Forex markup fee computed",
		"Forex Mark up Fee to be reversed",
		"Desired narration",
	})
	validRefundCsvRecords := make([][]string, 0)
	validRefunds := make([]*cardPb.DcForexTxnRefund, 0)
	inactiveAccountRefunds := make([]*cardPb.DcForexTxnRefund, 0)
	visitedTxns := make(map[string]bool)
	counter := 1
	txnIdToRefundParticulars := make(map[string]string)
	refundParticularsToUploadPath := make(map[string]string)

	for _, refund := range evaluatedRefunds {
		_, ok := visitedTxns[refund.GetTxnId()]
		if ok {
			continue
		}
		if erroredOutTxns[refund.GetTxnId()] {
			continue
		}
		visitedTxns[refund.GetTxnId()] = true
		outputRow := make([]string, 7)
		refundIssuedString, err := moneyPkg.ToString(refund.GetRefundAmount(), 2)
		if err != nil {
			logger.Error(ctx, "error in converting txn amount to string", zap.Error(err))
		}
		amountSign := ""
		if refund.GetTxnType() == cardEnumsPb.TransactionType_TRANSACTION_TYPE_CREDIT {
			continue
		}
		outputRow[5] = amountSign + refundIssuedString
		txnAmountString, err := moneyPkg.ToString(refund.GetTotalTxnAmount(), 2)
		if err != nil {
			logger.Error(ctx, "error in converting refund issued to string", zap.Error(err))
		} else {
			outputRow[1] = txnAmountString
		}
		orderWithTxn, ok := txnIdToOrderWithTxn[refund.GetTxnId()]
		if !ok {
			logger.Error(ctx, "no order with txns for the given txn id", zap.String(logger.TXN_ID, refund.GetTxnId()))
			continue
		}
		txnDetails := orderWithTxn.GetTransactions()[0]
		txnDateStr := datetime.DateToString(datetime.TimestampToDateInLoc(refund.GetTxnTime(), datetime.IST), recordLevelDateLayout, datetime.IST)
		forexMarkup, forexMarkupConvertErr := moneyPkg.ToString(refund.GetRefundAmount(), 2)
		if forexMarkupConvertErr != nil {
			logger.Error(ctx, "error in converting forex markup to string", zap.Error(forexMarkupConvertErr))
			continue
		}

		account := actorIdToAccountMap[refund.GetActorId()]

		partnerRefId := txnDetails.GetPartnerRefId()
		if len(partnerRefId) == 0 {
			partnerRefId = txnDetails.GetPartnerRefIdCredit()
		}
		if len(partnerRefId) == 0 {
			partnerRefId = txnDetails.GetPartnerRefIdDebit()
		}
		outputRow[0] = account.GetAccountNo()
		outputRow[2] = getBankTxnIdentifierFromPartnerRefId(partnerRefId)
		outputRow[3] = txnDateStr
		outputRow[4] = amountSign + forexMarkup
		outputRow[6] = fmt.Sprintf("ForexMarkupRefund/%s", orderWithTxn.GetOrder().GetExternalId())
		isAccActive := accountOperationalStatusActive[account.GetId()]
		if !isAccActive {
			inactiveAccountRefunds = append(inactiveAccountRefunds, refund)
			inactiveAccountTxns[txnDetails.GetId()] = true
			rejectedRecords = append(rejectedRecords, outputRow)
		} else if !moneyPkg.IsPositive(refund.GetRefundAmount()) {
			rejectedRecords = append(rejectedRecords, outputRow)
		} else {
			txnIdToRefundParticulars[txnDetails.GetId()] = fmt.Sprintf("ForexMarkupRefund/%s", orderWithTxn.GetOrder().GetExternalId())
			validRefunds = append(validRefunds, refund)

			// Get external txn ID from raw notification details with nil check
			var externalTxnId string
			txns := orderWithTxn.GetTransactions()
			if len(txns) > 0 && txns[0] != nil &&
				txns[0].GetRawNotificationDetails() != nil &&
				txns[0].GetRawNotificationDetails()["DEBIT"] != nil {

				debit := txns[0].GetRawNotificationDetails()["DEBIT"]
				externalTxnId = getExternalTxnIdFromTxnParticulars(debit.GetParticulars())
				if externalTxnId == "" {
					logger.Info(ctx, "external txn id not found in debit particulars")
				}
			} else {
				logger.Info(ctx, "couldn't extract external txn id: missing transaction data")
			}

			// Get original transaction with nil check
			validRefundCsvRecords = append(validRefundCsvRecords, []string{
				fmt.Sprintf("FREFUND%d", counter),
				refund.GetTxnTime().AsTime().Format(dateDDMMYYYYFormat),
				account.GetAccountNo()[0:4],
				account.GetAccountNo(),
				"C",
				amountSign + refundIssuedString,
				refund.GetTxnTime().AsTime().Format(dateDDMMYYYYFormat),
				fmt.Sprintf("ForexMarkupRefund/%s", orderWithTxn.GetOrder().GetExternalId()),
				"INR",
				"OCDEM",
				"",
				"BULKOPD",
				externalTxnId,                   // Will be empty string if not found
				refund.GetOriginalTransaction(), // Will be empty string if not found
			})
			counter++
		}
	}
	if err = p.performFileProcessing(ctx, validRefundCsvRecords, rejectedRecords, refundParticularsToUploadPath, rejectedFilePath, allRefunds, validRefunds, inactiveAccountRefunds); err != nil {
		logger.Error(ctx, "error in batching/zipping/uploading/mailing records", zap.Error(err))
	}

	for _, ref := range evaluatedRefunds {
		if erroredOutTxns[ref.GetTxnId()] == true {
			continue
		}
		updateMasks := make([]cardEnumsPb.DcForexTxnRefundFieldMask, 0)
		if inactiveAccountTxns[ref.GetTxnId()] {
			ref.RefundSubStatus = cardEnumsPb.RefundSubStatus_REFUND_SUB_STATUS_ACCOUNT_NON_OPERATIONAL
			updateMasks = append(updateMasks, cardEnumsPb.DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_SUB_STATUS)
		} else {
			ref.RefundStatus = cardEnumsPb.RefundStatus_REFUND_STATUS_PROCESSED
			// ref.ProcessIdentifier = refundParticularsToUploadPath[txnIdToRefundParticulars[ref.GetTxnId()]]
			// all the records from single run will go to same file
			ref.ProcessIdentifier = fmt.Sprintf("%s.csv", filepath.Join("dc-forex-refund", fmt.Sprintf("ForexRefund_%s", datetime.TimestampToString(timestamppb.Now(), "2006-01-02", datetime.IST))))
			updateMasks = append(updateMasks, cardEnumsPb.DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_STATUS,
				cardEnumsPb.DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_PROCESS_IDENTIFIER)
		}
		if updErr := dcForexRefundDao.Update(ctx, ref, updateMasks); updErr != nil {
			logger.Error(ctx, "error in updating refund record",
				zap.Error(updErr),
				zap.String(logger.TXN_ID, ref.GetTxnId()))
			continue
		}
	}
	logger.Info(ctx, "REFUND FILE SUCCESSFULLY GENERATED")
	return nil
}

// fetchRefundDataWithinTimeRange fetches all the refund records in a given time range . This will return the refunds
// in the evaluated state separately
func (p *ProcessCardForexTxnRefundJob) fetchRefundDataWithinTimeRange(ctx context.Context) (allRefunds, evaluatedRefunds []*cardPb.DcForexTxnRefund, err error) {
	// adding a 48 hour buffer as Federal bank reversals happen within that time frame & a refund can be rejected based on that
	txnTimeUpperLimit := time.Now().Add(-48 * time.Hour)
	refundsMdl := make([]*model.DcForexTxnRefund, 0)
	db := gormctxv2.FromContextOrDefault(ctx, p.db)
	res := db.Where("refund_status = ? AND txn_type = ? AND txn_time < ?", cardEnumsPb.RefundStatus_REFUND_STATUS_EVALUATED, cardEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT, txnTimeUpperLimit).Find(&refundsMdl)
	if res.Error != nil {
		logger.Error(ctx, "error in fetching db data", zap.Error(res.Error))
		return nil, nil, res.Error
	}
	if len(refundsMdl) == 0 {
		logger.Error(ctx, "NO REFUND RECORDS IN THE GIVEN TIME INTERVAL")
		return nil, nil, epifierrors.ErrRecordNotFound
	}
	for _, mdl := range refundsMdl {
		pb := mdl.GetProto()
		allRefunds = append(allRefunds, pb)
		if pb.GetRefundStatus() == cardEnumsPb.RefundStatus_REFUND_STATUS_EVALUATED {
			evaluatedRefunds = append(evaluatedRefunds, pb)
		}
	}
	return
}

// fetchAccountMap returns a map of actor id to savings account map
func (p *ProcessCardForexTxnRefundJob) fetchAccountMap(ctx context.Context, erroredOutTxns map[string]bool, refunds []*cardPb.DcForexTxnRefund) (map[string]*savingsClientPb.Account, error) {
	actorIds := make([][]string, apiBatchLimitCount)
	actorList := make([]*actorPb.GetEntityDetailsResponse_EntityDetail, 0)
	for idx, refund := range refunds {
		if erroredOutTxns[refund.GetTxnId()] {
			continue
		}
		actorIds[idx%apiBatchLimitCount] = append(actorIds[idx%apiBatchLimitCount], refund.GetActorId())
	}
	for i := 0; i < apiBatchLimitCount; i++ {
		if len(actorIds[i]) == 0 {
			continue
		}
		entityDetails, actorErr := p.helper.ActorClient.GetEntityDetails(ctx, &actorPb.GetEntityDetailsRequest{ActorIds: actorIds[i]})
		if te := epifigrpc.RPCError(entityDetails, actorErr); te != nil {
			logger.Error(ctx, "error in fetching actors list ", zap.Error(te))
			return nil, te
		}
		actorList = append(actorList, entityDetails.GetEntityDetails()...)
	}
	logger.Info(ctx, "USER ENTITY DETAILS FETCHED SUCCESSFULLY")
	userIds := make([][]string, apiBatchLimitCount)
	userIdToActorId := make(map[string]string)
	for idx, entityDetail := range actorList {
		userIds[idx%apiBatchLimitCount] = append(userIds[idx%apiBatchLimitCount], entityDetail.GetEntityId())
		userIdToActorId[entityDetail.GetEntityId()] = entityDetail.GetActorId()
	}
	accountsList := make([]*savingsClientPb.Account, 0)
	for i := 0; i < apiBatchLimitCount; i++ {
		if len(userIds[i]) == 0 {
			continue
		}
		accountListResp, accountErr := p.helper.SavingsClient.GetAccountsList(ctx, &savingsClientPb.GetAccountsListRequest{
			Identifier: &savingsClientPb.GetAccountsListRequest_UserIds{UserIds: &savingsClientPb.PrimaryUserIdentifier{PrimaryUserIds: userIds[i]}}})
		if te := epifigrpc.RPCError(accountListResp, accountErr); te != nil {
			logger.Error(ctx, "error in fetching account list ", zap.Error(te))
			return nil, te
		}
		accountsList = append(accountsList, accountListResp.GetAccounts()...)
	}
	logger.Info(ctx, "ACCOUNT DETAILS FETCHED SUCCESSFULLY")
	actorIdToAccountMap := make(map[string]*savingsClientPb.Account)
	for _, acc := range accountsList {
		actorIdToAccountMap[userIdToActorId[acc.GetPrimaryAccountHolder()]] = acc
	}
	return actorIdToAccountMap, nil
}

// fetchOrderWithTxnsMap returns a map of txn id to the order with txns object for all the order identifiers
func (p *ProcessCardForexTxnRefundJob) fetchOrderWithTxnsMap(ctx context.Context, orderIdentifiers [][]*orderPb.OrderIdentifier) (map[string]*orderPb.OrderWithTransactions, error) {
	orderWithTxns := make([]*orderPb.OrderWithTransactions, 0)
	for i := 0; i < apiBatchCount; i++ {
		logger.Info(ctx, fmt.Sprintf("FETCHING ORDERS WITH TXNS FOR BATCH %d", i), zap.Int("batchSize", len(orderIdentifiers[i])))
		orderWithTxnsResp, err := p.helper.OrderClient.GetOrdersWithTransactions(ctx, &orderPb.GetOrdersWithTransactionsRequest{
			OrderIdentifiers: orderIdentifiers[i],
		})
		if te := epifigrpc.RPCError(orderWithTxnsResp, err); te != nil {
			if orderWithTxnsResp.GetStatus().IsRecordNotFound() {
				logger.Error(ctx, "no order with txns for the given order identifiers", zap.Error(te))
				continue
			}
			logger.Error(ctx, "error in fetching orders with txns", zap.Error(te))
			return nil, te
		}
		fmt.Printf("orderWithTxnsResp: %v\n", orderWithTxnsResp)
		orderWithTxns = append(orderWithTxns, orderWithTxnsResp.GetOrderWithTransactions()...)
		time.Sleep(100 * time.Millisecond)
	}

	fmt.Printf("orderWithTxns: %v\n", orderWithTxns)
	txnIdToOrderWithTxn := make(map[string]*orderPb.OrderWithTransactions)
	for _, orderWithTxn := range orderWithTxns {
		if len(orderWithTxn.GetTransactions()) == 0 {
			logger.Error(ctx, "no  order with txns for given order id", zap.String(logger.ORDER_ID, orderWithTxn.GetOrder().GetId()))
			continue
		}
		txnIdToOrderWithTxn[orderWithTxn.GetTransactions()[0].GetId()] = orderWithTxn
	}
	return txnIdToOrderWithTxn, nil
}

// performFileProcessing splits the records into batches of at most 1000 and uploads the folder to s3. Also, it creates a
// zip file of the same data and mails it to a pre-defined email id
func (p *ProcessCardForexTxnRefundJob) performFileProcessing(ctx context.Context, recordsToSend, rejectedRecords [][]string,
	refundParticularsToUploadPath map[string]string, rejectedFilePath string, allRefunds, validRefunds, frozenAccountRefunds []*cardPb.DcForexTxnRefund) error {
	recordFolderName := fmt.Sprintf("ForexRefunds_%s", datetime.TimestampToString(timestamppb.Now(), "2006-01-02", datetime.IST))
	// FilePath -> {s3Bucket}/dc-forex-refund/ForexRefund_{date}.csv
	recordsToSendConsolidatedFilePath := fmt.Sprintf("%s.csv", filepath.Join("dc-forex-refund", fmt.Sprintf("ForexRefund_%s", datetime.TimestampToString(timestamppb.Now(), "2006-01-02", datetime.IST))))
	recordsToSendUrl, err := uploadToDcDocsS3WithPresignedUrl(ctx, p.helper.DebitCardDataS3Client, recordsToSend, recordsToSendConsolidatedFilePath)
	if err != nil {
		logger.Error(ctx, "error in uploading refund record docs to s3", zap.Error(err), zap.String(logger.AWS_FILE_PATH, recordsToSendConsolidatedFilePath))
		return err
	}
	logger.Info(ctx, fmt.Sprintf("recordsToSend files uploaded on url [%s]", recordsToSendUrl), zap.String(logger.AWS_FILE_PATH, recordsToSendConsolidatedFilePath))
	/*

		filesWithNames, err := p.splitAndUploadToDcDocsS3(ctx, recordsToSend, recordFolderName, recordFolderName, refundParticularsToUploadPath)
		if err != nil {
			logger.Error(ctx, "error in uploading  new format doc to s3", zap.Error(err))
			return err
		logger.Info(ctx, fmt.Sprintf("fed file record uploaded successfully on path : %s", recordFolderName))

	*/

	rejectedRecordsUrl, err := uploadToDcDocsS3WithPresignedUrl(ctx, p.helper.DcDocsS3Client, rejectedRecords, rejectedFilePath)
	if err != nil {
		logger.Error(ctx, "error in uploading rejected docs to s3", zap.Error(err), zap.String(logger.AWS_FILE_PATH, rejectedFilePath))
		return err
	}
	logger.Info(ctx, fmt.Sprintf("rejected files uploaded on url [%s]", rejectedRecordsUrl), zap.String(logger.AWS_FILE_PATH, rejectedFilePath))

	zipFile, err := fetchZippedFile([]*FileWithFileName{
		{
			FileName:   recordFolderName + ".csv",
			FileMatrix: recordsToSend,
		},
	}, recordFolderName+".zip")
	if err != nil {
		logger.Error(ctx, "error in fetching zipped file", zap.Error(err))
		return err
	}

	/*
		zipFileContents, err := ioutil.ReadFile(zipFile.Name())
		if err != nil {
			logger.Error(ctx, "error in reading zipped file contents from file",
				zap.Error(err),
				zap.String(logger.FILE_NAME, zipFile.Name()))
			return err
		}

			zippedFileUrl, wrErr := p.helper.DcDocsS3Client.WriteAndGetPreSignedUrl(filepath.Join("forex-refund", "splitted_refunds", "zip_file", recordFolderName, zipFile.Name()), zipFileContents, 36000)
			if wrErr != nil {
				logger.Error(ctx, "error in writing csv file to s3", zap.Error(wrErr))
				return wrErr
			}
			logger.Info(ctx, fmt.Sprintf("zipped file written in url [%s]", zippedFileUrl))
	*/

	if err := p.emailZipFile(ctx, zipFile, validRefunds, frozenAccountRefunds); err != nil {
		logger.Error(ctx, "error in sending email with file attachment", zap.Error(err))
		return err
	}
	return nil
}

func (p *ProcessCardForexTxnRefundJob) isAccountActive(ctx context.Context, account *savingsClientPb.Account) (bool, error) {
	operationalStatusRes, err := p.helper.OperationalServiceClient.GetOperationalStatus(ctx, &operationalStatusPb.GetOperationalStatusRequest{
		DataFreshness:     operationalStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
		AccountIdentifier: &operationalStatusPb.GetOperationalStatusRequest_SavingsAccountId{SavingsAccountId: account.GetId()},
	})
	if te := epifigrpc.RPCError(operationalStatusRes, err); te != nil {
		logger.Error(ctx, "error in fetching account operational status", zap.Error(te), zap.String(logger.ACCOUNT_ID, account.GetId()))

		isRisky, err := p.helper.IsRiskyUser(ctx, account.GetActorId())
		if err != nil {
			logger.Error(ctx, "error performing risk check on actor: ", zap.String(logger.ACTOR_ID_V2, account.GetActorId()), zap.Error(err))
			return true, nil
		}
		return !isRisky, nil
	}

	// NOTE: when GetOperationalStatusResponse_ACC_NUMBER_PHONE_MISMATCH is returned, we do not have any other field in the response
	// conscious call has been taken to take these accounts be to be active, ref -> https://epifi.slack.com/archives/C0101A42ZFW/p1706537311426789
	if operationalStatusRes.GetStatus().GetCode() == uint32(operationalStatusPb.GetOperationalStatusResponse_ACC_NUMBER_PHONE_MISMATCH) {
		logger.Error(ctx, "phone number mismatch", zap.String(logger.ACTOR_ID_V2, account.GetActorId()))
		return true, nil
	}
	if lo.Contains[accountEnumsPb.OperationalStatus](disallowedOperStatuses, operationalStatusRes.GetOperationalStatusInfo().GetOperationalStatus()) {
		return false, nil
	}
	if lo.Contains[accountEnumsPb.FreezeStatus](disallowedFreezeStatuses, operationalStatusRes.GetOperationalStatusInfo().GetFreezeStatus()) {
		return false, nil
	}
	return true, nil
}

// emailZipFile constructs mail content along with zip file attachment and sends it to a predefined email address
func (p *ProcessCardForexTxnRefundJob) emailZipFile(ctx context.Context, zipFile *os.File, validRefunds, frozenAccountRefunds []*cardPb.DcForexTxnRefund) error {
	fileContents, err := os.ReadFile(zipFile.Name())
	if err != nil {
		return errors.Wrap(err, "error in reading file contents from zip")
	}

	var (
		sendMsgErrors string
	)

	sendMessageRequest := p.getSendEmailRequest(ctx, zipFile, validRefunds, frozenAccountRefunds, fileContents)

	ccAddresses := p.getAllCcAddresses()
	sendMessageRequest.GetEmail().GetDestination().CcAddresses = ccAddresses

	for _, userEmail := range targetEmail {
		sendMessageRequest.UserIdentifier = &commsPb.SendMessageRequest_EmailId{
			EmailId: userEmail,
		}
		sendMsgResp, err := p.helper.CommsClient.SendMessage(ctx, sendMessageRequest)
		if te := epifigrpc.RPCError(sendMsgResp, err); te != nil {
			sendMsgErrors += fmt.Sprintf("%s : %s", userEmail, te.Error())
		}
	}

	return errors.New(sendMsgErrors)
}

func (p *ProcessCardForexTxnRefundJob) getAllCcAddresses() []*commsPb.EmailMessage_Destination_EmailAddress {
	var ccAddress []*commsPb.EmailMessage_Destination_EmailAddress
	for _, email := range ccEmails {
		ccAddress = append(ccAddress, &commsPb.EmailMessage_Destination_EmailAddress{EmailId: email})
	}
	return ccAddress
}

func (p *ProcessCardForexTxnRefundJob) getSendEmailRequest(ctx context.Context, zipFile *os.File, validRefunds, frozenAccountRefunds []*cardPb.DcForexTxnRefund, fileContents []byte) *commsPb.SendMessageRequest {

	externalRefId := fmt.Sprintf("ForexRefund_%s", datetime.TimestampToString(timestamppb.Now(), "02/01/2006", datetime.IST))
	tableRowDetails := getTableRowDetailsFromLayout("               ", "Number of transactions", "Number of unique users", "Total amount")
	tableRowDetails += getTableRowDetailsFromLayout("Forex reversals", fmt.Sprintf("%d", len(validRefunds)), fmt.Sprintf("%d", getUniqueActorsFromRefundData(validRefunds)), fmt.Sprintf("₹%.2f", getTotalAmountToBeReversed(validRefunds)))
	// tableRowDetails += getTableRowDetailsFromLayout("Frozen cases", fmt.Sprintf("%d", len(frozenAccountRefunds)), fmt.Sprintf("%d", getUniqueActorsFromRefundData(frozenAccountRefunds)), fmt.Sprintf("₹%.2f", getTotalAmountToBeReversed(frozenAccountRefunds)))

	tNow := timestamppb.Now()

	return &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_BEST_EFFORT,
		Medium: commsPb.Medium_EMAIL,
		UserIdentifier: &commsPb.SendMessageRequest_EmailId{
			EmailId: "",
		},
		Message: &commsPb.SendMessageRequest_Email{
			Email: &commsPb.EmailMessage{
				FromEmailId:   "<EMAIL>",
				FromEmailName: "Fi",
				Attachment: []*commsPb.EmailMessage_Attachment{
					{
						FileContent:    fileContents,
						FileName:       zipFile.Name(),
						Disposition:    commsPb.Disposition_ATTACHMENT,
						AttachmentType: "ZIP",
					},
				},
				Destination: &commsPb.EmailMessage_Destination{},
				EmailOption: &commsPb.EmailOption{
					Option: &commsPb.EmailOption_DebitCardForexRefundEmailOption{
						DebitCardForexRefundEmailOption: &commsPb.DebitCardForexRefundEmailOption{
							EmailType: commsPb.EmailType_DEBIT_CARD_FOREX_REFUND_EMAIL,
							Option: &commsPb.DebitCardForexRefundEmailOption_DebitCardForexRefundEmailOptionV1{
								DebitCardForexRefundEmailOptionV1: &commsPb.DebitCardForexRefundEmailOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									Heading:         fmt.Sprintf("PFA the forex refunds for transactions till %s", datetime.TimestampToString(tNow, "02 Jan 2006", datetime.IST)),
									Description:     "Below is the breakdown of the forex markup",
									IconUrl:         "https://epifi-icons.pointz.in/credit_card_images/cc_comms_green_tick",
									BackgroundColor: "#ECEEF0",
									TableRowDetails: tableRowDetails,
								},
							},
						},
					},
				},
			},
		},
		ExternalReferenceId: externalRefId,
	}
}

func getTableRowDetailsFromLayout(column1, column2, column3, column4 string) string {
	tableRowDetails := strings.Replace(refundEmailDataLayout, "{#column1_val}", column1, 1)
	tableRowDetails = strings.Replace(tableRowDetails, "{#column2_val}", column2, 1)
	tableRowDetails = strings.Replace(tableRowDetails, "{#column3_val}", column3, 1)
	tableRowDetails = strings.Replace(tableRowDetails, "{#column4_val}", column4, 1)
	return tableRowDetails
}

func getTotalAmountToBeReversed(refunds []*cardPb.DcForexTxnRefund) float64 {
	res := float64(0)
	for _, refund := range refunds {
		refundAmtFloat, _ := moneyPkg.ToDecimal(refund.GetRefundAmount()).Float64()
		res += refundAmtFloat
	}
	return res
}

func getUniqueActorsFromRefundData(refunds []*cardPb.DcForexTxnRefund) int {
	visitedActorsMap := make(map[string]bool)
	for _, ref := range refunds {
		if visitedActorsMap[ref.GetActorId()] {
			continue
		}
		visitedActorsMap[ref.GetActorId()] = true
	}
	return len(visitedActorsMap)
}

func (p *ProcessCardForexTxnRefundJob) splitAndUploadToDcDocsS3(ctx context.Context, records [][]string, folderName, filePrefix string, refundParticularToUploadPath map[string]string) ([]*FileWithFileName, error) {
	fileCount := 1 + len(records)/maxFileSizeForFed
	recordsFolder := make([][][]string, fileCount)
	fileNumberToRowCounter := make(map[int]int32)
	for idx, record := range records {
		fileNumber := idx % fileCount
		fileNumberToRowCounter[fileNumber] = 1 + fileNumberToRowCounter[fileNumber]
		recordRowNumber := fileNumberToRowCounter[fileNumber]
		record[0] = fmt.Sprintf("%d", recordRowNumber)
		recordsFolder[fileNumber] = append(recordsFolder[fileNumber], record)
		fileName := fmt.Sprintf("%s_%d.csv", filePrefix, fileNumber)
		filePath := filepath.Join("forex-refund", "splitted_refunds", folderName, fileName)
		if len(record) != 0 {
			refundParticularToUploadPath[record[len(record)-1]] = filePath
		}
	}
	filesWithNames := make([]*FileWithFileName, 0)
	for i := 0; i < fileCount; i++ {
		if len(recordsFolder[i]) == 0 {
			continue
		}
		fileName := fmt.Sprintf("%s_%d.csv", filePrefix, i)
		filesWithNames = append(filesWithNames, &FileWithFileName{
			FileName:   fileName,
			FileMatrix: recordsFolder[i],
		})
		filePath := filepath.Join("forex-refund", "splitted_refunds", folderName, fileName)
		if _, err := uploadToDcDocsS3WithPresignedUrl(ctx, p.helper.DcDocsS3Client, recordsFolder[i], filePath); err != nil {
			logger.Error(ctx, "error in uploading file to path", zap.Error(err), zap.String(logger.AWS_FILE_PATH, filePath))
			continue
		}
	}

	return filesWithNames, nil
}

func fetchZippedFile(csvFileSet []*FileWithFileName, zipFileName string) (*os.File, error) {
	zipFile, err := os.Create(zipFileName)
	if err != nil {
		return nil, errors.Wrap(err, "error in creating zip file")
	}
	zipWriter := zip.NewWriter(zipFile)
	for _, file := range csvFileSet {
		fileContent := file.FileMatrix
		fileName := file.FileName
		zipFileWriter, zipFileWrErr := zipWriter.Create(fileName)
		if zipFileWrErr != nil {
			return nil, zipFileWrErr
		}

		// Write CSV data to the new file in the zip archive
		csvWriter := csv.NewWriter(zipFileWriter)
		err = csvWriter.WriteAll(fileContent)
		if err != nil {
			return nil, err
		}
	}
	closeErr := zipWriter.Close()
	if closeErr != nil {
		return nil, closeErr
	}
	return zipFile, nil
}

func uploadToDcDocsS3WithPresignedUrl(ctx context.Context, s3Client s3.S3Client, records [][]string, filePath string) (string, error) {
	buf := new(bytes.Buffer)
	csvWriter := csv.NewWriter(buf)
	if writeErr := csvWriter.WriteAll(records); writeErr != nil {
		logger.Error(ctx, "error in writing records to csv", zap.Error(writeErr))
		return "", errors.Wrap(writeErr, "error in writing records to csv")
	}
	url, err := s3Client.WriteAndGetPreSignedUrl(ctx, filePath, buf.Bytes(), 36000)
	if err != nil {
		logger.Error(ctx, "error in writing csv file to s3", zap.Error(err))
		return "", errors.Wrap(err, "error in writing csv file to s3")
	}
	return url, nil
}

func getBankTxnIdentifierFromPartnerRefId(partnerRefId string) string {
	partnerRefIdArr := strings.Split(partnerRefId, colonDelimiter)
	if len(partnerRefIdArr) < 3 {
		return ""
	}
	return partnerRefIdArr[2]
}

func getExternalTxnIdFromTxnParticulars(txnParticulars string) string {
	txnPartArr := strings.Split(txnParticulars, txnAdditionalDetailsDelimiter)
	if len(txnPartArr) <= 1 {
		return ""
	}
	txnParticularContainingExternalTxnId := strings.Trim(txnPartArr[1], " ")
	alphaNumericStrings := alphanumericForexExtTxnIdIdentifierRegex.FindAllString(txnParticularContainingExternalTxnId, -1)
	for _, probableExternalTxnIdString := range alphaNumericStrings {
		if len(probableExternalTxnIdString) == externalTxnIdStringLength {
			return probableExternalTxnIdString
		}
	}
	logger.Info(context.Background(), "no external txn id found in the txn particulars")
	return ""
}
