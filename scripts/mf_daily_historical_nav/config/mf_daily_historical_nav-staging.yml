Application:
  Environment: "staging"
  Name: "mf_historical_nav"

AWS:
  Region: "ap-south-1"

HistoricalNavEventPublisher:
  QueueName: "staging-mf-historical-nav-request-queue"

EpifiDb:
  AppName: "investment"
  StatementTimeout: 1s
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Tracing:
  Enable: true
