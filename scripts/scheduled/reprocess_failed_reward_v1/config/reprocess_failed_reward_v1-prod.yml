Application:
  Environment: "prod"
  Name: "reprocess_failed_reward_v1"

RewardsDb:
  DBType: PGDB
  AppName: "rewards"
  StatementTimeout: 30s
  Name: "rewards"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 1
  MaxIdleConn: 1
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 10s
    UseInsecureLog: false

RewardProcessorSqsPublisher:
  QueueName: "prod-rewards-data-queue"

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbCredentials: "prod/rds/epifimetis/rewards"
