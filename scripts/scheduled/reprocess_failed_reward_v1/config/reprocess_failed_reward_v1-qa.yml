Application:
  Environment: "qa"
  Name: "reprocess_failed_reward_v1"

RewardsDb:
  DBType: PGDB
  AppName: "rewards"
  StatementTimeout: 30s
  Name: "rewards"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 1
  MaxIdleConn: 1
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

RewardProcessorSqsPublisher:
  QueueName: "qa-rewards-data-queue"

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbCredentials: "qa/rds/postgres/rewards"
