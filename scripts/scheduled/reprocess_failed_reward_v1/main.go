package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"go.uber.org/ratelimit"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/session"
	"github.com/epifi/be-common/pkg/aws/sqs"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	ffPb "github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/enums"
	kycPb "github.com/epifi/gamma/api/kyc"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	savingsPb "github.com/epifi/gamma/api/savings"
	typesAccountPb "github.com/epifi/gamma/api/typesv2/account"

	"github.com/epifi/gamma/rewards/generator/dao/model"
	"github.com/epifi/gamma/scripts/scheduled/reprocess_failed_reward_v1/config"
)

var (
	today = datetime.StartOfDay(time.Now())
)
var (
	rewardIds                          = flag.String("rewardIds", "", "Comma seperated reward ids. from and to date flags are  ignored")
	rewardIdsFilePath                  = flag.String("rewardIdsFilePath", "", "local file path with newline separated reward ids. from and to date flags are ignored")
	fromDateStr                        = flag.String("fromDate", today.Add(-90*24*time.Hour).Format(time.RFC3339), "date from which rewards should be reprocessed, format : RFC3339")
	toDateStr                          = flag.String("toDate", today.Add(-3*24*time.Hour).Format(time.RFC3339), "date till which rewards should be reprocessed, format : RFC3339")
	rewardOfferIds                     = flag.String("rewardOfferIds", "", "optional comma seperated reward offer ids for which rewards should be reprocessed (Pass rewardOfferIds, fromDate and toDate after checking the number of rewards and their type)")
	maxPages                           = flag.Int("maxPages", 1000, "max number of pages to fetch")
	pageSize                           = flag.Int("pageSize", 100, "number of rewards to fetch per page")
	rewardsProcessingRate              = flag.Int("rewardsProcessingRateLimit", 100, "number of rewards to process per second")
	rewardsProcessingRateLimitDuration = flag.Duration("rewardsProcessingRateLimitDuration", 1*time.Second, "duration for which rewards processing rate limit is applied")
	rewardsFilteringRate               = flag.Int("rewardsFilteringRateLimit", 100, "number of rewards to filter per second")
	rewardsFilteringRateLimitDuration  = flag.Duration("rewardsFilteringRateLimitDuration", 1*time.Second, "duration for which rewards filtering rate limit is applied")
	timeout                            = flag.Duration("timeout", 15*time.Minute, "timeout for the script")
	rewardsProcessingRateLimit         ratelimit.Limiter
	rewardsFilteringRateLimit          ratelimit.Limiter
	retriedRewardsCount                = 0
)

var allowedRewardStatusesForProcessingRetry = []rewardsPb.RewardStatus{rewardsPb.RewardStatus_PROCESSING_FAILED, rewardsPb.RewardStatus_PROCESSING_MANUAL_INTERVENTION, rewardsPb.RewardStatus_PROCESSING_PENDING}

// nolint: funlen
func main() {
	flag.Parse()

	if *pageSize == 0 {
		logger.Panic("pageSize cannot be 0")
	}
	if *maxPages == 0 {
		logger.Panic("maxPages cannot be 0")
	}

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
	}

	// connect to rewards db
	db, err := storagev2.NewGormDB(conf.RewardsDb)
	if err != nil {
		logger.Panic("failed to establish DB conn", zap.Error(err))
	}
	sqlDB, err := db.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	defer func() { _ = sqlDB.Close() }()

	rewardsProcessingRateLimit = ratelimit.New(*rewardsProcessingRate, ratelimit.Per(*rewardsProcessingRateLimitDuration))
	rewardsFilteringRateLimit = ratelimit.New(*rewardsFilteringRate, ratelimit.Per(*rewardsFilteringRateLimitDuration))

	// init actor service client
	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actorPb.NewActorClient(actorConn)

	// init bank customer service client
	bcConn := epifigrpc.NewConnByService(cfg.BANK_CUSTOMER_SERVICE)
	defer epifigrpc.CloseConn(bcConn)

	bankCustomerClient := bankcust.NewBankCustomerServiceClient(bcConn)

	// init savings service client
	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	defer epifigrpc.CloseConn(savingsConn)
	savingsClient := savingsPb.NewSavingsClient(savingsConn)

	fireflyConn := epifigrpc.NewConnByService(cfg.FIREFLY_SERVICE)
	defer epifigrpc.CloseConn(fireflyConn)
	fireflyClient := ffPb.NewFireflyClient(fireflyConn)

	// init rewards processing queue publisher
	rewardsProcessorPublisher := newPublisher(conf, conf.RewardProcessorSqsPublisher)
	ctx, cancel := context.WithTimeout(context.Background(), *timeout)
	defer cancel()

	switch {
	case *rewardIds != "":
		ids := splitCommaSepStringIntoList(*rewardIds)
		processRewardsByRewardIds(ctx, ids, db, actorClient, bankCustomerClient, savingsClient, fireflyClient, rewardsProcessorPublisher)
	case *rewardIdsFilePath != "":
		content, err := os.ReadFile(*rewardIdsFilePath)
		if err != nil {
			logger.Panic("error reading reward ids from file", zap.Error(err))
		}
		ids := strings.Split(strings.Trim(string(content), "\n"), "\n")

		processRewardsByRewardIds(ctx, ids, db, actorClient, bankCustomerClient, savingsClient, fireflyClient, rewardsProcessorPublisher)
	default:
		processRewardsByTime(ctx, db, actorClient, bankCustomerClient, savingsClient, fireflyClient, rewardsProcessorPublisher)
	}

	logger.Info(ctx, "script completed successfully", zap.Int("retriedRewardsCount", retriedRewardsCount))
}

func processRewardsByRewardIds(ctx context.Context, ids []string, db *gorm.DB, actorClient actorPb.ActorClient, bankCustomerClient bankcust.BankCustomerServiceClient, savingsClient savingsPb.SavingsClient, fireflyClient ffPb.FireflyClient, rewardsProcessorPublisher queue.Publisher) {

	var offset int
	var rewards []*model.Reward
	var err error
	for i := 0; i < *maxPages && offset < len(ids); i++ {

		rewards, offset, err = getPaginatedRewardsByIds(ctx, db, ids, offset)
		if err != nil {
			logger.Panic("error fetching rewards by ids", zap.Error(err))
		}
		if len(rewards) == 0 {
			logger.Info(ctx, "no rewards to process in the page. moving to next page if available", zap.Int("page", i+1))
			continue
		}
		processRewards(ctx, rewards, actorClient, bankCustomerClient, savingsClient, fireflyClient, db, rewardsProcessorPublisher)
	}
}

func getPaginatedRewardsByIds(ctx context.Context, db *gorm.DB, rewardIds []string, offset int) ([]*model.Reward, int, error) {
	end := offset + *pageSize
	if offset >= len(rewardIds) {
		return nil, offset, nil
	}

	end = min(end, len(rewardIds))

	ids := rewardIds[offset:end]
	db = gormctxv2.FromContextOrDefault(ctx, db)
	query := db.Model(&model.Reward{}).Where("id in (?) AND status in (?)", ids, allowedRewardStatusesForProcessingRetry).Order("created_at asc")

	var rewards []*model.Reward
	if err := query.Find(&rewards).Error; err != nil {
		return nil, 0, fmt.Errorf("error fetching rewards by ids, err : %w", err)
	}
	return rewards, end, nil
}

func processRewardsByTime(ctx context.Context, db *gorm.DB, actorClient actorPb.ActorClient, bankCustomerClient bankcust.BankCustomerServiceClient, savingsClient savingsPb.SavingsClient, fireflyClient ffPb.FireflyClient, rewardsProcessorPublisher queue.Publisher) {
	fromTime, err := time.Parse(time.RFC3339, *fromDateStr)
	if err != nil {
		logger.Panic("error in parsing fromDate from string to timestamp", zap.Error(err))
	}

	tillTime, err := time.Parse(time.RFC3339, *toDateStr)
	if err != nil {
		logger.Panic("error in parsing tillDate from string to timestamp", zap.Error(err))
	}

	var pageToken *pagination.PageToken
	var lastCreatedAt time.Time
	for i := 0; i < *maxPages; i++ {
		// get processing failed rewards in a paginated fashion
		rewards, pageResp, err := getProcessingFailedRewardsPaginated(ctx, db, fromTime, tillTime, pageToken)
		if err != nil {
			logger.Panic("error fetching processing failed rewards", zap.Error(err), zap.Time("lastCreatedAt", lastCreatedAt))
		}
		if len(rewards) == 0 || !pageResp.GetHasAfter() {
			logger.Info(ctx, "no more failed processing rewards left")
			break
		}
		pageToken = &pagination.PageToken{}
		err = pageToken.Unmarshal(pageResp.AfterToken)
		if err != nil {
			logger.Panic("error unmarshaling page token", zap.Error(err))
		}
		lastCreatedAt = rewards[len(rewards)-1].CreatedAt
		processRewards(ctx, rewards, actorClient, bankCustomerClient, savingsClient, fireflyClient, db, rewardsProcessorPublisher)

	}

}

func processRewards(ctx context.Context, rewards []*model.Reward, actorClient actorPb.ActorClient, bankCustomerClient bankcust.BankCustomerServiceClient, savingsClient savingsPb.SavingsClient, fireflyClient ffPb.FireflyClient, db *gorm.DB, rewardsProcessorPublisher queue.Publisher) {
	// filter in rewards whose processing can be retried.
	retryableRewards, err := filterInRetryableRewards(ctx, rewards, actorClient, bankCustomerClient, savingsClient, fireflyClient)
	if err != nil {
		logger.Panic("error filtering for retryable rewards", zap.Error(err))
	}

	// retry processing for retryableRewards rewards
	for _, reward := range retryableRewards {
		rewardsProcessingRateLimit.Take()
		ctx := epificontext.CtxWithTraceId(ctx, uuid.New().String())
		if err := reprocessReward(ctx, db, reward); err != nil {
			logger.Error(ctx, "error reprocessing reward", zap.String(logger.REWARD_ID, *reward.Id), zap.Error(err))
			return
		}
		// fetch updated reward from db
		rewardProto, err := getRewardById(db, *reward.Id)
		if err != nil {
			logger.Error(ctx, "error fetching reward by id", zap.String(logger.REWARD_ID, *reward.Id), zap.Error(err))
			return
		}
		// push reward to processing queue
		if _, err := rewardsProcessorPublisher.Publish(context.Background(), &rewardsPb.RewardEvent{Reward: rewardProto}); err != nil {
			logger.Error(ctx, "error push reward to processing queue", zap.String(logger.REWARD_ID, *reward.Id), zap.Error(err))
			return
		}
		retriedRewardsCount++
		logger.Info(ctx, "processing retry completed successfully", zap.String(logger.REWARD_ID, *reward.Id))
	}
}

type Rewards []*model.Reward

func (rs Rewards) Slice(start, end int) pagination.Rows { return rs[start:end] }
func (rs Rewards) GetTimestamp(index int) time.Time     { return rs[index].CreatedAt }
func (rs Rewards) Size() int                            { return len(rs) }

func getProcessingFailedRewardsPaginated(ctx context.Context, dbConn *gorm.DB, fromTime time.Time, uptoTime time.Time, pageToken *pagination.PageToken) ([]*model.Reward, *rpc.PageContextResponse, error) {

	db := gormctxv2.FromContextOrDefault(ctx, dbConn)

	// fetch pageSize + 1 extra row to compute next page availability.
	query := db.Model(&model.Reward{}).Where("status in (?) AND created_at >= ? AND created_at <= ?", allowedRewardStatusesForProcessingRetry, fromTime, uptoTime).Order("created_at asc")
	if *rewardOfferIds != "" {
		query = query.Where("offer_id in (?)", strings.Split(*rewardOfferIds, ","))
	}

	// fetch pageSize + 1 extra row to compute next page availability.
	query = query.Limit(*pageSize + 1)

	if pageToken != nil {
		query = query.Where("created_at >= ?", pageToken.Timestamp.AsTime())
		query = query.Offset(int(pageToken.Offset))
	}

	var rewardsList []*model.Reward
	if err := query.Find(&rewardsList).Error; err != nil {
		return nil, nil, fmt.Errorf("error fetching failed processing rewards from db, err : %w", err)
	}

	rows, resp, err := pagination.NewPageCtxResp(pageToken, int(*pageSize),
		Rewards(rewardsList))
	if err != nil {
		logger.Error(ctx, "Failed to create new page context", zap.Error(err))
		return nil, nil, err
	}
	rewardsList = rows.(Rewards)

	return rewardsList, resp, nil
}

// filterInRetryableRewards filters and returns the rewards whose processing could be retried.
//  1. For CREDIT_CARD_BILL_ERASER reward type we allow processing only if user has active cc
//  2. For CASH and US_STOCK reward types we allow processing if there is no credit freeze on the user's account
//     and any one of the following conditions pass:
//     a. user receiving the reward should be FULL_KYC
//     b. reward option is set to disable implicit locking
//  3. For other reward types we allow processing if all the following conditions pass:
//     a. user receiving the reward should be FULL_KYC
//     b. reward option is set to disable implicit locking
func filterInRetryableRewards(ctx context.Context, rewards []*model.Reward, actorClient actorPb.ActorClient, bankCustomerClient bankcust.BankCustomerServiceClient, savingsClient savingsPb.SavingsClient, fireflyClient ffPb.FireflyClient) ([]*model.Reward, error) {
	var retryableRewards []*model.Reward
	if len(rewards) == 0 {
		return retryableRewards, nil
	}

	var actorIds []string
	for _, reward := range rewards {
		actorIds = append(actorIds, reward.ActorId)
	}

	// get actorId to userId map
	actorIdToUserIdMap, err := getActorIdToUserIdMap(ctx, actorIds, actorClient)
	if err != nil {
		return nil, err
	}

	var userIdsList []string
	for _, userId := range actorIdToUserIdMap {
		userIdsList = append(userIdsList, userId)
	}

	// get kyc level of given users
	userIdToKycLevelMap := getUserIdToKycLevelMap(ctx, userIdsList, bankCustomerClient)

	// check if credit txns are allowed for given users.
	userIdToIsCreditTxnAllowedMap, err := getUserIdToIsCreditTxnAllowedMap(ctx, userIdsList, savingsClient)
	if err != nil {
		return nil, err
	}

	// filter in rewards which pass the retryable conditions.
	for _, reward := range rewards {
		ctx := epificontext.CtxWithTraceId(ctx, uuid.New().String())

		rewardsFilteringRateLimit.Take()
		rewardModel, err := reward.GetReward()
		if err != nil {
			return nil, fmt.Errorf("error getting reward model, err: %w", err)
		}
		var (
			actorId                   = rewardModel.ActorId
			userId                    = actorIdToUserIdMap[actorId]
			isImplicitLockingDisabled = rewardModel.RewardOptions.GetIsImplicitLockingDisabled()
			isCreditTxnAllowed        = userIdToIsCreditTxnAllowedMap[userId]
			isUserFullKyc             = userIdToKycLevelMap[userId] == kycPb.KYCLevel_FULL_KYC
			shouldRetryReward         = false
		)
		switch rewardModel.ChosenReward.GetRewardType() {
		case rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER:
			userHasActiveCc, checkErr := checkIfUserHasActiveCc(ctx, rewardModel.ActorId, fireflyClient)
			if checkErr != nil {
				return nil, fmt.Errorf("error checking if user has active cc, rewardId: %s, err: %w", rewardModel.Id, checkErr)
			}
			if userHasActiveCc {
				shouldRetryReward = true
			} else {
				logger.Info(ctx, "user doesn't have active cc, not re-processing reward", zap.String(logger.REWARD_ID, rewardModel.Id), zap.String(logger.ACTOR_ID_V2, actorId))
			}
		case rewardsPb.RewardType_CASH, rewardsPb.RewardType_US_STOCK:
			switch {
			case !isCreditTxnAllowed:
				logger.Info(ctx, "credit txns are not allowed for user, not re-processing reward", zap.String(logger.REWARD_ID, rewardModel.Id), zap.String(logger.ACTOR_ID_V2, actorId))
			case isImplicitLockingDisabled:
				shouldRetryReward = true
			case isUserFullKyc:
				shouldRetryReward = true
			default:
				logger.Info(ctx, "user is not full kyc, not re-processing reward", zap.String(logger.REWARD_ID, rewardModel.Id), zap.String(logger.ACTOR_ID_V2, actorId))
			}
		default:
			if isUserFullKyc || isImplicitLockingDisabled {
				shouldRetryReward = true
			} else {
				logger.Info(ctx, "user is neither full kyc nor implicit locking disabled, not re-processing reward", zap.String(logger.REWARD_ID, rewardModel.Id), zap.String(logger.ACTOR_ID_V2, actorId))
			}
		}
		if shouldRetryReward {
			retryableRewards = append(retryableRewards, reward)
		}
	}

	return retryableRewards, nil
}

func checkIfUserHasActiveCc(ctx context.Context, actorId string, fireflyClient ffPb.FireflyClient) (bool, error) {
	getCreditCardRes, err := fireflyClient.GetCreditCard(ctx, &ffPb.GetCreditCardRequest{GetBy: &ffPb.GetCreditCardRequest_ActorId{ActorId: actorId}})
	if rpcErr := epifigrpc.RPCError(getCreditCardRes, err); rpcErr != nil && !getCreditCardRes.GetStatus().IsRecordNotFound() {
		return false, fmt.Errorf("error while checking if user has a CC, err: %w", err)
	}

	return lo.Contains([]enums.CardState{enums.CardState_CARD_STATE_ACTIVATED, enums.CardState_CARD_STATE_DIGITALLY_ACTIVATED}, getCreditCardRes.GetCreditCard().GetCardState()), nil
}

func getActorIdToUserIdMap(ctx context.Context, actorIds []string, actorClient actorPb.ActorClient) (map[string]string, error) {
	actorIdToUserIdMap := make(map[string]string, len(actorIds))
	if len(actorIds) == 0 {
		return actorIdToUserIdMap, nil
	}

	entityDetailsRes, err := actorClient.GetEntityDetails(ctx, &actorPb.GetEntityDetailsRequest{ActorIds: actorIds})
	if rpcErr := epifigrpc.RPCError(entityDetailsRes, err); rpcErr != nil {
		return nil, rpcErr
	}

	for _, entityDetail := range entityDetailsRes.GetEntityDetails() {
		actorIdToUserIdMap[entityDetail.GetActorId()] = entityDetail.GetEntityId()
	}
	return actorIdToUserIdMap, nil
}

func getUserIdToKycLevelMap(ctx context.Context, userIds []string, bankCustomerClient bankcust.BankCustomerServiceClient) map[string]kycPb.KYCLevel {
	userIdToKycLevelMap := make(map[string]kycPb.KYCLevel, len(userIds))
	if len(userIds) == 0 {
		return userIdToKycLevelMap
	}

	for _, userId := range userIds {
		bcResp, err := bankCustomerClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bankcust.GetBankCustomerRequest_UserId{
				UserId: userId,
			},
		})
		if rpcErr := epifigrpc.RPCError(bcResp, err); rpcErr != nil {
			logger.Error(ctx, "error in getting bank customer info from backend", zap.Error(rpcErr))
			continue
		}
		userIdToKycLevelMap[userId] = bcResp.GetBankCustomer().GetKycInfo().GetKycLevel()
	}
	return userIdToKycLevelMap
}

func getUserIdToIsCreditTxnAllowedMap(ctx context.Context, userIds []string, savingsClient savingsPb.SavingsClient) (map[string]bool, error) {
	userIdToIsCreditTxnAllowedMap := make(map[string]bool, len(userIds))
	if len(userIds) == 0 {
		return userIdToIsCreditTxnAllowedMap, nil
	}

	getAccountsRes, err := savingsClient.GetAccountsList(ctx, &savingsPb.GetAccountsListRequest{
		Identifier: &savingsPb.GetAccountsListRequest_UserIds{
			UserIds: &savingsPb.PrimaryUserIdentifier{
				PrimaryUserIds: userIds,
				AccountProductOfferingList: []typesAccountPb.AccountProductOffering{
					typesAccountPb.AccountProductOffering_APO_REGULAR,
					typesAccountPb.AccountProductOffering_APO_NRO,
					// not adding NRE since credit txns to NRE account can be done only from NRE account. we don't reward from NRE account yet
				},
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(getAccountsRes, err); rpcErr != nil {
		return nil, rpcErr
	}

	for _, account := range getAccountsRes.GetAccounts() {
		// if credit txns are allowed on any one of the accounts (regular, nre etc), then we allow processing for the user.
		if userIdToIsCreditTxnAllowedMap[account.GetPrimaryAccountHolder()] {
			continue
		}

		accountAccessLevel := account.GetConstraints().GetAccessLevel()
		// ACCESS_LEVEL_UNSCPECIFIED also implies full access.
		if accountAccessLevel == savingsPb.AccessLevel_ACCESS_LEVEL_UNSCPECIFIED ||
			accountAccessLevel == savingsPb.AccessLevel_ACCESS_LEVEL_FULL_ACCESS ||
			accountAccessLevel == savingsPb.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS && !lo.Contains(account.GetConstraints().GetRestrictions(), savingsPb.Restriction_RESTRICTION_CREDIT_FREEZE) {
			userIdToIsCreditTxnAllowedMap[account.GetPrimaryAccountHolder()] = true
		}
	}
	return userIdToIsCreditTxnAllowedMap, nil
}

func reprocessReward(ctx context.Context, db *gorm.DB, rewardDbModel *model.Reward) error {
	switch rewardDbModel.Status {
	case rewardsPb.RewardStatus_PROCESSING_MANUAL_INTERVENTION, rewardsPb.RewardStatus_PROCESSING_FAILED:
		return updateRewardStateForInitiatingProcessingRetry(ctx, db, *rewardDbModel.Id)
	case rewardsPb.RewardStatus_PROCESSING_PENDING:
		// no need to reset processing ref for processing pending rewards since existing processing request might get success independently
		return nil
	default:
		return fmt.Errorf("unsupported reward status for reprocessing failed rewards")
	}
}

func updateRewardStateForInitiatingProcessingRetry(ctx context.Context, dbConn *gorm.DB, rewardId string) error {
	db := gormctxv2.FromContextOrDefault(ctx, dbConn)

	txnErr := db.Transaction(func(txn *gorm.DB) error {
		// fetch the processingRef of processing failed/manual_intervention reward
		var rewardModel model.Reward
		if err := txn.Where("id = ? AND status in (?)", rewardId, allowedRewardStatusesForProcessingRetry).Take(&rewardModel).Error; err != nil {
			return fmt.Errorf("error fetching reward by id and status, err : %w", err)
		}
		rewardProcessingRef := rewardModel.ProcessingRef
		if len(rewardProcessingRef) == 0 {
			return errors.New("reward processing ref is nil")
		}

		// update the state from FAILED/MANUAL_INTERVENTION to PROCESSING_PENDING and reset the processing ref.
		res1 := txn.Exec("Update rewards set processing_ref = ?, status = ?, updated_at = ? where id = ? and status in (?) and processing_ref = ?", "", rewardsPb.RewardStatus_PROCESSING_PENDING, time.Now(), rewardId, allowedRewardStatusesForProcessingRetry, rewardProcessingRef)
		if res1.Error != nil {
			return fmt.Errorf("error updating reward status to pending state, err : %w", res1.Error)
		}
		if res1.RowsAffected == 0 {
			return errors.New("error updating reward status, no rows updated")
		}

		// soft delete the failed processing request entry
		res2 := txn.Exec("Update processing_requests set is_deleted = ?, updated_at = ? where id = ?", true, time.Now(), rewardProcessingRef)
		if res2.Error != nil {
			return fmt.Errorf("error soft deleting reward processing request entry, err : %w", res2.Error)
		}
		if res2.RowsAffected == 0 {
			return errors.New("error soft deleting reward processing request entry, no rows affected")
		}
		return nil
	})
	return txnErr
}

func newPublisher(conf *config.Config, pubConf *cfg.SqsPublisher) queue.Publisher {
	// initialize sqs client and resolve queue URLs
	awsSession, err := session.NewSession(conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		logger.Panic("error to initialize AWS session", zap.Error(err))
	}
	sqsClient := sqs.InitSQSClient(awsSession)

	pub, err := sqs.NewPublisherWithConfig(pubConf, sqsClient, nil)
	if err != nil {
		logger.Panic("failed to initialize publisher", zap.Error(err))
	}
	return pub
}

func getRewardById(db *gorm.DB, id string) (*rewardsPb.Reward, error) {
	var rewardDbModel model.Reward
	if err := db.Where("id = ?", id).Take(&rewardDbModel).Error; err != nil {
		return nil, err
	}
	rewardSvcModel, err := rewardDbModel.GetReward()
	if err != nil {
		return nil, err
	}
	return rewardSvcModel.GetProtoReward()
}

// splits the comma separated entries into list of strings.
func splitCommaSepStringIntoList(commaSeparatedEntries string) []string {
	var sanitizedEntries []string
	// split the comma separated entries to get list of strings
	entries := strings.Split(commaSeparatedEntries, ",")

	// sanitize each entry of list to remove extra spaces
	for _, entry := range entries {
		sanitizedEntry := strings.TrimSpace(entry)
		if sanitizedEntry != "" {
			sanitizedEntries = append(sanitizedEntries, sanitizedEntry)
		}
	}
	logger.InfoNoCtx("sanitized list", zap.Any("sanitized_list", sanitizedEntries))

	return sanitizedEntries
}
