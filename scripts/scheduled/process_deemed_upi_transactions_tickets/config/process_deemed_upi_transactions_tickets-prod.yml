Application:
  Environment: "prod"
  Name: "process_deemed_upi_transactions_tickets"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "./crdb/prod/"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

ProcessTicketJobConfig:
  MaxTicketsThresholdMap:
    DEEMED_UPI_TRANSACTIONS: 10
  JobStatsEmailParam:
    FromEmailId: "<EMAIL>"
    FromEmailName: "Process Ticket Automation non-prod"
    ReceiverMailIdList:
      DEEMED_UPI_TRANSACTIONS:
        ReceiverMailInfo1:
          EmailName: "Pay Team"
          EmailId: "<EMAIL>"
        ReceiverMailInfo2:
          EmailName: "CX Ops Slack"
          EmailId: "<EMAIL>"
        ReceiverMailInfo3:
          EmailName: "CX Ops Jenkins Slack"
          EmailId: "<EMAIL>"
    EmailMsg:
      TRANSACTION: "Please find troubleshooting details in attachment."
  NumberOfDays: 89

DeemedP2PTransactionAutoResolveDuration: "48h"
DeemedP2MTransactionAutoResolveDuration: "120h"
