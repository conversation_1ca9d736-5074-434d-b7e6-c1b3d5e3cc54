package main

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/savings/extacct/dao"
)

type JobUpdateBankAccountVerificationJob struct {
	bankAccountVerificationsDao dao.BankAccountVerificationsDao
}

var (
	gotErrorForBavIds []string
	updatedBavIds     []string
)

func (j *JobUpdateBankAccountVerificationJob) DoJob(ctx context.Context, req *JobRequest) error {
	// arg1 will be comma separated bav id for which status needs to be updated
	// arg2 will be status to be updated. (example: OVERALL_STATUS_SUCCESS)
	if req.Args1 == "" || req.Args2 == "" {
		return fmt.Errorf("BAV Id and status are required")
	}

	bavIds := getActorIdsAfterCleaning(req.Args1, ",")

	to_status := extacct.OverallStatus(extacct.OverallStatus_value[req.Args2])
	for _, id := range bavIds {
		err := j.bankAccountVerificationsDao.UpdateByFieldMask(ctx, &extacct.BankAccountVerification{
			Id:            id,
			OverallStatus: to_status,
		}, []extacct.BankAccountVerificationFieldMask{
			extacct.BankAccountVerificationFieldMask_FIELD_MASK_OVERALL_STATUS,
		})
		if err != nil {
			gotErrorForBavIds = append(gotErrorForBavIds, id)
			fmt.Printf("\nFailed to update BAV\": %v", zap.Error(err))
			continue
		}
		updatedBavIds = append(updatedBavIds, id)
		fmt.Printf("\nSuccessfully updated BAV: %v", id)
	}

	if len(updatedBavIds) > 0 {
		fmt.Println("List of bavIds successfully updated are :")
		printUsers(updatedBavIds)
	}
	if len(gotErrorForBavIds) > 0 {
		fmt.Println("List of bavIds  which could not be updated are : ")
		printUsers(gotErrorForBavIds)
	}
	return nil
}

func printUsers(userSlice []string) {
	for _, val := range userSlice {
		fmt.Println(val)
	}
}
