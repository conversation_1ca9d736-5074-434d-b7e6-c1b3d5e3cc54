package main

import (
	"context"
	"flag"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifiserver"
	"github.com/epifi/be-common/pkg/logger"

	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
)

const (
	overrideCoolOff     = "OVERRIDE_COOL_OFF"
	automatic           = "AUTOMATIC"
	manualOrchestration = "MANUAL_ORCHESTRATION"
)

var (
	actorIdListFlag       = flag.String("actorId", "", "pass comma seperated actor ids")
	orchestrationTypeFlag = flag.String("orchestrationType", "", "orchestration type to do force movement")
)

func getActorIdsAfterCleaning(actorIdsUncleaned string) []string {
	actorIds := make([]string, 0)
	actorIdsCleaned := strings.Split(actorIdsUncleaned, ",")
	for _, actorId := range actorIdsCleaned {
		cleanedActorId := strings.TrimSpace(actorId)
		cleanedActorId = strings.Trim(cleanedActorId, "\n")
		actorIds = append(actorIds, cleanedActorId)
	}

	return actorIds
}

const readInputFromFile = "read-input-from-file"
const batchSize = 100
const sleepTime = time.Second

func main() {
	epifiserver.HandlePanic()
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}
	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	flag.Parse()
	actorIdList := *actorIdListFlag
	if actorIdList == "" {
		flag.Usage()
		logger.Panic("actorIdList is empty")
		return
	}

	if actorIdList == readInputFromFile {
		configDir, err := cfg.GetConfigDir()
		if err != nil {
			logger.Panic("error getting config dir", zap.Error(err))
		}

		inputFilePath := filepath.Join(configDir, "input.txt")

		logger.InfoNoCtx("reading actor ids from file", zap.String("inputFilePath", inputFilePath))

		// nolint: gosec
		fileContent, err := os.ReadFile(inputFilePath)
		if err != nil {
			logger.Panic("Error reading file", zap.Error(err))
		}
		actorIdList = string(fileContent)
	}

	actorIds := getActorIdsAfterCleaning(actorIdList)

	orchestrationType := *orchestrationTypeFlag

	logger.Info(context.Background(), "number of actor ids", zap.Int("length", len(actorIds)))

	tieringConn := epifigrpc.NewConnByService(cfg.TIERING_SERVICE)
	defer epifigrpc.CloseConn(tieringConn)
	tieringClient := tieringPb.NewTieringClient(tieringConn)

	var success []string
	var failure []string

	for idx, actorId := range actorIds {
		if idx%batchSize == 0 {
			time.Sleep(sleepTime)
		}

		ctx := context.Background()
		switch orchestrationType {
		case overrideCoolOff:
			overrideResp, overrideErr := tieringClient.OverrideCoolOffPeriod(ctx, &tieringPb.OverrideCoolOffPeriodRequest{
				ActorId:           actorId,
				OverrideTimestamp: timestampPb.Now(),
			})
			if rpcErr := epifigrpc.RPCError(overrideResp, overrideErr); rpcErr != nil {
				logger.Error(ctx, "error in overriding cool off RPC", zap.Error(rpcErr), zap.String("actorId", actorId))
				failure = append(failure, actorId)
				continue
			}
		case automatic:
			upgradeResp, upgradeErr := tieringClient.Upgrade(ctx, &tieringPb.UpgradeRequest{
				ActorId:    actorId,
				Provenance: tieringEnumPb.Provenance_PROVENANCE_AUTOMATIC,
			})
			if rpcErr := epifigrpc.RPCError(upgradeResp, upgradeErr); rpcErr != nil {
				logger.Error(ctx, "error in upgrade RPC", zap.Error(rpcErr), zap.String("actorId", actorId))
				failure = append(failure, actorId)
				continue
			}
		case manualOrchestration:
			upgradeResp, upgradeErr := tieringClient.Upgrade(ctx, &tieringPb.UpgradeRequest{
				ActorId:    actorId,
				Provenance: tieringEnumPb.Provenance_PROVENANCE_MANUAL_OVERRIDE,
			})
			if rpcErr := epifigrpc.RPCError(upgradeResp, upgradeErr); rpcErr != nil {
				logger.Error(ctx, "error in upgrade RPC", zap.Error(rpcErr), zap.String("actorId", actorId))
				failure = append(failure, actorId)
				continue
			}
		default:
			logger.Error(ctx, "orchestration method not handled", zap.String("orchestrationType", orchestrationType))
			failure = append(failure, actorId)
			continue
		}

		success = append(success, actorId)
	}

	logger.Info(context.Background(), "manual override upgrade RPC call successful")
	println("\nFailed Actors: ")
	for _, actorId := range failure {
		println("\t", actorId)
	}

	println("\nSuccessful Actors: ")
	for _, actorId := range success {
		println("\t", actorId)
	}
}
