Application:
  Environment: "prod"
  Name: "register_card_shipments"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 1
  MaxIdleConn: 1

AWS:
  Region: "ap-south-1"

RegisterShipmentPublisher:
  QueueName: "prod-card-shipment-register-queue"
