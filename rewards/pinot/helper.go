package pinot

import (
	"fmt"
	"time"

	rewardsPinotPb "github.com/epifi/gamma/api/rewards/pinot"
	"github.com/epifi/gamma/rewards/pinot/dao/model"
)

// populateTimeFilter populates the time range fields on a filter struct (passed as interface{})
// based on the provided timeRangeType, using type assertions.
func populateTimeFilter(filterInterface interface{}, timeRangeType rewardsPinotPb.TimeRangeType, fromTime, toTime time.Time) error {
	switch f := filterInterface.(type) {
	case *model.RewardAggregationFilter:
		// Handle RewardAggregationFilter
		switch timeRangeType {
		case rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT:
			f.FromCreatedAt = fromTime
			f.ToCreatedAt = toTime
		case rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME:
			f.FromActionTime = fromTime
			f.ToActionTime = toTime
		default:
			// This case should have been caught by extractTimeRangeFilter validation
			return fmt.Errorf("unexpected time range type for RewardAggregationFilter: %v", timeRangeType)
		}
	case *model.RewardProjectionAggregationFilter:
		// Handle RewardProjectionAggregationFilter
		switch timeRangeType {
		case rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT:
			f.FromCreatedAt = fromTime
			f.ToCreatedAt = toTime
		case rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME:
			f.FromActionTime = fromTime
			f.ToActionTime = toTime
		default:
			// This case should have been caught by extractTimeRangeFilter validation
			return fmt.Errorf("unexpected time range type for RewardProjectionAggregationFilter: %v", timeRangeType)
		}
	default:
		// Handle unexpected filter types
		return fmt.Errorf("unexpected filter type passed to populateTimeFilter: %T", filterInterface)
	}
	return nil // Success
}
