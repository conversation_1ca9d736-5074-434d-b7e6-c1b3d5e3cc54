package service

import (
	"context"
	"fmt"
	"math"
	"sort"
	"time"

	rewardOfferPb "github.com/epifi/gamma/api/rewards/rewardoffers"

	"github.com/jonboulle/clockwork"
	cmap "github.com/orcaman/concurrent-map"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/durationpb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/pagination"

	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	dataCollectorPb "github.com/epifi/gamma/api/rewards/datacollector"
	events2 "github.com/epifi/gamma/api/rewards/events"
	luckydrawPb "github.com/epifi/gamma/api/rewards/luckydraw"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	pkgRewards "github.com/epifi/gamma/pkg/rewards"
	"github.com/epifi/gamma/rewards/aggregator"
	clawbackDao "github.com/epifi/gamma/rewards/clawback/dao"
	clawbackDaoModel "github.com/epifi/gamma/rewards/clawback/dao/model"
	"github.com/epifi/gamma/rewards/config"
	"github.com/epifi/gamma/rewards/config/genconf"
	rewardConstants "github.com/epifi/gamma/rewards/constants"
	"github.com/epifi/gamma/rewards/generator/dao"
	daoModel "github.com/epifi/gamma/rewards/generator/dao/model"
	"github.com/epifi/gamma/rewards/generator/model"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/common"
	"github.com/epifi/gamma/rewards/helper/rewardamountcalculator"
	dao2 "github.com/epifi/gamma/rewards/projector/dao"
	model2 "github.com/epifi/gamma/rewards/projector/dao/model"
	"github.com/epifi/gamma/rewards/wire/types"
)

const PageSizeOfGetAllRewardsAndProjectionsRpc = 50

type RewardsService struct {
	rewardsPb.UnimplementedRewardsGeneratorServer
	rewardDao                           dao.RewardsDao
	creditCardRewardsDao                dao.CreditCardRewardDao
	clawbacksDao                        clawbackDao.RewardClawbacksDao
	ccRewardAggregator                  aggregator.ICreditCardRewardAggregator
	ccRewardAmtCalculator               rewardamountcalculator.ICCRewardAmountCalculator
	rewardProcessingPublisher           types.RewardsSqsPublisher
	rewardProcessingDelayPublisher      types.RewardsProcessingSqsCustomDelayPublisher
	rewardsManualGiveawayEventPublisher types.RewardsManualGiveawayEventSqsPublisher
	bulkClaimRewardsEventPublisher      types.BulkClaimRewardsEventSqsPublisher
	eventsBroker                        events.Broker
	luckyDrawSvcClient                  luckydrawPb.LuckyDrawServiceClient
	cacheStorage                        cache.CacheStorage
	conf                                *config.Config
	dyconf                              *genconf.Config
	clock                               clockwork.Clock
	rewardOfferDao                      dao.RewardOfferDao
	projectionsDao                      dao2.RewardsProjectionDao
	payClient                           payPb.PayClient
}

var _ rewardsPb.RewardsGeneratorServer = &RewardsService{}

func NewRewardsGeneratorService(
	rewardDao dao.RewardsDao,
	creditCardRewardsDao dao.CreditCardRewardDao,
	clawbacksDao clawbackDao.RewardClawbacksDao,
	ccRewardAggregator aggregator.ICreditCardRewardAggregator,
	ccRewardAmtCalculator rewardamountcalculator.ICCRewardAmountCalculator,
	rewardProcessingPublisher types.RewardsSqsPublisher,
	rewardProcessingDelayPublisher types.RewardsProcessingSqsCustomDelayPublisher,
	rewardsManualGiveawayEventPublisher types.RewardsManualGiveawayEventSqsPublisher,
	bulkClaimRewardsEventPublisher types.BulkClaimRewardsEventSqsPublisher,
	eventsBroker events.Broker,
	luckyDrawSvcClient luckydrawPb.LuckyDrawServiceClient,
	cacheStorage cache.CacheStorage,
	conf *config.Config,
	dyconf *genconf.Config,
	clock clockwork.Clock,
	rewardOfferDao dao.RewardOfferDao,
	projectionsDao dao2.RewardsProjectionDao,
	payClient payPb.PayClient,
) *RewardsService {
	return &RewardsService{
		rewardDao:                           rewardDao,
		creditCardRewardsDao:                creditCardRewardsDao,
		clawbacksDao:                        clawbacksDao,
		ccRewardAggregator:                  ccRewardAggregator,
		ccRewardAmtCalculator:               ccRewardAmtCalculator,
		rewardProcessingPublisher:           rewardProcessingPublisher,
		rewardProcessingDelayPublisher:      rewardProcessingDelayPublisher,
		rewardsManualGiveawayEventPublisher: rewardsManualGiveawayEventPublisher,
		bulkClaimRewardsEventPublisher:      bulkClaimRewardsEventPublisher,
		eventsBroker:                        eventsBroker,
		luckyDrawSvcClient:                  luckyDrawSvcClient,
		cacheStorage:                        cacheStorage,
		conf:                                conf,
		dyconf:                              dyconf,
		clock:                               clock,
		rewardOfferDao:                      rewardOfferDao,
		projectionsDao:                      projectionsDao,
		payClient:                           payClient,
	}
}

func (rgs *RewardsService) GetRewardsByActorId(ctx context.Context, req *rewardsPb.RewardsByActorIdRequest) (*rewardsPb.RewardsResponse, error) {
	pageToken, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "unable to decode page tokens", zap.String("actorId", req.GetActorId()), zap.Error(err))
		return &rewardsPb.RewardsResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(err.Error()),
		}, nil
	}

	var (
		rewards    []*model.Reward
		pageCtxRes *rpc.PageContextResponse
	)

	// `Filters` of RewardsByActorIdRequest struct is deprecated, implementation only exists for backwards compatibility
	if req.GetFilter() != nil {
		filters := &model.QueryRewardsFilter{
			ActorId:          req.GetActorId(),
			ExternalRefIds:   req.GetFilter().GetExternalRefList(),
			RefIds:           req.GetFilter().GetRefIds(),
			ClawbackRefIds:   req.GetFilter().GetClawbackRefIds(),
			RewardOfferId:    req.GetFilter().GetRewardOfferId(),
			RewardType:       req.GetFilter().GetRewardType(),
			VisibilityType:   req.GetFilter().GetVisibilityType(),
			Statuses:         req.GetFilter().GetStatuses(),
			PageSize:         int(req.GetPageContext().GetPageSize()),
			ActionTypes:      req.GetFilter().GetActionTypes(),
			ClaimType:        req.GetFilter().GetClaimType(),
			FromTime:         datetime.TimestampToTime(req.GetFilter().GetStartDate()),
			UptoTime:         datetime.TimestampToTime(req.GetFilter().GetEndDate()),
			RewardOfferTypes: req.GetFilter().GetRewardOfferTypes(),
		}

		if req.GetFilter().GetRewardId() != "" {
			filters.RewardIds = []string{req.GetFilter().GetRewardId()}
		}

		// applied only if RewardOfferTypes filter in req is empty
		if req.GetFilter().GetRewardOfferType() != rewardsPb.RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE && len(req.GetFilter().GetRewardOfferTypes()) == 0 {
			filters.RewardOfferTypes = []rewardsPb.RewardOfferType{req.GetFilter().GetRewardOfferType()}
		}

		rewards, pageCtxRes, err = rgs.rewardDao.FetchRewardsByPageAndFilter(ctx, filters, pageToken)
		if err != nil {
			logger.Error(ctx, "unable to fetch rewards by AND filter", zap.String("actorId", req.ActorId), zap.Error(err))
			return &rewardsPb.RewardsResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	} else if req.GetFiltersV2() != nil || req.GetActorId() != "" {
		// if ActorId is provided with no filters, we'll fetch data using FetchPaginatedRewardsByFiltersV2
		filters := &model.QueryRewardsFilterV2{}

		if req.GetFiltersV2().GetAndFilter() != nil {
			filters.AndFilter = &model.AndRewardsFilter{
				ExternalRefIds:   req.GetFiltersV2().GetAndFilter().GetExternalRefList(),
				RefIds:           req.GetFiltersV2().GetAndFilter().GetRefIds(),
				RewardOfferId:    req.GetFiltersV2().GetAndFilter().GetRewardOfferId(),
				RewardType:       req.GetFiltersV2().GetAndFilter().GetRewardType(),
				VisibilityType:   req.GetFiltersV2().GetAndFilter().GetVisibilityType(),
				Statuses:         req.GetFiltersV2().GetAndFilter().GetStatuses(),
				ActionTypes:      req.GetFiltersV2().GetAndFilter().GetActionTypes(),
				ClaimType:        req.GetFiltersV2().GetAndFilter().GetClaimType(),
				RewardOfferTypes: req.GetFiltersV2().GetAndFilter().GetRewardOfferTypes(),
				FromTime:         datetime.TimestampToTime(req.GetFiltersV2().GetAndFilter().GetStartDate()),
				UptoTime:         datetime.TimestampToTime(req.GetFiltersV2().GetAndFilter().GetEndDate()),
			}
		}

		// applied only if RewardOfferTypes filter in req is empty
		if req.GetFiltersV2().GetAndFilter().GetRewardOfferType() != rewardsPb.RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE && len(req.GetFiltersV2().GetAndFilter().GetRewardOfferTypes()) == 0 {
			filters.AndFilter.RewardOfferTypes = []rewardsPb.RewardOfferType{req.GetFiltersV2().GetAndFilter().GetRewardOfferType()}
		}

		if req.GetFiltersV2().GetOrFilter() != nil {
			filters.OrFilter = &model.OrRewardsFilter{
				ClaimType: req.GetFiltersV2().GetOrFilter().GetClaimType(),
				Statuses:  req.GetFiltersV2().GetOrFilter().GetStatuses(),
			}
		}

		rewards, pageCtxRes, err = rgs.rewardDao.FetchPaginatedRewardsByFiltersV2(ctx, req.GetActorId(), filters, pageToken, int(req.GetPageContext().GetPageSize()))
		if err != nil {
			logger.Error(ctx, "unable to fetch rewards by filter V2", zap.String("actorId", req.ActorId), zap.Error(err))
			return &rewardsPb.RewardsResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	}

	res := &rewardsPb.RewardsResponse{
		Status: rpc.StatusOk(),
	}
	for _, reward := range rewards {
		rewardProto, err := reward.GetProtoReward()
		if err != nil {
			logger.Error(ctx, "unable to convert reward to proto reward", zap.String("actorId", req.ActorId), zap.Error(err))
			return &rewardsPb.RewardsResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
		res.Rewards = append(res.Rewards, rewardProto)
	}

	res.PageContext = pageCtxRes
	return res, nil
}

func (rgs *RewardsService) GetRewardsByRewardId(ctx context.Context, req *rewardsPb.RewardsByRewardIdRequest) (*rewardsPb.RewardResponse, error) {
	reward, err := rgs.rewardDao.FetchRewardById(ctx, req.RewardId)
	if err != nil {
		logger.Error(ctx, "unable to fetch reward", zap.String(logger.REWARD_ID, req.RewardId), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &rewardsPb.RewardResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		return &rewardsPb.RewardResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	rewardProto, err := reward.GetProtoReward()
	if err != nil {
		logger.Error(ctx, "unable to convert reward to proto reward", zap.String(logger.ACTOR_ID, req.RewardId), zap.Error(err))
		return &rewardsPb.RewardResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &rewardsPb.RewardResponse{
		Status: rpc.StatusOk(),
		Reward: rewardProto,
	}, nil
}

func (rgs *RewardsService) GetRewards(ctx context.Context, req *rewardsPb.GetRewardsRequest) (*rewardsPb.GetRewardsResponse, error) {
	queryFilter := &model.QueryRewardsFilter{
		RewardIds:      req.GetIds(),
		ExternalRefIds: req.GetExternalRefIds(),
	}

	if req.GetRewardOfferType() != rewardsPb.RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE {
		queryFilter.RewardOfferTypes = []rewardsPb.RewardOfferType{req.GetRewardOfferType()}
	}

	rewards, err := rgs.rewardDao.FetchRewards(ctx, queryFilter)
	if err != nil {
		logger.Error(ctx, "unable to fetch rewards", zap.Any("request", req), zap.Error(err))
		return &rewardsPb.GetRewardsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	var rewardProtos []*rewardsPb.Reward
	for _, reward := range rewards {
		rewardProto, err := reward.GetProtoReward()
		if err != nil {
			logger.Error(ctx, "error converting reward model to proto", zap.String(logger.REWARD_ID, reward.Id), zap.Error(err))
			return &rewardsPb.GetRewardsResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
		rewardProtos = append(rewardProtos, rewardProto)
	}

	return &rewardsPb.GetRewardsResponse{
		Status:  rpc.StatusOk(),
		Rewards: rewardProtos,
	}, nil
}

func (rgs *RewardsService) ChooseReward(ctx context.Context, req *rewardsPb.ChooseRewardRequest) (*rewardsPb.ChooseRewardResponse, error) {
	reward, err := rgs.rewardDao.FetchRewardById(ctx, req.GetRewardId())
	if err != nil {
		logger.Error(ctx, "unable to fetch reward by given reward id", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String(logger.CHOSEN_OPTION_ID, req.GetRewardOptionId()), zap.Error(err))
		return &rewardsPb.ChooseRewardResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	if reward == nil {
		logger.Info(ctx, "invalid reward id", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String(logger.CHOSEN_OPTION_ID, req.GetRewardOptionId()))
		return &rewardsPb.ChooseRewardResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid reward id"),
		}, nil
	}

	var chosenOption *rewardsPb.RewardOption
	for _, option := range reward.RewardOptions.GetOptions() {
		if req.GetRewardOptionId() == option.GetId() {
			chosenOption = option
			break
		}
	}
	if chosenOption == nil {
		logger.Error(ctx, "invalid reward option id", zap.String(logger.REWARD_ID, reward.Id), zap.String(logger.CHOSEN_OPTION_ID, req.GetRewardOptionId()))
		return &rewardsPb.ChooseRewardResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid reward option id"),
		}, nil
	}

	if err = rgs.updateChosenOptionWithRequiredDetails(chosenOption, req.GetClaimMetadata()); err != nil {
		logger.Error(ctx, "failed to update chosen option with required details", zap.String(logger.REWARD_ID, reward.Id), zap.String(logger.CHOSEN_OPTION_ID, req.GetRewardOptionId()), zap.Error(err))
		return &rewardsPb.ChooseRewardResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	err = rgs.rewardDao.ChooseReward(ctx, reward, chosenOption, rewardsPb.RewardStatus_CREATED, rewardsPb.SubStatus_SUB_STATUS_UNSPECIFIED)
	if err != nil {
		// if err is ErrRowNotUpdated, it implies reward option was already chosen previously and as a result record was not updated.
		// Handling it gracefully by returning no error as multiple calls to ChooseOption api is expected due to network issue related retry by client.
		if errors.Is(err, epifierrors.ErrRowNotUpdated) {
			logger.Info(ctx, "reward option was already chosen for the reward", zap.String(logger.REWARD_ID, reward.Id), zap.String(logger.CHOSEN_OPTION_ID, chosenOption.GetId()))
			return &rewardsPb.ChooseRewardResponse{
				Status: rpc.StatusOk(),
			}, nil
		}
		logger.Error(ctx, "unable to update chosen reward option", zap.String(logger.REWARD_ID, reward.Id), zap.String(logger.CHOSEN_OPTION_ID, chosenOption.GetId()), zap.Error(err))
		return &rewardsPb.ChooseRewardResponse{
			Status: rpc.StatusInternalWithDebugMsg("unable to update chosen reward option"),
		}, nil
	}

	// setting chosen reward
	reward.ChosenReward = chosenOption

	err = rgs.publishRewardForProcessing(ctx, reward, false)
	if err != nil {
		logger.Error(ctx, "failed to publish reward for processing", zap.String(logger.REWARD_ID, reward.Id), zap.String(logger.CHOSEN_OPTION_ID, chosenOption.GetId()), zap.Error(err))
		return &rewardsPb.ChooseRewardResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to publish reward for processing"),
		}, nil
	}

	return &rewardsPb.ChooseRewardResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (rgs *RewardsService) GetUnOpenedRewardsCount(ctx context.Context, req *rewardsPb.GetUnOpenedRewardsCountRequest) (*rewardsPb.GetUnOpenedRewardsCountResponse, error) {
	queryRewardsFilter := &model.QueryRewardsFilter{
		ActorId:  req.GetActorId(),
		Statuses: []rewardsPb.RewardStatus{rewardsPb.RewardStatus_CREATED},
	}
	if req.GetFromTime().IsValid() {
		queryRewardsFilter.FromTime = req.GetFromTime().AsTime()
	}
	if req.GetUptoTime().IsValid() {
		queryRewardsFilter.UptoTime = req.GetUptoTime().AsTime()
	}
	unclaimedRewards, err := rgs.rewardDao.FetchRewards(ctx, queryRewardsFilter)
	if err != nil {
		logger.Error(ctx, "error fetching unclaimed rewards by actorId", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(err))
		return &rewardsPb.GetUnOpenedRewardsCountResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching unclaimed rewards for actor : " + err.Error()),
		}, nil
	}
	// filter out no reward type since they are not visible on my rewards screen
	// not filtering out other reward types since they are auto claimed
	unclaimedRewards = lo.Filter(unclaimedRewards, func(reward *model.Reward, _ int) bool {
		return reward.RewardType != rewardsPb.RewardType_NO_REWARD
	})
	unclaimedRewardCount := int32(len(unclaimedRewards))

	// get count of unclaimed lucky draw winnings
	winningsSummaryRes, err := rgs.luckyDrawSvcClient.GetWinningsSummaryForActor(ctx, &luckydrawPb.GetWinningsSummaryForActorRequest{
		ActorId: req.GetActorId(),
		Filter: &luckydrawPb.GetWinningsSummaryForActorRequest_Filter{
			// reward status CREATED implies reward from winning isn't claimed yet
			RewardStatuses: []luckydrawPb.RewardStatus{luckydrawPb.RewardStatus_CREATED},
		},
	})
	if err != nil || !winningsSummaryRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "luckyDrawSvcClient.GetWinningsSummaryForActor rpc call failed", zap.Any(logger.RPC_STATUS, winningsSummaryRes.GetStatus()), zap.Error(err))
		return &rewardsPb.GetUnOpenedRewardsCountResponse{
			Status: rpc.StatusInternalWithDebugMsg("luckyDrawSvcClient.GetWinningsSummaryForActor rpc call failed"),
		}, nil
	}
	unclaimedRewardCount += int32(winningsSummaryRes.GetWinningsCount())

	return &rewardsPb.GetUnOpenedRewardsCountResponse{
		Status:              rpc.StatusOk(),
		UnopenedRewardCount: unclaimedRewardCount,
	}, nil
}

// todo(growth-infra): optimise fetching of rewards summary. currently all
// rewards are being fetched for an actor and aggregates are being done. this is
// highly unoptimal and needs to be changed
func (rgs *RewardsService) GetRewardSummary(ctx context.Context, req *rewardsPb.GetRewardSummaryRequest) (*rewardsPb.GetRewardSummaryResponse, error) {
	queryFilter := &model.QueryRewardsFilter{
		ActorId:        req.GetActorId(),
		ExternalRefIds: req.GetFilter().GetExternalRefIds(),
		ActionTypes:    req.GetFilter().GetActionTypes(),
	}

	if req.GetFilter().GetFromTime() != nil {
		queryFilter.FromTime = req.GetFilter().GetFromTime().AsTime()
	}

	if req.GetFilter().GetUptoTime() != nil {
		queryFilter.UptoTime = req.GetFilter().GetUptoTime().AsTime()
	}

	// Priority: reward offer types filter > reward offer type filter
	if len(req.GetFilter().GetRewardOfferTypes()) > 0 {
		queryFilter.RewardOfferTypes = req.GetFilter().GetRewardOfferTypes()
	} else if req.GetFilter().GetRewardOfferType() != rewardsPb.RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE {
		queryFilter.RewardOfferTypes = []rewardsPb.RewardOfferType{req.GetFilter().GetRewardOfferType()}
	}

	earnedRewards, err := rgs.rewardDao.FetchRewards(ctx, queryFilter)

	if err != nil {
		logger.Error(ctx, "error fetching earned rewards by actorId", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(err))
		return &rewardsPb.GetRewardSummaryResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching earned rewards for actor : " + err.Error())}, nil
	}

	successResp := &rewardsPb.GetRewardSummaryResponse{
		Status:                            rpc.StatusOk(),
		TotalCashRewardEarned:             money.AmountINR(0).GetPb(),
		TotalInProcessingCashRewardAmount: money.AmountINR(0).GetPb(),
		TotalSidRewardEarned:              money.AmountINR(0).GetPb(),
		TotalInProcessingSidRewardAmount:  money.AmountINR(0).GetPb(),
		TotalLockedCashRewardAmount:       money.AmountINR(0).GetPb(),
		TotalCountOfRewards:               0,
	}

	var luckyDrawIds []string
	for _, reward := range earnedRewards {
		if reward.Status == rewardsPb.RewardStatus_CLAWED_BACK {
			continue
		}
		successResp.TotalCountOfRewards++

		switch reward.RewardType {
		case rewardsPb.RewardType_CASH:
			if reward.Status == rewardsPb.RewardStatus_PROCESSED {
				successResp.TotalCashRewardEarned.Units += reward.ChosenReward.GetCash().GetAmount().GetUnits()
			} else {
				successResp.TotalInProcessingCashRewardAmount.Units += reward.ChosenReward.GetCash().GetAmount().GetUnits()
			}
			successResp.TotalCountOfCashRewards++

		case rewardsPb.RewardType_SMART_DEPOSIT:
			if reward.Status == rewardsPb.RewardStatus_PROCESSED {
				successResp.TotalSidRewardEarned.Units += reward.ChosenReward.GetSmartDeposit().GetAmount().GetUnits()
			} else {
				successResp.TotalInProcessingSidRewardAmount.Units += reward.ChosenReward.GetSmartDeposit().GetAmount().GetUnits()
			}
			successResp.TotalCountOfSidRewards++

		case rewardsPb.RewardType_FI_COINS:
			if reward.Status == rewardsPb.RewardStatus_PROCESSED {
				successResp.TotalFiCoinsEarned += int32(reward.ChosenReward.GetFiCoins().GetUnits())
			} else {
				successResp.TotalInProcessingFiCoins += int32(reward.ChosenReward.GetFiCoins().GetUnits())
			}
			successResp.TotalCountOfFiCoinRewards++

		case rewardsPb.RewardType_LUCKY_DRAW:
			luckyDrawIds = append(luckyDrawIds, reward.ChosenReward.GetLuckyDraw().GetLuckyDrawId())

		case rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER:
			if reward.Status == rewardsPb.RewardStatus_PROCESSED {
				successResp.TotalCashRewardEarned.Units += reward.ChosenReward.GetCreditCardBillEraser().GetAmount().GetUnits()
			} else {
				successResp.TotalInProcessingCashRewardAmount.Units += reward.ChosenReward.GetCreditCardBillEraser().GetAmount().GetUnits()
			}
			successResp.TotalCountOfCashRewards++

		// added default case to avoid lint exhaustive error
		default:
		}

		if reward.Status == rewardsPb.RewardStatus_LOCKED && len(reward.RewardOptions.GetOptions()) == 1 && reward.ClaimType == rewardsPb.ClaimType_CLAIM_TYPE_AUTOMATIC {
			switch reward.RewardOptions.GetOptions()[0].GetRewardType() {
			case rewardsPb.RewardType_FI_COINS:
				successResp.TotalLockedFiCoinsRewardAmount += int32(reward.RewardOptions.GetOptions()[0].GetFiCoins().GetUnits())
				successResp.TotalCountOfFiCoinRewards++
			case rewardsPb.RewardType_CASH:
				successResp.TotalLockedCashRewardAmount.Units += reward.RewardOptions.GetOptions()[0].GetCash().GetAmount().GetUnits()
				successResp.TotalCountOfCashRewards++
			case rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER:
				successResp.TotalLockedCashRewardAmount.Units += reward.RewardOptions.GetOptions()[0].GetCreditCardBillEraser().GetAmount().GetUnits()
				successResp.TotalCountOfCashRewards++
			}
		}
	}

	if err = rgs.addWinningsSummaryInRewardSummaryResp(ctx, req.GetActorId(), luckyDrawIds, successResp); err != nil {
		logger.Error(ctx, "error adding winnings summary to reward summary", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(err))
		return &rewardsPb.GetRewardSummaryResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return successResp, nil
}

// addWinningsSummaryInRewardSummaryResp fetches luckyDraw winnings summary for an actor and adds it to reward summary resp.
func (rgs *RewardsService) addWinningsSummaryInRewardSummaryResp(ctx context.Context, actorId string, luckyDrawIds []string, summaryResp *rewardsPb.GetRewardSummaryResponse) error {
	if len(luckyDrawIds) == 0 {
		return nil
	}
	winningsSummaryRes, err := rgs.luckyDrawSvcClient.GetWinningsSummaryForActor(ctx, &luckydrawPb.GetWinningsSummaryForActorRequest{
		ActorId: actorId,
		Filter:  &luckydrawPb.GetWinningsSummaryForActorRequest_Filter{LuckyDrawIds: luckyDrawIds},
	})
	if err != nil || !winningsSummaryRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "luckyDrawService.GetWinningsSummaryForActor rpc call failed", zap.String(logger.ACTOR_ID, actorId), zap.Any(logger.RPC_STATUS, winningsSummaryRes.GetStatus()), zap.Error(err))
		return errors.New("luckyDrawService.GetWinningsSummaryForActor rpc call failed")
	}
	summaryResp.TotalCountOfCashRewards += int32(winningsSummaryRes.GetCashRewardCount())
	summaryResp.TotalCashRewardEarned.Units += winningsSummaryRes.GetCashRewardEarned().GetUnits()
	summaryResp.TotalInProcessingCashRewardAmount.Units += winningsSummaryRes.GetInProcessCashRewardAmount().GetUnits()

	summaryResp.TotalCountOfSidRewards += int32(winningsSummaryRes.GetSdRewardCount())
	summaryResp.TotalSidRewardEarned.Units += winningsSummaryRes.GetSdRewardEarned().GetUnits()
	summaryResp.TotalInProcessingSidRewardAmount.Units += winningsSummaryRes.GetInProcessSdRewardAmount().GetUnits()

	summaryResp.TotalCountOfFiCoinRewards += int32(winningsSummaryRes.GetFiCoinsRewardCount())
	summaryResp.TotalFiCoinsEarned += int32(winningsSummaryRes.GetFiCoinsEarned())
	summaryResp.TotalInProcessingFiCoins += int32(winningsSummaryRes.GetInProcessFiCoins())

	return nil
}

// RetryRewardProcessing method is used to retry the processing of a stuck reward.
// For now, only rewards whose status is 'PROCESSING_IN_PROGRESS' or 'PROCESSING_IN_PROGRESS' can be retried.
func (rgs *RewardsService) RetryRewardProcessing(ctx context.Context, req *rewardsPb.RetryRewardProcessingRequest) (*rewardsPb.RetryRewardProcessingResponse, error) {
	reward, err := rgs.rewardDao.FetchRewardById(ctx, req.GetRewardId())
	if err != nil || reward == nil {
		logger.Error(ctx, "error fetching reward by id", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.Error(err))
		return &rewardsPb.RetryRewardProcessingResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching reward by id")}, nil
	}
	rewardProto, err := reward.GetProtoReward()
	if err != nil {
		logger.Error(ctx, "error converting reward model to proto", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.Error(err))
		return &rewardsPb.RetryRewardProcessingResponse{Status: rpc.StatusInternalWithDebugMsg("error converting reward model to proto")}, nil
	}

	// check if reward is eligible for processing retry
	// only rewards in 'PROCESSING_IN_PROGRESS' or 'PROCESSING_PENDING' state can be retried.
	if rewardProto.GetStatus() != rewardsPb.RewardStatus_PROCESSING_IN_PROGRESS && rewardProto.GetStatus() != rewardsPb.RewardStatus_PROCESSING_PENDING {
		logger.Error(ctx, "reward status not eligible for processing retry", zap.String("reward_status", rewardProto.GetStatus().String()))
		return &rewardsPb.RetryRewardProcessingResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("reward is not eligible for processing retry")}, nil
	}

	// publish event to reward processing queue
	msgId, err := rgs.rewardProcessingPublisher.Publish(ctx, &rewardsPb.RewardEvent{Reward: rewardProto})
	if err != nil {
		logger.Error(ctx, "error publishing event to reward processing queue", zap.String(logger.REWARD_ID, rewardProto.GetId()), zap.Error(err))
		return &rewardsPb.RetryRewardProcessingResponse{Status: rpc.StatusInternalWithDebugMsg("error publishing event to reward processing queue")}, nil
	}
	logger.Info(ctx, "Published event to reward processing queue", zap.String(logger.REWARD_ID, rewardProto.GetId()), zap.String(logger.QUEUE_MESSAGE_ID, msgId))

	return &rewardsPb.RetryRewardProcessingResponse{Status: rpc.StatusOk()}, nil
}

// enrichChosenRewardWithAdditionalMetadata updates the chosen reward using passed metadata.
// metadata contains additional details for the chosen reward option. These additional details are important for reward
// processing but were unavailable at the time of reward generation, eg: nominee details in case of SD reward.
func enrichChosenRewardWithAdditionalMetadata(chosenRewardOption *rewardsPb.RewardOption, metadata *rewardsPb.RewardClaimMetadata) error {
	switch chosenRewardOption.GetRewardType() {
	// enrich sd reward with nominee details and maturity date
	case rewardsPb.RewardType_SMART_DEPOSIT:
		sdReward := chosenRewardOption.GetSmartDeposit()
		sdReward.NomineeInfoList = metadata.GetSdMetadata().GetNomineeInfoList()
		maturityDate, err := rewardsPb.GetRewardConfigDate(sdReward.GetMaturityDateConfig(), time.Now())
		if err != nil {
			return errors.Wrap(err, "error calculating sd maturity date from config")
		}
		sdReward.MaturityDate = maturityDate
		sdReward.Name = metadata.GetSdMetadata().GetSdName()

	// enrich gift hamper reward with shipping address details
	case rewardsPb.RewardType_GIFT_HAMPER:
		giftHamperOption := chosenRewardOption.GetGiftHamper()
		giftHamperOption.ShippingAddress = metadata.GetShippingAddress()

	default:

	}
	return nil
}

// getLatestReferralRewardsUnlockDate returns the latest date (prior to current time stamp) on which referral rewards were reset
// based on the original referralsUnlock date and the configured referralCapTimeDuration. It will always return a date <= current timestamp.
func (rgs *RewardsService) getLatestReferralRewardsUnlockDate(referralsUnlockTimestamp *timestampPb.Timestamp, referralCapTimeDuration time.Duration) *timestampPb.Timestamp {
	var latestReferralRewardsUnlockDate *timestampPb.Timestamp

	// for simplicity, we're ignoring the time of day of referralsUnlockDate and only considering the date for
	// computing latestReferralRewardsUnlockDate
	referralsUnlockDate := timestampPb.New(datetime.GetTimeAtStartOfTheDay(referralsUnlockTimestamp.AsTime()))

	currentDate := datetime.GetTimeAtStartOfTheDay(rgs.clock.Now())

	for dateUnderConsideration := referralsUnlockDate; dateUnderConsideration.AsTime().Before(currentDate) || dateUnderConsideration.AsTime().Equal(currentDate); dateUnderConsideration = timestampPb.New(dateUnderConsideration.AsTime().Add(referralCapTimeDuration)) {
		latestReferralRewardsUnlockDate = &timestampPb.Timestamp{
			Seconds: dateUnderConsideration.GetSeconds(),
			Nanos:   dateUnderConsideration.GetNanos(),
		}
	}
	return latestReferralRewardsUnlockDate
}

func (rgs *RewardsService) GetReferralRewardsCappingInfo(ctx context.Context, req *rewardsPb.GetReferralRewardsCappingInfoRequest) (*rewardsPb.GetReferralRewardsCappingInfoResponse, error) {
	latestReferralRewardsUnlockDate := rgs.getLatestReferralRewardsUnlockDate(req.GetReferralsUnlockDate(), rgs.dyconf.ReferralRewardsCappingAndPeriod().CappingPeriod())

	// todo(divyadeep): Write a new DAO method that only returns the count of rewards as we only need the count here
	referralRewardsSinceUnlockDate, _, err := rgs.rewardDao.FetchRewardsByPageAndFilter(ctx, &model.QueryRewardsFilter{
		ActorId:          req.GetActorId(),
		FromTime:         latestReferralRewardsUnlockDate.AsTime(),
		RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
		PageSize:         100,
	}, nil)
	if err != nil {
		logger.Error(ctx, "failed to fetch rewards for actor from latest referral rewards unlock date",
			zap.Error(err), zap.String(logger.ACTOR_ID, req.GetActorId()),
			zap.Time("latestReferralUnlockDate", latestReferralRewardsUnlockDate.AsTime()))
		return &rewardsPb.GetReferralRewardsCappingInfoResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch rewards for actor"),
		}, nil
	}

	if len(referralRewardsSinceUnlockDate) >= int(rgs.dyconf.ReferralRewardsCappingAndPeriod().RewardsCap(ctx)) {
		return &rewardsPb.GetReferralRewardsCappingInfoResponse{
			Status:      rpc.StatusOk(),
			IsMaxCapHit: true,
			MaxCap:      int32(rgs.dyconf.ReferralRewardsCappingAndPeriod().RewardsCap(ctx)),
			// since latestReferralRewardsUnlockDate stores the last date on which rewards for referrals were unlocked for the actor,
			// and we need to return the next date on which referral rewards will be unlocked for the actor, we add the
			// referral-rewards-capping period to the last unlock date to compute and return the next unlock date.
			MaxCapResetDate: timestampPb.New(latestReferralRewardsUnlockDate.AsTime().
				Add(rgs.dyconf.ReferralRewardsCappingAndPeriod().CappingPeriod())),
			LastCapResetDate: latestReferralRewardsUnlockDate,
		}, nil
	}

	// with the introduction of multiple cappings based on offer types, all the cappings applicable for REFERRAL_REFERRER_OFFER offer type is matched
	// if the number of rewards earned for the offer type since capping effective date for any of the cappings is exceeded, that capping info should be returned
	offerTypeCappings, getOfferTypeCappingsErr := common.GetRewardsCappingInfosForRewardOfferType(ctx, rgs.dyconf, rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER)
	if getOfferTypeCappingsErr != nil {
		logger.Error(ctx, "failed to cappings for offer types", zap.Error(err), zap.String(logger.ACTOR_ID, req.GetActorId()))
		return &rewardsPb.GetReferralRewardsCappingInfoResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to capping infos for rewards offer type"),
		}, nil
	}

	for _, offerTypeCapping := range offerTypeCappings {
		// fetch referral rewards since capping effective date start
		referralRewardsSinceEffectiveDateStart, _, err := rgs.rewardDao.FetchRewardsByPageAndFilter(ctx, &model.QueryRewardsFilter{
			ActorId:          req.GetActorId(),
			FromTime:         offerTypeCapping.CappingEffectiveDate,
			RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
			PageSize:         100,
		}, nil)
		if err != nil {
			logger.Error(ctx, "failed to fetch rewards for actor from capping effective date start",
				zap.Error(err), zap.String(logger.ACTOR_ID, req.GetActorId()),
				zap.Time("cappingEffectiveDate", offerTypeCapping.CappingEffectiveDate))
			return &rewardsPb.GetReferralRewardsCappingInfoResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch rewards for actor"),
			}, nil
		}
		if len(referralRewardsSinceEffectiveDateStart) >= int(offerTypeCapping.CapCount) {
			maxCapResetDate, getMaxCapResetDateErr := getMaxCapResetDateForCappingPeriodAndDuration(offerTypeCapping.CappingPeriod, offerTypeCapping.CappingDuration)
			if getMaxCapResetDateErr != nil {
				return nil, fmt.Errorf("error getting max cap reset date : %w", getMaxCapResetDateErr)
			}
			return &rewardsPb.GetReferralRewardsCappingInfoResponse{
				Status:           rpc.StatusOk(),
				IsMaxCapHit:      true,
				MaxCap:           int32(offerTypeCapping.CapCount),
				MaxCapResetDate:  maxCapResetDate,
				LastCapResetDate: timestampPb.New(offerTypeCapping.CappingEffectiveDate),
			}, nil
		}
	}

	return &rewardsPb.GetReferralRewardsCappingInfoResponse{
		Status:           rpc.StatusOk(),
		IsMaxCapHit:      false,
		MaxCapResetDate:  nil,
		LastCapResetDate: latestReferralRewardsUnlockDate,
	}, nil
}

func getMaxCapResetDateForCappingPeriodAndDuration(cappingPeriod int, cappingDuration string) (*timestampPb.Timestamp, error) {
	switch cappingDuration {
	case "CALENDAR_MONTH":
		return timestampPb.New(datetime.StartOfMonth(time.Now()).AddDate(0, cappingPeriod, 0)), nil
	case "CALENDAR_WEEK":
		return timestampPb.New(datetime.StartOfWeek(time.Now(), time.Sunday).AddDate(0, 0, 7*cappingPeriod)), nil
	case "CALENDAR_DAY":
		return timestampPb.New(datetime.StartOfDay(time.Now()).AddDate(0, 0, 1*cappingPeriod)), nil
	}

	return nil, fmt.Errorf("max cap reset date for capping duration %s is unimplemented", cappingDuration)
}

func (rgs *RewardsService) GetReferralRewardsCappingConfig(ctx context.Context, req *rewardsPb.GetReferralRewardsCappingConfigRequest) (*rewardsPb.GetReferralRewardsCappingConfigResponse, error) {
	return &rewardsPb.GetReferralRewardsCappingConfigResponse{
		Status:        rpc.StatusOk(),
		RewardsCap:    rgs.dyconf.ReferralRewardsCappingAndPeriod().RewardsCap(ctx),
		CappingPeriod: durationpb.New(rgs.dyconf.ReferralRewardsCappingAndPeriod().CappingPeriod()),
	}, nil
}

// BulkClaimRewardsWithDefaultOption is used for claiming multiple rewards at once for an actor.
// nolint:funlen
func (rgs *RewardsService) BulkClaimRewardsWithDefaultOption(ctx context.Context, req *rewardsPb.BulkClaimRewardsWithDefaultOptionRequest) (*rewardsPb.BulkClaimRewardsWithDefaultOptionResponse, error) {
	const excessRewardsToFetchForBulkClaiming = 15

	// fetch unclaimed rewards
	rewards, _, err := rgs.rewardDao.FetchRewardsByPageAndFilter(ctx, &model.QueryRewardsFilter{
		ActorId:  req.GetActorId(),
		Statuses: []rewardsPb.RewardStatus{rewardsPb.RewardStatus_CREATED},
		// fetching more rewards than the max number of rewards we need to claim, so that
		// if there are multiple un-claimable rewards in the initial set of rewards, we
		// can claim other rewards
		PageSize: int(req.GetMaxRewardsToClaim()) + excessRewardsToFetchForBulkClaiming,
	}, nil)
	if err != nil {
		logger.Error(ctx, "error while fetching rewards for actor", zap.Error(err))
		return &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusInternalWithDebugMsg("error while fetching rewards for actor")}, nil
	}

	// return a successful response if there are no unclaimed reward to claim
	if len(rewards) == 0 {
		logger.Info(ctx, "no unclaimed reward present for actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusOk()}, nil
	}

	rewardIdToChosenOptionMap := make(map[string]*rewardsPb.RewardOption)
	rewardIdToRewardMap := make(map[string]*model.Reward)
	var rewardsToClaim []*model.Reward

	for _, reward := range rewards {
		if len(rewardsToClaim) >= int(req.GetMaxRewardsToClaim()) {
			break
		}

		// we're going to choose the first option in case of multi-choice rewards
		chosenOption := reward.RewardOptions.GetOptions()[0]

		// check if we can claim this reward option or not
		if !rgs.canAutoChooseDefaultOption(chosenOption, reward.RewardOptions.GetUnlockDate()) {
			// todo(divyadeep): remove this log line after monitoring it for some time
			logger.Info(
				ctx,
				"reward can't be auto-claimed for actor",
				zap.String(logger.REWARD_ID, reward.Id),
				zap.String(logger.REWARD_TYPE, chosenOption.GetRewardType().String()),
				zap.Time("unlockDate", reward.RewardOptions.GetUnlockDate().AsTime()),
			)
			continue
		}

		// since we're only claiming Fi-coins and Cash type of rewards right now, we don't need to pass any claimMetaData
		err = rgs.updateChosenOptionWithRequiredDetails(chosenOption, nil)
		if err != nil {
			logger.Error(ctx, "unable to update chosen option with required details", zap.String(logger.REWARD_ID, reward.Id), zap.String(logger.CHOSEN_OPTION_ID, chosenOption.GetId()), zap.Error(err))
			return &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusInternalWithDebugMsg("unable to update chosen option with required details")}, nil
		}

		rewardIdToChosenOptionMap[reward.Id] = chosenOption
		rewardIdToRewardMap[reward.Id] = reward

		rewardsToClaim = append(rewardsToClaim, reward)
	}

	if len(rewardsToClaim) > 0 {
		// BulkChooseRewards will return an error even if a single reward is not claimed due to any reason
		err = rgs.rewardDao.BulkChooseRewards(ctx, rewardIdToChosenOptionMap, rewardIdToRewardMap)
		if err != nil {
			logger.Error(ctx, "failed to claim rewards in bulk for actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
			return &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusInternalWithDebugMsg("failed to claim rewards in bulk for actor")}, nil
		}

		for _, reward := range rewardsToClaim {
			reward.ChosenReward = rewardIdToChosenOptionMap[reward.Id]
			err = rgs.publishRewardForProcessing(ctx, reward, true)
			if err != nil {
				logger.Error(ctx, "error while publishing reward for processing", zap.String(logger.REWARD_ID, reward.Id), zap.Error(err))
				return &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusInternalWithDebugMsg("error while publishing reward for processing")}, nil
			}
		}
	} else {
		logger.Info(ctx, "no reward claimed for actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusOk()}, nil
	}

	return &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusOk()}, nil
}

func (rgs *RewardsService) canAutoChooseDefaultOption(rewardOption *rewardsPb.RewardOption, unlockDate *timestampPb.Timestamp) bool {
	switch rewardOption.GetRewardType() {
	// can only auto-claim cash, fi-coins, cc bill eraser type rewards in V1
	case rewardsPb.RewardType_CASH, rewardsPb.RewardType_FI_COINS, rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER:
		// do nothing
	default:
		return false
	}

	return rgs.isRewardUnlocked(unlockDate)
}

// isRewardUnlocked returns true if reward is unlocked for claiming or not.
// currently the locking is timestamp based but in future it can be extended to
// other use cases such as min-kyc locked rewards
func (rgs *RewardsService) isRewardUnlocked(unlockDate *timestampPb.Timestamp) bool {
	return time.Now().After(unlockDate.AsTime())
}

func (rgs *RewardsService) updateChosenOptionWithRequiredDetails(chosenOption *rewardsPb.RewardOption, claimMetaData *rewardsPb.RewardClaimMetadata) error {
	// enriching chosen option with claim metadata
	if err := enrichChosenRewardWithAdditionalMetadata(chosenOption, claimMetaData); err != nil {
		return fmt.Errorf("unable to enrich reward option with claim metadata. chosenOptionId: %s, err: %w", chosenOption.GetId(), err)
	}

	rewardProcessingDate, err := rewardsPb.GetRewardConfigDate(chosenOption.GetRewardProcessingTimeConfig(), time.Now())
	if err != nil {
		return fmt.Errorf("unable to calculate processing date. chosenOptionId: %s, err: %w", chosenOption.GetId(), err)
	}
	chosenOption.ProcessingDate = rewardProcessingDate

	return nil
}

func (rgs *RewardsService) publishRewardForProcessing(ctx context.Context, reward *model.Reward, shouldSuppressNotifications bool) error {
	rewardProto, err := reward.GetProtoReward()
	if err != nil {
		return fmt.Errorf("unable to convert reward to proto. rewardId: %s, err: %w", reward.Id, err)
	}
	rewardEvent := &rewardsPb.RewardEvent{
		Reward:                      rewardProto,
		ShouldSuppressNotifications: shouldSuppressNotifications,
	}

	rewardProcessingTime := reward.ChosenReward.GetProcessingDate().AsTime()

	if time.Now().After(rewardProcessingTime) {
		publishId, err := rgs.rewardProcessingPublisher.Publish(ctx, rewardEvent)
		if err != nil {
			return fmt.Errorf("unable to publish reward to processing queue. rewardId: %s, err: %w", reward.Id, err)
		}
		logger.Info(ctx, "Published to processing queue", zap.String(logger.QUEUE_MESSAGE_ID, publishId))
	} else {
		delayDuration := time.Until(rewardProcessingTime)
		publishId, err := rgs.rewardProcessingDelayPublisher.PublishWithDelay(ctx, rewardEvent, delayDuration)
		if err != nil {
			return fmt.Errorf("unable to publish reward to delay processing queue. rewardId: %s, err: %w", reward.Id, err)
		}
		logger.Info(ctx, "Published to delay processing queue", zap.String("publish_id", publishId), zap.Any("delay_duration", delayDuration))
	}

	return nil
}

func (rgs *RewardsService) GetAutoClaimableRewardsCount(ctx context.Context, req *rewardsPb.GetAutoClaimableRewardsCountRequest) (*rewardsPb.GetAutoClaimableRewardsCountResponse, error) {
	panic("unimplemented")
}

// TriggerManualGiveawayRewardForActor rpc is useful for triggering manual-giveaway reward for an actor, useful for appeasement purposes.
func (rgs *RewardsService) TriggerManualGiveawayRewardForActor(ctx context.Context, req *rewardsPb.TriggerManualGiveawayRewardForActorRequest) (*rewardsPb.TriggerManualGiveawayRewardForActorResponse, error) {
	// todo (utkarsh) : add validations on allowed rewardOfferId for the client.
	// publish manual giveaway event to trigger reward for the actor.
	manualGiveawayEvent := &dataCollectorPb.ManualGiveawayEvent{
		ActorId:        req.GetActorId(),
		EventId:        req.GetClientRequestId(),
		RewardOfferId:  req.GetRewardOfferId(),
		RewardAmount:   req.GetRewardAmount(),
		EventTimestamp: timestampPb.Now(),
	}
	msgId, err := rgs.rewardsManualGiveawayEventPublisher.Publish(ctx, manualGiveawayEvent)
	if err != nil {
		logger.Error(ctx, "error publishing manual giveaway event", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err))
		return &rewardsPb.TriggerManualGiveawayRewardForActorResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	logger.Info(ctx, "manual giveaway event published successfully", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.String(logger.QUEUE_MESSAGE_ID, msgId))

	return &rewardsPb.TriggerManualGiveawayRewardForActorResponse{Status: rpc.StatusOk()}, nil
}

// GetCreditCardLinkedRewardDetails rpc is useful to get details of earned (or projected) credit card linked rewards/reward-clawbacks.
// these details would be useful to fetch the reward/reward-clawback details for display on credit card all txns view.
// nolint: funlen
func (rgs *RewardsService) GetCreditCardLinkedRewardDetails(ctx context.Context, req *rewardsPb.GetCreditCardLinkedRewardDetailsRequest) (*rewardsPb.GetCreditCardLinkedRewardDetailsResponse, error) {
	rewards, err := rgs.rewardDao.FetchRewards(ctx, &model.QueryRewardsFilter{
		RefIds:           req.GetRefIds(),
		RewardOfferTypes: []rewardsPb.RewardOfferType{req.GetRewardOfferType()},
	})
	if err != nil {
		logger.Error(ctx, "error fetching rewards from db", zap.Error(err))
		return &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	var detailsList []*rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details

	refIdToIsRewardPresentMap := make(map[string]bool, len(rewards))
	for _, reward := range rewards {
		refIdToIsRewardPresentMap[reward.RefId] = true

		// assumption for now is that above rpc would be used for single choice rewards only.
		if len(reward.RewardOptions.Options) > 1 {
			logger.WarnWithCtx(ctx, "reward having more than one reward options are not supported in GetCreditCardLinkedRewardDetails response", zap.String(logger.REWARD_ID, reward.Id))
			continue
		}
		defaultRewardOption := reward.RewardOptions.Options[0]

		// get reward units from reward option
		rewardUnits, rewardUnitsErr := rewardsPb.GetRewardUnitsFromRewardOption(defaultRewardOption)
		if rewardUnitsErr != nil {
			logger.Error(ctx, "error fetching reward units from reward option", zap.String(logger.REWARD_ID, reward.Id), zap.Error(rewardUnitsErr))
			return &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{Status: rpc.StatusInternalWithDebugMsg(rewardUnitsErr.Error())}, nil
		}
		rewardDetails := &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails{
			RewardType:     defaultRewardOption.GetRewardType(),
			RewardUnits:    rewardUnits,
			Status:         reward.Status,
			RefId:          reward.RefId,
			RewardMetadata: reward.RewardMetadata,
		}
		detailsList = append(detailsList, &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details{
			Details: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails{
				RewardDetails: rewardDetails,
			},
		})
	}

	// for some offer types, we need to return the projected reward details in case reward is not yet generated.
	for _, refId := range req.GetRefIds() {
		if rewardExists := refIdToIsRewardPresentMap[refId]; rewardExists {
			continue
		}
		var (
			projectedRewardDetails *rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails
			getProjectedRewardErr  error
		)

		switch req.GetRewardOfferType() {
		case rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER:
			// for cc top merchant spends reward the refId is the billId of the credit card bill
			ccBillId := refId
			projectedRewardDetails, getProjectedRewardErr = rgs.getProjectedCcTopMerchantSpendsRewardDetails(ctx, ccBillId)
			if getProjectedRewardErr != nil {
				logger.Error(ctx, "error getting projected reward details for cc top merchant spends reward", zap.String(logger.REFERENCE_ID, refId), zap.Error(getProjectedRewardErr))
				return &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{Status: rpc.StatusInternalWithDebugMsg(getProjectedRewardErr.Error())}, nil
			}
		case rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER:
			// for cc curated merchants spends reward the refId is the billId of the credit card bill
			ccBillId := refId
			projectedRewardDetails, getProjectedRewardErr = rgs.getProjectedCcCuratedMerchantsSpendsRewardDetails(ctx, ccBillId)
			if getProjectedRewardErr != nil {
				logger.Error(ctx, "error getting projected reward details for cc curated merchants spends reward", zap.String(logger.REFERENCE_ID, refId), zap.Error(getProjectedRewardErr))
				return &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{Status: rpc.StatusInternalWithDebugMsg(getProjectedRewardErr.Error())}, nil
			}

		default:
			// don't need projected reward, so just continue with next iteration
			continue
		}

		if projectedRewardDetails != nil {
			detailsList = append(detailsList, &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details{
				Details: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails{
					RewardDetails: projectedRewardDetails,
				},
			})
		}
	}

	// fetch clawbacks with given refIds
	rewardClawbacks, err := rgs.clawbacksDao.GetClawbacks(ctx, &clawbackDaoModel.ClawbacksQueryFilter{
		ActionRefIds:    req.GetRefIds(),
		RewardOfferType: req.GetRewardOfferType(),
	})
	if err != nil {
		logger.Error(ctx, "error fetching clawbacks from db", zap.Error(err))
		return &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	for _, rewardClawback := range rewardClawbacks {
		detailsList = append(detailsList, &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details{
			Details: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardClawbackDetails{
				RewardClawbackDetails: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails{
					ClawedBackRewardUnits: rewardClawback.GetClawedBackRewardUnits(),
					RewardType:            rewardClawback.GetRewardType(),
					ClawbackStatus:        rewardClawback.GetStatus(),
					ClawbackRefId:         rewardClawback.GetActionRefId(),
				},
			},
		})
	}

	return &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{
		Status:      rpc.StatusOk(),
		DetailsList: detailsList,
	}, nil
}

func (rgs *RewardsService) getProjectedCcTopMerchantSpendsRewardDetails(ctx context.Context, ccBillId string) (*rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails, error) {
	// calculate the projected reward amount
	rewardCalculationRes, err := rgs.ccRewardAmtCalculator.CalculateCCTopMerchantsSpendsOfferRewardAmount(ctx, &rewardamountcalculator.CCTopMerchSpendsOfferRewardAmountReq{
		BillId: ccBillId,
	})
	if err != nil {
		return nil, fmt.Errorf("error calculating project reward amount, err : %w", err)
	}

	// we give Fi-Coins reward for now in cc top merchant spends reward
	// **Note** : if reward amount is zero then returning NO_REWARD as rewardType as the actual reward would also be generated with NO_REWARD type for such a case.
	rewardType := rewardsPb.RewardType_FI_COINS
	if rewardCalculationRes.RewardAmount == 0 {
		rewardType = rewardsPb.RewardType_NO_REWARD
	}

	return &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails{
		RewardType:  rewardType,
		RewardUnits: float32(rewardCalculationRes.RewardAmount),
		Status:      rewardsPb.RewardStatus_PROCESSING_PENDING,
		RefId:       ccBillId,
		RewardMetadata: &rewardsPb.RewardMetadata{
			OfferTypeSpecificMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata{
				Metadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata_{
					CcTopMerchantsSpendsRewardMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata{
						AppliedRewardMultiplier:          rewardCalculationRes.AppliedRewardMultiplier,
						TopMerchantNameTo_1XRewardAmount: rewardCalculationRes.TopMerchantNameTo1xRewardAmountMap,
					},
				},
			},
		},
		IsProjectedReward: true,
	}, nil
}

func (rgs *RewardsService) getProjectedCcCuratedMerchantsSpendsRewardDetails(ctx context.Context, ccBillId string) (*rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails, error) {
	// calculate the projected reward amount
	rewardCalculationRes, err := rgs.ccRewardAmtCalculator.CalculateCCCuratedMerchantsSpendsOfferRewardAmount(ctx, &rewardamountcalculator.CCCuratedMerchantsSpendsOfferRewardAmountReq{
		BillId: ccBillId,
	})
	if err != nil {
		return nil, fmt.Errorf("error calculating project reward amount, err : %w", err)
	}

	// we give Fi-Coins reward for now in cc curated merchants spends reward
	// **Note** : if reward amount is zero then returning NO_REWARD as rewardType as the actual reward would also be generated with NO_REWARD type for such a case.
	rewardType := rewardsPb.RewardType_FI_COINS
	if rewardCalculationRes.RewardAmount == 0 {
		rewardType = rewardsPb.RewardType_NO_REWARD
	}

	return &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails{
		RewardType:  rewardType,
		RewardUnits: float32(rewardCalculationRes.RewardAmount),
		Status:      rewardsPb.RewardStatus_PROCESSING_PENDING,
		RefId:       ccBillId,
		RewardMetadata: &rewardsPb.RewardMetadata{
			OfferTypeSpecificMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata{
				Metadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata_{
					CcCuratedMerchantsSpendsRewardMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata{
						CuratedMerchantNameTo_1XRewardAmount: rewardCalculationRes.CuratedMerchantNameTo1xRewardAmountMap,
					},
				},
			},
		},
		IsProjectedReward: true,
	}, nil
}

// todo (utkarsh/divyadeep) : add units test
// nolint: funlen
func (rgs *RewardsService) GetCreditCard1XRewardsSummary(ctx context.Context, req *rewardsPb.GetCreditCard1XRewardsSummaryRequest) (*rewardsPb.GetCreditCard1XRewardsSummaryResponse, error) {
	processed1xFiCoins := uint32(0)
	unprocessed1xFiCoins := uint32(0)

	// get 1x reward aggregates at reward status level
	rewardStatusLevelRewardAggregates, err := rgs.creditCardRewardsDao.Get1xRewardAggregatesAtRewardStatusLevel(ctx, &daoModel.CreditCardRewardQueryFilters{
		CreditCardAccountId: req.GetCreditCardAccountId(),
		FromActionTime:      datetime.TimestampToTime(req.GetTimeWindow().GetFromTime()),
		TillActionTime:      datetime.TimestampToTime(req.GetTimeWindow().GetTillTime()),
	})
	if err != nil {
		logger.Error(ctx, "error while fetching reward status level reward aggregates", zap.String("creditCardAccountId", req.GetCreditCardAccountId()),
			zap.Time("fromTimeFilter", req.GetTimeWindow().GetFromTime().AsTime()), zap.Time("tillTimeFilter", req.GetTimeWindow().GetTillTime().AsTime()), zap.Error(err))
		return &rewardsPb.GetCreditCard1XRewardsSummaryResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	for _, rewardAggr := range rewardStatusLevelRewardAggregates {
		switch rewardAggr.RewardStatus {
		case rewardsPb.RewardStatus_PROCESSED:
			processed1xFiCoins += uint32(rewardAggr.AggregateValue)
		case rewardsPb.RewardStatus_PROCESSING_PENDING, rewardsPb.RewardStatus_CREATED, rewardsPb.RewardStatus_PROCESSING_IN_PROGRESS, rewardsPb.RewardStatus_PROCESSING_MANUAL_INTERVENTION, rewardsPb.RewardStatus_PROCESSING_FAILED:
			unprocessed1xFiCoins += uint32(rewardAggr.AggregateValue)
		default:
			// any other state will not be considered in processed/processing fi coins
		}
	}

	// get clawback aggregates for 1x rewards at clawback status level
	clawbackStatusLevelClawbackAggregates, err := rgs.creditCardRewardsDao.Get1xRewardClawbackAggregatesAtClawbackStatusLevel(ctx, &daoModel.CreditCardRewardClawbackQueryFilters{
		CreditCardAccountId:    req.GetCreditCardAccountId(),
		ClawbackFromActionTime: datetime.TimestampToTime(req.GetTimeWindow().GetFromTime()),
		ClawbackTillActionTime: datetime.TimestampToTime(req.GetTimeWindow().GetTillTime()),
		// only aggregate clawbacks for rewards which were given for actions performed in given time range
		RewardFromActionTime: datetime.TimestampToTime(req.GetTimeWindow().GetFromTime()),
		RewardTillActionTime: datetime.TimestampToTime(req.GetTimeWindow().GetTillTime()),
	})
	if err != nil {
		logger.Error(ctx, "error while fetching clawback status level clawback aggregates", zap.String("creditCardAccountId", req.GetCreditCardAccountId()),
			zap.Time("fromTimeFilter", req.GetTimeWindow().GetFromTime().AsTime()), zap.Time("tillTimeFilter", req.GetTimeWindow().GetTillTime().AsTime()), zap.Error(err))
		return &rewardsPb.GetCreditCard1XRewardsSummaryResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	for _, clawbackAggr := range clawbackStatusLevelClawbackAggregates {
		switch clawbackAggr.ClawbackStatus {
		case rewardsPb.ClawbackStatus_CLAWBACK_STATUS_PROCESSED:
			processed1xFiCoins -= clawbackAggr.AggregateValue
		case rewardsPb.ClawbackStatus_CLAWBACK_STATUS_PROCESSING_PENDING, rewardsPb.ClawbackStatus_CLAWBACK_STATUS_PROCESSING_FAILED:
			unprocessed1xFiCoins -= clawbackAggr.AggregateValue
		default:
			// any other state will not be considered in processed/processing fi coins
		}
	}

	// get merchant level 1x reward aggregates
	merchantLevelRewardAggrRes, err := rgs.ccRewardAggregator.Get1xRewardAggregatesAtMerchantLevel(ctx, &aggregator.Get1xRewardAggregatesAtMerchantLevelRequest{
		CreditCardAccountId: req.GetCreditCardAccountId(),
		FromTime:            datetime.TimestampToTime(req.GetTimeWindow().GetFromTime()),
		TillTime:            datetime.TimestampToTime(req.GetTimeWindow().GetTillTime()),
	})
	if err != nil {
		logger.Error(ctx, "error fetching merchant level 1x reward aggregates", zap.String("creditCardAccountId", req.GetCreditCardAccountId()),
			zap.Time("fromTimeFilter", req.GetTimeWindow().GetFromTime().AsTime()), zap.Time("tillTimeFilter", req.GetTimeWindow().GetTillTime().AsTime()), zap.Error(err))
		return &rewardsPb.GetCreditCard1XRewardsSummaryResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// merchant aggregates need to be sorted in desc order of aggregate reward amount
	var merchantRewardAggregates []*rewardsPb.GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate
	for merchantName, rewardAmount := range merchantLevelRewardAggrRes.MerchantNameToNetRewardAmount {
		merchantRewardAggregates = append(merchantRewardAggregates, &rewardsPb.GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate{
			MerchantName: merchantName,
			NetFiCoins:   uint32(rewardAmount),
		})
	}
	sort.Slice(merchantRewardAggregates, func(i, j int) bool {
		return merchantRewardAggregates[i].NetFiCoins > merchantRewardAggregates[j].NetFiCoins
	})

	// todo (utkarsh) : move this log to debug level once the rpc is stable
	logger.Info(ctx, "merchant reward aggregate", zap.Any(logger.REQUEST, req), zap.Any(logger.RESPONSE, merchantRewardAggregates))

	return &rewardsPb.GetCreditCard1XRewardsSummaryResponse{
		Status:                   rpc.StatusOk(),
		Processed_1XFiCoins:      processed1xFiCoins,
		Processing_1XFiCoins:     unprocessed1xFiCoins,
		MerchantRewardAggregates: merchantRewardAggregates,
	}, nil
}

func (rgs *RewardsService) GetRewardsCount(ctx context.Context, request *rewardsPb.GetRewardsCountRequest) (*rewardsPb.GetRewardsCountResponse, error) {
	queryFilters := &daoModel.RewardsCountFilters{}

	if request.GetFilters().GetAndFilter() != nil {
		queryFilters.AndFilter = &daoModel.RewardsCountFilterFields{
			FromTime:         datetime.TimestampToTime(request.GetFilters().GetAndFilter().GetFromTime()),
			TillTime:         datetime.TimestampToTime(request.GetFilters().GetAndFilter().GetTillTime()),
			RewardOfferTypes: request.GetFilters().GetAndFilter().GetRewardOfferTypes(),
			RewardTypes:      request.GetFilters().GetAndFilter().GetRewardTypes(),
		}
	}
	if request.GetFilters().GetOrFilter() != nil {
		queryFilters.OrFilter = &daoModel.RewardsCountFilterFields{
			FromTime:         datetime.TimestampToTime(request.GetFilters().GetOrFilter().GetFromTime()),
			TillTime:         datetime.TimestampToTime(request.GetFilters().GetOrFilter().GetTillTime()),
			RewardOfferTypes: request.GetFilters().GetOrFilter().GetRewardOfferTypes(),
			RewardTypes:      request.GetFilters().GetOrFilter().GetRewardTypes(),
		}
	}

	rewardsCount, err := rgs.rewardDao.GetRewardsCount(ctx, request.GetActorId(), queryFilters)
	if err != nil {
		logger.Error(ctx, "error while getting count of rewards for actor", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		return &rewardsPb.GetRewardsCountResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &rewardsPb.GetRewardsCountResponse{
		Status:       rpc.StatusOk(),
		RewardsCount: rewardsCount,
	}, nil
}

func (rgs *RewardsService) GetRewardUnitsUtilisationForActorAndOfferInMonth(ctx context.Context, req *rewardsPb.GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) (*rewardsPb.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse, error) {
	monthlyRewardUnitsUtilisedForActorAndOffer, err := rgs.rewardDao.GetMonthlyRewardUnitsUtilisedForActorAndOffer(ctx, req.GetActorId(), req.GetRewardOfferId(), req.GetDate().AsTime())
	if err != nil && err != epifierrors.ErrRecordNotFound {
		logger.Error(ctx, "error in GetMonthlyRewardUnitsUtilisedForActorAndOffer dao", zap.Error(err))
		return &rewardsPb.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in GetMonthlyRewardUnitsUtilisedForActorAndOffer dao"),
		}, nil
	}

	// compute start and end of month as the required from/till time
	fromTime := datetime.StartOfMonth(req.GetDate().AsTime().In(datetime.IST))
	tillTime := *(datetime.AddNMonths(&fromTime, 1))
	monthlyRewardsCount, err := rgs.rewardDao.GetRewardsCount(ctx, req.GetActorId(), &daoModel.RewardsCountFilters{
		AndFilter: &daoModel.RewardsCountFilterFields{
			FromTime:       fromTime,
			TillTime:       tillTime,
			RewardOfferIds: []string{req.GetRewardOfferId()},
		},
	})
	if err != nil {
		logger.Error(ctx, "error in GetRewardsCount dao", zap.Error(err))
		return &rewardsPb.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in GetRewardsCount dao"),
		}, nil
	}

	return &rewardsPb.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse{
		Status: rpc.StatusOk(),
		MonthlyRewardOfferRewardUnitsActorUtilisation: monthlyRewardUnitsUtilisedForActorAndOffer,
		MonthlyRewardCount:                            monthlyRewardsCount,
	}, nil

}

// nolint: funlen
func (rgs *RewardsService) GetRewardUtilisationForActorAndOffer(ctx context.Context, req *rewardsPb.GetRewardUtilisationForActorAndOfferRequest) (*rewardsPb.GetRewardUtilisationForActorAndOfferResponse, error) {

	rewardOffer, err := rgs.rewardOfferDao.FetchRewardOfferById(ctx, req.GetRewardOfferId())
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Info(ctx, "error ErrRecordNotFound in FetchRewardOfferById dao", zap.Error(err), zap.String(logger.REWARD_OFFER_ID, req.GetRewardOfferId()))
		return &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
			Status: rpc.StatusRecordNotFoundWithDebugMsg("error ErrRecordNotFound in FetchRewardOfferById dao"),
		}, nil
	}
	if err != nil {
		logger.Error(ctx, "error in FetchRewardOfferById dao", zap.Error(err))
		return &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in FetchRewardOfferById dao"),
		}, nil
	}

	response := &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
		Status: rpc.StatusOk(),
	}

	// if timestampOfMonth is nil then fetch utilization data for actor and reward offer in reward offer period
	if req.GetFilters().GetTimestampOfMonth() == nil {
		// if capping is not set fot this reward offer, then we cannot compute utilization data
		if rewardOffer.GetRewardMeta().GetRewardAggregates().GetRewardUnitsCapUserAggregate() == nil {
			logger.Info(ctx, "capping not applicable to reward offer, cannot get utilization data", zap.String(logger.REWARD_OFFER_ID, req.GetRewardOfferId()))
			response.IsCappingNotConfiguredForRewardOffer = true
		} else {
			rewardUnitsUtilisedForActor, rewardUnitsUtilisedForActorErr := rgs.rewardOfferDao.GetRewardUnitsUtilisedForActor(ctx, req.GetActorId(), req.GetRewardOfferId())
			switch {
			case errors.Is(rewardUnitsUtilisedForActorErr, epifierrors.ErrRecordNotFound):
				logger.Info(ctx, "error ErrRecordNotFound in GetRewardUnitsUtilisedForActor dao", zap.Error(rewardUnitsUtilisedForActorErr), zap.String(logger.REWARD_OFFER_ID, req.GetRewardOfferId()))
				response.RewardOfferRewardUnitsUtilizationForActor = &rewardsPb.GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor{}
			case rewardUnitsUtilisedForActorErr != nil:
				logger.Error(ctx, "error in GetRewardUnitsUtilisedForActor dao", zap.Error(rewardUnitsUtilisedForActorErr), zap.String(logger.REWARD_OFFER_ID, req.GetRewardOfferId()))
				return &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
					Status: rpc.StatusInternalWithDebugMsg("error in GetRewardUnitsUtilisedForActor dao"),
				}, nil
			case rewardUnitsUtilisedForActorErr == nil:
				rewardUnitsUtilisedForActorProto := rewardUnitsUtilisedForActor.GetProto()
				response.RewardOfferRewardUnitsUtilizationForActor = &rewardsPb.GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor{
					FiCoinsUnits: rewardUnitsUtilisedForActorProto.GetFiCoinsUnits(),
					CashUnits:    rewardUnitsUtilisedForActorProto.GetSdCashUnits(),
					SdCashUnits:  rewardUnitsUtilisedForActorProto.GetSdCashUnits(),
				}
			}
		}
	} else { // if date is not nil then fetch utilization data for the month for actor and reward offer

		// if capping is not set fot this reward offer, then we cannot compute utilization data
		if rewardOffer.GetRewardMeta().GetRewardAggregates().GetRewardUnitsCapMonthlyUserAggregate() == nil {
			logger.Info(ctx, "capping not applicable to reward offer, cannot get utilization data", zap.String(logger.REWARD_OFFER_ID, req.GetRewardOfferId()))
			response.IsCappingNotConfiguredForRewardOffer = true
		} else {
			rewardUnitsUtilisedForActorAndOfferInMonth, monthlyRewardUnitsUtilisedForActorAndOfferErr := rgs.rewardDao.GetMonthlyRewardUnitsUtilisedForActorAndOffer(ctx, req.GetActorId(), req.GetRewardOfferId(), req.GetFilters().GetTimestampOfMonth().AsTime())
			switch {
			case errors.Is(monthlyRewardUnitsUtilisedForActorAndOfferErr, epifierrors.ErrRecordNotFound):
				logger.Info(ctx, "error ErrRecordNotFound in GetMonthlyRewardUnitsUtilisedForActorAndOffer dao", zap.Error(monthlyRewardUnitsUtilisedForActorAndOfferErr), zap.String(logger.REWARD_OFFER_ID, req.GetRewardOfferId()))
				response.RewardOfferRewardUnitsUtilizationForActor = &rewardsPb.GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor{}
			case monthlyRewardUnitsUtilisedForActorAndOfferErr != nil:
				logger.Error(ctx, "error in GetMonthlyRewardUnitsUtilisedForActorAndOffer dao", zap.Error(monthlyRewardUnitsUtilisedForActorAndOfferErr), zap.String(logger.REWARD_OFFER_ID, req.GetRewardOfferId()))
				return &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
					Status: rpc.StatusInternalWithDebugMsg("error in GetMonthlyRewardUnitsUtilisedForActorAndOffer dao"),
				}, nil
			case monthlyRewardUnitsUtilisedForActorAndOfferErr == nil:
				response.RewardOfferRewardUnitsUtilizationForActor = &rewardsPb.GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor{
					FiCoinsUnits: rewardUnitsUtilisedForActorAndOfferInMonth.GetFiCoinUnits(),
					CashUnits:    rewardUnitsUtilisedForActorAndOfferInMonth.GetCashUnits(),
					SdCashUnits:  rewardUnitsUtilisedForActorAndOfferInMonth.GetSdCashUnits(),
				}
			}
		}
	}

	rewardsCount, err := rgs.rewardDao.GetRewardsCount(ctx, req.GetActorId(), &daoModel.RewardsCountFilters{
		AndFilter: &daoModel.RewardsCountFilterFields{
			RewardOfferIds: []string{
				req.GetRewardOfferId(),
			},
		},
	})
	if err != nil {
		logger.Error(ctx, "error in GetRewardsCount dao", zap.Error(err), zap.String(logger.REWARD_OFFER_ID, req.GetRewardOfferId()))
		return &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in GetRewardsCount dao"),
		}, nil
	}
	response.TotalRewardsCount = rewardsCount

	return response, nil

}

// BulkClaimRewardsWithDefaultOptionV2 rpc is useful for triggering bulk claim rewards for an actor.
func (rgs *RewardsService) BulkClaimRewardsWithDefaultOptionV2(ctx context.Context, req *rewardsPb.BulkClaimRewardsWithDefaultOptionV2Request) (*rewardsPb.BulkClaimRewardsWithDefaultOptionV2Response, error) {

	// we acquire lock by actor id to prevent RPC call abuse to publish multiple events to queue for a single actor
	if err := rgs.acquireLockForBulkClaimRewards(ctx, rgs.dyconf.BulkClaimRewardsRedisLockDuration(), getBulkClaimRewardsLockKey(req.GetActorId())); err != nil {
		if errors.Is(err, lock.LockAlreadyAcquired) {
			logger.Error(ctx, "actor has already acquired lock", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
			return &rewardsPb.BulkClaimRewardsWithDefaultOptionV2Response{
				Status: rpc.StatusAlreadyExistsWithDebugMsg("actor has already acquired lock"),
			}, nil
		}
		logger.Error(ctx, "error while acquire lock for bulk claims rewards", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &rewardsPb.BulkClaimRewardsWithDefaultOptionV2Response{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	// publish bulk claim rewards event to trigger claiming bulk unclaimed rewards
	bulkClaimRewardsEvent := &events2.BulkClaimRewardsEvent{
		ActorId:        req.GetActorId(),
		EventTimestamp: timestampPb.Now(),
	}
	msgId, err := rgs.bulkClaimRewardsEventPublisher.Publish(ctx, bulkClaimRewardsEvent)
	if err != nil {
		logger.Error(ctx, "error publishing bulk claim rewards event", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &rewardsPb.BulkClaimRewardsWithDefaultOptionV2Response{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	logger.Info(ctx, "bulk claim rewards event published successfully", zap.String(logger.QUEUE_MESSAGE_ID, msgId))

	return &rewardsPb.BulkClaimRewardsWithDefaultOptionV2Response{Status: rpc.StatusOk()}, nil
}

// acquireLockForBulkClaimRewards acquires lock on the provided key for the given duration
// NOTE: this function is not exactly used for locking but will help as a flag.
func (rgs *RewardsService) acquireLockForBulkClaimRewards(ctx context.Context, lockDuration time.Duration, lockKey string) error {

	// check if already key exists in cache storage.
	_, err := rgs.cacheStorage.Get(ctx, lockKey)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			// set the lock as key doesn't exist
			if cacheErr := rgs.cacheStorage.Set(ctx, lockKey, lockKey, lockDuration); cacheErr != nil {
				return fmt.Errorf("error while inserting the bulk claim rewards key in cache, err: %v", cacheErr)
			}
			return nil
		} else {
			return fmt.Errorf("error while fetching the key in cache storage, err: %v", err)
		}
	}

	// if already key exist then return LockAlreadyAcquired.
	return lock.LockAlreadyAcquired
}

func getBulkClaimRewardsLockKey(actorId string) string {
	return rewardConstants.BulkClaimRewardsLockKeyPrefix + actorId
}

func (rgs *RewardsService) GetBulkClaimRewardsProcessingState(ctx context.Context, req *rewardsPb.GetBulkClaimRewardsProcessingStateRequest) (*rewardsPb.GetBulkClaimRewardsProcessingStateResponse, error) {
	// check if already key exists in cache storage.
	_, err := rgs.cacheStorage.Get(ctx, getBulkClaimRewardsLockKey(req.GetActorId()))
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		return &rewardsPb.GetBulkClaimRewardsProcessingStateResponse{
			Status:            rpc.StatusOk(),
			IsProcessingState: false,
		}, nil
	}
	if err != nil {
		logger.Error(ctx, "error while checking bulk rewards processing state", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &rewardsPb.GetBulkClaimRewardsProcessingStateResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while checking bulk rewards processing state"),
		}, nil
	}
	return &rewardsPb.GetBulkClaimRewardsProcessingStateResponse{
		Status:            rpc.StatusOk(),
		IsProcessingState: true,
	}, nil
}

// GetAllRewardsAndProjections returns all rewards, and projections generated for
// given action type and refIds. Please note:
// 1. Only rewards that are in one of PROCESSED, PROCESSING_PENDING, or PROCESSING_IN_PROGRESS are considered
// 2. Chosen options are returned in case of rewards
// 3. For projections, actualised reward is returned (if reward has been actualised)
// 4. If reward hasn't been actualised, projected reward is returned.
func (rgs *RewardsService) GetAllRewardsAndProjections(ctx context.Context, request *rewardsPb.GetAllRewardsAndProjectionRequest) (*rewardsPb.GetAllRewardsAndProjectionResponse, error) {
	// fetch rewards
	rewards, _, err := rgs.rewardDao.FetchPaginatedRewardsByFiltersV2(ctx, request.GetActorId(), &model.QueryRewardsFilterV2{
		AndFilter: &model.AndRewardsFilter{
			RefIds:      request.GetRefIds(),
			ActionTypes: []rewardsPb.CollectedDataType{request.GetActionType()},
			Statuses:    []rewardsPb.RewardStatus{rewardsPb.RewardStatus_PROCESSED, rewardsPb.RewardStatus_PROCESSING_PENDING, rewardsPb.RewardStatus_PROCESSING_IN_PROGRESS},
		},
	}, nil, PageSizeOfGetAllRewardsAndProjectionsRpc)
	if err != nil {
		logger.Error(ctx, "error while fetching rewards", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		return &rewardsPb.GetAllRewardsAndProjectionResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching rewards"),
		}, nil
	}

	// fetch projections
	projections, _, err := rgs.projectionsDao.FetchPaginatedProjectionsByFilters(ctx, &model2.QueryProjectionsFilter{
		AndFilter: &model2.AndProjectionFilter{
			ActorId:     request.GetActorId(),
			RefIds:      request.GetRefIds(),
			ActionTypes: []rewardsPb.CollectedDataType{request.GetActionType()},
		},
	}, nil, PageSizeOfGetAllRewardsAndProjectionsRpc, nil)
	if err != nil {
		logger.Error(ctx, "error while fetching projections", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		return &rewardsPb.GetAllRewardsAndProjectionResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching projections"),
		}, nil
	}

	refIdToRewardEntitiesConcurrentMap := cmap.New()

	updateRewardsErr := updateRewardsInCmap(ctx, rewards, &refIdToRewardEntitiesConcurrentMap)
	if updateRewardsErr != nil {
		logger.Error(ctx, "error while adding rewards to map", zap.Error(updateRewardsErr))
		return &rewardsPb.GetAllRewardsAndProjectionResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while getting rewards for given ref IDs"),
		}, nil
	}
	updateProjectionsErr := rgs.updateProjectionsInCmap(ctx, request.GetActorId(), projections, &refIdToRewardEntitiesConcurrentMap)
	if updateProjectionsErr != nil {
		logger.Error(ctx, "error while adding projections to map", zap.Error(updateProjectionsErr))
		return &rewardsPb.GetAllRewardsAndProjectionResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while getting projections for given ref IDs"),
		}, nil
	}

	// convert concurrent-map to regular map
	refIdToRewardEntitiesMap := map[string]*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities{}
	refIdToRewardEntitiesConcurrentMap.IterCb(func(key string, v interface{}) {
		rwdEntities := v.(*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities)
		refIdToRewardEntitiesMap[key] = rwdEntities
	})

	return &rewardsPb.GetAllRewardsAndProjectionResponse{
		Status:                   rpc.StatusOk(),
		RefIdToRewardEntitiesMap: refIdToRewardEntitiesMap,
	}, nil
}

func updateRewardsInCmap(ctx context.Context, rewards []*model.Reward, refIdToRewardEntitiesConcurrentMap *cmap.ConcurrentMap) error {
	errGrp, _ := errgroup.WithContext(ctx)
	for _, reward := range rewards {
		rwd := reward
		errGrp.Go(func() error {
			var minimalRewardOptionDetails []*rewardsPb.RewardOptionMinimal
			rewardUnits, getRewardUnitsErr := rewardsPb.GetRewardUnitsFromRewardOption(rwd.ChosenReward)
			if getRewardUnitsErr != nil {
				return fmt.Errorf("error while getting reward units from chosen option, err: %w", getRewardUnitsErr)
			}
			minimalRewardOptionDetails = append(minimalRewardOptionDetails, &rewardsPb.RewardOptionMinimal{
				RewardType: rwd.ChosenReward.GetRewardType(),
				Units:      rewardUnits,
			})

			rewardEntity := &rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
				EntityId:         rwd.Id,
				RewardOptions:    minimalRewardOptionDetails,
				OfferType:        rwd.OfferType,
				OfferId:          rwd.OfferId,
				RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD,
			}

			if data, ok := refIdToRewardEntitiesConcurrentMap.Get(rwd.RefId); ok {
				rwdEntities := data.(*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities)
				rwdEntities.RewardEntities = append(rwdEntities.GetRewardEntities(), rewardEntity)
				refIdToRewardEntitiesConcurrentMap.Set(rwd.RefId, rwdEntities)
			} else {
				refIdToRewardEntitiesConcurrentMap.Set(rwd.RefId, &rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities{
					RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{rewardEntity},
				})
			}

			return nil
		})
	}
	grpErr := errGrp.Wait()
	if grpErr != nil {
		return fmt.Errorf("error while getting rewards for given ref IDs, err: %w", grpErr)
	}

	return nil
}

// nolint: funlen
func (rgs *RewardsService) updateProjectionsInCmap(ctx context.Context, actorId string, projections []*rewardsProjectionPb.Projection, refIdToRewardEntitiesConcurrentMap *cmap.ConcurrentMap) error {
	offerIds := lo.Map(projections, func(projection *rewardsProjectionPb.Projection, _ int) string {
		return projection.GetOfferId()
	})
	uniqueOfferIds := lo.Union(offerIds)
	if len(uniqueOfferIds) == 0 {
		return nil
	}

	rewardOffers, rewardOffersErr := rgs.rewardOfferDao.FetchRewardOffersByIds(ctx, uniqueOfferIds)
	if rewardOffersErr != nil {
		logger.Error(ctx, "error in FetchRewardOffersByIds dao", zap.Strings("offerIds", uniqueOfferIds), zap.Error(rewardOffersErr))
		return rewardOffersErr
	}
	offerIdToRewardOfferMap := make(map[string]*rewardOfferPb.RewardOffer, 0)
	for _, rewardOffer := range rewardOffers {
		offerIdToRewardOfferMap[rewardOffer.GetId()] = rewardOffer
	}

	// filter all reward ids in projections
	rewardIds := lo.Union(lo.FilterMap(projections, func(proj *rewardsProjectionPb.Projection, _ int) (string, bool) {
		return proj.GetRewardId(), proj.GetRewardId() != ""
	}))

	// create a map of reward id to reward model
	rewardIdToRewardMap := make(map[string]*model.Reward, 0)
	if len(rewardIds) > 0 {
		rewardsRes, _, err := rgs.rewardDao.FetchPaginatedRewardsByFiltersV2(ctx, actorId, &model.QueryRewardsFilterV2{
			AndFilter: &model.AndRewardsFilter{
				RewardIds: rewardIds,
			},
		}, nil, PageSizeOfGetAllRewardsAndProjectionsRpc)
		if err != nil {
			logger.Error(ctx, "error while fetching FetchPaginatedRewardsByFiltersV2", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return err
		}

		lo.ForEach(rewardsRes, func(reward *model.Reward, _ int) {
			rewardIdToRewardMap[reward.Id] = reward
		})
	}

	errGrp, _ := errgroup.WithContext(ctx)
	for _, projection := range projections {
		prjctn := projection
		errGrp.Go(func() error {
			// Note: Currently for any change in reward offer we are recreating new rewards offer and regenerating projections again against the new offer id.
			// Because of this old projections which are created due to old reward offer are still exists.
			// Should not allow old projections to be return from here, so 2 possible fixes here.
			// 1. [We are using this method] For each projection, check if offer id is active at the time of projection creation. (Whenever we create new reward offer id we mark the old offer id active till to some old date using fixture.)
			// 2. Delete the old projections whenever we regenerate the projections for new offer id.
			isProjectionValid, err := isValidProjection(ctx, prjctn, offerIdToRewardOfferMap)
			if err != nil {
				return err
			}
			if !isProjectionValid {
				return nil
			}

			var minimalRewardOptionDetails []*rewardsPb.RewardOptionMinimal
			projectionOptionsOfInterest := prjctn.GetProjectedOptions()
			rewardEntityType := rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_PROJECTED_REWARD
			if prjctn.GetRewardId() != "" {
				projectionOptionsOfInterest = updateWithUserChosenRewardOptions(prjctn.GetRewardId(), prjctn.GetRewardContributions(), rewardIdToRewardMap)
				rewardEntityType = rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_ACTUALISED_PROJECTED_REWARD
			}

			lo.ForEach(projectionOptionsOfInterest.GetRewardUnitsWithTypes(), func(item *rewardsProjectionPb.RewardOption, _ int) {
				minimalRewardOptionDetails = append(minimalRewardOptionDetails, &rewardsPb.RewardOptionMinimal{
					RewardType: item.GetRewardType(),
					Units:      item.GetRewardUnits(),
				})
			})

			rewardEntity := &rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
				EntityId:         prjctn.GetId(),
				RewardOptions:    minimalRewardOptionDetails,
				OfferType:        prjctn.GetOfferType(),
				OfferId:          prjctn.GetOfferId(),
				RewardEntityType: rewardEntityType,
			}

			// try to set the key if there's no key/value pair present for the key
			successfullySet := refIdToRewardEntitiesConcurrentMap.SetIfAbsent(prjctn.GetRefId(), &rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities{
				RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{rewardEntity},
			})

			// append to the key's list in case the key already existed
			if !successfullySet {
				data, _ := refIdToRewardEntitiesConcurrentMap.Get(prjctn.GetRefId())
				rwdEntities := data.(*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities)
				rwdEntities.RewardEntities = append(rwdEntities.GetRewardEntities(), rewardEntity)
				refIdToRewardEntitiesConcurrentMap.Set(prjctn.GetRefId(), rwdEntities)
			}

			return nil
		})
	}
	grpErr := errGrp.Wait()
	if grpErr != nil {
		return fmt.Errorf("error while getting projections for given ref IDs, err: %w", grpErr)
	}

	return nil
}

// return the reward options chosen by user.
func updateWithUserChosenRewardOptions(rewardId string, rewardContribution *rewardsProjectionPb.OptionsInfo, rewardIdToRewardMap map[string]*model.Reward) *rewardsProjectionPb.OptionsInfo {
	reward, ok := rewardIdToRewardMap[rewardId]
	if ok {
		for _, rewardOption := range rewardContribution.GetRewardUnitsWithTypes() {
			if rewardOption.GetRewardType() == reward.ChosenReward.GetRewardType() {
				return &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{rewardOption}}
			}
		}
	}

	return rewardContribution
}

func isValidProjection(_ context.Context, projection *rewardsProjectionPb.Projection, offerIdToRewardOfferMap map[string]*rewardOfferPb.RewardOffer) (bool, error) {
	rewardOffer, ok := offerIdToRewardOfferMap[projection.GetOfferId()]
	if !ok {
		// if reward offer not exist, may be reward offer might be deleted so return false
		return false, nil
	}

	activeTill, err := time.Parse(time.RFC3339, rewardOffer.GetActiveTill())
	if err != nil {
		return false, fmt.Errorf("error converting active since string to time object, err: %w", err)
	}
	activeSince, err := time.Parse(time.RFC3339, rewardOffer.GetActiveSince())
	if err != nil {
		return false, fmt.Errorf("error converting active till string to time object, err: %w", err)
	}

	// check if the reward offer is active at the time of projection creation.
	if projection.GetCreatedAt().AsTime().Before(activeTill) && projection.GetCreatedAt().AsTime().After(activeSince) {
		return true, nil
	}

	return false, nil
}

// todo: use pinot as data-source for this RPC once ready
// nolint:funlen
func (rgs *RewardsService) GetRewardAggregates(ctx context.Context, req *rewardsPb.GetRewardAggregatesRequest) (*rewardsPb.GetRewardAggregatesResponse, error) {
	if len(req.GetFilter().GetTimeWindows()) == 0 || req.GetActorId() == "" {
		logger.Error(ctx, "mandatory params missing", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Int("timeWindowsLen", len(req.GetFilter().GetTimeWindows())))
		return &rewardsPb.GetRewardAggregatesResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("time windows and actor ID can't be empty")}, nil
	}
	var (
		rewards    []*model.Reward
		pageCtxRes = &rpc.PageContextResponse{}
		pageCtxReq = &rpc.PageContextRequest{}
		pageToken  *pagination.PageToken
		pageSize   = 30
		err        error

		rewardTypeToUnitsMap = make(map[rewardsPb.RewardType]float32)
		optionUnits          float32

		filters = &model.QueryRewardsFilterV2{AndFilter: &model.AndRewardsFilter{
			RewardOfferTypes: req.GetFilter().GetOfferTypes(),
			ActionTypes:      req.GetFilter().GetActionTypes(),
			Statuses:         []rewardsPb.RewardStatus{rewardsPb.RewardStatus_PROCESSED},
		}}
	)
	// we'll make separate calls for each of different time ranges, and club the result together
	// at least one time window is mandatory
	for _, timeWindow := range req.GetFilter().GetTimeWindows() {
		filters.AndFilter.FromTime = timeWindow.GetFromTime().AsTime()
		filters.AndFilter.UptoTime = timeWindow.GetTillTime().AsTime()
		for {
			pageToken, err = pagination.GetPageToken(pageCtxReq)
			if err != nil {
				logger.Error(ctx, "error while getting page token from pageCtxReq", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
				return &rewardsPb.GetRewardAggregatesResponse{
					Status: rpc.StatusInternalWithDebugMsg(err.Error()),
				}, nil
			}
			rewards, pageCtxRes, err = rgs.rewardDao.FetchPaginatedRewardsByFiltersV2(ctx, req.GetActorId(), filters, pageToken, pageSize)
			if err != nil {
				logger.Error(ctx, "unable to fetch rewards by filter V2", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
				return &rewardsPb.GetRewardAggregatesResponse{
					Status: rpc.StatusInternalWithDebugMsg(err.Error()),
				}, nil
			}

			for _, rwd := range rewards {
				optionUnits, err = rewardsPb.GetRewardUnitsFromRewardOption(rwd.ChosenReward)
				if err != nil {
					logger.Error(ctx, "error while getting reward units from reward", zap.String(logger.REWARD_ID, rwd.Id), zap.Error(err))
					return &rewardsPb.GetRewardAggregatesResponse{
						Status: rpc.StatusInternalWithDebugMsg(err.Error()),
					}, nil
				}
				if val, ok := rewardTypeToUnitsMap[rwd.RewardType]; ok {
					rewardTypeToUnitsMap[rwd.RewardType] = val + optionUnits
				} else {
					rewardTypeToUnitsMap[rwd.RewardType] = optionUnits
				}
			}

			if !pageCtxRes.GetHasAfter() {
				break
			}

			pageCtxReq.Token = &rpc.PageContextRequest_AfterToken{AfterToken: pageCtxRes.GetAfterToken()}
		}
	}

	var res []*rewardsPb.RewardOptionMinimal
	for rewardType, rewardUnits := range rewardTypeToUnitsMap {
		res = append(res, &rewardsPb.RewardOptionMinimal{
			RewardType: rewardType,
			Units:      rewardUnits,
		})
	}

	return &rewardsPb.GetRewardAggregatesResponse{
		Status:     rpc.StatusOk(),
		Aggregates: res,
	}, nil
}

// nolint:funlen
func (rgs *RewardsService) GetEstimatedRewardsInTimeDurationForTier(ctx context.Context, request *rewardsPb.GetEstimatedRewardsInTimeDurationForTierRequest) (*rewardsPb.GetEstimatedRewardsInTimeDurationForTierResponse, error) {
	if request.GetTimeWindow().GetFromTime() == nil || request.GetTimeWindow().GetTillTime() == nil {
		return &rewardsPb.GetEstimatedRewardsInTimeDurationForTierResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("from and till time need to be passed"),
		}, nil
	}

	// get net spends for the actor in given time period for UPI
	upiTxnAggregatesRes, err := rgs.payClient.GetTransactionAggregates(ctx, &payPb.GetTransactionAggregatesRequest{
		ActorId:         request.GetActorId(),
		FromTime:        request.GetTimeWindow().GetFromTime(),
		ToTime:          request.GetTimeWindow().GetTillTime(),
		DataFreshness:   payPb.GetTransactionAggregatesRequest_STALE,
		OrderTag:        []orderPb.OrderTag{orderPb.OrderTag_MERCHANT},
		PaymentProtocol: []paymentPb.PaymentProtocol{paymentPb.PaymentProtocol_UPI},
	})
	if rpcErr := epifigrpc.RPCError(upiTxnAggregatesRes, err); rpcErr != nil {
		logger.Error(ctx, "failed to fetch UPI aggregates for actor", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Time(logger.START_TIME, request.GetTimeWindow().GetFromTime().AsTime()), zap.Time(logger.END_TIME, request.GetTimeWindow().GetTillTime().AsTime()), zap.Error(rpcErr))
		return &rewardsPb.GetEstimatedRewardsInTimeDurationForTierResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch UPI txn aggregates"),
		}, nil
	}

	// get net spends for the actor in given time period for debit card transactions
	cardTxnAggregatesRes, err := rgs.payClient.GetTransactionAggregates(ctx, &payPb.GetTransactionAggregatesRequest{
		ActorId:         request.GetActorId(),
		FromTime:        request.GetTimeWindow().GetFromTime(),
		ToTime:          request.GetTimeWindow().GetTillTime(),
		DataFreshness:   payPb.GetTransactionAggregatesRequest_STALE,
		OrderTag:        []orderPb.OrderTag{orderPb.OrderTag_MERCHANT},
		PaymentProtocol: []paymentPb.PaymentProtocol{paymentPb.PaymentProtocol_UPI},
	})
	if rpcErr := epifigrpc.RPCError(cardTxnAggregatesRes, err); rpcErr != nil {
		logger.Error(ctx, "failed to fetch debit card aggregates for actor", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Time(logger.START_TIME, request.GetTimeWindow().GetFromTime().AsTime()), zap.Time(logger.END_TIME, request.GetTimeWindow().GetTillTime().AsTime()), zap.Error(rpcErr))
		return &rewardsPb.GetEstimatedRewardsInTimeDurationForTierResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch debit card txn aggregates"),
		}, nil
	}

	// get estimated fi coin rewards for UPI spends
	estimatedUpiFiCoinUnits := pkgRewards.BeTierToUpiSpendsToRewardUnitsFactorMap[pkgRewards.RewardsUserTierToBETierMap[request.GetUserTier()]] * float64(upiTxnAggregatesRes.GetTransactionAggregates().GetSumAmount().GetUnits())
	cappedEstimatedUpiFiCoinUnits := uint32(math.Min(float64(pkgRewards.BeTierToMaxFiCoinsAnchorRewardsForUpiSpendsCapMap[pkgRewards.RewardsUserTierToBETierMap[request.GetUserTier()]]), estimatedUpiFiCoinUnits))

	// get estimated fi coin rewards for debit card spends
	estimatedCardFiCoinUnits := pkgRewards.BeTierToCardSpendsToRewardUnitsFactorMap[pkgRewards.RewardsUserTierToBETierMap[request.GetUserTier()]] * float64(cardTxnAggregatesRes.GetTransactionAggregates().GetSumAmount().GetUnits())
	cappedEstimatedCardFiCoinUnits := uint32(math.Min(float64(pkgRewards.BeTierToMaxFiCoinsAnchorRewardsForCardSpendsCapMap[pkgRewards.RewardsUserTierToBETierMap[request.GetUserTier()]]), estimatedCardFiCoinUnits))

	// get estimated cashback reward
	estimatedCashbackUnits := pkgRewards.BeTierToCashbackFactorMap[pkgRewards.RewardsUserTierToBETierMap[request.GetUserTier()]] * float64((upiTxnAggregatesRes.GetTransactionAggregates().GetSumAmount().GetUnits())+cardTxnAggregatesRes.GetTransactionAggregates().GetSumAmount().GetUnits())
	cappedEstimatedCashback := float32(math.Min(float64(pkgRewards.BeTierToCashbackMonthlyCapMap[pkgRewards.RewardsUserTierToBETierMap[request.GetUserTier()]]), estimatedCashbackUnits))

	return &rewardsPb.GetEstimatedRewardsInTimeDurationForTierResponse{
		Status:              rpc.StatusOk(),
		AnchorFiCoinRewards: cappedEstimatedCardFiCoinUnits + cappedEstimatedUpiFiCoinUnits,
		CashbackReward:      cappedEstimatedCashback,
	}, nil

}

// ForceRetryRewardProcessing rpc is useful for initiating force retry for a reward processing.
// This rpc is useful in cases where reward processing failed or manual intervention is required.
// This rpc will reset the reward processing state and push the reward to processing queue.
func (rgs *RewardsService) ForceRetryRewardProcessing(ctx context.Context, request *rewardsPb.ForceRetryRewardProcessingRequest) (*rewardsPb.ForceRetryRewardProcessingResponse, error) {
	if request.GetRewardId() == "" {
		return &rewardsPb.ForceRetryRewardProcessingResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("rewardId is required")}, nil
	}

	// get reward proto by reward id
	reward, err := rgs.getRewardById(ctx, request.GetRewardId())
	if err != nil {
		logger.Error(ctx, "error while fetching reward by ID", zap.String(logger.REWARD_ID, request.GetRewardId()), zap.Error(err))
		return &rewardsPb.ForceRetryRewardProcessingResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// validate reward statuses for force retry
	validRewardStatusesForForceRetry := []rewardsPb.RewardStatus{
		rewardsPb.RewardStatus_PROCESSING_MANUAL_INTERVENTION,
		rewardsPb.RewardStatus_PROCESSING_FAILED,
		rewardsPb.RewardStatus_PROCESSING_PENDING}
	if !lo.Contains(validRewardStatusesForForceRetry, reward.GetStatus()) {
		return &rewardsPb.ForceRetryRewardProcessingResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("reward status is not supported for force retry")}, nil
	}

	// reset processing status for required reward statuses
	switch reward.GetStatus() {
	case rewardsPb.RewardStatus_PROCESSING_MANUAL_INTERVENTION, rewardsPb.RewardStatus_PROCESSING_FAILED:
		if err = rgs.rewardDao.ResetRewardProcessingStateById(ctx, request.GetRewardId()); err != nil {
			logger.Error(ctx, "error while updating reward state for initiating force retry", zap.String(logger.REWARD_ID, request.GetRewardId()), zap.Error(err))
			return &rewardsPb.ForceRetryRewardProcessingResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
	default:
		// do nothing
	}

	// get updated reward proto by reward id
	reward, err = rgs.getRewardById(ctx, request.GetRewardId())
	if err != nil {
		logger.Error(ctx, "error while fetching reward by ID", zap.String(logger.REWARD_ID, request.GetRewardId()), zap.Error(err))
		return &rewardsPb.ForceRetryRewardProcessingResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// push reward to processing queue
	if _, err := rgs.rewardProcessingPublisher.Publish(ctx, &rewardsPb.RewardEvent{Reward: reward}); err != nil {
		logger.Error(ctx, "error push reward to processing queue", zap.String(logger.REWARD_ID, reward.GetId()), zap.Error(err))
		return &rewardsPb.ForceRetryRewardProcessingResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	logger.Info(ctx, "reward retry processing event published successfully", zap.String(logger.REWARD_ID, reward.GetId()))
	return &rewardsPb.ForceRetryRewardProcessingResponse{Status: rpc.StatusOk()}, nil
}

func (rgs *RewardsService) getRewardById(ctx context.Context, rewardId string) (*rewardsPb.Reward, error) {
	rewardModel, err := rgs.rewardDao.FetchRewardById(ctx, rewardId)
	if err != nil {
		logger.Error(ctx, "error while fetching reward by ID", zap.String(logger.REWARD_ID, rewardId), zap.Error(err))
		return nil, fmt.Errorf("error while fetching reward by ID, err: %w", err)
	}
	reward, err := rewardModel.GetProtoReward()
	if err != nil {
		logger.Error(ctx, "error while converting reward model to proto", zap.String(logger.REWARD_ID, rewardId), zap.Error(err))
		return nil, fmt.Errorf("error while converting reward model to proto, err: %w", err)
	}
	return reward, nil
}
