package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
	"github.com/jonboulle/clockwork"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	mockCache "github.com/epifi/be-common/pkg/cache/mocks"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	eventMocks "github.com/epifi/be-common/pkg/events/mocks"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	mock_queue "github.com/epifi/be-common/pkg/queue/mocks"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	clawbackDaoModel "github.com/epifi/gamma/rewards/clawback/dao/model"
	"github.com/epifi/gamma/rewards/config"
	"github.com/epifi/gamma/rewards/config/genconf"
	model2 "github.com/epifi/gamma/rewards/generator/dao/model"
	"github.com/epifi/gamma/rewards/generator/model"
	"github.com/epifi/gamma/rewards/helper/rewardamountcalculator"
	model3 "github.com/epifi/gamma/rewards/projector/dao/model"
	clawbackDaoMock "github.com/epifi/gamma/rewards/test/mocks/clawback/dao"
	daoMock "github.com/epifi/gamma/rewards/test/mocks/generator/dao"
	rewardamtcalculatorMocks "github.com/epifi/gamma/rewards/test/mocks/helper/rewardamountcalculator"
	projectorDaoMock "github.com/epifi/gamma/rewards/test/mocks/projector/dao"
)

func TestRewardsGeneratorService_GetRewardsByActorId(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockRewardsDao := daoMock.NewMockRewardsDao(ctr)
	brokerMock := eventMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockPublisher := mock_queue.NewMockPublisher(ctr)
	mockDelayPublisher := mock_queue.NewMockDelayPublisher(ctr)
	rgs := &RewardsService{
		rewardDao:                      mockRewardsDao,
		rewardProcessingPublisher:      mockPublisher,
		rewardProcessingDelayPublisher: mockDelayPublisher,
		eventsBroker:                   brokerMock,
	}

	pageContext := &rpc.PageContextRequest{
		PageSize: 100,
	}
	req := &rewardsPb.RewardsByActorIdRequest{
		ActorId:     "act-1",
		Filter:      &rewardsPb.RewardsByActorIdRequest_Filter{RewardId: "reward-1"},
		PageContext: pageContext,
	}

	// we can't use time.Now() as this will change the tokens in the response for each run and can't be static for comparision
	createdAtTime := time.Unix(1602571742, 0)
	createdAtTimestamp := timestamppb.New(createdAtTime)

	t.Run("Get Multiple rewards for actor", func(t *testing.T) {

		var rws []*model.Reward

		reward1 := &model.Reward{
			Id:      "rw-1",
			RefId:   "cd-1",
			ActorId: "act-1",
			OfferId: "offer-1",
			Status:  0,
			Aggregates: &rewardOffersPb.RewardAggregates{
				UserAggregate:   2,
				ActionAggregate: 2,
			},
			RewardOptions: &rewardsPb.RewardOptions{
				Options: []*rewardsPb.RewardOption{
					{
						Option: &rewardsPb.RewardOption_Cash{
							Cash: &rewardsPb.Cash{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        10,
								},
							},
						},
					}, {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 0,
								ExpiresAt: &timestamppb.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
					},
				},
			},
			ChosenReward: &rewardsPb.RewardOption{
				Option: &rewardsPb.RewardOption_Cash{
					Cash: &rewardsPb.Cash{
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        10,
						},
					},
				},
			},
			ExpiresAt:       createdAtTime,
			CreatedAt:       createdAtTime,
			ActionTimestamp: createdAtTime,
			UpdatedAt:       createdAtTime,
		}
		reward2 := &model.Reward{
			Id:      "rw-2",
			RefId:   "cd-2",
			ActorId: "act-1",
			OfferId: "offer-1",
			Status:  0,
			Aggregates: &rewardOffersPb.RewardAggregates{
				UserAggregate:   2,
				ActionAggregate: 2,
			},
			RewardOptions: &rewardsPb.RewardOptions{
				Options: []*rewardsPb.RewardOption{
					{
						Option: &rewardsPb.RewardOption_Cash{
							Cash: &rewardsPb.Cash{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        10,
								},
							},
						},
					}, {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 0,
								ExpiresAt: &timestamppb.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
					},
				},
			},
			ChosenReward: &rewardsPb.RewardOption{
				Option: &rewardsPb.RewardOption_FiCoins{
					FiCoins: &rewardsPb.FiCoins{
						Units: 0,
						ExpiresAt: &timestamppb.Timestamp{
							Seconds: 123123,
							Nanos:   0,
						},
					},
				},
			},
			ExpiresAt:       createdAtTime,
			CreatedAt:       createdAtTime,
			ActionTimestamp: createdAtTime,
			UpdatedAt:       createdAtTime,
		}

		rws = append(rws, reward1, reward2)

		pbCashReward := &rewardsPb.Reward{
			Id:        "rw-1",
			RefId:     "cd-1",
			ActorId:   "act-1",
			OfferId:   "offer-1",
			Status:    0,
			SubStatus: "SUB_STATUS_UNSPECIFIED",
			RewardOptions: &rewardsPb.RewardOptions{
				Options: []*rewardsPb.RewardOption{
					{
						Option: &rewardsPb.RewardOption_Cash{
							Cash: &rewardsPb.Cash{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        10,
								},
							},
						},
					}, {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 0,
								ExpiresAt: &timestamppb.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
					},
				},
			},
			ChosenReward: &rewardsPb.RewardOption{
				Option: &rewardsPb.RewardOption_Cash{
					Cash: &rewardsPb.Cash{
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        10,
						},
					},
				},
			},
			ExpiresAt:  createdAtTimestamp,
			CreatedAt:  createdAtTimestamp,
			ActionTime: createdAtTimestamp,
			UpdatedAt:  createdAtTimestamp,
		}

		pbLpReward := &rewardsPb.Reward{
			Id:        "rw-2",
			RefId:     "cd-2",
			ActorId:   "act-1",
			OfferId:   "offer-1",
			Status:    0,
			SubStatus: "SUB_STATUS_UNSPECIFIED",
			RewardOptions: &rewardsPb.RewardOptions{
				Options: []*rewardsPb.RewardOption{
					{
						Option: &rewardsPb.RewardOption_Cash{
							Cash: &rewardsPb.Cash{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        10,
								},
							},
						},
					}, {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 0,
								ExpiresAt: &timestamppb.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
					},
				},
			},
			ChosenReward: &rewardsPb.RewardOption{
				Option: &rewardsPb.RewardOption_FiCoins{
					FiCoins: &rewardsPb.FiCoins{
						Units: 0,
						ExpiresAt: &timestamppb.Timestamp{
							Seconds: 123123,
							Nanos:   0,
						},
					},
				},
			},
			ExpiresAt:  createdAtTimestamp,
			CreatedAt:  createdAtTimestamp,
			ActionTime: createdAtTimestamp,
			UpdatedAt:  createdAtTimestamp,
		}

		var pbrArr []*rewardsPb.Reward

		pbrArr = append(pbrArr, pbCashReward, pbLpReward)

		expRes := &rewardsPb.RewardsResponse{
			Status:  rpc.StatusOk(),
			Rewards: pbrArr,
			PageContext: &rpc.PageContextResponse{
				BeforeToken: "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTYwMjU3MTc0Mn0sIk9mZnNldCI6MiwiSXNSZXZlcnNlIjp0cnVlfQ==",
				AfterToken:  "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTYwMjU3MTc0Mn0sIk9mZnNldCI6MiwiSXNSZXZlcnNlIjpmYWxzZX0=",
			},
		}

		mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(context.Background(), gomock.Any(), gomock.Any()).Return(rws, &rpc.PageContextResponse{
			BeforeToken: "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTYwMjU3MTc0Mn0sIk9mZnNldCI6MiwiSXNSZXZlcnNlIjp0cnVlfQ==",
			AfterToken:  "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTYwMjU3MTc0Mn0sIk9mZnNldCI6MiwiSXNSZXZlcnNlIjpmYWxzZX0=",
		}, nil)

		res, err := rgs.GetRewardsByActorId(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, expRes, res)
	})

	t.Run("No reward for actor", func(t *testing.T) {

		expRes := &rewardsPb.RewardsResponse{
			Status:  rpc.StatusOk(),
			Rewards: nil,
		}

		mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(context.Background(), gomock.Any(), gomock.Any()).Return(nil, nil, nil)

		res, err := rgs.GetRewardsByActorId(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, expRes, res)
	})

	t.Run("reward fetch from db failed", func(t *testing.T) {

		mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(context.Background(), gomock.Any(), gomock.Any()).Return(nil, nil, fmt.Errorf("reward fetch failed from db"))

		expRes := &rewardsPb.RewardsResponse{
			Status: rpc.StatusInternalWithDebugMsg("reward fetch failed from db"),
		}

		res, err := rgs.GetRewardsByActorId(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, expRes, res)
	})

	t.Run("should return rewards filtered by action_types if they exist", func(t *testing.T) {

		reqWithActionTypeFilter := &rewardsPb.RewardsByActorIdRequest{
			ActorId:     "actorId",
			Filter:      &rewardsPb.RewardsByActorIdRequest_Filter{ActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_ORDER}},
			PageContext: pageContext,
		}

		resWithActionTypeFilter := &rewardsPb.RewardsResponse{Rewards: []*rewardsPb.Reward{{Id: "rewardId"}}, Status: rpc.StatusOk()}

		mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(context.Background(), &model.QueryRewardsFilter{ActorId: "actorId", ActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_ORDER}, PageSize: 100}, gomock.Any()).Return([]*model.Reward{{Id: "rewardId"}}, nil, nil)

		res, err := rgs.GetRewardsByActorId(context.Background(), reqWithActionTypeFilter)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, resWithActionTypeFilter.GetStatus(), res.GetStatus())
		assert.Equal(t, len(resWithActionTypeFilter.GetRewards()), len(res.GetRewards()))
		assert.Equal(t, resWithActionTypeFilter.GetRewards()[0].GetId(), res.GetRewards()[0].GetId())
	})

	t.Run("should return rewards when 'and' filters_v2 are passed in input", func(t *testing.T) {
		reqWithActionTypeFilter := &rewardsPb.RewardsByActorIdRequest{
			ActorId: "actorId",
			FiltersV2: &rewardsPb.RewardsByActorIdRequest_FiltersV2{
				AndFilter: &rewardsPb.RewardsByActorIdRequest_Filter{
					ActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_ORDER},
				},
			},
			PageContext: pageContext,
		}

		resWithActionTypeFilter := &rewardsPb.RewardsResponse{Rewards: []*rewardsPb.Reward{{Id: "rewardId"}}, Status: rpc.StatusOk()}

		mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(context.Background(), "actorId", &model.QueryRewardsFilterV2{AndFilter: &model.AndRewardsFilter{ActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_ORDER}}}, gomock.Any(), 100).Return([]*model.Reward{{Id: "rewardId"}}, nil, nil)

		res, err := rgs.GetRewardsByActorId(context.Background(), reqWithActionTypeFilter)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, resWithActionTypeFilter.GetStatus(), res.GetStatus())
		assert.Equal(t, len(resWithActionTypeFilter.GetRewards()), len(res.GetRewards()))
		assert.Equal(t, resWithActionTypeFilter.GetRewards()[0].GetId(), res.GetRewards()[0].GetId())
	})

	t.Run("should return rewards when 'or' filters_v2 are passed in input", func(t *testing.T) {
		reqWithActionTypeFilter := &rewardsPb.RewardsByActorIdRequest{
			ActorId: "actorId",
			FiltersV2: &rewardsPb.RewardsByActorIdRequest_FiltersV2{
				OrFilter: &rewardsPb.RewardsByActorIdRequest_Filter{
					ClaimType: rewardsPb.ClaimType_CLAIM_TYPE_AUTOMATIC,
				},
			},
			PageContext: pageContext,
		}

		resWithActionTypeFilter := &rewardsPb.RewardsResponse{Rewards: []*rewardsPb.Reward{{Id: "rewardId"}}, Status: rpc.StatusOk()}

		mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(context.Background(), "actorId", &model.QueryRewardsFilterV2{OrFilter: &model.OrRewardsFilter{ClaimType: rewardsPb.ClaimType_CLAIM_TYPE_AUTOMATIC}}, gomock.Any(), 100).Return([]*model.Reward{{Id: "rewardId"}}, nil, nil)

		res, err := rgs.GetRewardsByActorId(context.Background(), reqWithActionTypeFilter)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, resWithActionTypeFilter.GetStatus(), res.GetStatus())
		assert.Equal(t, len(resWithActionTypeFilter.GetRewards()), len(res.GetRewards()))
		assert.Equal(t, resWithActionTypeFilter.GetRewards()[0].GetId(), res.GetRewards()[0].GetId())
	})

	t.Run("should return rewards when both 'and' and 'or' filters_v2 are passed in input", func(t *testing.T) {
		reqWithActionTypeFilter := &rewardsPb.RewardsByActorIdRequest{
			ActorId: "actorId",
			FiltersV2: &rewardsPb.RewardsByActorIdRequest_FiltersV2{
				OrFilter: &rewardsPb.RewardsByActorIdRequest_Filter{
					ClaimType: rewardsPb.ClaimType_CLAIM_TYPE_AUTOMATIC,
				},
				AndFilter: &rewardsPb.RewardsByActorIdRequest_Filter{
					ActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_ORDER},
				},
			},
			PageContext: pageContext,
		}

		resWithActionTypeFilter := &rewardsPb.RewardsResponse{Rewards: []*rewardsPb.Reward{{Id: "rewardId"}}, Status: rpc.StatusOk()}

		mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(context.Background(), "actorId", &model.QueryRewardsFilterV2{OrFilter: &model.OrRewardsFilter{ClaimType: rewardsPb.ClaimType_CLAIM_TYPE_AUTOMATIC}, AndFilter: &model.AndRewardsFilter{ActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_ORDER}}}, gomock.Any(), 100).Return([]*model.Reward{{Id: "rewardId"}}, nil, nil)

		res, err := rgs.GetRewardsByActorId(context.Background(), reqWithActionTypeFilter)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, resWithActionTypeFilter.GetStatus(), res.GetStatus())
		assert.Equal(t, len(resWithActionTypeFilter.GetRewards()), len(res.GetRewards()))
		assert.Equal(t, resWithActionTypeFilter.GetRewards()[0].GetId(), res.GetRewards()[0].GetId())
	})
}

func TestRewardsGeneratorService_GetRewardsByRewardId(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockRewardsDao := daoMock.NewMockRewardsDao(ctr)
	brokerMock := eventMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockPublisher := mock_queue.NewMockPublisher(ctr)
	mockDelayPublisher := mock_queue.NewMockDelayPublisher(ctr)

	rgs := &RewardsService{
		rewardDao:                      mockRewardsDao,
		rewardProcessingPublisher:      mockPublisher,
		rewardProcessingDelayPublisher: mockDelayPublisher,
		eventsBroker:                   brokerMock,
	}

	req := &rewardsPb.RewardsByRewardIdRequest{
		RewardId: "rw-1",
	}

	createdAtTime := time.Now()
	createdAtTimestamp := timestamppb.New(createdAtTime)

	t.Run("Get reward for id", func(t *testing.T) {

		reward1 := &model.Reward{
			Id:      "rw-1",
			RefId:   "cd-1",
			ActorId: "act-1",
			OfferId: "offer-1",
			Status:  0,
			Aggregates: &rewardOffersPb.RewardAggregates{
				UserAggregate:   2,
				ActionAggregate: 2,
			},
			RewardOptions: &rewardsPb.RewardOptions{
				Options: []*rewardsPb.RewardOption{
					{
						Option: &rewardsPb.RewardOption_Cash{
							Cash: &rewardsPb.Cash{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        10,
								},
							},
						},
					}, {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 0,
								ExpiresAt: &timestamppb.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
					},
				},
			},
			ChosenReward: &rewardsPb.RewardOption{
				Option: &rewardsPb.RewardOption_Cash{
					Cash: &rewardsPb.Cash{
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        10,
						},
					},
				},
			},
			ExpiresAt:       createdAtTime,
			CreatedAt:       createdAtTime,
			ActionTimestamp: createdAtTime,
			UpdatedAt:       createdAtTime,
		}

		pbReward1 := &rewardsPb.Reward{
			Id:        "rw-1",
			RefId:     "cd-1",
			ActorId:   "act-1",
			OfferId:   "offer-1",
			Status:    0,
			SubStatus: "SUB_STATUS_UNSPECIFIED",
			RewardOptions: &rewardsPb.RewardOptions{
				Options: []*rewardsPb.RewardOption{
					{
						Option: &rewardsPb.RewardOption_Cash{
							Cash: &rewardsPb.Cash{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        10,
								},
							},
						},
					}, {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 0,
								ExpiresAt: &timestamppb.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
					},
				},
			},
			ChosenReward: &rewardsPb.RewardOption{
				Option: &rewardsPb.RewardOption_Cash{
					Cash: &rewardsPb.Cash{
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        10,
						},
					},
				},
			},
			ExpiresAt:  createdAtTimestamp,
			CreatedAt:  createdAtTimestamp,
			ActionTime: createdAtTimestamp,
			UpdatedAt:  createdAtTimestamp,
		}

		expRes := &rewardsPb.RewardResponse{
			Status: rpc.StatusOk(),
			Reward: pbReward1,
		}

		mockRewardsDao.EXPECT().FetchRewardById(context.Background(), gomock.Any()).Return(reward1, nil)

		res, err := rgs.GetRewardsByRewardId(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, expRes, res)
	})

	t.Run("reward fetch from db failed", func(t *testing.T) {

		mockRewardsDao.EXPECT().FetchRewardById(context.Background(), gomock.Any()).Return(nil, fmt.Errorf("reward fetch failed from db"))

		expRes := &rewardsPb.RewardResponse{
			Status: rpc.StatusInternalWithDebugMsg("reward fetch failed from db"),
		}

		res, err := rgs.GetRewardsByRewardId(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, expRes, res)
	})
}

func TestRewardsService_GetRewards(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockRewardsDao := daoMock.NewMockRewardsDao(ctr)
	brokerMock := eventMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockPublisher := mock_queue.NewMockPublisher(ctr)
	mockDelayPublisher := mock_queue.NewMockDelayPublisher(ctr)

	rgs := &RewardsService{
		rewardDao:                      mockRewardsDao,
		rewardProcessingPublisher:      mockPublisher,
		rewardProcessingDelayPublisher: mockDelayPublisher,
		eventsBroker:                   brokerMock,
	}

	createdAtTime := time.Now()
	createdAtTimestamp := timestamppb.New(createdAtTime)

	getReward := func() *model.Reward {
		return &model.Reward{
			Id:      "rw-1",
			RefId:   "cd-1",
			ActorId: "act-1",
			OfferId: "offer-1",
			Status:  0,
			Aggregates: &rewardOffersPb.RewardAggregates{
				UserAggregate:   2,
				ActionAggregate: 2,
			},
			RewardOptions: &rewardsPb.RewardOptions{
				Options: []*rewardsPb.RewardOption{
					{
						Option: &rewardsPb.RewardOption_Cash{
							Cash: &rewardsPb.Cash{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        10,
								},
							},
						},
					}, {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 0,
								ExpiresAt: &timestamppb.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
					},
				},
			},
			ChosenReward: &rewardsPb.RewardOption{
				Option: &rewardsPb.RewardOption_Cash{
					Cash: &rewardsPb.Cash{
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        10,
						},
					},
				},
			},
			ExpiresAt:       createdAtTime,
			CreatedAt:       createdAtTime,
			ActionTimestamp: createdAtTime,
			UpdatedAt:       createdAtTime,
		}
	}

	getPbReward := func() *rewardsPb.Reward {
		return &rewardsPb.Reward{
			Id:        "rw-1",
			RefId:     "cd-1",
			ActorId:   "act-1",
			OfferId:   "offer-1",
			Status:    0,
			SubStatus: "SUB_STATUS_UNSPECIFIED",
			RewardOptions: &rewardsPb.RewardOptions{
				Options: []*rewardsPb.RewardOption{
					{
						Option: &rewardsPb.RewardOption_Cash{
							Cash: &rewardsPb.Cash{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        10,
								},
							},
						},
					}, {
						Option: &rewardsPb.RewardOption_FiCoins{
							FiCoins: &rewardsPb.FiCoins{
								Units: 0,
								ExpiresAt: &timestamppb.Timestamp{
									Seconds: 123123,
									Nanos:   0,
								},
							},
						},
					},
				},
			},
			ChosenReward: &rewardsPb.RewardOption{
				Option: &rewardsPb.RewardOption_Cash{
					Cash: &rewardsPb.Cash{
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        10,
						},
					},
				},
			},
			ExpiresAt:  createdAtTimestamp,
			CreatedAt:  createdAtTimestamp,
			ActionTime: createdAtTimestamp,
			UpdatedAt:  createdAtTimestamp,
		}
	}

	const rewardId = "rw-1"
	const externalRefId = "ex-ref-1"

	t.Run("With RewardOfferType", func(t *testing.T) {
		t.Run("Get rewards by ids", func(t *testing.T) {
			reward := getReward()
			reward.OfferType = rewardsPb.RewardOfferType_REFERRAL_REFEREE_OFFER

			pbReward := getPbReward()
			pbReward.OfferType = rewardsPb.RewardOfferType_REFERRAL_REFEREE_OFFER

			filter := &model.QueryRewardsFilter{
				RewardIds:        []string{rewardId},
				RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFEREE_OFFER},
			}

			req := &rewardsPb.GetRewardsRequest{
				Ids:             []string{rewardId},
				RewardOfferType: rewardsPb.RewardOfferType_REFERRAL_REFEREE_OFFER,
			}
			expRes := &rewardsPb.GetRewardsResponse{
				Status:  rpc.StatusOk(),
				Rewards: []*rewardsPb.Reward{pbReward},
			}

			mockRewardsDao.EXPECT().FetchRewards(context.Background(), filter).Return([]*model.Reward{reward}, nil)

			res, err := rgs.GetRewards(context.Background(), req)
			assert.Nil(t, err)
			assert.NotNil(t, res)
			assert.Equal(t, expRes, res)
		})

		t.Run("Get rewards by external-ref-ids", func(t *testing.T) {
			reward := getReward()
			reward.OfferType = rewardsPb.RewardOfferType_REFERRAL_REFEREE_OFFER
			reward.ExternalRef = externalRefId

			pbReward := getPbReward()
			pbReward.OfferType = rewardsPb.RewardOfferType_REFERRAL_REFEREE_OFFER
			pbReward.ExternalRef = externalRefId

			filter := &model.QueryRewardsFilter{
				ExternalRefIds:   []string{externalRefId},
				RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFEREE_OFFER},
			}

			req := &rewardsPb.GetRewardsRequest{
				ExternalRefIds:  []string{externalRefId},
				RewardOfferType: rewardsPb.RewardOfferType_REFERRAL_REFEREE_OFFER,
			}
			expRes := &rewardsPb.GetRewardsResponse{
				Status:  rpc.StatusOk(),
				Rewards: []*rewardsPb.Reward{pbReward},
			}

			mockRewardsDao.EXPECT().FetchRewards(context.Background(), filter).Return([]*model.Reward{reward}, nil)

			res, err := rgs.GetRewards(context.Background(), req)
			assert.Nil(t, err)
			assert.NotNil(t, res)
			assert.Equal(t, expRes, res)
		})
	})

	t.Run("Without RewardOfferType - Defaults to Unspecified-Reward-Type", func(t *testing.T) {
		t.Run("Get rewards by ids", func(t *testing.T) {
			reward := getReward()
			pbReward := getPbReward()

			filter := &model.QueryRewardsFilter{
				RewardIds: []string{rewardId},
			}

			req := &rewardsPb.GetRewardsRequest{
				Ids: []string{rewardId},
			}
			expRes := &rewardsPb.GetRewardsResponse{
				Status:  rpc.StatusOk(),
				Rewards: []*rewardsPb.Reward{pbReward},
			}

			mockRewardsDao.EXPECT().FetchRewards(context.Background(), filter).Return([]*model.Reward{reward}, nil)

			res, err := rgs.GetRewards(context.Background(), req)
			assert.Nil(t, err)
			assert.NotNil(t, res)
			assert.Equal(t, expRes, res)
		})

		t.Run("Get rewards by external-ref-ids", func(t *testing.T) {
			reward := getReward()
			reward.ExternalRef = externalRefId

			pbReward := getPbReward()
			pbReward.ExternalRef = externalRefId

			filter := &model.QueryRewardsFilter{
				ExternalRefIds: []string{externalRefId},
			}

			req := &rewardsPb.GetRewardsRequest{
				ExternalRefIds: []string{externalRefId},
			}
			expRes := &rewardsPb.GetRewardsResponse{
				Status:  rpc.StatusOk(),
				Rewards: []*rewardsPb.Reward{pbReward},
			}

			mockRewardsDao.EXPECT().FetchRewards(context.Background(), filter).Return([]*model.Reward{reward}, nil)

			res, err := rgs.GetRewards(context.Background(), req)
			assert.Nil(t, err)
			assert.NotNil(t, res)
			assert.Equal(t, expRes, res)
		})
	})

	t.Run("Rewards failed to fetch from DB", func(t *testing.T) {
		mockRewardsDao.EXPECT().FetchRewards(context.Background(), gomock.Any()).Return(nil, fmt.Errorf("reward fetch failed from db"))

		req := &rewardsPb.GetRewardsRequest{
			Ids:             []string{rewardId},
			RewardOfferType: rewardsPb.RewardOfferType_REFERRAL_REFEREE_OFFER,
		}
		expRes := &rewardsPb.GetRewardsResponse{
			Status: rpc.StatusInternalWithDebugMsg("reward fetch failed from db"),
		}

		res, err := rgs.GetRewards(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, expRes, res)
	})

}

func TestRewardsService_ChooseReward(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockRewardsDao := daoMock.NewMockRewardsDao(ctr)
	brokerMock := eventMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockPublisher := mock_queue.NewMockPublisher(ctr)
	mockDelayPublisher := mock_queue.NewMockDelayPublisher(ctr)

	rgs := &RewardsService{
		rewardDao:                      mockRewardsDao,
		rewardProcessingPublisher:      mockPublisher,
		rewardProcessingDelayPublisher: mockDelayPublisher,
		eventsBroker:                   brokerMock,
	}

	req := &rewardsPb.ChooseRewardRequest{
		RewardId:       "RW200911WjgI+xGYRY+0PhNl96WmYA==",
		RewardOptionId: "option_id_1",
	}
	reqWithInvalidOption := &rewardsPb.ChooseRewardRequest{
		RewardId:       "RW200911WjgI+xGYRY+0PhNl96WmYA==",
		RewardOptionId: "invalid_option_id",
	}
	reqForDelayedProcessing := &rewardsPb.ChooseRewardRequest{
		RewardId:       "RW200911WjgI+xGYRY+0PhNl96WmYA==",
		RewardOptionId: "option_id_2",
	}
	modelReward := &model.Reward{
		Id:         "RW200911WjgI+xGYRY+0PhNl96WmYA==",
		RewardType: 0,
		Status:     0,
		Aggregates: nil,
		RewardOptions: &rewardsPb.RewardOptions{
			Options: []*rewardsPb.RewardOption{
				{
					Id: "option_id_1",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{AbsoluteTime: timestamppb.Now()},
					},
					Option: &rewardsPb.RewardOption_Cash{},
				},
				{
					Id: "option_id_2",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 5},
					},
					Option: &rewardsPb.RewardOption_Cash{},
				},
			},
		},
		ChosenReward: nil,
	}
	type args struct {
		ctx context.Context
		req *rewardsPb.ChooseRewardRequest
	}
	tests := []struct {
		name    string
		mocks   []interface{}
		args    args
		want    *rewardsPb.ChooseRewardResponse
		wantErr bool
	}{
		{
			name: "Fetch reward by id failed",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), gomock.Any()).Return(nil, errors.New("Fetch reward by id failed")),
			},
			args: args{
				ctx: context.Background(),
				req: req,
			},
			want: &rewardsPb.ChooseRewardResponse{
				Status: rpc.StatusInternalWithDebugMsg("Fetch reward by id failed"),
			},
			wantErr: false,
		},
		{
			name: "invalid reward id",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), gomock.Any()).Return(nil, nil),
			},
			args: args{
				ctx: context.Background(),
				req: req,
			},
			want: &rewardsPb.ChooseRewardResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid reward id"),
			},
			wantErr: false,
		},
		{
			name: "invalid reward option id",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), gomock.Any()).Return(modelReward, nil),
			},
			args: args{
				ctx: context.Background(),
				req: reqWithInvalidOption,
			},
			want: &rewardsPb.ChooseRewardResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid reward option id"),
			},
			wantErr: false,
		},
		{
			name: "unable to update chosen reward option",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), gomock.Any()).Return(modelReward, nil),
				mockRewardsDao.EXPECT().ChooseReward(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("unable to update chosen reward option")),
			},
			args: args{
				ctx: context.Background(),
				req: req,
			},
			want: &rewardsPb.ChooseRewardResponse{
				Status: rpc.StatusInternalWithDebugMsg("unable to update chosen reward option"),
			},
			wantErr: false,
		},
		{
			name: "no rows update error on choosing option i.e reward option was already chosen before",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), gomock.Any()).Return(modelReward, nil),
				mockRewardsDao.EXPECT().ChooseReward(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(epifierrors.ErrRowNotUpdated),
			},
			args: args{
				ctx: context.Background(),
				req: req,
			},
			want: &rewardsPb.ChooseRewardResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "failed to choose reward option",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), gomock.Any()).Return(modelReward, nil),
				mockRewardsDao.EXPECT().ChooseReward(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", errors.New("unable to publish reward to processing queue")),
			},
			args: args{
				ctx: context.Background(),
				req: req,
			},
			want: &rewardsPb.ChooseRewardResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to publish reward for processing"),
			},
			wantErr: false,
		},
		{
			name: "Successful Immediate processing",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), gomock.Any()).Return(modelReward, nil),
				mockRewardsDao.EXPECT().ChooseReward(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("publish_id", nil),
			},
			args: args{
				ctx: context.Background(),
				req: req,
			},
			want: &rewardsPb.ChooseRewardResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "failed to choose reward option",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), gomock.Any()).Return(modelReward, nil),
				mockRewardsDao.EXPECT().ChooseReward(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				mockDelayPublisher.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", errors.New("unable to publish reward to delayed processing queue")),
			},
			args: args{
				ctx: context.Background(),
				req: reqForDelayedProcessing,
			},
			want: &rewardsPb.ChooseRewardResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to publish reward for processing"),
			},
			wantErr: false,
		},
		{
			name: "Successful Delayed processing",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), gomock.Any()).Return(modelReward, nil),
				mockRewardsDao.EXPECT().ChooseReward(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				mockDelayPublisher.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("publish_id", nil),
			},
			args: args{
				ctx: context.Background(),
				req: reqForDelayedProcessing,
			},
			want: &rewardsPb.ChooseRewardResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rgs.ChooseReward(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ChooseReward() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ChooseReward() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRewardsService_RetryRewardProcessing(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockRewardsDao := daoMock.NewMockRewardsDao(ctr)
	brokerMock := eventMocks.NewMockBroker(ctr)
	mockPublisher := mock_queue.NewMockPublisher(ctr)
	mockDelayPublisher := mock_queue.NewMockDelayPublisher(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	rgs := &RewardsService{
		rewardDao:                      mockRewardsDao,
		rewardProcessingPublisher:      mockPublisher,
		rewardProcessingDelayPublisher: mockDelayPublisher,
		eventsBroker:                   brokerMock,
	}

	// reward in 'CREATED' state
	modelReward1 := &model.Reward{
		Id:     uuid.New().String(),
		Status: rewardsPb.RewardStatus_CREATED,
		RewardOptions: &rewardsPb.RewardOptions{
			Options: []*rewardsPb.RewardOption{
				{
					Id: "option_id_1",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{AbsoluteTime: timestamppb.Now()},
					},
					Option: &rewardsPb.RewardOption_Cash{},
				},
				{
					Id: "option_id_2",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 5},
					},
					Option: &rewardsPb.RewardOption_Cash{},
				},
			},
		},
	}

	// reward in 'PROCESSING_IN_PROGRESS' state
	modelReward2 := &model.Reward{
		Id:     uuid.New().String(),
		Status: rewardsPb.RewardStatus_PROCESSING_IN_PROGRESS,
		RewardOptions: &rewardsPb.RewardOptions{
			Options: []*rewardsPb.RewardOption{
				{
					Id: "option_id_1",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{AbsoluteTime: timestamppb.Now()},
					},
					Option: &rewardsPb.RewardOption_Cash{},
				},
				{
					Id: "option_id_2",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 5},
					},
					Option: &rewardsPb.RewardOption_Cash{},
				},
			},
		},
		ChosenReward: &rewardsPb.RewardOption{
			Id: "option_id_2",
			RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
				Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 5},
			},
			Option: &rewardsPb.RewardOption_Cash{},
		},
	}

	// reward in 'PROCESSING_PENDING' state
	modelReward3 := &model.Reward{
		Id:     uuid.New().String(),
		Status: rewardsPb.RewardStatus_PROCESSING_PENDING,
		RewardOptions: &rewardsPb.RewardOptions{
			Options: []*rewardsPb.RewardOption{
				{
					Id: "option_id_1",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{AbsoluteTime: timestamppb.Now()},
					},
					Option: &rewardsPb.RewardOption_Cash{},
				},
				{
					Id: "option_id_2",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 5},
					},
					Option: &rewardsPb.RewardOption_Cash{},
				},
			},
		},
		ChosenReward: &rewardsPb.RewardOption{
			Id: "option_id_2",
			RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
				Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 5},
			},
			Option: &rewardsPb.RewardOption_Cash{},
		},
	}

	type args struct {
		ctx context.Context
		req *rewardsPb.RetryRewardProcessingRequest
	}
	tests := []struct {
		name    string
		mocks   []interface{}
		args    args
		want    *rewardsPb.RetryRewardProcessingResponse
		wantErr bool
	}{
		{
			name: "Error fetching reward from dao",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), modelReward2.Id).Return(nil, errors.New("error")),
			},
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.RetryRewardProcessingRequest{RewardId: modelReward2.Id},
			},
			want: &rewardsPb.RetryRewardProcessingResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "nil reward returned by dao",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), modelReward2.Id).Return(nil, nil),
			},
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.RetryRewardProcessingRequest{RewardId: modelReward2.Id},
			},
			want: &rewardsPb.RetryRewardProcessingResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "reward status (CREATED) not valid for processing retry",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), modelReward1.Id).Return(modelReward1, nil),
			},
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.RetryRewardProcessingRequest{RewardId: modelReward1.Id},
			},
			want: &rewardsPb.RetryRewardProcessingResponse{
				Status: rpc.StatusFailedPrecondition(),
			},
			wantErr: false,
		},
		{
			name: "error publishing retry processing event",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), modelReward2.Id).Return(modelReward2, nil),
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", errors.New("error")),
			},
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.RetryRewardProcessingRequest{RewardId: modelReward2.Id},
			},
			want: &rewardsPb.RetryRewardProcessingResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "successful retry processing flow for reward in PROCESSING_IN_PROGRESS state",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), modelReward2.Id).Return(modelReward2, nil),
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return(uuid.New().String(), nil),
			},
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.RetryRewardProcessingRequest{RewardId: modelReward2.Id},
			},
			want: &rewardsPb.RetryRewardProcessingResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "successful retry processing flow for reward in PROCESSING_PENDING state",
			mocks: []interface{}{
				mockRewardsDao.EXPECT().FetchRewardById(gomock.Any(), modelReward3.Id).Return(modelReward3, nil),
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return(uuid.New().String(), nil),
			},
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.RetryRewardProcessingRequest{RewardId: modelReward3.Id},
			},
			want: &rewardsPb.RetryRewardProcessingResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rgs.RetryRewardProcessing(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RetryRewardProcessing() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.GetStatus().GetCode() != tt.want.GetStatus().GetCode() {
				t.Errorf("RetryRewardProcessing() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRewardsService_GetReferralRewardsCappingInfo(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	var mockRewardsDao *daoMock.MockRewardsDao

	req1 := &rewardsPb.GetReferralRewardsCappingInfoRequest{
		ActorId:             "actor-1",
		ReferralsUnlockDate: timestamppb.New(time.Date(2022, 1, 1, 12, 0, 5, 0, time.UTC)),
	}

	cappingDuration, _ := time.ParseDuration("720h")

	conf := &config.Config{
		ReferralRewardsCappingAndPeriod: &config.ReferralRewardsCappingAndPeriod{
			RewardsCap:    2,
			CappingPeriod: cappingDuration,
			Active:        true},
		RewardsPeriodicCappingsPerOfferType: map[string]*config.RewardsPeriodicCappingInfo{
			"1": {
				RewardsCap:      1,
				OfferType:       "REFERRAL_REFERRER_OFFER",
				CappingPeriod:   1,
				CappingDuration: "CALENDAR_MONTH",
				Active:          true,
			},
		},
	}
	dyconf := &genconf.Config{}
	dyconf.Init("")
	_ = dyconf.Set(conf, false, nil)

	type fields struct {
		fakeClock clockwork.Clock
	}

	type args struct {
		ctx context.Context
		req *rewardsPb.GetReferralRewardsCappingInfoRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func()
		fields     fields
		want       *rewardsPb.GetReferralRewardsCappingInfoResponse
		wantErr    bool
	}{
		{
			name: "should return IsMaxCapHit true along with correct dates when number of rewards for actor >= configured capping for the capping duration",
			args: args{
				ctx: context.Background(),
				req: req1,
			},
			setupMocks: func() {
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(context.Background(), &model.QueryRewardsFilter{
					ActorId:          "actor-1",
					FromTime:         time.Date(2022, 3, 2, 0, 0, 0, 0, time.UTC),
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
					PageSize:         100,
				}, nil).Return([]*model.Reward{{Id: "reward-1"}, {Id: "reward-2"}}, nil, nil)
			},
			fields: fields{fakeClock: clockwork.NewFakeClockAt(time.Date(2022, 3, 22, 12, 2, 0, 0, time.UTC))},
			want: &rewardsPb.GetReferralRewardsCappingInfoResponse{
				Status:           rpc.StatusOk(),
				IsMaxCapHit:      true,
				MaxCap:           2,
				MaxCapResetDate:  timestamppb.New(time.Date(2022, 4, 1, 0, 0, 0, 0, time.UTC)),
				LastCapResetDate: timestamppb.New(time.Date(2022, 3, 2, 0, 0, 0, 0, time.UTC)),
			},
			wantErr: false,
		},
		{
			name: "should return error when error in fetching rewards for offerType cappings",
			args: args{
				ctx: context.Background(),
				req: req1,
			},
			setupMocks: func() {
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(context.Background(), &model.QueryRewardsFilter{
					ActorId:          "actor-1",
					FromTime:         time.Date(2022, 3, 2, 0, 0, 0, 0, time.UTC),
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
					PageSize:         100,
				}, nil).Return([]*model.Reward{}, nil, nil)
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(context.Background(), &model.QueryRewardsFilter{
					ActorId:          "actor-1",
					FromTime:         datetime.StartOfMonth(time.Now()),
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
					PageSize:         100,
				}, nil).Return(nil, nil, fmt.Errorf("error in fetching rewards"))
			},
			fields: fields{fakeClock: clockwork.NewFakeClockAt(time.Date(2022, 3, 2, 0, 0, 0, 0, time.UTC))},
			want: &rewardsPb.GetReferralRewardsCappingInfoResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch rewards for actor"),
			},
			wantErr: false,
		},
		{
			name: "should return IsMaxCapHit true offerType cappings are hit",
			args: args{
				ctx: context.Background(),
				req: req1,
			},
			setupMocks: func() {
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(context.Background(), &model.QueryRewardsFilter{
					ActorId:          "actor-1",
					FromTime:         time.Date(2022, 3, 2, 0, 0, 0, 0, time.UTC),
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
					PageSize:         100,
				}, nil).Return([]*model.Reward{}, nil, nil)
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(context.Background(), &model.QueryRewardsFilter{
					ActorId:          "actor-1",
					FromTime:         datetime.StartOfMonth(time.Now()),
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
					PageSize:         100,
				}, nil).Return([]*model.Reward{{Id: "reward-1"}, {Id: "reward-2"}}, nil, nil)
			},
			fields: fields{fakeClock: clockwork.NewFakeClockAt(time.Date(2022, 3, 2, 0, 0, 0, 0, time.UTC))},
			want: &rewardsPb.GetReferralRewardsCappingInfoResponse{
				Status:           rpc.StatusOk(),
				IsMaxCapHit:      true,
				MaxCap:           1,
				MaxCapResetDate:  timestamppb.New(datetime.StartOfMonth(time.Now()).AddDate(0, 1, 0)),
				LastCapResetDate: timestamppb.New(datetime.StartOfMonth(time.Now())),
			},
			wantErr: false,
		},
		{
			name: "should return IsMaxCapHit false along with correct lastCapResetDate when number of rewards for actor < configured capping for the capping duration and no rewardType cappings are hit",
			args: args{
				ctx: context.Background(),
				req: req1,
			},
			setupMocks: func() {
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(context.Background(), &model.QueryRewardsFilter{
					ActorId:          "actor-1",
					FromTime:         time.Date(2022, 3, 2, 0, 0, 0, 0, time.UTC),
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
					PageSize:         100,
				}, nil).Return([]*model.Reward{}, nil, nil)
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(context.Background(), &model.QueryRewardsFilter{
					ActorId:          "actor-1",
					FromTime:         datetime.StartOfMonth(time.Now()),
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
					PageSize:         100,
				}, nil).Return([]*model.Reward{}, nil, nil)
			},
			fields: fields{fakeClock: clockwork.NewFakeClockAt(time.Date(2022, 3, 2, 0, 0, 0, 0, time.UTC))},
			want: &rewardsPb.GetReferralRewardsCappingInfoResponse{
				Status:           rpc.StatusOk(),
				IsMaxCapHit:      false,
				MaxCapResetDate:  nil,
				LastCapResetDate: timestamppb.New(time.Date(2022, 3, 2, 0, 0, 0, 0, time.UTC)),
			},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when FetchRewardsByPageAndFilter call fails",
			args: args{
				ctx: context.Background(),
				req: req1,
			},
			setupMocks: func() {
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(context.Background(), &model.QueryRewardsFilter{
					ActorId:          "actor-1",
					FromTime:         time.Date(2022, 3, 2, 0, 0, 0, 0, time.UTC),
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER},
					PageSize:         100,
				}, nil).Return(nil, nil, fmt.Errorf("error"))
			},
			fields: fields{fakeClock: clockwork.NewFakeClockAt(time.Date(2022, 3, 2, 0, 0, 0, 0, time.UTC))},
			want: &rewardsPb.GetReferralRewardsCappingInfoResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch rewards for actor"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRewardsDao = daoMock.NewMockRewardsDao(ctr)
			tt.setupMocks()

			rgs := &RewardsService{
				rewardDao: mockRewardsDao,
				conf:      conf,
				dyconf:    dyconf,
				clock:     tt.fields.fakeClock,
			}

			got, err := rgs.GetReferralRewardsCappingInfo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetReferralRewardsCappingInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetReferralRewardsCappingInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRewardsService_BulkClaimRewardsWithDefaultOption(t *testing.T) {
	var (
		mockRewardsDao     *daoMock.MockRewardsDao
		brokerMock         *eventMocks.MockBroker
		mockPublisher      *mock_queue.MockPublisher
		mockDelayPublisher *mock_queue.MockDelayPublisher
	)

	modelReward1 := &model.Reward{
		Id:         "reward-id-1",
		RewardType: 0,
		Status:     0,
		Aggregates: nil,
		RewardOptions: &rewardsPb.RewardOptions{
			Options: []*rewardsPb.RewardOption{
				{
					Id: "option_id_1",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{AbsoluteTime: timestamppb.Now()},
					},
					Option:     &rewardsPb.RewardOption_Cash{},
					RewardType: rewardsPb.RewardType_CASH,
				},
				{
					Id: "option_id_2",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 5},
					},
					Option:     &rewardsPb.RewardOption_SmartDeposit{},
					RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
				},
			},
		},
		ChosenReward: nil,
	}

	modelReward2 := &model.Reward{
		Id:         "reward-id-2",
		RewardType: 0,
		Status:     0,
		Aggregates: nil,
		RewardOptions: &rewardsPb.RewardOptions{
			Options: []*rewardsPb.RewardOption{
				{
					Id: "option_id_1",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{AbsoluteTime: timestamppb.Now()},
					},
					Option:     &rewardsPb.RewardOption_SmartDeposit{},
					RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
				},
				{
					Id: "option_id_2",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 5},
					},
					Option:     &rewardsPb.RewardOption_Cash{},
					RewardType: rewardsPb.RewardType_CASH,
				},
			},
		},
		ChosenReward: nil,
	}

	modelReward3 := &model.Reward{
		Id:         "reward-id-2",
		RewardType: 0,
		Status:     0,
		Aggregates: nil,
		RewardOptions: &rewardsPb.RewardOptions{
			Options: []*rewardsPb.RewardOption{
				{
					Id: "option_id_1",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{AbsoluteTime: timestamppb.Now()},
					},
					Option:     &rewardsPb.RewardOption_Cash{},
					RewardType: rewardsPb.RewardType_CASH,
				},
				{
					Id: "option_id_2",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 5},
					},
					Option:     &rewardsPb.RewardOption_Cash{},
					RewardType: rewardsPb.RewardType_CASH,
				},
			},
		},
		ChosenReward: nil,
	}

	modelReward4 := &model.Reward{
		Id:         "reward-id-2",
		RewardType: 0,
		Status:     0,
		Aggregates: nil,
		RewardOptions: &rewardsPb.RewardOptions{
			Options: []*rewardsPb.RewardOption{
				{
					Id: "option_id_1",
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{AbsoluteTime: timestamppb.Now()},
					},
					Option:     &rewardsPb.RewardOption_Cash{},
					RewardType: rewardsPb.RewardType_CASH,
				},
			},
			UnlockDate: timestamppb.New(time.Date(2200, 1, 1, 0, 0, 0, 0, time.Local)),
		},
		ChosenReward: nil,
	}
	type args struct {
		ctx context.Context
		req *rewardsPb.BulkClaimRewardsWithDefaultOptionRequest
	}
	tests := []struct {
		name       string
		setupMocks func()
		args       args
		want       *rewardsPb.BulkClaimRewardsWithDefaultOptionResponse
		wantErr    bool
	}{
		{
			name: "should return StatusInternalWithDebugMsg when dao call FetchRewards fails",
			setupMocks: func() {
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, fmt.Errorf("error"))
			},
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.BulkClaimRewardsWithDefaultOptionRequest{
					ActorId:           "actor-id",
					MaxRewardsToClaim: 5,
				},
			},
			want:    &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusInternalWithDebugMsg("error while fetching rewards for actor")},
			wantErr: false,
		},
		{
			name: "should return Success when FetchRewards returns an empty array",
			setupMocks: func() {
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.Reward{}, nil, nil)
			},
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.BulkClaimRewardsWithDefaultOptionRequest{
					ActorId:           "actor-id",
					MaxRewardsToClaim: 5,
				},
			},
			want:    &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "should return StatusOk when all rewards have default option of non-currency based reward type",
			setupMocks: func() {
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.Reward{modelReward2}, nil, nil)
			},
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.BulkClaimRewardsWithDefaultOptionRequest{
					ActorId:           "actor-id",
					MaxRewardsToClaim: 5,
				},
			},
			want:    &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when all rewards have unlock date of future",
			setupMocks: func() {
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.Reward{modelReward4}, nil, nil)
			},
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.BulkClaimRewardsWithDefaultOptionRequest{
					ActorId:           "actor-id",
					MaxRewardsToClaim: 5,
				},
			},
			want:    &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when error occurs during choosing option",
			setupMocks: func() {
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.Reward{modelReward1}, nil, nil)
				mockRewardsDao.EXPECT().BulkChooseRewards(gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("error"))
			},
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.BulkClaimRewardsWithDefaultOptionRequest{
					ActorId:           "actor-id",
					MaxRewardsToClaim: 5,
				},
			},
			want:    &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusInternalWithDebugMsg("failed to claim rewards in bulk for actor")},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when error occurs during publishing reward for processing",
			setupMocks: func() {
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.Reward{modelReward1, modelReward2, modelReward3}, nil, nil)
				mockRewardsDao.EXPECT().BulkChooseRewards(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", fmt.Errorf("error"))
			},
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.BulkClaimRewardsWithDefaultOptionRequest{
					ActorId:           "actor-id",
					MaxRewardsToClaim: 2,
				},
			},
			want:    &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusInternalWithDebugMsg("error while publishing reward for processing")},
			wantErr: false,
		},
		{
			name: "should return StatusOk when MaxRewardsToClaim rewards have been claimed successfully",
			setupMocks: func() {
				mockRewardsDao.EXPECT().FetchRewardsByPageAndFilter(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.Reward{modelReward1, modelReward2, modelReward3}, nil, nil)
				mockRewardsDao.EXPECT().BulkChooseRewards(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("publish_id", nil).Times(2)
			},
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.BulkClaimRewardsWithDefaultOptionRequest{
					ActorId:           "actor-id",
					MaxRewardsToClaim: 2,
				},
			},
			want:    &rewardsPb.BulkClaimRewardsWithDefaultOptionResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockRewardsDao = daoMock.NewMockRewardsDao(ctr)
			brokerMock = eventMocks.NewMockBroker(ctr)
			mockPublisher = mock_queue.NewMockPublisher(ctr)
			mockDelayPublisher = mock_queue.NewMockDelayPublisher(ctr)

			brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
			tt.setupMocks()

			rgs := &RewardsService{
				rewardDao:                      mockRewardsDao,
				rewardProcessingPublisher:      mockPublisher,
				rewardProcessingDelayPublisher: mockDelayPublisher,
				eventsBroker:                   brokerMock,
			}
			got, err := rgs.BulkClaimRewardsWithDefaultOption(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BulkClaimRewardsWithDefaultOption() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BulkClaimRewardsWithDefaultOption() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRewardsService_GetCreditCardLinkedRewardDetails(t *testing.T) {
	type args struct {
		ctx context.Context
		req *rewardsPb.GetCreditCardLinkedRewardDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockRewardsDao *daoMock.MockRewardsDao, mockClawbacksDao *clawbackDaoMock.MockRewardClawbacksDao, mockCCRewardAmtCalculator *rewardamtcalculatorMocks.MockICCRewardAmountCalculator)
		want       *rewardsPb.GetCreditCardLinkedRewardDetailsResponse
		wantErr    bool
	}{
		{
			name: "should return ISE rpc status when dao call to fetch the rewards fails",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetCreditCardLinkedRewardDetailsRequest{
					RefIds:          []string{"txn-1", "txn-2", "txn-3", "txn-4"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockClawbacksDao *clawbackDaoMock.MockRewardClawbacksDao, mockCCRewardAmtCalculator *rewardamtcalculatorMocks.MockICCRewardAmountCalculator) {
				mockRewardsDao.EXPECT().FetchRewards(context.Background(), &model.QueryRewardsFilter{
					RefIds:           []string{"txn-1", "txn-2", "txn-3", "txn-4"},
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER},
				}).Return(nil, fmt.Errorf("error fetching rewards from db"))
			},
			want: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error fetching rewards from db"),
			},
			wantErr: false,
		},
		{
			name: "should return empty reward details list when no 1x spends rewards and clawbacks exists for given refIds",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetCreditCardLinkedRewardDetailsRequest{
					RefIds:          []string{"txn-1", "txn-2"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockClawbacksDao *clawbackDaoMock.MockRewardClawbacksDao, mockCCRewardAmtCalculator *rewardamtcalculatorMocks.MockICCRewardAmountCalculator) {
				mockRewardsDao.EXPECT().FetchRewards(context.Background(), &model.QueryRewardsFilter{
					RefIds:           []string{"txn-1", "txn-2"},
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER},
				}).Return([]*model.Reward{}, nil)

				mockClawbacksDao.EXPECT().GetClawbacks(context.Background(), &clawbackDaoModel.ClawbacksQueryFilter{
					ActionRefIds:    []string{"txn-1", "txn-2"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
				}).Return([]*rewardsPb.RewardClawback{}, nil)
			},
			want: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{
				Status:      rpc.StatusOk(),
				DetailsList: []*rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details{},
			},
			wantErr: false,
		},
		{
			name: "should return ISE rpc status when dao to fetch reward clawbacks fails",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetCreditCardLinkedRewardDetailsRequest{
					RefIds:          []string{"txn-1", "txn-2", "txn-3", "txn-4"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockClawbacksDao *clawbackDaoMock.MockRewardClawbacksDao, mockCCRewardAmtCalculator *rewardamtcalculatorMocks.MockICCRewardAmountCalculator) {
				mockRewardsDao.EXPECT().FetchRewards(context.Background(), &model.QueryRewardsFilter{
					RefIds:           []string{"txn-1", "txn-2", "txn-3", "txn-4"},
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER},
				}).Return([]*model.Reward{
					{
						Id: "id-1",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									RewardType: rewardsPb.RewardType_CASH,
									Option: &rewardsPb.RewardOption_Cash{
										Cash: &rewardsPb.Cash{
											Amount: moneyPb.FromPaisa(15550),
										},
									},
								},
							},
						},
						RefId:     "txn-1",
						Status:    rewardsPb.RewardStatus_CREATED,
						OfferType: rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
					},
					{
						Id: "id-2",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 501,
										},
									},
								},
							},
						},
						RefId:     "txn-4",
						Status:    rewardsPb.RewardStatus_PROCESSED,
						OfferType: rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
					},
				}, nil)

				mockClawbacksDao.EXPECT().GetClawbacks(context.Background(), &clawbackDaoModel.ClawbacksQueryFilter{
					ActionRefIds:    []string{"txn-1", "txn-2", "txn-3", "txn-4"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
				}).Return(nil, errors.New("error fetching clawbacks from db"))
			},
			want: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error fetching clawbacks from db"),
			},
			wantErr: false,
		},
		{
			name: "should successfully return cc spends 1x reward and clawbacks when reward and clawbacks exists for the refIds",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetCreditCardLinkedRewardDetailsRequest{
					RefIds:          []string{"txn-1", "txn-2", "txn-3", "txn-4"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockClawbacksDao *clawbackDaoMock.MockRewardClawbacksDao, mockCCRewardAmtCalculator *rewardamtcalculatorMocks.MockICCRewardAmountCalculator) {
				mockRewardsDao.EXPECT().FetchRewards(context.Background(), &model.QueryRewardsFilter{
					RefIds:           []string{"txn-1", "txn-2", "txn-3", "txn-4"},
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER},
				}).Return([]*model.Reward{
					{
						Id: "id-1",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									RewardType: rewardsPb.RewardType_CASH,
									Option: &rewardsPb.RewardOption_Cash{
										Cash: &rewardsPb.Cash{
											Amount: moneyPb.FromPaisa(15550),
										},
									},
								},
							},
						},
						RefId:     "txn-1",
						Status:    rewardsPb.RewardStatus_CREATED,
						OfferType: rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
					},
					{
						Id: "id-2",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 501,
										},
									},
								},
							},
						},
						RefId:     "txn-4",
						Status:    rewardsPb.RewardStatus_PROCESSED,
						OfferType: rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
					},
				}, nil)

				mockClawbacksDao.EXPECT().GetClawbacks(context.Background(), &clawbackDaoModel.ClawbacksQueryFilter{
					ActionRefIds:    []string{"txn-1", "txn-2", "txn-3", "txn-4"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
				}).Return([]*rewardsPb.RewardClawback{
					{
						ClawedBackRewardUnits: 10.5,
						RewardType:            rewardsPb.RewardType_FI_COINS,
						ActionRefId:           "txn-2",
						Status:                rewardsPb.ClawbackStatus_CLAWBACK_STATUS_PROCESSING_PENDING,
					},
				}, nil)
			},
			want: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{
				Status: rpc.StatusOk(),
				DetailsList: []*rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details{
					{
						Details: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails{
							RewardDetails: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails{
								RewardType:  rewardsPb.RewardType_CASH,
								RewardUnits: 155.5,
								RefId:       "txn-1",
								Status:      rewardsPb.RewardStatus_CREATED,
							},
						},
					},
					{
						Details: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails{
							RewardDetails: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails{
								RewardType:  rewardsPb.RewardType_FI_COINS,
								RewardUnits: 501,
								RefId:       "txn-4",
								Status:      rewardsPb.RewardStatus_PROCESSED,
							},
						},
					},
					{
						Details: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardClawbackDetails{
							RewardClawbackDetails: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails{
								ClawedBackRewardUnits: 10.5,
								RewardType:            rewardsPb.RewardType_FI_COINS,
								ClawbackRefId:         "txn-2",
								ClawbackStatus:        rewardsPb.ClawbackStatus_CLAWBACK_STATUS_PROCESSING_PENDING,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should successfully return cc top merchant spends rewards for given refIds",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetCreditCardLinkedRewardDetailsRequest{
					RefIds:          []string{"bill-id-1", "bill-id-2"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER,
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockClawbacksDao *clawbackDaoMock.MockRewardClawbacksDao, mockCCRewardAmtCalculator *rewardamtcalculatorMocks.MockICCRewardAmountCalculator) {
				mockRewardsDao.EXPECT().FetchRewards(context.Background(), &model.QueryRewardsFilter{
					RefIds:           []string{"bill-id-1", "bill-id-2"},
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER},
				}).Return([]*model.Reward{
					{
						Id: "id-1",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 500,
										},
									},
								},
							},
						},
						RefId:     "bill-id-1",
						Status:    rewardsPb.RewardStatus_CREATED,
						OfferType: rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER,
						RewardMetadata: &rewardsPb.RewardMetadata{
							OfferTypeSpecificMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata{
								Metadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata_{
									CcTopMerchantsSpendsRewardMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata{
										AppliedRewardMultiplier: 2,
										TopMerchantNameTo_1XRewardAmount: map[string]float32{
											"merchant-1": 100,
											"merchant-2": 150,
										}},
								},
							},
						},
					},
				}, nil)

				mockCCRewardAmtCalculator.EXPECT().CalculateCCTopMerchantsSpendsOfferRewardAmount(context.Background(), &rewardamountcalculator.CCTopMerchSpendsOfferRewardAmountReq{
					BillId: "bill-id-2",
				}).Return(&rewardamountcalculator.CCTopMerchSpendsOfferRewardAmountRes{
					RewardAmount:            125,
					AppliedRewardMultiplier: 5,
					TopMerchantNameTo1xRewardAmountMap: map[string]float32{
						"merchant-1": 10,
						"merchant-2": 15,
					}}, nil)

				mockClawbacksDao.EXPECT().GetClawbacks(context.Background(), &clawbackDaoModel.ClawbacksQueryFilter{
					ActionRefIds:    []string{"bill-id-1", "bill-id-2"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER,
				}).Return([]*rewardsPb.RewardClawback{
					{
						ClawedBackRewardUnits: 100,
						RewardType:            rewardsPb.RewardType_FI_COINS,
						ActionRefId:           "bill-id-1",
						Status:                rewardsPb.ClawbackStatus_CLAWBACK_STATUS_PROCESSING_PENDING,
					},
				}, nil)
			},
			want: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{
				Status: rpc.StatusOk(),
				DetailsList: []*rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details{
					{
						Details: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails{
							RewardDetails: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails{
								RewardType:  rewardsPb.RewardType_FI_COINS,
								RewardUnits: 500,
								RefId:       "bill-id-1",
								Status:      rewardsPb.RewardStatus_CREATED,
								RewardMetadata: &rewardsPb.RewardMetadata{
									OfferTypeSpecificMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata{
										Metadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata_{
											CcTopMerchantsSpendsRewardMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata{
												AppliedRewardMultiplier: 2,
												TopMerchantNameTo_1XRewardAmount: map[string]float32{
													"merchant-1": 100,
													"merchant-2": 150,
												}},
										},
									},
								},
							},
						},
					},
					{
						Details: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails{
							RewardDetails: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails{
								RewardType:  rewardsPb.RewardType_FI_COINS,
								RewardUnits: 125,
								RefId:       "bill-id-2",
								Status:      rewardsPb.RewardStatus_PROCESSING_PENDING,
								RewardMetadata: &rewardsPb.RewardMetadata{
									OfferTypeSpecificMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata{
										Metadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata_{
											CcTopMerchantsSpendsRewardMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata{
												AppliedRewardMultiplier: 5,
												TopMerchantNameTo_1XRewardAmount: map[string]float32{
													"merchant-1": 10,
													"merchant-2": 15,
												}},
										},
									},
								},
								IsProjectedReward: true,
							},
						},
					},
					{
						Details: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardClawbackDetails{
							RewardClawbackDetails: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardClawbackDetails{
								RewardType:            rewardsPb.RewardType_FI_COINS,
								ClawedBackRewardUnits: 100,
								ClawbackRefId:         "bill-id-1",
								ClawbackStatus:        rewardsPb.ClawbackStatus_CLAWBACK_STATUS_PROCESSING_PENDING,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return cc top merchants spends projected reward of NO_REWARD rewardType when projected reward amount is zero",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetCreditCardLinkedRewardDetailsRequest{
					RefIds:          []string{"bill-id-2"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER,
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockClawbacksDao *clawbackDaoMock.MockRewardClawbacksDao, mockCCRewardAmtCalculator *rewardamtcalculatorMocks.MockICCRewardAmountCalculator) {
				mockRewardsDao.EXPECT().FetchRewards(context.Background(), &model.QueryRewardsFilter{
					RefIds:           []string{"bill-id-2"},
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER},
				}).Return([]*model.Reward{}, nil)

				mockCCRewardAmtCalculator.EXPECT().CalculateCCTopMerchantsSpendsOfferRewardAmount(context.Background(), &rewardamountcalculator.CCTopMerchSpendsOfferRewardAmountReq{
					BillId: "bill-id-2",
				}).Return(&rewardamountcalculator.CCTopMerchSpendsOfferRewardAmountRes{
					RewardAmount: 0,
				}, nil)

				mockClawbacksDao.EXPECT().GetClawbacks(context.Background(), &clawbackDaoModel.ClawbacksQueryFilter{
					ActionRefIds:    []string{"bill-id-2"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER,
				}).Return([]*rewardsPb.RewardClawback{}, nil)
			},
			want: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{
				Status: rpc.StatusOk(),
				DetailsList: []*rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details{
					{
						Details: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails{
							RewardDetails: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails{
								RewardType:  rewardsPb.RewardType_NO_REWARD,
								RewardUnits: 0,
								RefId:       "bill-id-2",
								Status:      rewardsPb.RewardStatus_PROCESSING_PENDING,
								RewardMetadata: &rewardsPb.RewardMetadata{
									OfferTypeSpecificMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata{
										Metadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata_{
											CcTopMerchantsSpendsRewardMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata{},
										},
									},
								},
								IsProjectedReward: true,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return error when projected reward amount calculation fails for cc top merchant spends offer",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetCreditCardLinkedRewardDetailsRequest{
					RefIds:          []string{"bill-id-1", "bill-id-2"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER,
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockClawbacksDao *clawbackDaoMock.MockRewardClawbacksDao, mockCCRewardAmtCalculator *rewardamtcalculatorMocks.MockICCRewardAmountCalculator) {
				mockRewardsDao.EXPECT().FetchRewards(context.Background(), &model.QueryRewardsFilter{
					RefIds:           []string{"bill-id-1", "bill-id-2"},
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER},
				}).Return([]*model.Reward{
					{
						Id: "id-1",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 500,
										},
									},
								},
							},
						},
						RefId:     "bill-id-1",
						Status:    rewardsPb.RewardStatus_CREATED,
						OfferType: rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER,
						RewardMetadata: &rewardsPb.RewardMetadata{
							OfferTypeSpecificMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata{
								Metadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata_{
									CcTopMerchantsSpendsRewardMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcTopMerchantsSpendsRewardMetadata{
										AppliedRewardMultiplier: 2,
										TopMerchantNameTo_1XRewardAmount: map[string]float32{
											"merchant-1": 100,
											"merchant-2": 150,
										}},
								},
							},
						},
					},
				}, nil)

				mockCCRewardAmtCalculator.EXPECT().CalculateCCTopMerchantsSpendsOfferRewardAmount(context.Background(), &rewardamountcalculator.CCTopMerchSpendsOfferRewardAmountReq{
					BillId: "bill-id-2",
				}).Return(nil, fmt.Errorf("error calculating reward amount"))
			},
			want: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error calculating project reward amount, err : error calculating reward amount"),
			},
			wantErr: false,
		},
		{
			name: "should successfully return cc curated merchants spends rewards for given refIds",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetCreditCardLinkedRewardDetailsRequest{
					RefIds:          []string{"bill-id-1", "bill-id-2"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER,
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockClawbacksDao *clawbackDaoMock.MockRewardClawbacksDao, mockCCRewardAmtCalculator *rewardamtcalculatorMocks.MockICCRewardAmountCalculator) {
				mockRewardsDao.EXPECT().FetchRewards(context.Background(), &model.QueryRewardsFilter{
					RefIds:           []string{"bill-id-1", "bill-id-2"},
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER},
				}).Return([]*model.Reward{
					{
						Id: "id-1",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 500,
										},
									},
								},
							},
						},
						RefId:     "bill-id-1",
						Status:    rewardsPb.RewardStatus_CREATED,
						OfferType: rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER,
						RewardMetadata: &rewardsPb.RewardMetadata{
							OfferTypeSpecificMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata{
								Metadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata_{
									CcCuratedMerchantsSpendsRewardMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata{
										CuratedMerchantNameTo_1XRewardAmount: map[string]float32{
											"merchant-1": 100,
											"merchant-2": 150,
										}},
								},
							},
						},
					},
				}, nil)

				mockCCRewardAmtCalculator.EXPECT().CalculateCCCuratedMerchantsSpendsOfferRewardAmount(context.Background(), &rewardamountcalculator.CCCuratedMerchantsSpendsOfferRewardAmountReq{
					BillId: "bill-id-2",
				}).Return(&rewardamountcalculator.CCCuratedMerchantsSpendsOfferRewardAmountRes{
					RewardAmount: 125,
					CuratedMerchantNameTo1xRewardAmountMap: map[string]float32{
						"merchant-1": 10,
						"merchant-2": 15,
					}}, nil)

				mockClawbacksDao.EXPECT().GetClawbacks(context.Background(), &clawbackDaoModel.ClawbacksQueryFilter{
					ActionRefIds:    []string{"bill-id-1", "bill-id-2"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER,
				}).Return([]*rewardsPb.RewardClawback{}, nil)
			},
			want: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{
				Status: rpc.StatusOk(),
				DetailsList: []*rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details{
					{
						Details: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails{
							RewardDetails: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails{
								RewardType:  rewardsPb.RewardType_FI_COINS,
								RewardUnits: 500,
								RefId:       "bill-id-1",
								Status:      rewardsPb.RewardStatus_CREATED,
								RewardMetadata: &rewardsPb.RewardMetadata{
									OfferTypeSpecificMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata{
										Metadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata_{
											CcCuratedMerchantsSpendsRewardMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata{
												CuratedMerchantNameTo_1XRewardAmount: map[string]float32{
													"merchant-1": 100,
													"merchant-2": 150,
												}},
										},
									},
								},
							},
						},
					},
					{
						Details: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails{
							RewardDetails: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails{
								RewardType:  rewardsPb.RewardType_FI_COINS,
								RewardUnits: 125,
								RefId:       "bill-id-2",
								Status:      rewardsPb.RewardStatus_PROCESSING_PENDING,
								RewardMetadata: &rewardsPb.RewardMetadata{
									OfferTypeSpecificMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata{
										Metadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata_{
											CcCuratedMerchantsSpendsRewardMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata{
												CuratedMerchantNameTo_1XRewardAmount: map[string]float32{
													"merchant-1": 10,
													"merchant-2": 15,
												}},
										},
									},
								},
								IsProjectedReward: true,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return cc curated merchants spends projected reward of NO_REWARD rewardType when projected reward amount is zero",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetCreditCardLinkedRewardDetailsRequest{
					RefIds:          []string{"bill-id-2"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER,
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockClawbacksDao *clawbackDaoMock.MockRewardClawbacksDao, mockCCRewardAmtCalculator *rewardamtcalculatorMocks.MockICCRewardAmountCalculator) {
				mockRewardsDao.EXPECT().FetchRewards(context.Background(), &model.QueryRewardsFilter{
					RefIds:           []string{"bill-id-2"},
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER},
				}).Return([]*model.Reward{}, nil)

				mockCCRewardAmtCalculator.EXPECT().CalculateCCCuratedMerchantsSpendsOfferRewardAmount(context.Background(), &rewardamountcalculator.CCCuratedMerchantsSpendsOfferRewardAmountReq{
					BillId: "bill-id-2",
				}).Return(&rewardamountcalculator.CCCuratedMerchantsSpendsOfferRewardAmountRes{
					RewardAmount: 0,
				}, nil)

				mockClawbacksDao.EXPECT().GetClawbacks(context.Background(), &clawbackDaoModel.ClawbacksQueryFilter{
					ActionRefIds:    []string{"bill-id-2"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER,
				}).Return([]*rewardsPb.RewardClawback{}, nil)
			},
			want: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{
				Status: rpc.StatusOk(),
				DetailsList: []*rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details{
					{
						Details: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails{
							RewardDetails: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse_RewardDetails{
								RewardType:  rewardsPb.RewardType_NO_REWARD,
								RewardUnits: 0,
								RefId:       "bill-id-2",
								Status:      rewardsPb.RewardStatus_PROCESSING_PENDING,
								RewardMetadata: &rewardsPb.RewardMetadata{
									OfferTypeSpecificMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata{
										Metadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata_{
											CcCuratedMerchantsSpendsRewardMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata{},
										},
									},
								},
								IsProjectedReward: true,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return error when projected reward amount calculation fails for cc top merchant spends offer",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetCreditCardLinkedRewardDetailsRequest{
					RefIds:          []string{"bill-id-1", "bill-id-2"},
					RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER,
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockClawbacksDao *clawbackDaoMock.MockRewardClawbacksDao, mockCCRewardAmtCalculator *rewardamtcalculatorMocks.MockICCRewardAmountCalculator) {
				mockRewardsDao.EXPECT().FetchRewards(context.Background(), &model.QueryRewardsFilter{
					RefIds:           []string{"bill-id-1", "bill-id-2"},
					RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER},
				}).Return([]*model.Reward{
					{
						Id: "id-1",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 500,
										},
									},
								},
							},
						},
						RefId:     "bill-id-1",
						Status:    rewardsPb.RewardStatus_CREATED,
						OfferType: rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER,
						RewardMetadata: &rewardsPb.RewardMetadata{
							OfferTypeSpecificMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata{
								Metadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata_{
									CcCuratedMerchantsSpendsRewardMetadata: &rewardsPb.RewardMetadata_RewardOfferTypeSpecificMetadata_CcCuratedMerchantsSpendsRewardMetadata{
										CuratedMerchantNameTo_1XRewardAmount: map[string]float32{
											"merchant-1": 100,
											"merchant-2": 150,
										}},
								},
							},
						},
					},
				}, nil)

				mockCCRewardAmtCalculator.EXPECT().CalculateCCCuratedMerchantsSpendsOfferRewardAmount(context.Background(), &rewardamountcalculator.CCCuratedMerchantsSpendsOfferRewardAmountReq{
					BillId: "bill-id-2",
				}).Return(nil, fmt.Errorf("error calculating reward amount"))
			},
			want: &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error calculating project reward amount, err : error calculating reward amount"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRewardsDao := daoMock.NewMockRewardsDao(ctr)
			mockClawbacksDao := clawbackDaoMock.NewMockRewardClawbacksDao(ctr)
			mockCCRewardAmtCalculator := rewardamtcalculatorMocks.NewMockICCRewardAmountCalculator(ctr)

			tt.setupMocks(mockRewardsDao, mockClawbacksDao, mockCCRewardAmtCalculator)

			rgs := &RewardsService{
				rewardDao:             mockRewardsDao,
				clawbacksDao:          mockClawbacksDao,
				ccRewardAmtCalculator: mockCCRewardAmtCalculator,
			}
			got, err := rgs.GetCreditCardLinkedRewardDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCreditCardLinkedRewardDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetCreditCardLinkedRewardDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRewardsService_GetRewardsCount(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *rewardsPb.GetRewardsCountRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockRewardsDao *daoMock.MockRewardsDao)
		want       *rewardsPb.GetRewardsCountResponse
		wantErr    assert.ErrorAssertionFunc
	}{
		{
			name: "should return error if dao call returns error",
			args: args{
				ctx: context.Background(),
				request: &rewardsPb.GetRewardsCountRequest{
					ActorId: "actor-1",
					Filters: &rewardsPb.GetRewardsCountRequest_Filters{
						AndFilter: &rewardsPb.GetRewardsCountRequest_FilterFields{
							FromTime: timestamppb.New(time.Date(2020, 9, 12, 0, 0, 0, 0, time.UTC)),
						},
						OrFilter: &rewardsPb.GetRewardsCountRequest_FilterFields{
							RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER, rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER},
							RewardTypes:      []rewardsPb.RewardType{rewardsPb.RewardType_CASH},
						},
					},
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao) {
				mockRewardsDao.EXPECT().GetRewardsCount(context.Background(), "actor-1", &model2.RewardsCountFilters{
					AndFilter: &model2.RewardsCountFilterFields{
						FromTime: time.Date(2020, 9, 12, 0, 0, 0, 0, time.UTC),
					},
					OrFilter: &model2.RewardsCountFilterFields{
						RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER, rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER},
						RewardTypes:      []rewardsPb.RewardType{rewardsPb.RewardType_CASH},
					},
				}).Return(uint32(0), fmt.Errorf("error"))
			},
			want: &rewardsPb.GetRewardsCountResponse{
				Status: rpc.StatusInternalWithDebugMsg("error"),
			},
			wantErr: assert.NoError,
		},
		{
			name: "should return correct count if dao call does not return an error",
			args: args{
				ctx: context.Background(),
				request: &rewardsPb.GetRewardsCountRequest{
					ActorId: "actor-1",
					Filters: &rewardsPb.GetRewardsCountRequest_Filters{
						AndFilter: &rewardsPb.GetRewardsCountRequest_FilterFields{
							FromTime: timestamppb.New(time.Date(2020, 9, 12, 0, 0, 0, 0, time.UTC)),
						},
						OrFilter: &rewardsPb.GetRewardsCountRequest_FilterFields{
							RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER, rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER},
							RewardTypes:      []rewardsPb.RewardType{rewardsPb.RewardType_CASH},
						},
					},
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao) {
				mockRewardsDao.EXPECT().GetRewardsCount(context.Background(), "actor-1", &model2.RewardsCountFilters{
					AndFilter: &model2.RewardsCountFilterFields{
						FromTime: time.Date(2020, 9, 12, 0, 0, 0, 0, time.UTC),
					},
					OrFilter: &model2.RewardsCountFilterFields{
						RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER, rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER},
						RewardTypes:      []rewardsPb.RewardType{rewardsPb.RewardType_CASH},
					},
				}).Return(uint32(2), nil)
			},
			want: &rewardsPb.GetRewardsCountResponse{
				Status:       rpc.StatusOk(),
				RewardsCount: 2,
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()
			mockRewardsDao := daoMock.NewMockRewardsDao(ctr)

			tt.setupMocks(mockRewardsDao)

			rgs := &RewardsService{
				rewardDao: mockRewardsDao,
			}
			got, err := rgs.GetRewardsCount(tt.args.ctx, tt.args.request)
			if !tt.wantErr(t, err, fmt.Sprintf("GetRewardsCount(%v, %v)", tt.args.ctx, tt.args.request)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetRewardsCount(%v, %v)", tt.args.ctx, tt.args.request)
		})
	}
}

func TestRewardsService_GetRewardUtilisationForActorAndOffer(t *testing.T) {

	timestampOfMonth := &timestamppb.Timestamp{
		Seconds: 1000000001,
	}

	type args struct {
		ctx context.Context
		req *rewardsPb.GetRewardUtilisationForActorAndOfferRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockRewardsDao *daoMock.MockRewardsDao, mockRewardOfferDao *daoMock.MockRewardOfferDao)
		want       *rewardsPb.GetRewardUtilisationForActorAndOfferResponse
		wantErr    bool
	}{
		{
			name: "should return global level units utilization and rewards count data when valid parameters are given to fetch global utilization",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest{
					ActorId:       "actor-1",
					RewardOfferId: "reward-offer-1",
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardOfferDao.EXPECT().FetchRewardOfferById(context.Background(), "reward-offer-1").Return(&rewardOffersPb.RewardOffer{
					Id: "reward-offer-1",
					RewardMeta: &rewardOffersPb.RewardMeta{
						RewardAggregates: &rewardOffersPb.RewardAggregates{
							RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{},
						},
					},
				}, nil)
				mockRewardOfferDao.EXPECT().GetRewardUnitsUtilisedForActor(context.Background(), "actor-1", "reward-offer-1").Return(&model2.RewardOfferRewardUnitsActorUtilisation{
					FiCoinUnits: sql.NullInt32{
						Int32: 100,
						Valid: true,
					},
				}, nil)
				mockRewardsDao.EXPECT().GetRewardsCount(context.Background(), "actor-1", &model2.RewardsCountFilters{
					AndFilter: &model2.RewardsCountFilterFields{
						RewardOfferIds: []string{
							"reward-offer-1",
						},
					},
				}).Return(uint32(100), nil)
			},
			want: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
				Status: rpc.StatusOk(),
				RewardOfferRewardUnitsUtilizationForActor: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor{
					FiCoinsUnits: 100,
				},
				TotalRewardsCount: 100,
			},
			wantErr: false,
		},
		{
			name: "should return month level units utilization and rewards count data when valid parameters are given to fetch utilization in month",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest{
					ActorId:       "actor-1",
					RewardOfferId: "reward-offer-1",
					Filters: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest_Filters{
						TimestampOfMonth: timestampOfMonth,
					},
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardOfferDao.EXPECT().FetchRewardOfferById(context.Background(), "reward-offer-1").Return(&rewardOffersPb.RewardOffer{
					Id: "reward-offer-1",
					RewardMeta: &rewardOffersPb.RewardMeta{
						RewardAggregates: &rewardOffersPb.RewardAggregates{
							RewardUnitsCapMonthlyUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{},
						},
					},
				}, nil)
				mockRewardsDao.EXPECT().GetMonthlyRewardUnitsUtilisedForActorAndOffer(context.Background(), "actor-1", "reward-offer-1", timestampOfMonth.AsTime()).Return(&rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod{
					FiCoinUnits: 100,
				}, nil)
				mockRewardsDao.EXPECT().GetRewardsCount(context.Background(), "actor-1", &model2.RewardsCountFilters{
					AndFilter: &model2.RewardsCountFilterFields{
						RewardOfferIds: []string{
							"reward-offer-1",
						},
					},
				}).Return(uint32(100), nil)
			},
			want: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
				Status: rpc.StatusOk(),
				RewardOfferRewardUnitsUtilizationForActor: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor{
					FiCoinsUnits: 100,
				},
				TotalRewardsCount: 100,
			},
			wantErr: false,
		},
		{
			name: "should return month level units utilization and rewards count data when valid parameters are given to fetch utilization in month and GetMonthlyRewardUnitsUtilisedForActorAndOffer returns NotFound",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest{
					ActorId:       "actor-1",
					RewardOfferId: "reward-offer-1",
					Filters: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest_Filters{
						TimestampOfMonth: timestampOfMonth,
					},
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardOfferDao.EXPECT().FetchRewardOfferById(context.Background(), "reward-offer-1").Return(&rewardOffersPb.RewardOffer{
					Id: "reward-offer-1",
					RewardMeta: &rewardOffersPb.RewardMeta{
						RewardAggregates: &rewardOffersPb.RewardAggregates{
							RewardUnitsCapMonthlyUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{},
						},
					},
				}, nil)
				mockRewardsDao.EXPECT().GetMonthlyRewardUnitsUtilisedForActorAndOffer(context.Background(), "actor-1", "reward-offer-1", timestampOfMonth.AsTime()).Return(nil, epifierrors.ErrRecordNotFound)
				mockRewardsDao.EXPECT().GetRewardsCount(context.Background(), "actor-1", &model2.RewardsCountFilters{
					AndFilter: &model2.RewardsCountFilterFields{
						RewardOfferIds: []string{
							"reward-offer-1",
						},
					},
				}).Return(uint32(100), nil)
			},
			want: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
				Status: rpc.StatusOk(),
				RewardOfferRewardUnitsUtilizationForActor: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor{},
				TotalRewardsCount:                         100,
			},
			wantErr: false,
		},
		{
			name: "should return only rewards count data when valid parameters but no RewardUnitsCapMonthlyUserAggregate is set in reward offer",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest{
					ActorId:       "actor-1",
					RewardOfferId: "reward-offer-1",
					Filters: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest_Filters{
						TimestampOfMonth: timestampOfMonth,
					},
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardOfferDao.EXPECT().FetchRewardOfferById(context.Background(), "reward-offer-1").Return(&rewardOffersPb.RewardOffer{
					Id: "reward-offer-1",
					RewardMeta: &rewardOffersPb.RewardMeta{
						RewardAggregates: &rewardOffersPb.RewardAggregates{},
					},
				}, nil)
				mockRewardsDao.EXPECT().GetRewardsCount(context.Background(), "actor-1", &model2.RewardsCountFilters{
					AndFilter: &model2.RewardsCountFilterFields{
						RewardOfferIds: []string{
							"reward-offer-1",
						},
					},
				}).Return(uint32(100), nil)
			},
			want: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
				Status:                               rpc.StatusOk(),
				IsCappingNotConfiguredForRewardOffer: true,
				TotalRewardsCount:                    100,
			},
			wantErr: false,
		},
		{
			name: "should return error when GetMonthlyRewardUnitsUtilisedForActorAndOffer call fails",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest{
					ActorId:       "actor-1",
					RewardOfferId: "reward-offer-1",
					Filters: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest_Filters{
						TimestampOfMonth: timestampOfMonth,
					},
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardOfferDao.EXPECT().FetchRewardOfferById(context.Background(), "reward-offer-1").Return(&rewardOffersPb.RewardOffer{
					Id: "reward-offer-1",
					RewardMeta: &rewardOffersPb.RewardMeta{
						RewardAggregates: &rewardOffersPb.RewardAggregates{
							RewardUnitsCapMonthlyUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{},
						},
					},
				}, nil)
				mockRewardsDao.EXPECT().GetMonthlyRewardUnitsUtilisedForActorAndOffer(context.Background(), "actor-1", "reward-offer-1", timestampOfMonth.AsTime()).Return(nil, fmt.Errorf(""))
			},
			want: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
				Status: rpc.StatusInternalWithDebugMsg("error in GetMonthlyRewardUnitsUtilisedForActorAndOffer dao"),
			},
			wantErr: false,
		},
		{
			name: "should return global level units utilization and rewards count data when valid parameters are given and GetRewardUnitsUtilisedForActor gives NotFound",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest{
					ActorId:       "actor-1",
					RewardOfferId: "reward-offer-1",
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardOfferDao.EXPECT().FetchRewardOfferById(context.Background(), "reward-offer-1").Return(&rewardOffersPb.RewardOffer{
					Id: "reward-offer-1",
					RewardMeta: &rewardOffersPb.RewardMeta{
						RewardAggregates: &rewardOffersPb.RewardAggregates{
							RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{},
						},
					},
				}, nil)
				mockRewardOfferDao.EXPECT().GetRewardUnitsUtilisedForActor(context.Background(), "actor-1", "reward-offer-1").Return(nil, epifierrors.ErrRecordNotFound)
				mockRewardsDao.EXPECT().GetRewardsCount(context.Background(), "actor-1", &model2.RewardsCountFilters{
					AndFilter: &model2.RewardsCountFilterFields{
						RewardOfferIds: []string{
							"reward-offer-1",
						},
					},
				}).Return(uint32(100), nil)
			},
			want: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
				Status: rpc.StatusOk(),
				RewardOfferRewardUnitsUtilizationForActor: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse_RewardOfferRewardUnitsUtilizationForActor{},
				TotalRewardsCount:                         100,
			},
			wantErr: false,
		},
		{
			name: "should return only TotalRewardCount when requesting for global level utilization and no RewardUnitsCapUserAggregate is set for reward offer",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest{
					ActorId:       "actor-1",
					RewardOfferId: "reward-offer-1",
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardOfferDao.EXPECT().FetchRewardOfferById(context.Background(), "reward-offer-1").Return(&rewardOffersPb.RewardOffer{
					Id: "reward-offer-1",
				}, nil)
				mockRewardsDao.EXPECT().GetRewardsCount(context.Background(), "actor-1", &model2.RewardsCountFilters{
					AndFilter: &model2.RewardsCountFilterFields{
						RewardOfferIds: []string{
							"reward-offer-1",
						},
					},
				}).Return(uint32(100), nil)
			},
			want: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
				Status:                               rpc.StatusOk(),
				IsCappingNotConfiguredForRewardOffer: true,
				TotalRewardsCount:                    100,
			},
			wantErr: false,
		},
		{
			name: "should return error when FetchRewardOfferById dao call fails",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest{
					ActorId:       "actor-1",
					RewardOfferId: "reward-offer-1",
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardOfferDao.EXPECT().FetchRewardOfferById(context.Background(), "reward-offer-1").Return(nil, fmt.Errorf(""))
			},
			want: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
				Status: rpc.StatusInternalWithDebugMsg("error in FetchRewardOfferById dao"),
			},
			wantErr: false,
		},
		{
			name: "should return StatusRecordNotFound when FetchRewardOfferById returns not found",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest{
					ActorId:       "actor-1",
					RewardOfferId: "reward-offer-1",
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardOfferDao.EXPECT().FetchRewardOfferById(context.Background(), "reward-offer-1").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg("error ErrRecordNotFound in FetchRewardOfferById dao"),
			},
			wantErr: false,
		},
		{
			name: "should return error when GetRewardUnitsUtilisedForActor dao call fails",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest{
					ActorId:       "actor-1",
					RewardOfferId: "reward-offer-1",
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardOfferDao.EXPECT().FetchRewardOfferById(context.Background(), "reward-offer-1").Return(&rewardOffersPb.RewardOffer{
					Id: "reward-offer-1",
					RewardMeta: &rewardOffersPb.RewardMeta{
						RewardAggregates: &rewardOffersPb.RewardAggregates{
							RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{},
						},
					},
				}, nil)
				mockRewardOfferDao.EXPECT().GetRewardUnitsUtilisedForActor(context.Background(), "actor-1", "reward-offer-1").Return(nil, fmt.Errorf(""))
			},
			want: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
				Status: rpc.StatusInternalWithDebugMsg("error in GetRewardUnitsUtilisedForActor dao"),
			},
			wantErr: false,
		},
		{
			name: "should return error when GetRewardsCount dao call fails",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetRewardUtilisationForActorAndOfferRequest{
					ActorId:       "actor-1",
					RewardOfferId: "reward-offer-1",
				},
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardOfferDao.EXPECT().FetchRewardOfferById(context.Background(), "reward-offer-1").Return(&rewardOffersPb.RewardOffer{
					Id: "reward-offer-1",
					RewardMeta: &rewardOffersPb.RewardMeta{
						RewardAggregates: &rewardOffersPb.RewardAggregates{
							RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{},
						},
					},
				}, nil)
				mockRewardOfferDao.EXPECT().GetRewardUnitsUtilisedForActor(context.Background(), "actor-1", "reward-offer-1").Return(&model2.RewardOfferRewardUnitsActorUtilisation{}, nil)
				mockRewardsDao.EXPECT().GetRewardsCount(context.Background(), "actor-1", &model2.RewardsCountFilters{
					AndFilter: &model2.RewardsCountFilterFields{
						RewardOfferIds: []string{
							"reward-offer-1",
						},
					},
				}).Return(uint32(0), fmt.Errorf(""))
			},
			want: &rewardsPb.GetRewardUtilisationForActorAndOfferResponse{
				Status: rpc.StatusInternalWithDebugMsg("error in GetRewardsCount dao"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			ctr := gomock.NewController(t)
			mockRewardsDao := daoMock.NewMockRewardsDao(ctr)
			mockRewardOfferDao := daoMock.NewMockRewardOfferDao(ctr)

			rgs := &RewardsService{
				rewardDao:      mockRewardsDao,
				rewardOfferDao: mockRewardOfferDao,
			}

			// setupMocks
			tt.setupMocks(mockRewardsDao, mockRewardOfferDao)

			got, err := rgs.GetRewardUtilisationForActorAndOffer(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRewardUnitsUtilisationForActorAndOffer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetRewardUnitsUtilisationForActorAndOffer() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// nolint: govet
func TestNewRewardsGeneratorService_BulkClaimRewardsWithDefaultOptionV2(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	rewardsGenConf, _ := genconf.Load()
	type args struct {
		ctx context.Context
		req *rewardsPb.BulkClaimRewardsWithDefaultOptionV2Request
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(cacheStoreMock *mockCache.MockCacheStorage, mockPublisher *mock_queue.MockPublisher)
		want           *rewardsPb.BulkClaimRewardsWithDefaultOptionV2Response
		wantErr        bool
	}{
		{
			name: "publish bulk claim rewards event successfully",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.BulkClaimRewardsWithDefaultOptionV2Request{ActorId: "actor-1"},
			},
			setupMockCalls: func(cacheStoreMock *mockCache.MockCacheStorage, mockPublisher *mock_queue.MockPublisher) {
				cacheStoreMock.EXPECT().Get(gomock.Any(), "rewards:bulk_claim_rewards:actor-1").Return("", epifierrors.ErrRecordNotFound)
				cacheStoreMock.EXPECT().Set(gomock.Any(), "rewards:bulk_claim_rewards:actor-1", "rewards:bulk_claim_rewards:actor-1", time.Duration(1440)*time.Minute).Return(nil)
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("msg_id", nil).AnyTimes()
			},
			want:    &rewardsPb.BulkClaimRewardsWithDefaultOptionV2Response{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "failure: error in publishing events",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.BulkClaimRewardsWithDefaultOptionV2Request{ActorId: "actor-1"},
			},
			setupMockCalls: func(cacheStoreMock *mockCache.MockCacheStorage, mockPublisher *mock_queue.MockPublisher) {
				cacheStoreMock.EXPECT().Get(gomock.Any(), "rewards:bulk_claim_rewards:actor-1").Return("", epifierrors.ErrRecordNotFound).AnyTimes()
				cacheStoreMock.EXPECT().Set(gomock.Any(), "rewards:bulk_claim_rewards:actor-1", "rewards:bulk_claim_rewards:actor-1", time.Duration(1440)*time.Minute).Return(nil).AnyTimes()
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", errors.New("unable to publish reward to processing queue"))
			},
			want:    &rewardsPb.BulkClaimRewardsWithDefaultOptionV2Response{Status: rpc.StatusInternalWithDebugMsg("unable to publish reward to processing queue")},
			wantErr: false,
		},
		{
			name: "failure: error in redis lock - already lock exists",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.BulkClaimRewardsWithDefaultOptionV2Request{ActorId: "actor-1"},
			},
			setupMockCalls: func(cacheStoreMock *mockCache.MockCacheStorage, mockPublisher *mock_queue.MockPublisher) {
				cacheStoreMock.EXPECT().Get(gomock.Any(), "rewards:bulk_claim_rewards:actor-1").Return("", nil).AnyTimes()
			},
			want:    &rewardsPb.BulkClaimRewardsWithDefaultOptionV2Response{Status: rpc.StatusAlreadyExistsWithDebugMsg("actor has already acquired lock")},
			wantErr: false,
		},
		{
			name: "failure: error in redis lock",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.BulkClaimRewardsWithDefaultOptionV2Request{ActorId: "actor-1"},
			},
			setupMockCalls: func(cacheStoreMock *mockCache.MockCacheStorage, mockPublisher *mock_queue.MockPublisher) {
				cacheStoreMock.EXPECT().Get(gomock.Any(), "rewards:bulk_claim_rewards:actor-1").Return("", epifierrors.ErrRecordNotFound).AnyTimes()
				cacheStoreMock.EXPECT().Set(gomock.Any(), "rewards:bulk_claim_rewards:actor-1", "rewards:bulk_claim_rewards:actor-1", time.Duration(1440)*time.Minute).Return(errors.New("insert error")).AnyTimes()
			},
			want:    &rewardsPb.BulkClaimRewardsWithDefaultOptionV2Response{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error while inserting the bulk claim rewards key in cache, err: %v", errors.New("insert error")))},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockPublisher := mock_queue.NewMockPublisher(ctr)
			cacheStoreMock := mockCache.NewMockCacheStorage(ctr)
			rgs := &RewardsService{
				bulkClaimRewardsEventPublisher: mockPublisher,
				cacheStorage:                   cacheStoreMock,
				dyconf:                         rewardsGenConf,
			}
			tt.setupMockCalls(cacheStoreMock, mockPublisher)
			got, err := rgs.BulkClaimRewardsWithDefaultOptionV2(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BulkClaimRewardsWithDefaultOptionV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BulkClaimRewardsWithDefaultOptionV2()\n got %v\nwant %v", got, tt.want)
			}
		})
	}
}

// nolint: govet
func TestRewardsService_GetBulkClaimRewardsProcessingState(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	rewardsGenConf, _ := genconf.Load()
	type args struct {
		ctx context.Context
		req *rewardsPb.GetBulkClaimRewardsProcessingStateRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(cacheStoreMock *mockCache.MockCacheStorage)
		want           *rewardsPb.GetBulkClaimRewardsProcessingStateResponse
		wantErr        bool
	}{
		{
			name: "success: error in redis lock - already lock exists",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetBulkClaimRewardsProcessingStateRequest{ActorId: "actor-1"},
			},
			setupMockCalls: func(cacheStoreMock *mockCache.MockCacheStorage) {
				cacheStoreMock.EXPECT().Get(gomock.Any(), "rewards:bulk_claim_rewards:actor-1").Return("", nil).AnyTimes()
			},
			want: &rewardsPb.GetBulkClaimRewardsProcessingStateResponse{
				Status:            rpc.StatusOk(),
				IsProcessingState: true,
			},
			wantErr: false,
		},
		{
			name: "success: error in redis lock",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetBulkClaimRewardsProcessingStateRequest{ActorId: "actor-1"},
			},
			setupMockCalls: func(cacheStoreMock *mockCache.MockCacheStorage) {
				cacheStoreMock.EXPECT().Get(gomock.Any(), "rewards:bulk_claim_rewards:actor-1").Return("", epifierrors.ErrRecordNotFound)
			},
			want: &rewardsPb.GetBulkClaimRewardsProcessingStateResponse{
				Status:            rpc.StatusOk(),
				IsProcessingState: false,
			},
			wantErr: false,
		},
		{
			name: "failure: error in redis lock",
			args: args{
				ctx: context.Background(),
				req: &rewardsPb.GetBulkClaimRewardsProcessingStateRequest{ActorId: "actor-1"},
			},
			setupMockCalls: func(cacheStoreMock *mockCache.MockCacheStorage) {
				cacheStoreMock.EXPECT().Get(gomock.Any(), "rewards:bulk_claim_rewards:actor-1").Return("", epifierrors.ErrPermanent)
			},
			want: &rewardsPb.GetBulkClaimRewardsProcessingStateResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while checking bulk rewards processing state"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			cacheStoreMock := mockCache.NewMockCacheStorage(ctr)
			rgs := &RewardsService{
				cacheStorage: cacheStoreMock,
				dyconf:       rewardsGenConf,
			}
			tt.setupMockCalls(cacheStoreMock)
			got, err := rgs.GetBulkClaimRewardsProcessingState(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBulkClaimRewardsProcessingState() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetBulkClaimRewardsProcessingState() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRewardsService_GetAllRewardsAndProjection(t *testing.T) {
	ctx := context.Background()
	req := &rewardsPb.GetAllRewardsAndProjectionRequest{
		ActorId:    "actor-1",
		ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION,
		RefIds:     []string{"ref-1", "ref-2", "ref-3", "ref-4"},
	}
	fetchRewardsFilter := &model.QueryRewardsFilterV2{
		AndFilter: &model.AndRewardsFilter{
			ActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION},
			RefIds:      []string{"ref-1", "ref-2", "ref-3", "ref-4"},
			Statuses:    []rewardsPb.RewardStatus{rewardsPb.RewardStatus_PROCESSED, rewardsPb.RewardStatus_PROCESSING_PENDING, rewardsPb.RewardStatus_PROCESSING_IN_PROGRESS},
		},
	}
	fetchProjectionsFilter := &model3.QueryProjectionsFilter{
		AndFilter: &model3.AndProjectionFilter{
			ActorId:     "actor-1",
			ActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION},
			RefIds:      []string{"ref-1", "ref-2", "ref-3", "ref-4"},
		},
	}
	pageSize := 50
	tests := []struct {
		name       string
		setupMocks func(mockRewardsDao *daoMock.MockRewardsDao, mockProjectionDao *projectorDaoMock.MockRewardsProjectionDao, mockRewardOfferDao *daoMock.MockRewardOfferDao)
		want       *rewardsPb.GetAllRewardsAndProjectionResponse
		wantErr    assert.ErrorAssertionFunc
	}{
		{
			name: "should return error when error occurs while fetching rewards",
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockProjectionDao *projectorDaoMock.MockRewardsProjectionDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(ctx, "actor-1", fetchRewardsFilter, nil, pageSize).Return(nil, nil, fmt.Errorf("err"))
			},
			want: &rewardsPb.GetAllRewardsAndProjectionResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching rewards"),
			},
			wantErr: assert.NoError,
		},
		{
			name: "should return error when error occurs while fetching projections",
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockProjectionDao *projectorDaoMock.MockRewardsProjectionDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(ctx, "actor-1", fetchRewardsFilter, nil, pageSize).Return([]*model.Reward{
					{RefId: "ref-1", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
					{RefId: "ref-2", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
					{RefId: "ref-3", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
					{RefId: "ref-4", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
				}, nil, nil)
				mockProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(ctx, fetchProjectionsFilter, nil, pageSize, nil).Return(nil, nil, fmt.Errorf("err"))
			},
			want: &rewardsPb.GetAllRewardsAndProjectionResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching projections"),
			},
			wantErr: assert.NoError,
		},
		{
			name: "should return rewards against given ref_ids if only rewards exist, and no projections",
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockProjectionDao *projectorDaoMock.MockRewardsProjectionDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(ctx, "actor-1", fetchRewardsFilter, nil, pageSize).Return([]*model.Reward{
					{Id: "rwd-1", RefId: "ref-1", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
					{Id: "rwd-2", RefId: "ref-2", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
					{Id: "rwd-3", RefId: "ref-3", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
					{Id: "rwd-4", RefId: "ref-4", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
				}, nil, nil)
				mockProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(ctx, fetchProjectionsFilter, nil, pageSize, nil).Return(nil, nil, nil)
			},
			want: &rewardsPb.GetAllRewardsAndProjectionResponse{
				Status: rpc.StatusOk(),
				RefIdToRewardEntitiesMap: map[string]*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities{
					"ref-1": {
						RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
							{
								EntityId:         "rwd-1",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(200)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD,
							},
						},
					},
					"ref-2": {
						RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
							{
								EntityId:         "rwd-2",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(200)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD,
							},
						},
					},
					"ref-3": {
						RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
							{
								EntityId:         "rwd-3",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(200)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD,
							},
						},
					},
					"ref-4": {
						RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
							{
								EntityId:         "rwd-4",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(200)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD,
							},
						},
					},
				},
			},
			wantErr: assert.NoError,
		},
		{
			name: "should return projections against given ref_ids if only projections exist, and no rewards",
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockProjectionDao *projectorDaoMock.MockRewardsProjectionDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(ctx, "actor-1", fetchRewardsFilter, nil, pageSize).Return(nil, nil, nil)
				mockProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(ctx, fetchProjectionsFilter, nil, pageSize, nil).Return([]*rewardsProjectionPb.Projection{
					{Id: "pjn-1", ActorId: "actor-1", RefId: "ref-1", OfferId: "offer-1", ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, ActionTime: timestamppb.Now()},
					{Id: "pjn-2", ActorId: "actor-1", RewardId: "rw1", RefId: "ref-2", OfferId: "offer-1", ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 150}}}, ActionTime: timestamppb.Now()},
					{Id: "pjn-3", ActorId: "actor-1", RefId: "ref-3", OfferId: "offer-1", ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, ActionTime: timestamppb.Now()},
					{Id: "pjn-4", ActorId: "actor-1", RewardId: "rw1", RefId: "ref-4", OfferId: "offer-1", ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100}}}, ActionTime: timestamppb.Now()},
				}, nil, nil)
				mockRewardOfferDao.EXPECT().FetchRewardOffersByIds(ctx, []string{"offer-1"}).Return([]*rewardOffersPb.RewardOffer{
					{
						Id:          "offer-1",
						ActiveSince: "2006-01-02T15:04:05Z",
						ActiveTill:  "2060-01-02T15:04:05Z",
					},
				}, nil)
				mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(ctx, "actor-1", &model.QueryRewardsFilterV2{AndFilter: &model.AndRewardsFilter{RewardIds: []string{"rw1"}}}, nil, 50).Return([]*model.Reward{
					{Id: "rwd-1", RefId: "ref-1", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
				}, nil, nil)
			},
			want: &rewardsPb.GetAllRewardsAndProjectionResponse{
				Status: rpc.StatusOk(),
				RefIdToRewardEntitiesMap: map[string]*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities{
					"ref-1": {
						RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
							{
								EntityId:         "pjn-1",
								OfferId:          "offer-1",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(200)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_PROJECTED_REWARD,
							},
						},
					},
					"ref-2": {
						RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
							{
								EntityId:         "pjn-2",
								OfferId:          "offer-1",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(150)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_ACTUALISED_PROJECTED_REWARD,
							},
						},
					},
					"ref-3": {
						RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
							{
								EntityId:         "pjn-3",
								OfferId:          "offer-1",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(200)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_PROJECTED_REWARD,
							},
						},
					},
					"ref-4": {
						RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
							{
								EntityId:         "pjn-4",
								OfferId:          "offer-1",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(100)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_ACTUALISED_PROJECTED_REWARD,
							},
						},
					},
				},
			},
			wantErr: assert.NoError,
		},
		{
			name: "should return rewards & projections both against given ref_ids if both exist",
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao, mockProjectionDao *projectorDaoMock.MockRewardsProjectionDao, mockRewardOfferDao *daoMock.MockRewardOfferDao) {
				mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(ctx, "actor-1", fetchRewardsFilter, nil, pageSize).Return([]*model.Reward{
					{Id: "rwd-1", RefId: "ref-1", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
					{Id: "rwd-2", RefId: "ref-2", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
					{Id: "rwd-3", RefId: "ref-3", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
				}, nil, nil)
				mockProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(ctx, fetchProjectionsFilter, nil, pageSize, nil).Return([]*rewardsProjectionPb.Projection{
					{Id: "pjn-2", ActorId: "actor-1", RewardId: "rw1", RefId: "ref-2", OfferId: "offer-1", ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 150}}}, ActionTime: timestamppb.Now()},
					{Id: "pjn-3", ActorId: "actor-1", RefId: "ref-3", OfferId: "offer-1", ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, ActionTime: timestamppb.Now()},
					{Id: "pjn-4", ActorId: "actor-1", RewardId: "rw1", RefId: "ref-4", OfferId: "offer-1", ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100}}}, ActionTime: timestamppb.Now()},
				}, nil, nil)
				mockRewardOfferDao.EXPECT().FetchRewardOffersByIds(ctx, []string{"offer-1"}).Return([]*rewardOffersPb.RewardOffer{
					{
						Id:          "offer-1",
						ActiveSince: "2006-01-02T15:04:05Z",
						ActiveTill:  "2060-01-02T15:04:05Z",
					},
				}, nil)
				mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(ctx, "actor-1", &model.QueryRewardsFilterV2{AndFilter: &model.AndRewardsFilter{RewardIds: []string{"rw1"}}}, nil, 50).Return([]*model.Reward{
					{Id: "rwd-1", RefId: "ref-1", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
				}, nil, nil)
			},
			want: &rewardsPb.GetAllRewardsAndProjectionResponse{
				Status: rpc.StatusOk(),
				RefIdToRewardEntitiesMap: map[string]*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities{
					"ref-1": {
						RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
							{
								EntityId:         "rwd-1",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(200)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD,
							},
						},
					},
					"ref-2": {
						RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
							{
								EntityId:         "rwd-2",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(200)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD,
							},
							{
								EntityId:         "pjn-2",
								OfferId:          "offer-1",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(150)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_ACTUALISED_PROJECTED_REWARD,
							},
						},
					},
					"ref-3": {
						RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
							{
								EntityId:         "rwd-3",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(200)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD,
							},
							{
								EntityId:         "pjn-3",
								OfferId:          "offer-1",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(200)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_PROJECTED_REWARD,
							},
						},
					},
					"ref-4": {
						RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
							{
								EntityId:         "pjn-4",
								OfferId:          "offer-1",
								RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(100)}},
								RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_ACTUALISED_PROJECTED_REWARD,
							},
						},
					},
				},
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockRewardsDao := daoMock.NewMockRewardsDao(ctr)
			mockProjectionDao := projectorDaoMock.NewMockRewardsProjectionDao(ctr)
			mockRewardOfferDao := daoMock.NewMockRewardOfferDao(ctr)

			tt.setupMocks(mockRewardsDao, mockProjectionDao, mockRewardOfferDao)

			rgs := &RewardsService{
				rewardDao:      mockRewardsDao,
				projectionsDao: mockProjectionDao,
				rewardOfferDao: mockRewardOfferDao,
			}
			got, err := rgs.GetAllRewardsAndProjections(ctx, req)
			if !tt.wantErr(t, err, fmt.Sprintf("GetAllRewardsAndProjections(%v, %v)", ctx, req)) {
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetAllRewardsAndProjections(%v, %v) got = %v, want = %v, diff = %v", ctx, req, got, tt.want, diff)
			}
		})
	}
}

func TestRewardsService_GetRewardAggregates(t *testing.T) {
	var (
		req = &rewardsPb.GetRewardAggregatesRequest{
			ActorId: "act-1",
			Filter: &rewardsPb.GetRewardAggregatesRequest_Filter{
				TimeWindows: []*rewardsPb.TimeWindow{
					{
						FromTime: timestamppb.New(time.Date(2023, 11, 1, 0, 0, 0, 0, datetime.IST)),
						TillTime: timestamppb.New(time.Date(2023, 12, 1, 0, 0, 0, 0, datetime.IST)),
					},
					{
						FromTime: timestamppb.New(time.Date(2024, 2, 1, 0, 0, 0, 0, datetime.IST)),
						TillTime: timestamppb.New(time.Date(2024, 3, 1, 0, 0, 0, 0, datetime.IST)),
					},
				},
			},
		}
	)

	type args struct {
		req *rewardsPb.GetRewardAggregatesRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockRewardsDao *daoMock.MockRewardsDao)
		want       *rewardsPb.GetRewardAggregatesResponse
		wantErr    assert.ErrorAssertionFunc
	}{
		{
			name: "should return correct aggregates",
			args: args{
				req: req,
			},
			setupMocks: func(mockRewardsDao *daoMock.MockRewardsDao) {
				mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(context.Background(), "act-1", gomock.Any(), nil, 30).
					Return([]*model.Reward{
						{Id: "rwd-1", RefId: "ref-1", RewardType: rewardsPb.RewardType_FI_COINS, ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
						{Id: "rwd-2", RefId: "ref-2", RewardType: rewardsPb.RewardType_FI_COINS, ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
						{Id: "rwd-3", RefId: "ref-3", RewardType: rewardsPb.RewardType_FI_COINS, ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
					}, nil, nil).Times(1)
				mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(context.Background(), "act-1", gomock.Any(), nil, 30).
					Return([]*model.Reward{
						{Id: "rwd-4", RefId: "ref-4", RewardType: rewardsPb.RewardType_FI_COINS, ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
						{Id: "rwd-5", RefId: "ref-5", RewardType: rewardsPb.RewardType_FI_COINS, ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
						{Id: "rwd-6", RefId: "ref-6", RewardType: rewardsPb.RewardType_FI_COINS, ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: 200}}}},
					}, nil, nil).Times(1)
			},
			wantErr: assert.NoError,
			want: &rewardsPb.GetRewardAggregatesResponse{
				Status: rpc.StatusOk(),
				Aggregates: []*rewardsPb.RewardOptionMinimal{
					{RewardType: rewardsPb.RewardType_FI_COINS, Units: 1200},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockRewardsDao := daoMock.NewMockRewardsDao(ctr)

			tt.setupMocks(mockRewardsDao)
			rgs := &RewardsService{
				rewardDao: mockRewardsDao,
			}
			got, err := rgs.GetRewardAggregates(context.Background(), tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("GetRewardAggregates(%v, %v)", context.Background(), tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetRewardAggregates(%v, %v)", context.Background(), tt.args.req)
		})
	}
}
