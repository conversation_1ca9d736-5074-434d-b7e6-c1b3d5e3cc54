package luckydraw

import (
	"context"
	"sync"
	"time"

	"github.com/golang/protobuf/ptypes"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	luckyDrawPb "github.com/epifi/gamma/api/rewards/luckydraw"
	processorPb "github.com/epifi/gamma/api/rewards/processor"

	"github.com/epifi/gamma/rewards/luckydraw/dao"
	"github.com/epifi/gamma/rewards/luckydraw/distributionstrategy"
	"github.com/epifi/gamma/rewards/wire/types"
)

type LuckyDrawService struct {
	luckyDrawPb.UnimplementedLuckyDrawServiceServer
	campaignDao                 dao.LuckyDrawCampaignDao
	luckyDrawDao                dao.LuckyDrawDao
	registrationDao             dao.LuckyDrawRegistrationDao
	winningDao                  dao.LuckyDrawWinningDao
	txnExecutor                 storageV2.TxnExecutor
	winningProcessingPublisher  types.LuckyDrawWinningProcessingSqsPublisher
	distributionStrategyFactory distributionstrategy.IFactory
}

func NewLuckyDrawService(campaignDao dao.LuckyDrawCampaignDao, luckyDrawDao dao.LuckyDrawDao, registrationDao dao.LuckyDrawRegistrationDao, winningDao dao.LuckyDrawWinningDao, txnExecutor storageV2.TxnExecutor, winningProcessingPublisher types.LuckyDrawWinningProcessingSqsPublisher, distributionStrategyFactory distributionstrategy.IFactory) *LuckyDrawService {
	return &LuckyDrawService{campaignDao: campaignDao, luckyDrawDao: luckyDrawDao, registrationDao: registrationDao, winningDao: winningDao, txnExecutor: txnExecutor, winningProcessingPublisher: winningProcessingPublisher, distributionStrategyFactory: distributionStrategyFactory}
}

var _ luckyDrawPb.LuckyDrawServiceServer = &LuckyDrawService{}

// CreateLuckyDrawCampaign creates a lucky draw campaign.
func (l *LuckyDrawService) CreateLuckyDrawCampaign(ctx context.Context, request *luckyDrawPb.CreateLuckyDrawCampaignRequest) (*luckyDrawPb.CreateLuckyDrawCampaignResponse, error) {
	campaign, err := l.campaignDao.Create(ctx, request)
	if err != nil {
		logger.Error(ctx, "error creating lucky draw campaign", zap.Any(logger.REQUEST, request), zap.Error(err))
		return &luckyDrawPb.CreateLuckyDrawCampaignResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &luckyDrawPb.CreateLuckyDrawCampaignResponse{
		Status:            rpc.StatusOk(),
		LuckyDrawCampaign: campaign,
	}, nil
}

// CreateLuckyDraw creates a lucky draw.
func (l *LuckyDrawService) CreateLuckyDraw(ctx context.Context, req *luckyDrawPb.CreateLuckyDrawRequest) (*luckyDrawPb.CreateLuckyDrawResponse, error) {
	// validate if campaign (present in the request) exists or not
	_, err := l.campaignDao.GetById(ctx, req.GetCampaignId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "error creating lucky draw : invalid campaign id", zap.String(logger.LUCKY_DRAW_ID, req.GetCampaignId()), zap.Error(err))
		return &luckyDrawPb.CreateLuckyDrawResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("campaign id is invalid"),
		}, nil
	case err != nil:
		logger.Error(ctx, "error fetching lucky draw campaign by id", zap.String(logger.CAMPAIGN_ID, req.GetCampaignId()), zap.Error(err))
		return &luckyDrawPb.CreateLuckyDrawResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	luckyDraw, err := l.luckyDrawDao.Create(ctx, req)
	if err != nil {
		logger.Error(ctx, "error creating lucky draw", zap.Any(logger.REQUEST, req), zap.Error(err))
		return &luckyDrawPb.CreateLuckyDrawResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &luckyDrawPb.CreateLuckyDrawResponse{
		Status:    rpc.StatusOk(),
		LuckyDraw: luckyDraw,
	}, nil
}

func (l *LuckyDrawService) GetLuckyDrawById(ctx context.Context, req *luckyDrawPb.GetLuckyDrawByIdRequest) (*luckyDrawPb.GetLuckyDrawByIdResponse, error) {
	luckyDraw, err := l.luckyDrawDao.GetById(ctx, req.GetId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "cannot fetch luckyDraw, invalid lucky draw id", zap.String(logger.LUCKY_DRAW_ID, req.GetId()), zap.Error(err))
		return &luckyDrawPb.GetLuckyDrawByIdResponse{
			Status: rpc.StatusRecordNotFoundWithDebugMsg("lucky draw id is invalid"),
		}, nil
	case err != nil:
		logger.Error(ctx, "error fetching lucky draw by id", zap.String(logger.LUCKY_DRAW_ID, req.GetId()), zap.Error(err))
		return &luckyDrawPb.GetLuckyDrawByIdResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching lucky draw by id"),
		}, nil
	}

	return &luckyDrawPb.GetLuckyDrawByIdResponse{
		Status:    rpc.StatusOk(),
		LuckyDraw: luckyDraw,
	}, nil
}

// nolint: funlen
// RegisterForLuckyDraw allows user to register for a lucky draw.
func (l *LuckyDrawService) RegisterForLuckyDraw(ctx context.Context, req *luckyDrawPb.RegisterForLuckyDrawRequest) (*luckyDrawPb.RegisterForLuckyDrawResponse, error) {
	// validate if lucky draw (present in the req) exists or not
	luckyDraw, err := l.luckyDrawDao.GetById(ctx, req.GetLuckyDrawId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "error registering for lucky draw : invalid lucky draw id", zap.String(logger.LUCKY_DRAW_ID, req.GetLuckyDrawId()), zap.Error(err))
		return &luckyDrawPb.RegisterForLuckyDrawResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("lucky draw id is invalid"),
		}, nil
	case err != nil:
		logger.Error(ctx, "error fetching lucky draw by id", zap.String(logger.LUCKY_DRAW_ID, req.GetLuckyDrawId()), zap.Error(err))
		return &luckyDrawPb.RegisterForLuckyDrawResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching lucky draw by id : " + err.Error()),
		}, nil
	}

	// check if already registered
	alreadyInitiatedRegistration, err := l.registrationDao.GetByLuckyDrawIdAndActorId(ctx, req.GetLuckyDrawId(), req.GetActorId())
	switch {
	// if registration record is not found then continue with registration flow
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "not already registered for lucky draw", zap.String(logger.LUCKY_DRAW_ID, req.GetLuckyDrawId()), zap.String(logger.ACTOR_ID, req.GetActorId()))
		// if err return internal server error
	case err != nil:
		logger.Error(ctx, "error checking for user registration in lucky draw", zap.String(logger.LUCKY_DRAW_ID, req.GetLuckyDrawId()), zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(err))
		return &luckyDrawPb.RegisterForLuckyDrawResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
		// if registration record found then user was already registered, so just return with registration details
	case alreadyInitiatedRegistration != nil:
		return &luckyDrawPb.RegisterForLuckyDrawResponse{
			Status:                  rpc.ExtendedStatusAlreadyProcessed(),
			LuckyDrawRegistrationId: alreadyInitiatedRegistration.GetId(),
		}, nil
	}

	// validate if registration schedule of lucky draw hasn't passed yet
	registrationTill, err := ptypes.Timestamp(luckyDraw.GetRegistrationTill())
	if err != nil {
		logger.Error(ctx, "error converting registration till proto timestamp to time", zap.Any(logger.REQUEST, req), zap.Error(err))
		return &luckyDrawPb.RegisterForLuckyDrawResponse{
			Status: rpc.StatusInternalWithDebugMsg("error parsing lucky draw registration till timestamp"),
		}, nil
	}

	if !time.Now().Before(registrationTill) {
		logger.Error(ctx, "error registering for lucky draw : registrations are closed", zap.Any(logger.REQUEST, req), zap.Error(err))
		return &luckyDrawPb.RegisterForLuckyDrawResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("registrations are closed"),
		}, nil
	}

	registration, err := l.registrationDao.Create(ctx, req)
	if err != nil {
		logger.Error(ctx, "error creating registration entry in db", zap.Any(logger.REQUEST, req), zap.Error(err))
		return &luckyDrawPb.RegisterForLuckyDrawResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &luckyDrawPb.RegisterForLuckyDrawResponse{
		Status:                  rpc.StatusOk(),
		LuckyDrawRegistrationId: registration.GetId(),
	}, nil
}

func (l *LuckyDrawService) RevealLuckyDraw(ctx context.Context, req *luckyDrawPb.RevealLuckyDrawRequest) (*luckyDrawPb.RevealLuckyDrawResponse, error) {
	if err := l.revealLuckyDrawWinnings(context.Background(), req.GetLuckyDrawId()); err != nil {
		logger.Error(ctx, "error revealing lucky draw", zap.Any(logger.REQUEST, req), zap.Error(err))
		return &luckyDrawPb.RevealLuckyDrawResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// todo (utkarsh) : asynchronously auto process selected winnings (if required) based on some flag.

	return &luckyDrawPb.RevealLuckyDrawResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// CheckRegistrationStatus returns whether a user is registered or not for a lucky draw.
func (l *LuckyDrawService) CheckRegistrationStatus(ctx context.Context, req *luckyDrawPb.CheckRegistrationStatusRequest) (*luckyDrawPb.CheckRegistrationStatusResponse, error) {
	// check if already registered
	alreadyInitiatedRegistration, err := l.registrationDao.GetByLuckyDrawIdAndActorId(ctx, req.GetLuckyDrawId(), req.GetActorId())
	switch {
	// if registration record is not found then continue with registration flow
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "not already registered for lucky draw", zap.String(logger.LUCKY_DRAW_ID, req.GetLuckyDrawId()), zap.String(logger.ACTOR_ID, req.GetActorId()))
		return &luckyDrawPb.CheckRegistrationStatusResponse{
			Status:             rpc.StatusOk(),
			RegistrationStatus: luckyDrawPb.RegistrationStatus_NOT_REGISTERED,
		}, nil
		// if err return internal server error
	case err != nil:
		logger.Error(ctx, "error checking for user registration in lucky draw", zap.String(logger.LUCKY_DRAW_ID, req.GetLuckyDrawId()), zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(err))
		return &luckyDrawPb.CheckRegistrationStatusResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	case alreadyInitiatedRegistration == nil:
		logger.Error(ctx, "error registration dao contract violation, registration record nil but NO RECORD FOUND err was not returned", zap.Any(logger.REQUEST, req))
		return &luckyDrawPb.CheckRegistrationStatusResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &luckyDrawPb.CheckRegistrationStatusResponse{
		Status:             rpc.StatusOk(),
		RegistrationStatus: luckyDrawPb.RegistrationStatus_REGISTERED,
		RegistrationId:     alreadyInitiatedRegistration.Id,
	}, nil
}

// GetLuckyDrawStatus returns status of lucky draw.
func (l *LuckyDrawService) GetLuckyDrawStatus(ctx context.Context, req *luckyDrawPb.GetLuckyDrawStatusRequest) (*luckyDrawPb.GetLuckyDrawStatusResponse, error) {
	// get lucky draw by id
	luckyDraw, err := l.luckyDrawDao.GetById(ctx, req.GetLuckyDrawId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "error registering for lucky draw : invalid lucky draw id", zap.String(logger.LUCKY_DRAW_ID, req.GetLuckyDrawId()), zap.Error(err))
		return &luckyDrawPb.GetLuckyDrawStatusResponse{
			Status: rpc.StatusRecordNotFoundWithDebugMsg(err.Error()),
		}, nil
	case err != nil:
		logger.Error(ctx, "error fetching lucky draw by id", zap.String(logger.LUCKY_DRAW_ID, req.GetLuckyDrawId()), zap.Error(err))
		return &luckyDrawPb.GetLuckyDrawStatusResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching lucky draw by id : " + err.Error()),
		}, nil
	}
	return &luckyDrawPb.GetLuckyDrawStatusResponse{
		Status:          rpc.StatusOk(),
		LuckyDrawStatus: luckyDraw.GetLuckyDrawStatus(),
	}, nil
}

// GetLuckyDrawWinning allows fetching a winning for a lucky draw by registration_id.
func (l *LuckyDrawService) GetLuckyDrawWinning(ctx context.Context, request *luckyDrawPb.GetLuckyDrawWinningRequest) (*luckyDrawPb.GetLuckyDrawWinningResponse, error) {
	winning, err := l.winningDao.GetByRegistrationId(ctx, request.GetLuckyDrawRegistrationId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "error fetching lucky draw winning by registration id : invalid registration id", zap.String(logger.LUCKY_DRAW_REG_ID, request.GetLuckyDrawRegistrationId()), zap.Error(err))
		return &luckyDrawPb.GetLuckyDrawWinningResponse{
			Status: rpc.StatusRecordNotFoundWithDebugMsg("invalid registration id"),
		}, nil
	case err != nil:
		logger.Error(ctx, "error fetching lucky draw winning by registration id", zap.String(logger.LUCKY_DRAW_REG_ID, request.GetLuckyDrawRegistrationId()), zap.Error(err))
		return &luckyDrawPb.GetLuckyDrawWinningResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &luckyDrawPb.GetLuckyDrawWinningResponse{
		Status:           rpc.StatusOk(),
		LuckyDrawWinning: winning,
	}, nil
}

// nolint: funlen
func (l *LuckyDrawService) ClaimWinningByRegistrationId(ctx context.Context, req *luckyDrawPb.ClaimWinningByRegIdRequest) (*luckyDrawPb.ClaimWinningByRegIdResponse, error) {
	// get registration entry
	registration, err := l.registrationDao.GetById(ctx, req.GetLuckyDrawRegistrationId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "registration not found", zap.String(logger.LUCKY_DRAW_REG_ID, req.GetLuckyDrawRegistrationId()), zap.Error(err))
		return &luckyDrawPb.ClaimWinningByRegIdResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("error claiming winning, registration not found"),
		}, nil
	case err != nil:
		logger.Error(ctx, "error fetching lucky draw registration by id", zap.String(logger.LUCKY_DRAW_REG_ID, req.GetLuckyDrawRegistrationId()), zap.Error(err))
		return &luckyDrawPb.ClaimWinningByRegIdResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	// check if lucky draw has completed
	luckyDraw, err := l.luckyDrawDao.GetById(ctx, registration.GetLuckyDrawId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "lucky draw not found", zap.String(logger.LUCKY_DRAW_ID, registration.GetLuckyDrawId()), zap.Error(err))
		return &luckyDrawPb.ClaimWinningByRegIdResponse{
			Status: rpc.StatusInternalWithDebugMsg("error claiming winning, registration not found"),
		}, nil
	case err != nil:
		logger.Error(ctx, "error fetching lucky draw by id", zap.String(logger.LUCKY_DRAW_ID, registration.GetLuckyDrawId()), zap.Error(err))
		return &luckyDrawPb.ClaimWinningByRegIdResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	if !isLuckyDrawRevealed(luckyDraw) {
		logger.Error(ctx, "cannot claim winning, lucky draw hasn't completed yet", zap.String(logger.LUCKY_DRAW_ID, registration.GetLuckyDrawId()), zap.Error(err))
		return &luckyDrawPb.ClaimWinningByRegIdResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("cannot claim winning, lucky draw hasn't completed yet"),
		}, nil
	}

	// get winning using registration id
	winning, err := l.winningDao.GetByRegistrationId(ctx, req.GetLuckyDrawRegistrationId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "winning not found for registration", zap.String(logger.LUCKY_DRAW_REG_ID, req.GetLuckyDrawRegistrationId()), zap.Error(err))
		return &luckyDrawPb.ClaimWinningByRegIdResponse{
			Status: rpc.StatusInternalWithDebugMsg("error claiming winning, winning not found for registration"),
		}, nil
	case err != nil:
		logger.Error(ctx, "error fetching winning by registration id", zap.String(logger.LUCKY_DRAW_REG_ID, req.GetLuckyDrawRegistrationId()), zap.Error(err))
		return &luckyDrawPb.ClaimWinningByRegIdResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	// claim winning
	if err1 := l.claimWinningUsingClaimMetadata(ctx, winning, req.GetClaimMetadata()); err1 != nil {
		logger.Error(ctx, "error claiming winning using winning id", zap.String(logger.WINNING_ID, winning.GetId()), zap.Error(err1))
		return &luckyDrawPb.ClaimWinningByRegIdResponse{
			Status: rpc.StatusInternalWithDebugMsg("error claiming winning using winning id " + err1.Error()),
		}, nil
	}

	// publish winning processing event
	msgId, err := l.winningProcessingPublisher.Publish(ctx, &processorPb.ProcessLuckyDrawWinningRequest{WinningId: winning.GetId()})
	if err != nil {
		logger.Error(ctx, "error publishing winning processing event", zap.String(logger.WINNING_ID, winning.GetId()), zap.Error(err))
		return &luckyDrawPb.ClaimWinningByRegIdResponse{
			Status: rpc.StatusInternalWithDebugMsg("error publishing winning processing event : " + err.Error()),
		}, nil
	}
	logger.Info(ctx, "successfully published winning processing event", zap.String(logger.WINNING_ID, winning.GetId()), zap.String(logger.QUEUE_MESSAGE_ID, msgId))

	// return ok status
	return &luckyDrawPb.ClaimWinningByRegIdResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// RevealPendingLuckyDraws reveals those lucky draws whose reveal schedule has already passed
// and they haven't been revealed yet.
// Steps Involved :
//  1. Fetch all lucky draws whose status is CREATED and reveal time has already passed.
//     CREATED status implies that lucky draw hasn't been revealed yet.
//  2. Reveal all the lucky draws fetched in previous step.
func (l *LuckyDrawService) RevealPendingLuckyDraws(ctx context.Context, _ *luckyDrawPb.RevealPendingLuckyDrawsRequest) (*luckyDrawPb.RevealPendingLuckyDrawsResponse, error) {
	// lucky draws whose status is created and reveal time has passed are pending to be revealed.
	pendingLuckyDraws, err := l.luckyDrawDao.GetByStatusAndRevealTimeBefore(ctx, luckyDrawPb.LuckyDrawStatus_LUCKY_DRAW_STATUS_CREATED, time.Now())
	if err != nil {
		logger.Error(ctx, "error fetching pending lucky draws", zap.Error(err))
		return &luckyDrawPb.RevealPendingLuckyDrawsResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching pending lucky draws : " + err.Error())}, nil
	}

	// reveal all pending lucky draws in parallel
	revealFailedLuckyDraws := make(chan string, len(pendingLuckyDraws))
	var wg sync.WaitGroup
	for _, luckyDraw := range pendingLuckyDraws {
		luckyDrawId := luckyDraw.GetId()

		wg.Add(1)
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			defer wg.Done()
			if err := l.revealLuckyDrawWinnings(ctx, luckyDrawId); err != nil {
				logger.Error(ctx, "error revealing lucky draw", zap.String(logger.LUCKY_DRAW_ID, luckyDrawId), zap.Error(err))
				revealFailedLuckyDraws <- luckyDrawId
			}
		})
	}
	waitgroup.SafeWaitCtx(ctx, &wg)

	if len(revealFailedLuckyDraws) > 0 {
		return &luckyDrawPb.RevealPendingLuckyDrawsResponse{Status: rpc.StatusInternalWithDebugMsg("error reveal some lucky draws")}, nil
	}
	return &luckyDrawPb.RevealPendingLuckyDrawsResponse{Status: rpc.StatusOk()}, nil
}

// GetWinningsSummaryForActor is useful to fetch summary of rewards won by an actor from lucky draw winnings.
func (l *LuckyDrawService) GetWinningsSummaryForActor(ctx context.Context, req *luckyDrawPb.GetWinningsSummaryForActorRequest) (*luckyDrawPb.GetWinningsSummaryForActorResponse, error) {
	winnings, err := l.winningDao.GetByActorIdLuckyDrawIdsAndRewardStatuses(ctx, req.GetActorId(), req.GetFilter().GetLuckyDrawIds(), req.GetFilter().GetRewardStatuses())
	if err != nil {
		logger.Error(ctx, "error fetching winnings from db", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(err))
		return &luckyDrawPb.GetWinningsSummaryForActorResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	successResp := &luckyDrawPb.GetWinningsSummaryForActorResponse{
		Status:                    rpc.StatusOk(),
		CashRewardEarned:          money.AmountINR(0).GetPb(),
		SdRewardEarned:            money.AmountINR(0).GetPb(),
		InProcessCashRewardAmount: money.AmountINR(0).GetPb(),
		InProcessSdRewardAmount:   money.AmountINR(0).GetPb(),
		WinningsCount:             uint32(len(winnings)),
	}

	// todo (utkarsh) : migrate to an optimal approach for doing aggregations.
	for _, winning := range winnings {
		reward := winning.GetReward()
		switch reward.GetRewardType() {

		case rewardsPb.RewardType_FI_COINS:
			if winning.GetRewardStatus() == luckyDrawPb.RewardStatus_PROCESSED {
				successResp.FiCoinsEarned += reward.GetFiCoins().GetUnits()
			} else {
				successResp.InProcessFiCoins += reward.GetFiCoins().GetUnits()
			}
			successResp.FiCoinsRewardCount++

		case rewardsPb.RewardType_CASH:
			if winning.GetRewardStatus() == luckyDrawPb.RewardStatus_PROCESSED {
				successResp.CashRewardEarned.Units += reward.GetCash().GetAmount().GetUnits()
			} else {
				successResp.InProcessCashRewardAmount.Units += reward.GetCash().GetAmount().GetUnits()
			}
			successResp.CashRewardCount++

		case rewardsPb.RewardType_SMART_DEPOSIT:
			if winning.GetRewardStatus() == luckyDrawPb.RewardStatus_PROCESSED {
				successResp.SdRewardEarned.Units += reward.GetSmartDeposit().GetAmount().GetUnits()
			} else {
				successResp.InProcessSdRewardAmount.Units += reward.GetSmartDeposit().GetAmount().GetUnits()
			}
			successResp.SdRewardCount++

		// adding default case to avoid lint exhaustive errors
		default:
		}

	}

	return successResp, nil
}

// claimWinningUsingClaimMetadata enriches the reward present in the winning using claim metadata and updates the status of winning.
func (l *LuckyDrawService) claimWinningUsingClaimMetadata(ctx context.Context, winning *luckyDrawPb.LuckyDrawWinning, claimMetadata *rewardsPb.RewardClaimMetadata) error {
	// get reward from winning and enrich it (if required) using claim metadata
	updatedReward := winning.GetReward()

	switch updatedReward.GetRewardType() {
	// update fi coins reward details to add expiry timestamp.
	case rewardsPb.RewardType_FI_COINS:
		fiCoinsReward := updatedReward.GetFiCoins()
		// TODO: UPDATE TO PASS USING FetchDefaultCurrencyExpiryTimestamp whenever we start using lucky draw again
		// setting expiry time to 2 years after the claim date.
		fiCoinsExpiryTimestamp, err := ptypes.TimestampProto(time.Now().AddDate(2, 0, 0))
		if err != nil {
			return errors.Wrap(err, "unable to calculate expiry time for fi coins winning")
		}
		fiCoinsReward.ExpiresAt = fiCoinsExpiryTimestamp
		// TODO: UPDATE TO PASS IsFiPoints flag whenever we start using lucky draw again

	//  enrich sd reward with nominee details and maturity date.
	case rewardsPb.RewardType_SMART_DEPOSIT:
		sdReward := updatedReward.GetSmartDeposit()
		sdReward.NomineeInfoList = claimMetadata.GetSdMetadata().GetNomineeInfoList()
		maturityDate, err := rewardsPb.GetRewardConfigDate(sdReward.GetMaturityDateConfig(), time.Now())
		if err != nil {
			return errors.Wrap(err, "error calculating sd maturity date from config")
		}
		sdReward.MaturityDate = maturityDate
		sdReward.Name = claimMetadata.GetSdMetadata().GetSdName()

	//  enrich gift hamper reward with shipping address details
	case rewardsPb.RewardType_GIFT_HAMPER:
		giftHamperReward := updatedReward.GetGiftHamper()
		giftHamperReward.ShippingAddress = claimMetadata.GetShippingAddress()

	default:

	}

	// claim winning with enriched reward details
	err := l.winningDao.ClaimWinning(ctx, winning.GetId(), updatedReward)
	if err != nil {
		// if error is anything different from ErrRowNotUpdated return error.
		if !errors.Is(err, epifierrors.ErrRowNotUpdated) {
			return errors.Wrap(err, "error claiming the winning")
		}
		// ErrRowNotUpdated is possible when winning is already claimed, validate if that is the case.
		// return nil error is winning is already claimed.
		updatedWinning, getWinningErr := l.winningDao.GetByRegistrationId(ctx, winning.GetLuckyDrawRegistrationId())
		if getWinningErr != nil {
			return errors.Wrap(getWinningErr, "error fetching lucky draw winning by regId")
		}
		logger.Info(ctx, "checking if winning is already claimed", zap.String(logger.WINNING_ID, winning.GetId()), zap.String(logger.REWARD_STATUS, winning.GetRewardStatus().String()))
		if isWinningClaimed(updatedWinning) {
			return nil
		}
		return errors.Wrap(err, "error claiming the winning")
	}
	return nil
}

// Checks the reward status of winning to see if winning is claimed or not
// returns true if the winning is claimed.
func isWinningClaimed(winning *luckyDrawPb.LuckyDrawWinning) bool {
	return winning.GetRewardStatus() == luckyDrawPb.RewardStatus_PROCESSING_PENDING ||
		winning.GetRewardStatus() == luckyDrawPb.RewardStatus_PROCESSING_IN_PROGRESS ||
		winning.GetRewardStatus() == luckyDrawPb.RewardStatus_PROCESSING_FAILED ||
		winning.GetRewardStatus() == luckyDrawPb.RewardStatus_PROCESSED
}
