package subscriberService

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epifierrors"

	accountsPb "github.com/epifi/gamma/api/accounts"
	accountEnumsPb "github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/rewards/config/genconf"

	accountsOperStatusPb "github.com/epifi/gamma/api/accounts/operstatus"

	"go.uber.org/zap"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/logger"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/rewards/events"
	processorPb "github.com/epifi/gamma/api/rewards/processor"
	"github.com/epifi/gamma/rewards/clawback/dao"
	"github.com/epifi/gamma/rewards/clawback/dao/model"
	rewardConstants "github.com/epifi/gamma/rewards/constants"
	"github.com/epifi/gamma/rewards/processor/service"
	"github.com/epifi/gamma/rewards/wire/types"
)

type RewardProcessorSubscriberService struct {
	processorPb.UnimplementedSubscriberServer
	rewardProcessorService           service.IRewardProcessorService
	winningProcessorService          service.IWinningProcessorService
	clawbacksDao                     dao.RewardClawbacksDao
	clawbackProcessorService         service.IClawbackProcessorService
	clawbackProcessingEventPublisher types.RewardClawbackProcessingSqsPublisher
	bulkClaimRewardsService          service.IBulkClaimRewardsService
	accountStatusUpdateService       service.IAccountStatusUpdateService
	cacheStorage                     cache.CacheStorage
	dynConf                          *genconf.Config
}

var _ processorPb.SubscriberServer = &RewardProcessorSubscriberService{}

func NewRewardProcessorSubscriberService(
	rewardProcessorService service.IRewardProcessorService,
	winningProcessorService service.IWinningProcessorService,
	clawbacksDao dao.RewardClawbacksDao,
	clawbackProcessorService service.IClawbackProcessorService,
	clawbackProcessingEventPublisher types.RewardClawbackProcessingSqsPublisher,
	bulkClaimRewardsService service.IBulkClaimRewardsService,
	accountStatusUpdateService service.IAccountStatusUpdateService,
	cacheStorage cache.CacheStorage,
	dynConf *genconf.Config,
) *RewardProcessorSubscriberService {

	return &RewardProcessorSubscriberService{
		rewardProcessorService:           rewardProcessorService,
		winningProcessorService:          winningProcessorService,
		clawbacksDao:                     clawbacksDao,
		clawbackProcessorService:         clawbackProcessorService,
		clawbackProcessingEventPublisher: clawbackProcessingEventPublisher,
		bulkClaimRewardsService:          bulkClaimRewardsService,
		accountStatusUpdateService:       accountStatusUpdateService,
		cacheStorage:                     cacheStorage,
		dynConf:                          dynConf,
	}
}

var (
	getSuccessRes = func() *processorPb.ConsumerResponse {
		return &processorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}
	}
	getPermanentFailureRes = func() *processorPb.ConsumerResponse {
		return &processorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE}}
	}
	getTransientFailureRes = func() *processorPb.ConsumerResponse {
		return &processorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}
	}
)

func (rp *RewardProcessorSubscriberService) ProcessReward(ctx context.Context, req *rewardsPb.RewardEvent) (*processorPb.ConsumerResponse, error) {
	logger.Info(ctx, "got reward processing event", zap.String(logger.REWARD_ID, req.GetReward().GetId()))
	resp, err := rp.rewardProcessorService.ProcessReward(ctx, req.Reward, req.GetRequestHeader().GetIsLastAttempt(), req.GetShouldSuppressNotifications())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "reward not found, can't fulfil it", zap.String(logger.REWARD_ID, req.GetReward().GetId()))
			return getPermanentFailureRes(), nil
		}
		logger.Error(ctx, "Unable to process reward", zap.String(logger.REWARD_ID, req.GetReward().GetId()), zap.Error(err))
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "response from reward processor", zap.String(logger.REWARD_ID, req.GetReward().GetId()), zap.Any("response", resp))
	return getSuccessRes(), nil
}

// ProcessLuckyDrawWinning consumes lucky draw processing event to trigger processing of a lucky draw winning.
func (rp *RewardProcessorSubscriberService) ProcessLuckyDrawWinning(ctx context.Context, req *processorPb.ProcessLuckyDrawWinningRequest) (*processorPb.ConsumerResponse, error) {
	logger.Info(ctx, "got winning processing event", zap.String(logger.WINNING_ID, req.GetWinningId()))
	resp, err := rp.winningProcessorService.ProcessWinning(ctx, req.GetWinningId(), req.GetRequestHeader().GetIsLastAttempt())
	if err != nil {
		logger.Error(ctx, "Unable to process winning", zap.String(logger.WINNING_ID, req.GetWinningId()), zap.Error(err))
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "response from winning processor", zap.String(logger.REWARD_ID, req.GetWinningId()), zap.Any("response", resp))
	return getSuccessRes(), nil
}

// ProcessRewardStatusUpdateEvent consumes reward status update event to trigger processing for any pending reward-clawbacks once the reward is processed.
func (rp *RewardProcessorSubscriberService) ProcessRewardStatusUpdateEvent(ctx context.Context, req *events.RewardStatusUpdateEvent) (*processorPb.ConsumerResponse, error) {
	// we only need to process events for cases where a reward was successful processed
	if req.GetUpdatedRewardStatus() != rewardsPb.RewardStatus_PROCESSED {
		logger.Info(ctx, "ignoring the reward status update event, reward is not in processed state", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String(logger.REWARD_STATUS, req.GetUpdatedRewardStatus().String()))
		return getSuccessRes(), nil
	}

	if req.GetReward().GetDeletedAt().IsValid() {
		logger.Error(ctx, "reward is deleted, permanently failing this event", zap.String(logger.REWARD_ID, req.GetRewardId()))
		return getPermanentFailureRes(), nil
	}

	// All reward clawback event publishments are now controlled by the EnableRewardClawback flag.
	// Check the annotated PR for details.
	if rp.dynConf.Flags().EnableRewardClawback() {
		if err := rp.handlePendingClawbackProcessingEvents(ctx, req.GetRewardId()); err != nil {
			return getTransientFailureRes(), nil
		}
	}
	return getSuccessRes(), nil
}

// handlePendingClawbackProcessingEvents fetches all pending clawbacks for a reward and publishes processing events for them.
func (rp *RewardProcessorSubscriberService) handlePendingClawbackProcessingEvents(ctx context.Context, rewardId string) error {
	pendingClawbacks, err := rp.clawbacksDao.GetClawbacks(ctx, &model.ClawbacksQueryFilter{
		RewardIds:      []string{rewardId},
		ClawbackStatus: rewardsPb.ClawbackStatus_CLAWBACK_STATUS_PROCESSING_PENDING,
	})
	if err != nil {
		logger.Error(ctx, "error fetching processing pending clawbacks", zap.String(logger.REWARD_ID, rewardId), zap.Error(err))
		return err
	}

	var publishErrors []error
	for _, pendingClawback := range pendingClawbacks {
		if _, publishErr := rp.clawbackProcessingEventPublisher.Publish(ctx, &processorPb.RewardClawbackProcessingEvent{ClawbackId: pendingClawback.GetId()}); publishErr != nil {
			logger.Error(ctx, "error publishing clawback processing event", zap.String(logger.CLAWBACK_ID, pendingClawback.GetId()), zap.Error(publishErr))
			publishErrors = append(publishErrors, publishErr)
			continue
		}
		logger.Info(ctx, "clawback processing event published successfully", zap.String(logger.CLAWBACK_ID, pendingClawback.GetId()))
	}
	if len(publishErrors) > 0 {
		return fmt.Errorf("failed to publish %d clawback processing events; see logs for details", len(publishErrors))
	}
	return nil
}

// ProcessRewardClawback consumes clawback processing events to process the clawbacks.
func (rp *RewardProcessorSubscriberService) ProcessRewardClawback(ctx context.Context, req *processorPb.RewardClawbackProcessingEvent) (*processorPb.ConsumerResponse, error) {
	// All reward clawback event publishments are now controlled by the EnableRewardClawback flag.
	// Check the annotated PR for details.
	if !rp.dynConf.Flags().EnableRewardClawback() {
		logger.Info(ctx, "Reward clawback flows are disabled via EnableRewardClawback flag. See PR-LINK.")
		return getPermanentFailureRes(), nil
	}

	logger.Info(ctx, "got clawback processing event", zap.String("clawbackId", req.GetClawbackId()))
	resp, err := rp.clawbackProcessorService.ProcessClawback(ctx, req.GetClawbackId(), req.GetRequestHeader().GetIsLastAttempt())
	if err != nil {
		logger.Error(ctx, "Unable to process clawback", zap.String("clawbackId", req.GetClawbackId()), zap.Error(err))
		return getTransientFailureRes(), nil
	}
	logger.Debug(ctx, "response from clawback processor", zap.String("clawbackId", req.GetClawbackId()), zap.Any("response", resp))
	return getSuccessRes(), nil
}

// ProcessBulkClaimRewardsEvent consumes bulk claim rewards events to process the claiming bulk rewards for an actor.
func (rp *RewardProcessorSubscriberService) ProcessBulkClaimRewardsEvent(ctx context.Context, req *events.BulkClaimRewardsEvent) (*processorPb.ConsumerResponse, error) {
	logger.Info(ctx, "got bulk claim rewards processing event", zap.String("req", req.String()))

	ctx, cancelFunc := context.WithTimeout(ctx, 30*time.Second)
	defer cancelFunc()

	err := rp.bulkClaimRewardsService.ProcessBulkClaimRewards(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "Unable to process bulk claim rewards", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return getTransientFailureRes(), nil
	}
	// no error occurred means rewards are in processing state, so release the redis lock applied for bulk claim rewards.
	err = rp.cacheStorage.Delete(ctx, getBulkClaimRewardsLockKey(req.GetActorId()))
	if err != nil {
		logger.Error(ctx, "error while deleting the bulk claim rewards key for releasing the lock", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		// no need of returning failure as anyhow if given ttl duration exceeds the key will expire automatically.
	}
	return getSuccessRes(), nil
}

func getBulkClaimRewardsLockKey(actorId string) string {
	return rewardConstants.BulkClaimRewardsLockKeyPrefix + actorId
}

// ProcessAccountStatusUpdateEvent consumes OperationalStatusUpdateEvent and expire all rewards for an actor if account status is OPERATIONAL_STATUS_CLOSED.
func (rp *RewardProcessorSubscriberService) ProcessAccountStatusUpdateEvent(ctx context.Context, req *accountsOperStatusPb.OperationalStatusUpdateEvent) (*processorPb.ConsumerResponse, error) {
	logger.Info(ctx, "got rewards account status update event", zap.String("OperationalStatus", req.GetOperationalStatusInfo().GetOperationalStatus().String()))

	accountStatusInfo := req.GetOperationalStatusInfo()
	if accountStatusInfo.GetOperationalStatus() != accountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_CLOSED {
		logger.Debug(ctx, "account operation status is not closed", zap.String("accountStatus", accountStatusInfo.GetOperationalStatus().String()), zap.String(logger.ACCOUNT_ID, accountStatusInfo.GetAccountId()))
		return getSuccessRes(), nil
	}
	if accountStatusInfo.GetAccountType() != accountsPb.Type_SAVINGS || accountStatusInfo.GetPartnerBank() != commonvgpb.Vendor_FEDERAL_BANK || accountStatusInfo.GetAccountId() == "" {
		logger.Error(ctx, "received unsupported account information for account's operational status as closed", zap.String(logger.ACCOUNT_ID, accountStatusInfo.GetAccountId()),
			zap.String(logger.ACCOUNT_TYPE, accountStatusInfo.GetAccountType().String()), zap.String(logger.VENDOR, accountStatusInfo.GetPartnerBank().String()))
		return getPermanentFailureRes(), nil
	}

	err := rp.accountStatusUpdateService.ExpireRewardsForClosedAccount(ctx, accountStatusInfo.GetAccountId())
	if err != nil {
		logger.Error(ctx, "Unable to process update rewards expiry for account closure", zap.String(logger.ACCOUNT_ID, accountStatusInfo.GetAccountId()), zap.Error(err))
		return getTransientFailureRes(), nil
	}

	return getSuccessRes(), nil
}
