package processor

import (
	"context"
	"fmt"
	"time"

	"github.com/golang/protobuf/ptypes"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	actorPb "github.com/epifi/gamma/api/actor"
	types "github.com/epifi/gamma/api/typesv2"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/rewards/config"
	"github.com/epifi/gamma/rewards/generator/dao"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/frontend/deeplink"
	fcmPb "github.com/epifi/gamma/api/frontend/fcm"
	campaigncommPb "github.com/epifi/gamma/api/rewards/campaigncomm"
	savingsPb "github.com/epifi/gamma/api/savings"
)

type SalaryDepositV1 struct {
	savingsClient      savingsPb.SavingsClient
	commsClient        commsPb.CommsClient
	actorClient        actorPb.ActorClient
	rewardsDao         dao.RewardsDao
	campaignCommConfig *config.RewardsCampaignCommConfig
}

func NewSalaryDepositV1(savingsClient savingsPb.SavingsClient, commsClient commsPb.CommsClient, actorClient actorPb.ActorClient, rewardsDao dao.RewardsDao, campaignCommConfig *config.RewardsCampaignCommConfig) *SalaryDepositV1 {
	return &SalaryDepositV1{savingsClient: savingsClient, commsClient: commsClient, actorClient: actorClient, rewardsDao: rewardsDao, campaignCommConfig: campaignCommConfig}
}

// nolint: dupl
func (f *SalaryDepositV1) SendComm(ctx context.Context, _ *campaigncommPb.SendCommRequest) *campaigncommPb.SendCommResponse {
	currentTime := ptypes.TimestampNow()
	const batchSize = 150
	const delayBetweenBatches = 1 * time.Minute

	// perform rest of the work asynchronously with updated ctx to prevent deadline exceeded error.
	//nocustomlint:goroutine
	go func(ctx context.Context) {
		// sending notification in batches
		for currentBatchOffset := 0; currentBatchOffset < 30000; currentBatchOffset += batchSize {

			// fetch active savings accounts in batches
			activeSavingsAccountRes, err := f.savingsClient.GetListOfActiveAccounts(ctx, &savingsPb.GetListOfActiveAccountsRequest{CreatedBefore: currentTime, PageSize: int32(batchSize), Offset: int64(currentBatchOffset)})
			if err != nil || !activeSavingsAccountRes.GetStatus().IsSuccess() {
				logger.Error(ctx, "savingsClient.GetListOfActiveAccounts rpc call failed", zap.Any(logger.RPC_STATUS, activeSavingsAccountRes.GetStatus()), zap.Int("batch_size", batchSize), zap.Int("current_batch_offset", currentBatchOffset), zap.Error(err))
				// breaking the loop to prevent further iterations as they too are expected to fail.
				break
			}

			// break the loop if no more savings account are found in the current batch
			if len(activeSavingsAccountRes.GetAccountList()) == 0 {
				logger.Info(ctx, "no more savings account found", zap.Int("batch_size", batchSize), zap.Int("current_batch_offset", currentBatchOffset))
				break
			}

			// make list of unique userIds holding savings account
			// used map for ensuring uniqueness
			uniqueUserIds := make(map[string]struct{}, len(activeSavingsAccountRes.GetAccountList()))
			for _, account := range activeSavingsAccountRes.GetAccountList() {
				uniqueUserIds[account.GetPrimaryAccountHolder()] = struct{}{}
			}

			// filter in userIds who have not got the salary deposit v1 reward.
			var filteredUserIds []string
			for userId := range uniqueUserIds {
				hasUserAlreadyGotTheReward, err1 := f.hasUserAlreadyGotTheReward(ctx, userId)
				// muting error as one failed request shouldn't fail others
				if err1 == nil && !hasUserAlreadyGotTheReward {
					filteredUserIds = append(filteredUserIds, userId)
				}
			}

			successNotificationCount := 0
			// send notification to the users
			for _, userId := range filteredUserIds {
				// muting error as one failed request shouldn't fail others
				if err = f.sendNotification(ctx, userId); err == nil {
					successNotificationCount++
				}
			}
			logger.Info(ctx, "successfully sent salary deposit v1 reward notification to users", zap.Int("batch_size", batchSize), zap.Int("current_batch_offset", currentBatchOffset), zap.Int("success_notification_count", successNotificationCount), zap.Int("total_eligible_user_count", len(filteredUserIds)))

			// sleep between consecutive batches to avoid overloading the comms server
			time.Sleep(delayBetweenBatches)
		}
	}(context.Background())

	return &campaigncommPb.SendCommResponse{Status: rpc.StatusOk()}
}

// hasUserAlreadyGotTheReward checks if user has already earned the salary deposit v1 reward.
func (f *SalaryDepositV1) hasUserAlreadyGotTheReward(ctx context.Context, userId string) (bool, error) {
	// fetch actor id from user id
	actorByEntityIdRes, err := f.actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: userId,
	})
	if err != nil || !actorByEntityIdRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "actorClient.GetActorByEntityId call failed", zap.String(logger.USER_ID, userId), zap.Any(logger.RPC_STATUS, actorByEntityIdRes.GetStatus()), zap.Error(err))
		return false, errors.New("actorClient.GetActorByEntityId call failed")
	}
	actorId := actorByEntityIdRes.GetActor().GetId()

	salaryDepositV1RewardOfferId := f.campaignCommConfig.SalaryDepositV1RewardOfferId

	// fetch already earned rewards for actor and offer id combination
	earnedRewards, err := f.rewardsDao.FetchByActorAndOfferId(ctx, actorId, salaryDepositV1RewardOfferId)
	if err != nil {
		logger.Error(ctx, "error fetching reward bt actor and offer id", zap.String(logger.ACTOR_ID, actorId), zap.String(logger.REWARD_OFFER_ID, salaryDepositV1RewardOfferId), zap.Error(err))
		return false, errors.New("error fetching reward bt actor and offer id")
	}
	return len(earnedRewards) > 0, nil
}

// nolint: dupl
func (f *SalaryDepositV1) sendNotification(ctx context.Context, userId string) error {
	sendMessageReq := &commsPb.SendMessageRequest{
		Type:           commsPb.QoS_GUARANTEED,
		Medium:         commsPb.Medium_NOTIFICATION,
		UserIdentifier: &commsPb.SendMessageRequest_UserId{UserId: userId},
		Message: &commsPb.SendMessageRequest_Notification{
			Notification: &commsPb.NotificationMessage{
				Priority: commsPb.NotificationPriority_NORMAL,
				Notification: &fcmPb.Notification{
					NotificationType: fcmPb.NotificationType_SYSTEM_TRAY,
					NotificationTemplates: &fcmPb.Notification_SystemTrayTemplate{
						SystemTrayTemplate: &fcmPb.SystemTrayTemplate{
							CommonTemplateFields: &fcmPb.CommonTemplateFields{
								Title: fmt.Sprintf("Earn upto %d %s!", accrualPkg.ConvertFiCoinsToFiPoints(5000, false), accrualPkg.ReplaceCoinWithPointIfApplicable("Fi-Coins", nil)),
								Body:  fmt.Sprintf("Add your salary to Fi and get up to %d %s.", accrualPkg.ConvertFiCoinsToFiPoints(5000, false), accrualPkg.ReplaceCoinWithPointIfApplicable("Fi-Coins", nil)),
								Deeplink: &deeplink.Deeplink{
									Screen: deeplink.Screen_TRANSFER_IN,
								},
							},
						}},
				},
			}},
	}
	sendMessageRes, err := f.commsClient.SendMessage(ctx, sendMessageReq)
	if err != nil || !sendMessageRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error sending salary deposit v1 reward notification to the user", zap.String(logger.USER_ID, userId), zap.Any(logger.RPC_STATUS, sendMessageRes.GetStatus()), zap.Error(err))
		return errors.New("commsClient.SendMessage call failed")
	}
	return nil
}
