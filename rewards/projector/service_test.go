package projector

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/pagination"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsProjectorPb "github.com/epifi/gamma/api/rewards/projector"
	projectorDaoMock "github.com/epifi/gamma/rewards/test/mocks/projector/dao"
)

func TestRewardsProjectionService_GetRewardsProjections(t *testing.T) {
	var (
		fromTime = time.Date(2023, 11, 1, 0, 0, 0, 0, datetime.IST)
		tillTime = time.Date(2023, 11, 10, 0, 0, 0, 0, datetime.IST)
	)
	type args struct {
		ctx context.Context
		req *rewardsProjectorPb.GetRewardsProjectionsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockedProjectionDao *projectorDaoMock.MockRewardsProjectionDao)
		want       *rewardsProjectorPb.GetRewardsProjectionsResponse
		wantErr    bool
	}{
		{
			name: "should return error when error occurs while getting page token from page context",
			args: args{
				ctx: context.Background(),
				req: &rewardsProjectorPb.GetRewardsProjectionsRequest{
					ActorId: "actor-1",
					Filters: &rewardsProjectorPb.GetRewardsProjectionsRequest_Filters{
						TimeWindow: &rewardsPb.TimeWindow{
							FromTime: timestampPb.New(fromTime),
							TillTime: timestampPb.New(tillTime),
						},
					},
					PageCtxRequest: &rpc.PageContextRequest{
						Token:    &rpc.PageContextRequest_AfterToken{AfterToken: "incorrect"},
						PageSize: 10,
					},
				},
			},
			setupMocks: func(mockedProjectionDao *projectorDaoMock.MockRewardsProjectionDao) {},
			wantErr:    false,
			want: &rewardsProjectorPb.GetRewardsProjectionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to get page token from pageCtxReq"),
			},
		},
		{
			name: "should return error when error occurs while getting projections from DB",
			args: args{
				ctx: context.Background(),
				req: &rewardsProjectorPb.GetRewardsProjectionsRequest{
					ActorId: "actor-1",
					Filters: &rewardsProjectorPb.GetRewardsProjectionsRequest_Filters{
						AccountId:  "acc-1",
						ActionType: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION},
						OfferType:  []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER},
						RefIds:     []string{"ref-1"},
						TimeWindow: &rewardsPb.TimeWindow{
							FromTime: timestampPb.New(fromTime),
							TillTime: timestampPb.New(tillTime),
						},
					},
					PageCtxRequest: &rpc.PageContextRequest{
						PageSize: 2,
					},
				},
			},
			setupMocks: func(mockedProjectionDao *projectorDaoMock.MockRewardsProjectionDao) {
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(context.Background(), gomock.Any(), nil, 2, nil).
					Return(nil, nil, fmt.Errorf("err"))
			},
			wantErr: false,
			want: &rewardsProjectorPb.GetRewardsProjectionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch projections"),
			},
		},
		{
			name: "should return correct individual projections when returned from DB",
			args: args{
				ctx: context.Background(),
				req: &rewardsProjectorPb.GetRewardsProjectionsRequest{
					ActorId: "actor-1",
					Filters: &rewardsProjectorPb.GetRewardsProjectionsRequest_Filters{
						AccountId:  "acc-1",
						ActionType: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION},
						OfferType:  []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER},
						RefIds:     []string{"ref-1"},
						TimeWindow: &rewardsPb.TimeWindow{
							FromTime: timestampPb.New(fromTime),
							TillTime: timestampPb.New(tillTime),
						},
					},
					PageCtxRequest: &rpc.PageContextRequest{
						PageSize: 2,
					},
				},
			},
			setupMocks: func(mockedProjectionDao *projectorDaoMock.MockRewardsProjectionDao) {
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(context.Background(), gomock.Any(), nil, 2, nil).
					Return([]*rewardsProjectorPb.Projection{projection1, projection2}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "after-token"}, nil)
			},
			wantErr: false,
			want: &rewardsProjectorPb.GetRewardsProjectionsResponse{
				Status: rpc.StatusOk(),
				Projections: &rewardsProjectorPb.GetRewardsProjectionsResponse_IndividualProjections_{
					IndividualProjections: &rewardsProjectorPb.GetRewardsProjectionsResponse_IndividualProjections{
						Projections: []*rewardsProjectorPb.Projection{projection1, projection2},
					},
				},
				PageCtxResponse: &rpc.PageContextResponse{HasAfter: true, AfterToken: "after-token"},
			},
		},
		{
			name: "should return correct individual projections when returned from DB including deleted projections",
			args: args{
				ctx: context.Background(),
				req: &rewardsProjectorPb.GetRewardsProjectionsRequest{
					ActorId: "actor-1",
					Filters: &rewardsProjectorPb.GetRewardsProjectionsRequest_Filters{
						AccountId:  "acc-1",
						ActionType: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION},
						OfferType:  []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKEND_OFFER},
						RefIds:     []string{"ref-1"},
						TimeWindow: &rewardsPb.TimeWindow{
							FromTime: timestampPb.New(fromTime),
							TillTime: timestampPb.New(tillTime),
						},
					},
					PageCtxRequest: &rpc.PageContextRequest{
						PageSize: 2,
					},
				},
			},
			setupMocks: func(mockedProjectionDao *projectorDaoMock.MockRewardsProjectionDao) {
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(context.Background(), gomock.Any(), nil, 2, nil).
					Return([]*rewardsProjectorPb.Projection{projection1, deletedProjection}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "after-token"}, nil)
			},
			wantErr: false,
			want: &rewardsProjectorPb.GetRewardsProjectionsResponse{
				Status: rpc.StatusOk(),
				Projections: &rewardsProjectorPb.GetRewardsProjectionsResponse_IndividualProjections_{
					IndividualProjections: &rewardsProjectorPb.GetRewardsProjectionsResponse_IndividualProjections{
						Projections: []*rewardsProjectorPb.Projection{projection1, deletedProjection},
					},
				},
				PageCtxResponse: &rpc.PageContextResponse{HasAfter: true, AfterToken: "after-token"},
			},
		},
		{
			name: "should return correct individual projections when returned from DB",
			args: args{
				ctx: context.Background(),
				req: &rewardsProjectorPb.GetRewardsProjectionsRequest{
					ActorId: "actor-1",
					Filters: &rewardsProjectorPb.GetRewardsProjectionsRequest_Filters{
						AccountId:  "acc-1",
						ActionType: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION},
						OfferType:  []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER},
						RefIds:     []string{"ref-1"},
						TimeWindow: &rewardsPb.TimeWindow{
							FromTime: timestampPb.New(fromTime),
							TillTime: timestampPb.New(tillTime),
						},
					},
					PageCtxRequest: &rpc.PageContextRequest{
						PageSize: 2,
					},
				},
			},
			setupMocks: func(mockedProjectionDao *projectorDaoMock.MockRewardsProjectionDao) {
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(context.Background(), gomock.Any(), nil, 2, nil).
					Return([]*rewardsProjectorPb.Projection{projection1, projection2}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "after-token"}, nil)
			},
			wantErr: false,
			want: &rewardsProjectorPb.GetRewardsProjectionsResponse{
				Status: rpc.StatusOk(),
				Projections: &rewardsProjectorPb.GetRewardsProjectionsResponse_IndividualProjections_{
					IndividualProjections: &rewardsProjectorPb.GetRewardsProjectionsResponse_IndividualProjections{
						Projections: []*rewardsProjectorPb.Projection{projection1, projection2},
					},
				},
				PageCtxResponse: &rpc.PageContextResponse{HasAfter: true, AfterToken: "after-token"},
			},
		},
		{
			name: "should return error when error occurs while fetching projections for aggregates from DB",
			args: args{
				ctx: context.Background(),
				req: &rewardsProjectorPb.GetRewardsProjectionsRequest{
					ActorId: "actor-1",
					Filters: &rewardsProjectorPb.GetRewardsProjectionsRequest_Filters{
						AccountId:  "acc-1",
						ActionType: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION},
						OfferType:  []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER},
						RefIds:     []string{"ref-1"},
						TimeWindow: &rewardsPb.TimeWindow{
							FromTime: timestampPb.New(fromTime),
							TillTime: timestampPb.New(tillTime),
						},
					},
					FetchAggregates: true,
				},
			},
			setupMocks: func(mockedProjectionDao *projectorDaoMock.MockRewardsProjectionDao) {
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					gomock.Any(),
					nil,
					30,
					[]rewardsProjectorPb.ProjectionFieldMask{rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_PROJECTED_OPTIONS, rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS},
				).
					Return(nil, nil, fmt.Errorf("error"))
			},
			wantErr: false,
			want: &rewardsProjectorPb.GetRewardsProjectionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching aggregate projections"),
			},
		},
		{
			name: "should return correct aggregates when no error occurs - 1",
			args: args{
				ctx: context.Background(),
				req: &rewardsProjectorPb.GetRewardsProjectionsRequest{
					ActorId: "actor-1",
					Filters: &rewardsProjectorPb.GetRewardsProjectionsRequest_Filters{
						AccountId:  "acc-1",
						ActionType: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION},
						OfferType:  []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER},
						RefIds:     []string{"ref-1"},
						TimeWindow: &rewardsPb.TimeWindow{
							FromTime: timestampPb.New(fromTime),
							TillTime: timestampPb.New(tillTime),
						},
					},
					FetchAggregates: true,
				},
			},
			setupMocks: func(mockedProjectionDao *projectorDaoMock.MockRewardsProjectionDao) {
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					gomock.Any(),
					nil,
					30,
					[]rewardsProjectorPb.ProjectionFieldMask{rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_PROJECTED_OPTIONS, rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS},
				).
					Return(
						[]*rewardsProjectorPb.Projection{projection1, projection2},
						&rpc.PageContextResponse{HasAfter: true, AfterToken: "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTAwMDAwfSwiT2Zmc2V0IjoyfQ=="},
						nil,
					)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					gomock.Any(),
					&pagination.PageToken{Timestamp: &timestampPb.Timestamp{Seconds: 100000}, Offset: 2},
					30,
					[]rewardsProjectorPb.ProjectionFieldMask{rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_PROJECTED_OPTIONS, rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS},
				).
					Return([]*rewardsProjectorPb.Projection{projection3, projection4}, &rpc.PageContextResponse{HasAfter: false}, nil)
			},
			wantErr: false,
			want: &rewardsProjectorPb.GetRewardsProjectionsResponse{
				Status: rpc.StatusOk(),
				Projections: &rewardsProjectorPb.GetRewardsProjectionsResponse_AggregateProjections_{
					AggregateProjections: &rewardsProjectorPb.GetRewardsProjectionsResponse_AggregateProjections{
						RewardUnitsDetails: []*rewardsProjectorPb.GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails{
							{RewardType: rewardsPb.RewardType_FI_COINS, ProjectedRewardUnits: 1400, ActualRewardUnits: 600},
						},
					},
				},
			},
		},
		{
			name: "should return correct aggregates when no error occurs - 2",
			args: args{
				ctx: context.Background(),
				req: &rewardsProjectorPb.GetRewardsProjectionsRequest{
					ActorId: "actor-1",
					Filters: &rewardsProjectorPb.GetRewardsProjectionsRequest_Filters{
						AccountId:  "acc-1",
						ActionType: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION},
						OfferType:  []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER},
						RefIds:     []string{"ref-1"},
						TimeWindow: &rewardsPb.TimeWindow{
							FromTime: timestampPb.New(fromTime),
							TillTime: timestampPb.New(tillTime),
						},
					},
					FetchAggregates: true,
				},
			},
			setupMocks: func(mockedProjectionDao *projectorDaoMock.MockRewardsProjectionDao) {
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					gomock.Any(),
					nil,
					30,
					[]rewardsProjectorPb.ProjectionFieldMask{rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_PROJECTED_OPTIONS, rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS},
				).
					Return(
						[]*rewardsProjectorPb.Projection{projection1, projection2},
						&rpc.PageContextResponse{HasAfter: true, AfterToken: "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTAwMDAwfSwiT2Zmc2V0IjoyfQ=="},
						nil,
					)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					gomock.Any(),
					&pagination.PageToken{Timestamp: &timestampPb.Timestamp{Seconds: 100000}, Offset: 2},
					30,
					[]rewardsProjectorPb.ProjectionFieldMask{rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_PROJECTED_OPTIONS, rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS},
				).
					Return([]*rewardsProjectorPb.Projection{projection5, projection6}, &rpc.PageContextResponse{HasAfter: false}, nil)
			},
			wantErr: false,
			want: &rewardsProjectorPb.GetRewardsProjectionsResponse{
				Status: rpc.StatusOk(),
				Projections: &rewardsProjectorPb.GetRewardsProjectionsResponse_AggregateProjections_{
					AggregateProjections: &rewardsProjectorPb.GetRewardsProjectionsResponse_AggregateProjections{
						RewardUnitsDetails: []*rewardsProjectorPb.GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails{
							{RewardType: rewardsPb.RewardType_FI_COINS, ProjectedRewardUnits: -700, ActualRewardUnits: 300},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockedProjectionDao := projectorDaoMock.NewMockRewardsProjectionDao(ctrl)
			tt.setupMocks(mockedProjectionDao)

			p := &RewardsProjectionService{
				projectorDao: mockedProjectionDao,
			}
			got, err := p.GetRewardsProjections(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRewardsProjections() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRewardsProjections() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRewardsProjectionService_GetProjectionAggregates(t *testing.T) {
	var (
		req = &rewardsProjectorPb.GetProjectionAggregatesRequest{
			ActorId: "act-1",
			Filters: &rewardsProjectorPb.GetProjectionAggregatesRequest_Filters{
				TimeWindows: []*rewardsPb.TimeWindow{
					{
						FromTime: timestampPb.New(time.Date(2023, 11, 1, 0, 0, 0, 0, datetime.IST)),
						TillTime: timestampPb.New(time.Date(2023, 12, 1, 0, 0, 0, 0, datetime.IST)),
					},
					{
						FromTime: timestampPb.New(time.Date(2024, 2, 1, 0, 0, 0, 0, datetime.IST)),
						TillTime: timestampPb.New(time.Date(2024, 3, 1, 0, 0, 0, 0, datetime.IST)),
					},
				},
			},
		}

		fieldMask = []rewardsProjectorPb.ProjectionFieldMask{rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_PROJECTED_OPTIONS, rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS}
	)
	type args struct {
		req *rewardsProjectorPb.GetProjectionAggregatesRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockedProjectionDao *projectorDaoMock.MockRewardsProjectionDao)
		want       *rewardsProjectorPb.GetProjectionAggregatesResponse
		wantErr    bool
	}{
		{
			name: "should return correct aggregates",
			args: args{
				req: req,
			},
			setupMocks: func(mockedProjectionDao *projectorDaoMock.MockRewardsProjectionDao) {
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(context.Background(), gomock.Any(), nil, 30, fieldMask).
					Return([]*rewardsProjectorPb.Projection{projection7, projection8}, &rpc.PageContextResponse{HasAfter: false}, nil).Times(2)
			},
			wantErr: false,
			want: &rewardsProjectorPb.GetProjectionAggregatesResponse{
				Status: rpc.StatusOk(),
				Aggregates: []*rewardsProjectorPb.GetProjectionAggregatesResponse_RewardProjectionAggregate{
					{
						RewardType:           rewardsPb.RewardType_CASH,
						ProjectedRewardUnits: 1200,
						ActualRewardUnits:    400,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockedProjectionDao := projectorDaoMock.NewMockRewardsProjectionDao(ctrl)
			tt.setupMocks(mockedProjectionDao)

			p := &RewardsProjectionService{
				projectorDao: mockedProjectionDao,
			}
			got, err := p.GetProjectionAggregates(context.Background(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetProjectionAggregates() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetProjectionAggregates() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getRewardOptionOfInterest(t *testing.T) {
	type args struct {
		rewardOptions []*rewardsProjectorPb.RewardOption
	}
	tests := []struct {
		name string
		args args
		want *rewardsProjectorPb.RewardOption
	}{
		{
			name: "should return first option in case of single option",
			args: args{
				rewardOptions: []*rewardsProjectorPb.RewardOption{
					{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 20},
				},
			},
			want: &rewardsProjectorPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 20},
		},
		{
			name: "should return max value option in case all options are of same type",
			args: args{
				rewardOptions: []*rewardsProjectorPb.RewardOption{
					{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 20},
					{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 10},
				},
			},
			want: &rewardsProjectorPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 20},
		},
		{
			name: "should return highest priority option in case of multiple options - 1",
			args: args{
				rewardOptions: []*rewardsProjectorPb.RewardOption{
					{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 20},
					{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 10},
					{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 10},
				},
			},
			want: &rewardsProjectorPb.RewardOption{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 10},
		},
		{
			name: "should return highest priority option in case of multiple options - 2",
			args: args{
				rewardOptions: []*rewardsProjectorPb.RewardOption{
					{RewardType: rewardsPb.RewardType_EGV_BASKET, RewardUnits: 1},
					{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 10},
				},
			},
			want: &rewardsProjectorPb.RewardOption{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 10},
		},
		{
			name: "should return first option in case no prioritised options are present",
			args: args{
				rewardOptions: []*rewardsProjectorPb.RewardOption{
					{RewardType: rewardsPb.RewardType_EGV_BASKET, RewardUnits: 1},
					{RewardType: rewardsPb.RewardType_US_STOCK, RewardUnits: 10},
				},
			},
			want: &rewardsProjectorPb.RewardOption{RewardType: rewardsPb.RewardType_EGV_BASKET, RewardUnits: 1},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getRewardOptionOfInterest(tt.args.rewardOptions); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getRewardOptionOfInterest() = %v, want %v", got, tt.want)
			}
		})
	}
}
