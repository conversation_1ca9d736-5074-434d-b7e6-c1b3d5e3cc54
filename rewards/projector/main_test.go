package projector

import (
	"os"
	"testing"
	"time"

	"github.com/epifi/be-common/pkg/datetime"

	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	"github.com/epifi/gamma/rewards/test"
)

var (
	projection1 = &rewardsProjectionPb.Projection{
		Id: "proj-1",
		ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
		}},
		RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
		}},
	}
	projection2 = &rewardsProjectionPb.Projection{
		Id: "proj-2",
		ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 700},
		}},
		RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 300},
		}},
	}
	projection3 = &rewardsProjectionPb.Projection{
		Id: "proj-3",
		ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100},
		}},
		RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100},
		}},
	}
	projection4 = &rewardsProjectionPb.Projection{
		Id: "proj-4",
		ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 400},
		}},
	}
	projection5 = &rewardsProjectionPb.Projection{
		Id: "proj-5",
		ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -200},
		}},
		RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -200},
		}},
	}
	projection6 = &rewardsProjectionPb.Projection{
		Id: "proj-6",
		ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -1400},
		}},
	}
	projection7 = &rewardsProjectionPb.Projection{
		Id: "proj-7",
		ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 200},
		}},
		RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 200},
		}},
	}
	projection8 = &rewardsProjectionPb.Projection{
		Id: "proj-8",
		ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 400},
		}},
	}
	deletedProjection = &rewardsProjectionPb.Projection{
		Id: "proj-1",
		ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
		}},
		RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
		}},
		DeletedAt: timestamp.New(time.Date(2023, 11, 1, 1, 0, 0, 0, datetime.IST)),
	}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {

	var teardown func()
	// nolint: dogsled
	_, _, _, teardown = test.InitTestServer(false)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
