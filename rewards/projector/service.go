package projector

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsProjectorPb "github.com/epifi/gamma/api/rewards/projector"
	"github.com/epifi/gamma/rewards/projector/dao"
	"github.com/epifi/gamma/rewards/projector/dao/model"
)

type RewardsProjectionService struct {
	rewardsProjectorPb.UnimplementedProjectorServiceServer
	projectorDao dao.RewardsProjectionDao
}

func NewRewardsProjectionService(projectorDao dao.RewardsProjectionDao) *RewardsProjectionService {
	return &RewardsProjectionService{projectorDao: projectorDao}
}

var _ rewardsProjectorPb.ProjectorServiceServer = &RewardsProjectionService{}

func (p *RewardsProjectionService) GetRewardsProjections(ctx context.Context, req *rewardsProjectorPb.GetRewardsProjectionsRequest) (*rewardsProjectorPb.GetRewardsProjectionsResponse, error) {
	queryFilter := &model.QueryProjectionsFilter{
		AndFilter: &model.AndProjectionFilter{
			RefIds:      req.GetFilters().GetRefIds(),
			RewardIds:   req.GetFilters().GetRewardIds(),
			OfferIds:    req.GetFilters().GetOfferIds(),
			ActorId:     req.GetActorId(),
			AccountId:   req.GetFilters().GetAccountId(),
			OfferTypes:  req.GetFilters().GetOfferType(),
			ActionTypes: req.GetFilters().GetActionType(),
			FromTime:    datetime.TimestampToTime(req.GetFilters().GetTimeWindow().GetFromTime()),
			UptoTime:    datetime.TimestampToTime(req.GetFilters().GetTimeWindow().GetTillTime()),
		},
		FetchDeletedProjections: req.GetFilters().GetFetchDeletedProjections(),
	}

	// for fetching aggregates
	if req.GetFetchAggregates() {
		aggregateProjectionDetails, err := p.getAggregateProjectionDetails(ctx, queryFilter)
		if err != nil {
			logger.Error(ctx, "error while fetching aggregate projections", zap.Error(err))
			return &rewardsProjectorPb.GetRewardsProjectionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching aggregate projections"),
			}, nil
		}

		return &rewardsProjectorPb.GetRewardsProjectionsResponse{
			Status:      rpc.StatusOk(),
			Projections: aggregateProjectionDetails,
		}, nil
	}

	// for fetching individual projections
	if req.GetPageCtxRequest().GetPageSize() == 0 {
		logger.Error(ctx, "invalid page size in page context request: 0")
		return &rewardsProjectorPb.GetRewardsProjectionsResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid page size in page context request: 0"),
		}, nil
	}

	pageToken, err := pagination.GetPageToken(req.GetPageCtxRequest())
	if err != nil {
		logger.Error(ctx, "failed to get page token from pageCtxReq", zap.Any(logger.PAGE_CONTEXT, req.GetPageCtxRequest()), zap.Error(err))
		return &rewardsProjectorPb.GetRewardsProjectionsResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to get page token from pageCtxReq"),
		}, nil
	}
	projections, pageCtxRes, err := p.projectorDao.FetchPaginatedProjectionsByFilters(ctx, queryFilter, pageToken, int(req.GetPageCtxRequest().GetPageSize()), nil)
	if err != nil {
		logger.Error(ctx, "failed to fetch projections", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &rewardsProjectorPb.GetRewardsProjectionsResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch projections"),
		}, nil
	}

	return &rewardsProjectorPb.GetRewardsProjectionsResponse{
		Status: rpc.StatusOk(),
		Projections: &rewardsProjectorPb.GetRewardsProjectionsResponse_IndividualProjections_{
			IndividualProjections: &rewardsProjectorPb.GetRewardsProjectionsResponse_IndividualProjections{
				Projections: projections,
			},
		},
		PageCtxResponse: pageCtxRes,
	}, nil
}

// nolint: funlen
func (p *RewardsProjectionService) getAggregateProjectionDetails(ctx context.Context, queryFilter *model.QueryProjectionsFilter) (*rewardsProjectorPb.GetRewardsProjectionsResponse_AggregateProjections_, error) {
	if queryFilter.GetAndProjectionFilter().GetFromTime().IsZero() || queryFilter.GetAndProjectionFilter().GetUptoTime().IsZero() {
		logger.Error(ctx, "from/upto time filter can't be empty when fetching aggregates")
		return nil, fmt.Errorf("from/upto time filter can't be empty when fetching aggregates")
	}

	// fetching projections in a paginated way for computing aggregates
	// *** NOTE ***: we only fetch aggregates for single choice projections
	var (
		projections []*rewardsProjectorPb.Projection
		pageCtxRes  *rpc.PageContextResponse
		err         error
		pageToken   *pagination.PageToken
		pageSize    = 30
		pageCtxReq  = &rpc.PageContextRequest{
			PageSize: uint32(pageSize),
		}
		rewardTypeToProjectionAggregate = map[rewardsPb.RewardType]float32{}
		rewardTypeToActualAggregate     = map[rewardsPb.RewardType]float32{}
		rewardUnits                     float32
		rewardType                      rewardsPb.RewardType
		projectedRewardUnits            float32
		actualRewardUnits               float32
		aggregateProjections            = &rewardsProjectorPb.GetRewardsProjectionsResponse_AggregateProjections_{
			AggregateProjections: &rewardsProjectorPb.GetRewardsProjectionsResponse_AggregateProjections{
				RewardUnitsDetails: nil,
			},
		}
	)
	for {
		pageToken, err = pagination.GetPageToken(pageCtxReq)
		if err != nil {
			return nil, fmt.Errorf("failed to get page token from pageCtxReq, err: %w", err)
		}

		projections, pageCtxRes, err = p.projectorDao.FetchPaginatedProjectionsByFilters(ctx, queryFilter, pageToken, pageSize, []rewardsProjectorPb.ProjectionFieldMask{rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_PROJECTED_OPTIONS, rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS})
		if err != nil {
			return nil, fmt.Errorf("failed to fetch projections, err: %w", err)
		}

		// iterate on projections and compute aggregates
		for _, projection := range projections {
			if len(projection.GetProjectedOptions().GetRewardUnitsWithTypes()) != 1 {
				logger.Debug(ctx, "num options != 1, can't include projection in aggregate", zap.String("projectionId", projection.GetId()))
				continue
			}
			rewardUnits = projection.GetProjectedOptions().GetRewardUnitsWithTypes()[0].GetRewardUnits()
			rewardType = projection.GetProjectedOptions().GetRewardUnitsWithTypes()[0].GetRewardType()
			if val, ok := rewardTypeToProjectionAggregate[rewardType]; ok {
				rewardTypeToProjectionAggregate[rewardType] = val + rewardUnits
			} else {
				rewardTypeToProjectionAggregate[rewardType] = rewardUnits
			}

			if len(projection.GetRewardContributions().GetRewardUnitsWithTypes()) == 1 {
				rewardUnits = projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits()
				rewardType = projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardType()
				if val, ok := rewardTypeToActualAggregate[rewardType]; ok {
					rewardTypeToActualAggregate[rewardType] = val + rewardUnits
				} else {
					rewardTypeToActualAggregate[rewardType] = rewardUnits
				}
			}
		}

		if !pageCtxRes.GetHasAfter() {
			break
		}
		pageCtxReq.Token = &rpc.PageContextRequest_AfterToken{AfterToken: pageCtxRes.GetAfterToken()}
	}

	for _, rewardTypeValue := range rewardsPb.RewardType_value {
		rewardType = rewardsPb.RewardType(rewardTypeValue)
		projectedRewardUnits = 0
		actualRewardUnits = 0
		if units, ok := rewardTypeToProjectionAggregate[rewardType]; ok {
			projectedRewardUnits = units
		}
		if units, ok := rewardTypeToActualAggregate[rewardType]; ok {
			actualRewardUnits = units
		}
		if projectedRewardUnits != 0 || actualRewardUnits != 0 {
			aggregateProjections.AggregateProjections.RewardUnitsDetails = append(aggregateProjections.AggregateProjections.RewardUnitsDetails, &rewardsProjectorPb.GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails{
				RewardType:           rewardType,
				ProjectedRewardUnits: projectedRewardUnits,
				ActualRewardUnits:    actualRewardUnits,
			})
		}
	}

	return aggregateProjections, nil
}

// todo: use pinot as data-source for this RPC once ready
// nolint:funlen
func (p *RewardsProjectionService) GetProjectionAggregates(ctx context.Context, req *rewardsProjectorPb.GetProjectionAggregatesRequest) (*rewardsProjectorPb.GetProjectionAggregatesResponse, error) {
	if req.GetActorId() == "" || len(req.GetFilters().GetTimeWindows()) == 0 {
		logger.Error(ctx, "mandatory parameters missing", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Int("timeWindowsLen", len(req.GetFilters().GetTimeWindows())))
		return &rewardsProjectorPb.GetProjectionAggregatesResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("mandatory parameters missing"),
		}, nil
	}

	// fetching projections in a paginated way for computing aggregates
	var (
		projections []*rewardsProjectorPb.Projection
		pageCtxRes  *rpc.PageContextResponse
		err         error
		pageToken   *pagination.PageToken
		pageSize    = 30
		pageCtxReq  = &rpc.PageContextRequest{
			PageSize: uint32(pageSize),
		}
		rewardTypeToProjectionAggregate = map[rewardsPb.RewardType]float32{}
		rewardTypeToActualAggregate     = map[rewardsPb.RewardType]float32{}
		rewardUnits                     float32
		rewardType                      rewardsPb.RewardType
		queryFilter                     = &model.QueryProjectionsFilter{
			AndFilter: &model.AndProjectionFilter{
				ActorId:     req.GetActorId(),
				OfferTypes:  req.GetFilters().GetOfferTypes(),
				ActionTypes: req.GetFilters().GetActionTypes(),
			},
		}
	)
	for _, timeWindow := range req.GetFilters().GetTimeWindows() {
		queryFilter.AndFilter.FromTime = timeWindow.GetFromTime().AsTime()
		queryFilter.AndFilter.UptoTime = timeWindow.GetTillTime().AsTime()
		for {
			pageToken, err = pagination.GetPageToken(pageCtxReq)
			if err != nil {
				return nil, fmt.Errorf("failed to get page token from pageCtxReq, err: %w", err)
			}

			projections, pageCtxRes, err = p.projectorDao.FetchPaginatedProjectionsByFilters(ctx, queryFilter, pageToken, pageSize, []rewardsProjectorPb.ProjectionFieldMask{rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_PROJECTED_OPTIONS, rewardsProjectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS})
			if err != nil {
				return nil, fmt.Errorf("failed to fetch projections, err: %w", err)
			}

			// iterate on projections and compute aggregates
			for _, projection := range projections {
				projectedOption := getRewardOptionOfInterest(projection.GetProjectedOptions().GetRewardUnitsWithTypes())
				rewardUnits = projectedOption.GetRewardUnits()
				rewardType = projectedOption.GetRewardType()
				rewardTypeToProjectionAggregate[rewardType] += rewardUnits

				rewardContributionOption := getRewardOptionOfInterest(projection.GetRewardContributions().GetRewardUnitsWithTypes())
				if rewardContributionOption != nil {
					rewardUnits = projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits()
					rewardType = projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardType()
					rewardTypeToActualAggregate[rewardType] += rewardUnits
				}
			}

			if !pageCtxRes.GetHasAfter() {
				break
			}
			pageCtxReq.Token = &rpc.PageContextRequest_AfterToken{AfterToken: pageCtxRes.GetAfterToken()}
		}
	}

	var (
		aggregates []*rewardsProjectorPb.GetProjectionAggregatesResponse_RewardProjectionAggregate
		agg        *rewardsProjectorPb.GetProjectionAggregatesResponse_RewardProjectionAggregate
	)
	for rewardType, rewardUnits = range rewardTypeToProjectionAggregate {
		agg = &rewardsProjectorPb.GetProjectionAggregatesResponse_RewardProjectionAggregate{
			RewardType:           rewardType,
			ProjectedRewardUnits: rewardUnits,
		}
		if actualUnits, ok := rewardTypeToActualAggregate[rewardType]; ok {
			agg.ActualRewardUnits = actualUnits
		}

		aggregates = append(aggregates, agg)
	}

	return &rewardsProjectorPb.GetProjectionAggregatesResponse{
		Status:     rpc.StatusOk(),
		Aggregates: aggregates,
	}, nil
}

// getRewardTypeAndUnits returns one reward type and units from an array of projected reward options, based on the following criteria:
//  1. if only one option is present, return the same.
//  2. if all options are of same type, return the option with the highest value
//  3. if options are of multiple type, return the option with the highest priority, where the order of priority in decreasing order is:
//     a. CASH
//     b. CREDIT_CARD_BILL_ERASER
//     c. FI_COINS
//     d. Others
//
// 4. In other cases, the first option is returned
func getRewardOptionOfInterest(rewardOptions []*rewardsProjectorPb.RewardOption) *rewardsProjectorPb.RewardOption {
	if rewardOptions == nil {
		return nil
	}

	// return the first option in case there's only one option
	if len(rewardOptions) == 1 {
		return rewardOptions[0]
	}

	rewardTypeToOptionIndexMap := map[rewardsPb.RewardType]int{}

	for i, option := range rewardOptions {
		rewardTypeToOptionIndexMap[option.GetRewardType()] = i
	}

	// return option having max units in case all options are of same type
	if len(rewardTypeToOptionIndexMap) == 1 {
		idxOfMaxUnitsOption := 0
		for i, option := range rewardOptions {
			if option.GetRewardUnits() > rewardOptions[idxOfMaxUnitsOption].GetRewardUnits() {
				idxOfMaxUnitsOption = i
			}
		}
		return rewardOptions[idxOfMaxUnitsOption]
	}

	// return option according to reward type priority
	if idx, ok := rewardTypeToOptionIndexMap[rewardsPb.RewardType_CASH]; ok {
		return rewardOptions[idx]
	}
	if idx, ok := rewardTypeToOptionIndexMap[rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER]; ok {
		return rewardOptions[idx]
	}
	if idx, ok := rewardTypeToOptionIndexMap[rewardsPb.RewardType_FI_COINS]; ok {
		return rewardOptions[idx]
	}

	return rewardOptions[0]
}
