// nolint:dupl
package consumer

import (
	"context"
	"github.com/epifi/gamma/api/connected_account/enums"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	errorPkg "github.com/pkg/errors"
	"go.uber.org/zap"

	caExternalPb "github.com/epifi/gamma/api/connected_account/external"
	dataCollectorPb "github.com/epifi/gamma/api/rewards/datacollector"
	"github.com/epifi/gamma/rewards/datacollector/model"
	"github.com/epifi/gamma/rewards/metrics"
)

func (rcs *RewardsConsumerService) ProcessCAAccountDataSyncEvent(ctx context.Context, req *caExternalPb.AccountDataSyncEvent) (*dataCollectorPb.ConsumerResponse, error) {
	if req.GetAccInstrumentType() != enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT {
		return getSuccessRes(), nil
	}

	logger.Debug(ctx, "Collected ca account data sync event", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Stringer("request", req))
	publisher, protoCollectedData, err := rcs.getCAAccountDataSyncProcessInfo(req)
	if err != nil {
		logger.Error(ctx, "error getting processing info", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(err))
		// added check since some case protoCollectData can be nil
		if protoCollectedData != nil {
			recordDataCollectorCollectionError(protoCollectedData.GetDataType(), err)
		}
		return getTransientFailureRes(), nil
	}

	id, err := publisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing ca data sync event collected data", zap.String(logger.ACTOR_ID, protoCollectedData.GetActorId()), zap.Error(err))
		recordDataCollectorCollectionError(protoCollectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Debug(ctx, "Pushed ca data sync event reward event", zap.String(logger.QUEUE_MESSAGE_ID, id), zap.String(logger.ACTOR_ID, protoCollectedData.GetActorId()))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(protoCollectedData.GetDataType().String())
	return getSuccessRes(), nil
}

func (rcs *RewardsConsumerService) getCAAccountDataSyncProcessInfo(req *caExternalPb.AccountDataSyncEvent) (queue.Publisher, *dataCollectorPb.CollectedData, error) {
	collectedData := &model.CaAccountDataSyncDataCollectedData{
		AccountDataSyncEvent: req,
	}
	protoCollectedData, err := collectedData.GetProtoCollectedData()
	if err != nil {
		return nil, nil, errorPkg.Wrap(err, "error converting epf import event data model to proto")
	}
	return rcs.dataCollectorEventPublisher, protoCollectedData, nil
}
