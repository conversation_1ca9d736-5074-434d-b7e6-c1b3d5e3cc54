package helper

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/encoding/protojson"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	commsPb "github.com/epifi/gamma/api/comms"
	docsPb "github.com/epifi/gamma/api/docs"
	orderSerivcePb "github.com/epifi/gamma/api/order"
	paymentNotificationPb "github.com/epifi/gamma/api/order/payment/notification"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	helperPb "github.com/epifi/gamma/api/rewards/notification/helper"
	rewardProjectionsPb "github.com/epifi/gamma/api/rewards/projector"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	accTypes "github.com/epifi/gamma/api/typesv2/account"
	usersPb "github.com/epifi/gamma/api/user"
	vgAccountPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"

	"github.com/epifi/gamma/accounts/config"
	"github.com/epifi/gamma/pkg/address"
	"github.com/epifi/gamma/pkg/pay"
)

func (h *HelperService) GetPdfAsAttachment(ctx context.Context, userDetails *usersPb.User, reward *rewardsPb.Reward, rewardOffer *rewardOffersPb.RewardOffer, pdfTemplate docsPb.PDFTemplate, prioritizedRewardType rewardsPb.RewardType, prioritizedRewardAmount float32) (*commsPb.EmailMessage_Attachment, error) {
	switch pdfTemplate {
	// currently this only contains tiering value back summary
	case docsPb.PDFTemplate_SAVINGS_STATEMENT_WITH_TIERING_REWARDS_SUMMARY:
		attachment, err := h.getSavingsStatementWithTieringRewardsSummary(ctx, userDetails, reward, rewardOffer, prioritizedRewardType, prioritizedRewardAmount)
		if err != nil {
			return nil, fmt.Errorf("failed to get savings statement with tiering rewards summary: %w", err)
		}
		return attachment, nil
	default:
		return nil, nil
	}
}

func (h *HelperService) getSavingsStatementWithTieringRewardsSummary(ctx context.Context, userDetails *usersPb.User, reward *rewardsPb.Reward, rewardOffer *rewardOffersPb.RewardOffer, prioritizedRewardType rewardsPb.RewardType, prioritizedRewardAmount float32) (*commsPb.EmailMessage_Attachment, error) {
	var (
		accountStatementResp    *vgAccountPb.GetAccountStatementResponse
		err                     error
		startTime               = time.Now()
		tierText                string
		valueBackPercentageText string
		finalTierText           string
		actorId                 = userDetails.GetActorId()

		// TODO(Sahil) : add handling to ignore more than a month old events.
		// Have not done this to test previous rewards in this flow.
		statementMonth = datetime.PreviousMonth(reward.GetCreatedAt().AsTime().In(datetime.IST))
		fromDate       = datetime.TimeToDateInLoc(statementMonth, datetime.IST)
		toDate         = datetime.TimeToDateInLoc(datetime.EndOfMonth(statementMonth), datetime.IST)

		deviceId, deviceToken, customerId string
		account                           *savingsPb.Account

		uniquePartnerRefIdToStatementTransactionMap   = make(map[string]*helperPb.Transaction)
		additionalParticularToStatementTransactionMap = make(map[string]*helperPb.Transaction)
	)

	// set the tier and value back percentage text
	tierText, ok := offerTypeToTierText[rewardOffer.GetOfferType()]
	if !ok {
		logger.Error(ctx, "offer type not supported yet", zap.String("offerType", rewardOffer.GetOfferType().String()), zap.String(logger.OFFER_ID, rewardOffer.GetId()), zap.String(logger.REWARD_ID, reward.GetId()))
		return nil, fmt.Errorf("offer type not supported yet")
	}
	valueBackPercentageText = offerTypeToValueBackPercentageText[rewardOffer.GetOfferType()]
	if rewardOffer.GetOfferType() == rewardsPb.RewardOfferType_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER {
		finalTierText = valueBackPercentageText
	} else {
		finalTierText = valueBackPercentageText + " (" + tierText + ")"
	}

	// fetch savings account details registered with Fi
	savingsAccountRes, savingsAccountErr := h.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{
			ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
				ActorId:                actorId,
				AccountProductOffering: accTypes.AccountProductOffering_APO_REGULAR,
				PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if savingsAccountRes == nil && savingsAccountErr != nil {
		logger.Error(ctx, "failed to get savings account details", zap.Error(savingsAccountErr))
		return nil, fmt.Errorf("failed to get savings account details: %w", savingsAccountErr)
	}
	account = savingsAccountRes.GetAccount()

	customerId, err = h.getCustomerID(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get customer id: %w", err)
	}

	deviceId, deviceToken, err = h.getActorDeviceDetails(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get actor device details: %w", err)
	}

	// setting different dates for the federal vg request due to known issue
	// where federal doesn't return correct data due to settlement time difference at their end
	fromDateForVgReq := datetime.TimeToDateInLoc(statementMonth.AddDate(0, 0, -2), datetime.IST)
	toDateForVgReq := datetime.TimeToDateInLoc(datetime.EndOfMonth(statementMonth).AddDate(0, 0, 2), datetime.IST)

	vendorGatewayReq := &vgAccountPb.GetAccountStatementRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		DeviceId:      deviceId,
		DeviceToken:   deviceToken,
		CustomerId:    customerId,
		AccountNumber: account.GetAccountNo(),
		PhoneNumber:   userDetails.GetProfile().GetPhoneNumber(),
		FromDate:      fromDateForVgReq,
		ToDate:        toDateForVgReq,
		PageNumber:    1,
		ApiType:       vgAccountPb.GetAccountStatementRequest_API_TYPE_STATEMENT_DR_API,
	}

	statementWithRewardsSummary := &helperPb.SavingsStatementWithTieringRewardsSummary{
		HeaderDetails: &helperPb.HeaderDetails{
			Title: &helperPb.IconText{
				Text: "Fi Plans Rewards Summary",
			},
			FromDate: fromDate,
			ToDate:   toDate,
		},
		UserDetails: &helperPb.UserDetails{
			Name:          userDetails.GetProfile().GetKycName().ToString(),
			Email:         userDetails.GetProfile().GetEmail(),
			AccountNumber: account.GetAccountNo(),
			Ifsc:          account.GetIfscCode(),
			Address:       address.ConvertPostalAddressToString(userDetails.GetProfile().GetAddresses()[types.AddressType_MAILING.String()]),
			PhoneNumber:   userDetails.GetProfile().GetPhoneNumber().GetNationalNumber(),
		},
		RewardsSummary: &helperPb.RewardsSummary{
			RewardType: &helperPb.IconText{
				IconUrlIdentifier: getRewardTypeSummaryIcon(prioritizedRewardType, reward.GetActionTime()),
				Text:              finalTierText,
			},
			RewardAmount: &helperPb.IconText{
				IconUrlIdentifier: getRewardTypeSymbolIcon(prioritizedRewardType, reward.GetActionTime()),
				Text:              money.ToDisplayStringInIndianFormatFromFloatValue(float64(prioritizedRewardAmount), 0),
			},
		},
	}

	// fetch all the transactions from statement api in paginated way
	// keeping a limit of 200 for safety
	for i := 0; i < 200; i++ {
		accountStatementResp, err = h.vgAccountClient.GetAccountStatementByDrApi(ctx, vendorGatewayReq)
		if rpcErr := epifigrpc.RPCError(accountStatementResp, err); rpcErr != nil {
			if accountStatementResp.GetStatus().IsRecordNotFound() {
				return nil, fmt.Errorf("account statement not found: %w", epifierrors.ErrRecordNotFound)
			}
			return nil, fmt.Errorf("failed to get account statement: %w", rpcErr)
		}

		// make the transaction data for pdf
		for _, individualTransactionData := range accountStatementResp.GetTransactionData() {
			// since we are fetching transactions with a buffer of 2 days,
			// we need to filter out the transactions which are not in the transactions which are not in the current IST calendar month
			if checkIfTimestampNotInCalenderMonth(individualTransactionData.GetTransactionTimestamp(), fromDate, toDate) {
				continue
			}

			transaction := &helperPb.Transaction{
				TransactionTimestamp: &helperPb.Timestamp{
					Seconds: individualTransactionData.GetTransactionTimestamp().GetSeconds(),
					Nanos:   individualTransactionData.GetTransactionTimestamp().GetNanos(),
				},
				TransactionDetails: &helperPb.Transaction_TransactionDetails{
					ToUserName: strings.ReplaceAll(individualTransactionData.GetTransactionDescription(), "\\", "\\\\"),
				},
				AmountDetails: &helperPb.Transaction_AmountDetails{
					Amount:      individualTransactionData.GetAmount(),
					AmountBadge: txnTypeMapToAmountBadge[individualTransactionData.GetTransactionType()],
				},
				// have to set the reward details to empty to avoid any issue in the pdf generation
				RewardDetails: &helperPb.Transaction_RewardDetails{
					RewardAmount: []*helperPb.IconText{},
				},
			}

			// update transaction details
			updateTransactionDetails(transaction, individualTransactionData)

			// map transaction id to transaction map
			uniquePartnerRefIdToStatementTransactionMap[individualTransactionData.GetParsedTxnParticulars().GetUniqPartnerRefId()] = transaction
			if individualTransactionData.GetAdditionalParticular() != "" {
				additionalParticularToStatementTransactionMap[individualTransactionData.GetAdditionalParticular()] = transaction
			}
			logger.Debug(ctx, "transaction details", zap.String("partnerRefId", individualTransactionData.GetParsedTxnParticulars().GetUniqPartnerRefId()))

			// append transaction to the list
			statementWithRewardsSummary.Transactions = append(statementWithRewardsSummary.GetTransactions(), transaction)
		}

		// break if no more rows are present
		if !accountStatementResp.GetHasMoreRows() {
			logger.Debug(ctx, "no more rows present in account statement", zap.String(logger.ACTOR_ID_V2, actorId))
			break
		}
		logger.Debug(ctx, "more rows present in account statement", zap.String(logger.ACTOR_ID_V2, actorId))
		vendorGatewayReq.PageNumber = accountStatementResp.GetPageNumber()
	}

	logger.Debug(ctx, "number of statement transactions", zap.Int("count", len(statementWithRewardsSummary.GetTransactions())))

	// update reward details
	err = h.updateRewardDetails(ctx, reward, uniquePartnerRefIdToStatementTransactionMap, additionalParticularToStatementTransactionMap, prioritizedRewardAmount, prioritizedRewardType)
	if err != nil {
		return nil, fmt.Errorf("failed to update reward details: %w", err)
	}

	// updating the transactions to only include the ones with rewards
	var finalStatementTransactions []*helperPb.Transaction
	for _, statementTransaction := range statementWithRewardsSummary.GetTransactions() {
		if len(statementTransaction.GetRewardDetails().GetRewardAmount()) > 0 {
			finalStatementTransactions = append(finalStatementTransactions, statementTransaction)
		}
	}
	statementWithRewardsSummary.Transactions = finalStatementTransactions

	pdfReq, err := h.getGeneratePdfRequestParameter(userDetails, account, statementWithRewardsSummary)
	if err != nil {
		logger.Error(ctx, "failed to get generate pdf request parameters", zap.Error(err))
		return nil, fmt.Errorf("failed to get generate pdf request parameters: %w", err)
	}

	// generate pdf body
	body, err := h.generatePdfBody(ctx, pdfReq)
	if err != nil {
		logger.Error(ctx, "failed to generate pdf and send notification", zap.Error(err))
		return nil, fmt.Errorf("failed to generate pdf and send notification: %w", err)
	}

	logger.Info(ctx, fmt.Sprintf("tiering reward summary statement pdf generated successfully in %f", time.Since(startTime).Seconds()), zap.String(logger.ACTOR_ID_V2, actorId))

	return &commsPb.EmailMessage_Attachment{
		FileContent:    body,
		FileName:       fetchUserDisplayPdfFileName(fromDate, toDate),
		Disposition:    commsPb.Disposition_ATTACHMENT,
		AttachmentType: "application/pdf",
	}, nil
}

func fetchUserDisplayPdfFileName(fromDate *date.Date, toDate *date.Date) string {
	fromDateString := fmt.Sprintf("%02d %s %04d", fromDate.GetDay(),
		time.Month(fromDate.GetMonth()).String()[:3], fromDate.GetYear())
	toDateString := fmt.Sprintf("%02d %s %04d", toDate.GetDay(),
		time.Month(toDate.GetMonth()).String()[:3], toDate.GetYear())

	return fmt.Sprintf("Fi Plans Rewards Statement %s - %s", fromDateString, toDateString)
}

// generatePdf calls docs service to generate the pdf and then sends the pdf as attachment in email
// in case of successful pdf generation and a failure notification is sent otherwise
func (h *HelperService) generatePdfBody(ctx context.Context, pdfReq *docsPb.GeneratePdfRequest) ([]byte, error) {
	pdfRes, err := h.docsClient.GeneratePdf(ctx, pdfReq)
	if rpcErr := epifigrpc.RPCError(pdfRes, err); rpcErr != nil {
		logger.Error(ctx, "failed to generate pdf", zap.Error(rpcErr))
		return nil, fmt.Errorf("failed to generate pdf: %w", rpcErr)
	}

	fileContent, err := http.Get(pdfRes.GetFileUrl())
	if err != nil {
		logger.Error(ctx, "failed to get content from the pre signed url", zap.Error(err))
		return nil, fmt.Errorf("failed to get content from the pre signed url: %w", err)
	}

	defer func() {
		_ = fileContent.Body.Close()
	}()
	body, err := io.ReadAll(fileContent.Body)
	if err != nil {
		logger.Error(ctx, "failed to read content from the pre signed url", zap.Error(err))
		return nil, fmt.Errorf("failed to read content from the pre signed url: %w", err)
	}

	return body, nil
}

// getGeneratePdfRequestParameter prepares and returns the request parameters for generating a pdf
func (h *HelperService) getGeneratePdfRequestParameter(userDetails *usersPb.User, account *savingsPb.Account, statement *helperPb.SavingsStatementWithTieringRewardsSummary) (*docsPb.GeneratePdfRequest, error) {
	m := protojson.MarshalOptions{EmitUnpopulated: false}
	data, err := m.Marshal(statement)
	logger.DebugNoCtx(fmt.Sprintf("pdf data: %s", data))
	pdfData := ReplaceWithEscapeSequencesInMarshalledData(data)
	logger.DebugNoCtx(fmt.Sprintf("pdf data after replacing escape sequences: %s", pdfData))

	// fetch pdf password
	password := fetchPdfPassword(userDetails.GetProfile().GetKycName(), userDetails.GetProfile().GetDateOfBirth())

	// generate file name
	fileName := account.GetAccountNo() + time.Now().String()

	return &docsPb.GeneratePdfRequest{
		Data:                pdfData,
		OwnerPassword:       password,
		UserPassword:        password,
		ExpiryTimeInSeconds: StatementPreSignedURLExpiryTime,
		FileName:            fileName,
		PdfTemplate:         docsPb.PDFTemplate_SAVINGS_STATEMENT_WITH_TIERING_REWARDS_SUMMARY,
	}, err
}

func fetchPdfPassword(userName *commontypes.Name, dateOfBirth *date.Date) string {
	fullNameWithoutSpace := strings.ReplaceAll(userName.GetFirstName()+userName.GetMiddleName()+userName.GetLastName(), " ", "")
	day := fmt.Sprintf("%02d", dateOfBirth.GetDay())
	month := fmt.Sprintf("%02d", dateOfBirth.GetMonth())

	return strings.ToUpper(fullNameWithoutSpace[0:min(4, len(fullNameWithoutSpace))]) + day + month
}

func (h *HelperService) getCustomerID(ctx context.Context, actorId string) (string, error) {
	res, err := h.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_ActorId{ActorId: actorId},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "error in get user", zap.Error(rpcErr))
		return "", fmt.Errorf("error in GetUser,err : %w", rpcErr)
	}

	bcResp, bcErr := h.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(bcResp, bcErr); rpcErr != nil {
		logger.Error(ctx, "error in get bc", zap.Error(rpcErr))
		return "", rpcErr
	}
	return bcResp.GetBankCustomer().GetVendorCustomerId(), nil
}

// getActorDeviceDetails fetches actor device registration details
func (h *HelperService) getActorDeviceDetails(ctx context.Context, actorId string) (string, string, error) {
	res, err := h.authClient.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "error in get device auth", zap.Error(rpcErr))
		return "", "", fmt.Errorf("error in GetDeviceAuth,err : %w", rpcErr)
	}
	return res.GetDevice().GetDeviceId(), res.GetDeviceToken(), nil
}

// updateTransactionDetails updates the transaction details in case other actor name isn't present, but we have other identifiers
func updateTransactionDetails(transaction *helperPb.Transaction, detail *vgAccountPb.TransactionV1) {
	var (
		parsedTxn            bool
		parsedTxnParticulars = detail.GetParsedTxnParticulars()
	)

	if strings.Compare(detail.GetParsedTxnParticulars().GetOtherActorName(), config.OtherActorName) == 0 {

		switch parsedTxnParticulars.GetPiIdentifier().(type) { //nolint:exhaustive
		case *paymentNotificationPb.ParsedTxnParticulars_Vpa:
			transaction.GetTransactionDetails().ToUserName = parsedTxnParticulars.GetVpa()
			parsedTxn = true
		case *paymentNotificationPb.ParsedTxnParticulars_Account:
			if strings.Compare(parsedTxnParticulars.GetAccount().GetAccountNumber(), pay.DefaultAccountNumber) != 0 &&
				strings.Compare(parsedTxnParticulars.GetAccount().GetAccountNumber(), pay.FederalAccountGenericAccountNumber) != 0 {
				transaction.GetTransactionDetails().ToUserName = parsedTxnParticulars.GetAccount().GetAccountNumber()
				parsedTxn = true
			}
		}

		switch parsedTxnParticulars.GetTxnCategory() { //nolint:exhaustive
		case paymentNotificationPb.ParsedTxnParticulars_ATM_WITHDRAWAL:
			transaction.GetTransactionDetails().ToUserName = strings.ReplaceAll(parsedTxnParticulars.GetAtmMachineName(), "\\", "\\\\")
			parsedTxn = true
		case paymentNotificationPb.ParsedTxnParticulars_CREDIT_INTEREST:
			transaction.GetTransactionDetails().ToUserName = config.IntrestCredit
			parsedTxn = true
		case paymentNotificationPb.ParsedTxnParticulars_MISCELLANEOUS_CHARGES:
			parsedTxn = true
		}

		if parsedTxn {
			transaction.GetTransactionDetails().Comments = detail.GetRemarks()
		}
	} else {
		// if other actor name is present, populate merchant details, remarks and payment method
		transaction.GetTransactionDetails().ToUserName = strings.ReplaceAll(parsedTxnParticulars.GetOtherActorName(), "\\", "\\\\")
		transaction.GetTransactionDetails().Comments = strings.ReplaceAll(detail.GetRemarks(), "\\", "\\\\")
	}
}

func (h *HelperService) updateRewardDetails(ctx context.Context, reward *rewardsPb.Reward, uniquePartnerRefIdToStatementTransactionMap, additionalParticularToStatementTransactionMap map[string]*helperPb.Transaction, prioritizedRewardAmount float32, prioritizedRewardType rewardsPb.RewardType) error {
	statementTransactionToProjectionsMap, err := h.getStatementTransactionToProjectionsMap(ctx, reward, uniquePartnerRefIdToStatementTransactionMap, additionalParticularToStatementTransactionMap)
	if err != nil {
		return fmt.Errorf("failed to get projection to transaction statement map: %w", err)
	}

	var finalTxnsProjectedTotalAmount float32
	for _, projections := range statementTransactionToProjectionsMap {
		for _, projection := range projections {
			for _, rewardContribution := range projection.GetRewardContributions().GetRewardUnitsWithTypes() {
				if rewardContribution.GetRewardType() == prioritizedRewardType {
					finalTxnsProjectedTotalAmount += rewardContribution.GetRewardUnits()
				}
			}
		}
	}

	if finalTxnsProjectedTotalAmount != prioritizedRewardAmount {
		logger.Error(ctx, "reward amount and final transactions total amount mismatch",
			zap.Float32("prioritizedRewardAmount", prioritizedRewardAmount),
			zap.Float32("finalTxnsProjectedTotalAmount", finalTxnsProjectedTotalAmount),
			zap.String("prioritizedRewardType", prioritizedRewardType.String()))
		return fmt.Errorf("reward amount and final transactions total amount mismatch")
	}

	for statementTransaction, projections := range statementTransactionToProjectionsMap {
		var rewardDetails []*helperPb.IconText
		for _, projection := range projections {
			rewardContributions := projection.GetRewardContributions().GetRewardUnitsWithTypes()
			// Sort contributions to put prioritized reward type first
			sort.Slice(rewardContributions, func(i, j int) bool {
				return rewardContributions[i].GetRewardType() == prioritizedRewardType
			})

			for _, rewardContribution := range rewardContributions {
				if rewardContribution.GetRewardUnits() != 0 {
					if icon := getRewardTypeWithBgIcon(rewardContribution.GetRewardType(), reward.GetActionTime()); icon != "" {
						rewardDetails = append(rewardDetails, &helperPb.IconText{
							IconUrlIdentifier: icon,
							Text:              money.ToDisplayStringInIndianFormatFromFloatValue(float64(rewardContribution.GetRewardUnits()), 0),
						})
					}
				}
			}
		}
		statementTransaction.GetRewardDetails().RewardAmount = rewardDetails
	}

	return nil
}

// nolint: dupl
func (h *HelperService) getStatementTransactionToProjectionsMap(ctx context.Context, reward *rewardsPb.Reward, uniquePartnerRefIdToStatementTransactionMap, additionalParticularToStatementTransactionMap map[string]*helperPb.Transaction) (map[*helperPb.Transaction][]*rewardProjectionsPb.Projection, error) {
	var (
		getOrdersWithTransactionsPageSize = 50
		getRewardProjectionsPageCtxReq    = &rpcPb.PageContextRequest{
			PageSize: uint32(30),
		}

		rewardProjections      []*rewardProjectionsPb.Projection
		orderIdentifiers       []*orderSerivcePb.OrderIdentifier
		ordersWithTransactions []*orderSerivcePb.OrderWithTransactions

		refIdToProjectionsMap                = make(map[string][]*rewardProjectionsPb.Projection)
		refIdToStatementTransactionMap       = make(map[string]*helperPb.Transaction)
		statementTransactionToProjectionsMap = make(map[*helperPb.Transaction][]*rewardProjectionsPb.Projection)
	)

	// fetch reward rewardProjections and extract out the ref ids
	// keeping a limit of 200 for safety
	for i := 0; i < 200; i++ {
		resp, err := h.rewardProjectionsClient.GetRewardsProjections(ctx, &rewardProjectionsPb.GetRewardsProjectionsRequest{
			ActorId: reward.GetActorId(),
			Filters: &rewardProjectionsPb.GetRewardsProjectionsRequest_Filters{
				ActionType: []rewardsPb.CollectedDataType{
					rewardsPb.CollectedDataType_ORDER,
				},
				RewardIds: []string{reward.GetId()},
			},
			PageCtxRequest: getRewardProjectionsPageCtxReq,
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
			return nil, rpcErr
		}
		rewardProjections = append(rewardProjections, resp.GetIndividualProjections().GetProjections()...)
		pageContext := resp.GetPageCtxResponse()
		if !pageContext.GetHasAfter() {
			break
		}
		getRewardProjectionsPageCtxReq.Token = &rpcPb.PageContextRequest_AfterToken{AfterToken: pageContext.GetAfterToken()}
		// keeping a small delay considering rpc latency
		time.Sleep(20 * time.Millisecond)
	}

	// This should not happen as a reward reaches to notification service if it is not of no reward type
	// and if a reward is not of no reward type, it should have projections
	if len(rewardProjections) == 0 {
		logger.Error(ctx, "no reward projections found for the given reward", zap.String(logger.REWARD_ID, reward.GetId()))
		return nil, fmt.Errorf("no reward projections found for the given reward")
	}

	if len(rewardProjections) > 200 {
		logger.WarnWithCtx(ctx, "more than 200 reward projections found for the given reward", zap.String(logger.REWARD_ID, reward.GetId()))
	}

	// extract out the order identifiers from the reward projections
	for _, projection := range rewardProjections {
		// check if the ref id is already present in the order identifiers
		// NOTE : Handling for multiple projections is present but not enabled currently
		if refIdToProjectionsMap[projection.GetRefId()] != nil {
			logger.Error(ctx, "multiple projections found for same ref id", zap.String(logger.REFERENCE_ID, projection.GetRefId()))
			return nil, fmt.Errorf("multiple projections found for same ref id")
		}
		refIdToProjectionsMap[projection.GetRefId()] = append(refIdToProjectionsMap[projection.GetRefId()], projection)
		orderIdentifiers = append(orderIdentifiers, &orderSerivcePb.OrderIdentifier{
			Identifier: &orderSerivcePb.OrderIdentifier_ExternalId{
				ExternalId: projection.GetRefId(),
			},
		})
	}

	// fetch orders with transactions in paginated way
	for i := 0; i < len(orderIdentifiers); i += getOrdersWithTransactionsPageSize {
		resp, err := h.ordersClient.GetOrdersWithTransactions(ctx, &orderSerivcePb.GetOrdersWithTransactionsRequest{
			// this takes care of overflow
			OrderIdentifiers: lo.Slice(orderIdentifiers, i, i+getOrdersWithTransactionsPageSize),
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
			logger.Error(ctx, "failed to get orders with transactions", zap.Error(rpcErr))
			return nil, rpcErr
		}
		ordersWithTransactions = append(ordersWithTransactions, resp.GetOrderWithTransactions()...)
		// keeping a small delay considering rpc latency
		time.Sleep(20 * time.Millisecond)
	}

	// check if the order identifiers and orders with transactions count mismatch
	// this would also take care of the case when no orders are found for the given order identifiers
	if len(orderIdentifiers) != len(ordersWithTransactions) {
		logger.Error(ctx, "order identifiers and order with transactions count mismatch", zap.Int("orderIdentifiers", len(orderIdentifiers)), zap.Int("orderWithTransactions", len(ordersWithTransactions)))
		return nil, fmt.Errorf("order identifiers and order with transactions count mismatch")
	}

	// fetch order with transactions and populate the refIdToStatementTransactionMap
	for i, orderWithTransaction := range ordersWithTransactions {
		logger.Debug(ctx, "inside populating refIdToStatementTransactionMap", zap.Int("index", i), zap.String(logger.EXTERNAL_ID, orderWithTransaction.GetOrder().GetExternalId()))
		var isStatementTransactionFound bool
		for _, transaction := range orderWithTransaction.GetTransactions() {
			// check if credit partner ref id is equal to unique partner ref id
			if statementTransaction, ok := uniquePartnerRefIdToStatementTransactionMap[transaction.GetPartnerRefIdDebit()]; ok {
				refIdToStatementTransactionMap[orderWithTransaction.GetOrder().GetExternalId()] = statementTransaction
				isStatementTransactionFound = true
				break
			}
			// check if debit partner ref id is equal to unique partner ref id
			if statementTransaction, ok := uniquePartnerRefIdToStatementTransactionMap[transaction.GetPartnerRefIdCredit()]; ok {
				refIdToStatementTransactionMap[orderWithTransaction.GetOrder().GetExternalId()] = statementTransaction
				isStatementTransactionFound = true
				break
			}
			// check if transaction req id is equal to additional particular
			if statementTransaction, ok := additionalParticularToStatementTransactionMap[transaction.GetReqId()]; ok {
				refIdToStatementTransactionMap[orderWithTransaction.GetOrder().GetExternalId()] = statementTransaction
				isStatementTransactionFound = true
				break
			}
		}
		if !isStatementTransactionFound {
			logger.Error(ctx, "no statement transaction found for the transactions present in the give order", zap.String(logger.REFERENCE_ID, orderWithTransaction.GetOrder().GetExternalId()), zap.String(logger.ACTOR_ID_V2, reward.GetActorId()))
		}
	}

	for refId, projections := range refIdToProjectionsMap {
		logger.Debug(ctx, "inside populating statementTransactionToProjectionsMap", zap.String(logger.REFERENCE_ID, refId), zap.String(logger.ACTOR_ID_V2, reward.GetActorId()))
		if statementTransaction, ok := refIdToStatementTransactionMap[refId]; ok {
			statementTransactionToProjectionsMap[statementTransaction] = projections
			continue
		}
		logger.Error(ctx, "no statement transaction found for the ref id", zap.String(logger.REFERENCE_ID, refId))
	}

	return statementTransactionToProjectionsMap, nil
}
