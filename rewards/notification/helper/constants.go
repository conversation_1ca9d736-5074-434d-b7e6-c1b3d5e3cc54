package helper

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commsPb "github.com/epifi/gamma/api/comms"
	docsPb "github.com/epifi/gamma/api/docs"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	vgAccountPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
)

const (
	StatementPreSignedURLExpiryTime = 60 * 60 // 1 hour

	// icon text holders
	fiCoinsSymbolIcon   = "fi_coins_symbol"
	fiCoinsWithBgIcon   = "fi_coins_with_bg"
	fiCoinsSummaryIcon  = "fi_coins_summary"
	fiPointsSymbolIcon  = "fi_points_symbol"
	fiPointsWithBgIcon  = "fi_points_with_bg"
	fiPointsSummaryIcon = "fi_points_summary"
	cashSymbolIcon      = "cash_symbol"
	cashWithBgIcon      = "cash_with_bg"
	cashSummaryIcon     = "cash_summary"

	// icon urls
	partialRewardsEarnedIconUrl = "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/coin-rain.png"
	fullRewardsEarnedIconUrl    = "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/fire.png"
	tickSymbolIconUrl           = "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/check-circle.png"

	progressRingColorGreen = "#005244"
)

var (
	// emailTemplateToMaxDurationSinceActionTimeMap defines the maximum duration since action time for each email template
	// post which the email should not be sent.
	emailTemplateToMaxDurationSinceActionTimeMap = map[commsPb.EmailType]time.Duration{
		commsPb.EmailType_TIERING_REWARDS_SUMMARY_EMAIL: 28 * 24 * time.Hour, // 28 days
	}

	emailTemplateToPdfTemplateMap = map[commsPb.EmailType]docsPb.PDFTemplate{
		commsPb.EmailType_TIERING_REWARDS_SUMMARY_EMAIL: docsPb.PDFTemplate_SAVINGS_STATEMENT_WITH_TIERING_REWARDS_SUMMARY,
	}

	txnTypeMapToAmountBadge = map[vgAccountPb.TransactionType]string{
		vgAccountPb.TransactionType_DEBIT:  "DEBIT",
		vgAccountPb.TransactionType_CREDIT: "CREDIT",
	}

	offerTypeToValueBackPercentageText = map[rewardsPb.RewardOfferType]string{
		rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER:               "1% Back",
		rewardsPb.RewardOfferType_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER:           "2% Back",
		rewardsPb.RewardOfferType_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER:        "1% Back",
		rewardsPb.RewardOfferType_SALARY_BASIC_TIER_1_PERCENT_CASHBACK_OFFER:       "1% Back",
		rewardsPb.RewardOfferType_SALARY_TIER_2_PERCENT_CASHBACK_OFFER:             "2% Back",
		rewardsPb.RewardOfferType_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER: "2% Back",
		rewardsPb.RewardOfferType_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER:   "1% Back",
		rewardsPb.RewardOfferType_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER:   "2% Back",
		rewardsPb.RewardOfferType_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER:   "3% Back",
	}

	offerTypeToTierText = map[rewardsPb.RewardOfferType]string{
		rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER:               "Plus",
		rewardsPb.RewardOfferType_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER:           "Infinite",
		rewardsPb.RewardOfferType_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER:        "Salary Lite",
		rewardsPb.RewardOfferType_SALARY_BASIC_TIER_1_PERCENT_CASHBACK_OFFER:       "Salary Basic",
		rewardsPb.RewardOfferType_SALARY_TIER_2_PERCENT_CASHBACK_OFFER:             "Salary",
		rewardsPb.RewardOfferType_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER: "Infinite or Salary",
		rewardsPb.RewardOfferType_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER:   "Prime",
		rewardsPb.RewardOfferType_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER:   "Prime",
		rewardsPb.RewardOfferType_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER:   "Prime",
	}

	iconURLIdentifierToURL = map[string]string{
		fiCoinsSymbolIcon:   "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/mailer/fi_coins_symbol.png",
		fiCoinsWithBgIcon:   "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/mailer/fi_coins_with_bg.png",
		fiCoinsSummaryIcon:  "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/mailer/fi_coins_summary.png",
		fiPointsSymbolIcon:  "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/mailer/fi_coins_symbol.png",
		fiPointsWithBgIcon:  "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/mailer/fi_points_with_bg.png",
		fiPointsSummaryIcon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/mailer/fi_points_summary.png",
		cashSymbolIcon:      "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/mailer/cash_symbol.png",
		cashWithBgIcon:      "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/mailer/cash_with_bg.png",
		cashSummaryIcon:     "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/mailer/cash_summary.png",
	}
)

// getRewardTypeWithBgIcon returns the icon with background for a given reward type.
// actionTime is used to determine whether to return "fi_coins" or "fi_points" icon.
func getRewardTypeWithBgIcon(rewardType rewardsPb.RewardType, actionTime *timestampPb.Timestamp) string {
	switch rewardType {
	case rewardsPb.RewardType_FI_COINS:
		return accrualPkg.ReturnApplicableValue(fiCoinsWithBgIcon, fiPointsWithBgIcon, actionTime, true).(string)
	case rewardsPb.RewardType_CASH:
		return cashWithBgIcon
	default:
		return ""
	}
}

// getRewardTypeSummaryIcon returns the summary icon for a given reward type.
// actionTime is used to determine whether to return "fi_coins" or "fi_points" icon.
func getRewardTypeSummaryIcon(rewardType rewardsPb.RewardType, actionTime *timestampPb.Timestamp) string {
	switch rewardType {
	case rewardsPb.RewardType_FI_COINS:
		return accrualPkg.ReturnApplicableValue(fiCoinsSummaryIcon, fiPointsSummaryIcon, actionTime, true).(string)
	case rewardsPb.RewardType_CASH:
		return cashSummaryIcon
	default:
		return ""
	}
}

// getRewardTypeSymbolIcon returns the symbol icon for a given reward type.
// actionTime is used to determine whether to return "fi_coins" or "fi_points" icon.
func getRewardTypeSymbolIcon(rewardType rewardsPb.RewardType, actionTime *timestampPb.Timestamp) string {
	switch rewardType {
	case rewardsPb.RewardType_CASH:
		return cashSymbolIcon
	case rewardsPb.RewardType_FI_COINS:
		return accrualPkg.ReturnApplicableValue(fiCoinsSymbolIcon, fiPointsSymbolIcon, actionTime, true).(string)
	default:
		return ""
	}
}
