package search

import (
	"github.com/Knetic/govaluate"
	"github.com/pkg/errors"

	helper2 "github.com/epifi/gamma/rewards/generator/ruleengine/fact/helper"

	"github.com/epifi/gamma/api/search/enums"
	searchEventsPb "github.com/epifi/gamma/api/search/events"
	txn "github.com/epifi/gamma/api/search/txn"
	"github.com/epifi/gamma/rewards/generator/model"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/common"
	"github.com/epifi/gamma/rewards/helper"
)

type UserSearchFact struct {
	*common.CommonFact
	UserSearchEvent *searchEventsPb.UserSearchEvent
	Reward          model.Reward
}

var _ common.IFact = &UserSearchFact{}

func (us *UserSearchFact) GetExpressionFunctionMap() map[string]govaluate.ExpressionFunction {
	expressionFunctionMap := map[string]govaluate.ExpressionFunction{
		"GetOrderBadgeCount": func(args ...interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, errors.New("GetOrderBadgeCount expects single argument")
			}
			orderBadgeString, ok := args[0].(string)
			if !ok {
				return nil, errors.New("passed argument should be string")
			}
			orderBadge := txn.AmountBadge(txn.AmountBadge_value[orderBadgeString])
			if orderBadge == txn.AmountBadge_AMOUNT_TYP_UNSPECIFIED {
				return 0, nil
			}
			count := 0
			for _, od := range us.UserSearchEvent.GetOrderDetailForRewards() {
				if od.GetAmountBadge() == orderBadge {
					count += 1
				}
			}
			return float64(count), nil
		},
		// expects a list of account type in argument(FI/NON-FI/EMAIL)
		// IsResultForAnyGivenAccountTypes checks if any of the givens account types is present in the search result: return true/false
		"IsResultForAnyGivenAccountTypes": func(args ...interface{}) (interface{}, error) {
			if len(args) == 0 {
				return nil, errors.New("IsResultForAnyGivenAccountTypes expects at least one arg")
			}
			accountsTypeList, err := helper2.TypecastToStringList(args)
			if err != nil {
				return nil, errors.Wrap(err, "IsResultForAnyGivenAccountTypes: error converting accounts arg to string list")
			}

			accountTypeMap := make(map[string]struct{})

			for _, searchResult := range us.UserSearchEvent.GetSearchResultUnits() {
				if searchResult.GetRespType() != enums.ResponseType_TRANSACTION && searchResult.GetRespType() != enums.ResponseType_SUMMARY {
					continue
				}
				switch searchResult.GetTabName() {
				case enums.TabName_TAB_NAME_UNSPECIFIED:
					continue
				case enums.TabName_GMAIL:
					accountTypeMap["GMAIL"] = struct{}{}
				case enums.TabName_FI:
					accountTypeMap["FI"] = struct{}{}
				case enums.TabName_HDFC, enums.TabName_ICICI:
					accountTypeMap["NON-FI"] = struct{}{}
				default:

				}
			}

			for _, accountType := range accountsTypeList {
				if _, isAccountTypePresent := accountTypeMap[accountType]; isAccountTypePresent {
					return true, nil
				}
			}

			return false, nil
		},
		// expects a list of account type in argument(FI/NON-FI/EMAIL)
		// GetTotalTxnResultsCountForAccountTypes returns total count of transaction results having given account types
		"GetTotalTxnResultsCountForAccountTypes": func(args ...interface{}) (interface{}, error) {
			if len(args) == 0 {
				return nil, errors.New("GetTotalTxnResultsCountForAccountTypes expects at least one arg")
			}
			accountsTypeList, err := helper2.TypecastToStringList(args)
			if err != nil {
				return nil, errors.Wrap(err, "GetTotalTxnResultsCountForAccountTypes: error converting accounts arg to string list")
			}

			accountTypeMap := map[string]int{}

			for _, searchResult := range us.UserSearchEvent.GetSearchResultUnits() {
				if searchResult.GetRespType() != enums.ResponseType_TRANSACTION {
					continue
				}
				switch searchResult.GetTabName() {
				case enums.TabName_TAB_NAME_UNSPECIFIED:
					continue
				case enums.TabName_GMAIL:
					accountTypeMap["GMAIL"] += len(searchResult.GetOrderDetailForRewards())
				case enums.TabName_FI:
					accountTypeMap["FI"] += len(searchResult.GetOrderDetailForRewards())
				case enums.TabName_HDFC, enums.TabName_ICICI:
					accountTypeMap["NON-FI"] += len(searchResult.GetOrderDetailForRewards())
				default:

				}
			}

			resultsAccountsCount := 0
			for _, accountType := range accountsTypeList {
				resultsAccountsCount += accountTypeMap[accountType]
			}

			return float64(resultsAccountsCount), nil
		},
		// expects a list of account type in argument(FI/NON-FI/EMAIL)
		// GetTotalResultsCountForAccountTypes returns total count of results having given account types
		"GetTotalResultsCountForAccountTypes": func(args ...interface{}) (interface{}, error) {
			if len(args) == 0 {
				return nil, errors.New("GetTotalResultsCountForAccountTypes expects at least one arg")
			}
			accountsTypeList, err := helper2.TypecastToStringList(args)
			if err != nil {
				return nil, errors.Wrap(err, "GetTotalResultsCountForAccountTypes: error converting accounts arg to string list")
			}

			accountTypeMap := map[string]int{}

			for _, searchResult := range us.UserSearchEvent.GetSearchResultUnits() {
				if searchResult.GetRespType() != enums.ResponseType_TRANSACTION && searchResult.GetRespType() != enums.ResponseType_SUMMARY {
					continue
				}
				switch searchResult.GetTabName() {
				case enums.TabName_TAB_NAME_UNSPECIFIED:
					continue
				case enums.TabName_GMAIL:
					accountTypeMap["GMAIL"] += 1
				case enums.TabName_FI:
					accountTypeMap["FI"] += 1
				case enums.TabName_HDFC, enums.TabName_ICICI:
					accountTypeMap["NON-FI"] += 1
				default:

				}
			}

			resultsAccountsCount := 0
			for _, accountType := range accountsTypeList {
				resultsAccountsCount += accountTypeMap[accountType]
			}

			return float64(resultsAccountsCount), nil
		},
		// GetDurationSinceOnbInMinutes returns the duration since onb completion after which search event was triggered for the actor.
		"GetDurationSinceOnbInMinutes": func(args ...interface{}) (interface{}, error) {
			onbCompletionTime, err := us.GetOnbCompletionTimeForActor()
			if err != nil {
				return nil, errors.Wrap(err, "GetDurationSinceOnbInMinutes : error fetching GetOnbCompletionTimeForActor")
			}
			eventTime := us.UserSearchEvent.GetEventTimestamp()
			return eventTime.AsTime().Sub(*onbCompletionTime).Minutes(), nil
		},
		// returns the onb completion timestamp for the actor for whom fact is created.
		"GetOnbCompletionTimestampForUserInSecs": func(args ...interface{}) (interface{}, error) {
			onbCompletionTime, err := us.GetOnbCompletionTimeForActor()
			if err != nil {
				return nil, err
			}
			return float64(onbCompletionTime.UnixMilli() / 1000), nil
		},
	}
	return helper.MergeExpressionFunctionMap(expressionFunctionMap, us.CommonFact.GetExpressionFunctionMap())
}

func (us *UserSearchFact) GetExpressionParametersMap() map[string]interface{} {
	return map[string]interface{}{}
}
