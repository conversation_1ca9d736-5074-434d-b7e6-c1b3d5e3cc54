package ruleengine

import (
	"github.com/epifi/be-common/pkg/datetime"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	projectorPb "github.com/epifi/gamma/api/rewards/projector"
)

// getTieringRewardUnitsAndProjectionIdsToRewardOptionMapWithDailyUpperCap is a tiering specific implementation of daily upper cap -
// 1. Creates a list of list, bucketing all the projections on a day basis
// 2. Gets the negative projection to be accounted, if any available
// 3. For each day, caps all transactions exceeding the daily cap restriction
// 4. Ignores the negative projection if there's a daily cap hit before the txn
// nolint: funlen
func (re *CustomRuleEngine) getTieringRewardUnitsAndProjectionIdsToRewardOptionMapWithDailyUpperCap(projections []*projectorPb.Projection, dailyUpperCap float32, overallUpperCap float32, rewardType rewardsPb.RewardType) (float64, map[string]*projectorPb.RewardOption) {
	dailyProjections := make([][]*projectorPb.Projection, 0)
	var lastDayProjections []*projectorPb.Projection
	var lastDay string

	for _, projection := range projections {
		currentDay := projection.GetActionTime().AsTime().In(datetime.IST).Format("2006-01-02")
		// if it's a new projection day, we flush the last day projections to daily projections list
		if currentDay != lastDay {
			if len(lastDayProjections) != 0 {
				dailyProjections = append(dailyProjections, lastDayProjections)
			}
			// instantiate new list of projections for the new projection day
			lastDayProjections = []*projectorPb.Projection{projection}
			lastDay = currentDay
		} else {
			lastDayProjections = append(lastDayProjections, projection)
		}
	}
	if len(lastDayProjections) != 0 {
		dailyProjections = append(dailyProjections, lastDayProjections)
	}

	var (
		overallRewardUnits            = float32(0)
		projectionIdToRewardOptionMap = make(map[string]*projectorPb.RewardOption)
	)

	// we start overall reward units with negative projection amount, if we want to account negative projection
	negativeProjection, negativeProjectionDayIdx := re.getTieringRewardNegativeProjectionToBeAccounted(dailyProjections, dailyUpperCap, rewardType)
	var shouldAccountNegativeProjection bool
	if negativeProjection != nil {
		shouldAccountNegativeProjection = true
	}
	if shouldAccountNegativeProjection {
		for _, option := range negativeProjection.GetProjectedOptions().GetRewardUnitsWithTypes() {
			if option.GetRewardType() == rewardType {
				overallRewardUnits = option.GetRewardUnits()
				break
			}
		}
	}

	for dayIdx, dayProjections := range dailyProjections {
		var dailyRewardUnits = float32(0)
		// we start daily reward units with negative projection amount, if we want to account negative projection on that day
		if shouldAccountNegativeProjection && dayIdx == negativeProjectionDayIdx {
			for _, option := range negativeProjection.GetProjectedOptions().GetRewardUnitsWithTypes() {
				if option.GetRewardType() == rewardType {
					dailyRewardUnits = option.GetRewardUnits()
					break
				}
			}
		}

		for _, projection := range dayProjections {
			for _, option := range projection.GetProjectedOptions().GetRewardUnitsWithTypes() {
				if option.GetRewardType() == rewardType {
					if option.GetRewardUnits() >= 0 {
						switch {
						// if overall reward units is greater than overall upper cap, actual will be zero
						case overallRewardUnits >= overallUpperCap:
							projectionIdToRewardOptionMap[projection.GetId()] = &projectorPb.RewardOption{
								RewardType:                 rewardType,
								RewardUnits:                ZeroRewardUnits,
								RewardTypeSpecificMetadata: option.GetRewardTypeSpecificMetadata(),
							}
						// if overall reward units + projected reward units is greater than overall upper cap:
						// maximum units that can be rewarded is equal to overallUpperCap - overallRewardUnits
						// 1. if dailyRewardUnits already exceeds the dailyUpperCap, zero units will be rewarded
						// 2. if dailyRewardUnits + maxRewardableUnits exceeds the dailyUpperCap, dailyUpperCap - dailyRewardUnits units will be rewarded
						// 3. if dailyRewardUnits + maxRewardableUnits is less than dailyUpperCap, maxRewardableUnits units will be rewarded
						case overallRewardUnits+option.GetRewardUnits() >= overallUpperCap:
							maxRewardableUnits := overallUpperCap - overallRewardUnits
							switch {
							case dailyRewardUnits >= dailyUpperCap:
								projectionIdToRewardOptionMap[projection.GetId()] = &projectorPb.RewardOption{
									RewardType:                 rewardType,
									RewardUnits:                ZeroRewardUnits,
									RewardTypeSpecificMetadata: option.GetRewardTypeSpecificMetadata(),
								}
							case dailyRewardUnits+maxRewardableUnits >= dailyUpperCap:
								projectionIdToRewardOptionMap[projection.GetId()] = &projectorPb.RewardOption{
									RewardType:                 rewardType,
									RewardUnits:                dailyUpperCap - dailyRewardUnits,
									RewardTypeSpecificMetadata: option.GetRewardTypeSpecificMetadata(),
								}
								overallRewardUnits += dailyUpperCap - dailyRewardUnits
								dailyRewardUnits += dailyUpperCap - dailyRewardUnits
							case dailyRewardUnits+maxRewardableUnits < dailyUpperCap:
								projectionIdToRewardOptionMap[projection.GetId()] = &projectorPb.RewardOption{
									RewardType:                 rewardType,
									RewardUnits:                maxRewardableUnits,
									RewardTypeSpecificMetadata: option.GetRewardTypeSpecificMetadata(),
								}
								overallRewardUnits += maxRewardableUnits
								dailyRewardUnits += maxRewardableUnits
							}
						// if overall reward units + projected reward units is less than overall upper cap:
						// 1. if dailyRewardUnits already exceeds the dailyUpperCap, zero units will be rewarded
						// 2. if dailyRewardUnits + rewardUnits exceeds the dailyUpperCap, dailyUpperCap - dailyRewardUnits units will be rewarded
						// 3. if dailyRewardUnits + rewardUnits is less than dailyUpperCap, rewardUnits units will be rewarded
						case overallRewardUnits+option.GetRewardUnits() < overallUpperCap:
							switch {
							case dailyRewardUnits >= dailyUpperCap:
								projectionIdToRewardOptionMap[projection.GetId()] = &projectorPb.RewardOption{
									RewardType:                 rewardType,
									RewardUnits:                ZeroRewardUnits,
									RewardTypeSpecificMetadata: option.GetRewardTypeSpecificMetadata(),
								}
							case dailyRewardUnits+option.GetRewardUnits() >= dailyUpperCap:
								projectionIdToRewardOptionMap[projection.GetId()] = &projectorPb.RewardOption{
									RewardType:                 rewardType,
									RewardUnits:                dailyUpperCap - dailyRewardUnits,
									RewardTypeSpecificMetadata: option.GetRewardTypeSpecificMetadata(),
								}
								overallRewardUnits += dailyUpperCap - dailyRewardUnits
								dailyRewardUnits += dailyUpperCap - dailyRewardUnits
							case dailyRewardUnits+option.GetRewardUnits() < dailyUpperCap:
								projectionIdToRewardOptionMap[projection.GetId()] = option
								overallRewardUnits += option.GetRewardUnits()
								dailyRewardUnits += option.GetRewardUnits()
							}
						}
					} else {
						// actual of a negative projection will be negative only if shouldAccountNegativeProjection is true
						if shouldAccountNegativeProjection {
							projectionIdToRewardOptionMap[projection.GetId()] = option
						} else {
							projectionIdToRewardOptionMap[projection.GetId()] = &projectorPb.RewardOption{
								RewardType:                 rewardType,
								RewardUnits:                ZeroRewardUnits,
								RewardTypeSpecificMetadata: option.GetRewardTypeSpecificMetadata(),
							}
						}
					}
				}
			}
		}
	}
	return float64(overallRewardUnits), projectionIdToRewardOptionMap
}

// getTieringRewardNegativeProjectionToBeAccounted returns the negative projection iff there's a daily cap breached before
// this is done to avoid clawing back more than what we might have given because of the daily capping applied on previous days
func (re *CustomRuleEngine) getTieringRewardNegativeProjectionToBeAccounted(dayWiseProjections [][]*projectorPb.Projection, dailyUpperCap float32, rewardType rewardsPb.RewardType) (*projectorPb.Projection, int) {
	var isDailyUpperCapBreachedOnAnyDay bool
	for dayIdx, dayProjections := range dayWiseProjections {
		dailyRewardUnits := float32(0)
		for _, projection := range dayProjections {
			for _, option := range projection.GetProjectedOptions().GetRewardUnitsWithTypes() {
				if option.GetRewardType() == rewardType {
					if option.GetRewardUnits() >= 0 {
						switch {
						case dailyRewardUnits >= dailyUpperCap:
							dailyRewardUnits = dailyUpperCap
						// for transaction which exceeds the daily cap, set isDailyUpperCapBreachedOnAnyDay = true
						case dailyRewardUnits+option.GetRewardUnits() > dailyUpperCap:
							isDailyUpperCapBreachedOnAnyDay = true
							dailyRewardUnits = dailyUpperCap
						// for transactions not exceeding the daily cap, just add to daily reward units and continue
						case dailyRewardUnits+option.GetRewardUnits() <= dailyUpperCap:
							dailyRewardUnits += option.GetRewardUnits()
						}
					} else {
						if isDailyUpperCapBreachedOnAnyDay {
							return nil, 0
						}
						return projection, dayIdx
					}
				}
			}
		}
	}
	return nil, 0
}

// shouldTieringRewardDailyUpperCapBeApplied returns true only if total negative projections of the user is less than or equal to 1
func (re *CustomRuleEngine) shouldTieringRewardDailyUpperCapBeApplied(actionType rewardsPb.CollectedDataType, dailyUpperCap float32, projections []*projectorPb.Projection, rewardType rewardsPb.RewardType) bool {
	if len(projections) == 0 {
		return false
	}
	if actionType != rewardsPb.CollectedDataType_TIERING_PERIODIC_REWARD_EVENT || dailyUpperCap == 0 {
		return false
	}

	totalNegativeProjections := 0
	for _, projection := range projections {
		for _, option := range projection.GetProjectedOptions().GetRewardUnitsWithTypes() {
			if option.GetRewardType() == rewardType && option.GetRewardUnits() < 0 {
				totalNegativeProjections++
				if totalNegativeProjections > 1 {
					return false
				}
			}
		}
	}
	return true
}
