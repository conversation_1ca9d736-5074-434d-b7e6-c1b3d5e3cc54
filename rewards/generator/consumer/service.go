package consumer

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/errgroup"

	queuePb "github.com/epifi/be-common/api/queue"

	"github.com/epifi/gamma/rewards/config/genconf"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	dataCollectorPb "github.com/epifi/gamma/api/rewards/datacollector"
	rewardsEventsPb "github.com/epifi/gamma/api/rewards/events"
	rewardsExpiryPb "github.com/epifi/gamma/api/rewards/expiry"
	generatorPb "github.com/epifi/gamma/api/rewards/generator"
	rewardsGeneratorPb "github.com/epifi/gamma/api/rewards/generator"
	projectorPb "github.com/epifi/gamma/api/rewards/projector"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/rewards/config"
	rewardsEvents "github.com/epifi/gamma/rewards/events"
	"github.com/epifi/gamma/rewards/generator/dao"
	"github.com/epifi/gamma/rewards/generator/internalerrors"
	"github.com/epifi/gamma/rewards/generator/model"
	re "github.com/epifi/gamma/rewards/generator/ruleengine"
	errors2 "github.com/epifi/gamma/rewards/generator/ruleengine/errors"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact"
	"github.com/epifi/gamma/rewards/helper"
	"github.com/epifi/gamma/rewards/metrics"
	"github.com/epifi/gamma/rewards/notification"
	"github.com/epifi/gamma/rewards/wire/types"
)

var (
	// we return this when the event is consumed successfully
	getSuccessRes = func() *rewardsGeneratorPb.ConsumerResponse {
		return &rewardsGeneratorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}
	}
	// we return this when the event cannot be consumed even after retries,
	// for e.g. missing data, invalid input format for any field etc.
	getPermanentFailureRes = func() *rewardsGeneratorPb.ConsumerResponse {
		return &rewardsGeneratorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE}}
	}
	// we return this when the event could not be consumed due to any system failure and if we want to retry the particular event
	// for e.g. internal error from any rpc called inside the consumer etc.
	getTransientFailureRes = func() *rewardsGeneratorPb.ConsumerResponse {
		return &rewardsGeneratorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}
	}
)

const (
	maxTimeDeltaBetweenUnlockAndAutoClaimTimeForArrivingState = time.Minute * 2
)

type GeneratorConsumerService struct {
	ruleEngine                                 re.RuleEngine
	factEngine                                 *fact.FactEngine
	rewardDao                                  dao.RewardsDao
	rewardOfferDao                             dao.RewardOfferDao
	rewardPublisher                            types.RewardsSqsPublisher
	eventsBroker                               events.Broker
	notificationService                        notification.INotificationService
	rewardsGenerationEventSnsPublisher         types.RewardGenerationEventSnsPublisher
	claimRewardEventDelayPublisher             types.RewardsClaimRewardEventSqsCustomDelayPublisher
	rewardsUnlockerCustomDelayPublisher        types.RewardsRewardUnlockerSqsCustomDelayPublisher
	rewardsRewardExpirySqsCustomDelayPublisher types.RewardsRewardExpirySqsCustomDelayPublisher
	rewardProjectionsUpdateSqsPublisher        types.RewardsProjectionUpdateEventSqsPublisher
	rewardsClient                              rewardsPb.RewardsGeneratorClient
	conf                                       *config.Config
	dynConf                                    *genconf.Config
	rewardsCxActivityHelperService             helper.IRewardsCxActivityHelperService
	userHelper                                 helper.IUserHelperService
}

func NewGeneratorConsumerService(
	ruleEngine re.RuleEngine,
	factFactory *fact.FactEngine,
	rewardDao dao.RewardsDao,
	rewardOfferDao dao.RewardOfferDao,
	rewardPublisher types.RewardsSqsPublisher,
	eventsBroker events.Broker,
	notificationService notification.INotificationService,
	rewardsGenerationEventSnsPublisher types.RewardGenerationEventSnsPublisher,
	claimRewardEventDelayPublisher types.RewardsClaimRewardEventSqsCustomDelayPublisher,
	rewardsUnlockerCustomDelayPublisher types.RewardsRewardUnlockerSqsCustomDelayPublisher,
	rewardsRewardExpirySqsCustomDelayPublisher types.RewardsRewardExpirySqsCustomDelayPublisher,
	rewardProjectionsUpdateSqsPublisher types.RewardsProjectionUpdateEventSqsPublisher,
	rewardsClient rewardsPb.RewardsGeneratorClient,
	conf *config.Config,
	dynConf *genconf.Config,
	rewardsCxActivityHelperService helper.IRewardsCxActivityHelperService,
	userHelper helper.IUserHelperService,
) *GeneratorConsumerService {
	return &GeneratorConsumerService{
		ruleEngine:                          ruleEngine,
		factEngine:                          factFactory,
		rewardDao:                           rewardDao,
		rewardOfferDao:                      rewardOfferDao,
		rewardPublisher:                     rewardPublisher,
		eventsBroker:                        eventsBroker,
		notificationService:                 notificationService,
		rewardsGenerationEventSnsPublisher:  rewardsGenerationEventSnsPublisher,
		claimRewardEventDelayPublisher:      claimRewardEventDelayPublisher,
		rewardsUnlockerCustomDelayPublisher: rewardsUnlockerCustomDelayPublisher,
		rewardsRewardExpirySqsCustomDelayPublisher: rewardsRewardExpirySqsCustomDelayPublisher,
		rewardProjectionsUpdateSqsPublisher:        rewardProjectionsUpdateSqsPublisher,
		rewardsClient:                              rewardsClient,
		conf:                                       conf,
		dynConf:                                    dynConf,
		rewardsCxActivityHelperService:             rewardsCxActivityHelperService,
		userHelper:                                 userHelper,
	}
}

// compile time check to make sure GeneratorConsumerService implements ConsumerServer
var _ rewardsGeneratorPb.ConsumerServer = &GeneratorConsumerService{}

// rewardFactInfoForLogging type for having minimum/stripped data which can be used for logging purposes
type rewardFactInfoForLogging struct {
	// ReferenceId
	RefId string
	// Reward Offer Id
	ROId string
	// Actor Id
	AId string
}

// nolint: funlen,govet
func (gcs *GeneratorConsumerService) ProcessDataCollectorEvent(ctx context.Context, req *dataCollectorPb.CollectedData) (*generatorPb.ConsumerResponse, error) {
	// setting a lesser ctx timeout from what is passed by the parent (5 mins) to prevent events from getting starved due to some poison-pill events.
	// todo (utkarsh) : re-evaluate this timeout value
	ctx, cancelFunc := context.WithTimeout(ctx, 15*time.Second)
	defer cancelFunc()

	isEventEligible, err := gcs.isDataCollectorEventEligibleForRewardGeneration(ctx, req)
	if err != nil {
		logger.Error(ctx, "error checking if event is eligible for reward generation", zap.String(logger.EVENT_TYPE, req.GetDataType().String()), zap.String(logger.EVENT_ID, req.GetId()), zap.Error(err))
		return getTransientFailureRes(), nil
	}
	if !isEventEligible {
		logger.Debug(ctx, "event is not eligible for reward generation", zap.String(logger.EVENT_TYPE, req.GetDataType().String()), zap.String(logger.EVENT_ID, req.GetId()))
		return getSuccessRes(), nil
	}

	// get reward offers for action
	logger.Debug(ctx, "fetching active reward offers for collected data", zap.String(logger.EVENT_TYPE, req.GetDataType().String()), zap.String(logger.EVENT_ID, req.GetId()),
		zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Time("action_time", req.GetActionTime().AsTime()))
	rewardOffers, err := gcs.rewardOfferDao.FetchRewardOffersActiveAtTime(ctx, req.DataType.String(), req.GetActionTime().AsTime(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD)
	if err != nil {
		logger.Error(ctx, "error fetching reward offers for collected data", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.String(logger.EVENT_TYPE, req.GetDataType().String()), zap.String(logger.EVENT_ID, req.GetId()), zap.Error(err))
		metrics.MetricsRecorder.RecordRewardFactGenerationErr(nil)
		return getTransientFailureRes(), nil
	}

	// get facts for action
	myFacts, factGeneratorErrors := gcs.factEngine.GetFact(ctx, req, rewardOffers)
	if len(factGeneratorErrors) > 0 {
		logger.Error(ctx, "error generating some facts for collected data", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.String("collected_data_type", req.GetDataType().String()), zap.String("collected_data_id", req.GetId()), zap.Int("error_count", len(factGeneratorErrors)))
		metrics.MetricsRecorder.RecordRewardFactGenerationErr(factGeneratorErrors)
	}

	var (
		rewardCalculationFromFactWg        sync.WaitGroup
		rewardOfferIdToRewardOfferMapMutex sync.Mutex
	)
	rewardOfferIdToRewardOfferMap := make(map[string]*rewardOffersPb.RewardOffer)
	calculatedRewardsChannel := make(chan *model.Reward, len(myFacts))
	noRewardForFactsChannel := make(chan *rewardFactInfoForLogging, len(myFacts))
	factEvalPreConditionFailedErrorsChannel := make(chan *rewardFactInfoForLogging, len(myFacts))
	generationErrorsChannel := make(chan error, len(myFacts))

	// parallelly calculate reward for each fact
	// todo (utkarsh) : limit the number of go routines by doing processing in chunks
	for _, myFact := range myFacts {
		rewardCalculationFromFactWg.Add(1)
		fct := myFact
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			defer func() {
				if r := recover(); r != nil {
					logger.ErrorWithStackTrace(ctx, "encountered panic during reward calculation from fact", zap.String(logger.REFERENCE_ID, fct.GetRefId()), zap.String(logger.REWARD_OFFER_ID, fct.GetRewardOffer().GetId()), zap.String(logger.ACTOR_ID, fct.GetActorId()), zap.Any("recoverMsg", r))
					metrics.MetricsRecorder.PushRewardCalculationFromFactErrMetric(nil)
					generationErrorsChannel <- errors.New("encountered panic during reward calculation from fact")
				}
				rewardCalculationFromFactWg.Done()
			}()

			// calculate reward from fact
			reward, err := gcs.ruleEngine.CalculateRewards(ctx, fct)
			if err != nil {
				if errors.Is(err, internalerrors.FactEvaluationPreConditionFailed) {
					// ignoring the error because of some pre-condition failed for fact evaluation.
					logger.Debug(ctx, "ignoring generated fact because of failed pre-condition", zap.String(logger.REFERENCE_ID, fct.GetRefId()), zap.String(logger.REWARD_OFFER_ID, fct.GetRewardOffer().GetId()), zap.String(logger.ACTOR_ID, fct.GetActorId()))
					factEvalPreConditionFailedErrorsChannel <- &rewardFactInfoForLogging{
						RefId: fct.GetRefId(), ROId: fct.GetRewardOffer().GetId(), AId: fct.GetActorId(),
					}
					return
				}
				generationErrorsChannel <- err
				metrics.MetricsRecorder.PushRewardCalculationFromFactErrMetric(err)
				// only log error if it's last attempt for P2 errors, log all other priority errors
				if (errors.Is(err, errors2.P2RewardCalculationFromFactError) && req.GetRequestHeader().GetIsLastAttempt()) || !errors.Is(err, errors2.P2RewardCalculationFromFactError) {
					logger.Error(ctx, "reward generation error", zap.String(logger.REFERENCE_ID, fct.GetRefId()), zap.String(logger.REWARD_OFFER_ID, fct.GetRewardOffer().GetId()), zap.String(logger.ACTOR_ID, fct.GetActorId()), zap.Error(err))
				}
				return
			}
			if reward == nil {
				logger.Debug(ctx, "no reward for fact", zap.String(logger.REFERENCE_ID, fct.GetRefId()), zap.String(logger.REWARD_OFFER_ID, fct.GetRewardOffer().GetId()), zap.String(logger.ACTOR_ID, fct.GetActorId()))
				noRewardForFactsChannel <- &rewardFactInfoForLogging{
					RefId: fct.GetRefId(), ROId: fct.GetRewardOffer().GetId(), AId: fct.GetActorId(),
				}
			} else {
				calculatedRewardsChannel <- reward
				rewardOfferIdToRewardOfferMapMutex.Lock()
				rewardOfferIdToRewardOfferMap[reward.OfferId] = fct.GetRewardOffer()
				rewardOfferIdToRewardOfferMapMutex.Unlock()
			}
		})
	}
	// wait for reward calculation to complete
	waitgroup.SafeWaitCtx(ctx, &rewardCalculationFromFactWg)

	var noRewardForFactsInfo []*rewardFactInfoForLogging
	noRewardsForFactsCount := len(noRewardForFactsChannel)
	for i := 0; i < noRewardsForFactsCount; i++ {
		noRewardForFactsInfo = append(noRewardForFactsInfo, <-noRewardForFactsChannel)
	}

	marshalledNoRewardForFactsInfo, err := json.Marshal(noRewardForFactsInfo)
	if err != nil {
		logger.WarnWithCtx(ctx, "unable to json marshall no-reward-for-facts info", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.String(logger.EVENT_TYPE, req.GetDataType().String()), zap.String(logger.EVENT_ID, req.GetId()))
	} else if len(noRewardForFactsInfo) > 0 {
		logger.Info(ctx, "no reward for facts", zap.String("info", string(marshalledNoRewardForFactsInfo)), zap.String(logger.ACTOR_ID, req.GetActorId()))
	}

	// log info of facts for which fact eval pre-condition is failed
	var factEvalPreConditionFailedFactsInfo []*rewardFactInfoForLogging
	factEvalPreConditionFailedErrFactsCount := len(factEvalPreConditionFailedErrorsChannel)
	for i := 0; i < factEvalPreConditionFailedErrFactsCount; i++ {
		factEvalPreConditionFailedFactsInfo = append(factEvalPreConditionFailedFactsInfo, <-factEvalPreConditionFailedErrorsChannel)
	}

	marshalledFactEvalPreConditionFailedFactsInfo, err := json.Marshal(factEvalPreConditionFailedFactsInfo)
	if err != nil {
		logger.WarnWithCtx(ctx, "unable to json marshall fact eval pre-condition failed info", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.String(logger.EVENT_TYPE, req.GetDataType().String()), zap.String(logger.EVENT_ID, req.GetId()))
	} else if len(factEvalPreConditionFailedFactsInfo) > 0 {
		logger.Info(ctx, "ignoring generated facts due to failed pre-condition", zap.String("info", string(marshalledFactEvalPreConditionFailedFactsInfo)), zap.String(logger.ACTOR_ID, req.GetActorId()))
	}

	// convert reward generation error channel to a list
	var generationErrors []error
	generationErrorsCount := len(generationErrorsChannel)
	for i := 0; i < generationErrorsCount; i++ {
		generationErrors = append(generationErrors, <-generationErrorsChannel)
	}

	// convert calculated rewards channel to a list
	var calculatedRewards []*model.Reward
	calculatedRewardsCount := len(calculatedRewardsChannel)
	for i := 0; i < calculatedRewardsCount; i++ {
		calculatedRewards = append(calculatedRewards, <-calculatedRewardsChannel)
	}

	// fail the event if it's not eligible for reward generation according to shouldFailDataCollectorEvent - check comments for more details
	if shouldFailDataCollectorEvent(ctx, calculatedRewards) {
		return getTransientFailureRes(), nil
	}

	// persist rewards
	var persistedRewards []*model.Reward
	var persistenceError []error
	for _, reward := range calculatedRewards {
		err := gcs.rewardDao.PersistReward(ctx, reward)
		if err != nil {
			switch {
			// duplicate entry error implies reward was already persisted before.
			case errors.Is(err, epifierrors.ErrDuplicateEntry):
				logger.Info(ctx, "duplicate reward received", zap.String(logger.REWARD_ID, reward.Id), zap.String(logger.REWARD_OFFER_ID, reward.OfferId), zap.String(logger.ACTOR_ID, reward.ActorId), zap.Error(err))
				// fetch the reward if required and add it to persisted rewards in order to
				// repeat steps that are required to be executed for a persisted reward (to
				// handle cases in which something failed after a reward was persisted, for
				// example, publishing of message to a queue.)
				if shouldPersistedRewardBeFetched(reward) {
					fetchedRewards, _, rewardFetchErr := gcs.rewardDao.FetchPaginatedRewardsByFiltersV2(ctx, reward.ActorId, &model.QueryRewardsFilterV2{
						AndFilter: &model.AndRewardsFilter{
							RefIds:        []string{reward.RefId},
							RewardOfferId: reward.OfferId,
						},
					}, nil, 2)
					if rewardFetchErr != nil {
						logger.Error(ctx, "failed to fetch previously persisted reward", zap.String(logger.ACTOR_ID, reward.ActorId), zap.String(logger.REWARD_OFFER_ID, reward.OfferId), zap.String(logger.REFERENCE_ID, reward.RefId))
						persistenceError = append(persistenceError, rewardFetchErr)
						continue
					}
					// reprocess only original reward (not adjustment reward)
					// todo (team): add handling for reprocessing previously persisted adjustment rewards
					if len(fetchedRewards) != 1 {
						continue
					}

					err = gcs.reprocessPreviouslyPersistedReward(ctx, reward, fetchedRewards[0])
					if err != nil {
						logger.Error(ctx, "error while reprocessing a previously persisted reward", zap.String(logger.REWARD_ID, fetchedRewards[0].Id))
						persistenceError = append(persistenceError, rewardFetchErr)
					}
				}

			// maxAggregateReached error implies aggregate was breached and hence reward wasn't persisted which is expected.
			case errors.Is(err, internalerrors.MaxRewardAggregateReached):
				// this is not an error case so not appending it to persistence errors.
				logger.Info(ctx, "reward cannot be generated, aggregate reached", zap.String(logger.REWARD_ID, reward.Id), zap.String(logger.REWARD_OFFER_ID, reward.OfferId), zap.String(logger.ACTOR_ID, reward.ActorId), zap.Error(err))
			case errors.Is(err, internalerrors.RewardUnitsMaxCapReached):
				// this is not an error case so not appending it to persistence errors.
				logger.Info(ctx, "reward cannot be generated, max units cap reached", zap.String(logger.REWARD_ID, reward.Id), zap.String(logger.REWARD_OFFER_ID, reward.OfferId), zap.String(logger.ACTOR_ID, reward.ActorId), zap.Error(err))
			// in default case treat it as a persistence error
			default:
				persistenceError = append(persistenceError, err)
				logger.Error(ctx, "reward persistence error", zap.String(logger.REWARD_ID, reward.Id), zap.String(logger.REWARD_OFFER_ID, reward.OfferId), zap.String(logger.ACTOR_ID, reward.ActorId), zap.Error(err))
			}
		} else {
			logger.Info(ctx, "reward created successfully", zap.String(logger.REWARD_ID, reward.Id), zap.String(logger.REWARD_OFFER_ID, reward.OfferId), zap.String(logger.ACTOR_ID, reward.ActorId))
			gcs.instrumentRewardGenerationMetrics(ctx, reward, rewardOfferIdToRewardOfferMap)
			persistedRewards = append(persistedRewards, reward)
		}
	}
	// push persisted rewards events to further in pipeline for:
	// * fulfillment (if they are to be auto-processed)
	// * unlocking (if they are time based locked)
	var communicationError []error
	for _, reward := range persistedRewards {
		rewardProto, err := reward.GetProtoReward()
		if err != nil {
			logger.Error(ctx, "unable to convert reward to proto reward", zap.String(logger.REWARD_ID, reward.Id), zap.Error(err))
			communicationError = append(communicationError, err)
			continue
		}

		// note: in case reward generation event fails, it won't be retried as since reward has been persisted previously, these steps won't execute
		_, err = gcs.rewardsGenerationEventSnsPublisher.Publish(ctx, &rewardsEventsPb.RewardGenerationEvent{
			Reward:    rewardProto,
			RewardId:  rewardProto.GetId(),
			OfferType: rewardProto.GetOfferType(),
			ActorId:   rewardProto.GetActorId(),
			CreatedAt: rewardProto.GetCreatedAt(),
		})
		if err != nil {
			logger.Error(ctx, "failed to publish reward generation event to SNS topic", zap.Error(err))
			return &generatorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
		}

		// if reward was generated using projections, publish projections for being updated with reward details
		if reward.RewardAdditionalInfo != nil && reward.RewardAdditionalInfo.ProjectionIdToContributionsMap != nil {
			// publish to event queue for updating
			publishErr := gcs.updateProjectionsWithActualRewardContributions(ctx, reward.RewardAdditionalInfo.ProjectionIdToContributionsMap, reward.Id)
			if publishErr != nil {
				logger.Error(ctx, "error while publishing updates to projections", zap.String(logger.REWARD_ID, reward.Id), zap.Error(publishErr))
				return getTransientFailureRes(), nil
			}
		}

		// push reward for unlocking if it's time based locked
		if rewardProto.GetStatus() == rewardsPb.RewardStatus_LOCKED && rewardProto.GetSubStatusV2() == rewardsPb.SubStatus_SUB_STATUS_EXPLICITLY_LOCKED && rewardProto.GetRewardOptions().GetUnlockDate() != nil {
			rewardUnlockTime := rewardProto.GetRewardOptions().GetUnlockDate().AsTime()
			if rewardUnlockTime.After(time.Now().Add(gcs.conf.MinTimeDelayBetweenNowAndUnlockDateForLockingReward)) {
				_, publishForUnlockErr := gcs.rewardsUnlockerCustomDelayPublisher.PublishWithDelay(ctx, &dataCollectorPb.CollectedData{
					Id:       fmt.Sprintf("RewardUnlockEvent::RewardId-%s", rewardProto.GetId()),
					DataType: rewardsPb.CollectedDataType_UNLOCK_REWARD_EVENT,
					ActorId:  rewardProto.GetActorId(),
					Data: &dataCollectorPb.CollectedData_UnlockRewardEvent{
						UnlockRewardEvent: &dataCollectorPb.UnlockRewardEvent{RewardId: rewardProto.GetId()},
					},
					ActionTime:   rewardProto.GetRewardOptions().GetUnlockDate(),
					CreationTime: timestampPb.Now(),
				}, rewardUnlockTime.Sub(time.Now()))
				if publishForUnlockErr != nil {
					logger.Error(ctx, "error while publishing reward for unlocking", zap.String(logger.REWARD_ID, rewardProto.GetId()), zap.Error(publishForUnlockErr))
					communicationError = append(communicationError, publishForUnlockErr)
				} else {
					logger.Info(ctx, "successfully published unlock reward event", zap.String(logger.REWARD_ID, rewardProto.GetId()), zap.Time("unlockTime", rewardUnlockTime))
				}
			}
		}

		if !reward.ExpiresAt.IsZero() {
			delayDuration := reward.ExpiresAt.Sub(time.Now())
			if delayDuration <= time.Duration(0) {
				communicationError = append(communicationError, fmt.Errorf("delayDuration is negetive for expiry queue"))
			} else {
				if delayDuration < time.Second {
					delayDuration = time.Second
				}
				if rewardProto.GetStatus() == rewardsPb.RewardStatus_CREATED || rewardProto.GetStatus() == rewardsPb.RewardStatus_LOCKED {
					_, rewardExpirySqsCustomDelayPublisherErr := gcs.rewardsRewardExpirySqsCustomDelayPublisher.PublishWithDelay(ctx, &rewardsExpiryPb.ExpireRewardRequest{
						RewardId: rewardProto.GetId(),
					}, delayDuration)
					if rewardExpirySqsCustomDelayPublisherErr != nil {
						logger.Error(ctx, "error in publishing expiry event", zap.String(logger.REWARD_ID, rewardProto.GetId()), zap.Error(rewardExpirySqsCustomDelayPublisherErr))
						communicationError = append(communicationError, rewardExpirySqsCustomDelayPublisherErr)
					}
				}
			}
		}

		if rewardProto.GetRewardOptions().GetAutoClaimTime() != nil {
			// we'll only honor AutoClaimTime if reward is:
			// 1. in CREATED state OR
			// 2. will unlock around same time as the reward auto-claim time
			rewardUnlockTime := rewardProto.GetRewardOptions().GetUnlockDate()
			autoClaimTime := rewardProto.GetRewardOptions().GetAutoClaimTime()
			if rewardProto.GetStatus() == rewardsPb.RewardStatus_CREATED ||
				(rewardUnlockTime != nil && (datetime.GetAbsoluteTimeDifference(rewardUnlockTime.AsTime(), autoClaimTime.AsTime()) < maxTimeDeltaBetweenUnlockAndAutoClaimTimeForArrivingState)) {
				autoClaimDelay := time.Until(autoClaimTime.AsTime())
				// delayed publisher needs to be at-least 1 second of delay
				if autoClaimDelay < 1*time.Second {
					autoClaimDelay = 1 * time.Second
				}
				_, err = gcs.claimRewardEventDelayPublisher.PublishWithDelay(ctx, &rewardsGeneratorPb.ClaimRewardRequest{RewardId: rewardProto.GetId()}, autoClaimDelay)
				if err != nil {
					communicationError = append(communicationError, err)
					logger.Error(ctx, "error publishing auto-claim reward event", zap.String(logger.REWARD_ID, rewardProto.GetId()), zap.Error(err))
				}
			}
		}

		// send earned reward notification
		// nolint: errcheck
		goroutine.RunWithCtx(epificontext.CloneCtx(ctx), func(gctx context.Context) {
			_ = gcs.notificationService.SendEarnedRewardNotification(gctx, rewardProto)
		})

		if reward.Status != rewardsPb.RewardStatus_PROCESSING_PENDING {
			continue
		}
		rewardEvent := &rewardsPb.RewardEvent{
			Reward: rewardProto,
		}
		_, err = gcs.rewardPublisher.Publish(ctx, rewardEvent)
		if err != nil {
			communicationError = append(communicationError, err)
			logger.Error(ctx, "reward communication error", zap.Any(logger.REWARD_ID, rewardEvent.GetReward().GetId()), zap.Error(err))
		}
	}

	if len(factGeneratorErrors) > 0 || len(generationErrors) > 0 || len(persistenceError) > 0 || len(communicationError) > 0 {
		return getTransientFailureRes(), nil
	}
	return getSuccessRes(), nil
}

// shouldFailDataCollectorEvent fails the event if:
//  1. Any reward has fi coins as one of the types and one of the following conditions is true:
//     a. Fi coins to fi points migration is in progress
//     b. Current time is after fi coins to fi points migration and action time is before fi coins to fi points migration
//
// NOTE: This is a temporary fix for migration to avoid generating rewards with fi coins reward option after migration
// to avoid generating money rewards with fi coins reward option or auto processing rewards with fi coins reward option which will get credited as fi points
func shouldFailDataCollectorEvent(ctx context.Context, rewards []*model.Reward) bool {
	fiCoinsToFiPointsMigrationTime := accrualPkg.GetFiCoinsToFiPointsMigrationTime()
	isCurrentTimeAfterFiCoinsToFiPointsMigration := time.Now().After(fiCoinsToFiPointsMigrationTime)
	isFiCoinsToFiPointsMigrationInProgress := accrualPkg.IsFiCoinsToFiPointsMigrationInProgress()

	for _, reward := range rewards {
		doesRewardHaveFiCoinsRewardOption := helper.CheckIfAnyOptionOfRewardType(reward.RewardOptions.GetOptions(), rewardsPb.RewardType_FI_COINS)
		isActionTimeBeforeFiCoinsToFiPointsMigration := reward.ActionTimestamp.Before(fiCoinsToFiPointsMigrationTime)

		// if reward doesn't have fi coins reward option, skip it
		if !doesRewardHaveFiCoinsRewardOption {
			continue
		}

		// if fi coins to fi points migration is in progress, fail the event
		if isFiCoinsToFiPointsMigrationInProgress {
			logger.WarnWithCtx(ctx, "fi coins to fi points migration is in progress, failing the event as it is generating fi coin reward", zap.String(logger.ACTOR_ID_V2, reward.ActorId), zap.String(logger.REWARD_OFFER_ID, reward.OfferId), zap.String(logger.REFERENCE_ID, reward.RefId), zap.Time(logger.REQUEST_TIME, reward.ActionTimestamp))
			return true
		}

		// if current time is after fi coins to fi points migration and action time is before fi coins to fi points migration, fail the event
		if isCurrentTimeAfterFiCoinsToFiPointsMigration && isActionTimeBeforeFiCoinsToFiPointsMigration {
			logger.WarnWithCtx(ctx, "current time is after fi coins to fi points migration and action time is before fi coins to fi points migration, failing the event as it is generating fi coin reward", zap.String(logger.ACTOR_ID_V2, reward.ActorId), zap.String(logger.REWARD_OFFER_ID, reward.OfferId), zap.String(logger.REFERENCE_ID, reward.RefId), zap.Time(logger.REQUEST_TIME, reward.ActionTimestamp))
			return true
		}
	}

	return false
}

// ProcessClaimRewardEvent method to process claim reward events to claim a reward with the default option (if it was not already claimed),
// majorly useful for auto-claim use-case where a reward needs to auto-claimed for a user if haven't claimed the reward yet.
func (gcs *GeneratorConsumerService) ProcessClaimRewardEvent(ctx context.Context, req *generatorPb.ClaimRewardRequest) (*generatorPb.ConsumerResponse, error) {
	reward, err := gcs.rewardDao.FetchRewardById(ctx, req.GetRewardId())
	if err != nil {
		logger.Error(ctx, "error fetching reward by id", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.Error(err))
		return getTransientFailureRes(), nil
	}
	rewardProto, err := reward.GetProtoReward()
	if err != nil {
		logger.Error(ctx, "error convert reward db model to proto", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.Error(err))
		return getTransientFailureRes(), nil
	}

	// todo (utkarsh) : should we validate reward unlock-time here ?
	// check if reward was not already claimed yet.
	if rewardProto.GetStatus() != rewardsPb.RewardStatus_CREATED {
		logger.WarnWithCtx(ctx, "claim not allowed, reward should be in CREATED state", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String(logger.REWARD_STATUS, rewardProto.GetStatus().String()))
		return getPermanentFailureRes(), nil
	}
	// first reward option can be treated at the default option.
	defaultRewardOption := rewardProto.GetRewardOptions().GetOptions()[0]

	allowedRewardTypesForAutoChoose := []rewardsPb.RewardType{rewardsPb.RewardType_FI_COINS, rewardsPb.RewardType_CASH}
	if !lo.Contains(allowedRewardTypesForAutoChoose, defaultRewardOption.GetRewardType()) {
		logger.WarnWithCtx(ctx, "claim not allowed, auto-choose is not allowed for reward type present in default reward option", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String(logger.REWARD_TYPE, defaultRewardOption.GetRewardType().String()))
		return getPermanentFailureRes(), nil
	}

	// choose the default reward option.
	chooseRewardRes, err := gcs.rewardsClient.ChooseReward(ctx, &rewardsPb.ChooseRewardRequest{
		RewardId:       rewardProto.GetId(),
		RewardOptionId: defaultRewardOption.GetId(),
	})
	if rpcErr := epifigrpc.RPCError(chooseRewardRes, err); rpcErr != nil {
		logger.Error(ctx, "rewardsClient.ChooseReward rpc call failed", zap.String(logger.REWARD_ID, rewardProto.GetId()), zap.String(logger.CHOSEN_OPTION_ID, defaultRewardOption.GetId()), zap.Error(rpcErr))
		return getTransientFailureRes(), nil
	}
	return getSuccessRes(), nil
}

func (gcs *GeneratorConsumerService) instrumentRewardGenerationMetrics(ctx context.Context, reward *model.Reward, rewardOfferIdToRewardOfferMap map[string]*rewardOffersPb.RewardOffer) {
	// publish rudder event (with reward unit details)
	goroutine.RunWithCtx(epificontext.WithEventAttributesV2(ctx), func(gctx context.Context) {
		gcs.eventsBroker.AddToBatch(gctx, rewardsEvents.NewGivenRewardEvent(reward))
	})

	// publish rudder event (without reward unit details for being piped to moengage)
	goroutine.RunWithCtx(epificontext.WithEventAttributesV2(ctx), func(gctx context.Context) {
		gcs.eventsBroker.AddToBatch(gctx, rewardsEvents.NewRewardsRewardGenerationEvent(reward))
	})

	err := gcs.publishUserActivityEvent(ctx, reward, rewardOfferIdToRewardOfferMap)
	if err != nil {
		logger.Error(ctx, "error publishing user activity event for generated reward", zap.Error(err))
	}

	// instrument generated reward units
	for _, rewardOption := range reward.RewardOptions.Options {
		rewardUnits := float64(0)
		switch rewardOption.RewardType {
		case rewardsPb.RewardType_CASH:
			rewardUnits = helper.ConvertMoneyToValue(rewardOption.GetCash().GetAmount())
		case rewardsPb.RewardType_SMART_DEPOSIT:
			rewardUnits = helper.ConvertMoneyToValue(rewardOption.GetSmartDeposit().GetAmount())
		case rewardsPb.RewardType_FI_COINS:
			rewardUnits = float64(rewardOption.GetFiCoins().GetUnits())
		case rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER:
			rewardUnits = helper.ConvertMoneyToValue(rewardOption.GetCreditCardBillEraser().GetAmount())
		default:
			// added default case to avoid lint errors
		}
		if rewardUnits > 0 {
			metrics.MetricsRecorder.RecordRewardedRewardUnits(rewardOption.RewardType.String(), rewardUnits)
		}
	}
}

func (gcs *GeneratorConsumerService) publishUserActivityEvent(ctx context.Context, reward *model.Reward, rewardOfferIdToRewardOfferMap map[string]*rewardOffersPb.RewardOffer) error {
	protoReward, err := reward.GetProtoReward()
	if err != nil {
		logger.Error(ctx, "unable to convert reward to proto reward", zap.String(logger.REWARD_ID, reward.Id), zap.Error(err))
		return err
	}

	rewardOffer, check := rewardOfferIdToRewardOfferMap[protoReward.GetOfferId()]
	if !check {
		logger.Error(ctx, "unable to find reward offer corresponding to reward id", zap.String(logger.REWARD_ID, protoReward.GetId()))
		return fmt.Errorf("unable to find reward offer corresponding to reward id")
	}

	// publish rudder event for user activity stream
	goroutine.RunWithCtx(epificontext.WithEventAttributesV2(ctx), func(gctx context.Context) {
		gcs.rewardsCxActivityHelperService.PublishAsRewardUserActivityEvent(gctx, protoReward, rewardOffer)
	})
	return nil
}

func (gcs *GeneratorConsumerService) updateProjectionsWithActualRewardContributions(ctx context.Context, projectionIdToOptionsInfoMap map[string]*projectorPb.OptionsInfo, rewardId string) error {
	// convert projections map to a list of updates
	var projectionUpdates []*projectorPb.ProjectionUpdateRequest
	for projectionId, rewardOptionsInfo := range projectionIdToOptionsInfoMap {
		projectionUpdates = append(projectionUpdates, &projectorPb.ProjectionUpdateRequest{
			ProjectionId: projectionId,
			RewardId:     rewardId,
			OptionsInfo:  rewardOptionsInfo,
		})
	}

	messageId, err := gcs.rewardProjectionsUpdateSqsPublisher.Publish(ctx, &projectorPb.ProjectionUpdateEvent{ProjectionUpdateRequests: projectionUpdates})
	if err != nil {
		return fmt.Errorf("error while publishing projection updates, err: %w", err)
	}

	logger.Debug(ctx, "successfully published updates to projections", zap.String(logger.QUEUE_MESSAGE_ID, messageId))
	return nil
}

// shouldPersistedRewardBeFetched decides whether we want to fetch a previously
// persisted reward for reprocessing, in case a duplicate reward was generated
func shouldPersistedRewardBeFetched(reward *model.Reward) bool {
	// check if reward was generated using projections.
	// in this case we would want to send a projection update event, hence, we would
	if reward != nil && reward.RewardAdditionalInfo != nil && len(reward.RewardAdditionalInfo.ProjectionIdToContributionsMap) > 0 {
		return true
	}

	return false
}

// reprocessPreviouslyPersistedReward performs any actions required to be
// performed if a reward was previously generated but something went wrong in the
// subsequent flow and the event was retried. for example, if a reward was
// generated using projections but an error occurred during publishing of
// projection updates, we would retry the event. The projections will be
// recalculated when the event is received but since a reward was already
// persisted for this event, we'll need to update the projections with the
// previously generated reward's details.
func (gcs *GeneratorConsumerService) reprocessPreviouslyPersistedReward(ctx context.Context, generatedReward *model.Reward, persistedReward *model.Reward) error {
	// publish to projections queue for updating
	publishErr := gcs.updateProjectionsWithActualRewardContributions(ctx, generatedReward.RewardAdditionalInfo.ProjectionIdToContributionsMap, persistedReward.Id)
	if publishErr != nil {
		logger.Error(ctx, "error while publishing updates to projections", zap.String(logger.REWARD_ID, persistedReward.Id), zap.Error(publishErr))
		return fmt.Errorf("error while publishing updates to projections, err: %w", publishErr)
	}

	return nil
}

func (gcs *GeneratorConsumerService) isDataCollectorEventEligibleForRewardGeneration(ctx context.Context, req *dataCollectorPb.CollectedData) (bool, error) {
	actorIds := helper.GetActorIdsFromDataCollectorEvent(req)

	// if either of the actor is a non-resident user then the event is not eligible for reward generation.
	// this implies that referrer of these actor will also not be eligible for referral rewards.
	isNrUser, nrActorId, err := gcs.isEitherOfActorNonResidentUser(ctx, actorIds)
	if err != nil {
		return false, err
	}
	if isNrUser {
		logger.Info(ctx, "event is not eligible for reward generation, actor is non-resident",
			zap.String(logger.EVENT_TYPE, req.GetDataType().String()), zap.String(logger.EVENT_ID, req.GetId()), zap.String(logger.ACTOR_ID, nrActorId))
		return false, nil
	}

	if !lo.Contains(gcs.getCollectedDataTypesEligibleForFiLite(), req.GetDataType()) {
		isFiLiteUser, fiLiteActorId, isFiLiteErr := gcs.isEitherOfActorFiLiteUser(ctx, actorIds)
		if isFiLiteErr != nil {
			logger.Error(ctx, "error checking if actor is FiLite", zap.String(logger.ACTOR_ID, fiLiteActorId), zap.Error(isFiLiteErr))
			// if error is record not found then event is not eligible for reward generation
			if errors.Is(isFiLiteErr, epifierrors.ErrRecordNotFound) {
				return false, nil
			}
			return false, isFiLiteErr
		}
		if isFiLiteUser {
			logger.Info(ctx, "event is not eligible for reward generation, actor is FiLite",
				zap.String(logger.EVENT_TYPE, req.GetDataType().String()), zap.String(logger.EVENT_ID, req.GetId()), zap.String(logger.ACTOR_ID, fiLiteActorId))
			return false, nil
		}
	}

	return true, nil
}

func (gcs *GeneratorConsumerService) isEitherOfActorFiLiteUser(ctx context.Context, actorIds []string) (bool, string, error) {
	if len(actorIds) == 0 {
		return false, "", nil
	}

	type result struct {
		actorId      string
		isFiLiteUser bool
		err          error
	}

	resultsChan := make(chan result, len(actorIds))
	g, gctx := errgroup.WithContext(ctx)

	// Check all actors concurrently
	for _, actorId := range actorIds {
		actorId := actorId // capture loop variable
		g.Go(func() error {
			isFiLiteUser, err := gcs.userHelper.IsFiLiteUser(gctx, actorId)

			resultsChan <- result{
				actorId:      actorId,
				isFiLiteUser: isFiLiteUser,
				err:          err,
			}

			// Don't propagate ErrRecordNotFound to errgroup, handle it later
			if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
				return fmt.Errorf("error checking if user is FiLite, actorId: %s, err: %w", actorId, err)
			}
			return nil
		})
	}

	// Wait for all goroutines to complete
	if err := g.Wait(); err != nil {
		return false, "", err
	}

	// Close channel after all goroutines complete
	close(resultsChan)

	var recordNotFoundCount int

	// Process all results from closed channel
	for res := range resultsChan {
		if res.err != nil {
			if errors.Is(res.err, epifierrors.ErrRecordNotFound) {
				recordNotFoundCount++
				continue
			}
			// This shouldn't happen due to our errgroup logic, but keeping as safety
			return false, "", fmt.Errorf("error checking if user is FiLite, actorId: %s, err: %w", res.actorId, res.err)
		}

		// If any actor is FiLite user, return true
		if res.isFiLiteUser {
			return true, res.actorId, nil
		}
	}

	// If all actors resulted in ErrRecordNotFound, return ErrRecordNotFound
	if recordNotFoundCount == len(actorIds) {
		return false, "", epifierrors.ErrRecordNotFound
	}

	// If we reach here, no actor is FiLite but some actors were found (no ErrRecordNotFound)
	return false, "", nil
}

func (gcs *GeneratorConsumerService) isEitherOfActorNonResidentUser(ctx context.Context, actorIds []string) (bool, string, error) {
	for _, actorId := range actorIds {
		isNRUser, err := gcs.userHelper.IsNonResidentUser(ctx, actorId)
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			continue
		case err != nil:
			return false, "", fmt.Errorf("error checking if user is non-resident, actorId: %s, err: %w", actorId, err)
		case isNRUser:
			return true, actorId, nil
		}
	}
	return false, "", nil
}

func (gcs *GeneratorConsumerService) getCollectedDataTypesEligibleForFiLite() []rewardsPb.CollectedDataType {
	eligibleTypes := []rewardsPb.CollectedDataType{
		rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION,
		rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
		rewardsPb.CollectedDataType_CREDIT_CARD_REQUEST_STAGE_UPDATE,
		rewardsPb.CollectedDataType_OFFER_REDEMPTION_STATUS_UPDATE,
		rewardsPb.CollectedDataType_MANUAL_GIVEAWAY,
	}

	// Using the same flag which decides in datacollector whether TPAP txns should be processed or not to have a kill-switch control over both the flows.
	// Also, the flag's default value will preserve existing behaviour.
	// https://github.com/epiFi/tickets/issues/56397
	//
	// Tomorrow if we need to reward Fi-lite users for TPAP txns, we can remove this check & consider CollectedDataType_ORDER eligible for Fi-lite users.
	if !gcs.dynConf.Flags().EnableTPAPOrdersForRewardsProcessing() {
		eligibleTypes = append(eligibleTypes, rewardsPb.CollectedDataType_ORDER)
	}

	return eligibleTypes
}
