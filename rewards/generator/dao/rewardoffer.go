package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	gormv2 "gorm.io/gorm"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/nulltypes"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"

	"github.com/epifi/gamma/rewards/generator/dao/model"
)

type CrdbRewardOfferDao struct {
	DB *gormv2.DB
}

func NewCrdbRewardOfferDao(db pkgTypes.RewardsPGDB) *CrdbRewardOfferDao {
	return &CrdbRewardOfferDao{DB: db}
}

var _ RewardOfferDao = &CrdbRewardOfferDao{}

func (dao *CrdbRewardOfferDao) CreateRewardOffer(ctx context.Context, req *rewardOffersPb.CreateRewardOfferRequest) (*rewardOffersPb.RewardOffer, error) {
	defer metric_util.TrackDuration("rewards/generator/dao", "CrdbRewardOfferDao", "CreateRewardOffer", time.Now())
	db := dao.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	rewardOffer, err := model.NewRewardOffer(req)
	if err != nil {
		return nil, err
	}
	res := db.Create(rewardOffer)
	if res.Error != nil {
		return nil, res.Error
	}
	return rewardOffer.GetRewardOfferProto()
}

// Deprecated: FetchRewardOfferById is deprecated.
func (dao *CrdbRewardOfferDao) FetchRewardOfferById(ctx context.Context, id string) (*rewardOffersPb.RewardOffer, error) {
	defer metric_util.TrackDuration("rewards/generator/dao", "CrdbRewardOfferDao", "FetchRewardOfferById", time.Now())
	db := dao.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	var rewardOfferModel model.RewardOffer
	res := db.First(&rewardOfferModel, "id = ?", id)
	if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, errors.Wrap(res.Error, "error fetching reward offer by id")
	}
	return rewardOfferModel.GetRewardOfferProto()
}

func (dao *CrdbRewardOfferDao) FetchRewardOffersByIds(ctx context.Context, ids []string) ([]*rewardOffersPb.RewardOffer, error) {
	defer metric_util.TrackDuration("rewards/generator/dao", "CrdbRewardOfferDao", "FetchRewardOffersByIds", time.Now())
	return dao.getRewardOffersByIds(ctx, ids)
}

func (dao *CrdbRewardOfferDao) FetchRewardOffers(ctx context.Context, req *rewardOffersPb.GetRewardOffersRequest) ([]*rewardOffersPb.RewardOffer, error) {
	defer metric_util.TrackDuration("rewards/generator/dao", "CrdbRewardOfferDao", "FetchRewardOffers", time.Now())
	db := dao.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	query := db.Where("status=? and is_visible=?", req.Status, req.IsVisible)

	if req.GetId() != "" {
		query = query.Where("id = ?", req.GetId())
	}
	// add active since filter only if activeBefore is passed
	if req.ActiveSince != "" {
		activeSinceTime, err := time.Parse(time.RFC3339, req.ActiveSince)
		if err != nil {
			return nil, err
		}
		query = query.Where("active_since<=?", activeSinceTime)
	}
	// if active till passed the filter with active after else filter with current time
	if req.ActiveTill != "" {
		activeTillTime, err := time.Parse(time.RFC3339, req.ActiveTill)
		if err != nil {
			return nil, err
		}
		query = query.Where("active_till>?", activeTillTime)
	}
	// add display since filter only if activeBefore is passed
	if req.DisplaySince != "" {
		displaySinceTime, err := time.Parse(time.RFC3339, req.DisplaySince)
		if err != nil {
			return nil, err
		}
		query = query.Where("display_since<=?", displaySinceTime)
	}
	// if display till passed the filter with active after else filter with current time
	if req.DisplayTill != "" {
		displayTillTime, err := time.Parse(time.RFC3339, req.DisplayTill)
		if err != nil {
			return nil, err
		}
		query = query.Where("display_till>?", displayTillTime)
	}
	// filter on offer types
	if len(req.GetOfferTypes()) != 0 {
		query = query.Where("offer_type in (?)", req.GetOfferTypes())
	}
	// filter on segment id
	if req.GetDisplaySegmentId() != "" {
		query = query.Where("display_segment_id=? and is_display_segment_excluded=?", req.GetDisplaySegmentId(), req.GetIsDisplaySegmentExcluded())
	}
	// when SupportedPlatform filter is specified, we expect all reward offers to be returned that have the specified
	// value OR PLATFORM_UNSPECIFIED as value of supported_platform field. This expectation comes from the fact that
	// reward offers that have PLATFORM_UNSPECIFIED are platform independent, and are to be fetched for all platforms.
	if req.GetSupportedPlatform() != commontypes.Platform_PLATFORM_UNSPECIFIED {
		query = query.Where("supported_platform in (?, ?)", commontypes.Platform_PLATFORM_UNSPECIFIED.String(), req.GetSupportedPlatform().String())
	}

	if req.GetGenerationType() != rewardOffersPb.GenerationType_GENERATION_TYPE_UNSPECIFIED {
		query = query.Where("generation_type = ?", req.GetGenerationType())
	}

	// enforce ordering
	query = query.Order("created_at desc")

	var rewardOffers []*model.RewardOffer
	if err := query.Find(&rewardOffers).Error; err != nil {
		return nil, err
	}
	var rewardOfferProtos []*rewardOffersPb.RewardOffer
	for _, rewardOffer := range rewardOffers {
		offerProto, err := rewardOffer.GetRewardOfferProto()
		if err != nil {
			return nil, err
		}
		rewardOfferProtos = append(rewardOfferProtos, offerProto)
	}
	return rewardOfferProtos, nil
}

func (dao *CrdbRewardOfferDao) UpdateRewardOfferStatus(ctx context.Context, req *rewardOffersPb.UpdateRewardOfferStatusRequest) (*rewardOffersPb.RewardOffer, error) {
	defer metric_util.TrackDuration("rewards/generator/dao", "CrdbRewardOfferDao", "UpdateRewardOfferStatus", time.Now())
	db := dao.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	rewardOffer := &model.RewardOffer{Id: req.Id}
	query := db
	// to control state changes
	switch req.Status {
	case rewardOffersPb.RewardOfferStatus_CREATED:
		return nil, fmt.Errorf("can't update status to CREATED")
	case rewardOffersPb.RewardOfferStatus_APPROVED:
		query = query.Where("status = ?", rewardOffersPb.RewardOfferStatus_CREATED)
	case rewardOffersPb.RewardOfferStatus_ACTIVE:
		query = query.Where("status = ?", rewardOffersPb.RewardOfferStatus_APPROVED)
	case rewardOffersPb.RewardOfferStatus_INACTIVE:
		query = query.Where("status = ?", rewardOffersPb.RewardOfferStatus_ACTIVE)
	case rewardOffersPb.RewardOfferStatus_TERMINATED:
		query = query.Where("status != ?", rewardOffersPb.RewardOfferStatus_TERMINATED)
	default:
		return nil, fmt.Errorf("can't update status to %v", req.Status)
	}
	var updatedRowCount int64 = 0
	err := db.Transaction(func(tx *gormv2.DB) error {
		res := query.Model(rewardOffer).Updates(model.RewardOffer{Status: req.Status})
		if res.Error != nil {
			return fmt.Errorf("unable to update status req : %vm err: %v", req, res.Error)
		}
		updatedRowCount = res.RowsAffected
		return nil
	})
	if err != nil {
		return nil, err
	}
	if updatedRowCount == 0 {
		return nil, fmt.Errorf("invalid status change req %v", req)
	}

	if err := db.First(rewardOffer).Error; err != nil {
		return nil, err
	}
	return rewardOffer.GetRewardOfferProto()
}

func (dao *CrdbRewardOfferDao) UpdateRewardOfferDetails(ctx context.Context, req *rewardOffersPb.UpdateRewardOfferRequest) (*rewardOffersPb.RewardOffer, error) {
	defer metric_util.TrackDuration("rewards/generator/dao", "CrdbRewardOfferDao", "UpdateRewardOfferDetails", time.Now())
	updateRewardOfferModel := &model.RewardOffer{}
	var updateColumns []string
	db := dao.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	query := db.Model(&model.RewardOffer{}).Where("id = ?", req.GetId())
	// active_till of offer needs to be updated
	if req.GetActiveTill() != nil {
		activeTill := req.GetActiveTill().AsTime()
		updateRewardOfferModel.ActiveTill = &activeTill
		updateColumns = append(updateColumns, "active_till")
		query = query.Where("active_till>?", time.Now())
	}

	// active_since of offer needs to be updated
	if req.GetActiveSince() != nil {
		activeSince := req.GetActiveSince().AsTime()
		updateRewardOfferModel.ActiveSince = &activeSince
		updateColumns = append(updateColumns, "active_since")
	}

	if req.GetDisplayMeta() != nil {
		// display_since of offer needs to be updated
		displaySince, err := time.Parse(time.RFC3339, req.GetDisplayMeta().GetDisplaySince())
		if err != nil {
			return nil, fmt.Errorf("error converting display since string to time object, err: %w", err)
		}
		updateRewardOfferModel.DisplaySince = &displaySince
		updateColumns = append(updateColumns, "display_since")

		// display_till of offer needs to be updated
		displayTill, err := time.Parse(time.RFC3339, req.GetDisplayMeta().GetDisplayTill())
		if err != nil {
			return nil, fmt.Errorf("error converting display till string to time object, err: %w", err)
		}
		updateRewardOfferModel.DisplayTill = &displayTill
		updateColumns = append(updateColumns, "display_till")

		// display_meta of offer needs to be updated
		displayMetaBytes, err := protojson.Marshal(req.GetDisplayMeta())
		if err != nil {
			return nil, fmt.Errorf("error marshalling display metadata, err: %w", err)
		}
		updateRewardOfferModel.DisplayMeta = string(displayMetaBytes)
		updateColumns = append(updateColumns, "display_meta")
	}

	if req.GetDisplaySegmentExpression() != "" {
		updateRewardOfferModel.DisplaySegmentExpression = nulltypes.NewNullString(req.GetDisplaySegmentExpression())
		updateColumns = append(updateColumns, "display_segment_expression")
	}

	if req.GetGroupId() != "" {
		updateRewardOfferModel.GroupId = nulltypes.NewNullString(req.GetGroupId())
		updateColumns = append(updateColumns, "group_id")
	}

	// if request has tags, then tags also need to be updated for the offer
	if len(req.GetOfferTags()) > 0 {
		updateRewardOfferModel.Tags = &model.RewardOfferTags{Tags: req.GetOfferTags()}
		updateColumns = append(updateColumns, "tags")
	}

	if req.GetConstraintMeta() != "" {
		updateRewardOfferModel.ConstraintsMeta = req.GetConstraintMeta()
		updateColumns = append(updateColumns, "constraints_meta")
	}
	if req.GetShouldUpdateRewardOfferType() {
		updateRewardOfferModel.OfferType = req.GetRewardOfferType()
		updateColumns = append(updateColumns, "offer_type")
	}
	if req.GetActionType() != "" {
		updateRewardOfferModel.ActionType = req.GetActionType()
		updateColumns = append(updateColumns, "action_type")
	}
	if req.GetUnlockMeta() != nil {
		updateRewardOfferModel.UnlockMeta = req.GetUnlockMeta()
		updateColumns = append(updateColumns, "unlock_meta")
	}
	if req.GetUnlockEvent() != rewardsPb.CollectedDataType_UNSPECIFIED_COLLECTED_DATA_TYPE {
		updateRewardOfferModel.UnlockEvent = req.GetUnlockEvent()
		updateColumns = append(updateColumns, "unlock_event")
	}
	if req.GetRewardMeta() != "" {
		updateRewardOfferModel.RewardMeta = req.GetRewardMeta()
		updateColumns = append(updateColumns, "reward_meta")
	}

	if req.GetAdditionalDetails() != nil {
		updateRewardOfferModel.AdditionalDetails = req.GetAdditionalDetails()
		updateColumns = append(updateColumns, "additional_details")
	}

	if req.GetAnalyticsData() != nil {
		updateRewardOfferModel.AnalyticsData = req.GetAnalyticsData()
		updateColumns = append(updateColumns, "analytics_data")
	}

	if req.GetIsVisible() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		updateRewardOfferModel.IsVisible = req.GetIsVisible().ToBool()
		updateColumns = append(updateColumns, "is_visible")
	}

	// update reward offer properties
	res := query.Select(updateColumns).Updates(updateRewardOfferModel)
	if res.Error != nil {
		return nil, errors.Wrap(res.Error, "error updating reward offer details")
	}
	if res.RowsAffected == 0 {
		return nil, epifierrors.ErrRowNotUpdated
	}

	// fetch and return updated reward offer
	var updatedRewardOffer model.RewardOffer
	if err := db.First(&updatedRewardOffer, "id = ?", req.GetId()).Error; err != nil {
		return nil, fmt.Errorf("error fetching updated reward offer, err: %w", err)
	}
	return updatedRewardOffer.GetRewardOfferProto()
}

func (dao *CrdbRewardOfferDao) FetchRewardOffersActiveAtTime(ctx context.Context, actionType string, activeAtTime time.Time, generationType rewardOffersPb.GenerationType) ([]*rewardOffersPb.RewardOffer, error) {
	defer metric_util.TrackDuration("rewards/generator/dao", "CrdbRewardOfferDao", "FetchRewardOffersActiveAtTime", time.Now())
	if activeAtTime.IsZero() {
		return nil, errors.New("invalid request to fetch active offers at time, nil activeAtTime")
	}
	db := dao.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	query := db.Where("status=?", rewardOffersPb.RewardOfferStatus_ACTIVE)
	query = query.Where("action_type=?", actionType)
	query = query.Where("active_since<=?", activeAtTime)
	query = query.Where("active_till>?", activeAtTime)
	if generationType != rewardOffersPb.GenerationType_GENERATION_TYPE_UNSPECIFIED {
		query = query.Where("generation_type = ?", generationType)
	}

	var rewardOffers []*model.RewardOffer
	if err := query.Find(&rewardOffers).Error; err != nil {
		return nil, err
	}
	var offerProtos []*rewardOffersPb.RewardOffer
	for _, rewardOffer := range rewardOffers {
		offerProto, err := rewardOffer.GetRewardOfferProto()
		if err != nil {
			return nil, err
		}
		offerProtos = append(offerProtos, offerProto)
	}
	return offerProtos, nil
}

// GetActorLevelRewardOfferInventory returns actor level inventory for given reward offers and given actor.
func (dao *CrdbRewardOfferDao) GetActorLevelRewardOfferInventory(ctx context.Context, actorId string, offerIds []string) ([]*rewardOffersPb.RewardOfferInventory, error) {
	defer metric_util.TrackDuration("rewards/generator/dao", "CrdbRewardOfferDao", "GetActorLevelRewardOfferInventory", time.Now())
	actorLevelInventory := make([]*rewardOffersPb.RewardOfferInventory, 0, len(offerIds))
	if len(offerIds) == 0 {
		return actorLevelInventory, nil
	}
	// fetch reward offers with given ids
	rewardOfferProtos, err := dao.getRewardOffersByIds(ctx, offerIds)
	if err != nil {
		return nil, errors.Wrap(err, "error fetching reward offers with ids")
	}

	db := dao.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	// fetch actor level reward offer aggregates
	var rewardOfferActorAggregates []*model.RewardOfferActorAggregate
	if err := db.Where("actor_id = ? and offer_id in (?)", actorId, offerIds).Find(&rewardOfferActorAggregates).Error; err != nil {
		return nil, errors.Wrap(err, "error fetching reward offer actor aggregates using actor and offer id")
	}
	offerIdToOfferActorAggregateMap := make(map[string]*model.RewardOfferActorAggregate, len(rewardOfferProtos))
	for _, offerActorAggregate := range rewardOfferActorAggregates {
		offerIdToOfferActorAggregateMap[offerActorAggregate.OfferId] = offerActorAggregate
	}

	// rewardOfferActorAggregate entry is only created when that reward offer is triggered for that actor,
	// so no entry for a reward offer and actor combination implies inventory is not utilized at all.
	for _, rewardOffer := range rewardOfferProtos {
		totalOfferCount, remainingCount := rewardOffer.GetRewardMeta().GetRewardAggregates().GetUserAggregate(), 0
		rewardOfferActorAggregate, ok := offerIdToOfferActorAggregateMap[rewardOffer.GetId()]
		if ok {
			remainingCount = rewardOfferActorAggregate.Count
		} else {
			remainingCount = int(totalOfferCount)
		}
		actorLevelInventory = append(actorLevelInventory, &rewardOffersPb.RewardOfferInventory{
			RewardOfferId:  rewardOffer.GetId(),
			RemainingCount: uint32(remainingCount),
			TotalCount:     totalOfferCount,
		})
	}
	return actorLevelInventory, nil
}

// GetActionLevelRewardOfferInventory returns action level inventory for given reward offers.
func (dao *CrdbRewardOfferDao) GetActionLevelRewardOfferInventory(ctx context.Context, offerIds []string) ([]*rewardOffersPb.RewardOfferInventory, error) {
	defer metric_util.TrackDuration("rewards/generator/dao", "CrdbRewardOfferDao", "GetActionLevelRewardOfferInventory", time.Now())
	actionLevelInventory := make([]*rewardOffersPb.RewardOfferInventory, 0, len(offerIds))
	if len(offerIds) == 0 {
		return actionLevelInventory, nil
	}
	// fetch reward offers with given ids
	rewardOfferProtos, err := dao.getRewardOffersByIds(ctx, offerIds)
	if err != nil {
		return nil, errors.Wrap(err, "error fetching reward offers with ids")
	}

	db := dao.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	// fetch action level reward offer aggregates
	var rewardOfferAggregates []*model.RewardOfferAggregate
	if err := db.Where("offer_id in (?)", offerIds).Find(&rewardOfferAggregates).Error; err != nil {
		return nil, errors.Wrap(err, "error fetching reward offer aggregates using actor and offer id")
	}
	offerIdToActionAggregateMap := make(map[string]*model.RewardOfferAggregate, len(rewardOfferProtos))
	for _, offerAggregate := range rewardOfferAggregates {
		offerIdToActionAggregateMap[offerAggregate.OfferId] = offerAggregate
	}

	// rewardOfferAggregate entry is only created when that reward offer is triggered for some reward action,
	// so no entry for a reward offer implies inventory is not utilized at all.
	for _, rewardOffer := range rewardOfferProtos {
		totalOfferCount, remainingCount := rewardOffer.GetRewardMeta().GetRewardAggregates().GetActionAggregate(), 0
		rewardOfferAggregate, ok := offerIdToActionAggregateMap[rewardOffer.GetId()]
		if ok {
			remainingCount = rewardOfferAggregate.Count
		} else {
			remainingCount = int(totalOfferCount)
		}
		actionLevelInventory = append(actionLevelInventory, &rewardOffersPb.RewardOfferInventory{
			RewardOfferId:  rewardOffer.GetId(),
			RemainingCount: uint32(remainingCount),
			TotalCount:     totalOfferCount,
		})
	}
	return actionLevelInventory, nil
}

// Todo(dibyanshu): Depreciate this/ Use GetOffersRewardUnitsUtilized()
func (dao *CrdbRewardOfferDao) GetRewardUnitsUtilisedForActor(ctx context.Context, actorId, offerId string) (*model.RewardOfferRewardUnitsActorUtilisation, error) {
	defer metric_util.TrackDuration("rewards/generator/dao", "CrdbRewardOfferDao", "GetRewardUnitsUtilisedForActor", time.Now())
	db := dao.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	rewardUnitsUtilised := &model.RewardOfferRewardUnitsActorUtilisation{}
	res := db.Take(rewardUnitsUtilised, "actor_id = ? and offer_id = ?", actorId, offerId)
	if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}

	if res.Error != nil {
		return nil, errors.Wrap(res.Error, "error fetching reward-units cap for an actor at offer level")
	}

	return rewardUnitsUtilised, nil
}

// nolint: dupl
func (dao *CrdbRewardOfferDao) GetOffersRewardUnitsUtilized(ctx context.Context, actorId string, offerIds []string) ([]*rewardOffersPb.RewardOfferRewardUnitsActorUtilisation, error) {
	defer metric_util.TrackDuration("rewards/generator/dao", "CrdbRewardOfferDao", "GetOffersRewardUnitsUtilized", time.Now())
	var (
		offerUnitsUtilsProtoList = make([]*rewardOffersPb.RewardOfferRewardUnitsActorUtilisation, 0, len(offerIds))
		offerUnitsUtilsModelList []*model.RewardOfferRewardUnitsActorUtilisation
	)
	if len(offerIds) == 0 {
		return offerUnitsUtilsProtoList, nil
	}
	if actorId == "" {
		return nil, errors.New("actor id cannot be empty")
	}
	db := gormctxv2.FromContextOrDefault(ctx, dao.DB)
	if err := db.Where("actor_id = ? and offer_id in (?)", actorId, offerIds).Find(&offerUnitsUtilsModelList).Error; err != nil {
		return nil, errors.Wrap(err, "error fetching reward offers utilization by offer ids")
	}
	for _, offerUnitsUtilsModel := range offerUnitsUtilsModelList {
		offerUnitsUtilsProtoList = append(offerUnitsUtilsProtoList, offerUnitsUtilsModel.GetProto())
	}
	return offerUnitsUtilsProtoList, nil
}

// nolint: dupl
func (dao *CrdbRewardOfferDao) GetOffersRewardUnitsUtilizedInTimePeriod(ctx context.Context, actorId string, offerIds []string, fromTime, tillTime time.Time) ([]*rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod, error) {
	defer metric_util.TrackDuration("rewards/generator/dao", "CrdbRewardOfferDao", "GetOffersRewardUnitsUtilizedInTimePeriod", time.Now())
	var (
		offerUnitsUtilsProtoList = make([]*rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod, 0, len(offerIds))
		offerUnitsUtilsModelList []*model.RewardOfferRewardUnitsActorUtilisationInTimePeriod
	)
	if len(offerIds) == 0 {
		return offerUnitsUtilsProtoList, nil
	}
	if actorId == "" {
		return nil, errors.New("actor id cannot be empty")
	}
	db := gormctxv2.FromContextOrDefault(ctx, dao.DB)
	if err := db.Where("actor_id = ? and offer_id in (?) and from_Time = ? and till_time = ?", actorId, offerIds, fromTime, tillTime).Find(&offerUnitsUtilsModelList).Error; err != nil {
		return nil, errors.Wrap(err, "error fetching reward offers utilization by offer ids")
	}
	for _, offerUnitsUtilsModel := range offerUnitsUtilsModelList {
		offerUnitsUtilsProtoList = append(offerUnitsUtilsProtoList, offerUnitsUtilsModel.GetProto())
	}
	return offerUnitsUtilsProtoList, nil
}

// nolint: dupl
func (dao *CrdbRewardOfferDao) getRewardOffersByIds(ctx context.Context, ids []string) ([]*rewardOffersPb.RewardOffer, error) {
	rewardOfferProtos := make([]*rewardOffersPb.RewardOffer, 0, len(ids))
	if len(ids) == 0 {
		return rewardOfferProtos, nil
	}
	db := dao.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	var rewardOffers []*model.RewardOffer
	if err := db.Where("id in (?)", ids).Find(&rewardOffers).Error; err != nil {
		return nil, errors.Wrap(err, "error fetching reward offers by ids")
	}
	for _, rewardOffer := range rewardOffers {
		offerProto, err := rewardOffer.GetRewardOfferProto()
		if err != nil {
			return nil, err
		}
		rewardOfferProtos = append(rewardOfferProtos, offerProto)
	}
	return rewardOfferProtos, nil
}

func (dao *CrdbRewardOfferDao) GetByLikeQuery(ctx context.Context, queryString string, limit int) ([]*rewardOffersPb.RewardOffer, error) {
	defer metric_util.TrackDuration("rewards/generator/dao", "CrdbRewardOfferDao", "GetByLikeQuery", time.Now())
	if queryString == "" {
		return nil, fmt.Errorf("query string can't be empty")
	}
	if limit == 0 {
		limit = 50
	}

	db := dao.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	query := db.Table("reward_offers")
	query = query.Or("constraints_meta like ?", fmt.Sprintf("%%%s%%", queryString))
	query = query.Or("reward_meta like ?", fmt.Sprintf("%%%s%%", queryString))
	query = query.Or("display_meta like ?", fmt.Sprintf("%%%s%%", queryString))
	query = query.Limit(limit)

	var rewardOffers []*model.RewardOffer
	if err := query.Find(&rewardOffers).Error; err != nil {
		return nil, errors.Wrap(err, "error fetching reward offers by ids")
	}

	rewardOfferProtos := make([]*rewardOffersPb.RewardOffer, 0, len(rewardOffers))
	for _, rewardOffer := range rewardOffers {
		offerProto, err := rewardOffer.GetRewardOfferProto()
		if err != nil {
			return nil, err
		}
		rewardOfferProtos = append(rewardOfferProtos, offerProto)
	}
	return rewardOfferProtos, nil
}
