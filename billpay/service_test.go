package billpay_test

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	billpaypb "github.com/epifi/gamma/api/billpay"
	billpayenums "github.com/epifi/gamma/api/billpay/enums"
	userPb "github.com/epifi/gamma/api/user"
	rechargePb "github.com/epifi/gamma/api/vendorgateway/recharge"
	"github.com/epifi/gamma/billpay"
	"github.com/epifi/gamma/billpay/utils"

	// Import all required mocks
	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	cacheMocks "github.com/epifi/be-common/pkg/cache/mocks"

	userMocks "github.com/epifi/gamma/api/user/mocks"
	rechargeMocks "github.com/epifi/gamma/api/vendorgateway/recharge/mocks"
	daoMocks "github.com/epifi/gamma/billpay/dao/mocks"
)

func TestService_FetchRechargeOrders(t *testing.T) {
	type args struct {
		ctx context.Context
		req *billpaypb.FetchRechargeOrdersRequest
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(rechargeOrderDao *daoMocks.MockRechargeOrderDao)
		want       *billpaypb.FetchRechargeOrdersResponse
		wantErr    bool
	}{
		{
			name: "should successfully fetch recharge orders",
			args: args{
				ctx: context.Background(),
				req: &billpaypb.FetchRechargeOrdersRequest{
					ActorId: testActorID,
					PageContext: &rpcPb.PageContextRequest{
						PageSize: 30,
					},
				},
			},
			setupMocks: func(rechargeOrderDao *daoMocks.MockRechargeOrderDao) {
				expectedOrders := []*billpaypb.RechargeOrder{
					{
						Id:                "order-1",
						ActorId:           testActorID,
						AccountType:       billpayenums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
						AccountIdentifier: testAccountIdentifier,
						Status:            billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
						ClientRequestId:   "req-1",
					},
				}
				expectedPageCtx := &rpcPb.PageContextResponse{
					AfterToken: "",
				}

				rechargeOrderDao.EXPECT().
					GetByActorId(gomock.Any(), gomock.Any(), testActorID, gomock.Any(), uint32(30), nil).
					Return(expectedOrders, expectedPageCtx, nil).
					Times(1)
			},
			want: &billpaypb.FetchRechargeOrdersResponse{
				Status: rpcPb.StatusOk(),
				Orders: []*billpaypb.RechargeOrder{
					{
						Id:                "order-1",
						ActorId:           testActorID,
						AccountType:       billpayenums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
						AccountIdentifier: testAccountIdentifier,
						Status:            billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
						ClientRequestId:   "req-1",
					},
				},
				PageContextResponse: &rpcPb.PageContextResponse{
					AfterToken: "",
				},
			},
			wantErr: false,
		},
		{
			name: "should handle DAO error",
			args: args{
				ctx: context.Background(),
				req: &billpaypb.FetchRechargeOrdersRequest{
					ActorId: testActorID,
				},
			},
			setupMocks: func(rechargeOrderDao *daoMocks.MockRechargeOrderDao) {
				rechargeOrderDao.EXPECT().
					GetByActorId(gomock.Any(), gomock.Any(), testActorID, gomock.Any(), uint32(30), nil).
					Return(nil, nil, errors.New("dao error")).
					Times(1)
			},
			want: &billpaypb.FetchRechargeOrdersResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("dao error"),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Setup mocks
			mockRechargeOrderDao := daoMocks.NewMockRechargeOrderDao(ctrl)
			tt.setupMocks(mockRechargeOrderDao)

			// Create service
			service := billpay.NewBillPayService(
				gconf,
				nil, // billerManager not needed for this test
				mockRechargeOrderDao,
				nil, nil, nil, nil, nil,
			)

			got, err := service.FetchRechargeOrders(tt.args.ctx, tt.args.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("Service.FetchRechargeOrders() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("Service.FetchRechargeOrders() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestService_GetLatestRechargeOrdersForUniqueAccounts(t *testing.T) {
	type args struct {
		ctx context.Context
		req *billpaypb.GetLatestRechargeOrdersForUniqueAccountsRequest
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(rechargeOrderDao *daoMocks.MockRechargeOrderDao)
		want       *billpaypb.GetLatestRechargeOrdersForUniqueAccountsResponse
		wantErr    bool
	}{
		{
			name: "should successfully get latest orders for unique accounts",
			args: args{
				ctx: context.Background(),
				req: &billpaypb.GetLatestRechargeOrdersForUniqueAccountsRequest{
					ActorId:     testActorID,
					AccountType: billpayenums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
				},
			},
			setupMocks: func(rechargeOrderDao *daoMocks.MockRechargeOrderDao) {
				expectedOrders := []*billpaypb.RechargeOrder{
					{
						Id:                "order-1",
						ActorId:           testActorID,
						AccountType:       billpayenums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
						AccountIdentifier: testAccountIdentifier,
						Status:            billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
					},
				}

				rechargeOrderDao.EXPECT().
					GetLatestOrdersForUniqueAccountsByActor(
						gomock.Any(),
						testActorID,
						billpayenums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
					).
					Return(expectedOrders, nil).
					Times(1)
			},
			want: &billpaypb.GetLatestRechargeOrdersForUniqueAccountsResponse{
				Status: rpcPb.StatusOk(),
				LatestOrders: []*billpaypb.RechargeOrder{
					{
						Id:                "order-1",
						ActorId:           testActorID,
						AccountType:       billpayenums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
						AccountIdentifier: testAccountIdentifier,
						Status:            billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should handle DAO error",
			args: args{
				ctx: context.Background(),
				req: &billpaypb.GetLatestRechargeOrdersForUniqueAccountsRequest{
					ActorId:     testActorID,
					AccountType: billpayenums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
				},
			},
			setupMocks: func(rechargeOrderDao *daoMocks.MockRechargeOrderDao) {
				rechargeOrderDao.EXPECT().
					GetLatestOrdersForUniqueAccountsByActor(
						gomock.Any(),
						testActorID,
						billpayenums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
					).
					Return(nil, errors.New("dao error")).
					Times(1)
			},
			want: &billpaypb.GetLatestRechargeOrdersForUniqueAccountsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("dao error"),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Setup mocks
			mockRechargeOrderDao := daoMocks.NewMockRechargeOrderDao(ctrl)
			tt.setupMocks(mockRechargeOrderDao)

			// Create service
			service := billpay.NewBillPayService(
				gconf,
				nil, // billerManager not needed for this test
				mockRechargeOrderDao,
				nil, nil, nil, nil, nil,
			)

			got, err := service.GetLatestRechargeOrdersForUniqueAccounts(tt.args.ctx, tt.args.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("Service.GetLatestRechargeOrdersForUniqueAccounts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("Service.GetLatestRechargeOrdersForUniqueAccounts() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestService_FetchRechargePlans(t *testing.T) {
	type args struct {
		ctx context.Context
		req *billpaypb.FetchRechargePlansRequest
	}

	// Test plan with amount from which planId is generated
	testAmount := moneyPkg.ParseInt(100, "INR") // 100.00 INR
	expectedPlans := []*billpaypb.MobileRechargePlan{
		{
			PlanName:        "Test Plan",
			Amount:          testAmount,
			Validity:        "28 days",
			PlanDescription: "Test recharge plan",
		},
	}
	// Generate planId using the same logic as the GetPlanId() method
	// expectedPlanId := expectedPlans[0].GetPlanId() // This will be "OPERATOR_AIRTEL_₹100.00"

	tests := []struct {
		name       string
		args       args
		setupMocks func(cache *cacheMocks.MockCacheStorage, userClient *userMocks.MockUsersClient, rechargeClient *rechargeMocks.MockMobileRechargeServiceClient)
		want       *billpaypb.FetchRechargePlansResponse
		wantErr    bool
	}{
		{
			name: "should successfully fetch plans from cache",
			args: args{
				ctx: context.Background(),
				req: &billpaypb.FetchRechargePlansRequest{
					ActorId:           testActorID,
					AccountIdentifier: testAccountIdentifier,
				},
			},
			setupMocks: func(cache *cacheMocks.MockCacheStorage, userClient *userMocks.MockUsersClient, rechargeClient *rechargeMocks.MockMobileRechargeServiceClient) {
				// Mock cache hit
				cachedResponse := &billpaypb.RechargePlansData{
					Plans:    expectedPlans,
					Operator: billpayenums.Operator_OPERATOR_AIRTEL,
				}
				cachedData, _ := proto.Marshal(cachedResponse)
				cache.EXPECT().
					Get(gomock.Any(), gomock.Any()).
					Return(string(cachedData), nil).
					Times(1)
			},
			want: &billpaypb.FetchRechargePlansResponse{
				Status:   rpcPb.StatusOk(),
				Plans:    expectedPlans,
				Operator: billpayenums.Operator_OPERATOR_AIRTEL,
			},
			wantErr: false,
		},
		{
			name: "should handle cache miss and fetch from vendor",
			args: args{
				ctx: context.Background(),
				req: &billpaypb.FetchRechargePlansRequest{
					ActorId:           testActorID,
					AccountIdentifier: testAccountIdentifier,
				},
			},
			setupMocks: func(cache *cacheMocks.MockCacheStorage, userClient *userMocks.MockUsersClient, rechargeClient *rechargeMocks.MockMobileRechargeServiceClient) {
				// Mock cache miss
				cache.EXPECT().
					Get(gomock.Any(), gomock.Any()).
					Return("", errors.New("cache miss")).
					Times(1)

				// Mock user service
				userClient.EXPECT().
					GetUser(gomock.Any(), gomock.Eq(&userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{ActorId: testActorID},
					})).
					Return(&userPb.GetUserResponse{
						Status: rpcPb.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    commontypes.IndiaCountryCode,
									NationalNumber: **********,
								},
							},
						},
					}, nil).
					Times(1)

				// Mock operator details fetch
				rechargeClient.EXPECT().
					FetchOperatorDetails(gomock.Any(), gomock.Any()).
					Return(&rechargePb.FetchOperatorDetailsResponse{
						Status: rpcPb.StatusOk(),
						Data: &rechargePb.OperatorDetails{
							CurrentOperator: rechargePb.Operator_OPERATOR_AIRTEL,
							CurrentLocation: "Delhi",
						},
					}, nil).
					Times(1)

				// Mock plans fetch
				rechargeClient.EXPECT().
					FetchPlans(gomock.Any(), gomock.Any()).
					Return(&rechargePb.FetchPlansResponse{
						Status: rpcPb.StatusOk(),
						Plans: []*rechargePb.Plan{
							{
								PlanName:        "Test Plan",
								Amount:          moneyPkg.ParseInt(100, "INR"), // 100.00 INR in paise
								Validity:        "28 days",
								PlanDescription: "Test recharge plan",
							},
						},
					}, nil).
					Times(1)

				// Mock cache set
				cache.EXPECT().
					Set(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).
					Times(1)
			},
			want: &billpaypb.FetchRechargePlansResponse{
				Status:   rpcPb.StatusOk(),
				Plans:    expectedPlans,
				Operator: billpayenums.Operator_OPERATOR_AIRTEL,
			},
			wantErr: false,
		},
		{
			name: "should handle vendor service error",
			args: args{
				ctx: context.Background(),
				req: &billpaypb.FetchRechargePlansRequest{
					ActorId:           testActorID,
					AccountIdentifier: testAccountIdentifier,
				},
			},
			setupMocks: func(cache *cacheMocks.MockCacheStorage, userClient *userMocks.MockUsersClient, rechargeClient *rechargeMocks.MockMobileRechargeServiceClient) {
				// Mock cache miss
				cache.EXPECT().
					Get(gomock.Any(), gomock.Any()).
					Return("", errors.New("cache miss")).
					Times(1)

				// Mock user service error
				userClient.EXPECT().
					GetUser(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("user service error")).
					Times(1)
			},
			want: &billpaypb.FetchRechargePlansResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Setup mocks
			mockCache := cacheMocks.NewMockCacheStorage(ctrl)
			mockUserClient := userMocks.NewMockUsersClient(ctrl)
			mockRechargeClient := rechargeMocks.NewMockMobileRechargeServiceClient(ctrl)

			tt.setupMocks(mockCache, mockUserClient, mockRechargeClient)

			// Create service
			service := billpay.NewBillPayService(
				gconf,
				nil, // billerManager not needed for this test
				nil, // rechargeOrderDao not needed for this test
				nil, // rechargeOrderStageDao not needed for this test
				mockRechargeClient,
				mockUserClient,
				nil, // celestialClient not needed for this test
				mockCache,
			)

			got, err := service.FetchRechargePlans(tt.args.ctx, tt.args.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("Service.FetchRechargePlans() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("Service.FetchRechargePlans() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestService_CreateRechargeOrder(t *testing.T) {
	type args struct {
		ctx context.Context
		req *billpaypb.CreateRechargeOrderRequest
	}

	// Test plan with correct amount and generated planId
	testAmount := moneyPkg.ParseInt(100, "INR")
	testPlan := &billpaypb.MobileRechargePlan{
		PlanName:        "Test Plan",
		Amount:          testAmount,
		Validity:        "28 days",
		PlanDescription: "Test recharge plan",
	}
	testPlanId := testPlan.GetPlanId()

	tests := []struct {
		name       string
		args       args
		setupMocks func(
			rechargeOrderDao *daoMocks.MockRechargeOrderDao,
			cache *cacheMocks.MockCacheStorage,
			celestialClient *celestialMocks.MockCelestialClient,
		)
		want    *billpaypb.CreateRechargeOrderResponse
		wantErr bool
	}{
		{
			name: "should create new recharge order successfully",
			args: args{
				ctx: context.Background(),
				req: &billpaypb.CreateRechargeOrderRequest{
					ActorId:           testActorID,
					AccountType:       billpayenums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
					AccountIdentifier: testAccountIdentifier,
					Operator:          billpayenums.Operator_OPERATOR_AIRTEL,
					PlanId:            testPlanId,
				},
			},
			setupMocks: func(
				rechargeOrderDao *daoMocks.MockRechargeOrderDao,
				cache *cacheMocks.MockCacheStorage,
				celestialClient *celestialMocks.MockCelestialClient,
			) {
				// Mock idempotency check - no existing orders
				rechargeOrderDao.EXPECT().
					GetByActorIdAndAccountDetails(
						gomock.Any(),
						testActorID,
						billpayenums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
						testAccountIdentifier,
					).
					Return(nil, epifierrors.ErrRecordNotFound).
					Times(1)

				// Mock cache hit for plans
				cachedResponse := &billpaypb.RechargePlansData{
					Plans:    []*billpaypb.MobileRechargePlan{testPlan},
					Operator: billpayenums.Operator_OPERATOR_AIRTEL,
				}
				cachedData, _ := proto.Marshal(cachedResponse)
				cache.EXPECT().
					Get(gomock.Any(), gomock.Any()).
					Return(string(cachedData), nil).
					Times(1)

				// Mock order creation
				rechargeOrderDao.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, order *billpaypb.RechargeOrder) (*billpaypb.RechargeOrder, error) {
						order.Id = "order-123"
						order.ClientRequestId = testClientRequestID
						return order, nil
					}).
					Times(1)

				// Mock workflow initiation
				celestialClient.EXPECT().
					InitiateWorkflow(gomock.Any(), gomock.Any()).
					Return(&celestialPb.InitiateWorkflowResponse{
						Status: rpcPb.StatusOk(),
					}, nil).
					Times(1)
			},
			want: &billpaypb.CreateRechargeOrderResponse{
				Status:          rpcPb.StatusOk(),
				ClientRequestId: testClientRequestID,
			},
			wantErr: false,
		},
		{
			name: "should return existing client request id for duplicate order",
			args: args{
				ctx: context.Background(),
				req: &billpaypb.CreateRechargeOrderRequest{
					ActorId:           testActorID,
					AccountType:       billpayenums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
					AccountIdentifier: testAccountIdentifier,
					Operator:          billpayenums.Operator_OPERATOR_AIRTEL,
					PlanId:            testPlanId,
				},
			},
			setupMocks: func(
				rechargeOrderDao *daoMocks.MockRechargeOrderDao,
				cache *cacheMocks.MockCacheStorage,
				celestialClient *celestialMocks.MockCelestialClient,
			) {
				// Mock idempotency check - existing order with same plan, in progress
				existingOrder := &billpaypb.RechargeOrder{
					Id:              "existing-order",
					ActorId:         testActorID,
					Status:          billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_INITIATED,
					ClientRequestId: "existing-client-req-id",
					PlanDetails: &billpaypb.PlanDetails{
						MobileRechargePlanDetails: testPlan,
					},
				}

				rechargeOrderDao.EXPECT().
					GetByActorIdAndAccountDetails(
						gomock.Any(),
						testActorID,
						billpayenums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
						testAccountIdentifier,
					).
					Return([]*billpaypb.RechargeOrder{existingOrder}, nil).
					Times(1)
			},
			want: &billpaypb.CreateRechargeOrderResponse{
				Status:          rpcPb.StatusOk(),
				ClientRequestId: "existing-client-req-id",
			},
			wantErr: false,
		},
		{
			name: "should handle plan not found error",
			args: args{
				ctx: context.Background(),
				req: &billpaypb.CreateRechargeOrderRequest{
					ActorId:           testActorID,
					AccountType:       billpayenums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
					AccountIdentifier: testAccountIdentifier,
					Operator:          billpayenums.Operator_OPERATOR_AIRTEL,
					PlanId:            "non-existent-plan",
				},
			},
			setupMocks: func(
				rechargeOrderDao *daoMocks.MockRechargeOrderDao,
				cache *cacheMocks.MockCacheStorage,
				celestialClient *celestialMocks.MockCelestialClient,
			) {
				// Mock idempotency check - no existing orders
				rechargeOrderDao.EXPECT().
					GetByActorIdAndAccountDetails(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil, epifierrors.ErrRecordNotFound).
					Times(1)

				// Mock cache hit for plans (but plan ID won't match)
				cachedResponse := &billpaypb.RechargePlansData{
					Plans:    []*billpaypb.MobileRechargePlan{testPlan}, // Different from requested plan
					Operator: billpayenums.Operator_OPERATOR_AIRTEL,
				}
				cachedData, _ := proto.Marshal(cachedResponse)
				cache.EXPECT().
					Get(gomock.Any(), gomock.Any()).
					Return(string(cachedData), nil).
					Times(1)
			},
			want: &billpaypb.CreateRechargeOrderResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("Plan not found"),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Setup mocks
			mockRechargeOrderDao := daoMocks.NewMockRechargeOrderDao(ctrl)
			mockCelestialClient := celestialMocks.NewMockCelestialClient(ctrl)
			mockCache := cacheMocks.NewMockCacheStorage(ctrl)

			tt.setupMocks(mockRechargeOrderDao, mockCache, mockCelestialClient)

			// Create service
			service := billpay.NewBillPayService(
				gconf,
				nil, // billerManager not needed for this test
				mockRechargeOrderDao,
				nil, // rechargeOrderStageDao not needed for this test
				nil, // rechargeClient not needed for this test
				nil, // userClient not needed for this test
				mockCelestialClient,
				mockCache,
			)

			got, err := service.CreateRechargeOrder(tt.args.ctx, tt.args.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("Service.CreateRechargeOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("Service.CreateRechargeOrder() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestService_GetRechargeOrderStatus(t *testing.T) {
	type args struct {
		ctx context.Context
		req *billpaypb.GetRechargeOrderStatusRequest
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(
			rechargeOrderDao *daoMocks.MockRechargeOrderDao,
			celestialClient *celestialMocks.MockCelestialClient,
		)
		want    *billpaypb.GetRechargeOrderStatusResponse
		wantErr bool
	}{
		{
			name: "should successfully get order status from workflow",
			args: args{
				ctx: context.Background(),
				req: &billpaypb.GetRechargeOrderStatusRequest{
					ClientRequestId: testClientRequestID,
				},
			},
			setupMocks: func(
				rechargeOrderDao *daoMocks.MockRechargeOrderDao,
				celestialClient *celestialMocks.MockCelestialClient,
			) {
				// Mock DAO
				order := &billpaypb.RechargeOrder{
					Id:              "order-123",
					ClientRequestId: testClientRequestID,
					Status:          billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_INITIATED,
					SubStatus:       billpayenums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
				}

				rechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), gomock.Any(), testClientRequestID).
					Return(order, nil).
					Times(1)

				// Mock workflow query
				workflowData := &utils.RechargePaymentWorkflowData{
					OrderStatus:    billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
					OrderSubStatus: billpayenums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					NextAction:     nil,
				}
				workflowDataBytes, _ := json.Marshal(workflowData)

				celestialClient.EXPECT().
					QueryWorkflow(gomock.Any(), gomock.Eq(&celestialPb.QueryWorkflowRequest{
						Identifier: &celestialPb.QueryWorkflowRequest_ClientReqId{
							ClientReqId: &workflowPb.ClientReqId{
								Id:     testClientRequestID,
								Client: workflowPb.Client_PAY,
							},
						},
						QueryType: utils.RechargePaymentStatusQueryType,
						Ownership: commontypes.Ownership_EPIFI_TECH,
					})).
					Return(&celestialPb.QueryWorkflowResponse{
						Status:      rpcPb.StatusOk(),
						QueryResult: workflowDataBytes,
					}, nil).
					Times(1)
			},
			want: &billpaypb.GetRechargeOrderStatusResponse{
				Status:         rpcPb.StatusOk(),
				OrderStatus:    billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
				OrderSubStatus: billpayenums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
				NextAction:     nil,
			},
			wantErr: false,
		},
		{
			name: "should fallback to database status on workflow query error",
			args: args{
				ctx: context.Background(),
				req: &billpaypb.GetRechargeOrderStatusRequest{
					ClientRequestId: testClientRequestID,
				},
			},
			setupMocks: func(
				rechargeOrderDao *daoMocks.MockRechargeOrderDao,
				celestialClient *celestialMocks.MockCelestialClient,
			) {
				// Mock DAO
				order := &billpaypb.RechargeOrder{
					Id:              "order-123",
					ClientRequestId: testClientRequestID,
					Status:          billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_INITIATED,
					SubStatus:       billpayenums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
				}

				rechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), gomock.Any(), testClientRequestID).
					Return(order, nil).
					Times(1)

				// Mock workflow query error
				celestialClient.EXPECT().
					QueryWorkflow(gomock.Any(), gomock.Any()).
					Return(&celestialPb.QueryWorkflowResponse{
						Status: rpcPb.StatusInternal(),
					}, nil).
					Times(1)
			},
			want: &billpaypb.GetRechargeOrderStatusResponse{
				Status:         rpcPb.StatusOk(),
				OrderStatus:    billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_INITIATED,
				OrderSubStatus: billpayenums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
			},
			wantErr: false,
		},
		{
			name: "should handle DAO error",
			args: args{
				ctx: context.Background(),
				req: &billpaypb.GetRechargeOrderStatusRequest{
					ClientRequestId: testClientRequestID,
				},
			},
			setupMocks: func(
				rechargeOrderDao *daoMocks.MockRechargeOrderDao,
				celestialClient *celestialMocks.MockCelestialClient,
			) {
				rechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), gomock.Any(), testClientRequestID).
					Return(nil, errors.New("dao error")).
					Times(1)
			},
			want: &billpaypb.GetRechargeOrderStatusResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("dao error"),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Setup mocks
			mockRechargeOrderDao := daoMocks.NewMockRechargeOrderDao(ctrl)
			mockCelestialClient := celestialMocks.NewMockCelestialClient(ctrl)

			tt.setupMocks(mockRechargeOrderDao, mockCelestialClient)

			// Create service
			service := billpay.NewBillPayService(
				gconf,
				nil, // billerManager not needed for this test
				mockRechargeOrderDao,
				nil, // rechargeOrderStageDao not needed for this test
				nil, // rechargeClient not needed for this test
				nil, // userClient not needed for this test
				mockCelestialClient,
				nil, // plansCacheStorage not needed for this test
			)

			got, err := service.GetRechargeOrderStatus(tt.args.ctx, tt.args.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("Service.GetRechargeOrderStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Compare specific fields
			assert.Equal(t, tt.want.Status, got.Status)
			assert.Equal(t, tt.want.OrderStatus, got.OrderStatus)
			assert.Equal(t, tt.want.OrderSubStatus, got.OrderSubStatus)
		})
	}
}
