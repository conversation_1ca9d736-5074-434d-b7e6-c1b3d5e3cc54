package activity

import (
	"context"
	"errors"
	"fmt"

	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/billpay/dao"
)

func (p *Processor) UpdateRechargeOrderStatus(ctx context.Context, req *billpayActPb.UpdateRechargeOrderStatusRequest) (*billpayActPb.UpdateRechargeOrderStatusResponse, error) {
	var (
		res = &billpayActPb.UpdateRechargeOrderStatusResponse{}
	)

	lg := activity.GetLogger(ctx)

	rechargeOrder, getErr := p.rechargeOrderDao.GetByClientRequestId(ctx, dao.RechargeOrderFieldMasks.BasicInfo(), req.GetRequestHeader().GetClientReqId())
	switch {
	case errors.Is(getErr, epifierrors.ErrRecordNotFound):
		lg.Error("no such recharge order exists", zap.Any(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(getErr))
		return nil, epifitemporal.NewPermanentError(getErr)
	case getErr != nil:
		lg.Error("failed to fetch recharge order by client request id", zap.Error(getErr))
		return nil, epifitemporal.NewTransientError(getErr)
	}

	if req.GetStatusToUpdate() == enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_UNSPECIFIED {
		lg.Error("status cannot be unspecified",
			zap.String(logger.STATUS, req.GetStatusToUpdate().String()), zap.String(logger.SUB_STATUS, req.GetSubStatusToUpdate().String()))
		return nil, epifitemporal.NewTransientError(fmt.Errorf("status cannot be unspecified"))
	}

	if rechargeOrder.GetStatus() == req.GetStatusToUpdate() && rechargeOrder.GetSubStatus() == req.GetSubStatusToUpdate() {
		lg.Info("recharge order status already updated", zap.String(logger.ID, rechargeOrder.GetId()),
			zap.String(logger.STATUS, rechargeOrder.GetStatus().String()), zap.String(logger.SUB_STATUS, rechargeOrder.GetSubStatus().String()))
		return res, nil
	}

	rechargeOrder.Status = req.GetStatusToUpdate()
	rechargeOrder.SubStatus = req.GetSubStatusToUpdate()
	updateErr := p.rechargeOrderDao.Update(ctx, dao.RechargeOrderFieldMasks.StatusSubStatus(), rechargeOrder)
	if updateErr != nil {
		lg.Error("failed to update recharge order status", zap.Error(updateErr), zap.String(logger.CLIENT_REQUEST_ID,
			req.GetRequestHeader().GetClientReqId()), zap.String(logger.ID, rechargeOrder.GetId()))
		return nil, epifitemporal.NewTransientError(updateErr)
	}
	return res, nil
}
