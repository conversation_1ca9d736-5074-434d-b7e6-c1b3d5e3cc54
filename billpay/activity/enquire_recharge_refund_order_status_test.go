package activity_test

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	orderPb "github.com/epifi/gamma/api/order"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/billpay/dao"
)

func TestProcessor_EnquireRechargeRefundOrderStatus(t *testing.T) {
	p, mockDeps, assertTest := getBillpayActivityProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	var (
		testRechargeOrderClientRequestId = "test-recharge-order-client-req-id"
		testRefundClientRequestId        = "test-refund-client-req-id"
		testRechargeOrderId              = "test-recharge-order-id"
		testRefundStageId                = "test-refund-stage-id"
	)

	type args struct {
		ctx context.Context
		req *billpayActPb.EnquireRechargeRefundOrderStatusRequest
	}
	tests := []struct {
		name      string
		args      args
		mockFunc  func()
		want      *billpayActPb.EnquireRechargeRefundOrderStatusResponse
		wantErr   bool
		retryable bool
	}{
		{
			name: "happy_flow_success_completed",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.EnquireRechargeRefundOrderStatusRequest{
					RequestHeader:         &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
					RefundClientRequestId: testRefundClientRequestId,
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testRechargeOrderClientRequestId).
					Return(&billpayPb.RechargeOrder{
						Id:      testRechargeOrderId,
						ActorId: "test-actor-id",
					}, nil)

				// Mock successful GetFundTransferStatus call with OK status
				mockDeps.PayClient.EXPECT().
					GetFundTransferStatus(gomock.Any(), &payPb.GetFundTransferStatusRequest{
						Identifier: &payPb.GetFundTransferStatusRequest_ClientReqId{
							ClientReqId: &celestialPb.ClientReqId{
								Id:     testRefundClientRequestId,
								Client: workflowPb.Client_PAY,
							},
						},
					}).
					Return(&payPb.GetFundTransferStatusResponse{
						Status: rpc.StatusOk(),
					}, nil)

				// Mock successful GetOrder call for refund order
				mockDeps.OrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ClientReqId{
							ClientReqId: testRefundClientRequestId,
						},
					}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id: "test-refund-order-id",
						},
					}, nil)

				// Mock refund stage fetch for update
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), testRechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_REFUND).
					Return(&billpayPb.RechargeOrderStage{
						Id:              testRefundStageId,
						RechargeOrderId: testRechargeOrderId,
						Stage:           enums.RechargeStage_RECHARGE_STAGE_REFUND,
						Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
						Data:            &billpayPb.RechargeStageData{},
					}, nil)

				// Mock successful data update
				mockDeps.RechargeOrderStageDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderStageFieldMasks.Data(), gomock.Any()).
					Return(nil)
			},
			want: &billpayActPb.EnquireRechargeRefundOrderStatusResponse{
				RefundOrderId: "test-refund-order-id",
			},
			wantErr: false,
		},

		{
			name: "fund_transfer_in_progress_transient_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.EnquireRechargeRefundOrderStatusRequest{
					RequestHeader:         &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
					RefundClientRequestId: testRefundClientRequestId,
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testRechargeOrderClientRequestId).
					Return(&billpayPb.RechargeOrder{
						Id:      testRechargeOrderId,
						ActorId: "test-actor-id",
					}, nil)

				// Mock GetFundTransferStatus call with IN_PROGRESS status
				mockDeps.PayClient.EXPECT().
					GetFundTransferStatus(gomock.Any(), gomock.Any()).
					Return(&payPb.GetFundTransferStatusResponse{
						Status: &rpc.Status{Code: uint32(payPb.GetFundTransferStatusResponse_IN_PROGRESS)},
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "fund_transfer_failed_permanent_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.EnquireRechargeRefundOrderStatusRequest{
					RequestHeader:         &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
					RefundClientRequestId: testRefundClientRequestId,
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testRechargeOrderClientRequestId).
					Return(&billpayPb.RechargeOrder{
						Id:      testRechargeOrderId,
						ActorId: "test-actor-id",
					}, nil)

				// Mock GetFundTransferStatus call with FAILED status
				mockDeps.PayClient.EXPECT().
					GetFundTransferStatus(gomock.Any(), gomock.Any()).
					Return(&payPb.GetFundTransferStatusResponse{
						Status:       &rpc.Status{Code: uint32(payPb.GetFundTransferStatusResponse_FAILED)},
						PayErrorCode: "INSUFFICIENT_BALANCE",
						SubStatus:    payPb.GetFundTransferStatusResponse_TRANSACTION_NOT_ALLOWED,
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "missing_refund_client_request_id_permanent_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.EnquireRechargeRefundOrderStatusRequest{
					RequestHeader:         &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
					RefundClientRequestId: "",
				},
			},
			mockFunc: func() {
				// No mocks needed as validation happens before any DAO calls
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "missing_recharge_order_client_request_id_permanent_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.EnquireRechargeRefundOrderStatusRequest{
					RequestHeader:         &activityPb.RequestHeader{ClientReqId: ""},
					RefundClientRequestId: testRefundClientRequestId,
				},
			},
			mockFunc: func() {
				// No mocks needed as validation happens before any DAO calls
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "recharge_order_not_found_permanent_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.EnquireRechargeRefundOrderStatusRequest{
					RequestHeader:         &activityPb.RequestHeader{ClientReqId: "non-existent-recharge-order"},
					RefundClientRequestId: testRefundClientRequestId,
				},
			},
			mockFunc: func() {
				// Mock recharge order not found
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "non-existent-recharge-order").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},

		{
			name: "get_fund_transfer_status_rpc_error_transient",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.EnquireRechargeRefundOrderStatusRequest{
					RequestHeader:         &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
					RefundClientRequestId: testRefundClientRequestId,
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testRechargeOrderClientRequestId).
					Return(&billpayPb.RechargeOrder{
						Id:      testRechargeOrderId,
						ActorId: "test-actor-id",
					}, nil)

				// Mock RPC error
				mockDeps.PayClient.EXPECT().
					GetFundTransferStatus(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("rpc error"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "fund_transfer_not_found_permanent_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.EnquireRechargeRefundOrderStatusRequest{
					RequestHeader:         &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
					RefundClientRequestId: testRefundClientRequestId,
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testRechargeOrderClientRequestId).
					Return(&billpayPb.RechargeOrder{
						Id:      testRechargeOrderId,
						ActorId: "test-actor-id",
					}, nil)

				// Mock fund transfer not found
				mockDeps.PayClient.EXPECT().
					GetFundTransferStatus(gomock.Any(), gomock.Any()).
					Return(&payPb.GetFundTransferStatusResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "update_refund_stage_data_error_transient",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.EnquireRechargeRefundOrderStatusRequest{
					RequestHeader:         &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
					RefundClientRequestId: testRefundClientRequestId,
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testRechargeOrderClientRequestId).
					Return(&billpayPb.RechargeOrder{
						Id:      testRechargeOrderId,
						ActorId: "test-actor-id",
					}, nil)

				// Mock successful GetFundTransferStatus call with OK status
				mockDeps.PayClient.EXPECT().
					GetFundTransferStatus(gomock.Any(), gomock.Any()).
					Return(&payPb.GetFundTransferStatusResponse{
						Status: rpc.StatusOk(),
					}, nil)

				// Mock successful GetOrder call for refund order
				mockDeps.OrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ClientReqId{
							ClientReqId: testRefundClientRequestId,
						},
					}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id: "test-refund-order-id",
						},
					}, nil)

				// Mock refund stage fetch for update
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), testRechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_REFUND).
					Return(&billpayPb.RechargeOrderStage{
						Id:              testRefundStageId,
						RechargeOrderId: testRechargeOrderId,
						Stage:           enums.RechargeStage_RECHARGE_STAGE_REFUND,
						Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
						Data:            &billpayPb.RechargeStageData{},
					}, nil)

				// Mock failed data update
				mockDeps.RechargeOrderStageDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderStageFieldMasks.Data(), gomock.Any()).
					Return(errors.New("database error"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockFunc()

			val, err := env.ExecuteActivity(p.EnquireRechargeRefundOrderStatus, tt.args.req)

			// If we expect an error, validate it's the correct type
			if tt.wantErr {
				if tt.retryable && !epifitemporal.IsRetryableError(err) {
					t.Errorf("Expected retryable error for %s, got non-retryable error: %v", tt.name, err)
				}

				if !tt.retryable && epifitemporal.IsRetryableError(err) {
					t.Errorf("Expected non-retryable error, got retryable error: %v", err)
				}

				return
			}

			if err != nil {
				t.Errorf("EnquireRechargeRefundOrderStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			var got billpayActPb.EnquireRechargeRefundOrderStatusResponse
			if err := val.Get(&got); err != nil {
				t.Errorf("Failed to get activity result: %v", err)
				return
			}

			if diff := cmp.Diff(tt.want, &got, protocmp.Transform()); diff != "" {
				t.Errorf("EnquireRechargeRefundOrderStatus() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
