package activity_test

import (
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	epifiTemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	"github.com/epifi/be-common/pkg/idgen/mocks"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/billpay/test"

	orderMocks "github.com/epifi/gamma/api/order/mocks"
	payMocks "github.com/epifi/gamma/api/pay/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	rechargeVgMocks "github.com/epifi/gamma/api/vendorgateway/recharge/mocks"
	billpayActivity "github.com/epifi/gamma/billpay/activity"
	billpayDaoMocks "github.com/epifi/gamma/billpay/dao/mocks"
	payWorkerConf "github.com/epifi/gamma/pay/config/worker"
	payWorkerGenConf "github.com/epifi/gamma/pay/config/worker/genconf"
)

var (
	wts                epifitemporalTest.WorkflowTestSuite
	payWorkerConfig    *payWorkerConf.Config
	payWorkerGenConfig *payWorkerGenConf.Config
)

// MockDependencies groups all the mocked dependencies for easy access
type MockDependencies struct {
	RechargeOrderDao      *billpayDaoMocks.MockRechargeOrderDao
	RechargeOrderStageDao *billpayDaoMocks.MockRechargeOrderStageDao
	OrderClient           *orderMocks.MockOrderServiceClient
	RechargeClient        *rechargeVgMocks.MockMobileRechargeServiceClient
	UserClient            *userMocks.MockUsersClient
	PayClient             *payMocks.MockPayClient
	MockIdGen             *mocks.MockIdGenerator
}

func TestMain(m *testing.M) {
	var teardown func()
	payWorkerConfig, payWorkerGenConfig, teardown = test.InitTestWorker()

	// Initialize temporal test suite
	wts.SetLogger(epifiTemporalLogging.NewZapAdapter(logger.Log))

	// Run tests
	code := m.Run()
	teardown()
	os.Exit(code)
}

// getBillpayActivityProcessorWithMocks creates a billpay activity processor with mocked dependencies
func getBillpayActivityProcessorWithMocks(t *testing.T) (*billpayActivity.Processor, *MockDependencies, func()) {
	ctrl := gomock.NewController(t)

	// Create mocks
	mockRechargeOrderDao := billpayDaoMocks.NewMockRechargeOrderDao(ctrl)
	mockRechargeOrderStageDao := billpayDaoMocks.NewMockRechargeOrderStageDao(ctrl)
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctrl)
	mockRechargeClient := rechargeVgMocks.NewMockMobileRechargeServiceClient(ctrl)
	mockUserClient := userMocks.NewMockUsersClient(ctrl)
	mockPayClient := payMocks.NewMockPayClient(ctrl)
	mockIdGen := mocks.NewMockIdGenerator(ctrl)

	// Create processor with mocks
	processor := billpayActivity.NewProcessor(
		payWorkerGenConfig,
		mockRechargeOrderDao,
		mockRechargeOrderStageDao,
		mockOrderClient,
		mockRechargeClient,
		mockUserClient,
		mockPayClient,
		mockIdGen,
	)

	// Create mock dependencies struct for easy access
	mockDeps := &MockDependencies{
		RechargeOrderDao:      mockRechargeOrderDao,
		RechargeOrderStageDao: mockRechargeOrderStageDao,
		OrderClient:           mockOrderClient,
		RechargeClient:        mockRechargeClient,
		UserClient:            mockUserClient,
		PayClient:             mockPayClient,
		MockIdGen:             mockIdGen,
	}

	return processor, mockDeps, func() {
		ctrl.Finish()
	}
}
