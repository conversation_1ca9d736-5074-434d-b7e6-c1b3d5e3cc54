package activity

import (
	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	timelinePb "github.com/epifi/gamma/api/frontend/deeplink/timeline"
	txnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	orderPb "github.com/epifi/gamma/api/order"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	billPayScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/billpay"
	payScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay"
	"github.com/epifi/gamma/billpay/dao"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

// FetchRechargePaymentInitiationDetails activity fetches recharge order amount and generates payment options deeplink.
// This activity retrieves the plan amount from recharge order and constructs a payment options deeplink
// for initiating the payment process.
//
// Error handling:
// - Returns ErrPermanent for missing client request ID or if the order doesn't exist
// - Returns ErrTransient for database access errors or pay service failures
func (p *Processor) FetchRechargePaymentInitiationDetails(ctx context.Context, req *billpayActPb.FetchRechargePaymentInitiationDetailsRequest) (*billpayActPb.FetchRechargePaymentInitiationDetailsResponse, error) {
	lg := activity.GetLogger(ctx)
	if req.GetRequestHeader().GetClientReqId() == "" {
		lg.Error("Invalid request: missing client request ID")
		return nil, errors.Wrap(epifierrors.ErrPermanent, "client request ID is required")
	}
	clientRequestId := req.GetRequestHeader().GetClientReqId()

	rechargeOrder, err := p.rechargeOrderDao.GetByClientRequestId(ctx, dao.RechargeOrderFieldMasks.BasicInfo(), clientRequestId)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("Recharge order not found", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrPermanent, "recharge order not found")
		}
		lg.Error("Failed to fetch recharge order from database", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to fetch recharge order from database")
	}

	paymentAmount := rechargeOrder.GetPlanDetails().GetMobileRechargePlanDetails().GetAmount()
	if paymentAmount == nil {
		lg.Error("Transaction amount not found in plan details")
		return nil, errors.Wrap(epifierrors.ErrPermanent, "transaction amount not found in plan details")
	}

	paymentStage, err := p.rechargeOrderStageDao.GetByRechargeOrderIdAndStage(ctx, dao.RechargeOrderStageFieldMasks.ClientRequestId(), rechargeOrder.GetId(), enums.RechargeStage_RECHARGE_STAGE_PAYMENT)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("Recharge order stage not found", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrPermanent, "recharge order stage not found")
		}
		lg.Error("Failed to fetch recharge order stage from database", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to fetch recharge order stage from database")
	}

	paymentClientRequestId := paymentStage.GetClientRequestId()
	paymentOptionsDeeplink, err := p.getPaymentOptionsDeeplink(ctx, clientRequestId, paymentClientRequestId, paymentAmount)
	if err != nil {
		lg.Error("Failed to get payment options deeplink", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to get payment options deeplink")
	}

	response := &billpayActPb.FetchRechargePaymentInitiationDetailsResponse{
		PaymentStageClientRequestId: paymentClientRequestId,
		PaymentAmount:               paymentAmount,
		PaymentOptionsDeeplink:      paymentOptionsDeeplink,
	}

	lg.Debug("FetchPaymentInitiationDetails activity completed successfully")
	return response, nil
}

func (p *Processor) getPaymentOptionsDeeplink(ctx context.Context, clientRequestId, paymentClientRequestId string, paymentAmount *gmoney.Money) (*deepLinkPb.Deeplink, error) {
	// Construct workflow identification info
	workflowIdentificationInfo := &payPb.WorkflowIdentificationInfo{
		SignalId: string(payNs.RechargeFundTransferAuthSignal),
		ClientReqId: &workflow.ClientReqId{
			Id:     clientRequestId,
			Client: workflow.Client_PAY,
		},
	}

	// Marshal the client identification metadata for orchestration
	orchestrationMetadataBytes, err := proto.Marshal(&payPb.ClientIdentificationTxnMetaData{
		ClientReqId:     paymentClientRequestId,
		Workflow:        orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
		EntityOwnership: common.Ownership_EPIFI_TECH,
		DomainOrderData: &payPb.DomainOrderData{
			WorkflowIdentificationInfo: workflowIdentificationInfo,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal client identification metadata")
	}

	// Get signed data from pay service
	encryptRes, err := p.payClient.GetSignedData(ctx, &payPb.GetSignedDataRequest{
		Data: orchestrationMetadataBytes,
	})
	if te := epifigrpc.RPCError(encryptRes, err); te != nil {
		return nil, errors.Wrap(te, "error in getting signed data")
	}

	screenOptions := &payScreenOptions.PaymentOptionsScreenOptions{
		Header:                  &deeplink_screen_option.ScreenOptionHeader{},
		Amount:                  typesv2.GetFromBeMoney(paymentAmount),
		TransactionUiEntryPoint: timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_RECHARGE_PAYMENT,
		OrchestrationMetadata:   encryptRes.GetSignedData(),
		OrderedPaymentOptionTypes: []txnPb.PaymentOptionType{
			txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP,
		},
		ActorTo: p.gconf.BillpayParams().RechargePoolAccountActorId(),
		PiTo:    p.gconf.BillpayParams().RechargePoolAccountPiId(),
		PostPaymentDeeplink: deeplinkV3.GetDeeplinkV3WithoutError(deepLinkPb.Screen_RECHARGE_POLLING_SCREEN, &billPayScreenOptions.RechargePollingScreenOptions{
			ClientRequestId: clientRequestId,
			RetryIntervalMs: 500,
		}),
	}
	paymentOptionsDeeplink, err := deeplinkV3.GetDeeplinkV3(deepLinkPb.Screen_PAYMENT_OPTIONS_FULL_SCREEN, screenOptions)
	if err != nil {
		return nil, errors.Wrap(err, "failed to construct payment options deeplink")
	}

	return paymentOptionsDeeplink, nil
}
