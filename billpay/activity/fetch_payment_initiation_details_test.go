package activity_test

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/rpc/code"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/api/frontend/deeplink"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/billpay/dao"
)

func TestProcessor_FetchRechargePaymentInitiationDetails(t *testing.T) {
	p, mockDeps, assertTest := getBillpayActivityProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	var (
		testRechargeOrderClientRequestId = "test-recharge-order-client-req-id"
		testRechargeOrderId              = "test-recharge-order-id"
		testPaymentStageId               = "test-payment-stage-id"
		testPaymentClientRequestId       = "test-payment-client-req-id"
		testSignedData                   = []byte("test-signed-data")
	)

	testPaymentAmount := &gmoney.Money{
		CurrencyCode: "INR",
		Units:        100,
		Nanos:        0,
	}

	testRechargeOrder := &billpayPb.RechargeOrder{
		Id:              testRechargeOrderId,
		ClientRequestId: testRechargeOrderClientRequestId,
		ActorId:         "test-actor-id",
		PlanDetails: &billpayPb.PlanDetails{
			MobileRechargePlanDetails: &billpayPb.MobileRechargePlan{
				PlanName: "Test Plan ₹100",
				Amount:   testPaymentAmount,
			},
		},
		Status:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_INITIATED,
		SubStatus: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
	}

	testPaymentStage := &billpayPb.RechargeOrderStage{
		Id:              testPaymentStageId,
		RechargeOrderId: testRechargeOrderId,
		ClientRequestId: testPaymentClientRequestId,
		Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
		Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_INITIATED,
		Data:            &billpayPb.RechargeStageData{},
	}

	testPaymentOptionsDeeplink := &deeplink.Deeplink{
		Screen: deeplink.Screen_PAYMENT_OPTIONS_FULL_SCREEN,
	}

	type args struct {
		ctx context.Context
		req *billpayActPb.FetchRechargePaymentInitiationDetailsRequest
	}

	tests := []struct {
		name      string
		args      args
		mockFunc  func()
		want      *billpayActPb.FetchRechargePaymentInitiationDetailsResponse
		wantErr   bool
		retryable bool
	}{
		{
			name: "happy_flow_success",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.FetchRechargePaymentInitiationDetailsRequest{
					RequestHeader: &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testRechargeOrderClientRequestId).
					Return(testRechargeOrder, nil)

				// Mock successful payment stage fetch
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.ClientRequestId(), testRechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(testPaymentStage, nil)

				// Mock successful GetSignedData call
				mockDeps.PayClient.EXPECT().
					GetSignedData(gomock.Any(), gomock.Any()).
					Return(&payPb.GetSignedDataResponse{
						Status:     rpc.StatusOk(),
						SignedData: testSignedData,
					}, nil)
			},
			want: &billpayActPb.FetchRechargePaymentInitiationDetailsResponse{
				PaymentStageClientRequestId: testPaymentClientRequestId,
				PaymentAmount:               testPaymentAmount,
				PaymentOptionsDeeplink:      testPaymentOptionsDeeplink,
			},
			wantErr: false,
		},
		{
			name: "missing_client_request_id_permanent_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.FetchRechargePaymentInitiationDetailsRequest{
					RequestHeader: &activityPb.RequestHeader{ClientReqId: ""},
				},
			},
			mockFunc: func() {
				// No mocks needed as validation happens before any DAO calls
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "recharge_order_not_found_permanent_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.FetchRechargePaymentInitiationDetailsRequest{
					RequestHeader: &activityPb.RequestHeader{ClientReqId: "non-existent-recharge-order"},
				},
			},
			mockFunc: func() {
				// Mock recharge order not found
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "non-existent-recharge-order").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "recharge_order_dao_error_transient",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.FetchRechargePaymentInitiationDetailsRequest{
					RequestHeader: &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
				},
			},
			mockFunc: func() {
				// Mock database error
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testRechargeOrderClientRequestId).
					Return(nil, errors.New("database connection error"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "missing_payment_amount_permanent_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.FetchRechargePaymentInitiationDetailsRequest{
					RequestHeader: &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
				},
			},
			mockFunc: func() {
				rechargeOrderWithoutAmount := &billpayPb.RechargeOrder{
					Id:              testRechargeOrderId,
					ClientRequestId: testRechargeOrderClientRequestId,
					ActorId:         "test-actor-id",
					PlanDetails: &billpayPb.PlanDetails{
						MobileRechargePlanDetails: &billpayPb.MobileRechargePlan{
							PlanName: "Test Plan",
							Amount:   nil, // Missing amount
						},
					},
				}

				// Mock recharge order fetch with missing amount
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testRechargeOrderClientRequestId).
					Return(rechargeOrderWithoutAmount, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "payment_stage_not_found_permanent_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.FetchRechargePaymentInitiationDetailsRequest{
					RequestHeader: &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testRechargeOrderClientRequestId).
					Return(testRechargeOrder, nil)

				// Mock payment stage not found
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.ClientRequestId(), testRechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "payment_stage_dao_error_transient",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.FetchRechargePaymentInitiationDetailsRequest{
					RequestHeader: &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testRechargeOrderClientRequestId).
					Return(testRechargeOrder, nil)

				// Mock payment stage database error
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.ClientRequestId(), testRechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(nil, errors.New("database connection error"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "get_signed_data_rpc_error_transient",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.FetchRechargePaymentInitiationDetailsRequest{
					RequestHeader: &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testRechargeOrderClientRequestId).
					Return(testRechargeOrder, nil)

				// Mock successful payment stage fetch
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.ClientRequestId(), testRechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(testPaymentStage, nil)

				// Mock RPC error
				mockDeps.PayClient.EXPECT().
					GetSignedData(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("rpc error"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "get_signed_data_internal_error_transient",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.FetchRechargePaymentInitiationDetailsRequest{
					RequestHeader: &activityPb.RequestHeader{ClientReqId: testRechargeOrderClientRequestId},
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testRechargeOrderClientRequestId).
					Return(testRechargeOrder, nil)

				// Mock successful payment stage fetch
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.ClientRequestId(), testRechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(testPaymentStage, nil)

				// Mock GetSignedData call with error status
				mockDeps.PayClient.EXPECT().
					GetSignedData(gomock.Any(), gomock.Any()).
					Return(&payPb.GetSignedDataResponse{
						Status: &rpc.Status{
							Code:         uint32(code.Code_INTERNAL),
							ShortMessage: "internal error",
						},
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockFunc()

			val, err := env.ExecuteActivity(p.FetchRechargePaymentInitiationDetails, tt.args.req)

			// If we expect an error, validate it's the correct type
			if tt.wantErr {
				if err == nil {
					t.Errorf("Expected error for %s, but got none", tt.name)
					return
				}

				if tt.retryable && !epifitemporal.IsRetryableError(err) {
					t.Errorf("Expected retryable error for %s, got non-retryable error: %v", tt.name, err)
				}

				if !tt.retryable && epifitemporal.IsRetryableError(err) {
					t.Errorf("Expected non-retryable error for %s, got retryable error: %v", tt.name, err)
				}

				return
			}

			if err != nil {
				t.Errorf("FetchRechargePaymentInitiationDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			var got billpayActPb.FetchRechargePaymentInitiationDetailsResponse
			if err := val.Get(&got); err != nil {
				t.Errorf("Failed to get activity result: %v", err)
				return
			}

			// For successful cases, validate the response structure
			if tt.want != nil {
				if got.PaymentStageClientRequestId != tt.want.PaymentStageClientRequestId {
					t.Errorf("PaymentStageClientRequestId = %v, want %v", got.PaymentStageClientRequestId, tt.want.PaymentStageClientRequestId)
				}

				if diff := cmp.Diff(tt.want.PaymentAmount, got.PaymentAmount, protocmp.Transform()); diff != "" {
					t.Errorf("PaymentAmount mismatch (-want +got):\n%s", diff)
				}

				if got.PaymentOptionsDeeplink == nil {
					t.Errorf("PaymentOptionsDeeplink is nil, expected non-nil deeplink")
				}
			}
		})
	}
}
