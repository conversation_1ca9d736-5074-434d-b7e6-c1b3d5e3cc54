package activity_test

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	billpaypb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/billpay/dao"
)

func TestProcessor_GetRechargeOrderDetails(t *testing.T) {
	p, md, assertTest := getBillpayActivityProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	var (
		testClientRequestId = "test-client-request-id"
	)

	type args struct {
		ctx context.Context
		req *billpayActPb.GetRechargeOrderDetailsRequest
	}
	tests := []struct {
		name      string
		args      args
		mockFunc  func()
		want      *billpayActPb.GetRechargeOrderDetailsResponse
		wantErr   bool
		retryable bool
	}{
		{
			name: "happy_flow_success",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.GetRechargeOrderDetailsRequest{
					RequestHeader: &activity.RequestHeader{ClientReqId: testClientRequestId},
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				expectedOrder := &billpaypb.RechargeOrder{
					Id:              "test-recharge-order-id",
					ClientRequestId: testClientRequestId,
					ActorId:         "test-actor-id",
					AccountType:     enums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
					Status:          enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
				}
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.All(), testClientRequestId).
					Return(expectedOrder, nil)
			},
			want: &billpayActPb.GetRechargeOrderDetailsResponse{
				RechargeOrder: &billpaypb.RechargeOrder{
					Id:              "test-recharge-order-id",
					ClientRequestId: testClientRequestId,
					ActorId:         "test-actor-id",
					AccountType:     enums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
					Status:          enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
				},
			},
			wantErr:   false,
			retryable: false,
		},
		{
			name: "missing_client_request_id_permanent_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.GetRechargeOrderDetailsRequest{
					RequestHeader: &activity.RequestHeader{ClientReqId: ""},
				},
			},
			mockFunc: func() {
				// No mocks needed as validation happens before any DAO calls
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "recharge_order_not_found_permanent_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.GetRechargeOrderDetailsRequest{
					RequestHeader: &activity.RequestHeader{ClientReqId: "non-existent-client-request-id"},
				},
			},
			mockFunc: func() {
				// Mock recharge order not found
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.All(), "non-existent-client-request-id").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "database_error_transient_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.GetRechargeOrderDetailsRequest{
					RequestHeader: &activity.RequestHeader{ClientReqId: testClientRequestId},
				},
			},
			mockFunc: func() {
				// Mock database error
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.All(), testClientRequestId).
					Return(nil, errors.New("database connection failed"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "idempotent_multiple_calls_with_same_client_request_id",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.GetRechargeOrderDetailsRequest{
					RequestHeader: &activity.RequestHeader{ClientReqId: testClientRequestId},
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch (same as first test case to simulate idempotency)
				expectedOrder := &billpaypb.RechargeOrder{
					Id:              "test-recharge-order-id",
					ClientRequestId: testClientRequestId,
					ActorId:         "test-actor-id",
					AccountType:     enums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
					Status:          enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
				}
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.All(), testClientRequestId).
					Return(expectedOrder, nil)
			},
			want: &billpayActPb.GetRechargeOrderDetailsResponse{
				RechargeOrder: &billpaypb.RechargeOrder{
					Id:              "test-recharge-order-id",
					ClientRequestId: testClientRequestId,
					ActorId:         "test-actor-id",
					AccountType:     enums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
					Status:          enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
				},
			},
			wantErr:   false,
			retryable: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			tt.mockFunc()

			// Execute activity
			encoded, err := env.ExecuteActivity(p.GetRechargeOrderDetails, tt.args.req)

			// Check error expectation
			if tt.wantErr {
				if err == nil {
					t.Errorf("GetRechargeOrderDetails() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				// Check retryability
				if tt.retryable {
					if !epifitemporal.IsRetryableError(err) {
						t.Errorf("GetRechargeOrderDetails() expected retryable error, got %v", err)
					}
				} else {
					if epifitemporal.IsRetryableError(err) {
						t.Errorf("GetRechargeOrderDetails() expected non-retryable error, got %v", err)
					}
				}
				return
			}

			// Check success case
			if err != nil {
				t.Errorf("GetRechargeOrderDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Decode result
			var got billpayActPb.GetRechargeOrderDetailsResponse
			if err := encoded.Get(&got); err != nil {
				t.Errorf("Failed to decode result: %v", err)
				return
			}

			// Compare result
			if diff := cmp.Diff(tt.want, &got, protocmp.Transform()); diff != "" {
				t.Errorf("GetRechargeOrderDetails() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
