package activity

import (
	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/billpay/dao"
)

// GetRechargeOrderDetails activity fetches recharge order details by client request ID.
// This activity retrieves the complete recharge order information for the given client request ID.
//
// Error handling:
// - Returns ErrPermanent for missing client request ID or if the order doesn't exist
// - Returns ErrTransient for database access errors
func (p *Processor) GetRechargeOrderDetails(ctx context.Context, req *billpayActPb.GetRechargeOrderDetailsRequest) (*billpayActPb.GetRechargeOrderDetailsResponse, error) {
	lg := activity.GetLogger(ctx)

	// Get client request ID from request header
	clientRequestId := req.GetRequestHeader().GetClientReqId()
	if clientRequestId == "" {
		lg.Error("client request id is required in request header")
		return nil, errors.Wrap(epifierrors.ErrPermanent, "client request id is required in request header")
	}

	// Get recharge order by client request ID
	rechargeOrder, err := p.rechargeOrderDao.GetByClientRequestId(ctx, dao.RechargeOrderFieldMasks.All(), clientRequestId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		lg.Error("no such recharge order exists",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "recharge order not found")
	case err != nil:
		lg.Error("failed to fetch recharge order by client request id",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to fetch recharge order")
	}

	return &billpayActPb.GetRechargeOrderDetailsResponse{
		RechargeOrder: rechargeOrder,
	}, nil
}
