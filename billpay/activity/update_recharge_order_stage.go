package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	billpayEnums "github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/billpay/dao"
)

// UpdateRechargeOrderStage activity updates the status of a recharge order stage
func (p *Processor) UpdateRechargeOrderStage(ctx context.Context, req *billpayActPb.UpdateRechargeOrderStageRequest) (*billpayActPb.UpdateRechargeOrderStageResponse, error) {
	lg := activity.GetLogger(ctx)
	clientRequestId := req.GetRequestHeader().GetClientReqId()

	// Validate input parameters
	if req.GetStage() == billpayEnums.RechargeStage_RECHARGE_STAGE_UNSPECIFIED {
		lg.Error("stage cannot be unspecified",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "stage cannot be unspecified")
	}

	if req.GetStageStatusToUpdate() == billpayEnums.RechargeStageStatus_RECHARGE_STAGE_STATUS_UNSPECIFIED {
		lg.Error("stage status to update cannot be unspecified",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "stage status to update cannot be unspecified")
	}

	// Get recharge order by client request ID to validate it exists and get the recharge order ID
	rechargeOrder, err := p.rechargeOrderDao.GetByClientRequestId(ctx, dao.RechargeOrderFieldMasks.BasicInfo(), clientRequestId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		lg.Error("recharge order not found",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("recharge order not found for client_request_id: %s", clientRequestId))
	case err != nil:
		lg.Error("failed to get recharge order by client request id",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	// Add actor ID to context for better logging
	ctx = epificontext.CtxWithActorId(ctx, rechargeOrder.GetActorId())

	lg.Debug("recharge order found",
		zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
		zap.String(logger.ACTOR_ID_V2, rechargeOrder.GetActorId()),
		zap.String("recharge_order_id", rechargeOrder.GetId()))

	// Get the existing stage by recharge order ID and stage
	existingStage, err := p.rechargeOrderStageDao.GetByRechargeOrderIdAndStage(ctx, dao.RechargeOrderStageFieldMasks.BasicInfo(), rechargeOrder.GetId(), req.GetStage())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		lg.Error("recharge order stage not found",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.String(logger.ACTOR_ID_V2, rechargeOrder.GetActorId()),
			zap.String("recharge_order_id", rechargeOrder.GetId()),
			zap.String("stage", req.GetStage().String()),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("recharge order stage not found for recharge_order_id: %s and stage: %s", rechargeOrder.GetId(), req.GetStage().String()))
	case err != nil:
		lg.Error("failed to get recharge order stage",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.String(logger.ACTOR_ID_V2, rechargeOrder.GetActorId()),
			zap.String("recharge_order_id", rechargeOrder.GetId()),
			zap.String("stage", req.GetStage().String()),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	lg.Debug("existing recharge order stage found",
		zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
		zap.String(logger.ACTOR_ID_V2, rechargeOrder.GetActorId()),
		zap.String("recharge_order_stage_id", existingStage.GetId()),
		zap.String("current_status", existingStage.GetStatus().String()))

	// Check if the stage status is already the same (idempotent operation)
	if existingStage.GetStatus() == req.GetStageStatusToUpdate() {
		lg.Info("stage status is already the same, no update needed (idempotent operation)",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.String(logger.ACTOR_ID_V2, rechargeOrder.GetActorId()),
			zap.String("recharge_order_stage_id", existingStage.GetId()),
			zap.String("current_status", existingStage.GetStatus().String()),
			zap.String("requested_status", req.GetStageStatusToUpdate().String()))

		// Return the existing stage without any changes
		return &billpayActPb.UpdateRechargeOrderStageResponse{
			RechargeOrderStage: existingStage,
		}, nil
	}

	// Capture the old status before updating
	oldStatus := existingStage.GetStatus()

	// Update the stage status
	existingStage.Status = req.GetStageStatusToUpdate()

	err = p.rechargeOrderStageDao.Update(ctx, dao.RechargeOrderStageFieldMasks.Status(), existingStage)
	if err != nil {
		lg.Error("failed to update recharge order stage status",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.String(logger.ACTOR_ID_V2, rechargeOrder.GetActorId()),
			zap.String("recharge_order_stage_id", existingStage.GetId()),
			zap.String("new_status", req.GetStageStatusToUpdate().String()),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	lg.Debug("recharge order stage status updated successfully",
		zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
		zap.String(logger.ACTOR_ID_V2, rechargeOrder.GetActorId()),
		zap.String("recharge_order_stage_id", existingStage.GetId()),
		zap.String("old_status", oldStatus.String()),
		zap.String("new_status", req.GetStageStatusToUpdate().String()))

	return &billpayActPb.UpdateRechargeOrderStageResponse{
		RechargeOrderStage: existingStage,
	}, nil
}
