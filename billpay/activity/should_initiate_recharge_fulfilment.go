//nolint:dupl
package activity

import (
	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/billpay/dao"
)

// ShouldInitiateRechargeFulfilment activity checks if recharge fulfilment should be initiated.
// This activity gets the Payment stage from RechargeOrderStage table and checks if it's in SUCCESS state.
// Returns true if payment stage status is RECHARGE_STAGE_STATUS_SUCCESSFUL, false otherwise.
//
// Error handling:
// - Returns ErrPermanent if client request ID is missing or recharge order not found
// - Returns ErrTransient for database access errors when fetching order or payment stage
func (p *Processor) ShouldInitiateRechargeFulfilment(ctx context.Context, req *billpayActPb.ShouldInitiateRechargeFulfilmentRequest) (*billpayActPb.ShouldInitiateRechargeFulfilmentResponse, error) {
	lg := activity.GetLogger(ctx)
	clientRequestId := req.GetRequestHeader().GetClientReqId()

	// Validate input parameters
	if clientRequestId == "" {
		lg.Error("client request id is required")
		return nil, errors.Wrap(epifierrors.ErrPermanent, "client request id is required")
	}

	// Get recharge order by client request id to get the recharge order id
	rechargeOrder, err := p.rechargeOrderDao.GetByClientRequestId(ctx, dao.RechargeOrderFieldMasks.BasicInfo(), clientRequestId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		lg.Error("recharge order not found",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "recharge order not found")

	case err != nil:
		lg.Error("error while fetching recharge order",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error fetching recharge order")
	}

	rechargeOrderId := rechargeOrder.GetId()

	// Get the payment stage from RechargeOrderStage table
	paymentStage, err := p.rechargeOrderStageDao.GetByRechargeOrderIdAndStage(ctx, dao.RechargeOrderStageFieldMasks.All(), rechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT)
	if err != nil {
		lg.Error("error while fetching payment stage",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.String("recharge_order_id", rechargeOrderId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error fetching payment stage")
	}

	// Check if payment stage is in successful state
	shouldInitiateFulfilment := paymentStage.GetStatus() == enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL

	lg.Info("payment stage status checked",
		zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
		zap.String("recharge_order_id", rechargeOrderId),
		zap.String("payment_stage_id", paymentStage.GetId()),
		zap.String("payment_stage_status", paymentStage.GetStatus().String()),
		zap.Bool("should_initiate_fulfilment", shouldInitiateFulfilment))

	return &billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
		ShouldInitiateFulfilment: shouldInitiateFulfilment,
	}, nil
}
