//nolint:dupl
package activity

import (
	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/billpay/dao"
)

// ShouldInitiateRechargeRefundOrder activity checks if a recharge refund order should be initiated.
// This activity gets the Fulfilment stage from RechargeOrderStage table and checks if it's in FAILED state.
// Returns true if fulfilment stage status is RECHARGE_STAGE_STATUS_FAILED, false otherwise.
//
// Error handling:
// - Returns ErrPermanent if client request ID is missing or recharge order not found
// - Returns ErrTransient for database access errors when fetching order or fulfilment stage
func (p *Processor) ShouldInitiateRechargeRefundOrder(ctx context.Context, req *billpayActPb.ShouldInitiateRechargeRefundOrderRequest) (*billpayActPb.ShouldInitiateRechargeRefundOrderResponse, error) {
	lg := activity.GetLogger(ctx)
	clientRequestId := req.GetRequestHeader().GetClientReqId()

	// Validate input parameters
	if clientRequestId == "" {
		lg.Error("client request id is required")
		return nil, errors.Wrap(epifierrors.ErrPermanent, "client request id is required")
	}

	// Get recharge order by client request id to get the recharge order id
	rechargeOrder, err := p.rechargeOrderDao.GetByClientRequestId(ctx, dao.RechargeOrderFieldMasks.BasicInfo(), clientRequestId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		lg.Error("recharge order not found",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "recharge order not found")

	case err != nil:
		lg.Error("error while fetching recharge order",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error fetching recharge order")
	}

	rechargeOrderId := rechargeOrder.GetId()

	// Get the fulfilment stage from RechargeOrderStage table
	fulfilmentStage, err := p.rechargeOrderStageDao.GetByRechargeOrderIdAndStage(ctx, dao.RechargeOrderStageFieldMasks.All(), rechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT)
	if err != nil {
		lg.Error("error while fetching fulfilment stage",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.String("recharge_order_id", rechargeOrderId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error fetching fulfilment stage")
	}

	// Check if fulfilment stage is in failed state
	shouldInitiateRefund := fulfilmentStage.GetStatus() == enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED

	lg.Info("fulfilment stage status checked",
		zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
		zap.String("recharge_order_id", rechargeOrderId),
		zap.String("fulfilment_stage_id", fulfilmentStage.GetId()),
		zap.String("fulfilment_stage_status", fulfilmentStage.GetStatus().String()),
		zap.Bool("should_initiate_refund", shouldInitiateRefund))

	return &billpayActPb.ShouldInitiateRechargeRefundOrderResponse{
		ShouldInitiateRefund: shouldInitiateRefund,
	}, nil
}
