package activity_test

import (
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/billpay/dao"
)

func TestProcessor_EnquireRechargePaymentStatus(t *testing.T) {
	p, md, assertTest := getBillpayActivityProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	type args struct {
		req *billpayActPb.EnquireRechargePaymentStatusRequest
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(mockDeps *MockDependencies)
		want       *billpayActPb.EnquireRechargePaymentStatusResponse
		wantErr    bool
		retryable  bool
	}{
		{
			name: "happy_flow_payment_successful",
			args: args{
				req: &billpayActPb.EnquireRechargePaymentStatusRequest{
					PaymentStageClientRequestId: "test-payment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				testTimestamp := timestampPb.Now()

				// Mock GetOrder RPC call returning successful payment
				mockDeps.OrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "test-payment-client-req-id",
					},
				}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:        "test-order-id",
						Status:    orderPb.OrderStatus_PAID,
						CreatedAt: testTimestamp,
					},
				}, nil)

				// Mock GetOrderWithTransactions for payment mode extraction
				mockDeps.OrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "test-order-id",
				}).Return(&orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Transactions: []*payment.Transaction{
							{
								PaymentProtocol: payment.PaymentProtocol_UPI,
							},
						},
					},
				}, nil)

				// Mock getting stage by client request id
				existingStage := &billpayPb.RechargeOrderStage{
					Id:              "test-stage-id",
					ClientRequestId: "test-payment-client-req-id",
					Data:            nil, // No existing data
				}
				mockDeps.RechargeOrderStageDao.EXPECT().GetByClientRequestId(
					gomock.Any(),
					dao.RechargeOrderStageFieldMasks.BasicInfo(),
					"test-payment-client-req-id",
				).Return(existingStage, nil)

				// Mock updating stage with order id and payment mode
				mockDeps.RechargeOrderStageDao.EXPECT().Update(
					gomock.Any(),
					dao.RechargeOrderStageFieldMasks.Data(),
					gomock.Any(), // Use gomock.Any() for the whole struct since timestamp comparison is complex
				).Return(nil)
			},
			want: &billpayActPb.EnquireRechargePaymentStatusResponse{
				OrderId: "test-order-id",
			},
			wantErr:   false,
			retryable: false,
		},
		{
			name: "payment_already_updated_idempotent",
			args: args{
				req: &billpayActPb.EnquireRechargePaymentStatusRequest{
					PaymentStageClientRequestId: "test-payment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				testTimestamp := timestampPb.Now()

				// Mock GetOrder RPC call returning successful payment
				mockDeps.OrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "test-payment-client-req-id",
					},
				}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:        "test-order-id",
						Status:    orderPb.OrderStatus_PAID,
						CreatedAt: testTimestamp,
					},
				}, nil)

				// Mock GetOrderWithTransactions for payment mode extraction
				mockDeps.OrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "test-order-id",
				}).Return(&orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Transactions: []*payment.Transaction{
							{
								PaymentProtocol: payment.PaymentProtocol_UPI,
							},
						},
					},
				}, nil)

				// Mock getting stage by client request id with existing data
				existingStage := &billpayPb.RechargeOrderStage{
					Id:              "test-stage-id",
					ClientRequestId: "test-payment-client-req-id",
					Data: &billpayPb.RechargeStageData{
						Data: &billpayPb.RechargeStageData_PoolAccountPaymentDetails{
							PoolAccountPaymentDetails: &billpayPb.RechargePoolAccountPaymentStageData{
								OrderId:     "test-order-id",                    // Same order ID already exists
								PaymentMode: enums.PaymentMode_PAYMENT_MODE_UPI, // Same payment mode already exists
							},
						},
					},
				}
				mockDeps.RechargeOrderStageDao.EXPECT().GetByClientRequestId(
					gomock.Any(),
					dao.RechargeOrderStageFieldMasks.BasicInfo(),
					"test-payment-client-req-id",
				).Return(existingStage, nil)

				// No update call should be made due to idempotent behavior
			},
			want: &billpayActPb.EnquireRechargePaymentStatusResponse{
				OrderId: "test-order-id",
			},
			wantErr:   false,
			retryable: false,
		},
		{
			name: "payment_failed_permanent_error",
			args: args{
				req: &billpayActPb.EnquireRechargePaymentStatusRequest{
					PaymentStageClientRequestId: "test-payment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				testTimestamp := timestampPb.Now()

				// Mock GetOrder RPC call returning failed payment
				mockDeps.OrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "test-payment-client-req-id",
					},
				}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:        "test-order-id",
						Status:    orderPb.OrderStatus_PAYMENT_FAILED,
						CreatedAt: testTimestamp,
					},
				}, nil)

				// Mock GetOrderWithTransactions for payment mode extraction
				mockDeps.OrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "test-order-id",
				}).Return(&orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Transactions: []*payment.Transaction{
							{
								PaymentProtocol: payment.PaymentProtocol_CARD,
							},
						},
					},
				}, nil)

				// Mock getting stage by client request id
				existingStage := &billpayPb.RechargeOrderStage{
					Id:              "test-stage-id",
					ClientRequestId: "test-payment-client-req-id",
					Data:            nil,
				}
				mockDeps.RechargeOrderStageDao.EXPECT().GetByClientRequestId(
					gomock.Any(),
					dao.RechargeOrderStageFieldMasks.BasicInfo(),
					"test-payment-client-req-id",
				).Return(existingStage, nil)

				// Mock updating stage with order id even for failed payment
				mockDeps.RechargeOrderStageDao.EXPECT().Update(
					gomock.Any(),
					dao.RechargeOrderStageFieldMasks.Data(),
					gomock.Any(), // Use gomock.Any() for the whole struct since timestamp comparison is complex
				).Return(nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: false, // Permanent error
		},
		{
			name: "payment_in_progress_transient_error",
			args: args{
				req: &billpayActPb.EnquireRechargePaymentStatusRequest{
					PaymentStageClientRequestId: "test-payment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				testTimestamp := timestampPb.Now()

				// Mock GetOrder RPC call returning in-progress payment
				mockDeps.OrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "test-payment-client-req-id",
					},
				}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:        "test-order-id",
						Status:    orderPb.OrderStatus_IN_PAYMENT,
						CreatedAt: testTimestamp,
					},
				}, nil)

				// Mock GetOrderWithTransactions for payment mode extraction
				mockDeps.OrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "test-order-id",
				}).Return(&orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Transactions: []*payment.Transaction{
							{
								PaymentProtocol: payment.PaymentProtocol_IMPS,
							},
						},
					},
				}, nil)

				// Mock getting stage by client request id
				existingStage := &billpayPb.RechargeOrderStage{
					Id:              "test-stage-id",
					ClientRequestId: "test-payment-client-req-id",
					Data:            nil,
				}
				mockDeps.RechargeOrderStageDao.EXPECT().GetByClientRequestId(
					gomock.Any(),
					dao.RechargeOrderStageFieldMasks.BasicInfo(),
					"test-payment-client-req-id",
				).Return(existingStage, nil)

				// Mock updating stage with order id
				mockDeps.RechargeOrderStageDao.EXPECT().Update(
					gomock.Any(),
					dao.RechargeOrderStageFieldMasks.Data(),
					gomock.Any(), // Use gomock.Any() for the whole struct since timestamp comparison is complex
				).Return(nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error for retry
		},
		{
			name: "order_not_found_permanent_error",
			args: args{
				req: &billpayActPb.EnquireRechargePaymentStatusRequest{
					PaymentStageClientRequestId: "test-payment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock GetOrder RPC call returning record not found
				mockDeps.OrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "test-payment-client-req-id",
					},
				}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "get_order_rpc_failure_transient_error",
			args: args{
				req: &billpayActPb.EnquireRechargePaymentStatusRequest{
					PaymentStageClientRequestId: "test-payment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock GetOrder RPC call failing
				mockDeps.OrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "test-payment-client-req-id",
					},
				}).Return(nil, errors.New("RPC call failed"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error for retry
		},
		{
			name: "empty_payment_stage_client_request_id_permanent_error",
			args: args{
				req: &billpayActPb.EnquireRechargePaymentStatusRequest{
					PaymentStageClientRequestId: "", // Empty client request ID
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// No mocks needed for validation error
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "get_stage_by_client_request_id_fails_transient_error",
			args: args{
				req: &billpayActPb.EnquireRechargePaymentStatusRequest{
					PaymentStageClientRequestId: "test-payment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				testTimestamp := timestampPb.Now()

				// Mock GetOrder RPC call returning successful payment
				mockDeps.OrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "test-payment-client-req-id",
					},
				}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:        "test-order-id",
						Status:    orderPb.OrderStatus_PAID,
						CreatedAt: testTimestamp,
					},
				}, nil)

				// Mock getting stage by client request id failing
				mockDeps.RechargeOrderStageDao.EXPECT().GetByClientRequestId(
					gomock.Any(),
					dao.RechargeOrderStageFieldMasks.BasicInfo(),
					"test-payment-client-req-id",
				).Return(nil, errors.New("database error"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error for retry
		},
		{
			name: "update_stage_fails_transient_error",
			args: args{
				req: &billpayActPb.EnquireRechargePaymentStatusRequest{
					PaymentStageClientRequestId: "test-payment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				testTimestamp := timestampPb.Now()

				// Mock GetOrder RPC call returning successful payment
				mockDeps.OrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "test-payment-client-req-id",
					},
				}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:        "test-order-id",
						Status:    orderPb.OrderStatus_PAID,
						CreatedAt: testTimestamp,
					},
				}, nil)

				// Mock GetOrderWithTransactions for payment mode extraction
				mockDeps.OrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "test-order-id",
				}).Return(&orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Transactions: []*payment.Transaction{
							{
								PaymentProtocol: payment.PaymentProtocol_RTGS,
							},
						},
					},
				}, nil)

				// Mock getting stage by client request id
				existingStage := &billpayPb.RechargeOrderStage{
					Id:              "test-stage-id",
					ClientRequestId: "test-payment-client-req-id",
					Data:            nil,
				}
				mockDeps.RechargeOrderStageDao.EXPECT().GetByClientRequestId(
					gomock.Any(),
					dao.RechargeOrderStageFieldMasks.BasicInfo(),
					"test-payment-client-req-id",
				).Return(existingStage, nil)

				// Mock updating stage failing
				mockDeps.RechargeOrderStageDao.EXPECT().Update(
					gomock.Any(),
					dao.RechargeOrderStageFieldMasks.Data(),
					gomock.Any(), // Use gomock.Any() for the whole struct since timestamp comparison is complex
				).Return(errors.New("database update error"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error for retry
		},
		{
			name: "get_payment_mode_fails_transient_error",
			args: args{
				req: &billpayActPb.EnquireRechargePaymentStatusRequest{
					PaymentStageClientRequestId: "test-payment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				testTimestamp := timestampPb.Now()

				// Mock GetOrder RPC call returning successful payment
				mockDeps.OrderClient.EXPECT().GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_ClientReqId{
						ClientReqId: "test-payment-client-req-id",
					},
				}).Return(&orderPb.GetOrderResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:        "test-order-id",
						Status:    orderPb.OrderStatus_PAID,
						CreatedAt: testTimestamp,
					},
				}, nil)

				// Mock getting stage by client request id
				existingStage := &billpayPb.RechargeOrderStage{
					Id:              "test-stage-id",
					ClientRequestId: "test-payment-client-req-id",
					Data:            nil,
				}
				mockDeps.RechargeOrderStageDao.EXPECT().GetByClientRequestId(
					gomock.Any(),
					dao.RechargeOrderStageFieldMasks.BasicInfo(),
					"test-payment-client-req-id",
				).Return(existingStage, nil)

				// Mock GetOrderWithTransactions failing
				mockDeps.OrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "test-order-id",
				}).Return(nil, errors.New("RPC error"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error for retry
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks for this test case
			tt.setupMocks(md)

			// Execute the activity using temporal test environment
			encoded, err := env.ExecuteActivity(payNs.EnquireRechargePaymentStatus, tt.args.req)

			// Check error expectation
			if (err != nil) != tt.wantErr {
				t.Errorf("EnquireRechargePaymentStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// If we expect an error, validate it's the correct type
			if tt.wantErr {
				if tt.retryable && !epifitemporal.IsRetryableError(err) {
					t.Errorf("Expected retryable error for %s, got non-retryable error: %v", tt.name, err)
				}

				if !tt.retryable && epifitemporal.IsRetryableError(err) {
					t.Errorf("Expected non-retryable error, got retryable error: %v", err)
				}

				return
			}

			// Decode and compare successful responses
			var got billpayActPb.EnquireRechargePaymentStatusResponse
			err = encoded.Get(&got)
			if err != nil {
				t.Errorf("Failed to decode response: %v", err)
				return
			}

			// Compare responses using protocmp
			if diff := cmp.Diff(tt.want, &got, protocmp.Transform()); diff != "" {
				t.Errorf("EnquireRechargePaymentStatus() response mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
