package conversion

import (
	"fmt"

	"github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/api/order/payment"
)

var paymentProtocolToPaymentModeMap = map[payment.PaymentProtocol]enums.PaymentMode{
	payment.PaymentProtocol_INTRA_BANK: enums.PaymentMode_PAYMENT_MODE_INTERNET_BANKING,
	payment.PaymentProtocol_NEFT:       enums.PaymentMode_PAYMENT_MODE_INTERNET_BANKING,
	payment.PaymentProtocol_IMPS:       enums.PaymentMode_PAYMENT_MODE_INTERNET_BANKING,
	payment.PaymentProtocol_RTGS:       enums.PaymentMode_PAYMENT_MODE_INTERNET_BANKING,
	payment.PaymentProtocol_UPI:        enums.PaymentMode_PAYMENT_MODE_UPI,
	payment.PaymentProtocol_CARD:       enums.PaymentMode_PAYMENT_MODE_DEBIT_CARD,
}

func GetPaymentModeFromPaymentProtocol(protocol payment.PaymentProtocol) (enums.PaymentMode, error) {
	paymentMode, ok := paymentProtocolToPaymentModeMap[protocol]
	if !ok {
		return enums.PaymentMode_PAYMENT_MODE_UNSPECIFIED, fmt.Errorf("unsupported payment protocol: %s", protocol)
	}

	return paymentMode, nil
}
