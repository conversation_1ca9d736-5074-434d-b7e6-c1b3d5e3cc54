package deeplink

import (
	"fmt"

	"github.com/epifi/gamma/api/billpay/enums"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	billPayScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/billpay"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

type NextActionProcessor interface {
	GetRechargeNextAction(params *RechargeNextActionParams) (*deepLinkPb.Deeplink, error)
}

type NextActionProcessorImpl struct{}

type RechargeNextActionParams struct {
	ClientRequestId           string
	OrderStatus               enums.RechargeOrderStatus
	OrderSubStatus            enums.RechargeOrderSubStatus
	CurrStage                 enums.RechargeStage
	CurrStageStatus           enums.RechargeStageStatus
	PaymentOptionsDeeplink    *deepLinkPb.Deeplink // optional, generated from FetchRechargePaymentInitiationDetails activity
	PoolAccountPaymentOrderId string
}

func (n *NextActionProcessorImpl) GetRechargeNextAction(params *RechargeNextActionParams) (*deepLinkPb.Deeplink, error) {
	if params == nil {
		return nil, fmt.Errorf("params cannot be nil")
	}

	switch params.OrderStatus {
	case enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_INITIATED:
		// order being INITIATED means order entries are created but the workflow has not started yet.
		// we return RECHARGE_POLLING_SCREEN since workflow will get started in the background which updates the order status to IN_PROGRESS
		return n.getRechargePollingScreen(params), nil
	case enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS:
		return n.getInProgressNextAction(params)
	case enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
		enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_FAILED,
		enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_EXPIRED,
		enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION:
		return n.getRechargeTransactionReceiptScreen(params), nil
	default:
		return nil, fmt.Errorf("unsupported order status: %s", params.OrderStatus)
	}
}

// IN_PROGRESS means the workflow is running and getInProgressNextAction will return the next action based on the current stage of the workflow.
func (n *NextActionProcessorImpl) getInProgressNextAction(params *RechargeNextActionParams) (*deepLinkPb.Deeplink, error) {
	switch params.CurrStage {
	case enums.RechargeStage_RECHARGE_STAGE_UNSPECIFIED:
		// Stage can be UNSPECIFIED when the workflow is started but the first stage is not yet started
		// we return RECHARGE_POLLING_SCREEN since the workflow will create the stage
		return n.getRechargePollingScreen(params), nil
	case enums.RechargeStage_RECHARGE_STAGE_PAYMENT:
		switch params.CurrStageStatus {
		case enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_INITIATED:
			// Stage is INITIATED means the payment is initiated, and the workflow is waiting for the user to authenticate the payment
			// we land user on PaymentsOptions screen which allows user to continue to the payment authentication
			if params.PaymentOptionsDeeplink != nil {
				return params.PaymentOptionsDeeplink, nil
			} else {
				return n.getRechargePollingScreen(params), nil
			}
		case enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
			enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
			enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED,
			enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION,
			enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_EXPIRED:
			// after user has authenticated the payment,
			// we land user on Summary Screen which tells the status of the payment
			return n.getRechargeTransactionReceiptScreen(params), nil
		default:
			return nil, fmt.Errorf("unsupported stage status: %s", params.CurrStageStatus)
		}
	case enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT, enums.RechargeStage_RECHARGE_STAGE_REFUND:
		return n.getRechargeTransactionReceiptScreen(params), nil
	default:
		return nil, fmt.Errorf("unsupported stage: %s", params.CurrStage)
	}
}

func (n *NextActionProcessorImpl) getRechargePollingScreen(params *RechargeNextActionParams) *deepLinkPb.Deeplink {
	return deeplinkV3.GetDeeplinkV3WithoutError(deepLinkPb.Screen_RECHARGE_POLLING_SCREEN, &billPayScreenOptions.RechargePollingScreenOptions{
		ClientRequestId: params.ClientRequestId,
		RetryIntervalMs: 500,
	})
}

func (n *NextActionProcessorImpl) getRechargeTransactionReceiptScreen(params *RechargeNextActionParams) *deepLinkPb.Deeplink {
	// add logic based on stage and stage status to determine the States in the Summary screen
	return &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_TRANSACTION_RECEIPT,
		ScreenOptions: &deepLinkPb.Deeplink_TransactionReceiptScreenOptions{
			TransactionReceiptScreenOptions: &deepLinkPb.TransactionReceiptScreenOptions{
				OrderId:    params.PoolAccountPaymentOrderId,
				Identifier: &deepLinkPb.TransactionReceiptScreenOptions_OrdersId{OrdersId: params.PoolAccountPaymentOrderId},
			},
		},
	}
}
