package billpay_test

import (
	"os"
	"testing"

	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cfg"

	"github.com/epifi/gamma/billpay/config/genconf"
	"github.com/epifi/gamma/billpay/test"
)

var (
	db     *gorm.DB
	dbConf *cfg.DB
	gconf  *genconf.Config
)

var (
	testActorID           = "test-actor-id"
	testClientRequestID   = "test-client-request-id"
	testAccountIdentifier = "**********"
)

// TestMain initializes test components, runs tests and exits
func TestMain(m *testing.M) {
	var teardown func()
	gconf, dbConf, db, teardown = test.InitTestServer(false) // No DB required for service unit tests

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
