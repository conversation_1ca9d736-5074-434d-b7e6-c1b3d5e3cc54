package dao_test

import (
	"context"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/pagination"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	billpaypb "github.com/epifi/gamma/api/billpay"
	"github.com/epifi/gamma/billpay/dao"
)

// Helper function to set up a complete payment chain for testing disputes
func setupPaymentChainForDispute(t *testing.T) (*billpaypb.Payment, string) {
	a := require.New(t)

	// Create category
	category := &billpaypb.Category{
		Id:           uuid.New().String(),
		CategoryName: "Test Category",
		BillerCount:  0,
	}
	createdCategory, err := categoryDao.Create(context.Background(), category)
	a.NoError(err, "Failed to create category")

	// Create biller
	biller := &billpaypb.Biller{
		Id:               uuid.New().String(),
		CategoryId:       createdCategory.GetId(),
		ExternalBillerId: "ext-" + uuid.New().String(),
		BillerName:       "Test Biller",
		CustomerParams:   &billpaypb.Params{},
		Status:           billpaypb.BillerStatus_BILLER_STATUS_ACTIVE,
		Details:          &billpaypb.BillerDetails{},
	}
	createdBiller, err := billerDao.Create(context.Background(), biller)
	a.NoError(err, "Failed to create biller")

	// Create actor biller account
	actorId := uuid.New().String()
	account := &billpaypb.ActorBillerAccount{
		ActorId:        actorId,
		BillerId:       createdBiller.GetId(),
		CustomerParams: &billpaypb.ParamValues{},
		Nickname:       "Test Account",
	}
	createdAccount, err := actorBillerAccountDao.Create(context.Background(), account)
	a.NoError(err, "Failed to create actor biller account")

	// Create bill fetch request
	request := &billpaypb.BillFetchRequest{
		ActorId:              actorId,
		ActorBillerAccountId: createdAccount.GetId(),
		VendorRefId:          "vendor-" + uuid.New().String(),
		Details:              &billpaypb.BillFetchDetails{},
	}
	createdRequest, err := billFetchRequestDao.Create(context.Background(), request)
	a.NoError(err, "Failed to create bill fetch request")

	// Create bill
	bill := &billpaypb.Bill{
		BillFetchRequestId: createdRequest.GetId(),
		ActorId:            actorId,
		Status:             billpaypb.BillStatus_BILL_STATUS_GENERATED,
		Details:            &billpaypb.BillDetails{},
		DueDate:            timestamppb.New(time.Now().Add(24 * time.Hour)),
	}
	createdBill, err := billDao.Create(context.Background(), bill)
	a.NoError(err, "Failed to create bill")

	// Create payment
	payment := &billpaypb.Payment{
		BillsRefId:  createdBill.GetId(),
		ActorId:     actorId,
		BillerId:    createdBiller.GetId(),
		RequestId:   "req-" + uuid.New().String(),
		VendorRefId: "vendor-" + uuid.New().String(),
		Details:     &billpaypb.PaymentDetails{},
	}
	createdPayment, err := paymentDao.Create(context.Background(), payment)
	a.NoError(err, "Failed to create payment")

	return createdPayment, actorId
}

// Helper function to create a new dispute
func newDispute(actorId, paymentId string) *billpaypb.Dispute {
	return &billpaypb.Dispute{
		Id:                    uuid.New().String(),
		ActorId:               actorId,
		PaymentId:             paymentId,
		VendorRefId:           "vendor-" + uuid.New().String(),
		ExternalDisputeId:     "ext-dispute-" + uuid.New().String(),
		ExternalTransactionId: "ext-tx-" + uuid.New().String(),
		DisputeType:           "PAYMENT_NOT_CREDITED",
		Status:                billpaypb.DisputeStatus_DISPUTE_STATUS_ASSIGNED_TO_BOU,
		UserDescription:       "Payment not credited to biller",
		Remarks:               "",
	}
}

func TestDisputeImpl_Create(t *testing.T) {
	t.Skip()
	pkgTest.TruncateTestDatabase(t, db, dbConf.GetName())

	// Setup payment chain
	payment, actorId := setupPaymentChainForDispute(t)

	type args struct {
		dispute *billpaypb.Dispute
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "valid dispute",
			args: args{
				dispute: newDispute(actorId, payment.GetId()),
			},
			wantErr: false,
		},
		{
			name: "invalid payment id",
			args: args{
				dispute: newDispute(actorId, uuid.New().String()),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := disputeDao.Create(context.Background(), tt.args.dispute)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestDisputeImpl_Update(t *testing.T) {
	t.Skip()
	pkgTest.TruncateTestDatabase(t, db, dbConf.GetName())
	a := require.New(t)

	// Setup payment chain
	payment, actorId := setupPaymentChainForDispute(t)

	// Create a dispute
	dispute := newDispute(actorId, payment.GetId())
	createdDispute, err := disputeDao.Create(context.Background(), dispute)
	a.NoError(err, "Failed to create dispute")

	// Update fields
	fm := &fieldmaskpb.FieldMask{Paths: []string{"status", "remarks"}}
	createdDispute.Status = billpaypb.DisputeStatus_DISPUTE_STATUS_RESOLVED
	createdDispute.Remarks = "Resolved by customer service"

	err = disputeDao.Update(context.Background(), fm, createdDispute)
	a.NoError(err, "Failed to update dispute")

	// Get the updated dispute to verify
	updatedDispute, err := disputeDao.GetById(context.Background(), dao.DisputeFieldMasks.All(), createdDispute.GetId())
	a.NoError(err, "Failed to get dispute by id")

	// Check if the fields were updated correctly
	a.Equal(billpaypb.DisputeStatus_DISPUTE_STATUS_RESOLVED, updatedDispute.GetStatus(), "status was not updated correctly")
	a.Equal("Resolved by customer service", updatedDispute.GetRemarks(), "remarks was not updated correctly")
}

func TestDisputeImpl_GetById(t *testing.T) {
	t.Skip()
	pkgTest.TruncateTestDatabase(t, db, dbConf.GetName())
	a := require.New(t)

	// Setup payment chain
	payment, actorId := setupPaymentChainForDispute(t)

	// Create a dispute
	dispute := newDispute(actorId, payment.GetId())
	createdDispute, err := disputeDao.Create(context.Background(), dispute)
	a.NoError(err, "Failed to create dispute")

	type args struct {
		fieldMask *fieldmaskpb.FieldMask
		id        string
	}
	tests := []struct {
		name    string
		args    args
		want    *billpaypb.Dispute
		wantErr bool
	}{
		{
			name: "valid dispute id",
			args: args{
				fieldMask: dao.DisputeFieldMasks.All(),
				id:        createdDispute.GetId(),
			},
			want:    createdDispute,
			wantErr: false,
		},
		{
			name: "invalid dispute id",
			args: args{
				fieldMask: dao.DisputeFieldMasks.All(),
				id:        "non-existent-id",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "get dispute with basic info field mask",
			args: args{
				fieldMask: dao.DisputeFieldMasks.BasicInfo(),
				id:        createdDispute.GetId(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := disputeDao.GetById(context.Background(), tt.args.fieldMask, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.name == "get dispute with basic info field mask" {
				a.NotNil(got, "expected to get a dispute")
				a.Equal(createdDispute.GetId(), got.GetId(), "expected to get the correct dispute id")
				a.Equal(createdDispute.GetActorId(), got.GetActorId(), "expected to get the correct actor id")
				a.Equal(createdDispute.GetPaymentId(), got.GetPaymentId(), "expected to get the correct payment id")
				a.Equal(createdDispute.GetStatus(), got.GetStatus(), "expected to get the correct status")
				a.Equal(createdDispute.GetDisputeType(), got.GetDisputeType(), "expected to get the correct dispute type")
			} else if !tt.wantErr && tt.want != nil {
				if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
					t.Errorf("Disputes are unequal - got = %v, want %v \n diff: %v", got, tt.want, diff)
				}
			}
		})
	}
}

func TestDisputeImpl_GetByActorId(t *testing.T) {
	t.Skip()
	pkgTest.TruncateTestDatabase(t, db, dbConf.GetName())
	a := require.New(t)

	// Setup payment chain
	payment, actorId := setupPaymentChainForDispute(t)

	// Create another payment for a different actor
	payment2, actorId2 := setupPaymentChainForDispute(t)

	// Create multiple disputes for the same actor
	dispute1 := newDispute(actorId, payment.GetId())
	dispute1.Status = billpaypb.DisputeStatus_DISPUTE_STATUS_ASSIGNED_TO_BOU
	createdDispute1, err := disputeDao.Create(context.Background(), dispute1)
	a.NoError(err, "Failed to create dispute 1")

	// Wait a moment to ensure different creation timestamps
	time.Sleep(10 * time.Millisecond)

	dispute2 := newDispute(actorId, payment.GetId())
	dispute2.Status = billpaypb.DisputeStatus_DISPUTE_STATUS_ASSIGNED_TO_BOU
	createdDispute2, err := disputeDao.Create(context.Background(), dispute2)
	a.NoError(err, "Failed to create dispute 2")

	// Create a dispute for a different actor
	dispute3 := newDispute(actorId2, payment2.GetId())
	createdDispute3, err := disputeDao.Create(context.Background(), dispute3)
	a.NoError(err, "Failed to create dispute 3")

	type args struct {
		fieldMask *fieldmaskpb.FieldMask
		actorId   string
		pageToken *pagination.PageToken
		pageSize  uint32
	}
	tests := []struct {
		name    string
		args    args
		want    []*billpaypb.Dispute
		wantErr bool
	}{
		{
			name: "get disputes for actor 1",
			args: args{
				fieldMask: dao.DisputeFieldMasks.All(),
				actorId:   actorId,
				pageToken: nil,
				pageSize:  10,
			},
			want:    []*billpaypb.Dispute{createdDispute2, createdDispute1},
			wantErr: false,
		},
		{
			name: "get disputes for actor 2",
			args: args{
				fieldMask: dao.DisputeFieldMasks.All(),
				actorId:   actorId2,
				pageToken: nil,
				pageSize:  10,
			},
			want:    []*billpaypb.Dispute{createdDispute3},
			wantErr: false,
		},
		{
			name: "get disputes with pagination",
			args: args{
				fieldMask: dao.DisputeFieldMasks.All(),
				actorId:   actorId,
				pageToken: nil,
				pageSize:  1,
			},
			want:    []*billpaypb.Dispute{createdDispute2},
			wantErr: false,
		},
		{
			name: "get disputes for non-existent actor",
			args: args{
				fieldMask: dao.DisputeFieldMasks.All(),
				actorId:   "non-existent-actor",
				pageToken: nil,
				pageSize:  10,
			},
			want:    []*billpaypb.Dispute{},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, _, err := disputeDao.GetByActorId(context.Background(), tt.args.fieldMask, tt.args.actorId, tt.args.pageToken, tt.args.pageSize)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
					t.Errorf("Disputes are unequal - got = %v, want %v \n diff: %v", got, tt.want, diff)
				}
			}
		})
	}

	// Test pagination with page token
	t.Run("test pagination with page token", func(t *testing.T) {
		// First get the first page
		firstPage, pageCtx, err := disputeDao.GetByActorId(context.Background(), dao.DisputeFieldMasks.All(), actorId, nil, 1)
		a.NoError(err, "couldn't get first page")
		a.Len(firstPage, 1, "expected 1 dispute in first page")
		a.Equal(createdDispute2.GetId(), firstPage[0].GetId(), "expected dispute2 in first page")

		pageToken := &pagination.PageToken{}
		err = pageToken.Unmarshal(pageCtx.GetAfterToken())
		a.NoError(err, "couldn't unmarshal page token")

		// Then get the second page using the page token
		secondPage, _, err := disputeDao.GetByActorId(context.Background(), dao.DisputeFieldMasks.All(), actorId, pageToken, 1)
		a.NoError(err, "couldn't get second page")
		a.Len(secondPage, 1, "expected 1 dispute in second page")
		a.Equal(createdDispute1.GetId(), secondPage[0].GetId(), "expected dispute1 in second page")
	})
}

func TestDisputeImpl_GetByPaymentId(t *testing.T) {
	t.Skip()
	pkgTest.TruncateTestDatabase(t, db, dbConf.GetName())
	a := require.New(t)

	// Setup payment chain
	payment, actorId := setupPaymentChainForDispute(t)

	// Create another payment
	payment2, actorId2 := setupPaymentChainForDispute(t)

	// Create multiple disputes for the same payment
	dispute1 := newDispute(actorId, payment.GetId())
	dispute1.DisputeType = "PAYMENT_NOT_CREDITED"
	createdDispute1, err := disputeDao.Create(context.Background(), dispute1)
	a.NoError(err, "Failed to create dispute 1")

	dispute2 := newDispute(actorId, payment.GetId())
	dispute2.DisputeType = "WRONG_AMOUNT"
	createdDispute2, err := disputeDao.Create(context.Background(), dispute2)
	a.NoError(err, "Failed to create dispute 2")

	// Create a dispute for a different payment
	dispute3 := newDispute(actorId2, payment2.GetId())
	createdDispute3, err := disputeDao.Create(context.Background(), dispute3)
	a.NoError(err, "Failed to create dispute 3")

	type args struct {
		fieldMask *fieldmaskpb.FieldMask
		paymentId string
	}
	tests := []struct {
		name    string
		args    args
		want    []*billpaypb.Dispute
		wantErr bool
	}{
		{
			name: "get disputes for payment 1",
			args: args{
				fieldMask: dao.DisputeFieldMasks.All(),
				paymentId: payment.GetId(),
			},
			want:    []*billpaypb.Dispute{createdDispute1, createdDispute2},
			wantErr: false,
		},
		{
			name: "get disputes for payment 2",
			args: args{
				fieldMask: dao.DisputeFieldMasks.All(),
				paymentId: payment2.GetId(),
			},
			want:    []*billpaypb.Dispute{createdDispute3},
			wantErr: false,
		},
		{
			name: "get disputes for non-existent payment",
			args: args{
				fieldMask: dao.DisputeFieldMasks.All(),
				paymentId: uuid.New().String(),
			},
			want:    []*billpaypb.Dispute{},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := disputeDao.GetByPaymentId(context.Background(), tt.args.fieldMask, tt.args.paymentId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByPaymentId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
					t.Errorf("Disputes are unequal - got = %v, want %v \n diff: %v", got, tt.want, diff)
				}
			}
		})
	}
}

func TestDisputeImpl_GetByStatus(t *testing.T) {
	t.Skip()
	pkgTest.TruncateTestDatabase(t, db, dbConf.GetName())
	a := require.New(t)

	// Setup payment chain
	payment, actorId := setupPaymentChainForDispute(t)

	// Create disputes with different statuses
	dispute1 := newDispute(actorId, payment.GetId())
	dispute1.Status = billpaypb.DisputeStatus_DISPUTE_STATUS_ASSIGNED_TO_BOU
	createdDispute1, err := disputeDao.Create(context.Background(), dispute1)
	a.NoError(err, "Failed to create dispute 1")

	// Wait a moment to ensure different creation timestamps
	time.Sleep(10 * time.Millisecond)

	dispute2 := newDispute(actorId, payment.GetId())
	dispute2.Status = billpaypb.DisputeStatus_DISPUTE_STATUS_ASSIGNED_TO_BOU
	createdDispute2, err := disputeDao.Create(context.Background(), dispute2)
	a.NoError(err, "Failed to create dispute 2")

	// Wait a moment to ensure different creation timestamps
	time.Sleep(10 * time.Millisecond)

	dispute3 := newDispute(actorId, payment.GetId())
	dispute3.Status = billpaypb.DisputeStatus_DISPUTE_STATUS_RESOLVED
	createdDispute3, err := disputeDao.Create(context.Background(), dispute3)
	a.NoError(err, "Failed to create dispute 3")

	type args struct {
		fieldMask *fieldmaskpb.FieldMask
		status    billpaypb.DisputeStatus
		pageToken *pagination.PageToken
		pageSize  uint32
	}
	tests := []struct {
		name    string
		args    args
		want    []*billpaypb.Dispute
		wantErr bool
	}{
		{
			name: "get pending disputes",
			args: args{
				fieldMask: dao.DisputeFieldMasks.All(),
				status:    billpaypb.DisputeStatus_DISPUTE_STATUS_ASSIGNED_TO_BOU,
				pageToken: nil,
				pageSize:  10,
			},
			want:    []*billpaypb.Dispute{createdDispute2, createdDispute1},
			wantErr: false,
		},
		{
			name: "get resolved disputes",
			args: args{
				fieldMask: dao.DisputeFieldMasks.All(),
				status:    billpaypb.DisputeStatus_DISPUTE_STATUS_RESOLVED,
				pageToken: nil,
				pageSize:  10,
			},
			want:    []*billpaypb.Dispute{createdDispute3},
			wantErr: false,
		},
		{
			name: "get disputes with non-existent status",
			args: args{
				fieldMask: dao.DisputeFieldMasks.All(),
				status:    billpaypb.DisputeStatus_DISPUTE_STATUS_UNRESOLVED,
				pageToken: nil,
				pageSize:  10,
			},
			want:    []*billpaypb.Dispute{},
			wantErr: false,
		},
		{
			name: "get pending disputes with pagination",
			args: args{
				fieldMask: dao.DisputeFieldMasks.All(),
				status:    billpaypb.DisputeStatus_DISPUTE_STATUS_ASSIGNED_TO_BOU,
				pageToken: nil,
				pageSize:  1,
			},
			want:    []*billpaypb.Dispute{createdDispute2},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, _, err := disputeDao.GetByStatus(context.Background(), tt.args.fieldMask, tt.args.status, tt.args.pageToken, tt.args.pageSize)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
					t.Errorf("Disputes are unequal - got = %v, want %v \n diff: %v", got, tt.want, diff)
				}
			}
		})
	}

	// Test pagination with page token
	t.Run("test pagination with page token", func(t *testing.T) {
		// First get the first page
		firstPage, pageCtx, err := disputeDao.GetByStatus(context.Background(), dao.DisputeFieldMasks.All(), billpaypb.DisputeStatus_DISPUTE_STATUS_ASSIGNED_TO_BOU, nil, 1)
		a.NoError(err, "couldn't get first page")
		a.Len(firstPage, 1, "expected 1 dispute in first page")
		a.Equal(createdDispute2.GetId(), firstPage[0].GetId(), "expected dispute2 in first page")

		pageToken := &pagination.PageToken{}
		err = pageToken.Unmarshal(pageCtx.GetAfterToken())
		a.NoError(err, "couldn't unmarshal page token")

		// Then get the second page using the page token
		secondPage, _, err := disputeDao.GetByStatus(context.Background(), dao.DisputeFieldMasks.All(), billpaypb.DisputeStatus_DISPUTE_STATUS_ASSIGNED_TO_BOU, pageToken, 1)
		a.NoError(err, "couldn't get second page")
		a.Len(secondPage, 1, "expected 1 dispute in second page")
		a.Equal(createdDispute1.GetId(), secondPage[0].GetId(), "expected dispute1 in second page")
	})
}
