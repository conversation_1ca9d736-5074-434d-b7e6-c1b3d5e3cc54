package test

import (
	"log"

	"go.uber.org/zap"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cfg/dynconf"
	cmdconfig "github.com/epifi/be-common/pkg/cmd/config"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/billpay/config"
	"github.com/epifi/gamma/billpay/config/genconf"
	payWorkerConfig "github.com/epifi/gamma/pay/config/worker"
	payWorkerGenConf "github.com/epifi/gamma/pay/config/worker/genconf"
)

// InitTestServer initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
func InitTestServer(isDbReq bool) (*genconf.Config, *cfg.DB, *gormv2.DB, func()) {
	conf, dbConf, db, closeFn := initTestServer(isDbReq)
	return conf, dbConf, db, closeFn
}

func initTestServer(isDbReq bool) (*genconf.Config, *cfg.DB, *gormv2.DB, func()) {

	serverConf, err := cmdconfig.Load(cfg.ORDER_SERVER)
	if err != nil {
		log.Fatal("failed to load server genconf", err)
	}
	// Setup logger
	logger.Init(serverConf.Environment)
	conf, err := dynconf.LoadConfig(config.Load, genconf.NewConfig, cfg.BILL_PAY_SERVICE)
	if err != nil {
		log.Fatal("failed to load genconf", err)
	}
	var db *gormv2.DB
	var dbConf *cfg.DB
	if isDbReq {
		var closeFn func()
		dbConf = serverConf.Databases["BillpayPGDB"]
		db, _, closeFn, err = pkgTest.PrepareRandomScopedRdsTestDb(dbConf, false)
		if err != nil {
			log.Fatalf("error making connection to test rds database %v", err)
		}
		return conf, dbConf, db, func() {
			closeFn()
			_ = logger.Log.Sync()
		}
	}
	return conf, dbConf, db, func() {
		_ = logger.Log.Sync()
	}
}

// InitTestWorker initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
// nolint: dupl
func InitTestWorker() (*payWorkerConfig.Config, *payWorkerGenConf.Config, func()) {
	var err error
	// Init config
	conf, err := payWorkerConfig.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}
	gconf, _ := payWorkerGenConf.NewConfig()

	// Setup logger
	logger.Init(conf.Application.Environment)
	err = epifitemporal.InitWorkflowParams(conf.WorkflowParamsList.GetWorkflowParamsMap())
	if err != nil {
		logger.Panic("failed to load workflow params", zap.Error(err))
	}

	err = epifitemporal.InitDefaultActivityParams(conf.DefaultActivityParamsList.GetActivityParamsMap())
	if err != nil {
		logger.Panic("failed to load default activity params", zap.Error(err))
	}

	return conf, gconf, func() {
		_ = logger.Log.Sync()
	}
}
