package workflow

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	"github.com/epifi/be-common/pkg/proto/json"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	billpayWfPb "github.com/epifi/gamma/api/billpay/workflow"
	"github.com/epifi/gamma/api/pay/signal"
	billpayDeeplink "github.com/epifi/gamma/billpay/deeplink"
	billpayUtils "github.com/epifi/gamma/billpay/utils"
)

var (
	nextActionProcessor = &billpayDeeplink.NextActionProcessorImpl{}
)

// RechargePayment executes a three-stage recharge payment workflow for mobile/DTH transactions.
// The workflow handles Payment, Fulfillment, and conditional Refund stages with proper error handling.
//
// Stages:
// 1. RECHARGE_STAGE_PAYMENT - Waits for auth signal and validates payment status
// 2. RECHARGE_STAGE_FULFILLMENT - Initiates recharge with vendor and checks completion
// 3. RECHARGE_STAGE_REFUND - Processes refund if fulfillment fails (conditional)
//
// The workflow uses signals for user authentication and implements comprehensive error handling:
// - Transient errors result in MANUAL_INTERVENTION status
// - Permanent errors result in FAILED status
// - Timeouts result in EXPIRED status
//
// Returns nil on successful completion or an error if workflow fails.
func RechargePayment(ctx workflow.Context, _ *billpayWfPb.RechargePaymentRequest) error {
	var err error
	lg := workflow.GetLogger(ctx)
	lg.Info("starting recharge payment workflow")

	// ------------------- GET WORKFLOW PROCESSING PARAMS -------------------
	wfProcessingParams, err := getWorkflowProcessingParams(ctx)
	if err != nil {
		lg.Error("error while getting workflow processing params", zap.Error(err))
		return fmt.Errorf("error while getting workflow processing params %w", err)
	}
	req := &billpayWfPb.RechargePaymentRequest{}
	if err = (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(wfProcessingParams.GetPayload(), req); err != nil {
		lg.Error("failed to unmarshal workflow payload", zap.Error(err))
		return fmt.Errorf("failed to unmarshal payload %w", err)
	}

	clientReqId := req.GetClientRequestId()
	if clientReqId == "" {
		lg.Error("client request id is empty in workflow payload")
		return fmt.Errorf("client request id is required")
	}

	// ------------------- INITIALISE WF DATA AND SETUP QUERY HANDLER -------------------
	workflowData := billpayUtils.NewRechargePaymentWorkflowData(clientReqId)
	if err = SetupQueryHandler(ctx, workflowData); err != nil {
		lg.Error("failed to setup query handler", zap.Error(err))
		return err
	}

	// ------------------- UPDATE RECHARGE ORDER TO IN_PROGRESS -------------------
	if err = updateRechargeOrderStatus(ctx, workflowData, clientReqId, enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS, enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED); err != nil {
		lg.Error("failed to update order status to IN_PROGRESS", zap.Error(err))
		return fmt.Errorf("failed to update order status to IN_PROGRESS: %w", err)
	}

	// ------------------- RECHARGE_STAGE_PAYMENT -------------------
	err = executePaymentStage(ctx, workflowData, clientReqId)
	if err != nil {
		lg.Error("payment stage failed", zap.Error(err))
		return err
	}

	// ------------------- CHECK IF FULFILLMENT SHOULD BE INITIATED -------------------
	shouldFulfil, err := shouldInitiateRechargeFulfilment(ctx, clientReqId)
	if err != nil {
		lg.Error("failed to check if fulfilment should be initiated", zap.Error(err))
		return err
	}

	if !shouldFulfil {
		lg.Info("workflow completed, fulfilment stage is not needed", zap.Bool("should_fulfil", shouldFulfil))
		return nil
	}

	// ------------------- RECHARGE_STAGE_FULFILLMENT -------------------
	err = executeFulfillmentStage(ctx, workflowData, clientReqId)
	if err != nil {
		lg.Error("fulfillment stage failed", zap.Error(err))
		return err
	}

	// ------------------- CHECK IF REFUND IS NEEDED -------------------
	shouldRefund, err := shouldInitiateRechargeRefundOrder(ctx, clientReqId)
	if err != nil {
		lg.Error("failed to check if refund should be initiated", zap.Error(err))
		return err
	}

	if !shouldRefund {
		lg.Info("workflow completed, refund stage is not needed", zap.Bool("should_refund", shouldRefund))
		return nil
	}

	// ------------------- RECHARGE_STAGE_REFUND -------------------
	lg.Info("refund is needed, proceeding to RECHARGE_STAGE_REFUND", zap.Bool("should_refund", shouldRefund))
	err = executeRefundStage(ctx, workflowData, clientReqId)
	if err != nil {
		lg.Error("refund stage failed", zap.Error(err))
		return err
	}

	lg.Info("recharge payment workflow completed successfully")
	return nil
}

// nolint:unused
func getRechargeOrderDetails(ctx workflow.Context, clientRequestId string) (*billpayPb.RechargeOrder, error) {
	res := &billpayActPb.GetRechargeOrderDetailsResponse{}
	err := activityPkg.Execute(
		ctx,
		payNs.GetRechargeOrderDetails,
		res,
		&billpayActPb.GetRechargeOrderDetailsRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientRequestId,
			},
		},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get recharge order details: %w", err)
	}

	return res.GetRechargeOrder(), nil
}

// executePaymentStage handles the RECHARGE_STAGE_PAYMENT stage of the workflow.
// It creates a payment stage, listens for authentication signal with 20-minute timeout,
// and enquires payment status. The stage can result in SUCCESS, FAILED, EXPIRED, or MANUAL_INTERVENTION.
func executePaymentStage(ctx workflow.Context, workflowData *billpayUtils.RechargePaymentWorkflowData, clientReqId string) error {
	var err error
	lg := workflow.GetLogger(ctx)

	// Step 1: Create RechargeOrderStage for PAYMENT stage
	var createStageRes *billpayActPb.CreateRechargeOrderStageResponse
	if createStageRes, err = createRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT); err != nil {
		return fmt.Errorf("failed to create payment stage: %w", err)
	}
	paymentClientRequestId := createStageRes.GetRechargeOrderStage().GetClientRequestId()

	// Step 2: Fetch payment initiation details to get payment options deeplink
	fetchPaymentRes := &billpayActPb.FetchRechargePaymentInitiationDetailsResponse{}
	err = activityPkg.Execute(
		ctx,
		payNs.FetchRechargePaymentInitiationDetails,
		fetchPaymentRes,
		&billpayActPb.FetchRechargePaymentInitiationDetailsRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
			},
		},
	)
	if err != nil {
		return fmt.Errorf("failed to fetch payment initiation details: %w", err)
	}

	// Step 3: Update payment stage details with payment options deeplink
	err = workflowData.UpdatePaymentStageDetails(fetchPaymentRes.GetPaymentOptionsDeeplink(), nextActionProcessor)
	if err != nil {
		return fmt.Errorf("failed to update payment stage details in workflow data: %w", err)
	}

	// Step 4: Listen for RechargeFundTransferAuthSignal with timeout
	var signalReceived bool
	var workflowStageStatus enums.RechargeStageStatus

	var signalErr error
	signalData := &signal.FundTransferStatusSignal{}
	workflow.NewSelector(ctx).
		AddReceive(workflow.GetSignalChannel(ctx, string(payNs.RechargeFundTransferAuthSignal)), func(c workflow.ReceiveChannel, more bool) {
			lg.Debug("received RechargeFundTransferAuthSignal")
			var payload []byte
			c.Receive(ctx, &payload)
			signalErr = protojson.Unmarshal(payload, signalData)
			if signalErr != nil {
				// Log the error but continue with enquiry
				lg.Error("failed to unmarshal RechargeFundTransferAuthSignal", zap.Error(signalErr))
			} else {
				signalReceived = true
				workflowStageStatus = enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS
			}
		}).
		AddFuture(workflow.NewTimer(ctx, 20*time.Minute), func(f workflow.Future) {
			lg.Debug("RechargeFundTransferAuthSignal not received within timeout, marking as expired")
			signalReceived = false
			workflowStageStatus = enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_EXPIRED
		}).Select(ctx)

	// Step 5: Update stage status to IN_PROGRESS if signal received, keep current status if timeout
	if signalReceived {
		if err := updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT, workflowStageStatus); err != nil {
			return fmt.Errorf("failed to update payment stage to IN_PROGRESS: %w", err)
		}

		err = workflowData.UpdatePoolAccountPaymentOrderId(signalData.GetOrderId(), nextActionProcessor)
		if err != nil {
			return fmt.Errorf("failed to update pool account payment order ID in workflow data: %w", err)
		}
	}

	// Step 6: Always enquire payment status regardless of signal reception
	enquireRes := &billpayActPb.EnquireRechargePaymentStatusResponse{}
	err = activityPkg.Execute(
		ctx,
		payNs.EnquireRechargePaymentStatus,
		enquireRes,
		&billpayActPb.EnquireRechargePaymentStatusRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
			},
			PaymentStageClientRequestId: paymentClientRequestId,
		},
	)

	// Step 7: Handle enquire payment status response
	if err != nil {
		return handlePaymentEnquiryErrorWithTimeout(ctx, workflowData, clientReqId, err, signalReceived)
	}

	// Step 8: Payment enquiry successful, update stage to SUCCESS
	if err = updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL); err != nil {
		return fmt.Errorf("failed to update payment stage to SUCCESS: %w", err)
	}

	lg.Debug("RECHARGE_STAGE_PAYMENT completed successfully")
	return nil
}

// executeFulfillmentStage handles the RECHARGE_STAGE_FULFILLMENT stage of the workflow.
// It creates a fulfillment stage, initiates recharge with vendor, and checks status.
// On success, updates order status to SUCCESS. On failure, proceeds to refund check.
func executeFulfillmentStage(ctx workflow.Context, workflowData *billpayUtils.RechargePaymentWorkflowData, clientReqId string) error {
	var err error
	lg := workflow.GetLogger(ctx)

	// Step 1: Create RechargeOrderStage for FULFILLMENT stage
	var createStageRes *billpayActPb.CreateRechargeOrderStageResponse
	if createStageRes, err = createRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT); err != nil {
		return fmt.Errorf("failed to create fulfillment stage: %w", err)
	}
	fulfillmentClientRequestId := createStageRes.GetRechargeOrderStage().GetClientRequestId()

	// Step 2: Initiate Recharge with Vendor
	err = activityPkg.Execute(
		ctx,
		payNs.InitiateRechargeWithVendor,
		&billpayActPb.InitiateRechargeWithVendorResponse{},
		&billpayActPb.InitiateRechargeWithVendorRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
			},
			FulfilmentStageClientRequestId: fulfillmentClientRequestId,
		},
	)

	// Step 3: Handle InitiateRechargeWithVendor response
	if err != nil {
		return handleFulfillmentInitiateError(ctx, workflowData, clientReqId, err)
	}

	// Step 4: Enquire Recharge Status with Vendor
	err = activityPkg.Execute(
		ctx,
		payNs.EnquireRechargeStatusWithVendor,
		&billpayActPb.EnquireRechargeStatusWithVendorResponse{},
		&billpayActPb.EnquireRechargeStatusWithVendorRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
			},
			FulfilmentStageClientRequestId: fulfillmentClientRequestId,
		},
	)

	// Step 5: Handle EnquireRechargeStatusWithVendor response
	if err != nil {
		return handleFulfillmentEnquiryError(ctx, workflowData, clientReqId, fulfillmentClientRequestId, err)
	}

	// Step 6: Fulfillment successful, update stage to SUCCESS
	if err = updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL); err != nil {
		return fmt.Errorf("failed to update fulfillment stage to SUCCESS: %w", err)
	}

	// Step 7: Update RechargeOrderStatus to SUCCESS
	if err = updateRechargeOrderStatus(ctx, workflowData, clientReqId, enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS, enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED); err != nil {
		return fmt.Errorf("failed to update order status to SUCCESS: %w", err)
	}

	lg.Debug("RECHARGE_STAGE_FULFILLMENT completed successfully")
	return nil
}

// Helper method to update recharge order status
func updateRechargeOrderStatus(ctx workflow.Context, workflowData *billpayUtils.RechargePaymentWorkflowData, clientReqId string, status enums.RechargeOrderStatus, subStatus enums.RechargeOrderSubStatus) error {
	lg := workflow.GetLogger(ctx)
	lg.Debug("updating recharge order status", zap.String("status", status.String()))

	if err := workflowData.UpdateOrderStatus(status, subStatus, nextActionProcessor); err != nil {
		lg.Error("failed to update workflow data order status", zap.Error(err))
		return fmt.Errorf("failed to update workflow data: %w", err)
	}

	err := activityPkg.Execute(
		ctx,
		payNs.UpdateRechargeOrderStatus,
		&billpayActPb.UpdateRechargeOrderStatusResponse{},
		&billpayActPb.UpdateRechargeOrderStatusRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
			},
			StatusToUpdate:    status,
			SubStatusToUpdate: subStatus,
		},
	)
	if err != nil {
		lg.Error("failed to update recharge order status", zap.Error(err), zap.String("status", status.String()))
		return err
	}

	lg.Debug("successfully updated recharge order status", zap.String("status", status.String()))
	return nil
}

// Helper method to create recharge order stage
func createRechargeOrderStage(ctx workflow.Context, workflowData *billpayUtils.RechargePaymentWorkflowData, clientReqId string, stage enums.RechargeStage) (*billpayActPb.CreateRechargeOrderStageResponse, error) {
	lg := workflow.GetLogger(ctx)
	lg.Info("creating recharge order stage", zap.String("stage", stage.String()))

	if err := workflowData.UpdateStageStatus(stage, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_INITIATED, nextActionProcessor); err != nil {
		lg.Error("failed to update workflow data stage status", zap.Error(err))
		return nil, fmt.Errorf("failed to update workflow data: %w", err)
	}

	createStageRes := &billpayActPb.CreateRechargeOrderStageResponse{}
	err := activityPkg.Execute(
		ctx,
		payNs.CreateRechargeOrderStage,
		createStageRes,
		&billpayActPb.CreateRechargeOrderStageRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
			},
			Stage: stage,
		},
	)
	if err != nil {
		lg.Error("failed to create recharge order stage", zap.Error(err), zap.String("stage", stage.String()))
		return createStageRes, err
	}

	lg.Debug("successfully created recharge order stage", zap.String("stage", stage.String()), zap.String("client_request_id", createStageRes.GetRechargeOrderStage().GetClientRequestId()))
	return createStageRes, nil
}

// Helper method to update recharge order stage
func updateRechargeOrderStage(ctx workflow.Context, workflowData *billpayUtils.RechargePaymentWorkflowData, clientReqId string, stage enums.RechargeStage, stageStatus enums.RechargeStageStatus) error {
	lg := workflow.GetLogger(ctx)
	lg.Debug("updating recharge order stage", zap.String("stage", stage.String()), zap.String("status", stageStatus.String()))

	if err := workflowData.UpdateStageStatus(stage, stageStatus, nextActionProcessor); err != nil {
		lg.Error("failed to update workflow data stage status", zap.Error(err))
		return fmt.Errorf("failed to update workflow data: %w", err)
	}

	err := activityPkg.Execute(
		ctx,
		payNs.UpdateRechargeOrderStage,
		&billpayActPb.UpdateRechargeOrderStageResponse{},
		&billpayActPb.UpdateRechargeOrderStageRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
			},
			Stage:               stage,
			StageStatusToUpdate: stageStatus,
		},
	)
	if err != nil {
		lg.Error("failed to update recharge order stage", zap.Error(err), zap.String("stage", stage.String()), zap.String("status", stageStatus.String()))
		return err
	}

	return nil
}

// Helper method to handle payment enquiry errors with timeout consideration
func handlePaymentEnquiryErrorWithTimeout(ctx workflow.Context, workflowData *billpayUtils.RechargePaymentWorkflowData, clientReqId string, err error, signalReceived bool) error {
	lg := workflow.GetLogger(ctx)
	lg.Error("enquire recharge payment status failed", zap.Error(err), zap.Bool("signal_received", signalReceived))

	if epifitemporal.IsRetryableError(err) {
		// Update stage to MANUAL_INTERVENTION
		if updateErr := updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION); updateErr != nil {
			return fmt.Errorf("failed to update payment stage to MANUAL_INTERVENTION: %w", updateErr)
		}

		// Update order status to MANUAL_INTERVENTION
		if updateErr := updateRechargeOrderStatus(ctx, workflowData, clientReqId, enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION, enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED); updateErr != nil {
			return fmt.Errorf("failed to update order status to MANUAL_INTERVENTION: %w", updateErr)
		}

		return fmt.Errorf("workflow failed due to payment enquiry transient error requiring manual intervention: %w", err)
	}

	if !epifitemporal.IsRetryableError(err) {
		// Check if this is a permanent failure AND signal was not received (timeout case)
		if !signalReceived {
			lg.Debug("permanent failure with timeout, marking as expired")
			// Update stage to EXPIRED
			if updateErr := updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_EXPIRED); updateErr != nil {
				return fmt.Errorf("failed to update payment stage to EXPIRED: %w", updateErr)
			}

			// Update order status to EXPIRED
			if updateErr := updateRechargeOrderStatus(ctx, workflowData, clientReqId, enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_EXPIRED, enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED); updateErr != nil {
				return fmt.Errorf("failed to update order status to EXPIRED: %w", updateErr)
			}

			return nil
		} else {
			lg.Debug("permanent failure with signal received, marking as failed")
			// Update stage to FAILED
			if updateErr := updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED); updateErr != nil {
				return fmt.Errorf("failed to update payment stage to FAILED: %w", updateErr)
			}

			// Update order status to FAILED
			if updateErr := updateRechargeOrderStatus(ctx, workflowData, clientReqId, enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_FAILED, enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED); updateErr != nil {
				return fmt.Errorf("failed to update order status to FAILED: %w", updateErr)
			}

			return nil
		}
	}

	// Unknown error type, return it
	return fmt.Errorf("enquire payment status failed with unknown error: %w", err)
}

// Helper method to handle InitiateRechargeWithVendor errors
//
//nolint:dupl,unparam
func handleFulfillmentInitiateError(ctx workflow.Context, workflowData *billpayUtils.RechargePaymentWorkflowData, clientReqId string, err error) error {
	lg := workflow.GetLogger(ctx)
	lg.Error("initiate recharge with vendor failed", zap.Error(err))

	if epifitemporal.IsRetryableError(err) {
		// Update stage to MANUAL_INTERVENTION
		if updateErr := updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION); updateErr != nil {
			return fmt.Errorf("failed to update fulfillment stage to MANUAL_INTERVENTION: %w", updateErr)
		}

		// Update order status to MANUAL_INTERVENTION
		if updateErr := updateRechargeOrderStatus(ctx, workflowData, clientReqId, enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION, enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED); updateErr != nil {
			return fmt.Errorf("failed to update order status to MANUAL_INTERVENTION: %w", updateErr)
		}

		lg.Info("workflow failed due to transient failure in initiate recharge with vendor")
		return fmt.Errorf("workflow failed due to fulfillment initiate transient error requiring manual intervention: %w", err)
	}

	if !epifitemporal.IsRetryableError(err) {
		// Update stage to FAILED
		if updateErr := updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED); updateErr != nil {
			return fmt.Errorf("failed to update fulfillment stage to FAILED: %w", updateErr)
		}

		lg.Info("fulfillment stage failed, proceeding to refund stage")
		return nil
	}

	// Unknown error type, return it
	return fmt.Errorf("initiate recharge with vendor failed with unknown error: %w", err)
}

// Helper method to handle EnquireRechargeStatusWithVendor errors
//
//nolint:dupl,unparam
func handleFulfillmentEnquiryError(ctx workflow.Context, workflowData *billpayUtils.RechargePaymentWorkflowData, clientReqId, fulfillmentClientRequestId string, err error) error {
	lg := workflow.GetLogger(ctx)
	lg.Error("enquire recharge status with vendor failed", zap.Error(err))

	if epifitemporal.IsRetryableError(err) {
		// Update stage to MANUAL_INTERVENTION
		if updateErr := updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION); updateErr != nil {
			return fmt.Errorf("failed to update fulfillment stage to MANUAL_INTERVENTION: %w", updateErr)
		}

		// Update order status to MANUAL_INTERVENTION
		if updateErr := updateRechargeOrderStatus(ctx, workflowData, clientReqId, enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION, enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED); updateErr != nil {
			return fmt.Errorf("failed to update order status to MANUAL_INTERVENTION: %w", updateErr)
		}

		lg.Info("workflow failed due to transient failure in enquire recharge status with vendor")
		return fmt.Errorf("workflow failed due to fulfillment enquiry transient error requiring manual intervention: %w", err)
	}

	if !epifitemporal.IsRetryableError(err) {
		// Update stage to FAILED
		if updateErr := updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED); updateErr != nil {
			return fmt.Errorf("failed to update fulfillment stage to FAILED: %w", updateErr)
		}

		lg.Info("fulfillment stage failed, proceeding to refund stage")
		return nil
	}

	// Unknown error type, return it
	return fmt.Errorf("enquire recharge status with vendor failed with unknown error: %w", err)
}

// shouldInitiateRechargeRefundOrder checks if refund should be initiated
//
//nolint:dupl
func shouldInitiateRechargeRefundOrder(ctx workflow.Context, clientReqId string) (bool, error) {
	lg := workflow.GetLogger(ctx)

	var res billpayActPb.ShouldInitiateRechargeRefundOrderResponse
	err := activityPkg.Execute(
		ctx,
		payNs.ShouldInitiateRechargeRefundOrder,
		&res,
		&billpayActPb.ShouldInitiateRechargeRefundOrderRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
			},
		},
	)
	if err != nil {
		lg.Error("ShouldInitiateRechargeRefundOrder activity failed", zap.Error(err))
		return false, fmt.Errorf("failed to check if refund should be initiated: %w", err)
	}

	lg.Debug("ShouldInitiateRechargeRefundOrder completed", zap.Bool("should_initiate_refund", res.GetShouldInitiateRefund()))
	return res.GetShouldInitiateRefund(), nil
}

// shouldInitiateRechargeFulfilment checks if recharge fulfilment should be initiated
//
//nolint:dupl
func shouldInitiateRechargeFulfilment(ctx workflow.Context, clientReqId string) (bool, error) {
	lg := workflow.GetLogger(ctx)

	var res billpayActPb.ShouldInitiateRechargeFulfilmentResponse
	err := activityPkg.Execute(
		ctx,
		payNs.ShouldInitiateRechargeFulfilment,
		&res,
		&billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
			},
		},
	)
	if err != nil {
		lg.Error("ShouldInitiateRechargeFulfilment activity failed", zap.Error(err))
		return false, fmt.Errorf("failed to check if fulfilment should be initiated: %w", err)
	}

	lg.Debug("ShouldInitiateRechargeFulfilment completed", zap.Bool("should_initiate_fulfilment", res.GetShouldInitiateFulfilment()))
	return res.GetShouldInitiateFulfilment(), nil
}

// executeRefundStage handles the RECHARGE_STAGE_REFUND stage of the workflow.
// It creates a refund stage, initiates refund order, and enquires refund status.
// On success, updates order status to FAILED with REFUNDED sub-status.
func executeRefundStage(ctx workflow.Context, workflowData *billpayUtils.RechargePaymentWorkflowData, clientReqId string) error {
	var err error
	lg := workflow.GetLogger(ctx)

	// Step 1: Create RechargeOrderStage for REFUND stage
	var createStageRes *billpayActPb.CreateRechargeOrderStageResponse
	createStageRes, err = createRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_REFUND)
	if err != nil {
		return fmt.Errorf("failed to create refund stage: %w", err)
	}
	refundClientRequestId := createStageRes.RechargeOrderStage.ClientRequestId

	// Step 2: Initiate Recharge Refund Order
	err = initiateRechargeRefundOrder(ctx, clientReqId, refundClientRequestId)
	if err != nil {
		return handleRefundInitiateError(ctx, workflowData, clientReqId, refundClientRequestId, err)
	}

	// Step 3: Enquire Recharge Refund Order Status
	err = enquireRechargeRefundOrderStatus(ctx, clientReqId, refundClientRequestId)
	if err != nil {
		return handleRefundEnquiryError(ctx, workflowData, clientReqId, refundClientRequestId, err)
	}

	// Step 4: Refund enquiry successful, update stage to SUCCESS
	if err = updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_REFUND, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL); err != nil {
		return fmt.Errorf("failed to update refund stage to SUCCESS: %w", err)
	}

	// Step 5: Update order status to FAILED with REFUNDED sub-status
	if err = updateRechargeOrderStatus(ctx, workflowData, clientReqId, enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_FAILED, enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED); err != nil {
		return fmt.Errorf("failed to update order status to FAILED with REFUNDED sub-status: %w", err)
	}

	lg.Debug("RECHARGE_STAGE_REFUND completed successfully")
	return nil
}

// initiateRechargeRefundOrder calls the InitiateRechargeRefundOrder activity
func initiateRechargeRefundOrder(ctx workflow.Context, clientReqId, refundClientRequestId string) error {
	lg := workflow.GetLogger(ctx)

	var res billpayActPb.InitiateRechargeRefundOrderResponse
	err := activityPkg.Execute(
		ctx,
		payNs.InitiateRechargeRefundOrder,
		&res,
		&billpayActPb.InitiateRechargeRefundOrderRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
			},
			RefundClientRequestId: refundClientRequestId,
		},
	)
	if err != nil {
		lg.Error("InitiateRechargeRefundOrder activity failed", zap.Error(err))
		return err
	}

	lg.Info("InitiateRechargeRefundOrder completed successfully")
	return nil
}

// enquireRechargeRefundOrderStatus calls the EnquireRechargeRefundOrderStatus activity
func enquireRechargeRefundOrderStatus(ctx workflow.Context, clientReqId, refundClientRequestId string) error {
	lg := workflow.GetLogger(ctx)

	var res billpayActPb.EnquireRechargeRefundOrderStatusResponse
	err := activityPkg.Execute(
		ctx,
		payNs.EnquireRechargeRefundOrderStatus,
		&res,
		&billpayActPb.EnquireRechargeRefundOrderStatusRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
			},
			RefundClientRequestId: refundClientRequestId,
		},
	)
	if err != nil {
		lg.Error("EnquireRechargeRefundOrderStatus activity failed", zap.Error(err))
		return err
	}

	lg.Info("EnquireRechargeRefundOrderStatus completed successfully")
	return nil
}

// handleRefundInitiateError handles errors from InitiateRechargeRefundOrder activity
//
//nolint:dupl,unparam
func handleRefundInitiateError(ctx workflow.Context, workflowData *billpayUtils.RechargePaymentWorkflowData, clientReqId, refundClientRequestId string, err error) error {
	lg := workflow.GetLogger(ctx)
	lg.Error("InitiateRechargeRefundOrder failed", zap.Error(err))

	if epifitemporal.IsRetryableError(err) {
		// Transient error: mark refund stage as MANUAL_INTERVENTION and order as MANUAL_INTERVENTION
		if stageErr := updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_REFUND, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION); stageErr != nil {
			return fmt.Errorf("failed to update refund stage to MANUAL_INTERVENTION, %w", stageErr)
		}

		if orderErr := updateRechargeOrderStatus(ctx, workflowData, clientReqId, enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION, enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED); orderErr != nil {
			return fmt.Errorf("failed to update order status to MANUAL_INTERVENTION, %w", orderErr)
		}

		return fmt.Errorf("workflow failed due to refund initiate transient error requiring manual intervention: %w", err)
	}

	if !epifitemporal.IsRetryableError(err) {
		// Permanent error: mark refund stage as FAILED and order as MANUAL_INTERVENTION
		if stageErr := updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_REFUND, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED); stageErr != nil {
			return fmt.Errorf("failed to update refund stage to FAILED, %w", stageErr)
		}

		if orderErr := updateRechargeOrderStatus(ctx, workflowData, clientReqId, enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION, enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED); orderErr != nil {
			return fmt.Errorf("failed to update order status to MANUAL_INTERVENTION, %w", orderErr)
		}

		return fmt.Errorf("workflow failed due to refund initiate permanent error requiring manual intervention: %w", err)
	}

	return fmt.Errorf("InitiateRechargeRefundOrder failed with unknown error: %w", err)
}

// handleRefundEnquiryError handles errors from EnquireRechargeRefundOrderStatus activity
//
//nolint:dupl,unparam
func handleRefundEnquiryError(ctx workflow.Context, workflowData *billpayUtils.RechargePaymentWorkflowData, clientReqId, refundClientRequestId string, err error) error {
	lg := workflow.GetLogger(ctx)
	lg.Error("EnquireRechargeRefundOrderStatus failed", zap.Error(err))

	if epifitemporal.IsRetryableError(err) {
		// Transient error: mark refund stage as MANUAL_INTERVENTION and order as MANUAL_INTERVENTION
		if stageErr := updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_REFUND, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION); stageErr != nil {
			return fmt.Errorf("failed to update refund stage to MANUAL_INTERVENTION, %w", stageErr)
		}

		if orderErr := updateRechargeOrderStatus(ctx, workflowData, clientReqId, enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION, enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED); orderErr != nil {
			return fmt.Errorf("failed to update order status to MANUAL_INTERVENTION, %w", orderErr)
		}

		return fmt.Errorf("workflow failed due to refund enquiry transient error requiring manual intervention: %w", err)
	}

	if !epifitemporal.IsRetryableError(err) {
		// Permanent error: mark refund stage as FAILED and order as MANUAL_INTERVENTION
		if stageErr := updateRechargeOrderStage(ctx, workflowData, clientReqId, enums.RechargeStage_RECHARGE_STAGE_REFUND, enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED); stageErr != nil {
			return fmt.Errorf("failed to update refund stage to FAILED, %w", stageErr)
		}

		if orderErr := updateRechargeOrderStatus(ctx, workflowData, clientReqId, enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION, enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED); orderErr != nil {
			return fmt.Errorf("failed to update order status to MANUAL_INTERVENTION, %w", orderErr)
		}

		return fmt.Errorf("workflow failed due to refund enquiry permanent error requiring manual intervention: %w", err)
	}

	return fmt.Errorf("EnquireRechargeRefundOrderStatus failed with unknown error: %w", err)
}

// SetupQueryHandler registers the query handler for the workflow
func SetupQueryHandler(ctx workflow.Context, workflowData *billpayUtils.RechargePaymentWorkflowData) error {
	err := workflow.SetQueryHandler(ctx, billpayUtils.RechargePaymentStatusQueryType, func() ([]byte, error) {
		workflowDataBytes, marshalErr := json.Marshal(workflowData)
		if marshalErr != nil {
			return nil, fmt.Errorf("failed to marshal workflow data: %w", marshalErr)
		}
		return workflowDataBytes, nil
	})

	if err != nil {
		return fmt.Errorf("failed to register query handler '%s': %w", billpayUtils.RechargePaymentStatusQueryType, err)
	}

	return nil
}
