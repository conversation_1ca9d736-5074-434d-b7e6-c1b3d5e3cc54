// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/billpay"
	"github.com/epifi/gamma/api/vendorgateway/recharge"
	billpay2 "github.com/epifi/gamma/billpay"
	"github.com/epifi/gamma/billpay/activity"
	"github.com/epifi/gamma/billpay/billermanager"
	"github.com/epifi/gamma/billpay/config/genconf"
	"github.com/epifi/gamma/billpay/dao"
	types2 "github.com/epifi/gamma/billpay/wire/types"
	genconf2 "github.com/epifi/gamma/pay/config/worker/genconf"
)

// Injectors from wire.go:

func InitializeService(gconf *genconf.Config, db types.BillpayPGDB, setuClient billpay.BillPayServiceClient, rechargeClient recharge.MobileRechargeServiceClient, userClient user.UsersClient, celestialClient celestial.CelestialClient, billersRedisStore types2.BillersRedisStore) *billpay2.Service {
	gormDB := types.BillpayPGDBGormDBProvider(db)
	billerImpl := dao.NewBillerImpl(gormDB, gconf)
	categoryImpl := dao.NewCategoryImpl(gormDB, gconf)
	billerManagerImpl := billermanager.NewBillerManager(gconf, billerImpl, categoryImpl, setuClient)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	rechargeOrderImpl := dao.NewRechargeOrderImpl(gormDB, domainIdGenerator)
	rechargeOrderStageImpl := dao.NewRechargeOrderStageImpl(gormDB, domainIdGenerator)
	plansCacheStorage := types2.PlansCacheStorageProvider(billersRedisStore)
	service := billpay2.NewBillPayService(gconf, billerManagerImpl, rechargeOrderImpl, rechargeOrderStageImpl, rechargeClient, userClient, celestialClient, plansCacheStorage)
	return service
}

func InitializeActivityProcessor(gconf *genconf2.Config, db types.BillpayPGDB, orderClient order.OrderServiceClient, rechargeVgClient recharge.MobileRechargeServiceClient, usersClient user.UsersClient, payClient pay.PayClient) *activity.Processor {
	gormDB := types.BillpayPGDBGormDBProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	rechargeOrderImpl := dao.NewRechargeOrderImpl(gormDB, domainIdGenerator)
	rechargeOrderStageImpl := dao.NewRechargeOrderStageImpl(gormDB, domainIdGenerator)
	processor := activity.NewProcessor(gconf, rechargeOrderImpl, rechargeOrderStageImpl, orderClient, rechargeVgClient, usersClient, payClient, domainIdGenerator)
	return processor
}
