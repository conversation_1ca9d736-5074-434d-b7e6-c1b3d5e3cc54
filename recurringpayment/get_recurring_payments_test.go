package recurringpayment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/money"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/actor/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	mocks4 "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	mocks2 "github.com/epifi/gamma/api/paymentinstrument/mocks"
	pb "github.com/epifi/gamma/api/recurringpayment"
	consumerPb "github.com/epifi/gamma/api/recurringpayment/consumer"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/upi/mandate"
	mocks3 "github.com/epifi/gamma/api/upi/mandate/mocks"
	dao2 "github.com/epifi/gamma/recurringpayment/dao"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	mocks5 "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

type filterOptionMatcher struct {
	want []dao2.FilterOption
}

func newFilterOptionMatcher(want []dao2.FilterOption) *filterOptionMatcher {
	return &filterOptionMatcher{want: want}
}

func (f *filterOptionMatcher) Matches(x interface{}) bool {

	got, ok := x.([]dao2.FilterOption)
	if !ok || len(f.want) != len(got) {
		return false
	}

	for i, w := range got {
		f.want[i] = w
	}

	return reflect.DeepEqual(f.want, got)
}

func (f *filterOptionMatcher) String() string {
	return fmt.Sprintf("want: %v", f.want)
}

func TestService_GetRecurringPaymentsForActor(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockActorClient := mocks.NewMockActorClient(ctr)
	mockRecurringPaymentActionsDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockPublisher := queueMocks.NewMockPublisher(ctr)

	mockRecurringPaymentVendorDetailsDao := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)
	svc := NewService(mockRecurringPaymentDao, nil, nil, mockRecurringPaymentActionsDao, nil, mockActorClient, nil, nil, nil, conf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockPublisher, nil, nil, nil, nil, nil, nil, mockRecurringPaymentVendorDetailsDao, nil, nil, nil, nil)

	timeNow := time.Now().Add(7 * time.Minute).UTC()
	expiryTime := time.Now().Add(15 * time.Minute).UTC()

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))

	recurringPayment := &pb.RecurringPayment{
		Id:          "id-1",
		ExternalId:  "ext-id-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		ShareToPayee:             true,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
	}

	recurringPayment2 := &pb.RecurringPayment{
		Id:          "id-1",
		ExternalId:  "ext-id-2",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_CREATION_AUTHORISED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		ShareToPayee:             true,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
	}

	tests := []struct {
		name           string
		req            *pb.GetRecurringPaymentsForActorRequest
		setupMockCalls func()
		want           *pb.GetRecurringPaymentsForActorResponse
		wantErr        bool
	}{
		{
			name: "fetch recurring payments successfully for activated state",
			req: &pb.GetRecurringPaymentsForActorRequest{
				CurrentActorId: "actor-1",
				PageSize:       5,
				Offset:         0,
				StartTimestamp: timestampPb.New(timeNow),
				States:         []pb.RecurringPaymentState{pb.RecurringPaymentState_ACTIVATED},
				Descending:     true,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetByActorId(gomock.Any(), "actor-1", timeNow, int32(5), int32(0), true, []pb.RecurringPaymentState{pb.RecurringPaymentState_ACTIVATED}).
					Return([]*pb.RecurringPayment{recurringPayment}, nil)
				mockActorClient.EXPECT().GetEntityDetails(gomock.Any(), &actor.GetEntityDetailsRequest{ActorIds: []string{"actor-2"}}).Return(
					&actor.GetEntityDetailsResponse{
						EntityDetails: []*actor.GetEntityDetailsResponse_EntityDetail{
							{Name: &commontypes.Name{
								FirstName: "first",
								LastName:  "last",
							},
								ProfileImageUrl: "Img",
								ActorId:         "actor-2",
							},
						}, Status: rpc.StatusOk(),
					}, nil)
				mockPublisher.EXPECT().Publish(gomock.Any(), &consumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: "actor-1",
				}).Return("", nil)
			},
			want: &pb.GetRecurringPaymentsForActorResponse{
				RecurringPayments: []*pb.GetRecurringPaymentsForActorResponse_RecurringPaymentTile{
					{
						Id:               "id-1",
						AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
						Amount:           money.ZeroINR().Pb,
						Name: (&commontypes.Name{
							FirstName: "first",
							LastName:  "last",
						}).ToString(),
						ImageUrl:   "Img",
						Type:       pb.RecurringPaymentType_UPI_MANDATES,
						State:      pb.RecurringPaymentState_ACTIVATED,
						ExternalId: "ext-id-1",
						Interval: &types.Interval{
							StartTime: startDate,
							EndTime:   endDate,
						},
					},
				},
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "error in fetching recurring payments due to no timestamp",
			req: &pb.GetRecurringPaymentsForActorRequest{
				CurrentActorId: "actor-1",
				PageSize:       5,
				Offset:         0,
				States:         []pb.RecurringPaymentState{pb.RecurringPaymentState_ACTIVATED},
			},
			setupMockCalls: func() {
			},
			want:    &pb.GetRecurringPaymentsForActorResponse{Status: rpc.StatusInvalidArgument()},
			wantErr: false,
		},
		{
			name: "error in fetching recurring payments due to invalid actor id",
			req: &pb.GetRecurringPaymentsForActorRequest{
				CurrentActorId: "invalid actor",
				PageSize:       5,
				Offset:         0,
				StartTimestamp: timestampPb.New(timeNow),
				States:         []pb.RecurringPaymentState{pb.RecurringPaymentState_MODIFY_QUEUED},
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetByActorId(gomock.Any(), "invalid actor", timeNow, int32(5),
					int32(0), false, []pb.RecurringPaymentState{pb.RecurringPaymentState_MODIFY_QUEUED}).
					Return(nil, gorm.ErrRecordNotFound)
				mockPublisher.EXPECT().Publish(gomock.Any(), &consumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: "invalid actor",
				}).Return("", nil)
			},
			want:    &pb.GetRecurringPaymentsForActorResponse{Status: rpc.StatusRecordNotFound()},
			wantErr: false,
		},
		{
			name: "fetch recurring payments successfully for pending state",
			req: &pb.GetRecurringPaymentsForActorRequest{
				CurrentActorId: "actor-1",
				PageSize:       5,
				Offset:         0,
				StartTimestamp: timestampPb.New(timeNow),
				States:         []pb.RecurringPaymentState{pb.RecurringPaymentState_CREATION_AUTHORISED},
				Descending:     true,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetByActorId(gomock.Any(), "actor-1", timeNow, int32(5), int32(0), true,
					[]pb.RecurringPaymentState{pb.RecurringPaymentState_CREATION_AUTHORISED}).
					Return([]*pb.RecurringPayment{recurringPayment2}, nil)
				mockActorClient.EXPECT().GetEntityDetails(gomock.Any(), &actor.GetEntityDetailsRequest{ActorIds: []string{"actor-2"}}).Return(
					&actor.GetEntityDetailsResponse{
						EntityDetails: []*actor.GetEntityDetailsResponse_EntityDetail{
							{Name: &commontypes.Name{
								FirstName: "first",
								LastName:  "last",
							},
								ProfileImageUrl: "Img",
								ActorId:         "actor-2",
							},
						}, Status: rpc.StatusOk(),
					}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "id-1",
					newFilterOptionMatcher([]dao2.FilterOption{dao2.WithActionsFilter([]pb.Action{pb.Action_CREATE}),
						dao2.WithOrderByCreatedAt(true)})).Return(
					[]*pb.RecurringPaymentsAction{
						{
							ExpireAt: timestampPb.New(expiryTime),
						},
					}, nil)
				mockPublisher.EXPECT().Publish(gomock.Any(), &consumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: "actor-1",
				}).Return("", nil)
			},
			want: &pb.GetRecurringPaymentsForActorResponse{
				RecurringPayments: []*pb.GetRecurringPaymentsForActorResponse_RecurringPaymentTile{
					{
						Id:               "id-1",
						AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
						Amount:           money.ZeroINR().Pb,
						Name: (&commontypes.Name{
							FirstName: "first",
							LastName:  "last",
						}).ToString(),
						ImageUrl:   "Img",
						Type:       pb.RecurringPaymentType_UPI_MANDATES,
						State:      pb.RecurringPaymentState_CREATION_AUTHORISED,
						ExternalId: "ext-id-2",
						Interval: &types.Interval{
							StartTime: startDate,
							EndTime:   endDate,
						},
						Expiry: timestampPb.New(expiryTime),
					},
				},
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "error due to failure in fetching entity details",
			req: &pb.GetRecurringPaymentsForActorRequest{
				CurrentActorId: "actor-1",
				PageSize:       5,
				Offset:         0,
				StartTimestamp: timestampPb.New(timeNow),
				States:         []pb.RecurringPaymentState{pb.RecurringPaymentState_CREATION_QUEUED},
				Descending:     true,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetByActorId(gomock.Any(), "actor-1", timeNow, int32(5), int32(0), true,
					[]pb.RecurringPaymentState{pb.RecurringPaymentState_CREATION_QUEUED}).
					Return([]*pb.RecurringPayment{recurringPayment2}, nil)
				mockActorClient.EXPECT().GetEntityDetails(gomock.Any(), &actor.GetEntityDetailsRequest{ActorIds: []string{"actor-2"}}).Return(
					&actor.GetEntityDetailsResponse{
						EntityDetails: []*actor.GetEntityDetailsResponse_EntityDetail{}, Status: rpc.StatusInternal(),
					}, nil)
				mockPublisher.EXPECT().Publish(gomock.Any(), &consumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: "actor-1",
				}).Return("", nil)
			},
			want: &pb.GetRecurringPaymentsForActorResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "error due to failure in fetching recurring payment actions",
			req: &pb.GetRecurringPaymentsForActorRequest{
				CurrentActorId: "actor-1",
				PageSize:       5,
				Offset:         0,
				StartTimestamp: timestampPb.New(timeNow),
				States:         []pb.RecurringPaymentState{pb.RecurringPaymentState_CREATION_AUTHORISED},
				Descending:     true,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetByActorId(gomock.Any(), "actor-1", timeNow, int32(5), int32(0), true,
					[]pb.RecurringPaymentState{pb.RecurringPaymentState_CREATION_AUTHORISED}).
					Return([]*pb.RecurringPayment{recurringPayment2}, nil)
				mockActorClient.EXPECT().GetEntityDetails(gomock.Any(), &actor.GetEntityDetailsRequest{ActorIds: []string{"actor-2"}}).Return(
					&actor.GetEntityDetailsResponse{
						EntityDetails: []*actor.GetEntityDetailsResponse_EntityDetail{
							{Name: &commontypes.Name{
								FirstName: "first",
								LastName:  "last",
							},
								ProfileImageUrl: "Img",
								ActorId:         "actor-2",
							},
						}, Status: rpc.StatusOk(),
					}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByRecurringPaymentId(gomock.Any(), "id-1",
					newFilterOptionMatcher([]dao2.FilterOption{dao2.WithActionsFilter([]pb.Action{pb.Action_CREATE}),
						dao2.WithOrderByCreatedAt(true)})).Return(nil, errors.New("no recurring payment action"))
				mockPublisher.EXPECT().Publish(gomock.Any(), &consumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
					ActorId: "actor-1",
				}).Return("", nil)
			},
			want: &pb.GetRecurringPaymentsForActorResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.GetRecurringPaymentsForActor(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecurringPaymentsForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRecurringPaymentsForActor() got = %v, want %v", got, tt.want)
			}
		})
	}

}

func TestService_GetRecurringPaymentDetailsById(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentActionsDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockActorClient := mocks.NewMockActorClient(ctr)
	mockUpiMandateClient := mocks3.NewMockMandateServiceClient(ctr)
	mockAccountPIRelationsClient := mocks4.NewMockAccountPIRelationClient(ctr)
	mockPiClient := mocks2.NewMockPiClient(ctr)
	mockRecurringPaymentProcessor := mocks5.NewMockRecurringPaymentProcessor(ctr)
	mockRecurringPaymentVendorDetailsDao := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)
	svc := NewService(mockRecurringPaymentDao, nil, nil, mockRecurringPaymentActionsDao, nil, mockActorClient, nil, nil, nil, conf, mockRecurringPaymentProcessor, mockUpiMandateClient, mockAccountPIRelationsClient, mockPiClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockRecurringPaymentVendorDetailsDao, nil, nil, nil, nil)

	tt := time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)

	expiryTime := tt.Add(7 * time.Minute).UTC()

	startDate := timestampPb.New(tt)
	endDate := timestampPb.New(tt.Add(48 * time.Hour))

	recurringPayment := &pb.RecurringPayment{
		Id:          "id-1",
		ExternalId:  "ext-id-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_UPI,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}

	recurringPayment2 := &pb.RecurringPayment{
		Id:          "id-1",
		ExternalId:  "ext-id-2",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_UPI,
		State:                    pb.RecurringPaymentState_CREATION_AUTHORISED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
	}

	recurringPayment3 := &pb.RecurringPayment{
		Id:          "id-1",
		ExternalId:  "ext-id-3",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_UPI,
		State:                    pb.RecurringPaymentState_MODIFY_INITIATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYEE,
	}

	recurringPayment4 := &pb.RecurringPayment{
		Id:          "id-1",
		ExternalId:  "ext-id-4",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_UPI,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}

	type mockGetById struct {
		enable bool
		reqId  string
		want   *pb.RecurringPayment
		err    error
	}

	type mockGetEntityDetailsByActorId struct {
		enable bool
		req    *actor.GetEntityDetailsByActorIdRequest
		want   *actor.GetEntityDetailsByActorIdResponse
		err    error
	}

	type mockGetByRecurringPaymentId struct {
		enable             bool
		recurringPaymentId string
		option             []dao2.FilterOption
		want               []*pb.RecurringPaymentsAction
		err                error
	}

	type mockGetMandateRequestParameters struct {
		enable bool
		req    *mandate.GetMandateRequestParametersRequest
		want   *mandate.GetMandateRequestParametersResponse
		err    error
	}

	type mockGetCredBlockType struct {
		enable        bool
		rpType        pb.RecurringPaymentType
		actorRole     pb.ActorRole
		credBlockType pb.CredBlockType
		err           error
	}

	type mockGetByPiId struct {
		enable bool
		req    *account_pi.GetByPiIdRequest
		want   *account_pi.GetByPiIdResponse
		err    error
	}

	type mockGetPiById struct {
		enable bool
		req    *piPb.GetPiByIdRequest
		want   *piPb.GetPiByIdResponse
		err    error
	}

	type mockGetMandate struct {
		enable bool
		req    *mandate.GetMandateRequest
		res    *mandate.GetMandateResponse
		err    error
	}

	tests := []struct {
		name                            string
		req                             *pb.GetRecurringPaymentDetailsByIdRequest
		mockGetById                     mockGetById
		mockGetEntityDetailsByActorId   []mockGetEntityDetailsByActorId
		mockGetCredBlockType            mockGetCredBlockType
		mockGetPiById                   []mockGetPiById
		mockGetByRecurringPaymentId     []mockGetByRecurringPaymentId
		mockGetMandateRequestParameters mockGetMandateRequestParameters
		mockGetByPiId                   mockGetByPiId
		mockGetPiByIds                  []mockGetPiById
		want                            *pb.GetRecurringPaymentDetailsByIdResponse
		mockGetMandate                  mockGetMandate
		wantErr                         bool
	}{
		{
			name: "get recurring payment details successfully in activated state",
			req: &pb.GetRecurringPaymentDetailsByIdRequest{
				RecurringPaymentId: "id-1",
				CurrentActorId:     "actor-1",
			},
			mockGetById: mockGetById{
				enable: true,
				reqId:  "id-1",
				want:   recurringPayment,
			},
			mockGetMandate: mockGetMandate{
				enable: true,
				req: &mandate.GetMandateRequest{
					Identifier: &mandate.GetMandateRequest_RecurringPaymentId{RecurringPaymentId: "id-1"},
				},
				res: &mandate.GetMandateResponse{
					Status: rpc.StatusOk(),
					Mandate: &mandate.MandateEntity{
						Revokeable: true,
						Umn:        "abc",
					},
				},
				err: nil,
			},
			mockGetEntityDetailsByActorId: []mockGetEntityDetailsByActorId{{
				enable: true,
				req: &actor.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-2",
				},
				want: &actor.GetEntityDetailsByActorIdResponse{
					Name: &commontypes.Name{
						FirstName: "first",
						LastName:  "last",
					},
					ProfileImageUrl: "Img",
					Status:          rpc.StatusOk(),
				}},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-2",
					},
					want: &piPb.GetPiByIdResponse{
						PaymentInstrument: &piPb.PaymentInstrument{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "vpa",
								},
							},
						},
						Status: rpc.StatusOk(),
					},
				},
			},
			mockGetByRecurringPaymentId: []mockGetByRecurringPaymentId{
				{
					enable:             true,
					recurringPaymentId: "id-1",
					option: []dao2.FilterOption{dao2.WithActionsFilter([]pb.Action{pb.Action_EXECUTE}),
						dao2.WithActionStateFilter([]pb.ActionState{pb.ActionState_ACTION_SUCCESS})},
					want: []*pb.RecurringPaymentsAction{},
				},
			},
			want: &pb.GetRecurringPaymentDetailsByIdResponse{
				RecurringPayment: recurringPayment,
				Name: &commontypes.Name{
					FirstName: "first",
					LastName:  "last",
				},
				ImageUrl: "Img",
				AdditionalParams: &pb.GetRecurringPaymentDetailsByIdResponse_UpiMandateParams{
					UpiMandateParams: &pb.UPIMandateParams{
						Umn: "abc",
						Vpa: "vpa",
						Urn: "upi://collect?am=0&amrule=MAX&mode=11&note=Mandate%20execution&pa=vpa&umn=abc&validityend=03012021&validitystart=01012021",
					},
				},
				SupportedActions: []pb.Action{pb.Action_PAUSE},
				SecondaryActorId: "actor-2",
				Status:           rpc.StatusOk(),
			},
		},
		{
			name: "get recurring payment details successfully in creation in progress",
			req: &pb.GetRecurringPaymentDetailsByIdRequest{
				RecurringPaymentId: "id-1",
				CurrentActorId:     "actor-1",
			},
			mockGetById: mockGetById{
				enable: true,
				reqId:  "id-1",
				want:   recurringPayment2,
			},
			mockGetMandate: mockGetMandate{
				enable: true,
				req: &mandate.GetMandateRequest{
					Identifier: &mandate.GetMandateRequest_RecurringPaymentId{RecurringPaymentId: "id-1"},
				},
				res: &mandate.GetMandateResponse{
					Status: rpc.StatusOk(),
					Mandate: &mandate.MandateEntity{
						Revokeable: true,
						Umn:        "abc",
					},
				},
				err: nil,
			},
			mockGetEntityDetailsByActorId: []mockGetEntityDetailsByActorId{{
				enable: true,
				req: &actor.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-2",
				},
				want: &actor.GetEntityDetailsByActorIdResponse{
					Name: &commontypes.Name{
						FirstName: "first",
						LastName:  "last",
					},
					ProfileImageUrl: "Img",
					Status:          rpc.StatusOk(),
				}},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-2",
					},
					want: &piPb.GetPiByIdResponse{
						PaymentInstrument: &piPb.PaymentInstrument{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "vpa",
								},
							},
						},
						Status: rpc.StatusOk(),
					},
				},
			},
			mockGetByRecurringPaymentId: []mockGetByRecurringPaymentId{{
				enable:             true,
				recurringPaymentId: "id-1",
				option: []dao2.FilterOption{dao2.WithActionsFilter([]pb.Action{pb.Action_EXECUTE}),
					dao2.WithActionStateFilter([]pb.ActionState{pb.ActionState_ACTION_SUCCESS})},
				want: []*pb.RecurringPaymentsAction{},
			}, {
				enable:             true,
				recurringPaymentId: "id-1",
				option:             []dao2.FilterOption{dao2.WithActionsFilter([]pb.Action{pb.Action_CREATE}), dao2.WithOrderByCreatedAt(true)},
				want: []*pb.RecurringPaymentsAction{
					{
						Action:          pb.Action_CREATE,
						ExpireAt:        timestampPb.New(expiryTime),
						ClientRequestId: "client req id",
					},
				},
			},
			},
			want: &pb.GetRecurringPaymentDetailsByIdResponse{
				RecurringPayment: recurringPayment2,
				Name: &commontypes.Name{
					FirstName: "first",
					LastName:  "last",
				},
				ImageUrl: "Img",
				AdditionalParams: &pb.GetRecurringPaymentDetailsByIdResponse_UpiMandateParams{
					UpiMandateParams: &pb.UPIMandateParams{
						Umn: "abc",
						Vpa: "vpa",
						Urn: "upi://collect?am=0&amrule=MAX&mode=11&note=Mandate%20execution&pa=vpa&umn=abc&validityend=03012021&validitystart=01012021",
					},
				},
				StateParams: &pb.GetRecurringPaymentDetailsByIdResponse_PendingActionParams{
					PendingActionParams: &pb.PendingActionParams{
						Expiry:          timestampPb.New(expiryTime),
						ClientRequestId: "client req id",
					},
				},
				SecondaryActorId: "actor-2",
				Status:           rpc.StatusOk(),
			},
		},
		{
			name: "get recurring payment details successfully when modification is initiated",
			req: &pb.GetRecurringPaymentDetailsByIdRequest{
				RecurringPaymentId: "id-1",
				CurrentActorId:     "actor-1",
			},
			mockGetById: mockGetById{
				enable: true,
				reqId:  "id-1",
				want:   recurringPayment3,
			},
			mockGetMandate: mockGetMandate{
				enable: true,
				req: &mandate.GetMandateRequest{
					Identifier: &mandate.GetMandateRequest_RecurringPaymentId{RecurringPaymentId: "id-1"},
				},
				res: &mandate.GetMandateResponse{
					Status: rpc.StatusOk(),
					Mandate: &mandate.MandateEntity{
						Revokeable: true,
						Umn:        "abc",
					},
				},
				err: nil,
			},
			mockGetEntityDetailsByActorId: []mockGetEntityDetailsByActorId{{
				enable: true,
				req: &actor.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-2",
				},
				want: &actor.GetEntityDetailsByActorIdResponse{
					Name: &commontypes.Name{
						FirstName: "first",
						LastName:  "last",
					},
					ProfileImageUrl: "Img",
					Status:          rpc.StatusOk(),
				}}, {
				enable: true,
				req:    &actor.GetEntityDetailsByActorIdRequest{ActorId: "actor-2"},
				want:   &actor.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "payee"}, Status: rpc.StatusOk()},
				err:    nil,
			},
			},
			mockGetCredBlockType: mockGetCredBlockType{
				enable:        true,
				rpType:        pb.RecurringPaymentType_UPI_MANDATES,
				actorRole:     pb.ActorRole_ACTOR_ROLE_PAYER,
				credBlockType: pb.CredBlockType_NPCI,
				err:           nil,
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-2",
					},
					want: &piPb.GetPiByIdResponse{
						PaymentInstrument: &piPb.PaymentInstrument{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "vpa",
								},
							},
						},
						Status: rpc.StatusOk(),
					},
				},
			},
			mockGetByRecurringPaymentId: []mockGetByRecurringPaymentId{{
				enable:             true,
				recurringPaymentId: "id-1",
				option: []dao2.FilterOption{dao2.WithActionsFilter([]pb.Action{pb.Action_EXECUTE}),
					dao2.WithActionStateFilter([]pb.ActionState{pb.ActionState_ACTION_SUCCESS})},
				want: []*pb.RecurringPaymentsAction{},
			}, {
				enable:             true,
				recurringPaymentId: "id-1",
				option:             []dao2.FilterOption{dao2.WithActionsFilter([]pb.Action{pb.Action_CREATE}), dao2.WithOrderByCreatedAt(true)},
				want: []*pb.RecurringPaymentsAction{
					{
						Action:          pb.Action_CREATE,
						ExpireAt:        timestampPb.New(expiryTime),
						ClientRequestId: "client req id",
						VendorRequestId: "v-1",
					},
				},
			},
			},
			mockGetByPiId: mockGetByPiId{
				enable: true,
				req:    &account_pi.GetByPiIdRequest{PiId: "pi-1"},
				want:   &account_pi.GetByPiIdResponse{AccountId: "acc-1", Status: rpc.StatusOk()},
				err:    nil,
			},

			mockGetPiByIds: []mockGetPiById{
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: "pi-1"},
					want: &piPb.GetPiByIdResponse{
						PaymentInstrument: &piPb.PaymentInstrument{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "vpa",
								},
							},
						},
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: "pi-2"},
					want: &piPb.GetPiByIdResponse{
						PaymentInstrument: &piPb.PaymentInstrument{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "vpa",
								},
							},
						},
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetMandateRequestParameters: mockGetMandateRequestParameters{
				enable: true,
				req: &mandate.GetMandateRequestParametersRequest{
					ReqId: "v-1",
				},
				want: &mandate.GetMandateRequestParametersResponse{
					MerchantRefId: "mref-1",
					RefUrl:        "refUrl",
					Status:        rpc.StatusOk(),
				},
			},
			want: &pb.GetRecurringPaymentDetailsByIdResponse{
				RecurringPayment: recurringPayment3,
				Name: &commontypes.Name{
					FirstName: "first",
					LastName:  "last",
				},
				ImageUrl: "Img",
				AdditionalParams: &pb.GetRecurringPaymentDetailsByIdResponse_UpiMandateParams{
					UpiMandateParams: &pb.UPIMandateParams{
						Umn: "abc",
						Vpa: "vpa",
						Urn: "upi://collect?am=0&amrule=MAX&mode=11&note=Mandate%20execution&pa=vpa&umn=abc&validityend=03012021&validitystart=01012021",
					},
				},
				StateParams: &pb.GetRecurringPaymentDetailsByIdResponse_PendingActionParams{
					PendingActionParams: &pb.PendingActionParams{
						Expiry:          timestampPb.New(expiryTime),
						ClientRequestId: "client req id",
						PendingRequestActions: []*pb.RecurringPaymentPendingRequestAction{
							{PendingAction: pb.PendingRequestAction_AUTHORIZE, IsAuthRequired: true},
							{PendingAction: pb.PendingRequestAction_DECLINE, IsAuthRequired: false},
						},
						CredBlockType: pb.CredBlockType_NPCI,
						AdditionalParams: &pb.PendingActionParams_UpdatedParams{
							UpdatedParams: nil,
						},
						TransactionId: "v-1",
						TransactionAttributes: []*pb.TransactionAttribute{
							{
								PayerAccountId:                "acc-1",
								PaymentProtocol:               paymentPb.PaymentProtocol_UPI,
								PayeePaymentInstrument:        "vpa",
								PayerPaymentInstrument:        "vpa",
								DisplayPayeePaymentInstrument: "vpa",
								Amount:                        money.ZeroINR().Pb,
								PayeeActorName:                "payee",
								TransactionId:                 "v-1",
								MerchantRefId:                 "mref-1",
								ReferenceUrl:                  "refUrl",
							},
						},
					},
				},
				SecondaryActorId: "actor-2",
				Status:           rpc.StatusOk(),
			},
		},
		{
			name: "get SI successfully in activated state",
			req: &pb.GetRecurringPaymentDetailsByIdRequest{
				RecurringPaymentId: "id-1",
				CurrentActorId:     "actor-1",
			},
			mockGetById: mockGetById{
				enable: true,
				reqId:  "id-1",
				want:   recurringPayment4,
			},
			mockGetEntityDetailsByActorId: []mockGetEntityDetailsByActorId{{
				enable: true,
				req: &actor.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-2",
				},
				want: &actor.GetEntityDetailsByActorIdResponse{
					Name: &commontypes.Name{
						FirstName: "first",
						LastName:  "last",
					},
					ProfileImageUrl: "Img",
					Status:          rpc.StatusOk(),
				}},
			},
			mockGetByRecurringPaymentId: []mockGetByRecurringPaymentId{
				{
					enable:             true,
					recurringPaymentId: "id-1",
					option: []dao2.FilterOption{dao2.WithActionsFilter([]pb.Action{pb.Action_EXECUTE}),
						dao2.WithActionStateFilter([]pb.ActionState{pb.ActionState_ACTION_SUCCESS})},
					want: []*pb.RecurringPaymentsAction{},
				},
			},
			want: &pb.GetRecurringPaymentDetailsByIdResponse{
				RecurringPayment: recurringPayment4,
				Name: &commontypes.Name{
					FirstName: "first",
					LastName:  "last",
				},
				ImageUrl:         "Img",
				SupportedActions: []pb.Action{pb.Action_REVOKE, pb.Action_MODIFY},
				SecondaryActorId: "actor-2",
				Status:           rpc.StatusOk(),
			},
		},
		{
			name: "Pause not allowed for mcc",
			req: &pb.GetRecurringPaymentDetailsByIdRequest{
				RecurringPaymentId: "id-1",
				CurrentActorId:     "actor-1",
			},
			mockGetById: mockGetById{
				enable: true,
				reqId:  "id-1",
				want:   recurringPayment,
			},
			mockGetMandate: mockGetMandate{
				enable: true,
				req: &mandate.GetMandateRequest{
					Identifier: &mandate.GetMandateRequest_RecurringPaymentId{RecurringPaymentId: "id-1"},
				},
				res: &mandate.GetMandateResponse{
					Status: rpc.StatusOk(),
					Mandate: &mandate.MandateEntity{
						Revokeable: false,
						Umn:        "abc",
					},
				},
				err: nil,
			},
			mockGetEntityDetailsByActorId: []mockGetEntityDetailsByActorId{{
				enable: true,
				req: &actor.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-2",
				},
				want: &actor.GetEntityDetailsByActorIdResponse{
					Name: &commontypes.Name{
						FirstName: "first",
						LastName:  "last",
					},
					ProfileImageUrl: "Img",
					Status:          rpc.StatusOk(),
				}},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-2",
					},
					want: &piPb.GetPiByIdResponse{
						PaymentInstrument: &piPb.PaymentInstrument{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "vpa",
									MerchantDetails: &piPb.Upi_MerchantDetails{
										Mcc: "7322",
									},
								},
							},
						},
						Status: rpc.StatusOk(),
					},
				},
				{
					enable: true,
					req: &piPb.GetPiByIdRequest{
						Id: "pi-2",
					},
					want: &piPb.GetPiByIdResponse{
						PaymentInstrument: &piPb.PaymentInstrument{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa: "vpa",
									MerchantDetails: &piPb.Upi_MerchantDetails{
										Mcc: "7322",
									},
								},
							},
						},
						Status: rpc.StatusOk(),
					},
				},
			},
			mockGetByRecurringPaymentId: []mockGetByRecurringPaymentId{
				{
					enable:             true,
					recurringPaymentId: "id-1",
					option: []dao2.FilterOption{dao2.WithActionsFilter([]pb.Action{pb.Action_EXECUTE}),
						dao2.WithActionStateFilter([]pb.ActionState{pb.ActionState_ACTION_SUCCESS})},
					want: []*pb.RecurringPaymentsAction{},
				},
			},
			want: &pb.GetRecurringPaymentDetailsByIdResponse{
				RecurringPayment: recurringPayment,
				Name: &commontypes.Name{
					FirstName: "first",
					LastName:  "last",
				},
				ImageUrl: "Img",
				AdditionalParams: &pb.GetRecurringPaymentDetailsByIdResponse_UpiMandateParams{
					UpiMandateParams: &pb.UPIMandateParams{
						Umn: "abc",
						Vpa: "vpa",
						Urn: "upi://collect?am=0&amrule=MAX&mode=11&note=Mandate%20execution&pa=vpa&umn=abc&validityend=03012021&validitystart=01012021",
					},
				},
				SecondaryActorId: "actor-2",
				Status:           rpc.StatusOk(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.mockGetById.enable {
				mockRecurringPaymentDao.EXPECT().GetById(context.Background(), tt.mockGetById.reqId).Return(tt.mockGetById.want, tt.mockGetById.err)
			}

			for _, t := range tt.mockGetEntityDetailsByActorId {
				if t.enable {
					mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), t.req).Return(t.want, t.err)
				}
			}

			for _, t := range tt.mockGetByRecurringPaymentId {
				if t.enable {
					mockRecurringPaymentActionsDao.EXPECT().GetByRecurringPaymentId(context.Background(), t.recurringPaymentId,
						newFilterOptionMatcher(t.option)).
						Return(t.want, t.err)
				}
			}

			if tt.mockGetMandate.enable {
				mockUpiMandateClient.EXPECT().GetMandate(context.Background(), tt.mockGetMandate.req).
					Return(tt.mockGetMandate.res, tt.mockGetMandate.err)
			}

			if tt.mockGetCredBlockType.enable {
				mockRecurringPaymentProcessor.EXPECT().GetCredBlockType(tt.mockGetCredBlockType.rpType, tt.mockGetCredBlockType.actorRole).
					Return(true, tt.mockGetCredBlockType.credBlockType, tt.mockGetCredBlockType.err)
			}

			for _, mock := range tt.mockGetPiById {
				if mock.enable {
					mockPiClient.EXPECT().GetPiById(context.Background(), mock.req).Return(
						mock.want, mock.err)
				}
			}

			if tt.mockGetByPiId.enable {
				mockAccountPIRelationsClient.EXPECT().GetByPiId(context.Background(), tt.mockGetByPiId.req).Return(
					tt.mockGetByPiId.want, tt.mockGetByPiId.err)
			}

			for _, t := range tt.mockGetPiByIds {
				if t.enable {
					mockPiClient.EXPECT().GetPiById(context.Background(), t.req).Return(t.want, t.err)
				}
			}

			if tt.mockGetMandateRequestParameters.enable {
				mockUpiMandateClient.EXPECT().GetMandateRequestParameters(context.Background(), tt.mockGetMandateRequestParameters.req).
					Return(tt.mockGetMandateRequestParameters.want, tt.mockGetMandateRequestParameters.err)
			}

			got, err := svc.GetRecurringPaymentDetailsById(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecurringPaymentDetailsById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRecurringPaymentDetailsById() got = %v, want %v", got, tt.want)
			}
		})
	}

}

func TestService_GetRecurringPaymentDetailsByExternalId(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)

	mockRecurringPaymentVendorDetailsDao := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)
	svc := NewService(mockRecurringPaymentDao, nil, nil, nil, nil, nil, nil, nil, nil, conf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockRecurringPaymentVendorDetailsDao, nil, nil, nil, nil)

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))

	recurringPayment := &pb.RecurringPayment{
		Id:          "id-1",
		ExternalId:  "ext-id-5",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_UPI,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
		InitiatedBy:              pb.InitiatedBy_PAYER,
	}

	type mockGetByExternalId struct {
		enable     bool
		externalId string
		want       *pb.RecurringPayment
		err        error
	}

	tests := []struct {
		name                string
		req                 *pb.GetRecurringPaymentByExternalIdRequest
		mockGetByExternalId mockGetByExternalId
		want                *pb.GetRecurringPaymentByExternalIdResponse
		wantErr             bool
	}{
		{
			name: "get recurring payment details successfully",
			req: &pb.GetRecurringPaymentByExternalIdRequest{
				ExternalId: "id-1",
			},
			mockGetByExternalId: mockGetByExternalId{
				enable:     true,
				externalId: "id-1",
				want:       recurringPayment,
			},
			want: &pb.GetRecurringPaymentByExternalIdResponse{
				RecurringPayment: recurringPayment,
				Status:           rpc.StatusOk(),
			},
		},
		{
			name: "get recurring payment invalid External Id",
			req: &pb.GetRecurringPaymentByExternalIdRequest{
				ExternalId: "id-2",
			},
			mockGetByExternalId: mockGetByExternalId{
				enable:     true,
				externalId: "id-2",
				err:        errors.New("invalid External Id"),
			},
			want: &pb.GetRecurringPaymentByExternalIdResponse{
				RecurringPayment: nil,
				Status:           rpc.StatusInternal(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.mockGetByExternalId.enable {
				mockRecurringPaymentDao.EXPECT().GetByExternalId(context.Background(), tt.mockGetByExternalId.externalId).Return(tt.mockGetByExternalId.want, tt.mockGetByExternalId.err)
			}

			got, err := svc.GetRecurringPaymentByExternalId(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecurringPaymentDetailsById() gotErr :%v, wantErr :%v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRecurringPaymentDetailsById() got = %v, want %v", got, tt.want)
			}
		})
	}

}
