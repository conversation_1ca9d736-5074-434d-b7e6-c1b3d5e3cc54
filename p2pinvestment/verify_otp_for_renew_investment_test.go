package p2pinvestment_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/p2pinvestment"
	vgP2pPb "github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	p2pVgMocks "github.com/epifi/gamma/api/vendorgateway/investments/p2p/mocks"
	p2p "github.com/epifi/gamma/p2pinvestment"
	daoMocks "github.com/epifi/gamma/p2pinvestment/dao/mocks"

	p2pIPb "github.com/epifi/gamma/api/p2pinvestment"
)

var (
	testActorId   = "testActor"
	testOtp       = "testOtp"
	testRenewalId = "TRAN5765432"

	schemeList1 = []*p2pIPb.Scheme{
		{
			Id:             "invest-scheme-flexi-7-id",
			VendorSchemeId: "421",
			Vendor:         p2pIPb.Vendor_VENDOR_LIQUILOANS,
			Details: &p2pIPb.SchemeDetails{
				InvestmentRoi:      7,
				LockInTenureInDays: 90,
				TenureInDays:       90,
				EligibilityDetails: &p2pIPb.SchemeEligibilityDetails{
					DefaultAvgBalanceThreshold: 5000,
				},
				WithdrawalConstraints: &p2pIPb.SchemeWithdrawalConstraints{
					HardLockInTerm: &p2pIPb.Term{
						Months: 3,
					},
					SoftLockInTerm: &p2pIPb.Term{
						Months: 3,
					},
				},
				Tenure: &p2pIPb.Term{
					Months: 3,
				},
			},
			Name: p2pIPb.SchemeName_SCHEME_NAME_LL_FLEXI,
		},
	}

	defaultInvestor1 = &p2pIPb.Investor{
		ActorId:          testActor,
		Vendor:           p2pIPb.Vendor_VENDOR_LIQUILOANS,
		VendorInvestorId: "421",
		Details: &p2pIPb.InvestorDetails{
			EligibilityDetails: nil,
			SchemeEligibilityDetails: &p2pIPb.InvestorDetails_SchemeEligibilityDetails{
				EligibleForAll: true,
			},
		},
		TotalInvestmentCount: 1000,
		Status:               p2pIPb.InvestorStatus_INVESTOR_STATUS_CREATION_PENDING,
	}
	defaultMaturity = &p2pIPb.MaturityAction{
		Id:                      "ID123",
		InvestmentTransactionId: testRenewalId,
		ActorId:                 testActor,
		SchemeName:              1,
		Status:                  0,
		SubStatus:               0,
		Type:                    p2pIPb.MaturityActionType_MATURITY_ACTION_TYPE_PRINCIPAL_AND_INTEREST_REINVESTMENT,
	}

	defaultInvestmentTrans = &p2pIPb.InvestmentTransaction{
		SchemeId:            scheme1,
		VendorTransactionId: "TRANSID",
	}

	defaultMaturityActionRequest = &vgP2pPb.CreateMaturityActionRequest{
		Header:                &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
		InvestorId:            defaultInvestor1.VendorInvestorId,
		Otp:                   testOtp,
		LinkToken:             defaultMaturity.LinkToken,
		PortfolioId:           defaultInvestmentTrans.VendorTransactionId,
		MaturityType:          vgP2pPb.MaturityType_MATURITY_TYPE_PRINCIPAL_AND_INTEREST_REINVESTMENT,
		SchemeId:              schemeList[0].VendorSchemeId,
		Mhp:                   3,
		PayoutType:            vgP2pPb.PayoutType_PAYOUT_TYPE_GROWTH,
		DoubleAdvantageScheme: false,
	}

	defaultMaturityActionResponseStatusOk = &vgP2pPb.CreateMaturityActionResponse{
		Status:    rpc.StatusOk(),
		OtpStatus: vgP2pPb.OtpStatus_OTP_STATUS_OK,
	}

	defaultMaturityActionResponseStatusNotOk = &vgP2pPb.CreateMaturityActionResponse{
		Status:    rpc.StatusOk(),
		OtpStatus: vgP2pPb.OtpStatus_OTP_STATUS_OTP_INCORRECT,
	}

	defaultMaturityActionResponseStatusDefault = &vgP2pPb.CreateMaturityActionResponse{
		Status:    rpc.StatusOk(),
		OtpStatus: vgP2pPb.OtpStatus_OTP_STATUS_OTP_VERIFY_LIMIT_EXCEEDED,
	}
)

func TestService_VerifyOTPForRenewInvestment(t *testing.T) {
	// Skipping until we add maturity consent handling
	t.Skip()
	ctr := gomock.NewController(t)
	ctx := context.Background()

	mockP2pVG := p2pVgMocks.NewMockP2PClient(ctr)

	// Dao mocks
	mockInvestorDao := daoMocks.NewMockInvestorDao(ctr)
	mockMaturityDao := daoMocks.NewMockMaturityActionDao(ctr)
	mockSchemeDao := daoMocks.NewMockSchemeDao(ctr)
	mockInvestmentTracDao := daoMocks.NewMockInvestmentTransactionDao(ctr)
	type fields struct {
		p2pVgClient vgP2pPb.P2PClient
	}
	type args struct {
		ctx   context.Context
		req   *p2pIPb.VerifyOTPForRenewInvestmentRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *p2pIPb.VerifyOTPForRenewInvestmentResponse
		wantErr bool
	}{
		{
			name: "#1 Not able to fetch investor",
			args: args{
				ctx: ctx,
				req: &p2pIPb.VerifyOTPForRenewInvestmentRequest{
					ActorId:                    testActorId,
					Otp:                        testOtp,
					RenewalInvestmentRequestId: testRenewalId,
				},
				mocks: []interface{}{
					mockInvestorDao.EXPECT().GetByActorIdAndVendor(gomock.Any(), testActorId, p2pinvestment.Vendor_VENDOR_LIQUILOANS).Return(nil, errors.New("no actor id is present")),
				},
			},
			want: &p2pIPb.VerifyOTPForRenewInvestmentResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "#2 Not able to fetch Maturity Action",
			args: args{
				ctx: ctx,
				req: &p2pIPb.VerifyOTPForRenewInvestmentRequest{
					ActorId:                    testActorId,
					Otp:                        testOtp,
					RenewalInvestmentRequestId: testRenewalId,
				},
				mocks: []interface{}{
					mockInvestorDao.EXPECT().GetByActorIdAndVendor(gomock.Any(), testActorId, p2pinvestment.Vendor_VENDOR_LIQUILOANS).Return(defaultInvestor1, nil),
					mockMaturityDao.EXPECT().GetById(gomock.Any(), testRenewalId).Return(nil, errors.New("fail to fetch maturity action using by id")),
				},
			},
			want: &p2pIPb.VerifyOTPForRenewInvestmentResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "#3 failed to fetch scheme details by scheme name",
			args: args{
				ctx: ctx,
				req: &p2pIPb.VerifyOTPForRenewInvestmentRequest{
					ActorId:                    testActorId,
					Otp:                        testOtp,
					RenewalInvestmentRequestId: testRenewalId,
				},
				mocks: []interface{}{
					mockInvestorDao.EXPECT().GetByActorIdAndVendor(gomock.Any(), testActorId, p2pinvestment.Vendor_VENDOR_LIQUILOANS).Return(defaultInvestor1, nil),
					mockMaturityDao.EXPECT().GetById(gomock.Any(), testRenewalId).Return(defaultMaturity, nil),
					mockSchemeDao.EXPECT().GetByName(gomock.Any(), p2pIPb.SchemeName_SCHEME_NAME_LL_DEFAULT).Return(nil, errors.New("failed to fetch scheme details by scheme name")),
				},
			},
			want: &p2pIPb.VerifyOTPForRenewInvestmentResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "#4 failed to fetch investment txn by client req id",
			args: args{
				ctx: ctx,
				req: &p2pIPb.VerifyOTPForRenewInvestmentRequest{
					ActorId:                    testActorId,
					Otp:                        testOtp,
					RenewalInvestmentRequestId: testRenewalId,
				},
				mocks: []interface{}{
					mockInvestorDao.EXPECT().GetByActorIdAndVendor(gomock.Any(), testActorId, p2pinvestment.Vendor_VENDOR_LIQUILOANS).Return(defaultInvestor1, nil),
					mockMaturityDao.EXPECT().GetById(gomock.Any(), testRenewalId).Return(defaultMaturity, nil),
					mockSchemeDao.EXPECT().GetByName(gomock.Any(), p2pIPb.SchemeName_SCHEME_NAME_LL_DEFAULT).Return(schemeList1[0], nil),
					mockInvestmentTracDao.EXPECT().GetById(ctx, defaultMaturity.InvestmentTransactionId).Return(nil, errors.New("failed to fetch investment txn by client req id")),
				},
			},
			want: &p2pIPb.VerifyOTPForRenewInvestmentResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "#5 error in verifying OTP",
			args: args{
				ctx: ctx,
				req: &p2pIPb.VerifyOTPForRenewInvestmentRequest{
					ActorId:                    testActorId,
					Otp:                        testOtp,
					RenewalInvestmentRequestId: testRenewalId,
				},
				mocks: []interface{}{
					mockInvestorDao.EXPECT().GetByActorIdAndVendor(gomock.Any(), testActorId, p2pinvestment.Vendor_VENDOR_LIQUILOANS).Return(defaultInvestor1, nil),
					mockMaturityDao.EXPECT().GetById(gomock.Any(), testRenewalId).Return(defaultMaturity, nil),
					mockSchemeDao.EXPECT().GetByName(gomock.Any(), p2pIPb.SchemeName_SCHEME_NAME_LL_DEFAULT).Return(schemeList1[0], nil),
					mockInvestmentTracDao.EXPECT().GetById(gomock.Any(), defaultMaturity.InvestmentTransactionId).Return(defaultInvestmentTrans, nil),
					mockP2pVG.EXPECT().CreateMaturityAction(gomock.Any(), defaultMaturityActionRequest).Return(nil, errors.New("error in making renew investment request to vendor")),
					mockMaturityDao.EXPECT().Update(gomock.Any(), defaultMaturity, []p2pIPb.MaturityActionFieldMask{
						p2pIPb.MaturityActionFieldMask_MATURITY_ACTION_FIELD_MASK_SUB_STATUS,
					}).Return(nil),
				},
			},
			want: &p2pIPb.VerifyOTPForRenewInvestmentResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "#6 No error in verifying OTP",
			args: args{
				ctx: ctx,
				req: &p2pIPb.VerifyOTPForRenewInvestmentRequest{
					ActorId:                    testActorId,
					Otp:                        testOtp,
					RenewalInvestmentRequestId: testRenewalId,
				},
				mocks: []interface{}{
					mockInvestorDao.EXPECT().GetByActorIdAndVendor(gomock.Any(), testActorId, p2pinvestment.Vendor_VENDOR_LIQUILOANS).Return(defaultInvestor1, nil),
					mockMaturityDao.EXPECT().GetById(gomock.Any(), testRenewalId).Return(defaultMaturity, nil),
					mockSchemeDao.EXPECT().GetByName(gomock.Any(), p2pIPb.SchemeName_SCHEME_NAME_LL_DEFAULT).Return(schemeList1[0], nil),
					mockInvestmentTracDao.EXPECT().GetById(gomock.Any(), defaultMaturity.InvestmentTransactionId).Return(defaultInvestmentTrans, nil),
					mockP2pVG.EXPECT().CreateMaturityAction(gomock.Any(), defaultMaturityActionRequest).Return(defaultMaturityActionResponseStatusOk, nil),
					mockMaturityDao.EXPECT().Update(gomock.Any(), defaultMaturity, []p2pIPb.MaturityActionFieldMask{
						p2pIPb.MaturityActionFieldMask_MATURITY_ACTION_FIELD_MASK_STATUS,
						p2pIPb.MaturityActionFieldMask_MATURITY_ACTION_FIELD_MASK_SUB_STATUS,
					}).Return(nil).AnyTimes(),
				},
			},
			want: &p2pIPb.VerifyOTPForRenewInvestmentResponse{
				OtpStatus:          p2pIPb.OtpStatus_OK,
				MaturityActionType: p2pIPb.MaturityActionType_MATURITY_ACTION_TYPE_PRINCIPAL_AND_INTEREST_REINVESTMENT,
				Status:             rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "#7 OTP doesn't match",
			args: args{
				ctx: ctx,
				req: &p2pIPb.VerifyOTPForRenewInvestmentRequest{
					ActorId:                    testActorId,
					Otp:                        testOtp,
					RenewalInvestmentRequestId: testRenewalId,
				},
				mocks: []interface{}{
					mockInvestorDao.EXPECT().GetByActorIdAndVendor(gomock.Any(), testActorId, p2pinvestment.Vendor_VENDOR_LIQUILOANS).Return(defaultInvestor1, nil),
					mockMaturityDao.EXPECT().GetById(gomock.Any(), testRenewalId).Return(defaultMaturity, nil),
					mockSchemeDao.EXPECT().GetByName(gomock.Any(), p2pIPb.SchemeName_SCHEME_NAME_LL_DEFAULT).Return(schemeList1[0], nil),
					mockInvestmentTracDao.EXPECT().GetById(gomock.Any(), defaultMaturity.InvestmentTransactionId).Return(defaultInvestmentTrans, nil),
					mockP2pVG.EXPECT().CreateMaturityAction(gomock.Any(), defaultMaturityActionRequest).Return(defaultMaturityActionResponseStatusNotOk, nil),
					mockMaturityDao.EXPECT().Update(gomock.Any(), defaultMaturity, []p2pIPb.MaturityActionFieldMask{
						p2pIPb.MaturityActionFieldMask_MATURITY_ACTION_FIELD_MASK_SUB_STATUS,
					}).Return(nil),
				},
			},
			want: &p2pIPb.VerifyOTPForRenewInvestmentResponse{
				OtpStatus: p2pIPb.OtpStatus_OTP_INCORRECT,
				Status:    rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "#8 OTP Limit Exceeded",
			args: args{
				ctx: ctx,
				req: &p2pIPb.VerifyOTPForRenewInvestmentRequest{
					ActorId:                    testActorId,
					Otp:                        testOtp,
					RenewalInvestmentRequestId: testRenewalId,
				},
				mocks: []interface{}{
					mockInvestorDao.EXPECT().GetByActorIdAndVendor(gomock.Any(), testActorId, p2pinvestment.Vendor_VENDOR_LIQUILOANS).Return(defaultInvestor1, nil),
					mockMaturityDao.EXPECT().GetById(gomock.Any(), testRenewalId).Return(defaultMaturity, nil),
					mockSchemeDao.EXPECT().GetByName(gomock.Any(), p2pIPb.SchemeName_SCHEME_NAME_LL_DEFAULT).Return(schemeList1[0], nil),
					mockInvestmentTracDao.EXPECT().GetById(gomock.Any(), defaultMaturity.InvestmentTransactionId).Return(defaultInvestmentTrans, nil),
					mockP2pVG.EXPECT().CreateMaturityAction(gomock.Any(), defaultMaturityActionRequest).Return(defaultMaturityActionResponseStatusDefault, nil),
				},
			},
			want: &p2pIPb.VerifyOTPForRenewInvestmentResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := p2p.NewService(mockInvestmentTracDao, mockSchemeDao, nil, mockP2pVG, mockInvestorDao, nil, nil,
				nil, nil, nil, nil, nil, nil,
				nil, nil, nil, nil, nil, nil,
				nil, nil, mockMaturityDao, nil, nil, nil,
				nil, nil, nil, nil, nil, nil, nil, nil, nil)

			got, err := s.VerifyOTPForRenewInvestment(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("VerifyOTPForRenewInvestment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("VerifyOTPForRenewInvestment() got = %v, want %v", got, tt.want)
			}
		})
	}
}
