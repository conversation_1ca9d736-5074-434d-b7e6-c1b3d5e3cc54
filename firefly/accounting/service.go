package accounting

import (
	"context"
	"fmt"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/pagination"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	depositPb "github.com/epifi/gamma/api/deposit"
	ffBePb "github.com/epifi/gamma/api/firefly"
	faPb "github.com/epifi/gamma/api/firefly/accounting"
	ffAccEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	billingPb "github.com/epifi/gamma/api/firefly/billing"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	paymentinstrumentPb "github.com/epifi/gamma/api/paymentinstrument"
	types "github.com/epifi/gamma/api/typesv2"
	ccVgPb "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	"github.com/epifi/gamma/firefly/accounting/dao"
	"github.com/epifi/gamma/firefly/accounting/dao/options"
	"github.com/epifi/gamma/firefly/accounting/helper"
	ffdao "github.com/epifi/gamma/firefly/dao"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

const (
	// max page size for fetching transactions
	// TODO(priyansh) : Move this to dynamic config
	maxSize = 30
)

type Service struct {
	faPb.UnimplementedAccountingServer
	creditAccountDao               dao.CreditAccountDao
	creditCardTransactionDao       dao.CreditCardTransactionDao
	creditCardTxnAdditionalInfoDao dao.TransactionAdditionalInfoDao
	disputedTransactionDao         dao.DisputedTransactionDao
	piClient                       paymentinstrumentPb.PiClient
	actorClient                    actorPb.ActorClient
	txnCategorizerClient           categorizerPb.TxnCategorizerClient
	creditCardDao                  ffdao.CreditCardDao
	billingClient                  billingPb.BillingClient
	ccVgClient                     ccVgPb.CreditCardClient
	bcClient                       bankcust.BankCustomerServiceClient
	fireflyClient                  ffBePb.FireflyClient
	depositClient                  depositPb.DepositClient
}

func NewService(
	creditAccountDao dao.CreditAccountDao,
	creditCardTransactionDao dao.CreditCardTransactionDao,
	creditCardTxnAdditionalInfoDao dao.TransactionAdditionalInfoDao,
	disputedTransactionDao dao.DisputedTransactionDao,
	piClient paymentinstrumentPb.PiClient,
	actorClient actorPb.ActorClient,
	txnCategorizerClient categorizerPb.TxnCategorizerClient,
	creditCardDao ffdao.CreditCardDao,
	billingClient billingPb.BillingClient,
	ccVgClient ccVgPb.CreditCardClient,
	bcClient bankcust.BankCustomerServiceClient,
	fireflyClient ffBePb.FireflyClient,
	depositClient depositPb.DepositClient,
) *Service {
	return &Service{
		creditAccountDao:               creditAccountDao,
		creditCardTransactionDao:       creditCardTransactionDao,
		creditCardTxnAdditionalInfoDao: creditCardTxnAdditionalInfoDao,
		piClient:                       piClient,
		actorClient:                    actorClient,
		txnCategorizerClient:           txnCategorizerClient,
		creditCardDao:                  creditCardDao,
		disputedTransactionDao:         disputedTransactionDao,
		billingClient:                  billingClient,
		ccVgClient:                     ccVgClient,
		bcClient:                       bcClient,
		fireflyClient:                  fireflyClient,
		depositClient:                  depositClient,
	}
}

func (s *Service) CreateAccount(ctx context.Context, req *faPb.CreateAccountRequest) (res *faPb.CreateAccountResponse, returnErr error) {
	returnErr = nil
	res = &faPb.CreateAccountResponse{
		Status: rpc.StatusInternal(),
	}
	ca, err := s.creditAccountDao.Create(ctx, &faPb.CreditAccount{
		ActorId:           req.GetActorId(),
		ReferenceId:       req.GetReferenceId(),
		TotalLimit:        getAmountInFloat(req.GetTotalLimit()),
		CardProgram:       req.GetCardProgram(),
		CollateralDetails: req.GetCollateralDetails(),
	})
	if err != nil {
		// in case of account is already created for the given actor and ref id return already exists
		if errors.Is(err, epifierrors.ErrAlreadyExists) {
			res.Status = rpc.StatusAlreadyExists()
			return
		}
		// if any other error than already exists log and return internal
		logger.Error(ctx, "failed to create credit account in db", zap.Error(err))
		return
	}
	res.Status = rpc.StatusOk()
	res.Account = ca
	return
}

func (s *Service) GetAccount(ctx context.Context, req *faPb.GetAccountRequest) (res *faPb.GetAccountResponse, returnErr error) {
	returnErr = nil
	res = &faPb.GetAccountResponse{
		Status: rpc.StatusInternal(),
	}
	var ca *faPb.CreditAccount
	var err error
	switch req.GetGetBy().(type) {
	case *faPb.GetAccountRequest_AccountId:
		ca, err = s.creditAccountDao.GetById(ctx, req.GetAccountId(), nil)
	case *faPb.GetAccountRequest_ByActorIdAndRefId:
		ca, err = s.creditAccountDao.GetByActorIdAndRefId(ctx, req.GetByActorIdAndRefId().GetActorId(), req.GetByActorIdAndRefId().GetReferenceId(), nil)
	case *faPb.GetAccountRequest_ReferenceId:
		ca, err = s.creditAccountDao.GetByRefId(ctx, req.GetReferenceId(), nil)
	}
	if err != nil {
		// in case of no record found for the given criteria return status RecordNotFound
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return
		}
		// if any other error than not found log and return internal
		logger.Error(ctx, "failed to get credit account from db", zap.Error(err))
		return
	}
	res.Status = rpc.StatusOk()
	res.Account = ca
	return
}

func (s *Service) GetAccounts(ctx context.Context, req *faPb.GetAccountsRequest) (*faPb.GetAccountsResponse, error) {
	res := &faPb.GetAccountsResponse{}
	accounts := make([]*faPb.CreditAccount, 0)
	var err error

	switch req.GetGetBy().(type) {
	case *faPb.GetAccountsRequest_ActorId:
		accounts, err = s.creditAccountDao.GetByActorId(ctx, req.GetActorId(), nil)
	default:
	}
	if err != nil {
		// in case of no record found for the given criteria return status RecordNotFound
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		// if any other error than not found log and return internal
		logger.Error(ctx, "failed to get credit accounts from db", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Accounts = accounts
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) PostTransaction(ctx context.Context, req *faPb.PostTransactionRequest) (res *faPb.PostTransactionResponse, returnErr error) {
	returnErr = nil
	res = &faPb.PostTransactionResponse{
		Status: rpc.StatusInternal(),
	}
	req.GetTransaction().AccountId = req.AccountId
	_, err := s.creditCardTransactionDao.Create(ctx, req.GetTransaction())
	if err != nil {
		if errors.Is(err, epifierrors.ErrDuplicateEntry) {
			res.Status = rpc.StatusAlreadyExists()
			return
		}
		logger.Error(ctx, "error in creditCardTransactionDao create", zap.Error(err))
		return
	}
	res.Status = rpc.StatusOk()
	return
}

func (s *Service) GetTransactions(ctx context.Context, req *faPb.GetTransactionsRequest) (res *faPb.GetTransactionsResponse, returnErr error) {
	returnErr = nil
	res = &faPb.GetTransactionsResponse{
		Status: rpc.StatusInternal(),
	}
	// there can be two types params
	// 	1. for which max one txn to be returned
	// 	2. for which more than one txn also can be returned
	// based on the param type, inside switch case appropriate dao method will be called
	// and *faPb.CardTransaction or []*faPb.CardTransaction variable will be set
	var cTxn *faPb.CardTransaction
	var cTxns []*faPb.CardTransaction
	var err error
	switch req.GetGetBy().(type) {
	case *faPb.GetTransactionsRequest_TransactionId:
		cTxn, err = s.creditCardTransactionDao.GetById(ctx, req.GetTransactionId(), nil)
	case *faPb.GetTransactionsRequest_DedupeId:
		cTxn, err = s.creditCardTransactionDao.GetByDedupeId(ctx, req.GetDedupeId(), nil)
	case *faPb.GetTransactionsRequest_AccountId:
		cTxns, err = s.creditCardTransactionDao.GetByAccountId(ctx, req.GetAccountId(), nil)
	case *faPb.GetTransactionsRequest_BatchExternalTxnIds:
		cTxns, err = s.creditCardTransactionDao.GetByExternalTxnIds(ctx, req.GetBatchExternalTxnIds().GetExternalTxnIds(), nil)
	case *faPb.GetTransactionsRequest_BatchTxnIds:
		cTxns, err = s.creditCardTransactionDao.GetByIds(ctx, req.GetBatchTxnIds().GetTxnIds(), nil)
	case *faPb.GetTransactionsRequest_VendorExternalTransactionId:
		cTxn, err = s.creditCardTransactionDao.GetByVendorExternalTxnId(ctx, req.GetVendorExternalTransactionId(), nil)
	// Deprecated: do not use
	case *faPb.GetTransactionsRequest_BatchDedupeIdGenerationParameter:
		dedupeIds, dedupeErr := generateDedupeIds(req.GetBatchDedupeIdGenerationParameter())
		if dedupeErr != nil {
			logger.Error(ctx, "Failed to fetch dedupe ids from dedupe id generation params ", zap.Error(dedupeErr))
			return
		}
		cTxns, err = s.creditCardTransactionDao.GetByDedupeIds(ctx, dedupeIds, nil)
	case *faPb.GetTransactionsRequest_AccountIdAndCategories:
		// Loop through each transaction category provided in the request
		for _, category := range req.GetAccountIdAndCategories().GetTransactionCategories() {
			// Fetch transactions for the given account ID and category
			transactions, txnErr := s.creditCardTransactionDao.GetByAccountIdAndTxnCategory(ctx, req.GetAccountIdAndCategories().GetAccountId(), category, nil)

			// Check if there's an error and it's not a "record not found" error
			switch {
			case errors.Is(txnErr, epifierrors.ErrRecordNotFound):
				continue
			case txnErr != nil:
				// If there's an error other than "record not found", set the error and break the loop
				err = txnErr
				break
			default:
				// do nothing
			}

			// If transactions are found for the category, append them to the result
			if len(transactions) > 0 {
				cTxns = append(cTxns, transactions...)
			}
		}

		// If no transactions were found for any category, set the error to "record not found"
		if len(cTxns) == 0 {
			err = epifierrors.ErrRecordNotFound
		}
	}

	if err != nil {
		// in case of no record found for the given criteria return status RecordNotFound
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return
		}
		// if any other error than not found log and return internal
		logger.Error(ctx, "failed to fetch card txns from db", zap.Error(err))
		return
	}
	// add only if cTxn is not nil
	if cTxn != nil {
		res.Transactions = append(res.Transactions, cTxn)
	}
	// add only if cTxns is not nil and is has atleast one entry
	if cTxns != nil && len(cTxns) > 0 {
		res.Transactions = append(res.Transactions, cTxns...)
	}
	res.Status = rpc.StatusOk()
	return
}

// generateDedupeIds function for fetching the dedupe id for successful txns
func generateDedupeIds(dedupeIdGenerationParams *faPb.BatchDedupeIdGenerationParameter) ([]string, error) {
	dedupeIds := make([]string, 0)
	for _, dedupeIdGenerationParam := range dedupeIdGenerationParams.GetDedupeIdGenerationParameters() {
		dedupeId, err := helper.GenerateDedupeIdV2(dedupeIdGenerationParam.GetRrnDedupeIdentifier().GetRrn(),
			dedupeIdGenerationParam.GetExternalTxnIdDedupeIdentifier().GetExternalTxnId(),
			dedupeIdGenerationParam.GetEntityIdAndAmountIdentifier().GetEntityId(), ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS)
		if err != nil {
			return nil, err
		}
		dedupeIds = append(dedupeIds, dedupeId)
	}
	return dedupeIds, nil
}

func (s *Service) GetPaginatedTxns(ctx context.Context, req *faPb.GetPaginatedTxnsRequest) (*faPb.GetPaginatedTxnsResponse, error) {
	var (
		res       = &faPb.GetPaginatedTxnsResponse{}
		err       error
		accountId string
	)
	switch req.GetIdentifier().(type) {
	case *faPb.GetPaginatedTxnsRequest_ActorId:
		accountId, err = s.getAccountForActor(ctx, req.GetActorId())
		if err != nil {
			logger.Error(ctx, "error in fetching account for actor", zap.Error(err))
			res.Status = rpc.StatusFromError(err)
			return res, nil
		}
	case *faPb.GetPaginatedTxnsRequest_AccountId:
		accountId = req.GetAccountId()
	}

	txnIdToTxnAndAdditionalInfoList, err := s.fetchTxnsAndAdditionalInfo(ctx, accountId,
		req.GetStatuses(), req.GetCardTransactionFieldMasks(), req.GetStartTimestamp(), req.GetPageSize(),
		req.GetOffset(), req.GetDescending(), req.GetTransactionAdditionalInfoFieldMasks(), req.GetIncludeFrmDeclineTxns())
	if err != nil {
		logger.Error(ctx, "error in fetching transactions and additional info", zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}
	for _, val := range txnIdToTxnAndAdditionalInfoList {
		res.TransactionWithAdditionalInfoList = append(res.TransactionWithAdditionalInfoList, val)
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) GetPaginatedCreditCardTxnView(ctx context.Context,
	req *faPb.GetPaginatedCreditCardTxnViewRequest) (*faPb.GetPaginatedCreditCardTxnViewResponse, error) {
	var (
		res       = &faPb.GetPaginatedCreditCardTxnViewResponse{}
		accountId string
		err       error
	)
	switch req.GetIdentifier().(type) {
	case *faPb.GetPaginatedCreditCardTxnViewRequest_ActorId:
		accountId, err = s.getAccountForActor(ctx, req.GetActorId())
		if err != nil {
			logger.Error(ctx, "error in fetching account for actor", zap.Error(err))
			res.Status = rpc.StatusFromError(err)
			return res, nil
		}
	case *faPb.GetPaginatedCreditCardTxnViewRequest_AccountId:
		accountId = req.GetAccountId()
	}
	txnIdToTxnAndAdditionalInfoList, err := s.fetchTxnsAndAdditionalInfo(ctx, accountId,
		req.GetStatuses(), nil, req.GetStartTimestamp(), req.GetPageSize(), req.GetOffset(),
		req.GetDescending(), nil, false)
	if err != nil {
		logger.Error(ctx, "error in fetching transactions and additional info", zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}
	for _, val := range txnIdToTxnAndAdditionalInfoList {
		var (
			primaryActorId string
			otherActorId   string
			cardPi         string
			otherPiId      string
		)
		if val.GetTransaction().GetTxnType() == ffAccEnumsPb.TransactionType_TRANSACTION_TYPE_CREDIT {
			primaryActorId = val.GetAdditionalInfo().GetActorTo()
			otherActorId = val.GetAdditionalInfo().GetActorFrom()
			cardPi = val.GetAdditionalInfo().GetPiTo()
			otherPiId = val.GetAdditionalInfo().GetPiFrom()
		} else {
			primaryActorId = val.GetAdditionalInfo().GetActorFrom()
			otherActorId = val.GetAdditionalInfo().GetActorTo()
			cardPi = val.GetAdditionalInfo().GetPiFrom()
			otherPiId = val.GetAdditionalInfo().GetPiTo()
		}
		res.TxnsViewModelList = append(res.TxnsViewModelList, &faPb.CreditCardTransactionViewModel{
			TransactionId:        val.GetTransaction().GetId(),
			TransactionTimestamp: val.GetTransaction().GetTxnTime(),
			Amount:               val.GetTransaction().GetAmount(),
			TransactionStatus:    val.GetTransaction().GetTxnStatus(),
			TransactionType:      val.GetTransaction().GetTxnType(),
			TransactionCategory:  val.GetTransaction().GetTxnCategory(),
			PrimaryActorId:       primaryActorId,
			OtherActorId:         otherActorId,
			CardPi:               cardPi,
			OtherActorPi:         otherPiId,
			TxnDescription:       val.GetTransaction().GetDescription(),
			ExternalTxnId:        val.GetTransaction().GetExternalTxnId(),
			BillRefId:            val.GetAdditionalInfo().GetBillRefId(),
		})
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) fetchTxnsAndAdditionalInfo(ctx context.Context, accountId string, statuses []ffAccEnumsPb.TransactionStatus,
	selectFieldMask []ffAccEnumsPb.CardTransactionFieldMask, startTimestamp *timestampPb.Timestamp, pageSize, offset int32,
	descending bool, additionalInfoSelectFieldMask []ffAccEnumsPb.TransactionAdditionalInfoFieldMask, includeFrmDeclines bool) ([]*faPb.CardTransactionWithAdditionalInfo, error) {

	daoFilterOptions := make([]storageV2.FilterOption, 0)
	if !includeFrmDeclines {
		daoFilterOptions = append(daoFilterOptions, options.WithTxnFailureTypeNotIn([]ffAccEnumsPb.TransactionFailureType{ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_FRAUD}))
	}
	transactions, err := s.creditCardTransactionDao.GetByAccountIdWithLimitAndOffset(ctx,
		accountId,
		statuses,
		selectFieldMask,
		startTimestamp.AsTime(),
		pageSize,
		offset,
		descending,
		daoFilterOptions...)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil, rpc.StatusAsError(rpc.StatusRecordNotFound())
	case err != nil:
		logger.Error(ctx, "error in fetching transactions", zap.String(logger.ACCOUNT_ID, accountId), zap.Error(err))
		return nil, rpc.StatusAsError(rpc.StatusInternal())
	}
	return s.getTxnWithAdditionalInfoFromTxns(ctx, transactions, additionalInfoSelectFieldMask, accountId)
}

func (s *Service) getTxnWithAdditionalInfoFromTxns(ctx context.Context, transactions []*faPb.CardTransaction,
	additionalInfoSelectFieldMask []ffAccEnumsPb.TransactionAdditionalInfoFieldMask, accountId string) ([]*faPb.CardTransactionWithAdditionalInfo, error) {
	var (
		txnAndAdditionalInfoResult []*faPb.CardTransactionWithAdditionalInfo
	)
	txnIdToTxnAndAdditionalInfoMap := make(map[string]*faPb.CardTransactionWithAdditionalInfo)
	var txnIds []string
	for _, txn := range transactions {
		txnIdToTxnAndAdditionalInfoMap[txn.GetId()] = &faPb.CardTransactionWithAdditionalInfo{
			Transaction: txn,
		}
		txnIds = append(txnIds, txn.GetId())
	}
	txnAdditionalInfos, err := s.creditCardTxnAdditionalInfoDao.GetByTxnIds(ctx, txnIds, additionalInfoSelectFieldMask)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no transaction additional info found", zap.String(logger.ACCOUNT_ID, accountId))
		return nil, rpc.StatusAsError(rpc.StatusRecordNotFound())
	case err != nil:
		logger.Error(ctx, "error in fetching transaction additional info", zap.String(logger.ACCOUNT_ID, accountId), zap.Error(err))
		return nil, rpc.StatusAsError(rpc.StatusInternal())
	}
	for _, txnAdditionalInfo := range txnAdditionalInfos {
		txnAndAdditionalInfo, ok := txnIdToTxnAndAdditionalInfoMap[txnAdditionalInfo.GetTransactionId()]
		if !ok {
			logger.Error(ctx, "no record found in map", zap.String(logger.TXN_ID, txnAdditionalInfo.GetTransactionId()))
			continue
		}
		txnAndAdditionalInfo.AdditionalInfo = txnAdditionalInfo
	}
	for _, txnId := range txnIds {
		txnAndAdditionalInfoResult = append(txnAndAdditionalInfoResult, txnIdToTxnAndAdditionalInfoMap[txnId])
	}
	return txnAndAdditionalInfoResult, nil
}

func (s *Service) getAccountForActor(ctx context.Context, actorId string) (string, error) {
	bcResp, errResp := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err := epifigrpc.RPCError(bcResp, errResp); err != nil {
		return "", fmt.Errorf("error while fetching bank customer by actor id: %w", err)
	}
	account, err := s.creditAccountDao.GetByActorIdAndRefId(ctx, actorId,
		bcResp.GetBankCustomer().GetVendorCustomerId(), nil)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return "", rpc.StatusAsError(rpc.StatusRecordNotFound())
		}
		return "", fmt.Errorf("error in fetching account %w", err)
	}
	return account.GetId(), nil
}

// nolint: funlen
func (s *Service) GetTxnsAndAdditionalInfoBetweenActors(ctx context.Context,
	req *faPb.GetTxnsAndAdditionalInfoBetweenActorsRequest) (*faPb.GetTxnsAndAdditionalInfoBetweenActorsResponse, error) {
	var (
		res            = &faPb.GetTxnsAndAdditionalInfoBetweenActorsResponse{}
		transactionIds []string
	)
	txnAdditionalInfos, err := s.creditCardTxnAdditionalInfoDao.GetTxnAdditionalInfoBetweenActors(ctx,
		req.GetPrimaryActorId(),
		req.GetSecondaryActorId(),
		req.GetStartTimestamp().AsTime(),
		req.GetPageSize(),
		req.GetOffset(),
		req.GetDescending(),
	)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Debug(ctx, "no txns additional info found", zap.String(logger.ACTOR_ID_V2, req.GetPrimaryActorId()),
			zap.String(logger.SECONDARY_ACTOR_ID, req.GetSecondaryActorId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching additional info for user", zap.String(logger.ACTOR_ID_V2, req.GetPrimaryActorId()),
			zap.String(logger.SECONDARY_ACTOR_ID, req.GetSecondaryActorId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	txnIdToTxnAndAdditionalInfoMap := make(map[string]*faPb.CardTransactionWithAdditionalInfo)
	for _, txnAdditionalInfo := range txnAdditionalInfos {
		transactionIds = append(transactionIds, txnAdditionalInfo.GetTransactionId())
		txnIdToTxnAndAdditionalInfoMap[txnAdditionalInfo.GetTransactionId()] = &faPb.CardTransactionWithAdditionalInfo{
			AdditionalInfo: txnAdditionalInfo,
		}
	}
	txns, err := s.creditCardTransactionDao.GetByIds(ctx, transactionIds, nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no txns found", zap.String(logger.ACTOR_ID_V2, req.GetPrimaryActorId()),
			zap.String(logger.SECONDARY_ACTOR_ID, req.GetSecondaryActorId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching txns for user", zap.String(logger.ACTOR_ID_V2, req.GetPrimaryActorId()),
			zap.String(logger.SECONDARY_ACTOR_ID, req.GetSecondaryActorId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	for _, txn := range txns {
		txnWithAdditionalInfo, ok := txnIdToTxnAndAdditionalInfoMap[txn.GetId()]
		if !ok {
			logger.Info(ctx, "transaction additional info not found for txn id",
				zap.String(logger.TXN_ID, txn.GetId()))
			continue
		}
		txnWithAdditionalInfo.Transaction = txn
	}
	for _, txnWithAdditionalInfo := range txnIdToTxnAndAdditionalInfoMap {
		if txnWithAdditionalInfo.GetTransaction() != nil {
			res.TransactionWithAdditionalInfoList = append(res.TransactionWithAdditionalInfoList, txnWithAdditionalInfo)
		}
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) GetTransactionWithAdditionalInfo(ctx context.Context, req *faPb.GetTransactionWithAdditionalInfoRequest) (*faPb.GetTransactionWithAdditionalInfoResponse, error) {
	var (
		res         = &faPb.GetTransactionWithAdditionalInfoResponse{}
		transaction = &faPb.CardTransaction{}
		err         error
	)

	switch req.GetBy.(type) {
	case *faPb.GetTransactionWithAdditionalInfoRequest_TransactionId:
		transaction, err = s.creditCardTransactionDao.GetById(ctx, req.GetTransactionId(), nil)

	case *faPb.GetTransactionWithAdditionalInfoRequest_VendorExternalTransactionId:
		transaction, err = s.creditCardTransactionDao.GetByVendorExternalTxnId(ctx, req.GetVendorExternalTransactionId(), nil)

	default:
		logger.Error(ctx, "invalid request received to fetch transaction and additional info")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "record not found for txn", zap.String(logger.TXN_ID, req.GetTransactionId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching txn", zap.String(logger.TXN_ID, req.GetTransactionId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	txnAdditionalInfo, err := s.creditCardTxnAdditionalInfoDao.GetByTxnId(ctx, transaction.GetId(), nil)
	if err != nil {
		logger.Error(ctx, "error in fetching txn additional info", zap.String(logger.TXN_ID, req.GetTransactionId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	res.CardTransactionWithAdditionalInfo = &faPb.CardTransactionWithAdditionalInfo{
		Transaction:    transaction,
		AdditionalInfo: txnAdditionalInfo,
	}
	return res, nil
}

func (s *Service) GetTransactionsForATimeInterval(ctx context.Context, req *faPb.GetTransactionsForATimeIntervalRequest) (*faPb.GetTransactionsForATimeIntervalResponse, error) {
	res := &faPb.GetTransactionsForATimeIntervalResponse{}

	account, err := s.creditAccountDao.GetByActorId(ctx, req.GetActorId(), nil)
	if err != nil {
		logger.Error(ctx, "error in fetching credit account details for the user", zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}

	daoFilterOptions := make([]storageV2.FilterOption, 0)
	if !req.GetIncludeFrmDeclineTxns() {
		daoFilterOptions = append(daoFilterOptions, options.WithTxnFailureTypeNotIn([]ffAccEnumsPb.TransactionFailureType{ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_FRAUD}))
	}

	txns, err := s.creditCardTransactionDao.GetByAccountIdAndTerminalTimestamps(ctx, account[0].GetId(), req.GetFromTime(), req.GetToTime(), nil, daoFilterOptions...)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	}

	res.Transactions = txns
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) GetAllDisputes(ctx context.Context, req *faPb.GetAllDisputesRequest) (*faPb.GetAllDisputesResponse, error) {
	res := &faPb.GetAllDisputesResponse{}
	disputes, err := s.disputedTransactionDao.GetByActorId(ctx, req.GetActorId(), nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	}
	res.DisputedTransactions = disputes
	res.Status = rpc.StatusOk()
	return res, nil
}

//nolint:funlen
func (s *Service) GetTransactionIdsByBillId(ctx context.Context, req *faPb.GetTransactionIdsByBillIdRequest) (*faPb.GetTransactionIdsByBillIdResponse, error) {
	var (
		resp = &faPb.GetTransactionIdsByBillIdResponse{}
	)

	billInfo, err := s.billingClient.GetCreditCardBill(ctx, &billingPb.GetCreditCardBillRequest{
		GetBy: &billingPb.GetCreditCardBillRequest_BillId{BillId: req.GetBillId()},
	})
	if te := epifigrpc.RPCError(billInfo, err); te != nil {
		if billInfo.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "no bill found for given bill id: ", zap.String(logger.BILL_ID, req.GetBillId()))
			resp.Status = rpc.StatusRecordNotFound()
			return resp, nil
		}
		logger.Info(ctx, "error fetching bill from given bill id: ", zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}

	txnAdditionalInfos, txnAdditionalInfoErr := s.creditCardTxnAdditionalInfoDao.GetByBillRefId(ctx, billInfo.GetCreditCardBill().GetId(), []ffAccEnumsPb.TransactionAdditionalInfoFieldMask{ffAccEnumsPb.TransactionAdditionalInfoFieldMask_TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_TRANSACTION_ID})
	switch {
	case errors.Is(txnAdditionalInfoErr, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no txn additional info records found for given bill id: ", zap.String(logger.BILL_ID, req.GetBillId()))
		resp.Status = rpc.StatusRecordNotFound()
		return resp, nil
	case txnAdditionalInfoErr != nil:
		logger.Info(ctx, "error fetching additional info records for given bill id: ", zap.Error(txnAdditionalInfoErr))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}

	txnIds := make([]string, 0)
	for _, txnAdditionalInfo := range txnAdditionalInfos {
		txnIds = append(txnIds, txnAdditionalInfo.GetTransactionId())
	}

	ccTxns, txnErr := s.creditCardTransactionDao.GetByIds(ctx,
		txnIds,
		[]ffAccEnumsPb.CardTransactionFieldMask{ffAccEnumsPb.CardTransactionFieldMask_CARD_TRANSACTION_FIELD_MASK_ID,
			ffAccEnumsPb.CardTransactionFieldMask_CARD_TRANSACTION_FIELD_MASK_EXTERNAL_TXN_ID,
			ffAccEnumsPb.CardTransactionFieldMask_CARD_TRANSACTION_FIELD_MASK_CHILD_TRANSACTION_IDS,
			ffAccEnumsPb.CardTransactionFieldMask_CARD_TRANSACTION_FIELD_MASK_TXN_CATEGORY})
	switch {
	case errors.Is(txnErr, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no txn info records found for given txn ids")
		resp.Status = rpc.StatusRecordNotFound()
		return resp, nil
	case txnErr != nil:
		logger.Info(ctx, "error fetching txn info records from given txn ids: ", zap.Error(txnErr))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}
	// this includes all the child transactions
	if req.GetFetchChildTransactions() {
		childTxns, err := s.getChildTxnsExtIdsFromTxns(ctx, ccTxns)
		if err != nil {
			logger.Error(ctx, "error fetching child transactions", zap.Error(err), zap.String(logger.BILL_ID, req.GetBillId()))
			resp.Status = rpc.StatusInternal()
			return resp, nil
		}

		// Append child txns to ccTxns slice
		ccTxns = append(ccTxns, childTxns...)
	}
	// if we only need to fetch refund and reversal child txns
	if req.GetFetchFutureRefundReversalChildTransactions() && !req.GetFetchChildTransactions() {
		childTxns, err := s.getChildTxnsExtIdsFromTxns(ctx, ccTxns)
		if err != nil {
			logger.Error(ctx, "error fetching child transactions", zap.Error(err), zap.String(logger.BILL_ID, req.GetBillId()))
			resp.Status = rpc.StatusInternal()
			return resp, nil
		}
		for _, txn := range childTxns {
			if ffPkg.IsReversalOrRefundTransaction(txn.GetTxnCategory()) {
				// Append child txn to ccTxns slice
				ccTxns = append(ccTxns, txn)
			}
		}
	}
	transactionsMap := make(map[string]*faPb.CardTransaction)
	for _, txn := range ccTxns {
		transactionsMap[txn.GetId()] = txn
	}
	txnIdResponses := make([]*faPb.GetTransactionIdsByBillIdResponse_TransactionIdResponse, 0)
	for _, txn := range transactionsMap {
		txnIdResponses = append(txnIdResponses, &faPb.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
			ExternalTxnId:       txn.GetExternalTxnId(),
			TxnId:               txn.GetId(),
			TransactionCategory: txn.GetTxnCategory(),
		})
	}
	resp.Status = rpc.StatusOk()
	resp.TransactionIdResponses = txnIdResponses
	return resp, nil
}

// nolint:dupl,funlen
func (s *Service) GetTxnsWithAdditionalInfos(ctx context.Context, req *faPb.GetTxnsWithAdditionalInfosRequest) (res *faPb.GetTxnsWithAdditionalInfosResponse, returnErr error) {
	returnErr = nil
	res = &faPb.GetTxnsWithAdditionalInfosResponse{
		Status: rpc.StatusInternal(),
	}
	var ccTxns []*faPb.CardTransaction
	var err error
	switch req.GetGetBy().(type) {
	// Deprecated: Do not use
	case *faPb.GetTxnsWithAdditionalInfosRequest_BatchDedupeIdGenerationParameter:
		dedupeIds, dedupeErr := generateDedupeIds(req.GetBatchDedupeIdGenerationParameter())
		if dedupeErr != nil {
			logger.Error(ctx, "Failed to fetch dedupe ids from dedupe id generation params ", zap.Error(dedupeErr))
			return
		}
		ccTxns, err = s.creditCardTransactionDao.GetByDedupeIds(ctx, dedupeIds, nil)
	case *faPb.GetTxnsWithAdditionalInfosRequest_BatchDedupeIds:
		ccTxns, err = s.creditCardTransactionDao.GetByDedupeIds(ctx, req.GetBatchDedupeIds().GetDedupeIds(), nil)
	case *faPb.GetTxnsWithAdditionalInfosRequest_BatchTxnIds:
		ccTxns, err = s.creditCardTransactionDao.GetByIds(ctx, req.GetBatchTxnIds().GetTxnIds(), nil)
	case *faPb.GetTxnsWithAdditionalInfosRequest_BatchExternalTxnIds:
		ccTxns, err = s.creditCardTransactionDao.GetByExternalTxnIds(ctx, req.GetBatchExternalTxnIds().GetExternalTxnIds(), nil)
	}

	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no cc transactions info found")
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching transactions info", zap.Error(err))
		return res, nil
	}

	txnIdToTxnAndAdditionalInfoMap := make(map[string]*faPb.CardTransactionWithAdditionalInfo)
	txnIds := make([]string, 0)
	for _, txn := range ccTxns {
		txnWithAddnInfo := &faPb.CardTransactionWithAdditionalInfo{
			Transaction:    txn,
			AdditionalInfo: nil,
		}
		txnIdToTxnAndAdditionalInfoMap[txn.GetId()] = txnWithAddnInfo
		txnIds = append(txnIds, txn.GetId())
	}
	txnAdditionalInfos, err := s.creditCardTxnAdditionalInfoDao.GetByTxnIds(ctx, txnIds, nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no cc transactions additional info found")
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching transactions additional info", zap.Error(err))
		return res, nil
	}

	for _, additionalInfo := range txnAdditionalInfos {
		txnWithAdditionalInfo, ok := txnIdToTxnAndAdditionalInfoMap[additionalInfo.GetTransactionId()]
		if ok {
			txnWithAdditionalInfo.AdditionalInfo = additionalInfo
		}
	}
	for _, txnWithAdditionalInfo := range txnIdToTxnAndAdditionalInfoMap {
		if txnWithAdditionalInfo.GetTransaction() != nil {
			res.CardTransactionWithAdditionalInfos = append(res.CardTransactionWithAdditionalInfos, txnWithAdditionalInfo)
		}
	}
	res.Status = rpc.StatusOk()
	return
}

func (s *Service) UpdateBillToTxnMapping(ctx context.Context, req *faPb.UpdateBillToTxnMappingRequest) (res *faPb.UpdateBillToTxnMappingResponse, returnErr error) {
	returnErr = nil
	res = &faPb.UpdateBillToTxnMappingResponse{
		Status: rpc.StatusInternal(),
	}
	for _, txnId := range req.GetTxnIds() {
		err := s.creditCardTxnAdditionalInfoDao.UpdateByTxnId(ctx,
			&faPb.TransactionAdditionalInfo{TransactionId: txnId, BillRefId: req.GetBillRefId()}, []ffAccEnumsPb.TransactionAdditionalInfoFieldMask{ffAccEnumsPb.TransactionAdditionalInfoFieldMask_TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_BILL_REF_ID})
		switch {
		case errors.Is(err, epifierrors.ErrRowNotUpdated):
			logger.Info(ctx, "no txn additional info record found for given txn id: ", zap.String(logger.TXN_ID, txnId))
			res.Status = rpc.StatusRecordNotFound()
			return
		case errors.Is(err, epifierrors.ErrInvalidArgument):
			logger.Info(ctx, "invalid arguments passed for updating txn additional info: ", zap.String(logger.TXN_ID, txnId))
			res.Status = rpc.StatusInvalidArgument()
			return
		case err != nil:
			logger.Info(ctx, "error occurred while updating txn additional info for given txn id: ", zap.String(logger.TXN_ID, txnId), zap.Error(err))
			return
		}
	}

	res.Status = rpc.StatusOk()
	return
}

// getAmountInFloat converts money.Money to float32
func getAmountInFloat(amount *moneyPb.Money) float32 {
	amountInDecimal := moneyPkg.ToDecimal(amount)
	amountInFloat64, _ := amountInDecimal.Float64()
	return float32(amountInFloat64)
}

func (s *Service) GetPaginatedTxnWithAdditionalInfo(ctx context.Context, req *faPb.GetPaginatedTxnWithAdditionalInfoRequest) (*faPb.GetPaginatedTxnWithAdditionalInfoResponse, error) {
	var (
		res       = &faPb.GetPaginatedTxnWithAdditionalInfoResponse{}
		accountId string
		err       error
	)
	pageSize := req.GetPageContext().GetPageSize()
	if pageSize > maxSize || pageSize == 0 {
		pageSize = maxSize
	}
	pageToken, pageTokenErr := pagination.GetPageToken(req.GetPageContext())
	if pageTokenErr != nil {
		logger.Error(ctx, "Unable to fetch pageToken from request", zap.Error(pageTokenErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	switch req.GetIdentifier().(type) {
	case *faPb.GetPaginatedTxnWithAdditionalInfoRequest_ActorId:
		accountId, err = s.getAccountForActor(ctx, req.GetActorId())
		if err != nil {
			logger.Error(ctx, "error in fetching account for actor", zap.Error(err))
			res.Status = rpc.StatusFromError(err)
			return res, nil
		}
	case *faPb.GetPaginatedTxnWithAdditionalInfoRequest_AccountId:
		accountId = req.GetAccountId()
	}
	txns, pageContext, dbErr := s.creditCardTransactionDao.GetByAccountIdPaginated(ctx, accountId, req.GetStatuses(),
		req.GetCardTransactionFieldMasks(), pageToken, pageSize, options.WithTxnFailureTypeNotIn([]ffAccEnumsPb.TransactionFailureType{ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_FRAUD}))
	switch {
	case errors.Is(dbErr, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no txns found for account", zap.String(logger.ACCOUNT_ID, accountId))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	default:
	}
	txnWithAdditionalInfoList, err := s.getTxnWithAdditionalInfoFromTxns(ctx, txns, nil, accountId)
	if err != nil {
		logger.Error(ctx, "error in fetching txn additional info from txns", zap.Error(err),
			zap.String(logger.ACCOUNT_ID, accountId))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}
	res.CardTransactionWithAdditionalInfos = txnWithAdditionalInfoList
	res.PageContext = pageContext
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) getChildTxnsExtIdsFromTxns(ctx context.Context, txns []*faPb.CardTransaction) ([]*faPb.CardTransaction, error) {
	childTxnIds := []string{}
	for _, txn := range txns {
		if len(txn.GetChildTransactionIds()) > 0 {
			childTxnIds = append(childTxnIds, txn.GetChildTransactionIds()...)
		}
	}

	if len(childTxnIds) == 0 {
		return nil, nil
	}

	childTxns, txnErr := s.creditCardTransactionDao.GetByIds(ctx,
		childTxnIds,
		[]ffAccEnumsPb.CardTransactionFieldMask{ffAccEnumsPb.CardTransactionFieldMask_CARD_TRANSACTION_FIELD_MASK_ID,
			ffAccEnumsPb.CardTransactionFieldMask_CARD_TRANSACTION_FIELD_MASK_EXTERNAL_TXN_ID,
			ffAccEnumsPb.CardTransactionFieldMask_CARD_TRANSACTION_FIELD_MASK_TXN_CATEGORY})
	switch {
	case errors.Is(txnErr, epifierrors.ErrRecordNotFound):
		return nil, nil
	case txnErr != nil:
		return nil, txnErr
	}

	return childTxns, nil
}

// nolint:funlen
func (s *Service) GetCollateralLienStatus(ctx context.Context, req *faPb.GetCollateralLienStatusRequest) (*faPb.GetCollateralLienStatusResponse, error) {
	var (
		res = &faPb.GetCollateralLienStatusResponse{}
		err error
	)
	accounts, err := s.creditAccountDao.GetByActorId(ctx,
		req.GetActorAndCollateralIdentifier().GetActorId(),
		[]ffAccEnumsPb.CreditAccountFieldMask{
			ffAccEnumsPb.CreditAccountFieldMask_CREDIT_ACCOUNT_FIELD_MASK_COLLATERAL_DETAILS,
			ffAccEnumsPb.CreditAccountFieldMask_CREDIT_ACCOUNT_FIELD_MASK_ID,
		})

	switch {
	// in case an account is not found, it is assumed that
	// card onboarding is in progress as of now and user intent for card creation is fetched
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no account found for the actor: ", zap.String(logger.ACTOR_ID_V2, req.GetActorAndCollateralIdentifier().GetActorId()))

		userIntentResp, userIntentErr := s.fireflyClient.GetUserCreditCardIntent(ctx, &ffBePb.GetUserCreditCardIntentRequest{
			ActorId: req.GetActorAndCollateralIdentifier().GetActorId(),
		})
		if te := epifigrpc.RPCError(userIntentResp, userIntentErr); te != nil {
			logger.Error(ctx, "error in fetching user intent: ", zap.Error(err))
			res.Status = rpc.StatusInternal()
			res.CollateralLienStatus = types.CollateralLienStatus_COLLATERAL_LIEN_STATUS_UNSPECIFIED
			return res, nil
		}
		// if the card creation is progress, the lien status is returned as marked.
		// Once the workflow fails, this will revert back to unmarked.
		if userIntentResp.GetHasUserShownIntent() {
			res.Status = rpc.StatusOk()
			res.CollateralLienStatus = types.CollateralLienStatus_COLLATERAL_LIEN_STATUS_MARKED
			return res, nil
		}
		res.Status = rpc.StatusOk()
		res.CollateralLienStatus = types.CollateralLienStatus_COLLATERAL_LIEN_STATUS_UNMARKED
		return res, nil
	case err != nil:
		logger.Error(ctx, "err fetching account for the actor: ", zap.Error(err))
		res.Status = rpc.StatusInternal()
		res.CollateralLienStatus = types.CollateralLienStatus_COLLATERAL_LIEN_STATUS_UNSPECIFIED
		return res, nil
	}
	account := accounts[0]
	if account.GetCollateralDetails().GetCollateralId() != req.GetActorAndCollateralIdentifier().GetCollateralId() ||
		account.GetCollateralDetails().GetCollateralType() != req.GetActorAndCollateralIdentifier().GetCollateralType() {

		isCollateralLienUnmarked, depositCheckErr := s.checkCollateralLienDetails(ctx, req)
		if depositCheckErr != nil {
			logger.Error(ctx, "error checking deposit details", zap.Error(depositCheckErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		if isCollateralLienUnmarked {
			res.Status = rpc.StatusOk()
			res.CollateralLienStatus = types.CollateralLienStatus_COLLATERAL_LIEN_STATUS_UNMARKED
			return res, nil
		}
		logger.Error(ctx, "account collateral details do not match request collateral identifier: ", zap.String(logger.ACCOUNT_ID, account.GetId()))
		res.Status = rpc.StatusInvalidArgument()
		res.CollateralLienStatus = types.CollateralLienStatus_COLLATERAL_LIEN_STATUS_UNSPECIFIED
		return res, nil
	}

	// in case details match, we fetch the credit card for the user.
	creditCard, err := s.fireflyClient.GetCreditCard(ctx, &ffBePb.GetCreditCardRequest{
		GetBy: &ffBePb.GetCreditCardRequest_ActorId{
			ActorId: req.GetActorAndCollateralIdentifier().GetActorId(),
		},
		SelectFieldMasks: []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE},
	})
	if te := epifigrpc.RPCError(creditCard, err); te != nil {
		logger.Error(ctx, "err fetching credit card for the actor: ", zap.Error(te))
		res.Status = rpc.StatusInternal()
		res.CollateralLienStatus = types.CollateralLienStatus_COLLATERAL_LIEN_STATUS_UNSPECIFIED
		return res, nil
	}

	// if the card is closed, lien is assumed to be marked.
	if creditCard.GetCreditCard().GetCardState() == ffEnumsPb.CardState_CARD_STATE_CLOSED {
		res.Status = rpc.StatusOk()
		res.CollateralLienStatus = types.CollateralLienStatus_COLLATERAL_LIEN_STATUS_UNMARKED
		return res, nil
	}

	// for all other cases, it is assumed that the lien is still marked.
	res.Status = rpc.StatusOk()
	res.CollateralLienStatus = types.CollateralLienStatus_COLLATERAL_LIEN_STATUS_MARKED
	return res, nil
}

// returns true if collateral can be assumed unmarked, false otherwise
func (s *Service) checkCollateralLienDetails(ctx context.Context, req *faPb.GetCollateralLienStatusRequest) (bool, error) {
	depositResp, err := s.depositClient.GetById(ctx, &depositPb.GetByIdRequest{
		Id: req.GetActorAndCollateralIdentifier().GetCollateralId(),
	})
	if te := epifigrpc.RPCError(depositResp, err); te != nil {
		return false, errors.Wrap(te, "error fetching details")
	}
	// if the deposit was created for credit card, but is not linked to a credit account, we can assume it to be lien unmarked. This is observed in cases where
	// reg customer fails on the first attempt but succeeds on a different onboarding attempt.
	if depositResp.GetAccount().GetProvenance() == depositPb.DepositAccountProvenance_CREDIT_CARD {
		return true, nil
	}
	return false, nil
}
