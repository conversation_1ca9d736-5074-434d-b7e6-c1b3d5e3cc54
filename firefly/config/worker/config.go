package worker

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/pkg/cfg"
	cfgV2 "github.com/epifi/be-common/pkg/cfg/v2"
	"github.com/epifi/be-common/pkg/frontend/app"
	typesPkg "github.com/epifi/be-common/pkg/types"
	sdkconfig "github.com/epifi/be-common/quest/sdk/config"

	"github.com/epifi/be-common/pkg/epifitemporal"

	"github.com/epifi/gamma/firefly/config/common"
	releaseConfig "github.com/epifi/gamma/pkg/feature/release/config"
)

var (
	_, b, _, _ = runtime.Caller(0)
	once       sync.Once
	config     *Config
	err        error
)

//go:generate conf_gen github.com/epifi/gamma/firefly/config/worker Config
type Config struct {
	Server                                  *cfg.ServerPorts
	Application                             *cfg.TemporalWorkerApplication `dynamic:"true"`
	WorkflowParamsList                      cfgV2.WorkflowParamsList
	DefaultActivityParamsList               cfgV2.ActivityParamsList
	PausedWorkflowList                      cfgV2.PausedWorkflowsList
	DbConfigMap                             cfg.DbConfigMap
	UsecaseDbConfigMap                      cfg.UseCaseDbConfigMap
	CreditCardDb                            *cfg.DB
	AWS                                     *cfg.AWS
	WorkflowUpdatePublisher                 *cfg.SnsPublisher
	CreditCardUrl                           *CreditCardUrl
	Tracing                                 *cfg.Tracing
	NotificationSchedule                    *NotificationSchedule
	Notification                            *common.Notification
	StepInfo                                *StepInfo
	EnableActivityConfig                    *EnableActivityConfig
	FundTransferPoolAccountActorId          string
	TpapFundTransferPoolAccountActorPiId    string
	TpapFundTransferPoolAccountActorId      string
	TpapFundTransferUpiPoolAccountActorPiId string
	TpapFundTransferUpiPoolAccountActorId   string
	CCTransactionEventPublisher             *cfg.SnsPublisher
	CCBillGenerationEventPublisher          *cfg.SnsPublisher
	CCStageUpdateEventPublisher             *cfg.SnsPublisher
	CCTransactionNotificationPublisher      *cfg.SqsPublisher
	StatementPdfConfig                      *StatementPdfConfig
	Flags                                   *Flags
	RudderStack                             *cfg.RudderStackBroker
	Secrets                                 *cfg.Secrets
	// Joining fees amount for CC
	JoiningFeesAmount int64
	// HighCreditScoreThreshold for adding cc eligibility
	HighCreditScoreThreshold         int32
	Screens                          *Screens `dynamic:"true" ,quest:"component,area:CreditCard"`
	CardProgramNudgeConfigurations   *CardProgramNudgeConfigurations
	FeatureReleaseConfig             *releaseConfig.FeatureReleaseConfig `dynamic:"true"`
	QuestRedisOptions                *cfg.RedisOptions
	QuestSdk                         *sdkconfig.Config `dynamic:"true"`
	SecuredCardDepositConfig         *SecuredCardDepositConfig
	EpifiBusinessAccountActorId      string
	EpifiBusinessAccountPiIds        []string
	FundTransferPoolAccountActorPiId string
	UsePgdbConnForCcDb               bool
	DbConnectionAliases              *DbConnectionAlias
	CreditCardPgDb                   *cfg.DB
	RepaymentNudges                  *RepaymentNudges
	InHouseBreConfig                 *InHouseBreConfig
	// config for enabling mass unsecured card
	MassUnsecuredCardFeatureReleaseConfig *releaseConfig.FeatureReleaseConfig
	RewardCvpVersionConfigs               []*RewardCvpVersionConfig
	StatementDetailsConfigs               []*StatementDetailsConfig
	// config used to store communication type based comms list
	CommunicationTypeToCommunicationParamsMap map[string]map[string][]*CommunicationParams
	// config used to control user communication activity's comm triggering and returning constant sleep time to workflow
	UserCommunicationConfig *UserCommunicationConfig
	// application download urls for different comms carrier type
	AppDownloadUrls                   *AppDownloadUrls
	SimplifiConfig                    *SimplifiConfig
	FireflyRedisStore                 *cfg.RedisOptions
	CreditCardCacheConfig             *CreditCardCacheConfig
	ProcrastinatorWorkflowPublisher   *cfg.SqsPublisher
	DisableVendorCallsConfig          *DisableVendorCallsConfig
	CreditCardDefaultBillPaymentInfo  *CreditCardBillPaymentInfo         `dynamic:"true"`
	UnsecuredCCRenewalFeeWaiverConfig *UnsecuredCCRenewalFeeWaiverConfig `dynamic:"true"`
}

type UnsecuredCCRenewalFeeWaiverConfig struct {
	IneligibleUsersSegmentId    string `dynamic:"true"`
	IsRenewalFeeVouchersEnabled bool   `dynamic:"true"`
	IsRenewalFeeReversalEnabled bool   `dynamic:"true"`
}

type CreditCardBillPaymentInfo struct {
	BillGenDate    int64 `dynamic:"true"`
	PaymentDueDate int64 `dynamic:"true"`
}

type CreditCardCacheConfig struct {
	CreditCardOfferCacheConfig *cfg.CacheConfig
	CardRequestCacheConfig     *cfg.CacheConfig
}

type AppDownloadUrls struct {
	ForEmailComm            string
	ForSmsComm              string
	ForPushNotificationComm string
	ForWhatsappComm         string
}
type UserCommunicationConfig struct {
	UserCommunicationTriggerTimeWindow *UserCommunicationTriggerTimeWindow
	WorkFlowSleepTimeDuration          time.Duration
}
type UserCommunicationTriggerTimeWindow struct {
	StartTime string
	EndTime   string
}
type CommunicationParams struct {
	CommNotificationType   string
	DelayDuration          time.Duration
	CommNotificationMedium string
}

type DbConnectionAlias struct {
	CreditCardPgdbConnAlias string
}

type EnableActivityConfig struct {
	// EnableProfileValidation introduced while moving profile validation stage from first stage to card creation stage
	EnableProfileValidation bool
	// EnableSkipProfileValidationForAllProgram is used to completely skip the profile validation stage
	EnableSkipProfileValidationForAllProgram bool
	EnablePanAadhaarCheck                    bool
	EnableActiveProductsCheck                bool
	EnablePollCreditReportFeature            bool
	EnablePanAadhaarValidation               bool
}

type DisableVendorCallsConfig struct {
	DisableBreCall      bool
	DisableCardCreation bool
}

type StepInfo struct {
	TotalSteps                       int32
	BillGenDateSelectionStepNumber   int32
	AddressSelectionScreenStepNumber int32
}

type CreditCardUrl struct {
	CCTermsAndConditionsUrl    string
	CardActivationPageImageUrl string
	PermanentFailureImageUrl   string
}

type StatementPdfConfig struct {
	StatementDocumentsBucketName                               string
	EnableRewardsInStatements                                  bool
	StatementRewardsGenerationPauseDuration                    time.Duration
	StatementRewardsGenerationPauseDurationDuringFcFpMigration time.Duration
}

type NotificationSchedule struct {
	PaymentDeadlineExceededReminderAfterHours                 map[string][]int
	PaymentDeadlineExceededWithInterestRateReminderAfterHours map[string][]int
	BeforeDueDateReminderHours                                map[string][]int
	OnDueDateReminderHours                                    map[string][]int
	AfterDueDateReminderHours                                 map[string][]int
}

type Flags struct {
	EnableAddressV2Screen     *app.FeatureConfig
	EnableNewFdScreen         *app.FeatureConfig
	EnableRevampedNewFdScreen *app.FeatureConfig
}

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(false)
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

// LoadOnlyStaticConf loads configs only from static config files if not done already and returns it
func LoadOnlyStaticConf() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(true)
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig(onlyStaticFiles bool) (*Config, error) {
	conf := &Config{}

	koanf, _, err := cfg.LoadWorkerConfigUsingKoanf(testEnvConfigDir(epifitemporal.FireflyWorker), "", cfg.ServiceName(epifitemporal.FireflyWorker))
	if err != nil {
		return nil, err
	}

	if err = koanf.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf)); err != nil {
		return nil, err
	}

	dbOwnershipMap := conf.DbConfigMap.GetOwnershipToDbConfigMap()
	dbValues := make([]*cfg.DB, 0)
	for _, v := range dbOwnershipMap {
		dbValues = append(dbValues, v)
	}
	dbValues = append(dbValues, conf.CreditCardDb, conf.CreditCardPgDb)

	if err = readAndSetEnv(conf, dbOwnershipMap); err != nil {
		return nil, fmt.Errorf("failed to read and set env var: %w", err)
	}
	_, err = cfg.LoadSecretsAndPrepareDBConfig(conf.Secrets, conf.Application.Environment, conf.AWS.Region, dbValues...)
	if err != nil {
		return nil, err
	}
	if onlyStaticFiles {
		return conf, nil
	}
	return conf, nil
}

func testEnvConfigDir(worker epifitemporal.Worker) string {
	configPath := filepath.Join(b, "..", "..", "..", "..", "cmd", "worker", worker.GetDirectory(), "config")
	return configPath
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, dbOwnershipMap map[commontypes.Ownership]*cfg.DB) error {
	ccDbKeyToSecret, err := cfg.LoadAllSecrets(c.CreditCardDb, nil, c.Application.Environment, c.AWS.Region)
	if err != nil {
		return err
	}
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.CreditCardDb, dbServerEndpoint)
	cfg.UpdateSecretValues(c.CreditCardDb, nil, ccDbKeyToSecret)
	cfg.UpdateDbEndpointsInConfigMap(dbOwnershipMap, dbServerEndpoint)
	if _, err := cfg.LoadAndUpdateSecretValues(dbOwnershipMap, c.Secrets, c.Application.Environment, c.AWS.Region); err != nil {
		return fmt.Errorf("failed to load and update secret values %w", err)
	}
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config, dbOwnershipMap map[commontypes.Ownership]*cfg.DB) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Namespace = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.HttpPort = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		cfg.OverwriteDbHost(dbOwnershipMap, val)
		c.CreditCardDb.Host = val
	}

	return nil
}

type Screens struct {
	CardActivationScreenOptions              *CardActivationScreenOptions    `dynamic:"true" ,quest:"component,area:CreditCard"`
	BillDateSelectionScreenOptions           *BillDateSelectionScreenOptions `dynamic:"true" ,quest:"component,area:CreditCard"`
	AddressSelectionScreenOptions            *AddressSelectionScreenOptions  `dynamic:"true" ,quest:"component,area:CreditCard"`
	SecuredCardActivationScreenOptions       *CardActivationScreenOptions    `dynamic:"true" ,quest:"component,area:CreditCard"`
	SecuredAddressSelectionScreenOptions     *AddressSelectionScreenOptions  `dynamic:"true" ,quest:"component,area:CreditCard"`
	SecuredBillDateSelectionScreenOptions    *BillDateSelectionScreenOptions `dynamic:"true" ,quest:"component,area:CreditCard"`
	AuthScreenOptions                        *AuthScreenOptions              `dynamic:"true" ,quest:"component,area:CreditCard"`
	MassUnsecuredCardActivationScreenOptions *CardActivationScreenOptions    `dynamic:"true" ,quest:"component,area:CreditCard"`
	FiLiteSecuredCardActivationScreenOptions *CardActivationScreenOptions    `dynamic:"true" ,quest:"component,area:CreditCard"`
}

type AddressSelectionScreenOptions struct {
	HeaderImageUrl             string `dynamic:"true" ,quest:"variable"`
	HeaderImageHeight          int32  `dynamic:"true" ,quest:"variable"`
	HeaderImageWidth           int32  `dynamic:"true" ,quest:"variable"`
	HeaderText                 *typesPkg.Text
	HeaderSubText              *typesPkg.Text
	AddressSelectionStepNumber int32  `dynamic:"true" ,quest:"variable"`
	TotalSteps                 int32  `dynamic:"true" ,quest:"variable"`
	ContinueCtaText            string `dynamic:"true" ,quest:"variable"`
	AddAddressCtaText          *typesPkg.Text
	AddAddressCtaImageUrl      string `dynamic:"true" ,quest:"variable"`
	AddAddressCtaImageHeight   int32  `dynamic:"true" ,quest:"variable"`
	AddAddressCtaImageWidth    int32  `dynamic:"true" ,quest:"variable"`
}

type BillDateSelectionScreenOptions struct {
	BillGenDateSelectionStepNumber int32 `dynamic:"true" ,quest:"variable"`
	TotalSteps                     int32 `dynamic:"true" ,quest:"variable"`
	HeaderDetailsTitle             *typesPkg.Text
	HeaderDetailsSubTitle          *typesPkg.Text
	FooterText                     *typesPkg.Text
	ContinueTextCta                string `dynamic:"true" ,quest:"variable"`
}

type CardActivationScreenOptions struct {
	InfoItems                  []*InfoItem
	CardImageUrl               string
	Heading                    *typesPkg.Text
	SubHeading                 *typesPkg.Text
	Tnc                        string
	ActivateDigitalCardCtaText string `dynamic:"true" ,quest:"variable"`
	ImportantTncUrl            string
	KeyFactStatementTncUrl     string
	CreditCardTncUrl           string
	DefaultBillGenDate         int64 `dynamic:"true" ,quest:"variable"`
	DefaultPaymentDueDate      int64 `dynamic:"true" ,quest:"variable"`
}

type InfoItem struct {
	TitleText            *typesPkg.Text
	SubTitleText         *typesPkg.Text
	DescriptionText      *typesPkg.Text
	InfoBlockTitle       string
	InfoBlockDescription string
}

// CardProgramNudgeConfigurations is a map that defines Nudge configurations
// for different card program types and their respective request stages.
// It is structured as a map with the following hierarchy:
//   - Card Program Type (e.g., CARD_PROGRAM_TYPE_UNSECURED)
//     -- Card Request Stage Name (e.g., CARD_REQUEST_STAGE_NAME_AUTH)
//     --- Nudge Configuration (e.g., NudgeId)
type CardProgramNudgeConfigurations map[string]map[string]*NudgeConfig

type NudgeConfig struct {
	NudgeId string
}

type RepaymentNudges struct {
	SevenDaysPriorToDueDateNudgeId string
	OneDayPriorToDueDateNudgeId    string
	DueDateNudgeId                 string
	OneDayPostDueDateNudgeId       string
}

type SecuredCardDepositConfig struct {
	FederalSecuredCardConfig *SecuredCardConfig
}

type SecuredCardConfig struct {
	FixedDepositTemplateId string
	DepositPoolAccountPiId string
}

type AuthScreenOptions struct {
	LivenessScreenData *LivenessScreenData `dynamic:"true" ,quest:"component"`
}

type LivenessScreenData struct {
	VisualElementData *VisualElementData `dynamic:"true" ,quest:"component"`
	Title             *TextItem          `dynamic:"true" ,quest:"component"`
	Subtitle          *TextItem          `dynamic:"true" ,quest:"component"`
	ListItems         []*TextItem
	BgColor           string `dynamic:"true" ,quest:"variable"`
}

type TextItem struct {
	Content   string `dynamic:"true" ,quest:"variable"`
	FontColor string `dynamic:"true" ,quest:"variable"`
	FontStyle int32  `dynamic:"true" ,quest:"variable"`
}

type VisualElementData struct {
	ImageUrl    string `dynamic:"true" ,quest:"variable"`
	ImageWidth  int32  `dynamic:"true" ,quest:"variable"`
	ImageHeight int32  `dynamic:"true" ,quest:"variable"`
}

type InHouseBreConfig struct {
	EnableInHouseBre bool
}

type RewardCvpVersionConfig struct {
	CvpType                   string
	SupportedCardProgramTypes []string
	CvpVersions               []*CvpVersion
}

type CvpVersion struct {
	Name              string
	StartDate         *date.Date
	EndDate           *date.Date
	LastStatementDate *date.Date
}

type StatementDetailsConfig struct {
	CvpType                 string
	SupportedVersionDetails []string
	CvpVersionDetails       []*CvpVersionDetails
}

type CvpVersionDetails struct {
	Name                string
	MitcLink            string
	TncLink             string
	KfsLink             string
	CardProgramLogo     string
	CustomerCareDetails []*CustomerCareDetail
}

type CustomerCareDetail struct {
	Title string
}

type SimplifiConfig struct {
	ControlConfig *ControlConfig
}

type ControlConfig struct {
	AtmMaxLimitMultiplier float32
}
