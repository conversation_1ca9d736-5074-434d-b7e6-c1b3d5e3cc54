// nolint:gosec
package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/frontend/app"
	sdkconfig "github.com/epifi/be-common/quest/sdk/config"

	releaseConfig "github.com/epifi/gamma/pkg/feature/release/config"
	"github.com/epifi/gamma/pkg/pinot"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

const (
	RudderWriteKey                          = "RudderWriteKey"
	ClientEncryptionKey                     = "ClientEncryptionKey"
	ClientEncryptionInitialisationVector    = "ClientEncryptionInitialisationVector"
	MctClientEncryptionKey                  = "MctClientEncryptionKey"
	MctClientEncryptionInitialisationVector = "MctClientEncryptionInitialisationVector"
	VendorCardPinEncryptionKey              = "VendorCardPinEncryptionKey"
	ClientPinRsaPrivateKey                  = "ClientPinRsaPrivateKey"
	StartreePinotAuthToken                  = "StartreePinotAuthToken"
	CreditCardPgdbCredentialsKey            = ""
	EpifiSeshaasaiPgpPrivateKey             = "EpifiSeshaasaiPgpPrivateKey"
	EpifiSeshaasaiPgpPassphrase             = "EpifiSeshaasaiPgpPassphrase"
	SeshaasaiPgpPublicKey                   = "SeshaasaiPgpPublicKey"
	FederalPgpPublicKey                     = "FederalPgpPublicKey"
	EpifiFederalPgpPrivateKey               = "EpifiFederalPgpPrivateKey"
	EpifiFederalPgpPassphrase               = "EpifiFederalPgpPassphrase"
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(false)
	})
	if err != nil {
		return nil, err
	}
	return config, err
}

// LoadOnlyStaticConf loads configs only from static config files if not done already and returns it
func LoadOnlyStaticConf() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(true)
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

// nolint:unparam
func loadConfig(onlyStaticFiles bool) (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.FIREFLY_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	dbValues := []*cfg.DB{conf.EpifiDb, conf.CreditCardDb, conf.CreditCardPgDb}
	if conf.CreditCardFederalPgDb != nil {
		dbValues = append(dbValues, conf.CreditCardFederalPgDb)
	}

	keyToSecret, err := cfg.LoadSecretsAndPrepareDBConfig(conf.Secrets, conf.Application.Environment, conf.AWS.Region, dbValues...)
	if err != nil {
		return nil, err
	}
	// update pinot config
	conf.PinotConfig.Endpoint = cfg.GetPinotEndpoint(cfg.PINOT)
	if _, ok := keyToSecret[StartreePinotAuthToken]; !ok {
		return nil, fmt.Errorf("startree pinot authtoken not fetched from secrets manager")
	}
	pinot.UpdateAuthTokenInConfig(conf.PinotConfig, keyToSecret[StartreePinotAuthToken])

	// same secrets load has to be done wrt credit card db as well since that will also be used from the firefly service
	return conf, nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, dbServerEndpoint)
	cfg.UpdateDbEndpointInConfig(c.CreditCardDb, dbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	// update pinot config
	c.PinotConfig.Endpoint = cfg.GetPinotEndpoint(cfg.PINOT)
	if _, ok := keyToSecret[StartreePinotAuthToken]; !ok {
		return fmt.Errorf("startree pinot authtoken not fetched from secrets manager")
	}
	pinot.UpdateAuthTokenInConfig(c.PinotConfig, keyToSecret[StartreePinotAuthToken])
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		c.EpifiDb.Host = val
		c.CreditCardDb.Host = val
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

//go:generate conf_gen github.com/epifi/gamma/firefly/config Config
type Config struct {
	Application             *Application
	Server                  *Server
	Logging                 *cfg.Logging
	SecureLogging           *cfg.SecureLogging
	Flags                   *Flags `dynamic:"true"`
	EpifiDb                 *cfg.DB
	CreditCardDb            *cfg.DB
	PinotConfig             *pinot.Config
	AWS                     *Aws
	RudderStack             *cfg.RudderStackBroker
	Secrets                 *cfg.Secrets
	Tracing                 *cfg.Tracing
	SignalWorkflowPublisher *cfg.SqsPublisher
	Vendor                  *Vendor
	RedisCachePrefix        string

	CreditCardUrl                            *CreditCardUrl
	CCTransactionNotificationSubscriber      *cfg.SqsSubscriber `dynamic:"true"`
	CCAcsNotificationSubscriber              *cfg.SqsSubscriber `dynamic:"true"`
	CCStatementNotificationSubscriber        *cfg.SqsSubscriber `dynamic:"true"`
	CreditCardBillPaymentInfo                []*CreditCardBillPaymentInfo
	CardsSentForPrintingCsvFileSubscriber    *cfg.SqsSubscriber `dynamic:"true"`
	CardsDispatchedCsvFileSubscriber         *cfg.SqsSubscriber `dynamic:"true"`
	CCTransactionsForPinotSubscriber         *cfg.SqsSubscriber `dynamic:"true"`
	CategorizerUpdateForPinotSubscriber      *cfg.SqsSubscriber `dynamic:"true"`
	CCTransactionEventPublisher              *cfg.SnsPublisher
	CCAuthFactorUpdateNotificationSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	CCCreditReportDownloadEventSubscriber    *cfg.SqsSubscriber `dynamic:"true"`
	ProcessCreditCardOfferFileSubscriber     *cfg.SqsSubscriber `dynamic:"true"`
	CCNonFinancialNotificationSubscriber     *cfg.SqsSubscriber `dynamic:"true"`
	TransactionsProducer                     *cfg.KinesisProducer
	// s3 configs
	CardsSentForPrintingBucketName           string `iam:"s3-readwrite"`
	CardsDispatchedBucketName                string `iam:"s3-readwrite"`
	AcsBucketName                            string `iam:"s3-readwrite"`
	AcsNotificationRawDataStore              *AcsNotificationRawDataStore
	EnableViewCardDetailsViaVgPciServer      bool
	FeatureReleaseConfig                     *releaseConfig.FeatureReleaseConfig `dynamic:"true"`
	DepositConfigMap                         map[string]*DepositConfig
	CreditCardOffersScreenOptions            *CreditCardOffersScreenOptions   `dynamic:"true" ,quest:"component,area:CreditCard"`
	CreditCardOffersScreenOptionsV2          *CreditCardOffersScreenOptionsV2 `dynamic:"true" ,quest:"component,area:CreditCard"`
	QuestRedisOptions                        *cfg.RedisOptions
	FireflyRedisStore                        *cfg.RedisOptions
	QuestSdk                                 *sdkconfig.Config `dynamic:"true"`
	EnableCcOffersScreenOlderStringAlignment *app.FeatureConfig
	EnableCreditReportDownloadConsumer       *app.FeatureConfig
	SkipWorkflowInitiationForViewCardDetails *app.FeatureConfig
	EnableExpiryFromTokenizer                *app.FeatureConfig
	UsePgdbConnForCcDb                       bool `dynamic:"true"`
	DbConnectionAliases                      *DbConnectionAlias
	CreditCardPgDb                           *cfg.DB
	DisableHorizontalLayoutByQuestEngine     bool                 `dynamic:"true" ,quest:"variable,area:CreditCard"`
	RateLimiterConfig                        *cfg.RateLimitConfig `dynamic:"true"`
	EnableNewCvpForUnsecuredCreditCard       bool                 `dynamic:"true"`
	CardRecommendationConfig                 *CardRecommendationConfig
	CardProgramToSegmentIdMap                map[string]map[string]string
	SimplifiConfig                           *SimplifiConfig
	CreditCardCacheConfig                    *CreditCardCacheConfig
	DisableCreditCardOnboarding              bool `dynamic:"true"`
	// SkipEligibilityCheckForDisabledCCOnboarding will skip eligibility check when DisableCreditCardOnboarding is true
	SkipEligibilityCheckForDisabledCCOnboarding bool                                 `dynamic:"true"`
	DisabledOnboardingScreenOptions             *DisabledOnboardingScreenOptions     `dynamic:"true"`
	EnableMassUnsecuredOnboardingV2             bool                                 `dynamic:"true" ,quest:"variable,area:CreditCard"`
	EnableUnsecuredOnboardingV2                 bool                                 `dynamic:"true" ,quest:"variable,area:CreditCard"`
	EnableUnsecuredConsentFlowOnboarding        bool                                 `dynamic:"true" ,quest:"variable,area:CreditCard"`
	UnsecuredCCRenewalFeeReversalConfig         *UnsecuredCCRenewalFeeReversalConfig `dynamic:"true"`
	CardProgramToBeneficiaryDetails             map[string]*BeneficiaryDetails
	DpdThirtySegmentId                          string
	CreditCardFederalPgDb                       *cfg.DB
	CCOnboardingStateUpdateEventSqsSubscriber   *cfg.SqsSubscriber `dynamic:"true"`
	CcOnboardingStateUpdateEventPublisher       *cfg.SqsPublisher
	CreditCardsDispatchedFileSubscriberV2       *cfg.SqsSubscriber `dynamic:"true"`
	FireflyV2Config                             *FireflyV2Config   `dynamic:"true"`
	Buckets                                     *Buckets
}

type FireflyV2Config struct {
	IntroScreenV2Config  *IntroScreenV2Config `dynamic:"true"`
	CcIneligibleSegments []string             `dynamic:"true"`
	// Cool-off applicable before user can re-initiate onboarding in case last attempt resulted in failure
	OnboardingRetryCoolOff          time.Duration                    `dynamic:"true"`
	IsFeatureForceUpdateEnabled     bool                             `dynamic:"true"`
	ForceUpdateScreenOptions        *ForceUpdateScreenOptions        `dynamic:"true"`
	CCDispatchedFileProcessingParam *CCDispatchedFileProcessingParam `dynamic:"true"`
}
type Buckets struct {
	CreditCardDocs string `iam:"s3-readwrite"`
}

type CCDispatchedFileProcessingParam struct {
	InitiateTrackingWfBatchSize int64 `dynamic:"true"`
}

type ForceUpdateScreenOptions struct {
	IconUrl     string `dynamic:"true"`
	Title       string `dynamic:"true"`
	Description string `dynamic:"true"`
	CtaText     string `dynamic:"true"`
}

type IntroScreenV2Config struct {
	IntroScreenV2LoaderAnimation       string `dynamic:"true"`
	IntroScreenV2LoaderAnimationHeight int32  `dynamic:"true"`
	IntroScreenV2LoaderAnimationWidth  int32  `dynamic:"true"`
	IntroScreenV2BgImage               string `dynamic:"true"`
	IntroScreenV2BgImageHeight         int32  `dynamic:"true"`
	IntroScreenV2BgImageWidth          int32  `dynamic:"true"`
}

type FeatureConfig struct {
	PlatformVersionCheck *cfg.PlatformVersionCheck `dynamic:"true"`
	UserGroupCheck       *UserGroupCheck           `dynamic:"true"`
}

type UserGroupCheck struct {
	// Flag to enable feature for a set of users present in the allowed user groups below.
	// If false we will not have any user group checks.
	EnableUserGroupCheck bool `dynamic:"true"`
	// Set of user group for which feature will be enabled if the EnableUserGroupCheck is true.
	AllowedUserGrp []commontypes.UserGroup
}

type DisabledOnboardingScreenOptions struct {
	Text    string `dynamic:"true"`
	SubText string `dynamic:"true"`
	Icon    string `dynamic:"true"`
}

type BeneficiaryDetails struct {
	ActorId             string
	PaymentInstrumentId string
}

type UnsecuredCCRenewalFeeReversalConfig struct {
	IneligibleUsersSegmentId string `dynamic:"true"`
}

type CreditCardCacheConfig struct {
	CreditCardOfferCacheConfig *cfg.CacheConfig
	CardRequestCacheConfig     *cfg.CacheConfig
}

type CardRecommendationConfig struct {
	RecommendationRulesLists                    *RecommendationRulesLists
	RecommendationRuleToCardProgramWeightageMap map[string]map[string]map[string]float32
	DefaultWeightageConfig                      map[string]map[string]float32
	CardProgramReleaseConfig                    map[string]*CardProgramReleaseConfig
}

type CardProgramReleaseConfig struct {
	EnableCardProgram bool
	FeatureName       string
}

type RecommendationRulesLists struct {
	ActorSpecificRulesList       []string
	CardProgramSpecificRulesList []string
}

type DbConnectionAlias struct {
	CreditCardPgdbConnAlias string
}

type CreditCardUrl struct {
	CardActivationPageImageUrl        string
	GiftBoxIconUrl                    string
	RupeeIconUrl                      string
	CheckBoxIconUrl                   string
	PartnerShipUrl                    string
	CardDeliveryScreenIconUrl         string
	CCTermsAndConditionsUrl           string
	CCDeliveryAddressSelectStepNumber int
	CCOnboardingTotalSteps            int
}

type CreditCardBillPaymentInfo struct {
	BillGenDate    int64 `dynamic:"true"`
	PaymentDueDate int64 `dynamic:"true"`
	Default        bool  `dynamic:"true"`
}

type Application struct {
	Environment string
	Name        string
	ServerName  string
}

type Server struct {
	Ports *cfg.ServerPorts
}

type Aws struct {
	Region string
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool `dynamic:"true"`
	// Deprecated: Flag for enabling EMI conversion
	EnableEmiConversion bool `dynamic:"true"`
	// flag to enable/disable reissue card rate limiter
	EnableReissueCardRateLimiter bool `dynamic:"true"`
	// Flag to control whether to force an update before the credit card onboarding for the user.
	MinVersionForCCOnboarding *cfg.PlatformVersionCheck `dynamic:"true"`
}

type EnableFlagForCardProgram struct {
	CardPrograms  []string
	FeatureConfig *app.FeatureConfig `dynamic:"true"`
}

type Vendor struct {
	SftpUploadRemotePath string
}

type DepositConfig struct {
	MaxDepositAmountInRs       int
	MinDepositAmountInRs       int
	DepositLimitMultiplier     float32
	CreditLimitMultiplier      float32
	DefaultCreditLimitsInRs    []int
	DefaultSelectedCreditLimit int
	DefaultDepositTerm         *DepositTerm
	MinDepositTerm             *DepositTerm
	MaxDepositTerm             *DepositTerm
	MinCreditLimitInRs         int
	DepositSliderAmountsInRs   []*DepositAmountWithText
	DefaultDepositSliderAmount *DepositAmountWithText
}

type DepositAmountWithText struct {
	Amount        int
	DisplayString string
}

type DepositTerm struct {
	Days   int
	Months int
}

type CreditCardOffersScreenOptions struct {
	CreditLimitAmountString  string                  `dynamic:"true" quest:"variable"`
	CardImageUrl             string                  `dynamic:"true" quest:"variable"`
	InfoBlock                *InfoItem               `dynamic:"true" quest:"component"`
	TermsAndConditions       string                  `dynamic:"true" quest:"variable"`
	PartnershipUrl           string                  `dynamic:"true" quest:"variable"`
	GetCreditCardCtaText     string                  `dynamic:"true" quest:"variable"`
	Title                    string                  `dynamic:"true" quest:"variable"`
	HeaderDescription        string                  `dynamic:"true" quest:"variable"`
	PopUpTextBlock           *PopUpTextBlock         `dynamic:"true" quest:"component"`
	StaticImages             *InfoItem               `dynamic:"true" quest:"component"`
	FeesInfoHeader           string                  `dynamic:"true" quest:"variable"`
	FeesInfo                 *FeesInfoItem           `dynamic:"true" quest:"component"`
	AllFeesCta               *AllFeesCta             `dynamic:"true" quest:"component"`
	BroadVisualElementImgUrl string                  `dynamic:"true" quest:"variable"`
	RewardsWorthCtaInfo      *RewardsWorthCtaInfo    `dynamic:"true" quest:"component"`
	JoiningFeeVoucherInfo    *JoiningFeeVoucherInfo  `dynamic:"true" quest:"component"`
	AcceleratedRewardsInfo   *AcceleratedRewardsInfo `dynamic:"true" quest:"component"`
	FullScreenBlock          *CtaBlockInfo           `dynamic:"true" quest:"component"`
	LeftHalfBlock            *CtaBlockInfo           `dynamic:"true" quest:"component"`
	RightHalfBlock           *CtaBlockInfo           `dynamic:"true" quest:"component"`
}

type CreditCardOffersScreenOptionsV2 struct {
	CreditLimitAmountString     string                  `dynamic:"true" quest:"variable"`
	CardImageUrl                string                  `dynamic:"true" quest:"variable"`
	TermsAndConditions          string                  `dynamic:"true" quest:"variable"`
	PartnershipUrl              string                  `dynamic:"true" quest:"variable"`
	GetCreditCardCtaText        string                  `dynamic:"true" quest:"variable"`
	Title                       string                  `dynamic:"true" quest:"variable"`
	HeaderDescription           string                  `dynamic:"true" quest:"variable"`
	StaticImages                *InfoItem               `dynamic:"true" quest:"component"`
	FeesInfoHeader              string                  `dynamic:"true" quest:"variable"`
	FeesInfo                    *FeesInfoItem           `dynamic:"true" quest:"component"`
	AllFeesCta                  *AllFeesCta             `dynamic:"true" quest:"component"`
	BroadVisualElementImgUrl    string                  `dynamic:"true" quest:"variable"`
	BroadVisualElementImgUrlIOS string                  `dynamic:"true" quest:"variable"`
	RewardsWorthCtaInfo         *RewardsWorthCtaInfo    `dynamic:"true" quest:"component"`
	RewardsWorthInfo            *RewardsWorthInfo       `dynamic:"true" quest:"component"`
	FeesInfoV2                  *FessInfoV2             `dynamic:"true" quest:"component"`
	JoiningFeeVoucherInfo       *JoiningFeeVoucherInfo  `dynamic:"true" quest:"component"`
	AcceleratedRewardsInfo      *AcceleratedRewardsInfo `dynamic:"true" quest:"component"`
	WelcomeVoucherCta           *CtaBlockInfoV2         `dynamic:"true" quest:"component"`
	TopBrandsCta                *CtaBlockInfoV2         `dynamic:"true" quest:"component"`
	RewardEstimationCta         *CtaBlockInfoV2         `dynamic:"true" quest:"component"`
	// These are deprecated, we are still having them to support backward compatibility for older screens
	Cta1Name string `dynamic:"true" quest:"variable"`
	Cta2Name string `dynamic:"true" quest:"variable"`
	Cta3Name string `dynamic:"true" quest:"variable"`
}

type InfoItem struct {
	IconLink  string `dynamic:"true" quest:"variable"`
	Title     string `dynamic:"true" quest:"variable"`
	Desc      string `dynamic:"true" quest:"variable"`
	IconLink1 string `dynamic:"true" quest:"variable"`
	Title1    string `dynamic:"true" quest:"variable"`
	Desc1     string `dynamic:"true" quest:"variable"`
	IconLink2 string `dynamic:"true" quest:"variable"`
	Title2    string `dynamic:"true" quest:"variable"`
	Desc2     string `dynamic:"true" quest:"variable"`
}

type PopUpTextBlock struct {
	InfoTitle string `dynamic:"true" quest:"variable"`
	InfoDesc  string `dynamic:"true" quest:"variable"`
	CtaText   string `dynamic:"true" quest:"variable"`
}

type FeesInfoItem struct {
	Title           string          `dynamic:"true" quest:"variable"`
	Desc            string          `dynamic:"true" quest:"variable"`
	CtaTitle        string          `dynamic:"true" quest:"variable"`
	CtaInfoItem     *CtaInfoItem    `dynamic:"true" quest:"component"`
	BottomInfoItem  *BottomInfoItem `dynamic:"true" quest:"component"`
	CtaText         string          `dynamic:"true" quest:"variable"`
	CtaWebLink      string          `dynamic:"true" quest:"variable"`
	Title1          string          `dynamic:"true" quest:"variable"`
	Desc1           string          `dynamic:"true" quest:"variable"`
	CtaTitle1       string          `dynamic:"true" quest:"variable"`
	CtaInfoItem1    *CtaInfoItem    `dynamic:"true" quest:"component"`
	BottomInfoItem1 *BottomInfoItem `dynamic:"true" quest:"component"`
	CtaText1        string          `dynamic:"true" quest:"variable"`
	CtaWebLink1     string          `dynamic:"true" quest:"variable"`
}

type CtaInfoItem struct {
	IconLink  string `dynamic:"true" quest:"variable"`
	Title     string `dynamic:"true" quest:"variable"`
	Desc      string `dynamic:"true" quest:"variable"`
	IconLink1 string `dynamic:"true" quest:"variable"`
	Title1    string `dynamic:"true" quest:"variable"`
	Desc1     string `dynamic:"true" quest:"variable"`
	IconLink2 string `dynamic:"true" quest:"variable"`
	Title2    string `dynamic:"true" quest:"variable"`
	Desc2     string `dynamic:"true" quest:"variable"`
	IconLink3 string `dynamic:"true" quest:"variable"`
	Title3    string `dynamic:"true" quest:"variable"`
	Desc3     string `dynamic:"true" quest:"variable"`
	IconLink4 string `dynamic:"true" quest:"variable"`
	Title4    string `dynamic:"true" quest:"variable"`
	Desc4     string `dynamic:"true" quest:"variable"`
	IconLink5 string `dynamic:"true" quest:"variable"`
	Title5    string `dynamic:"true" quest:"variable"`
	Desc5     string `dynamic:"true" quest:"variable"`
	IconLink6 string `dynamic:"true" quest:"variable"`
	Title6    string `dynamic:"true" quest:"variable"`
	Desc6     string `dynamic:"true" quest:"variable"`
}

type BottomInfoItem struct {
	IconLink string `dynamic:"true" quest:"variable"`
	Title    string `dynamic:"true" quest:"variable"`
	Desc     string `dynamic:"true" quest:"variable"`
}

type AllFeesCta struct {
	CtaText    string `dynamic:"true" quest:"variable"`
	CtaWebLink string `dynamic:"true" quest:"variable"`
}

type RewardsWorthCtaInfo struct {
	CtaText           string `dynamic:"true" quest:"variable"`
	DpLinkScreenTitle string `dynamic:"true" quest:"variable"`
	DpLinkDescription string `dynamic:"true" quest:"variable"`
	DpLinkSubtitle    string `dynamic:"true" quest:"variable"`
}

type JoiningFeeVoucherInfo struct {
	IconLink   string `dynamic:"true" quest:"variable"`
	Title      string `dynamic:"true" quest:"variable"`
	CtaText    string `dynamic:"true" quest:"variable"`
	CtaWebLink string `dynamic:"true" quest:"variable"`
}

type AcceleratedRewardsInfo struct {
	Title             string `dynamic:"true" quest:"variable"`
	InfoIconLink      string `dynamic:"true" quest:"variable"`
	CtaText           string `dynamic:"true" quest:"variable"`
	DpLinkScreenTitle string `dynamic:"true" quest:"variable"`
	DpLinkDescription string `dynamic:"true" quest:"variable"`
	DpLinkSubtitle    string `dynamic:"true" quest:"variable"`
}

type CtaBlockInfo struct {
	IconLink string `dynamic:"true" quest:"variable"`
	Title    string `dynamic:"true" quest:"variable"`
	SubTitle string `dynamic:"true" quest:"variable"`
	CtaName  string `dynamic:"true" quest:"variable"`
	CtaText  string `dynamic:"true" quest:"variable"`
}

type CtaBlockInfoV2 struct {
	IconLink string `dynamic:"true" quest:"variable"`
	Title    string `dynamic:"true" quest:"variable"`
	CtaText  string `dynamic:"true" quest:"variable"`
}

type RewardsWorthInfo struct {
	Title    string `dynamic:"true" quest:"variable"`
	SubTitle string `dynamic:"true" quest:"variable"`
	Desc     string `dynamic:"true" quest:"variable"`
}

type FessInfoV2 struct {
	Title    string `dynamic:"true" quest:"variable"`
	SubTitle string `dynamic:"true" quest:"variable"`
	Desc     string `dynamic:"true" quest:"variable"`
}

type AcsNotificationRawDataStore struct {
	BucketName string `iam:"s3-readwrite"`
	ObjectKey  string
}

type SimplifiConfig struct {
	ControlConfig *ControlConfig
}

type ControlConfig struct {
	AtmMaxLimitMultiplier float32
}
