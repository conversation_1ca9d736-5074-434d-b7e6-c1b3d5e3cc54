//nolint:goimports
package firefly_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"testing"

	"github.com/go-redis/redismock/v9"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/money"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	ffPb "github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/accounting/enums"
	ffAccountingMocks "github.com/epifi/gamma/api/firefly/accounting/mocks"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/segment"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditcard/mocks"
	"github.com/epifi/gamma/firefly"
	daoMocks "github.com/epifi/gamma/firefly/dao/mocks"
)

var (
	CreditCards = []*ffPb.CreditCard{{
		Id:               "sampleId",
		ActorId:          "sampleActor",
		AccountId:        "sampleAccount",
		VendorIdentifier: "123",
		Vendor:           0,
		CardState:        ffEnumsPb.CardState_CARD_STATE_CREATED,
	},
		{
			Id:               "sampleId1",
			ActorId:          "sampleActor",
			AccountId:        "sampleAccount",
			VendorIdentifier: "1234",
			Vendor:           0,
			CardState:        ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
		},
	}

	sampleGetAccountResponse = &accounting.GetAccountResponse{
		Status: rpc.StatusOk(),
		Account: &accounting.CreditAccount{
			Id:          "ID",
			ActorId:     "sampleActor",
			ReferenceId: "rID",
		},
	}

	sampleGetCardList = &creditcard.GetCardListResponse{
		Status: rpc.StatusOk(),
		CardDetails: []*creditcard.CardDetails{{
			ExpiryDate:   nil,
			KitNumber:    "123",
			CardMaterial: 0,
			NetworkType:  0,
			CardNumber:   "",
			CardStatus:   ffEnumsPb.CardStatus_CLOSED,
		},
			{
				ExpiryDate:   nil,
				KitNumber:    "1234",
				CardMaterial: 0,
				NetworkType:  0,
				CardNumber:   "",
				CardStatus:   ffEnumsPb.CardStatus_ALLOCATED,
			},
		},
	}
)

func TestService_GetCreditCard(t *testing.T) {
	svc, md, testfunc := getFireflySvcWithMocks(t)
	defer testfunc()

	tests := []struct {
		name           string
		req            *ffPb.GetCreditCardRequest
		setupMockCalls func()
		want           *ffPb.GetCreditCardResponse
		wantErr        bool
	}{
		{
			name: "fetched successfully with card id",
			req: &ffPb.GetCreditCardRequest{
				GetBy: &ffPb.GetCreditCardRequest_CreditCardId{CreditCardId: "cc-id-1"},
			},
			setupMockCalls: func() {
				md.creditCardDao.EXPECT().GetById(gomock.Any(), "cc-id-1", nil).Return(&ffPb.CreditCard{
					Id: "cc-id-1",
				}, nil)
			},
			want: &ffPb.GetCreditCardResponse{
				Status: rpc.StatusOk(),
				CreditCard: &ffPb.CreditCard{
					Id: "cc-id-1",
				},
			},
			wantErr: false,
		},
		{
			name: "fetched successfully with card id",
			req: &ffPb.GetCreditCardRequest{
				GetBy: &ffPb.GetCreditCardRequest_VendorIdentifier{VendorIdentifier: "vendor-identifier-1"},
			},
			setupMockCalls: func() {
				md.creditCardDao.EXPECT().GetByVendorIdentifier(gomock.Any(), "vendor-identifier-1",
					nil).Return(&ffPb.CreditCard{
					Id: "cc-id-1",
				}, nil)
			},
			want: &ffPb.GetCreditCardResponse{
				Status: rpc.StatusOk(),
				CreditCard: &ffPb.CreditCard{
					Id: "cc-id-1",
				},
			},
			wantErr: false,
		},
		{
			name: "record not found",
			req: &ffPb.GetCreditCardRequest{
				GetBy: &ffPb.GetCreditCardRequest_VendorIdentifier{VendorIdentifier: "vendor-identifier-1"},
			},
			setupMockCalls: func() {
				md.creditCardDao.EXPECT().GetByVendorIdentifier(gomock.Any(), "vendor-identifier-1",
					nil).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &ffPb.GetCreditCardResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.GetCreditCard(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCreditCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCreditCard() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_RefreshCardDetails(t *testing.T) {

	ctr := gomock.NewController(t)
	ctx := context.Background()
	mockCCDao := daoMocks.NewMockCreditCardDao(ctr)
	mockCCVgClient := mocks.NewMockCreditCardClient(ctr)
	mockAccClient := ffAccountingMocks.NewMockAccountingClient(ctr)

	svc := firefly.NewService(nil, nil, mockCCDao, nil, nil, nil,
		mockCCVgClient, nil, nil, nil, mockAccClient, nil, nil,
		nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	type args struct {
		ctx context.Context
		req *ffPb.RefreshCardDetailsRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func()
		want           *ffPb.RefreshCardDetailsResponse
		wantErr        bool
	}{
		{
			name: "success, fetched and refreshed card state",
			args: args{
				ctx: ctx,
				req: &ffPb.RefreshCardDetailsRequest{ActorId: "sampleActor"},
			},
			setupMockCalls: func() {
				mockCCDao.EXPECT().GetByActorId(gomock.Any(), "sampleActor", nil).Return(CreditCards, nil)
				mockAccClient.EXPECT().GetAccount(gomock.Any(), &ffAccPb.GetAccountRequest{
					GetBy: &ffAccPb.GetAccountRequest_AccountId{AccountId: "sampleAccount"},
				}).Return(sampleGetAccountResponse, nil)
				mockCCVgClient.EXPECT().GetCardList(gomock.Any(), &creditcard.GetCardListRequest{
					Header:     &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_M2P},
					CustomerId: "rID",
				}).Return(sampleGetCardList, nil)
				mockCCDao.EXPECT().Update(gomock.Any(), &ffPb.CreditCard{
					Id:               "sampleId",
					ActorId:          "sampleActor",
					AccountId:        "sampleAccount",
					VendorIdentifier: "123",
					Vendor:           0,
					CardState:        ffEnumsPb.CardState_CARD_STATE_CLOSED,
				}, []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE}).Return(nil).AnyTimes()
			},
			want: &ffPb.RefreshCardDetailsResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "success no card found for user(actor)",
			args: args{
				ctx: ctx,
				req: &ffPb.RefreshCardDetailsRequest{ActorId: "sampleActor"},
			},
			setupMockCalls: func() {
				mockCCDao.EXPECT().GetByActorId(gomock.Any(), "sampleActor", nil).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &ffPb.RefreshCardDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failure, fetching cards from DB",
			args: args{
				ctx: ctx,
				req: &ffPb.RefreshCardDetailsRequest{ActorId: "sampleActor"},
			},
			setupMockCalls: func() {
				mockCCDao.EXPECT().GetByActorId(gomock.Any(), "sampleActor", nil).Return(nil, errors.New("error"))
			},
			want: &ffPb.RefreshCardDetailsResponse{
				Status: rpc.StatusFromError(errors.New("error")),
			},
			wantErr: false,
		},
		{
			name: "failure, fetching cards from vendor",
			args: args{
				ctx: ctx,
				req: &ffPb.RefreshCardDetailsRequest{ActorId: "sampleActor"},
			},
			setupMockCalls: func() {
				mockCCDao.EXPECT().GetByActorId(gomock.Any(), "sampleActor", nil).Return(CreditCards, nil)
				mockAccClient.EXPECT().GetAccount(gomock.Any(), &ffAccPb.GetAccountRequest{
					GetBy: &ffAccPb.GetAccountRequest_AccountId{AccountId: "sampleAccount"},
				}).Return(sampleGetAccountResponse, nil)
				mockCCVgClient.EXPECT().GetCardList(gomock.Any(), &creditcard.GetCardListRequest{
					Header:     &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_M2P},
					CustomerId: "rID",
				}).Return(&creditcard.GetCardListResponse{CardDetails: []*creditcard.CardDetails{{}}}, nil)
			},
			want: &ffPb.RefreshCardDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg("response status nil"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.RefreshCardDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RefreshCardDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RefreshCardDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_TriggerRealtimeCardEligibilityCheck(t *testing.T) {
	s, md, tearDown := getFireflySvcWithMocks(t)
	defer tearDown()

	tests := []struct {
		name           string
		req            *ffPb.TriggerRealtimeCardEligibilityCheckRequest
		want           *ffPb.TriggerRealtimeCardEligibilityCheckResponse
		wantErr        bool
		setupMockCalls func()
	}{
		{
			name: "TriggerRealtimeCardEligibilityCheck from scratch",
			req: &ffPb.TriggerRealtimeCardEligibilityCheckRequest{
				ActorId: "actor-1",
			},
			want: &ffPb.TriggerRealtimeCardEligibilityCheckResponse{
				Status: rpc.StatusOk(),
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_FIREFLY_GET_REQUEST_STATUS,
					ScreenOptions: &deeplink.Deeplink_FireflyGetRequestStatusScreenOptions{
						FireflyGetRequestStatusScreenOptions: &deeplink.FireflyGetRequestStatusScreenOptions{
							CardRequestId:  "card-req-1",
							DisplayMessage: "Hold on, while we check for your eligibility",
						},
					},
				},
			},
			wantErr: false,
			setupMockCalls: func() {
				md.cardRequestDao.EXPECT().GetByActorIdAndWorkflow(gomock.Any(), "actor-1", ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK,
					nil).Return(nil, epifierrors.ErrRecordNotFound).Times(1)

				md.cardRequestDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&ffPb.CardRequest{
					Id:              "card-req-1",
					CardId:          "card-1",
					ActorId:         "actor-1",
					OrchestrationId: "orch-1",
					Vendor:          commonvgpb.Vendor_M2P,
					Workflow:        ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK,
					Status:          ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_CREATED,
					Provenance:      ffEnumsPb.Provenance_PROVENANCE_APP,
				}, nil).Times(1)

				md.cardRequestDao.EXPECT().Update(gomock.Any(), &ffPb.CardRequest{
					Id:              "card-req-1",
					CardId:          "card-1",
					ActorId:         "actor-1",
					OrchestrationId: "orch-1",
					Vendor:          commonvgpb.Vendor_M2P,
					Workflow:        ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK,
					Status:          ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_CREATED,
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_FIREFLY_GET_REQUEST_STATUS,
						ScreenOptions: &deeplink.Deeplink_FireflyGetRequestStatusScreenOptions{
							FireflyGetRequestStatusScreenOptions: &deeplink.FireflyGetRequestStatusScreenOptions{
								CardRequestId:  "card-req-1",
								DisplayMessage: "Hold on, while we check for your eligibility",
							},
						},
					},
					Provenance: ffEnumsPb.Provenance_PROVENANCE_APP,
				}, []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION}).
					Return(nil).Times(1)

				md.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{ActorId: "actor-1"},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
				}, nil)

				md.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any()).Return(
					&celestialPb.InitiateWorkflowResponse{
						Status: rpc.StatusOk(),
					}, nil)

			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()

			got, err := s.TriggerRealtimeCardEligibilityCheck(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("TriggerRealtimeCardEligibilityCheck() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("TriggerRealtimeCardEligibilityCheck() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsCreditCardUser(t *testing.T) {
	tests := []struct {
		name            string
		actorId         string
		redisResponse   string
		redisErr        error
		mockCreditCards []*ffPb.CreditCard
		mockCCDaoTimes  int
		databaseResult  []*ffPb.CreditCard
		expectedResult  bool
		expectedError   error
	}{
		{
			name:           "Check Redis cache - present & success",
			actorId:        "actor1",
			redisResponse:  "true",
			mockCCDaoTimes: 0,
			expectedResult: true,
		},
		{
			name:           "Check Redis cache - false & success",
			actorId:        "actor1",
			redisResponse:  "false",
			mockCCDaoTimes: 0,
			expectedResult: false,
		},
		{
			name:           "Card state is active",
			actorId:        "actor3",
			redisErr:       errors.New("redis error"),
			expectedResult: true,
			mockCCDaoTimes: 1,
			mockCreditCards: []*ffPb.CreditCard{
				{
					Id:        "card3",
					ActorId:   "actor3",
					CardState: ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
				},
			},
		},
		{
			name:           "Card state is closed for the user",
			actorId:        "actor4",
			redisErr:       errors.New("redis error"),
			expectedResult: false,
			mockCCDaoTimes: 1,
			mockCreditCards: []*ffPb.CreditCard{
				{
					Id:        "card4",
					ActorId:   "actor4",
					CardState: ffEnumsPb.CardState_CARD_STATE_CLOSED,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockCreditCardDao := daoMocks.NewMockCreditCardDao(ctrl)
			mockCreditCardDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.mockCreditCards, nil).Times(tt.mockCCDaoTimes)

			db, mock := redismock.NewClientMock()
			if tt.redisErr != nil {
				mock.ExpectGet(fmt.Sprintf("%s-%s", ffConf.RedisCachePrefix, tt.actorId)).SetErr(tt.redisErr)
				// mock.ExpectGet(tt.actorId).SetErr(tt.redisErr)
			} else {
				mock.ExpectGet(fmt.Sprintf("%s-%s", ffConf.RedisCachePrefix, tt.actorId)).SetVal(tt.redisResponse)
			}

			service := firefly.NewService(nil, nil, mockCreditCardDao, nil, nil, nil, nil, nil, nil, ffConf, nil,
				nil, nil, nil, nil, nil, nil, nil, nil,
				nil, nil, nil, nil, nil, nil,
				nil, nil, db, nil, nil, nil, nil, nil,
				nil, nil, nil, nil, nil)

			response, err := service.IsCreditCardUser(context.Background(), &ffPb.IsCreditCardUserRequest{ActorId: tt.actorId})

			assert.Equal(t, tt.expectedResult, response.IsCreditCardUser)
			assert.Equal(t, tt.expectedError, err)
		})
	}
}

func TestService_UserCreditCardHistory(t *testing.T) {
	s, md, tearDown := getFireflySvcWithMocks(t)
	defer tearDown()

	tests := []struct {
		name           string
		req            *ffPb.UserCreditCardHistoryRequest
		setupMockCalls func()
		want           *ffPb.UserCreditCardHistoryResponse
		wantErr        bool
	}{
		{
			name: "Success - Card found in V1",
			req:  &ffPb.UserCreditCardHistoryRequest{ActorId: "actor1"},
			setupMockCalls: func() {
				md.creditCardDao.EXPECT().GetByActorId(gomock.Any(), "actor1", []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID, ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE}).
					Return([]*ffPb.CreditCard{{Id: "card1", CardState: ffEnumsPb.CardState_CARD_STATE_CLOSED}}, nil)
			},
			want: &ffPb.UserCreditCardHistoryResponse{Status: rpc.StatusOk(), HasEverHadCreditCard: true},
		},
		{
			name: "Success - Card not found in V1 or V2",
			req:  &ffPb.UserCreditCardHistoryRequest{ActorId: "actor3"},
			setupMockCalls: func() {
				md.creditCardDao.EXPECT().GetByActorId(gomock.Any(), "actor3", []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID, ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE}).
					Return(nil, epifierrors.ErrRecordNotFound)
				md.fireflyV2Client.EXPECT().GetCreditCards(gomock.Any(), &ffBeV2Pb.GetCreditCardsRequest{
					Identifier: &ffBeV2Pb.GetCreditCardsRequest_ActorId{
						ActorId: "actor3",
					},
					StateFilters: []ccEnumsV2Pb.CardState{ccEnumsV2Pb.CardState_CARD_STATE_CREATED, ccEnumsV2Pb.CardState_CARD_STATE_CLOSED}}).
					Return(&ffBeV2Pb.GetCreditCardsResponse{Status: rpc.StatusRecordNotFound()}, nil)
			},
			want: &ffPb.UserCreditCardHistoryResponse{Status: rpc.StatusOk(), HasEverHadCreditCard: false},
		},
		{
			name: "Failure - DB error on V1 call",
			req:  &ffPb.UserCreditCardHistoryRequest{ActorId: "actor4"},
			setupMockCalls: func() {
				md.creditCardDao.EXPECT().GetByActorId(gomock.Any(), "actor4", []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID, ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE}).
					Return(nil, errors.New("db error"))
			},
			want: &ffPb.UserCreditCardHistoryResponse{Status: rpc.StatusFromError(errors.New("db error"))},
		},
		{
			name: "Success - Card found in V2",
			req:  &ffPb.UserCreditCardHistoryRequest{ActorId: "actor2"},
			setupMockCalls: func() {
				md.creditCardDao.EXPECT().GetByActorId(gomock.Any(), "actor2", []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID, ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE}).
					Return(nil, epifierrors.ErrRecordNotFound)
				md.fireflyV2Client.EXPECT().GetCreditCards(gomock.Any(), &ffBeV2Pb.GetCreditCardsRequest{
					Identifier: &ffBeV2Pb.GetCreditCardsRequest_ActorId{
						ActorId: "actor2",
					},
					StateFilters: []ccEnumsV2Pb.CardState{ccEnumsV2Pb.CardState_CARD_STATE_CREATED, ccEnumsV2Pb.CardState_CARD_STATE_CLOSED}}).Return(&ffBeV2Pb.GetCreditCardsResponse{
					Status: rpc.StatusOk(),
					CreditCards: []*ffBeV2Pb.CreditCard{{
						Id:    "card1",
						State: ccEnumsV2Pb.CardState_CARD_STATE_CLOSED,
					}},
				}, nil)
			},
			want: &ffPb.UserCreditCardHistoryResponse{Status: rpc.StatusOk(), HasEverHadCreditCard: true},
		},
		{
			name: "Failure - DB error on V1 call",
			req:  &ffPb.UserCreditCardHistoryRequest{ActorId: "actor4"},
			setupMockCalls: func() {
				md.creditCardDao.EXPECT().GetByActorId(gomock.Any(), "actor4", []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID, ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE}).
					Return(nil, errors.New("db error"))
			},
			want: &ffPb.UserCreditCardHistoryResponse{Status: rpc.StatusFromError(errors.New("db error"))},
		},
		{
			name: "Failure - V2 client error",
			req:  &ffPb.UserCreditCardHistoryRequest{ActorId: "actor5"},
			setupMockCalls: func() {
				md.creditCardDao.EXPECT().GetByActorId(gomock.Any(), "actor5", []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID, ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE}).
					Return(nil, epifierrors.ErrRecordNotFound)
				md.fireflyV2Client.EXPECT().GetCreditCards(gomock.Any(), &ffBeV2Pb.GetCreditCardsRequest{
					Identifier: &ffBeV2Pb.GetCreditCardsRequest_ActorId{
						ActorId: "actor5",
					},
					StateFilters: []ccEnumsV2Pb.CardState{ccEnumsV2Pb.CardState_CARD_STATE_CREATED, ccEnumsV2Pb.CardState_CARD_STATE_CLOSED}}).Return(nil, errors.New("v2 error"))
			},
			want: &ffPb.UserCreditCardHistoryResponse{Status: rpc.StatusFromError(epifigrpc.RPCError(nil, errors.New("v2 error")))},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := s.UserCreditCardHistory(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserCreditCardHistory() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UserCreditCardHistory() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_FetchCardLimits(t *testing.T) {
	svc, md, testfunc := getFireflySvcWithMocks(t)
	defer testfunc()

	type args struct {
		ctx context.Context
		req *ffPb.FetchCardLimitsRequest
	}
	tests := []struct {
		name       string
		args       args
		want       *ffPb.FetchCardLimitsResponse
		wantErr    bool
		setupMocks func()
	}{
		{
			name: "Successful limit fetch with no disabled types",
			args: args{
				ctx: context.Background(),
				req: &ffPb.FetchCardLimitsRequest{CreditCardId: "cc-1"},
			},
			want: &ffPb.FetchCardLimitsResponse{
				Status: rpc.StatusOk(),
				ControlLimits: []*ffPb.FetchCardLimitsResponse_ControlLimits{
					{
						CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
						DailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        100000,
						},
						MinDailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        1,
						},
						MaxDailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        100000,
						},
						LimitDisabled: false,
					},
					{
						CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS,
						DailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        100000,
						},
						MinDailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        1,
						},
						MaxDailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        100000,
						},
						LimitDisabled: false,
					},
					{
						CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM,
						DailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        100000,
						},
						MinDailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        1,
						},
						MaxDailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        100000,
						},
						LimitDisabled: false,
					},
				},
				CardState: ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
				CardLimits: map[string]*ffPb.FetchCardLimitsResponse_CardLimitDetails{
					"CARD_USAGE_LOCATION_TYPE_DOMESTIC": {
						ControlLimits: []*ffPb.FetchCardLimitsResponse_ControlLimits{
							{
								CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LimitDisabled: false,
							},
							{
								CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LimitDisabled: false,
							},
							{
								CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LimitDisabled: false,
							},
						},
					},
					"CARD_USAGE_LOCATION_TYPE_INTERNATIONAL": {
						ControlLimits: []*ffPb.FetchCardLimitsResponse_ControlLimits{
							{
								CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LimitDisabled: false,
							},
							{
								CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LimitDisabled: false,
							},
							{
								CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LimitDisabled: false,
							},
						},
					},
				},
				CreditCardId: "cc-1",
			},
			wantErr: false,
			setupMocks: func() {
				md.creditCardDao.EXPECT().GetById(gomock.Any(), "cc-1", gomock.Any()).Return(&ffPb.CreditCard{
					Id:               "cc-1",
					ActorId:          "ac-1",
					AccountId:        "acc-1",
					CardForm:         ffEnumsPb.CardForm_CARD_FORM_PHYSICAL,
					CardNetworkType:  ffEnumsPb.CardNetworkType_CARD_NETWORK_TYPE_VISA,
					VendorIdentifier: "vi-1",
					Vendor:           commonvgpb.Vendor_M2P,
					CardState:        ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
					ControlDetails: &ffPb.CardControlDetails{
						Atm:           true,
						Ecom:          true,
						Pos:           true,
						Contactless:   true,
						International: true,
					},
					BasicInfo: nil,
					CardLimits: &ffPb.CardLimits{
						Limits: []*ffPb.CardLimits_LimitsForControlTypePerLocation{
							{
								ControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LocationType: ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_DOMESTIC,
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
							{
								ControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LocationType: ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_DOMESTIC,
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
							{
								ControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LocationType: ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_DOMESTIC,
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
							{
								ControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LocationType: ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_INTERNATIONAL,
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
							{
								ControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LocationType: ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_INTERNATIONAL,
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
							{
								ControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LocationType: ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_INTERNATIONAL,
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
						},
					},
				}, nil)
				md.ffAccountingClient.EXPECT().GetAccount(gomock.Any(), &ffAccPb.GetAccountRequest{
					GetBy: &ffAccPb.GetAccountRequest_AccountId{AccountId: "acc-1"},
				}).Return(&ffAccPb.GetAccountResponse{Status: rpc.StatusOk(), Account: &ffAccPb.CreditAccount{
					Id:          "acc-1",
					ActorId:     "ac-1",
					ReferenceId: "ref-1",
					TotalLimit:  100000,
				}}, nil)
			},
		},
		{
			name: "Successful limit fetch with International off",
			args: args{
				ctx: context.Background(),
				req: &ffPb.FetchCardLimitsRequest{CreditCardId: "cc-2"},
			},
			want: &ffPb.FetchCardLimitsResponse{
				Status: rpc.StatusOk(),
				ControlLimits: []*ffPb.FetchCardLimitsResponse_ControlLimits{
					{
						CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
						DailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        100000,
						},
						MinDailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        1,
						},
						MaxDailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        100000,
						},
						LimitDisabled: false,
					},
					{
						CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS,
						DailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        100000,
						},
						MinDailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        1,
						},
						MaxDailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        100000,
						},
						LimitDisabled: false,
					},
					{
						CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM,
						DailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        100000,
						},
						MinDailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        1,
						},
						MaxDailyLimitValue: &money.Money{
							CurrencyCode: "INR",
							Units:        100000,
						},
						LimitDisabled: false,
					},
				},
				CardState: ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
				CardLimits: map[string]*ffPb.FetchCardLimitsResponse_CardLimitDetails{
					"CARD_USAGE_LOCATION_TYPE_DOMESTIC": {
						ControlLimits: []*ffPb.FetchCardLimitsResponse_ControlLimits{
							{
								CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LimitDisabled: false,
							},
							{
								CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LimitDisabled: false,
							},
							{
								CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LimitDisabled: false,
							},
						},
					},
					"CARD_USAGE_LOCATION_TYPE_INTERNATIONAL": {
						CardLimitDisablementType: ffEnumsPb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_INTERNATIONAL_DISABLED,
						ControlLimits: []*ffPb.FetchCardLimitsResponse_ControlLimits{
							{
								CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LimitDisabled: true,
							},
							{
								CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LimitDisabled: true,
							},
							{
								CardControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LimitDisabled: true,
							},
						},
					},
				},
				CreditCardId: "cc-2",
			},
			wantErr: false,
			setupMocks: func() {
				md.creditCardDao.EXPECT().GetById(gomock.Any(), "cc-2", gomock.Any()).Return(&ffPb.CreditCard{
					Id:               "cc-2",
					ActorId:          "ac-2",
					AccountId:        "acc-2",
					CardForm:         ffEnumsPb.CardForm_CARD_FORM_PHYSICAL,
					CardNetworkType:  ffEnumsPb.CardNetworkType_CARD_NETWORK_TYPE_VISA,
					VendorIdentifier: "vi-2",
					Vendor:           commonvgpb.Vendor_M2P,
					CardState:        ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
					ControlDetails: &ffPb.CardControlDetails{
						Atm:           true,
						Ecom:          true,
						Pos:           true,
						Contactless:   true,
						International: false,
					},
					BasicInfo: nil,
					CardLimits: &ffPb.CardLimits{
						Limits: []*ffPb.CardLimits_LimitsForControlTypePerLocation{
							{
								ControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LocationType: ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_DOMESTIC,
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
							{
								ControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LocationType: ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_DOMESTIC,
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
							{
								ControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LocationType: ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_DOMESTIC,
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
							{
								ControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LocationType: ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_INTERNATIONAL,
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
							{
								ControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LocationType: ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_INTERNATIONAL,
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
							{
								ControlType: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM,
								DailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LocationType: ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_INTERNATIONAL,
								MinDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1,
								},
								MaxDailyLimitValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
						},
					},
				}, nil)
				md.ffAccountingClient.EXPECT().GetAccount(gomock.Any(), &ffAccPb.GetAccountRequest{
					GetBy: &ffAccPb.GetAccountRequest_AccountId{AccountId: "acc-2"},
				}).Return(&ffAccPb.GetAccountResponse{Status: rpc.StatusOk(), Account: &ffAccPb.CreditAccount{
					Id:          "acc-2",
					ActorId:     "ac-2",
					ReferenceId: "ref-2",
					TotalLimit:  100000,
				}}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			got, err := svc.FetchCardLimits(tt.args.ctx, tt.args.req)
			assert.Equal(t, tt.wantErr, err != nil, "FetchCardLimits(%v, %v)", tt.args.ctx, tt.args.req)
			assert.Equal(t, tt.want, got, "FetchCardLimits(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}

func TestService_TriggerUnsecuredCCRenewalFeeReversal(t *testing.T) {
	ctx := context.Background()
	svc, md, testfunc := getFireflySvcWithMocks(t)
	defer testfunc()

	sampleActorId := "sample-actor"
	sampleAccountId := "sample-account"
	sampleRefId := "sample-ref"
	sampleCardId := "sample-card"
	sampleVendorExternalTxnId := "vendorExtTxnId_FEES"
	sampleAmount := &money.Money{
		CurrencyCode: "INR",
		Units:        2000,
	}
	annualMembershipFeesBeneficiaryName := "AmpliFi Renewal Fee"
	testSegmentId := "AWS_test-segment"
	provenance := ffEnumsPb.Provenance_PROVENANCE_INTERNAL
	sampleDescription := "sampleDescription"
	unsecuredCardProgram := &types.CardProgram{
		CardProgramType: types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED,
	}

	type args struct {
		ctx context.Context
		req *ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest
	}
	t.Parallel()
	tests := []struct {
		name           string
		args           args
		setupMockCalls func()
		want           *ffPb.TriggerUnsecuredCCRenewalFeeReversalResponse
		wantErr        bool
	}{
		{
			name: "success",
			args: args{
				ctx: ctx,
				req: &ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest{
					ActorId:     sampleActorId,
					Provenance:  provenance,
					Description: sampleDescription,
				},
			},
			want: &ffPb.TriggerUnsecuredCCRenewalFeeReversalResponse{
				Status: rpc.StatusOk(),
			},
			setupMockCalls: func() {
				md.mockSegmentationService.EXPECT().IsMember(ctx, &segment.IsMemberRequest{
					ActorId:    sampleActorId,
					SegmentIds: []string{testSegmentId},
				}).Return(&segment.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						testSegmentId: {
							IsActorMember: false,
						},
					},
				}, nil)
				md.ffAccountingClient.EXPECT().GetAccounts(ctx, &ffAccPb.GetAccountsRequest{
					GetBy: &ffAccPb.GetAccountsRequest_ActorId{ActorId: sampleActorId},
				}).Return(&ffAccPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*ffAccPb.CreditAccount{
						{
							Id:          sampleAccountId,
							CardProgram: unsecuredCardProgram,
							ReferenceId: sampleRefId,
						},
					},
				}, nil)

				md.creditCardDao.EXPECT().GetByActorId(ctx, sampleActorId, nil).Return([]*ffPb.CreditCard{{
					Id:        sampleCardId,
					CardState: ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
				}}, nil)

				md.ffAccountingClient.EXPECT().GetTransactions(ctx, &ffAccPb.GetTransactionsRequest{
					GetBy: &ffAccPb.GetTransactionsRequest_AccountIdAndCategories{
						AccountIdAndCategories: &ffAccPb.AccountIdAndCategories{
							AccountId:             sampleAccountId,
							TransactionCategories: []enums.TransactionCategory{enums.TransactionCategory_TRANSACTION_CATEGORY_FEES},
						},
					},
				}).Return(&ffAccPb.GetTransactionsResponse{
					Status: rpc.StatusOk(),
					Transactions: []*ffAccPb.CardTransaction{{
						VendorExtTxnId: sampleVendorExternalTxnId,
						Amount:         sampleAmount,
						BeneficiaryInfo: &ffAccPb.BeneficiaryInfo{
							BeneficiaryName: annualMembershipFeesBeneficiaryName,
						},
					}},
				}, nil)

				md.cardRequestDao.EXPECT().GetByOrchestrationId(ctx, sampleVendorExternalTxnId, nil).Return(nil, epifierrors.ErrRecordNotFound)
				md.cardRequestDao.EXPECT().Create(ctx, gomock.Any()).Return(&ffPb.CardRequest{
					OrchestrationId: sampleVendorExternalTxnId,
					Vendor:          commonvgpb.Vendor_M2P,
					RequestDetails: &ffPb.CardRequestDetails{
						Data: &ffPb.CardRequestDetails_RenewalFeeWaiverRequestDetails{
							RenewalFeeWaiverRequestDetails: &ffPb.RenewalFeeWaiverRequestDetails{
								RenewalFeeWaiverType: ffEnumsPb.FeeWaiverType_FEE_WAIVER_TYPE_FEE_REVERSAL,
							},
						},
						CardProgram: unsecuredCardProgram,
					},
				}, nil)

				md.mockVgClient.EXPECT().FeeReversal(ctx, &creditcard.FeeReversalRequest{
					Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_M2P},
					ExtTxnId:    strings.TrimSuffix(sampleVendorExternalTxnId, "_FEES"),
					EntityId:    sampleRefId,
					Description: sampleDescription,
					Amount:      sampleAmount,
				}).Return(&creditcard.FeeReversalResponse{
					Status: rpc.StatusOk(),
				}, nil)

				md.cardRequestDao.EXPECT().Update(ctx, gomock.Any(), []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS}).Return(nil)
			},
		},
		{
			name: "failure, user is dpd30+",
			args: args{
				ctx: ctx,
				req: &ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest{
					ActorId:     sampleActorId,
					Provenance:  provenance,
					Description: sampleDescription,
				},
			},
			want: &ffPb.TriggerUnsecuredCCRenewalFeeReversalResponse{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("User is Dpd"),
			},
			setupMockCalls: func() {
				md.mockSegmentationService.EXPECT().IsMember(ctx, &segment.IsMemberRequest{
					ActorId:    sampleActorId,
					SegmentIds: []string{testSegmentId},
				}).Return(&segment.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						testSegmentId: {
							IsActorMember: true,
						},
					},
				}, nil)
			},
		},
		{
			name: "failure, for non unsecured card program",
			args: args{
				ctx: ctx,
				req: &ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest{
					ActorId:     sampleActorId,
					Provenance:  provenance,
					Description: sampleDescription,
				},
			},
			want: &ffPb.TriggerUnsecuredCCRenewalFeeReversalResponse{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("user does not have an unsecured credit card"),
			},
			setupMockCalls: func() {
				md.mockSegmentationService.EXPECT().IsMember(ctx, &segment.IsMemberRequest{
					ActorId:    sampleActorId,
					SegmentIds: []string{testSegmentId},
				}).Return(&segment.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						testSegmentId: {
							IsActorMember: false,
						},
					},
				}, nil)
				md.ffAccountingClient.EXPECT().GetAccounts(ctx, &ffAccPb.GetAccountsRequest{
					GetBy: &ffAccPb.GetAccountsRequest_ActorId{ActorId: sampleActorId},
				}).Return(&ffAccPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*ffAccPb.CreditAccount{
						{
							Id:          sampleAccountId,
							CardProgram: &types.CardProgram{CardProgramType: types.CardProgramType_CARD_PROGRAM_TYPE_SECURED},
							ReferenceId: sampleRefId,
						},
					},
				}, nil)
			},
		},
		{
			name: "failure, card state closed",
			args: args{
				ctx: ctx,
				req: &ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest{
					ActorId:     sampleActorId,
					Provenance:  provenance,
					Description: sampleDescription,
				},
			},
			want: &ffPb.TriggerUnsecuredCCRenewalFeeReversalResponse{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("credit card has been closed, cannot reverse the fee"),
			},
			setupMockCalls: func() {
				md.mockSegmentationService.EXPECT().IsMember(ctx, &segment.IsMemberRequest{
					ActorId:    sampleActorId,
					SegmentIds: []string{testSegmentId},
				}).Return(&segment.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						testSegmentId: {
							IsActorMember: false,
						},
					},
				}, nil)
				md.ffAccountingClient.EXPECT().GetAccounts(ctx, &ffAccPb.GetAccountsRequest{
					GetBy: &ffAccPb.GetAccountsRequest_ActorId{ActorId: sampleActorId},
				}).Return(&ffAccPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*ffAccPb.CreditAccount{
						{
							Id:          sampleAccountId,
							CardProgram: unsecuredCardProgram,
							ReferenceId: sampleRefId,
						},
					},
				}, nil)

				md.creditCardDao.EXPECT().GetByActorId(ctx, sampleActorId, nil).Return([]*ffPb.CreditCard{{
					Id:        sampleCardId,
					CardState: ffEnumsPb.CardState_CARD_STATE_CLOSED,
				}}, nil)
			},
		},
		{
			name: "card request found, with failed status",
			args: args{
				ctx: ctx,
				req: &ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest{
					ActorId:     sampleActorId,
					Provenance:  provenance,
					Description: sampleDescription,
				},
			},
			want: &ffPb.TriggerUnsecuredCCRenewalFeeReversalResponse{
				Status: rpc.StatusOk(),
			},
			setupMockCalls: func() {
				md.mockSegmentationService.EXPECT().IsMember(ctx, &segment.IsMemberRequest{
					ActorId:    sampleActorId,
					SegmentIds: []string{testSegmentId},
				}).Return(&segment.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						testSegmentId: {
							IsActorMember: false,
						},
					},
				}, nil)
				md.ffAccountingClient.EXPECT().GetAccounts(ctx, &ffAccPb.GetAccountsRequest{
					GetBy: &ffAccPb.GetAccountsRequest_ActorId{ActorId: sampleActorId},
				}).Return(&ffAccPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*ffAccPb.CreditAccount{
						{
							Id:          sampleAccountId,
							CardProgram: unsecuredCardProgram,
							ReferenceId: sampleRefId,
						},
					},
				}, nil)
				md.creditCardDao.EXPECT().GetByActorId(ctx, sampleActorId, nil).Return([]*ffPb.CreditCard{{
					Id:        sampleCardId,
					CardState: ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
				}}, nil)
				md.ffAccountingClient.EXPECT().GetTransactions(ctx, gomock.Any()).Return(&ffAccPb.GetTransactionsResponse{
					Status: rpc.StatusOk(),
					Transactions: []*ffAccPb.CardTransaction{{
						VendorExtTxnId: sampleVendorExternalTxnId,
						Amount:         sampleAmount,
						BeneficiaryInfo: &ffAccPb.BeneficiaryInfo{
							BeneficiaryName: annualMembershipFeesBeneficiaryName,
						},
					}},
				}, nil)
				md.cardRequestDao.EXPECT().GetByOrchestrationId(ctx, sampleVendorExternalTxnId, nil).Return(&ffPb.CardRequest{
					OrchestrationId: sampleVendorExternalTxnId,
					Vendor:          commonvgpb.Vendor_M2P,
					RequestDetails: &ffPb.CardRequestDetails{
						Data: &ffPb.CardRequestDetails_RenewalFeeWaiverRequestDetails{
							RenewalFeeWaiverRequestDetails: &ffPb.RenewalFeeWaiverRequestDetails{
								RenewalFeeWaiverType: ffEnumsPb.FeeWaiverType_FEE_WAIVER_TYPE_FEE_REVERSAL,
							},
						},
						CardProgram: unsecuredCardProgram,
					},
					Status: ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED,
				}, nil)
				md.cardRequestDao.EXPECT().Create(ctx, gomock.Any()).Return(&ffPb.CardRequest{
					OrchestrationId: sampleVendorExternalTxnId,
					Vendor:          commonvgpb.Vendor_M2P,
					RequestDetails: &ffPb.CardRequestDetails{
						Data: &ffPb.CardRequestDetails_RenewalFeeWaiverRequestDetails{
							RenewalFeeWaiverRequestDetails: &ffPb.RenewalFeeWaiverRequestDetails{
								RenewalFeeWaiverType: ffEnumsPb.FeeWaiverType_FEE_WAIVER_TYPE_FEE_REVERSAL,
							},
						},
						CardProgram: unsecuredCardProgram,
					},
				}, nil)

				md.mockVgClient.EXPECT().FeeReversal(ctx, &creditcard.FeeReversalRequest{
					Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_M2P},
					ExtTxnId:    strings.TrimSuffix(sampleVendorExternalTxnId, "_FEES"),
					EntityId:    sampleRefId,
					Description: sampleDescription,
					Amount:      sampleAmount,
				}).Return(&creditcard.FeeReversalResponse{
					Status: rpc.StatusOk(),
				}, nil)

				md.cardRequestDao.EXPECT().Update(ctx, gomock.Any(), []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS}).Return(nil)
			},
		},
		{
			name: "segmentation service error",
			args: args{
				ctx: ctx,
				req: &ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest{
					ActorId:     sampleActorId,
					Provenance:  provenance,
					Description: sampleDescription,
				},
			},
			want: nil,
			setupMockCalls: func() {
				md.mockSegmentationService.EXPECT().IsMember(ctx, &segment.IsMemberRequest{
					ActorId:    sampleActorId,
					SegmentIds: []string{testSegmentId},
				}).Return(nil, fmt.Errorf("segmentation service error"))
			},
			wantErr: true,
		},
		{
			name: "accounting client error",
			args: args{
				ctx: ctx,
				req: &ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest{
					ActorId:     sampleActorId,
					Provenance:  provenance,
					Description: sampleDescription,
				},
			},
			want: nil,
			setupMockCalls: func() {
				md.mockSegmentationService.EXPECT().IsMember(ctx, &segment.IsMemberRequest{
					ActorId:    sampleActorId,
					SegmentIds: []string{testSegmentId},
				}).Return(&segment.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						testSegmentId: {
							IsActorMember: false,
						},
					},
				}, nil)
				md.ffAccountingClient.EXPECT().GetAccounts(ctx, &ffAccPb.GetAccountsRequest{
					GetBy: &ffAccPb.GetAccountsRequest_ActorId{ActorId: sampleActorId},
				}).Return(&ffAccPb.GetAccountsResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "credit card dao error",
			args: args{
				ctx: ctx,
				req: &ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest{
					ActorId:     sampleActorId,
					Provenance:  provenance,
					Description: sampleDescription,
				},
			},
			want: nil,
			setupMockCalls: func() {
				md.mockSegmentationService.EXPECT().IsMember(ctx, &segment.IsMemberRequest{
					ActorId:    sampleActorId,
					SegmentIds: []string{testSegmentId},
				}).Return(&segment.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						testSegmentId: {
							IsActorMember: false,
						},
					},
				}, nil)
				md.ffAccountingClient.EXPECT().GetAccounts(ctx, &ffAccPb.GetAccountsRequest{
					GetBy: &ffAccPb.GetAccountsRequest_ActorId{ActorId: sampleActorId},
				}).Return(&ffAccPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*ffAccPb.CreditAccount{
						{
							Id:          sampleAccountId,
							CardProgram: unsecuredCardProgram,
							ReferenceId: sampleRefId,
						},
					},
				}, nil)
				md.creditCardDao.EXPECT().GetByActorId(ctx, sampleActorId, nil).Return(nil, fmt.Errorf("credit card dao error"))
			},
			wantErr: true,
		},
		{
			name: "get transactions error",
			args: args{
				ctx: ctx,
				req: &ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest{
					ActorId:     sampleActorId,
					Provenance:  provenance,
					Description: sampleDescription,
				},
			},
			want: nil,
			setupMockCalls: func() {
				md.mockSegmentationService.EXPECT().IsMember(ctx, &segment.IsMemberRequest{
					ActorId:    sampleActorId,
					SegmentIds: []string{testSegmentId},
				}).Return(&segment.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						testSegmentId: {
							IsActorMember: false,
						},
					},
				}, nil)
				md.ffAccountingClient.EXPECT().GetAccounts(ctx, &ffAccPb.GetAccountsRequest{
					GetBy: &ffAccPb.GetAccountsRequest_ActorId{ActorId: sampleActorId},
				}).Return(&ffAccPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*ffAccPb.CreditAccount{
						{
							Id:          sampleAccountId,
							CardProgram: unsecuredCardProgram,
							ReferenceId: sampleRefId,
						},
					},
				}, nil)
				md.creditCardDao.EXPECT().GetByActorId(ctx, sampleActorId, nil).Return([]*ffPb.CreditCard{{
					Id:        sampleCardId,
					CardState: ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
				}}, nil)
				md.ffAccountingClient.EXPECT().GetTransactions(ctx, gomock.Any()).Return(&ffAccPb.GetTransactionsResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "card request create error",
			args: args{
				ctx: ctx,
				req: &ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest{
					ActorId:     sampleActorId,
					Provenance:  provenance,
					Description: sampleDescription,
				},
			},
			want: nil,
			setupMockCalls: func() {
				md.mockSegmentationService.EXPECT().IsMember(ctx, &segment.IsMemberRequest{
					ActorId:    sampleActorId,
					SegmentIds: []string{testSegmentId},
				}).Return(&segment.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						testSegmentId: {
							IsActorMember: false,
						},
					},
				}, nil)
				md.ffAccountingClient.EXPECT().GetAccounts(ctx, &ffAccPb.GetAccountsRequest{
					GetBy: &ffAccPb.GetAccountsRequest_ActorId{ActorId: sampleActorId},
				}).Return(&ffAccPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*ffAccPb.CreditAccount{
						{
							Id:          sampleAccountId,
							CardProgram: unsecuredCardProgram,
							ReferenceId: sampleRefId,
						},
					},
				}, nil)
				md.creditCardDao.EXPECT().GetByActorId(ctx, sampleActorId, nil).Return([]*ffPb.CreditCard{{
					Id:        sampleCardId,
					CardState: ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
				}}, nil)
				md.ffAccountingClient.EXPECT().GetTransactions(ctx, gomock.Any()).Return(&ffAccPb.GetTransactionsResponse{
					Status: rpc.StatusOk(),
					Transactions: []*ffAccPb.CardTransaction{{
						VendorExtTxnId: sampleVendorExternalTxnId,
						Amount:         sampleAmount,
						BeneficiaryInfo: &ffAccPb.BeneficiaryInfo{
							BeneficiaryName: annualMembershipFeesBeneficiaryName,
						},
					}},
				}, nil)
				md.cardRequestDao.EXPECT().GetByOrchestrationId(ctx, sampleVendorExternalTxnId, nil).Return(nil, epifierrors.ErrRecordNotFound)
				md.cardRequestDao.EXPECT().Create(ctx, gomock.Any()).Return(nil, fmt.Errorf("card request create error"))
			},
			wantErr: true,
		},
		{
			name: "fee reversal error",
			args: args{
				ctx: ctx,
				req: &ffPb.TriggerUnsecuredCCRenewalFeeReversalRequest{
					ActorId:     sampleActorId,
					Provenance:  provenance,
					Description: sampleDescription,
				},
			},
			want: nil,
			setupMockCalls: func() {
				md.mockSegmentationService.EXPECT().IsMember(ctx, &segment.IsMemberRequest{
					ActorId:    sampleActorId,
					SegmentIds: []string{testSegmentId},
				}).Return(&segment.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						testSegmentId: {
							IsActorMember: false,
						},
					},
				}, nil)
				md.ffAccountingClient.EXPECT().GetAccounts(ctx, &ffAccPb.GetAccountsRequest{
					GetBy: &ffAccPb.GetAccountsRequest_ActorId{ActorId: sampleActorId},
				}).Return(&ffAccPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*ffAccPb.CreditAccount{
						{
							Id:          sampleAccountId,
							CardProgram: unsecuredCardProgram,
							ReferenceId: sampleRefId,
						},
					},
				}, nil)
				md.creditCardDao.EXPECT().GetByActorId(ctx, sampleActorId, nil).Return([]*ffPb.CreditCard{{
					Id:        sampleCardId,
					CardState: ffEnumsPb.CardState_CARD_STATE_ACTIVATED,
				}}, nil)
				md.ffAccountingClient.EXPECT().GetTransactions(ctx, gomock.Any()).Return(&ffAccPb.GetTransactionsResponse{
					Status: rpc.StatusOk(),
					Transactions: []*ffAccPb.CardTransaction{{
						VendorExtTxnId: sampleVendorExternalTxnId,
						Amount:         sampleAmount,
						BeneficiaryInfo: &ffAccPb.BeneficiaryInfo{
							BeneficiaryName: annualMembershipFeesBeneficiaryName,
						},
					}},
				}, nil)
				md.cardRequestDao.EXPECT().GetByOrchestrationId(ctx, sampleVendorExternalTxnId, nil).Return(nil, epifierrors.ErrRecordNotFound)
				md.cardRequestDao.EXPECT().Create(ctx, gomock.Any()).Return(&ffPb.CardRequest{
					OrchestrationId: sampleVendorExternalTxnId,
					Vendor:          commonvgpb.Vendor_M2P,
					RequestDetails: &ffPb.CardRequestDetails{
						Data: &ffPb.CardRequestDetails_RenewalFeeWaiverRequestDetails{
							RenewalFeeWaiverRequestDetails: &ffPb.RenewalFeeWaiverRequestDetails{
								RenewalFeeWaiverType: ffEnumsPb.FeeWaiverType_FEE_WAIVER_TYPE_FEE_REVERSAL,
							},
						},
						CardProgram: unsecuredCardProgram,
					},
				}, nil)
				md.mockVgClient.EXPECT().FeeReversal(ctx, gomock.Any()).Return(&creditcard.FeeReversalResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.TriggerUnsecuredCCRenewalFeeReversal(tt.args.ctx, tt.args.req)
			if tt.wantErr {
				if assert.Equal(t, tt.wantErr, epifigrpc.RPCError(got, err) != nil, "TriggerUnsecuredCCRenewalFeeReversal(%v, %v)", tt.args.ctx, tt.args.req) {
					return
				}
			}
			if assert.Equalf(t, tt.want, got, "TriggerUnsecuredCCRenewalFeeReversal(%v, %v)", tt.args.ctx, tt.args.req) {
				return
			}
		})
	}
}
