// nolint
package activity

import (
	"context"
	"fmt"
	"regexp"
	"sort"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	actorPb "github.com/epifi/gamma/api/actor"
	ccAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	ffAccEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	ffActivityPb "github.com/epifi/gamma/api/firefly/activity"
	ccFireflyBillingPb "github.com/epifi/gamma/api/firefly/billing"
	ffBeBillingEnums "github.com/epifi/gamma/api/firefly/billing/enums"
	ccEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	ccLmsPb "github.com/epifi/gamma/api/firefly/lms"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	types "github.com/epifi/gamma/api/typesv2"
	ccVgPb "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	ccActivity "github.com/epifi/gamma/firefly/accounting/activity"
	accHelper "github.com/epifi/gamma/firefly/accounting/helper"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

var (
	feeCategories = []ccEnumsPb.TransactionType{
		ccEnumsPb.TransactionType_TRANSACTION_TYPE_FEES,
		ccEnumsPb.TransactionType_SERVICETAX,
		ccEnumsPb.TransactionType_SERVICE_FEES,
		ccEnumsPb.TransactionType_SERVICETAX_REVERSAL,
		ccEnumsPb.TransactionType_SERVICE_CREATION,
		ccEnumsPb.TransactionType_FEES_REVERSAL,
		ccEnumsPb.TransactionType_DOMESTIC_NOTIFICATION_FEES,
		ccEnumsPb.TransactionType_INTERNATIONAL_NOTIFICATION_FEES,
		ccEnumsPb.TransactionType_FEES_ADJUSTMENT,
		ccEnumsPb.TransactionType_REGISTRATION_FEE,
		ccEnumsPb.TransactionType_REGISTRATION_FEE_REVERSAL,
		ccEnumsPb.TransactionType_INACTIVITY_FEE,
		ccEnumsPb.TransactionType_NOBALANCE_FEE,
		ccEnumsPb.TransactionType_LOAN_PRE_CLOSURE_FEES,
		ccEnumsPb.TransactionType_LOAN_PRE_CLOSURE_FEES_TAX,
		ccEnumsPb.TransactionType_LOAN_PROCESSING_FEES,
		ccEnumsPb.TransactionType_LOAN_PROCESSING_FEES_TAX,
		ccEnumsPb.TransactionType_LOAN_PRE_CLOSURE_PROCESSING_FEES,
		ccEnumsPb.TransactionType_LOAN_PRE_CLOSURE_PROCESSING_FEES_TAX,
		ccEnumsPb.TransactionType_LOAN_PROCESSING_FEES_REVERSAL,
		ccEnumsPb.TransactionType_LOAN_PROCESSING_FEES_TAX_REVERSAL,
	}
	maxDate = &date.Date{
		Year:  2999,
		Month: 12,
		Day:   31,
	}
)

func (p *Processor) FetchAndCreateStatement(ctx context.Context, req *ffActivityPb.FetchAndCreateStatementRequest) (*ffActivityPb.FetchAndCreateStatementResponse, error) {

	res := &ffActivityPb.FetchAndCreateStatementResponse{}
	lg := activity.GetLogger(ctx)

	cardRequest, cardRequestStage, err := p.getCardReqAndCreateCardReqStage(ctx, req.GetRequestHeader().GetClientReqId(),
		ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_BILL_GENERATE_STATEMENT_PDF,
		ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_CREATED, ccEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFIED, "")
	if err != nil {
		lg.Error("error in getCardReqAndCreateCardReqStage", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, errors.Wrap(err, "error in getCardReqAndCreateCardReqStage")
	}
	finishActivity, err := p.checkIfStageAlreadyTerminated(cardRequestStage)
	switch {
	case err != nil:
		return nil, err
	case finishActivity:
		return res, nil
	default:
		// continue
	}

	statementResponse, statementError := p.fetchAndGenerateCreditCardStatement(ctx, req, cardRequest.GetActorId())
	if statementError != nil {
		lg.Error("error in generating statement:: ", zap.String("Error: ", statementError.Error()))
		if errors.Is(statementError, epifierrors.ErrPermanent) {
			return nil, statementError
		}
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(statementError, "Error creating statement").Error())
	}

	return statementResponse, nil
}

// nolint: funlen,ineffassign
func (p *Processor) fetchAndGenerateCreditCardStatement(ctx context.Context, payload *ffActivityPb.FetchAndCreateStatementRequest, actorId string) (*ffActivityPb.FetchAndCreateStatementResponse, error) {
	res := &ffActivityPb.FetchAndCreateStatementResponse{}
	var (
		ccStmt                                               = &ccVgPb.FetchMonthlyStatementResponse{}
		ccBalance                                            = &ccVgPb.BalanceDetails{}
		creditAccount                                        = &ccAccountsPb.GetAccountResponse{}
		ccBillResp                                           = &ccFireflyBillingPb.GetCreditCardBillResponse{}
		loanAccount                                          = &ccLmsPb.GetLoanAccountResponse{}
		ccStmtErr, ccBalanceErr, creditAccountErr, ccBillErr error
		loanAccountErr                                       error
		txnsFromAccountingErr                                error
	)

	creditAccount, creditAccountErr = p.rpcHelper.GetCreditAccountByRefId(ctx, payload.GetEntityId())
	switch {
	case errors.Is(creditAccountErr, epifierrors.ErrRecordNotFound):
		return nil, errors.Wrap(epifierrors.ErrPermanent, "No credit account id found for given ref id")
	case creditAccountErr != nil:
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(creditAccountErr, "Error fetching credit card account").Error())
	}
	ccBillResp, ccBillErr = p.rpcHelper.GetCreditCardBill(ctx, actorId, datetime.DateToTimestamp(payload.GetStatementDate(), datetime.IST))
	if ccBillErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(ccBillErr, "Error fetching credit card bill for given actor and statement date").Error())
	}

	rewardCvpIdentifier := p.getStatementRewardCvpIdentifier(creditAccount.GetAccount(), payload.GetStatementDate())
	if rewardCvpIdentifier == ccEnumsPb.RewardCvp_REWARD_CVP_UNSPECIFIED {
		return nil, epifitemporal.NewPermanentError(errors.New("error fetching reward cvp identifier for the statement"))
	}

	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		statementNumber, err := p.getStatementNumberFromMonthlyGeneratedStatements(ctx, payload.GetEntityId(), payload.GetStatementDate())
		if err != nil {
			return epifitemporal.NewTransientError(err)
		}
		ccStmt, ccStmtErr = p.rpcHelper.GetMonthlyStatement(grpCtx, payload.GetEntityId(), payload.GetStatementDate(), statementNumber)
		if ccStmtErr != nil {
			return errors.Wrap(ccStmtErr, "Error while fetching monthly statement")
		}
		return nil
	})
	grp.Go(func() error {
		ccBalance, ccBalanceErr = p.rpcHelper.GetBalance(grpCtx, payload.GetEntityId())
		if ccBalanceErr != nil {
			return errors.Wrap(ccBalanceErr, "Error fetching balance")
		}
		return nil
	})
	grp.Go(func() error {
		loanAccount, loanAccountErr = p.rpcHelper.GetLoanAccount(grpCtx, &ccLmsPb.GetLoanAccountRequest{
			ActorId:     actorId,
			RequestType: ccLmsPb.LoanAccountRequestType_LOAN_ACCOUNT_REQUEST_TYPE_ACTIVE_ACCOUNTS,
		})
		if loanAccountErr != nil {
			return errors.Wrap(loanAccountErr, "error fetching loan account details")
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		logger.Error(ctx, "error in fetching details", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "error fetching details").Error())
	}

	dedupeIds, err := accHelper.GetDedupeIdsFromVgTxnsV2(ccStmt.GetTransactions(), payload.GetEntityId(), ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "error fetching dedupe ids").Error())
	}
	txnsFromAccounting := &ccAccountsPb.GetTxnsWithAdditionalInfosResponse{}
	if len(ccStmt.GetTransactions()) > 0 {
		txnsFromAccounting, txnsFromAccountingErr = p.rpcHelper.GetTransactionsWithAdditionalInfos(ctx, &ccAccountsPb.GetTxnsWithAdditionalInfosRequest{
			GetBy: &ccAccountsPb.GetTxnsWithAdditionalInfosRequest_BatchDedupeIds{
				BatchDedupeIds: &ccAccountsPb.BatchDedupeIds{DedupeIds: dedupeIds},
			},
		})
		switch {
		case errors.Is(txnsFromAccountingErr, epifierrors.ErrRecordNotFound):
			// silently ignore record not found error.
		case txnsFromAccountingErr != nil:
			return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(txnsFromAccountingErr, "Error fetching txns and additional info").Error())
		}
	}

	refIds := make([]string, 0)
	if len(txnsFromAccounting.GetCardTransactionWithAdditionalInfos()) > 0 {
		for _, accountingTxn := range txnsFromAccounting.GetCardTransactionWithAdditionalInfos() {
			refIds = append(refIds, accountingTxn.GetTransaction().GetExternalTxnId())
		}
	}

	rewardCvpDetails, err := p.getRewardCvpDetails(ctx, &RewardCvpDetailsRequest{
		ccBillResp:          ccBillResp,
		creditAccount:       creditAccount.GetAccount(),
		rewardCvpIdentifier: rewardCvpIdentifier,
		txnRefIds:           refIds,
		statementDate:       payload.GetStatementDate(),
	})
	if err != nil {
		return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error fetching reward cvp details"))
	}

	// [older logic] ccFees (including taxes) = total debit - purchase
	ccFees, feesBreakDown, err := getFeeBreakDownDetailsFromVgTxns(ccStmt.GetTransactions())
	if err != nil {
		logger.Error(ctx, "error fetching fees details", zap.Error(err))
		return nil, epifitemporal.NewPermanentError(errors.Wrap(err, "error fetching fees details"))
	}

	res.Summary = &ccFireflyBillingPb.StatementSummary{
		StatementDate:       ccStmt.GetCustomerClosingDetails().GetStatementDate(),
		PaymentDueDate:      ccStmt.GetCustomerClosingDetails().GetCustomerDueDate(),
		AvailableLimit:      ccBalance.GetBalance(),
		TotalAmountDue:      ccStmt.GetCustomerClosingDetails().GetCurrentStatementAmount(),
		MinAmountDue:        ccStmt.GetCustomerClosingDetails().GetMinDueAmount(),
		OpeningBalance:      ccStmt.GetCustomerClosingDetails().GetLastStatementBalance(),
		Spends:              ccStmt.GetCustomerClosingDetails().GetPurchase(),
		Fees:                ccFees,
		RepaymentAndRefunds: ccStmt.GetCustomerClosingDetails().GetTotalCredit(),
	}
	res.FeeBreakDown = feesBreakDown

	emiSummary, spendsConvertedToEmi, err := p.getEmiSummaryAndSpendsFromLoanAccount(ctx, loanAccount, actorId)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Error while creating emi summary").Error())
	}
	res.EmiSummary = emiSummary
	res.Summary.SpendConvertedToEmi = spendsConvertedToEmi
	res.Transactions, err = p.enrichTxnInformation(ctx, txnsFromAccounting.GetCardTransactionWithAdditionalInfos(), ccStmt.GetTransactions(), payload.GetEntityId(), rewardCvpDetails.txnToRewardsMapping, rewardCvpDetails.txnToRewardsMappingV2)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}
	sort.Slice(res.Transactions, func(i, j int) bool {
		return res.Transactions[i].TransactionTimestamp.AsTime().After(res.Transactions[j].TransactionTimestamp.AsTime())
	})
	rewardCoinsEarned := int32(0)
	for _, stmtTxn := range res.Transactions {
		rewardCoinsEarned = stmtTxn.GetRewardCoins() + rewardCoinsEarned
	}
	res.RewardsInfo = rewardCvpDetails.rewardsInfo
	return res, nil
}

func getFeeBreakDownDetailsFromVgTxns(vgTxns []*ccVgPb.Transaction) (*money.Money, *ccFireflyBillingPb.FeeBreakDown, error) {
	var (
		feeBreakDownDetails = &ccFireflyBillingPb.FeeBreakDown{
			Title:                  "Fees, Interest and Charges Breakdown",
			FeeBreakDownComponents: []*ccFireflyBillingPb.FeeBreakDownComponents{},
		}
		totalFees = &money.Money{
			CurrencyCode: "INR",
		}
		sumErr error
	)

	for _, txn := range vgTxns {
		if lo.Contains(feeCategories, txn.GetTransactionType()) {
			totalFees, sumErr = moneyPkg.Sum(totalFees, txn.GetMonetaryDetails().GetAmount())
			if sumErr != nil {
				return nil, nil, errors.Wrap(sumErr, fmt.Sprintf("error while summing the totalFees: %s and %s",
					totalFees.String(), txn.GetMonetaryDetails().GetAmount().String()))
			}

			feeType := txn.GetTransactionDescription()
			if txn.GetTransactionType() == ccEnumsPb.TransactionType_TRANSACTION_TYPE_FEES && strings.ToLower(regexp.MustCompile("[^a-zA-Z]+").ReplaceAllString(txn.GetTransactionDescription(), "")) == ccActivity.AnnualMembershipFeesIdentifier && strings.HasSuffix(txn.GetExternalTransactionId(), "_FEES") {
				// Annual renewal fees transaction: base
				feeType = "Renewal fee"
			} else if txn.GetTransactionType() == ccEnumsPb.TransactionType_SERVICETAX && strings.ToLower(regexp.MustCompile("[^a-zA-Z]+").ReplaceAllString(txn.GetTransactionDescription(), "")) == ccActivity.AnnualMembershipFeesGSTIdentifier && strings.HasSuffix(txn.GetExternalTransactionId(), "_FEES_ST") {
				// Annual renewal fees transaction: service tax
				feeType = "GST on Renewal fee"
			}
			if feeType == "" {
				feeType = txn.GetMerchantInformation().GetMerchantName()
			}
			feeBreakDownComponent := &ccFireflyBillingPb.FeeBreakDownComponents{
				FeeType: feeType,
				Amount:  txn.GetMonetaryDetails().GetAmount(),
			}
			if txn.GetTransactionTransferType() == types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_CREDIT {
				feeBreakDownComponent.FeeAmountType = "MINUS"
			} else {
				feeBreakDownComponent.FeeAmountType = "PLUS"
			}
			feeBreakDownDetails.FeeBreakDownComponents = append(feeBreakDownDetails.FeeBreakDownComponents, feeBreakDownComponent)
		}
	}
	return totalFees, feeBreakDownDetails, nil
}

// enrichTxnInformation adds reward coins details and beneficiary information from rewards and txns stored in accounting
func (p *Processor) enrichTxnInformation(ctx context.Context, cardTxnsWithAdditionalInfo []*ccAccountsPb.CardTransactionWithAdditionalInfo,
	ccVgTxns []*ccVgPb.Transaction, entityId string, txnRewardsMapping map[string]int32, txnRewardsMappingV2 map[string]*rewardsUnitsInfo) ([]*ccFireflyBillingPb.StatementTransaction, error) {

	stmtTxns := make([]*ccFireflyBillingPb.StatementTransaction, 0)
	var (
		emiTxnVendorLoanIds                       []string
		vendorLoanIdToEmiTxnWithAdditionalInfoMap = make(map[string]*ccAccountsPb.CardTransactionWithAdditionalInfo)
		err                                       error
	)
	for _, vgTxn := range ccVgTxns {
		if vgTxn.GetTransactionOrigin() == ccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_LOAN {
			emiTxnVendorLoanIds = append(emiTxnVendorLoanIds, getVendorLoanIdFromExtTxnID(vgTxn.GetExternalTransactionId()))
		}
	}
	if len(emiTxnVendorLoanIds) > 0 {
		vendorLoanIdToEmiTxnWithAdditionalInfoMap, err = p.getParentEmiTransactions(ctx, emiTxnVendorLoanIds)
		if err != nil {
			// logging on error to confirm from logs that utility is failing
			logger.Error(ctx, "error in fetching vendorLoanIdToTxnAdditionalInfoMap for enhancing emi txn", zap.Error(err))
			return nil, errors.Wrap(err, "Error while fetching parent txns with additional info for emi txns")
		}
	}
	for _, vgTxn := range ccVgTxns {
		stmtTxn := &ccFireflyBillingPb.StatementTransaction{
			Amount:                  vgTxn.GetMonetaryDetails().GetAmount(),
			TransactionTimestamp:    vgTxn.GetTransactionTime(),
			MerchantName:            vgTxn.GetMerchantInformation().GetMerchantName(),
			Location:                vgTxn.GetMerchantInformation().GetMerchantLocation(),
			TransactionOrigin:       vgTxn.GetTransactionOrigin(),
			TransactionTransferType: vgTxn.GetTransactionTransferType(),
		}
		if vgTxn.GetTransactionOrigin() == ccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_LOAN {
			emiTxnWithAdditionalInfo, ok := vendorLoanIdToEmiTxnWithAdditionalInfoMap[getVendorLoanIdFromExtTxnID(vgTxn.GetExternalTransactionId())]
			if ok {
				stmtTxn.MerchantName = accHelper.GetBeneficiaryName(emiTxnWithAdditionalInfo) + getEmiMerchantNameSuffix(vgTxn)
				stmtTxns = append(stmtTxns, stmtTxn)
				continue
			}
			logger.Error(ctx, "Error no emi txn with additional info exists for vendor loan id", zap.String(logger.TXN_ID, vgTxn.GetExternalTransactionId()))
			return nil, errors.Wrap(err, "Error no emi txn with additional info exists for vendor loan id")
		}
		for _, cardTxnWithAdditionalInfo := range cardTxnsWithAdditionalInfo {
			// if ext txn id is not "", this means we have a txn stored on our end that we can map to vg txn.
			// hence we will use the beneficiary name from the txn instead of directly using merchant name from vg txn.
			extTxnId := accHelper.GetExternalTxnIdFromVgAndAccountingTxns(vgTxn, cardTxnWithAdditionalInfo.GetTransaction(),
				entityId, ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS)
			if extTxnId != "" {
				stmtTxn.MerchantName = accHelper.GetBeneficiaryName(cardTxnWithAdditionalInfo)
				rewardUnits, ok := txnRewardsMappingV2[extTxnId]
				if ok {
					stmtTxn.RewardCoins = rewardUnits.fiCoinUnits
					stmtTxn.RewardPoints = rewardUnits.fiPointUnits
				}
			}
		}
		stmtTxns = append(stmtTxns, stmtTxn)
	}
	return stmtTxns, nil
}

func (p *Processor) getProjectedTopMerchantRewardsForBillingCycle(merchantAggregates []*rewardsPb.GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate, netSpendsAggregate *money.Money) []*ccFireflyBillingPb.MerchantRewardsInfo {
	topMerchantsRewardInfo := make([]*ccFireflyBillingPb.MerchantRewardsInfo, 0)
	// In case the first merchant itself doesn't have reward coins, then we can just ignore top merchant rewards
	if len(merchantAggregates) == 0 || merchantAggregates[0].GetNetFiCoins() == 0 {
		return topMerchantsRewardInfo
	}
	for idx, merchantWiseRewards := range merchantAggregates {
		if idx >= ffPkg.CountOfMerchantsToBeRewarded5x {
			break
		}
		merchantRewards := &ccFireflyBillingPb.MerchantRewardsInfo{
			TotalRewardCoins: int32(merchantWiseRewards.GetNetFiCoins()),
			MerchantName:     merchantWiseRewards.GetMerchantName(),
		}
		if moneyPkg.Compare(netSpendsAggregate, ffPkg.SpendLimitToUnlock5xRewards) >= 0 {
			merchantRewards.MerchantRewardType = ffBeBillingEnums.MerchantRewardType_MERCHANT_REWARD_TYPE_FIVE_X
			merchantRewards.TotalRewardCoins *= 3
		} else {
			merchantRewards.MerchantRewardType = ffBeBillingEnums.MerchantRewardType_MERCHANT_REWARD_TYPE_TWO_X
		}
		topMerchantsRewardInfo = append(topMerchantsRewardInfo, merchantRewards)
	}
	return topMerchantsRewardInfo
}

func (p *Processor) getEmiSummaryAndSpendsFromLoanAccount(ctx context.Context, loanAccounts *ccLmsPb.GetLoanAccountResponse, actorId string) (*ccFireflyBillingPb.EmiSummary, *money.Money, error) {
	res := &ccFireflyBillingPb.EmiSummary{}
	res.NumberOfActiveEmi = int64(len(loanAccounts.GetLoanAccounts()))
	spendsConvertedToEmi := moneyPkg.ZeroINR().GetPb()
	for _, loanAccount := range loanAccounts.GetLoanAccounts() {
		merchantName, err := p.rpcHelper.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: loanAccount.GetOtherActorId()})
		if err != nil {
			return nil, nil, errors.Wrap(err, "Error while fetching merchant name")
		}
		res.EmiDetails = append(res.EmiDetails, &ccFireflyBillingPb.EmiDetail{
			MerchantName:      merchantName.GetName().GetFirstName(),
			InstallmentNumber: loanAccount.GetTenureInMonths() - loanAccount.GetNumberOfDueRepayments() + 1,
			TotalInstallments: loanAccount.GetTenureInMonths(),
			Amount:            loanAccount.GetEmiAmount(),
		})
		res.DueAmount, _ = moneyPkg.Sum(res.DueAmount, loanAccount.GetEmiAmount())
		if loanAccount.GetNumberOfDueRepayments() == loanAccount.GetTenureInMonths() {
			loanAccountAdditionalInfo, rpcErr := p.rpcHelper.GetLoanAccountAdditionalInfo(ctx, &ccLmsPb.GetLoanAccountDetailsByIdRequest{
				LoanAccountId: loanAccount.GetLoanAccountId(),
				ActorId:       actorId,
			})
			if te := epifigrpc.RPCError(loanAccountAdditionalInfo, rpcErr); te != nil {
				return nil, nil, errors.Wrap(te, "Error while fetching loan account additional info from Lms client")
			}
			spendsConvertedToEmi, err = moneyPkg.Sum(spendsConvertedToEmi, loanAccountAdditionalInfo.GetLoanAccount().GetAmountInfo().GetLoanAmount())
			if err != nil {
				return nil, nil, errors.Wrap(err, "Error while adding money")
			}
		}
	}
	return res, spendsConvertedToEmi, nil
}

func (p *Processor) getStatementRewardCvpIdentifier(creditAccount *ccAccountsPb.CreditAccount, currentStatementDate *date.Date) ccEnumsPb.RewardCvp {
	accountCreationDate := datetime.TimestampToDateInLoc(creditAccount.GetCreatedAt(), datetime.IST)

	for _, rewardCvpConfig := range p.conf.RewardCvpVersionConfigs {
		cardProgramType := creditAccount.GetCardProgram().GetCardProgramType().String()
		if !lo.Contains(rewardCvpConfig.SupportedCardProgramTypes, cardProgramType) {
			continue
		}
		for _, versionConfig := range rewardCvpConfig.CvpVersions {
			versionEndDate := versionConfig.EndDate
			versionLastStatementDate := versionConfig.LastStatementDate
			if versionEndDate == nil {
				versionEndDate = maxDate
			}
			if versionLastStatementDate == nil {
				versionLastStatementDate = maxDate
			}

			// Account creation date should be on or before version end date
			if datetime.DateEquals(accountCreationDate, versionEndDate) || datetime.IsDateAfter(versionEndDate, accountCreationDate) {
				// current statement should be on or before version last statement end date
				if datetime.DateEquals(currentStatementDate, versionLastStatementDate) || datetime.IsDateAfter(versionLastStatementDate, currentStatementDate) {
					return ccEnumsPb.RewardCvp(ccEnumsPb.RewardCvp_value[versionConfig.Name])
				}
			}
		}
	}

	return ccEnumsPb.RewardCvp_REWARD_CVP_UNSPECIFIED
}

func getVendorLoanIdFromExtTxnID(extTxnID string) string {
	constructors := strings.Split(extTxnID, "_")
	return constructors[0]
}

func getEmiMerchantNameSuffix(transaction *ccVgPb.Transaction) string {
	var (
		res = " EMI "
	)

	categoryConstruct := strings.Split(transaction.GetTransactionType().String(), "_")
	if transaction.GetTransactionType() != ccEnumsPb.TransactionType_TRANSACTION_TYPE_UNSPECIFIED {
		res += strings.ToLower(strings.Join(categoryConstruct[1:], " "))
	}

	// Define a regular expression pattern to match fractions
	pattern := `\(\s*(\d+)\s*/\s*(\d+)\s*\)`
	// Compile the regular expression
	re := regexp.MustCompile(pattern)
	// Find the submatch (the fraction) in the input string
	matches := re.FindStringSubmatch(transaction.GetTransactionDescription())
	// Check if there is a match
	if len(matches) >= 3 {
		// matches[0] contains the entire matched string, matches[1] and matches[2] contain the numerator and denominator respectively
		numerator := matches[1]
		denominator := matches[2]
		// attach the result
		res += fmt.Sprintf(" ( %s / %s )", numerator, denominator)
	}
	return res
}

func (p *Processor) getParentEmiTransactions(ctx context.Context, vendorLoanIds []string) (map[string]*ccAccountsPb.CardTransactionWithAdditionalInfo, error) {
	var (
		vendorLoanIdToTxnWithAdditionalInfoMap                     = make(map[string]*ccAccountsPb.CardTransactionWithAdditionalInfo)
		parentEmiTransactionsIdToTransactionsWithAdditionalInfoMap = make(map[string]*ccAccountsPb.CardTransactionWithAdditionalInfo)
		parentEmiTxnIds                                            = make([]string, 0)
	)

	txnLoanOffers, err := p.rpcHelper.GetTxnLoanOffers(ctx, vendorLoanIds)
	switch {
	case err != nil:
		return nil, err
	default:
		for _, txnLoanOffer := range txnLoanOffers {
			parentEmiTxnIds = append(parentEmiTxnIds, txnLoanOffer.GetTransactionId())
		}
	}

	txnsWithAdditionalInfos, err := p.rpcHelper.GetTxnsWithAdditionalInfo(ctx, parentEmiTxnIds)
	switch {
	case err != nil:
		return nil, err
	default:
		for _, txnWithAdditionalInfo := range txnsWithAdditionalInfos {
			parentEmiTransactionsIdToTransactionsWithAdditionalInfoMap[txnWithAdditionalInfo.GetAdditionalInfo().GetTransactionId()] = txnWithAdditionalInfo
		}
	}

	for i := 0; i < len(vendorLoanIds); i++ {
		vendorLoanIdToTxnWithAdditionalInfoMap[vendorLoanIds[i]] = parentEmiTransactionsIdToTransactionsWithAdditionalInfoMap[parentEmiTxnIds[i]]
	}
	return vendorLoanIdToTxnWithAdditionalInfoMap, nil
}
