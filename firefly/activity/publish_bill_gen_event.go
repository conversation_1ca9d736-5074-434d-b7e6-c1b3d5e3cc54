package activity

import (
	"context"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/gamma/api/firefly/accounting"
	ffActivityPb "github.com/epifi/gamma/api/firefly/activity"
	"github.com/epifi/gamma/api/firefly/billing/events"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	fireflyEvents "github.com/epifi/gamma/firefly/events"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
)

func (p *Processor) PublishBillGenerationEvent(ctx context.Context,
	req *ffActivityPb.PublishBillGenerationEventRequest) (*ffActivityPb.PublishBillGenerationEventResponse, error) {
	res := &ffActivityPb.PublishBillGenerationEventResponse{}
	lg := activity.GetLogger(ctx)

	_, cardRequestStage, err := p.getCardReqAndCreateCardReqStage(ctx, req.GetRequestHeader().GetClientReqId(),
		ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_PUBLISH_BILL_GENERATION_EVENT,
		ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_CREATED,
		ffEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFIED, "")
	if err != nil {
		lg.Error("error in getCardReqAndCreateCardReqStage", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, errors.Wrap(err, "error in getCardReqAndCreateCardReqStage")
	}

	finishActivity, err := p.checkIfStageAlreadyTerminated(cardRequestStage)
	switch {
	case err != nil:
		return nil, err
	case finishActivity:
		return res, nil
	default:
		// continue
	}

	creditCardBill, billWindow, err := p.rpcHelper.GetBillAndBillWindowById(ctx, req.GetBillId())
	if err != nil {
		lg.Error("error in fetching bill and bill window by id", zap.String(logger.BILL_ID,
			req.GetBillId()), zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}
	_, err = p.ccBillGenerationEventPublisher.Publish(ctx, &events.CreditCardBillGenerationEvent{
		ActorId:    creditCardBill.GetActorId(),
		BillId:     creditCardBill.GetId(),
		AccountId:  creditCardBill.GetAccountId(),
		BillWindow: billWindow,
		CreatedAt:  creditCardBill.GetCreatedAt(),
	})
	if err != nil {
		lg.Error("error in publishing bill generation event", zap.String(logger.BILL_ID,
			req.GetBillId()), zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	// Configure rewards generation sleep duration with special handling for migration day
	//
	// Default Behavior:
	// Uses the standard configured pause duration to allow reward projections to be processed
	// before statement generation. This ensures all reward calculations are complete and
	// projections are available for inclusion in the statement.
	res.RewardsGenerationSleepDuration = int64(p.conf.StatementPdfConfig.StatementRewardsGenerationPauseDuration)

	// Special Case: Fi-Coins to Fi-Points Migration Day
	// On the exact day of the Fi-Coins to Fi-Points migration, we extend the sleep duration
	// to 12 hours because we're expecting the Fi-Coins to Fi-Points migration to finish by then.
	//
	// This ensures:
	// - The migration process completes before statement generation begins
	// - All Fi-Coins have been converted to Fi-Points
	// - Reward projections are stable and consistent
	// - Statements accurately reflect the post-migration reward state
	if datetime.StartOfDay(creditCardBill.GetStatementDate().AsTime()).Equal(datetime.StartOfDay(accrualPkg.GetFiCoinsToFiPointsMigrationTime())) ||
		accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		res.RewardsGenerationSleepDuration = int64(p.conf.StatementPdfConfig.StatementRewardsGenerationPauseDurationDuringFcFpMigration)
	}

	cardRequestStage.Status = ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SUCCESS
	updateErr := p.cardRequestStageDao.Update(ctx, cardRequestStage,
		[]ffEnumsPb.CardRequestStageFieldMask{ffEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STATUS})
	if updateErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, updateErr.Error())
	}

	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		getAccResp, getAccErr := p.ffAccClient.GetAccounts(ctx, &accounting.GetAccountsRequest{GetBy: &accounting.GetAccountsRequest_ActorId{ActorId: creditCardBill.GetActorId()}})
		if ve := epifigrpc.RPCError(getAccResp, getAccErr); ve == nil {
			p.eventBroker.AddToBatch(ctx, fireflyEvents.NewBillGenerated(creditCardBill.GetActorId(), getAccResp.GetAccounts()[0].GetCardProgram(), creditCardBill.GetSoftDueDate(), creditCardBill.GetCreatedAt()))
		}
	})
	return res, nil
}
