// nolint
package activity

import (
	"errors"
	"strings"

	ccFireflyBillingPb "github.com/epifi/gamma/api/firefly/billing"
	ccEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	ccFireflyBillingModel "github.com/epifi/gamma/firefly/billing/model"
	fireflyWorkerConf "github.com/epifi/gamma/firefly/config/worker"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
)

type StatementCvpDetailsResponse struct {
	rewardSummary             *ccFireflyBillingModel.RewardsSummary
	extraRewardsInfo          []*ccFireflyBillingModel.ExtraRewardsInfo
	tipsAndInformationSection *ccFireflyBillingModel.TipsAndInformationSection
	ContactUsDetails          *ccFireflyBillingModel.ContactUsDetails
	cardProgramLogo           string
}

type StatementCvpDetailsRequest struct {
	rewardCvpIdentifier ccEnumsPb.RewardCvp
	rewardsInfo         *ccFireflyBillingPb.RewardsInfo
	billingWindow       *ccFireflyBillingPb.BillWindow
}

func (p *Processor) getSecuredStatementCvpDetails(request *StatementCvpDetailsRequest) (*StatementCvpDetailsResponse, error) {

	if request == nil || request.rewardCvpIdentifier != ccEnumsPb.RewardCvp_REWARD_CVP_SECURED_V1 {
		return nil, errors.New("invalid request to fetch secured reward v1 cvp")
	}

	cvpConfigDetails := p.getStatementCvpConfigDetails(request.rewardCvpIdentifier)
	if cvpConfigDetails == nil {
		return nil, errors.New("no cvp config details found for reward identifier")
	}

	rewardSummary := &ccFireflyBillingModel.RewardsSummary{
		MerchantWiseRewardInfo: []*ccFireflyBillingModel.MerchantWiseRewardInfo{},
		BottomText:             "We don't endorse these brands & vice versa. These brand logos are for representation & information purposes only.",
		RewardCoinsSummary: &ccFireflyBillingModel.RewardCoinsSummary{
			Title:                  accrualPkg.ReturnApplicableValue("FI-COINS EARNED", "FI-POINTS EARNED", request.billingWindow.GetToTimestamp(), true).(string),
			TotalRewardCoinsEarned: request.rewardsInfo.GetSecuredCardsRewardInfo().GetProjectedTotalRewardCoins(),
		},
	}

	extraRewardsInfo := []*ccFireflyBillingModel.ExtraRewardsInfo{
		{
			Text:              "Weekday Rewards",
			RewardCoinsEarned: request.rewardsInfo.GetSecuredCardsRewardInfo().GetProjectedWeekdayRewardCoins(),
			RewardTypeLogo:    weekdayRewardsIconUrl,
		}, {
			Text:              "Weekend Rewards",
			RewardCoinsEarned: request.rewardsInfo.GetSecuredCardsRewardInfo().GetProjectedWeekendRewardCoins(),
			RewardTypeLogo:    weekendRewardsIconUrl,
		},
	}

	tipsAndInfoSection := p.getTipsAndInformationSection(cvpConfigDetails.MitcLink)
	customerCareDetails := make([]*ccFireflyBillingModel.CustomerCareDetails, 0)
	for _, customerCareDetail := range cvpConfigDetails.CustomerCareDetails {
		customerCareDetails = append(customerCareDetails, &ccFireflyBillingModel.CustomerCareDetails{
			Title: customerCareDetail.Title,
		})
	}
	customerCareDetails = append(customerCareDetails, p.getCommonCustomerCareDetails()...)

	return &StatementCvpDetailsResponse{
		rewardSummary:             rewardSummary,
		extraRewardsInfo:          extraRewardsInfo,
		tipsAndInformationSection: tipsAndInfoSection,
		ContactUsDetails: &ccFireflyBillingModel.ContactUsDetails{
			Title:               "IMPORTANT INFORMATION",
			CustomerCareDetails: customerCareDetails,
			TnCSection:          p.getTncSectionDetails(cvpConfigDetails.MitcLink, cvpConfigDetails.KfsLink, cvpConfigDetails.TncLink),
			IssuerDetails:       p.getIssuerDetails(),
		},
		cardProgramLogo: cvpConfigDetails.CardProgramLogo,
	}, nil
}

func (p *Processor) getUnSecuredStatementCvpDetailsV1(request *StatementCvpDetailsRequest) (*StatementCvpDetailsResponse, error) {
	if request == nil || request.rewardCvpIdentifier != ccEnumsPb.RewardCvp_REWARD_CVP_UNSECURED_V1 {
		return nil, errors.New("invalid request to fetch unsecured reward v1 cvp")
	}
	cvpConfigDetails := p.getStatementCvpConfigDetails(request.rewardCvpIdentifier)
	if cvpConfigDetails == nil {
		return nil, errors.New("no cvp config details found for reward identifier")
	}

	rewardSummary := &ccFireflyBillingModel.RewardsSummary{
		MerchantWiseRewardInfo: []*ccFireflyBillingModel.MerchantWiseRewardInfo{},
		BottomText:             "We don't endorse these brands & vice versa. These brand logos are for representation & information purposes only.",
		RewardCoinsSummary: &ccFireflyBillingModel.RewardCoinsSummary{
			Title:                  accrualPkg.ReturnApplicableValue("FI-COINS EARNED", "FI-POINTS EARNED", request.billingWindow.GetToTimestamp(), true).(string),
			TotalRewardCoinsEarned: request.rewardsInfo.GetExtraRewardsConstructInfo().GetTotalRewardCoins(),
		},
	}

	if len(request.rewardsInfo.GetTopMerchantRewards()) != 0 {
		rewardSummary.Title = rewardTypeToStringMap[request.rewardsInfo.GetTopMerchantRewards()[0].GetMerchantRewardType()]
		rewardSummary.MerchantWiseRewardInfo = p.getStatementMerchantWiseRewardsInfo(request.rewardsInfo.GetTopMerchantRewards())
	}

	extraRewardsInfo := []*ccFireflyBillingModel.ExtraRewardsInfo{
		{
			Text:              "Extra Rewards",
			RewardCoinsEarned: request.rewardsInfo.GetExtraRewardsConstructInfo().GetProjected_2XRewardsCoins(),
			RewardTypeLogo:    extraRewards2xIconUrl,
		}, {
			Text:              "Extra Rewards",
			RewardCoinsEarned: request.rewardsInfo.GetExtraRewardsConstructInfo().GetProjected_5XRewardsCoins(),
			RewardTypeLogo:    extraRewards5xIconUrl,
		},
	}

	tipsAndInfoSection := p.getTipsAndInformationSection(cvpConfigDetails.MitcLink)
	customerCareDetails := make([]*ccFireflyBillingModel.CustomerCareDetails, 0)
	for _, customerCareDetail := range cvpConfigDetails.CustomerCareDetails {
		customerCareDetails = append(customerCareDetails, &ccFireflyBillingModel.CustomerCareDetails{
			Title: customerCareDetail.Title,
		})
	}
	customerCareDetails = append(customerCareDetails, p.getCommonCustomerCareDetails()...)

	return &StatementCvpDetailsResponse{
		rewardSummary:             rewardSummary,
		extraRewardsInfo:          extraRewardsInfo,
		tipsAndInformationSection: tipsAndInfoSection,
		ContactUsDetails: &ccFireflyBillingModel.ContactUsDetails{
			Title:               "IMPORTANT INFORMATION",
			CustomerCareDetails: customerCareDetails,
			TnCSection:          p.getTncSectionDetails(cvpConfigDetails.MitcLink, cvpConfigDetails.KfsLink, cvpConfigDetails.TncLink),
			IssuerDetails:       p.getIssuerDetails(),
		},
		cardProgramLogo: cvpConfigDetails.CardProgramLogo,
	}, nil
}

func (p *Processor) getUnSecuredStatementCvpDetailsV2(request *StatementCvpDetailsRequest) (*StatementCvpDetailsResponse, error) {
	if request == nil || request.rewardCvpIdentifier != ccEnumsPb.RewardCvp_REWARD_CVP_UNSECURED_V2 {
		return nil, errors.New("invalid request to fetch unsecured reward v2 cvp")
	}
	cvpConfigDetails := p.getStatementCvpConfigDetails(request.rewardCvpIdentifier)
	if cvpConfigDetails == nil {
		return nil, errors.New("no cvp config details found for reward identifier")
	}

	rewardSummary := &ccFireflyBillingModel.RewardsSummary{
		MerchantWiseRewardInfo: []*ccFireflyBillingModel.MerchantWiseRewardInfo{},
		BottomText:             "We don't endorse these brands & vice versa. These brand logos are for representation & information purposes only.",
		RewardCoinsSummary: &ccFireflyBillingModel.RewardCoinsSummary{
			Title:                  accrualPkg.ReturnApplicableValue("FI-COINS EARNED", "FI-POINTS EARNED", request.billingWindow.GetToTimestamp(), true).(string),
			TotalRewardCoinsEarned: request.rewardsInfo.GetExtraRewardsConstructInfo().GetTotalRewardCoins(),
		},
	}

	extraRewardsInfo := []*ccFireflyBillingModel.ExtraRewardsInfo{
		{
			Text:              "Rewards on all spends",
			RewardCoinsEarned: request.rewardsInfo.GetExtraRewardsConstructInfo().GetTotal_1XRewardsCoins(),
			RewardTypeLogo:    extraRewards1xIconUrl,
		}, {
			Text:              "Rewards on the Fi Collection",
			RewardCoinsEarned: request.rewardsInfo.GetExtraRewardsConstructInfo().GetProjected_5XRewardsCoins(),
			RewardTypeLogo:    extraRewardsFiCollectionIconUrl,
		},
	}

	tipsAndInfoSection := p.getTipsAndInformationSection(cvpConfigDetails.MitcLink)
	customerCareDetails := make([]*ccFireflyBillingModel.CustomerCareDetails, 0)
	for _, customerCareDetail := range cvpConfigDetails.CustomerCareDetails {
		customerCareDetails = append(customerCareDetails, &ccFireflyBillingModel.CustomerCareDetails{
			Title: customerCareDetail.Title,
		})
	}
	customerCareDetails = append(customerCareDetails, p.getCommonCustomerCareDetails()...)

	return &StatementCvpDetailsResponse{
		rewardSummary:             rewardSummary,
		extraRewardsInfo:          extraRewardsInfo,
		tipsAndInformationSection: tipsAndInfoSection,
		ContactUsDetails: &ccFireflyBillingModel.ContactUsDetails{
			Title:               "IMPORTANT INFORMATION",
			CustomerCareDetails: customerCareDetails,
			TnCSection:          p.getTncSectionDetails(cvpConfigDetails.MitcLink, cvpConfigDetails.KfsLink, cvpConfigDetails.TncLink),
			IssuerDetails:       p.getIssuerDetails(),
		},
		cardProgramLogo: cvpConfigDetails.CardProgramLogo,
	}, nil
}

func (p *Processor) getMassUnSecuredStatementCvpDetailsV1(request *StatementCvpDetailsRequest) (*StatementCvpDetailsResponse, error) {
	if request == nil || request.rewardCvpIdentifier != ccEnumsPb.RewardCvp_REWARD_CVP_MASS_UNSECURED_V1 {
		return nil, errors.New("invalid request to fetch unsecured reward v2 cvp")
	}
	cvpConfigDetails := p.getStatementCvpConfigDetails(request.rewardCvpIdentifier)
	if cvpConfigDetails == nil {
		return nil, errors.New("no cvp config details found for reward identifier")
	}

	rewardSummary := &ccFireflyBillingModel.RewardsSummary{
		MerchantWiseRewardInfo: []*ccFireflyBillingModel.MerchantWiseRewardInfo{},
		BottomText:             "We don't endorse these brands & vice versa. These brand logos are for representation & information purposes only.",
		RewardCoinsSummary: &ccFireflyBillingModel.RewardCoinsSummary{
			Title:                  accrualPkg.ReturnApplicableValue("FI-COINS EARNED", "FI-POINTS EARNED", request.billingWindow.GetToTimestamp(), true).(string),
			TotalRewardCoinsEarned: request.rewardsInfo.GetRewardsConstructInfo().GetProjectedTotalRewardCoins(),
		},
	}

	extraRewardsInfo := []*ccFireflyBillingModel.ExtraRewardsInfo{
		{
			Text:              "Rewards on weekend spends",
			RewardCoinsEarned: request.rewardsInfo.GetRewardsConstructInfo().GetProjectedAcceleratedRewardCoins(),
			RewardTypeLogo:    massUnsecuredAcceleratedRewardsIconUrl,
		}, {
			Text:              "Rewards on all spends",
			RewardCoinsEarned: request.rewardsInfo.GetRewardsConstructInfo().GetProjectedBaseRewardCoins(),
			RewardTypeLogo:    massUnsecuredBaseRewardsIconUrl,
		},
	}

	tipsAndInfoSection := p.getTipsAndInformationSection(cvpConfigDetails.MitcLink)
	customerCareDetails := make([]*ccFireflyBillingModel.CustomerCareDetails, 0)
	for _, customerCareDetail := range cvpConfigDetails.CustomerCareDetails {
		customerCareDetails = append(customerCareDetails, &ccFireflyBillingModel.CustomerCareDetails{
			Title: customerCareDetail.Title,
		})
	}
	customerCareDetails = append(customerCareDetails, p.getCommonCustomerCareDetails()...)

	return &StatementCvpDetailsResponse{
		rewardSummary:             rewardSummary,
		extraRewardsInfo:          extraRewardsInfo,
		tipsAndInformationSection: tipsAndInfoSection,
		ContactUsDetails: &ccFireflyBillingModel.ContactUsDetails{
			Title:               "IMPORTANT INFORMATION",
			CustomerCareDetails: customerCareDetails,
			TnCSection:          p.getTncSectionDetails(cvpConfigDetails.MitcLink, cvpConfigDetails.KfsLink, cvpConfigDetails.TncLink),
			IssuerDetails:       p.getIssuerDetails(),
		},
		cardProgramLogo: cvpConfigDetails.CardProgramLogo,
	}, nil
}

func (p *Processor) getStatementCvpDetails(request *StatementCvpDetailsRequest) (*StatementCvpDetailsResponse, error) {
	if request == nil {
		return nil, errors.New("invalid request for cvp details")
	}
	switch request.rewardCvpIdentifier {
	case ccEnumsPb.RewardCvp_REWARD_CVP_SECURED_V1:
		return p.getSecuredStatementCvpDetails(request)
	case ccEnumsPb.RewardCvp_REWARD_CVP_UNSECURED_V1:
		return p.getUnSecuredStatementCvpDetailsV1(request)
	case ccEnumsPb.RewardCvp_REWARD_CVP_UNSECURED_V2:
		return p.getUnSecuredStatementCvpDetailsV2(request)
	case ccEnumsPb.RewardCvp_REWARD_CVP_MASS_UNSECURED_V1:
		return p.getMassUnSecuredStatementCvpDetailsV1(request)
	}
	return nil, errors.New("no details found for cvp identifier")
}

func (p *Processor) getStatementCvpConfigDetails(cvpIdentifier ccEnumsPb.RewardCvp) *fireflyWorkerConf.CvpVersionDetails {
	for _, statementCvpDetails := range p.conf.StatementDetailsConfigs {
		for _, statementCvpVersionDetails := range statementCvpDetails.CvpVersionDetails {
			if strings.Compare(statementCvpVersionDetails.Name, cvpIdentifier.String()) == 0 {
				return statementCvpVersionDetails
			}
		}
	}
	return nil
}

func (p *Processor) getCommonCustomerCareDetails() []*ccFireflyBillingModel.CustomerCareDetails {
	return []*ccFireflyBillingModel.CustomerCareDetails{
		{
			Title: "For further assistance on this or any other matters related to your credit card, reach out to Fi's Customer Care:",
			TextWithLinkAndValues: []*ccFireflyBillingModel.TextWithLinkAndValue{
				{
					Title: "Email",
					Value: "<EMAIL>",
					Link:  "<EMAIL>",
					Type:  "mailto",
				},
				{
					Title: "Phone",
					Value: "***********",
					Link:  "***********",
					Type:  "tel",
				},
			}},
		{
			Title: "For credit card grievances that need to be addressed by Federal Bank’s Principal Nodal officer for Grievance Redressal: ",
			TextWithLinkAndValues: []*ccFireflyBillingModel.TextWithLinkAndValue{
				{
					Title: "Name: ",
					Value: "Minimole Liz Thomas, Head – Service Quality Department",
				},
				{
					Title: "Phone: ",
					Value: "0484-2626366",
				},
				{
					Title: "Email: ",
					Value: "<EMAIL>",
					Type:  "mailto",
					Link:  "<EMAIL>",
				},
				{
					Title: "Postal address: CEO’s Secretariat, The Federal Bank Ltd., Federal Towers, Bank Junction, Aluva, Kerala, 6831010.",
				},
			},
		},
	}
}

func (p *Processor) getTncSectionDetails(mitcLink, kfsLink, tncLink string) *ccFireflyBillingModel.TnCSection {
	return &ccFireflyBillingModel.TnCSection{
		Text: "To know more, read Federal Bank’s Most Important Terms & Conditions, Key Fact Statement and Terms & Conditions.",
		TextWithLinkAndValues: []*ccFireflyBillingModel.TextWithLinkAndValue{
			{
				Title: "Most Important Terms & Conditions",
				Value: "Most Important Terms & Conditions",
				Link:  mitcLink,
			},
			{
				Title: "Key Fact Statement",
				Value: "Key Fact Statement",
				Link:  kfsLink,
			},
			{
				Title: "Terms & Conditions.",
				Value: "Terms & Conditions.",
				Link:  tncLink,
			},
		},
	}
}

func (p *Processor) getIssuerDetails() *ccFireflyBillingModel.IssuerDetails {
	return &ccFireflyBillingModel.IssuerDetails{
		Title: "ISSUER DETAILS",
		LeftTextWithLinkAndValues: []*ccFireflyBillingModel.LeftTextWithLinkAndValue{
			{Title: "The Federal Bank Limited"},
			{Title: "GSTN:", Value: "32AABCT0020H3Z3"},
			{Title: "Parackal Towers, Thottakkatukara, Aluva,\n\t\tErnakulam, Kerala - 683101"},
		},
		RightTextWithLinkAndValues: []*ccFireflyBillingModel.RightTextWithLinkAndValue{
			{Title: "Place Of Service:", Value: "Kerala"},
			{Title: "State Code:", Value: "32"},
			{Title: "HSN", Value: "- 997114 - Financial and Related Services"},
		},
	}
}

func (p *Processor) getTipsAndInformationSection(mitcUrl string) *ccFireflyBillingModel.TipsAndInformationSection {
	return &ccFireflyBillingModel.TipsAndInformationSection{
		IconUrl: bulbIcon,
		Title:   "Pro tip",
		InformationText: []ccFireflyBillingModel.InformationText{
			{
				Title:                 "Want to improve your credit score? Pay your bills before the due date.To reduce late payment charges, pay more than the minimum due.",
				TextWithLinkAndValues: []*ccFireflyBillingModel.TextWithLinkAndValue{},
			},
			{
				Title: "Paying only the minimum monthly due would stretch repayment over many months/years. And each time, your interest payments get compounded & pile on your outstanding balance. See a sample calculation for interest applied when only minimum amount due is paid, in the ",
				TextWithLinkAndValues: []*ccFireflyBillingModel.TextWithLinkAndValue{{
					Title: "MITC.",
					Value: "MITC.",
					Link:  mitcUrl,
				}},
			},
		},
	}
}
