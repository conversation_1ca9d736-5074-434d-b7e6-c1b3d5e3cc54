// nolint
package activity

import (
	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	commsPb "github.com/epifi/gamma/api/comms"
	ffActivityPb "github.com/epifi/gamma/api/firefly/activity"
	ccEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	ccFireflyBillingModel "github.com/epifi/gamma/firefly/billing/model"
	"github.com/epifi/gamma/firefly/helper"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

const (
	commonHeaderImage = "https://epifi-icons.pointz.in/credit_card_images/cc_statement_header_image_common_4x.png"
)

func (p *Processor) GetEmailStatementNotification(ctx context.Context, req *ffActivityPb.GetEmailStatementNotificationRequest) (*ffActivityPb.GetEmailStatementNotificationResponse, error) {
	res := &ffActivityPb.GetEmailStatementNotificationResponse{}
	lg := activity.GetLogger(ctx)

	_, cardRequestStage, err := p.getCardReqAndCreateCardReqStage(ctx, req.GetRequestHeader().GetClientReqId(),
		ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_EMAIL_STATEMENT,
		ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_CREATED, ccEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFIED, "")
	if err != nil {
		lg.Error("error in getCardReqAndCreateCardReqStage", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, errors.Wrap(err, "error in getCardReqAndCreateCardReqStage")
	}

	finishActivity, err := p.checkIfStageAlreadyTerminated(cardRequestStage)
	switch {
	case err != nil:
		return nil, err
	case finishActivity:
		return res, nil
	default:
		// continue
	}

	notification, err := p.getEmailNotificationDetails(ctx, req.GetBillId())
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Error while getting email notification").Error())
	}
	res.EmailNotification = notification
	return res, nil
}

// nolint: funlen
func (p *Processor) getEmailNotificationDetails(ctx context.Context, billId string) (*notificationPb.Notification, error) {

	billInfo, billWinodw, err := p.rpcHelper.GetBillAndBillWindowById(ctx, billId)
	if err != nil {
		return nil, err
	}
	accountDetail, err := p.rpcHelper.GetCreditAccountById(ctx, billInfo.GetAccountId())
	if err != nil {
		return nil, err
	}
	billS3Path := billInfo.GetS3Path()
	fileContent, err := p.s3Client.Read(ctx, billS3Path)
	// nolint
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, "Error while reading file from s3path")
	}
	// nolint:ineffassign
	creditCardDetails, err := p.getValidCreditCardDetailByAccountId(ctx, accountDetail.GetId(), []ccEnumsPb.CreditCardFieldMask{
		ccEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO,
		ccEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE})
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Error while getting valid cc detail").Error())
	}
	fileName := generateFileName(creditCardDetails.GetBasicInfo().GetMaskedCardNumber(), datetime.TimestampToDateInLoc(billInfo.GetStatementDate(), datetime.IST))

	userDetails, err := p.rpcHelper.GetUserDetailsFromActorId(ctx, billInfo.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "Error fetching user details from actor id")
	}

	ccLastFourDigits, err := helper.GetCreditCardLastKDigits(creditCardDetails.GetBasicInfo().GetMaskedCardNumber(), 4)
	if err != nil {
		return nil, err
	}

	creditCardImage, creditCardHeaderImage := "", commonHeaderImage
	var totalFiCoins int64
	switch {
	case ffPkg.IsCreditCardProgramSecured(accountDetail.GetCardProgram()):
		creditCardImage = "https://epifi-icons.pointz.in/credit_card_images/credit_card_statement_card_image_simplifi_4x.png"
		totalFiCoins = int64(billInfo.GetRewardsInfo().GetSecuredCardsRewardInfo().GetProjectedTotalRewardCoins())
	case ffPkg.IsCreditCardProgramMassUnsecured(accountDetail.GetCardProgram()):
		creditCardImage = "https://epifi-icons.pointz.in/credit_card_images/credit_card_statement_card_image_magnifi_4x.png"
		totalFiCoins = int64(billInfo.GetRewardsInfo().GetRewardsConstructInfo().GetProjectedTotalRewardCoins())
	default:
		creditCardImage = "https://epifi-icons.pointz.in/credit_card_images/credit_card_statement_card_image_4x.png"
		totalFiCoins = int64(billInfo.GetRewardsInfo().GetExtraRewardsConstructInfo().GetTotalRewardCoins())
	}

	ccStatementAttributes := &helper.CcStatementEmailAttributes{
		CreditCardLastFourDigits:   ccLastFourDigits,
		StatementFromDate:          datetime.TimestampToDateInLoc(billWinodw.GetFromTimestamp(), datetime.IST),
		StatementToDate:            datetime.TimestampToDateInLoc(billWinodw.GetToTimestamp(), datetime.IST),
		TotalDue:                   billInfo.GetTotalDue(),
		MinimumDue:                 billInfo.GetMinDue(),
		TotalFiCoinsEarned:         totalFiCoins,
		CreditCardPayBillUrl:       "https://fi.onelink.me/GvZH/ccdash",
		PaymentDueDate:             datetime.TimestampToDateInLoc(billInfo.GetSoftDueDate(), datetime.IST),
		FileContent:                fileContent,
		FileName:                   fileName,
		UserId:                     userDetails.GetUser().GetId(),
		CreditCardImage:            creditCardImage,
		CreditCardHeaderImage:      creditCardHeaderImage,
		CreditCardMaskedCardNumber: creditCardDetails.GetBasicInfo().GetMaskedCardNumber(),
		StatementDate:              datetime.TimestampToDateInLoc(billInfo.GetStatementDate(), datetime.IST),
		TotalRewardPointsEarned:    totalFiCoins,
	}

	var emailRequest *commsPb.SendMessageRequest
	statementVersion := determineStatementVersion(datetime.TimestampToDateInLoc(billInfo.GetStatementDate(), datetime.IST))
	if statementVersion == ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V0 {
		emailRequest, err = p.notificationHelper.GetEmailRequestV2(ccStatementAttributes)
	} else {
		emailRequest, err = p.notificationHelper.GetEmailRequestV3(ccStatementAttributes)
	}
	if err != nil {
		logger.Error(ctx, "error while fetching email notification request", zap.Error(err))
		return nil, errors.Wrap(err, "error while fetching email notification request")
	}
	return &notificationPb.Notification{
		UserIdentifier: &notificationPb.Notification_EmailId{EmailId: userDetails.GetUser().GetProfile().GetEmail()},
		CommunicationList: []*commsPb.Communication{
			{
				Medium:  commsPb.Medium_EMAIL,
				Message: &commsPb.Communication_Email{Email: emailRequest.GetEmail()},
			},
		},
		QualityOfService: commsPb.QoS_GUARANTEED,
	}, nil
}
