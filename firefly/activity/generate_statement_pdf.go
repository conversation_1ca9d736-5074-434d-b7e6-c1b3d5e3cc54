// nolint: gosec
package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/log"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"

	docsPb "github.com/epifi/gamma/api/docs"
	ffActivityPb "github.com/epifi/gamma/api/firefly/activity"
	"github.com/epifi/gamma/api/firefly/billing"
	ffBeBillingEnums "github.com/epifi/gamma/api/firefly/billing/enums"
	ccEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	typesPb "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	ccFireflyBillingModel "github.com/epifi/gamma/firefly/billing/model"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/pkg/address"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

var (
	txnOriginMap = map[ccEnumsPb.TransactionOrigin]string{
		ccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_CARD:   "Card",
		ccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_WEB:    "Web",
		ccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_MOBILE: "Mobile",
		ccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_ATM:    "Atm",
		ccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_POS:    "Pos",
		ccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_ECOM:   "Ecom",
	}

	txnTransactionType = map[typesPb.TransactionTransferType]string{
		typesPb.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_CREDIT: "Credit",
		typesPb.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT:  "Debit",
	}

	maskedCardNumberTemplate = "•••• •••• •••• %s"

	bulbIcon                               = "https://epifi-icons.s3.ap-south-1.amazonaws.com/credit_card_images/Bulb_4x.png"
	extraRewards2xIconUrl                  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/credit_card_images/2x_statement_extra_rewards.png"
	extraRewards1xIconUrl                  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/credit_card_images/unsecured_v2_cvp_base_4x.png"
	extraRewardsFiCollectionIconUrl        = "https://epifi-icons.s3.ap-south-1.amazonaws.com/credit_card_images/unsecured_v2_cvp_accelarated_4x.png"
	extraRewards5xIconUrl                  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/credit_card_images/5x_statement_extra_rewards.png"
	weekendRewardsIconUrl                  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/credit_card_images/secured_cards_rewards_weekend_dashboard_4x.png"
	weekdayRewardsIconUrl                  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/credit_card_images/secured_cards_rewards_weekday_dashboard_4x.png"
	massUnsecuredBaseRewardsIconUrl        = "https://epifi-icons.s3.ap-south-1.amazonaws.com/credit_card_images/massunsecured_base_rewards_4x.png"
	massUnsecuredAcceleratedRewardsIconUrl = "https://epifi-icons.s3.ap-south-1.amazonaws.com/credit_card_images/massunsecured_accelerated_rewards_4x.png"
	rewardTypeToStringMap                  = map[ffBeBillingEnums.MerchantRewardType]string{
		ffBeBillingEnums.MerchantRewardType_MERCHANT_REWARD_TYPE_FIVE_X:      "5X rewards on your top merchants",
		ffBeBillingEnums.MerchantRewardType_MERCHANT_REWARD_TYPE_TWO_X:       "2X rewards on your top merchants",
		ffBeBillingEnums.MerchantRewardType_MERCHANT_REWARD_TYPE_UNSPECIFIED: "",
	}

	fallbackAddress = "Please login to Fi app to get your address here"

	fiBrandLogo     = "https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/svgs/logo_v2.svg"
	partnerBankLogo = "https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/svgs/federal-logo_v2.svg"
)

func (p *Processor) GenerateStatementPdf(ctx context.Context, req *ffActivityPb.GenerateStatementPdfRequest) (*ffActivityPb.GenerateStatementPdfResponse, error) {
	res := &ffActivityPb.GenerateStatementPdfResponse{}
	lg := activity.GetLogger(ctx)

	_, cardRequestStage, err := p.getCardReqAndCreateCardReqStage(ctx, req.GetRequestHeader().GetClientReqId(),
		ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_BILL_GENERATE_STATEMENT_PDF,
		ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_CREATED, ccEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFIED, "")
	if err != nil {
		lg.Error("error in getCardReqAndCreateCardReqStage", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, errors.Wrap(err, "error in getCardReqAndCreateCardReqStage")
	}

	finishActivity, err := p.checkIfStageAlreadyTerminated(cardRequestStage)
	switch {
	case err != nil:
		return nil, err
	case finishActivity:
		return res, nil
	default:
		// continue
	}

	res, err = p.generateStatementPdf(ctx, req, lg)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Error in generating pdf of the statement").Error())
	}

	return res, nil
}

// TODO(@saurabh): Move constants to another static file
// nolint: funlen
func (p *Processor) generateStatementPdf(ctx context.Context, req *ffActivityPb.GenerateStatementPdfRequest, lg log.Logger) (*ffActivityPb.GenerateStatementPdfResponse, error) {
	var (
		fromDate, toDate = getStatementWindow(req.GetSummary().GetStatementDate())
	)
	ccAccount, err := p.rpcHelper.GetCreditAccountDetails(ctx, req.GetEntityId())
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Error in fetching account").Error())
	}
	creditCardDetails, err := p.getValidCreditCardDetailByAccountId(ctx, ccAccount.GetId(), []ccEnumsPb.CreditCardFieldMask{
		ccEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACTOR_ID,
		ccEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE,
		ccEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO,
	})
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil, errors.Wrap(epifierrors.ErrPermanent, errors.Wrap(err, "Unable to fetch credit card").Error())
	case err != nil:
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Unable to fetch credit card").Error())
	}

	rewardCvpIdentifier := p.getStatementRewardCvpIdentifier(ccAccount, req.GetStatementDate())
	if rewardCvpIdentifier == ccEnumsPb.RewardCvp_REWARD_CVP_UNSPECIFIED {
		return nil, epifitemporal.NewPermanentError(errors.New("error fetching reward cvp identifier for the statement"))
	}
	statementCvpDetails, err := p.getStatementCvpDetails(&StatementCvpDetailsRequest{
		rewardCvpIdentifier: rewardCvpIdentifier,
		rewardsInfo:         req.GetRewardsInfo(),
		billingWindow: &billing.BillWindow{
			FromTimestamp: datetime.DateToTimestamp(fromDate, datetime.IST),
			ToTimestamp:   datetime.DateToTimestamp(toDate, datetime.IST),
		},
	})
	if err != nil {
		return nil, epifitemporal.NewTransientError(errors.New("error fetching statement cvp details"))
	}

	userDetails, err := p.rpcHelper.GetUserDetailsFromActorId(ctx, creditCardDetails.GetActorId())
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Error fetching user details from actor id").Error())
	}

	lastFourDigitsCardNumber := creditCardDetails.GetBasicInfo().GetMaskedCardNumber()[len(creditCardDetails.GetBasicInfo().GetMaskedCardNumber())-4:]
	transactions := make([]*ccFireflyBillingModel.StatementTransaction, 0)
	for _, txn := range req.GetTransactions() {
		txnDateInTimeStamp := txn.GetTransactionTimestamp()
		transactions = append(transactions, &ccFireflyBillingModel.StatementTransaction{
			Amount:           txn.GetAmount(),
			Date:             txnDateInTimeStamp,
			MerchantName:     txn.GetMerchantName(),
			MerchantLocation: txn.GetLocation(),
			TransactionType:  txnTransactionType[txn.GetTransactionTransferType()],
			PaymentMethod:    txnOriginMap[txn.GetTransactionOrigin()],
			RewardCoins:      txn.GetRewardCoins(),
			RewardPoints:     txn.GetRewardPoints(),
		})
	}

	userAddress := ""
	userPartnerBankDetails, err := p.rpcHelper.GetFederalCustomerInfo(ctx, creditCardDetails.GetActorId())
	switch {
	case err != nil:
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Error fetching user partner bank details from partner bank apis").Error())
	case userPartnerBankDetails.GetStatus().IsEqualTo(rpc.NewStatus(uint32(userPb.GetCustomerDetailsResponse_VENDOR_API_FAILURE), "", "")):
		logger.Error(ctx, "vendor error in fetching user address details,using fallback address text now")
		userAddress = fallbackAddress
	default:
		userAddress = address.ConvertPostalAddressToString(userPartnerBankDetails.GetAddresses()[typesPb.AddressType_PERMANENT.String()])
	}
	statement := &ccFireflyBillingModel.StatementPdf{
		UserDetails: &ccFireflyBillingModel.StatementUserDetail{
			Name:                   userDetails.GetUser().GetProfile().GetKycName().ToString(),
			PhoneNumber:            userDetails.GetUser().GetProfile().GetPhoneNumber().GetNationalNumber(),
			Email:                  userDetails.GetUser().GetProfile().GetEmail(),
			Address:                userAddress,
			MaskedCreditCardNumber: fmt.Sprintf(maskedCardNumberTemplate, lastFourDigitsCardNumber),
		},
		FromDate: fromDate,
		ToDate:   toDate,
		Summary: &ccFireflyBillingModel.StatementSummary{
			StatementDate:        req.GetSummary().GetStatementDate(),
			PaymentDueDate:       req.GetSummary().GetPaymentDueDate(),
			AvailableLimit:       req.GetSummary().GetAvailableLimit(),
			TotalAmountDue:       req.GetSummary().GetTotalAmountDue(),
			MinAmountDue:         req.GetSummary().GetMinAmountDue(),
			OpeningBalance:       req.GetSummary().GetOpeningBalance(),
			Spends:               req.GetSummary().GetSpends(),
			Fees:                 req.GetSummary().GetFees(),
			RepaymentsAndRefunds: req.GetSummary().GetRepaymentAndRefunds(),
			SpendsConvertedToEmi: req.GetSummary().GetSpendConvertedToEmi(),
		},
		TransactionDetails:        transactions,
		RewardsSummary:            statementCvpDetails.rewardSummary,
		ExtraRewardsInfo:          statementCvpDetails.extraRewardsInfo,
		TipsAndInformationSection: statementCvpDetails.tipsAndInformationSection,
		FeeBreakDown:              p.getStatementFeeBreakdown(req.GetFeeBreakDown()),
		ContactUsDetails:          statementCvpDetails.ContactUsDetails,
		EmiSummary:                p.getStatementEmiSummaryDetails(req.GetEmiSummary()),
		FiBrandLogo:               fiBrandLogo,
		PartnerBankLogo:           partnerBankLogo,
		CreditCardProgramLogo:     statementCvpDetails.cardProgramLogo,
		TransactionModelVersion:   ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V2,
	}

	// determine the appropriate transaction model version based on the statement period
	// relative to the Fi-Coins to Fi-Points migration timeline.
	//
	// Version Logic:
	// - V0 (Legacy): Statement period ends before migration - shows only Fi-Coins
	// - V1 (Transition): Statement period spans migration - shows both Fi-Coins and Fi-Points
	// - V2 (Current): Statement period starts after migration - shows only Fi-Points
	//
	// This versioning ensures:
	// 1. Historical statements maintain Fi-Coins branding for consistency
	// 2. Transition period statements show both reward types for clarity
	// 3. Future statements use the new Fi-Points branding exclusively
	statement.TransactionModelVersion = determineStatementVersion(req.GetStatementDate())

	data, err := json.Marshal(statement)
	if err != nil {
		lg.Error("failed to marshal pdf data", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}
	fileName := generateFileName(creditCardDetails.GetBasicInfo().GetMaskedCardNumber(), req.GetSummary().GetStatementDate())
	password := generateUserPassword(userDetails)
	pdfReq := &docsPb.GeneratePdfRequest{
		PdfTemplate:         docsPb.PDFTemplate_CREDIT_CARD_STATEMENT_V2,
		Data:                data,
		OwnerPassword:       password,
		UserPassword:        password,
		ExpiryTimeInSeconds: int64(15 * time.Minute.Seconds()),
		FileName:            fileName,
		FileNamePrefix:      docsPb.FileNamePrefix_STATEMENT,
	}
	resp, err := p.rpcHelper.GeneratePdfUsingDocsClient(ctx, pdfReq)
	if err != nil {
		lg.Error("failed to generate pdf", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Failed to generate pdf").Error())
	}
	return &ffActivityPb.GenerateStatementPdfResponse{
		FileUrl:  resp.GetFileUrl(),
		FileName: fileName,
	}, nil
}

func generateUserPassword(userDetails *userPb.GetUserResponse) string {
	// password is first four letters of name and dob in ddmm format
	// Example if kyc name is JOLLY JOSEPH And dob is 20 Jan 1990, then password is JOLL2001
	kycName := userDetails.GetUser().GetProfile().GetKycName().ToString()
	kycName = strings.ReplaceAll(kycName, " ", "")
	if len(kycName) >= 4 {
		kycName = kycName[:4]
	}
	kycName = strings.ToUpper(kycName)
	dobString := datetime.DateToString(userDetails.GetUser().GetProfile().GetDateOfBirth(), "0201", datetime.IST)
	return kycName + dobString
}

func getStatementWindow(statementGenerationDate *date.Date) (*date.Date, *date.Date) {
	statementTimestamp := datetime.DateToTime(statementGenerationDate, datetime.IST)
	fromTimeStamp := statementTimestamp.AddDate(0, -1, 0)
	toTimeStamp := statementTimestamp.AddDate(0, 0, -1)
	return datetime.TimeToDateInLoc(fromTimeStamp, datetime.IST), datetime.TimeToDateInLoc(toTimeStamp, datetime.IST)
}

func generateFileName(maskedCardNumber string, statementDate *date.Date) string {
	statementDateString := datetime.DateToString(statementDate, "012006", datetime.IST)
	return fmt.Sprintf("%s_%s_%s", "statement", maskedCardNumber, statementDateString)
}

func (p *Processor) getStatementFeeBreakdown(feeBreakDown *billing.FeeBreakDown) *ccFireflyBillingModel.FeeBreakDown {
	return &ccFireflyBillingModel.FeeBreakDown{
		Title:                  feeBreakDown.GetTitle(),
		FeeBreakDownComponents: p.getStatementFeeBreakdownComponents(feeBreakDown.GetFeeBreakDownComponents()),
	}
}

func (p *Processor) getStatementFeeBreakdownComponents(breakdownComponents []*billing.FeeBreakDownComponents) []*ccFireflyBillingModel.FeeBreakDownComponents {
	statementBreakdownComponents := make([]*ccFireflyBillingModel.FeeBreakDownComponents, 0)
	for _, breakdownComponent := range breakdownComponents {
		statementBreakdownComponents = append(statementBreakdownComponents, &ccFireflyBillingModel.FeeBreakDownComponents{
			FeeType:       breakdownComponent.GetFeeType(),
			Amount:        breakdownComponent.GetAmount(),
			FeeAmountType: breakdownComponent.GetFeeAmountType(),
		})
	}
	return statementBreakdownComponents
}

func (p *Processor) getStatementMerchantWiseRewardsInfo(merchantRewardInfos []*billing.MerchantRewardsInfo) []*ccFireflyBillingModel.MerchantWiseRewardInfo {
	statementRewardInfo := make([]*ccFireflyBillingModel.MerchantWiseRewardInfo, 0)
	for _, merchantRewardInfo := range merchantRewardInfos {
		statementRewardInfo = append(statementRewardInfo, &ccFireflyBillingModel.MerchantWiseRewardInfo{
			MerchantDisplayName:    ffPkg.GetMerchantDisplayNameByMerchantName(merchantRewardInfo.GetMerchantName()),
			TotalRewardCoinsEarned: merchantRewardInfo.GetTotalRewardCoins(),
			MerchantIconUrl:        ffPkg.GetMerchantLogoByMerchantName(merchantRewardInfo.GetMerchantName()),
		})
	}
	return statementRewardInfo
}

func (p *Processor) getStatementEmiSummaryDetails(emiSummary *billing.EmiSummary) *ccFireflyBillingModel.EmiSummary {
	return &ccFireflyBillingModel.EmiSummary{
		NoOfActiveEmi: int32(emiSummary.GetNumberOfActiveEmi()),
		DueAmount:     emiSummary.GetDueAmount(),
		EmiDetails:    getStatementMerchantLevelEmiDetails(emiSummary.GetEmiDetails()),
	}
}

func getStatementMerchantLevelEmiDetails(emiDetails []*billing.EmiDetail) []*ccFireflyBillingModel.EmiDetail {
	statementEmiDetails := make([]*ccFireflyBillingModel.EmiDetail, 0)
	for _, emiDetail := range emiDetails {
		statementEmiDetails = append(statementEmiDetails, &ccFireflyBillingModel.EmiDetail{
			MerchantName:      emiDetail.GetMerchantName(),
			InstallmentNumber: int32(emiDetail.GetInstallmentNumber()),
			TotalInstallment:  int32(emiDetail.GetTotalInstallments()),
			Amount:            emiDetail.GetAmount(),
		})
	}
	return statementEmiDetails
}

// determineStatementVersion determines the appropriate transaction model version
// based on the statement period's relationship to the Fi-Coins to Fi-Points migration.
//
// This function implements a three-tier versioning strategy:
//
// V0 (Legacy Fi-Coins Only):
//   - Used when the entire statement period occurs before the migration
//   - Ensures historical consistency for statements generated before Fi-Points introduction
//   - Template displays only Fi-Coins columns and branding
//
// V1 (Transition Period):
//   - Used when the statement period spans across the migration boundary
//   - Handles the migration transition period where both reward types may exist
//   - Template displays both Fi-Coins and Fi-Points columns with appropriate indicators
//   - Critical for maintaining data integrity during the migration window
//
// V2 (Current Fi-Points Only):
//   - Used when the entire statement period occurs after the migration
//   - Represents the current state where only Fi-Points are awarded
//   - Template displays only Fi-Points columns and branding
//
// Parameters:
//   - fromDate: Statement period start date
//   - toDate: Statement period end date
//
// Returns:
//   - Appropriate statement version constant for template rendering
func determineStatementVersion(statementDate *date.Date) string {
	migrationTime := accrualPkg.GetFiCoinsToFiPointsMigrationTime()
	fromDate, _ := getStatementWindow(statementDate)
	fromTimestamp := datetime.DateToTimestamp(fromDate, datetime.IST).AsTime()

	// V0: Statement period ends before migration (legacy Fi-Coins only)
	if datetime.DateToTimestamp(statementDate, datetime.IST).AsTime().Before(migrationTime) {
		return ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V0
	}

	// V2: Statement period starts after or on migration day (current Fi-Points only)
	if fromTimestamp.After(migrationTime) || fromTimestamp.Equal(migrationTime) {
		return ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V2
	}

	// V1: Statement period spans migration (transition period with both reward types)
	return ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V1
}
