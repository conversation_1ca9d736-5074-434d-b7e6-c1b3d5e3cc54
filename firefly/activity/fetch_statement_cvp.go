// nolint
package activity

import (
	"context"
	"errors"

	pkgErrors "github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/errgroup"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	ffAccEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	ccFireflyBillingPb "github.com/epifi/gamma/api/firefly/billing"
	ccEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	ccPinotPb "github.com/epifi/gamma/api/firefly/pinot"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	ccFireflyBillingModel "github.com/epifi/gamma/firefly/billing/model"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

// RewardCvpDetailsResponse holds the necessary details for a given reward cvp for which the statement will be generated.
type RewardCvpDetailsResponse struct {
	// Deprecated: in favour of `txnToRewardsMappingV2`
	txnToRewardsMapping   map[string]int32
	rewardsInfo           *ccFireflyBillingPb.RewardsInfo
	txnToRewardsMappingV2 map[string]*rewardsUnitsInfo
}

type rewardsUnitsInfo struct {
	fiCoinUnits  int32
	fiPointUnits int32
}

type RewardCvpDetailsRequest struct {
	ccBillResp          *ccFireflyBillingPb.GetCreditCardBillResponse
	creditAccount       *ffAccPb.CreditAccount
	rewardCvpIdentifier ccEnumsPb.RewardCvp
	txnRefIds           []string
	statementDate       *date.Date
}

// For All the card CVPs:
// Aggregate rewards will be fetched as follows:
// Bill ref id will be used to fetch aggregated rewards (base and accelerated rewards) using GetRewardsByActorId RPC.
// Txn level rewards will be fetched as follows:
// Txn projections if actualised has a field called rewards_contributions, which stores the actualised rewards against a projection.
// For a txn both base and accelerated projections are generated.
// However only one of it gets actualised. This will be used to fetch txn level rewards to be shown in the statement.
// Priority will be given to accelerated txn projections actualised units (if this is actualised, base txn projections is not)
// and then to base txn projections.

func (p *Processor) getSecuredRewardCvp(ctx context.Context, request *RewardCvpDetailsRequest) (*RewardCvpDetailsResponse, error) {

	if request == nil || request.rewardCvpIdentifier != ccEnumsPb.RewardCvp_REWARD_CVP_SECURED_V1 {
		return nil, errors.New("invalid request to fetch secured reward v1 cvp")
	}

	var (
		billRewards                                   = &rewardsPb.RewardsResponse{}
		baseTxnProjections                            = &rewardsProjectionPb.GetRewardsProjectionsResponse{}
		acceleratedTxnProjections                     = &rewardsProjectionPb.GetRewardsProjectionsResponse{}
		baseTxnErr, acceleratedTxnErr, billRewardsErr error
		shouldFetchDeletedRewardProjections           = shouldFetchDeletedProjections(request.statementDate)
		projectionRewardsPageSize                     = uint32(len(request.txnRefIds))
	)
	if shouldFetchDeletedRewardProjections {
		// Although overall projections should not be more than 2*(number of txns in billing window)
		// we're fetching 3*(number of txns in billing window) to be on safer side
		projectionRewardsPageSize = 3 * projectionRewardsPageSize
	}

	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		billRewards, billRewardsErr = p.rpcHelper.GetRewards(grpCtx, &rewardsPb.RewardsByActorIdRequest{
			ActorId: request.creditAccount.GetActorId(),
			FiltersV2: &rewardsPb.RewardsByActorIdRequest_FiltersV2{
				AndFilter: &rewardsPb.RewardsByActorIdRequest_Filter{
					RefIds: []string{request.ccBillResp.GetCreditCardBill().GetId()},
				},
			},
			PageContext: &rpc.PageContextRequest{
				PageSize: uint32(10),
			},
		})
		if billRewardsErr != nil {
			return pkgErrors.Wrap(billRewardsErr, "error in fetching bill rewards")
		}
		return nil
	})
	grp.Go(func() error {
		if len(request.txnRefIds) == 0 {
			return nil
		}
		baseTxnProjections, baseTxnErr = p.rpcHelper.GetRewardsProjection(grpCtx, &rewardsProjectionPb.GetRewardsProjectionsRequest{
			ActorId: request.creditAccount.GetActorId(),
			Filters: &rewardsProjectionPb.GetRewardsProjectionsRequest_Filters{
				RefIds: request.txnRefIds,
				OfferType: []rewardsPb.RewardOfferType{
					rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER,
				},
				FetchDeletedProjections: shouldFetchDeletedRewardProjections,
			},
			PageCtxRequest: &rpc.PageContextRequest{
				PageSize: projectionRewardsPageSize,
			},
		})
		if baseTxnErr != nil {
			return pkgErrors.Wrap(baseTxnErr, "error in base txn fetch")
		}
		return nil
	})
	grp.Go(func() error {
		if len(request.txnRefIds) == 0 {
			return nil
		}
		acceleratedTxnProjections, acceleratedTxnErr = p.rpcHelper.GetRewardsProjection(grpCtx, &rewardsProjectionPb.GetRewardsProjectionsRequest{
			ActorId: request.creditAccount.GetActorId(),
			Filters: &rewardsProjectionPb.GetRewardsProjectionsRequest_Filters{
				RefIds: request.txnRefIds,
				OfferType: []rewardsPb.RewardOfferType{
					rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKEND_OFFER,
				},
				FetchDeletedProjections: shouldFetchDeletedRewardProjections,
			},
			PageCtxRequest: &rpc.PageContextRequest{
				PageSize: projectionRewardsPageSize,
			},
		})
		if acceleratedTxnErr != nil {
			return pkgErrors.Wrap(acceleratedTxnErr, "error in accelerated txns projections fetch")
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		return nil, pkgErrors.Wrap(err, "error fetching details")
	}

	baseRewards, acceleratedRewards := 0, 0
	if len(billRewards.GetRewards()) != 0 {
		for _, rewards := range billRewards.GetRewards() {
			switch rewards.GetOfferType() {
			case rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER:
				if isRewardOptionGenerated(rewards.GetRewardOptions().GetOptions()) {
					baseRewards = int(rewards.GetRewardOptions().GetOptions()[0].GetRewardUnitsCalculationInfo().GetRewardUnitsCalculationEntries()[0].GetRewardValue())
				}
			case rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKEND_OFFER:
				if isRewardOptionGenerated(rewards.GetRewardOptions().GetOptions()) {
					acceleratedRewards = int(rewards.GetRewardOptions().GetOptions()[0].GetRewardUnitsCalculationInfo().GetRewardUnitsCalculationEntries()[0].GetRewardValue())
				}
			}
		}
	}
	totalRewardCoins := baseRewards + acceleratedRewards

	txnToRewardsMapping := make(map[string]int32)
	txnToRewardsMappingV2 := make(map[string]*rewardsUnitsInfo)
	for _, projection := range acceleratedTxnProjections.GetIndividualProjections().GetProjections() {
		if len(projection.GetRewardContributions().GetRewardUnitsWithTypes()) > 0 {
			_, ok := txnToRewardsMappingV2[projection.GetRefId()]
			if !ok {
				txnToRewardsMappingV2[projection.GetRefId()] = &rewardsUnitsInfo{}
			}
			if projection.GetDeletedAt() != nil {
				txnToRewardsMapping[projection.GetRefId()] = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
				txnToRewardsMappingV2[projection.GetRefId()].fiCoinUnits = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
			} else {
				txnToRewardsMappingV2[projection.GetRefId()].fiPointUnits = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
			}
		}
	}
	for _, projection := range baseTxnProjections.GetIndividualProjections().GetProjections() {
		_, ok := txnToRewardsMappingV2[projection.GetRefId()]
		if !ok && len(projection.GetRewardContributions().GetRewardUnitsWithTypes()) > 0 {
			txnToRewardsMappingV2[projection.GetRefId()] = &rewardsUnitsInfo{}
			if projection.GetDeletedAt() != nil {
				txnToRewardsMapping[projection.GetRefId()] = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
				txnToRewardsMappingV2[projection.GetRefId()].fiCoinUnits = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
			} else {
				txnToRewardsMappingV2[projection.GetRefId()].fiPointUnits = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
			}
		}
	}

	// in case of deleted reward projections were not fetched i.e. statement version is either V0 or V2,
	// fi-coins value should be same as fi-points values to maintain backward and forward compatibility
	if !shouldFetchDeletedRewardProjections {
		for refId, rewardUnits := range txnToRewardsMappingV2 {
			txnToRewardsMapping[refId] = rewardUnits.fiPointUnits
			rewardUnits.fiCoinUnits = rewardUnits.fiPointUnits
		}
	}

	rewardsInfo := &ccFireflyBillingPb.RewardsInfo{
		RewardCoinsEarned: int32(totalRewardCoins),
		SecuredCardsRewardInfo: &ccFireflyBillingPb.SecuredCardsRewardInfo{
			ProjectedTotalRewardCoins:   int32(totalRewardCoins),
			ProjectedWeekdayRewardCoins: int32(baseRewards),
			ProjectedWeekendRewardCoins: int32(acceleratedRewards),
		},
	}

	return &RewardCvpDetailsResponse{
		txnToRewardsMapping:   txnToRewardsMapping,
		rewardsInfo:           rewardsInfo,
		txnToRewardsMappingV2: txnToRewardsMappingV2,
	}, nil
}

// NOTE: no specific handling is required for `REWARD_CVP_UNSECURED_V1` since this CVP version was already disabled and all the users were migrated to CVP version V2.
func (p *Processor) getUnSecuredRewardCvpV1(ctx context.Context, request *RewardCvpDetailsRequest) (*RewardCvpDetailsResponse, error) {
	if request == nil || request.rewardCvpIdentifier != ccEnumsPb.RewardCvp_REWARD_CVP_UNSECURED_V1 {
		return nil, errors.New("invalid request to fetch unsecured reward v1 cvp")
	}

	var (
		spendRefundAggregate                                                            = &ccPinotPb.GetRefundTransactionAggregatesResponse{}
		spendsAggregate                                                                 = &ccPinotPb.GetTransactionAggregatesResponse{}
		rewardsForTxns                                                                  = &rewardsPb.GetCreditCardLinkedRewardDetailsResponse{}
		extraRewardsInfo                                                                = &rewardsPb.GetCreditCard1XRewardsSummaryResponse{}
		spendAggregatesErr, spendRefundAggregateErr, rewardsForTxnsErr, extraRewardsErr error
	)

	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		if len(request.txnRefIds) == 0 {
			return nil
		}
		rewardsForTxns, rewardsForTxnsErr = p.rpcHelper.GetTransactionRewardDetails(grpCtx, &rewardsPb.GetCreditCardLinkedRewardDetailsRequest{
			RefIds:          request.txnRefIds,
			RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
		})
		if rewardsForTxnsErr != nil {
			return pkgErrors.Wrap(rewardsForTxnsErr, "Error fetching rewards for txns")
		}
		return nil
	})
	grp.Go(func() error {
		extraRewardsInfo, extraRewardsErr = p.rpcHelper.GetProjectedExtraRewardDetails(grpCtx, &rewardsPb.GetCreditCard1XRewardsSummaryRequest{
			CreditCardAccountId: request.creditAccount.GetId(),
			TimeWindow: &rewardsPb.TimeWindow{
				FromTime: request.ccBillResp.GetBillWindow().GetFromTimestamp(),
				TillTime: request.ccBillResp.GetBillWindow().GetToTimestamp(),
			},
		})
		if extraRewardsErr != nil {
			return pkgErrors.Wrap(extraRewardsErr, "Error fetching balance")
		}
		return nil
	})
	grp.Go(func() error {
		spendsAggregate, spendAggregatesErr = p.rpcHelper.GetSpendsAggregate(grpCtx, &ccPinotPb.GetTransactionAggregatesRequest{
			ActorId:              request.creditAccount.GetActorId(),
			FromExecutedTime:     request.ccBillResp.GetBillWindow().GetFromTimestamp(),
			ToExecutedTime:       request.ccBillResp.GetBillWindow().GetToTimestamp(),
			TransactionStatus:    []ffAccEnumsPb.TransactionStatus{ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS},
			TransactionType:      ffAccEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT,
			CreditAccountId:      request.creditAccount.GetId(),
			ExcludeDsOntologyIds: ffPkg.GetOntologyIdsToExcludeWhileCalculatingAggregateSpends(),
		})
		if spendAggregatesErr != nil {
			return pkgErrors.Wrap(spendAggregatesErr, "Error fetching spends from pinot client")
		}
		return nil
	})
	grp.Go(func() error {
		spendRefundAggregate, spendRefundAggregateErr = p.rpcHelper.GetSpendsRefundAggregates(grpCtx, &ccPinotPb.GetRefundTransactionAggregatesRequest{
			ActorId:                    request.creditAccount.GetActorId(),
			FromParentExecutedTime:     request.ccBillResp.GetBillWindow().GetFromTimestamp(),
			ToParentExecutedTime:       request.ccBillResp.GetBillWindow().GetToTimestamp(),
			CreditAccountId:            request.creditAccount.GetId(),
			ExcludeParentDsOntologyIds: ffPkg.GetOntologyIdsToExcludeWhileCalculatingAggregateSpends(),
		})
		if spendRefundAggregateErr != nil {
			return pkgErrors.Wrap(spendRefundAggregateErr, "Error fetching spends refunds from pinot client")
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		return nil, err
	}

	netSpendsAggregate, calculateErr := moneyPkg.Subtract(spendsAggregate.GetTransactionAggregates().GetAmount(), spendRefundAggregate.GetTransactionAggregates().GetAmount())
	if calculateErr != nil {
		return nil, pkgErrors.Wrap(calculateErr, "Error in calculating net spends from spend aggregate and refund aggregates")
	}

	txnToRewardsMapping := make(map[string]int32, 0)
	for _, txnRewards := range rewardsForTxns.GetDetailsList() {

		switch txnRewards.GetDetails().(type) {
		case *rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails:
			if txnRewards.GetRewardDetails().GetRewardType() == rewardsPb.RewardType_FI_COINS {
				txnToRewardsMapping[txnRewards.GetRewardDetails().GetRefId()] = int32(txnRewards.GetRewardDetails().GetRewardUnits())
			}
		case *rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardClawbackDetails:
			if txnRewards.GetRewardClawbackDetails().GetRewardType() == rewardsPb.RewardType_FI_COINS {
				txnToRewardsMapping[txnRewards.GetRewardClawbackDetails().GetClawbackRefId()] = int32(-1 * txnRewards.GetRewardClawbackDetails().GetClawedBackRewardUnits())
			}
		}
	}

	rewardCoinsEarned := int32(0)
	for _, txnReward := range txnToRewardsMapping {
		rewardCoinsEarned = txnReward + rewardCoinsEarned
	}

	total2xProjectedRewardCoins, total5xProjectedRewardCoins := ffPkg.GetTotalProjectedRewardCoins(extraRewardsInfo.GetMerchantRewardAggregates(), netSpendsAggregate)
	rewardsInfo := &ccFireflyBillingPb.RewardsInfo{
		// 1x Reward coins
		RewardCoinsEarned:  rewardCoinsEarned,
		TopMerchantRewards: p.getProjectedTopMerchantRewardsForBillingCycle(extraRewardsInfo.GetMerchantRewardAggregates(), netSpendsAggregate),
		ExtraRewardsConstructInfo: &ccFireflyBillingPb.ExtraRewardsConstructInfo{
			Projected_2XRewardsCoins: int32(total2xProjectedRewardCoins),
			Projected_5XRewardsCoins: int32(total5xProjectedRewardCoins),
			Total_1XRewardsCoins:     rewardCoinsEarned,
			TotalRewardCoins:         rewardCoinsEarned + int32(total5xProjectedRewardCoins+total2xProjectedRewardCoins),
		},
	}

	return &RewardCvpDetailsResponse{
		txnToRewardsMapping: txnToRewardsMapping,
		rewardsInfo:         rewardsInfo,
	}, nil
}

func (p *Processor) getUnSecuredRewardCvpV2(ctx context.Context, request *RewardCvpDetailsRequest) (*RewardCvpDetailsResponse, error) {
	if request == nil || request.rewardCvpIdentifier != ccEnumsPb.RewardCvp_REWARD_CVP_UNSECURED_V2 {
		return nil, errors.New("invalid request to fetch unsecured reward v2 cvp")
	}

	var (
		billRewards                                   = &rewardsPb.RewardsResponse{}
		baseTxnProjections                            = &rewardsProjectionPb.GetRewardsProjectionsResponse{}
		acceleratedTxnProjections                     = &rewardsProjectionPb.GetRewardsProjectionsResponse{}
		baseTxnErr, acceleratedTxnErr, billRewardsErr error
		shouldFetchDeletedRewardProjections           = shouldFetchDeletedProjections(request.statementDate)
		projectionRewardsPageSize                     = uint32(len(request.txnRefIds))
	)
	if shouldFetchDeletedRewardProjections {
		// Although overall projections should not be more than 2*(number of txns in billing window)
		// we're fetching 3*(number of txns in billing window) to be on safer side
		projectionRewardsPageSize = 3 * projectionRewardsPageSize
	}

	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		billRewards, billRewardsErr = p.rpcHelper.GetRewards(grpCtx, &rewardsPb.RewardsByActorIdRequest{
			ActorId: request.creditAccount.GetActorId(),
			FiltersV2: &rewardsPb.RewardsByActorIdRequest_FiltersV2{
				AndFilter: &rewardsPb.RewardsByActorIdRequest_Filter{
					RefIds: []string{request.ccBillResp.GetCreditCardBill().GetId()},
				},
			},
			PageContext: &rpc.PageContextRequest{
				PageSize: uint32(10),
			},
		})
		if billRewardsErr != nil {
			return pkgErrors.Wrap(billRewardsErr, "error in fetching bill rewards")
		}
		return nil
	})
	grp.Go(func() error {
		if len(request.txnRefIds) == 0 {
			return nil
		}
		baseTxnProjections, baseTxnErr = p.rpcHelper.GetRewardsProjection(grpCtx, &rewardsProjectionPb.GetRewardsProjectionsRequest{
			ActorId: request.creditAccount.GetActorId(),
			Filters: &rewardsProjectionPb.GetRewardsProjectionsRequest_Filters{
				RefIds: request.txnRefIds,
				OfferType: []rewardsPb.RewardOfferType{
					rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
					rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_BASE_OFFER,
					rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_BASE_OFFER,
				},
				FetchDeletedProjections: shouldFetchDeletedRewardProjections,
			},
			PageCtxRequest: &rpc.PageContextRequest{
				PageSize: 2 * projectionRewardsPageSize,
			},
		})
		if baseTxnErr != nil {
			return pkgErrors.Wrap(baseTxnErr, "error in fetching base txn projections")
		}
		return nil
	})
	grp.Go(func() error {
		if len(request.txnRefIds) == 0 {
			return nil
		}
		acceleratedTxnProjections, acceleratedTxnErr = p.rpcHelper.GetRewardsProjection(grpCtx, &rewardsProjectionPb.GetRewardsProjectionsRequest{
			ActorId: request.creditAccount.GetActorId(),
			Filters: &rewardsProjectionPb.GetRewardsProjectionsRequest_Filters{
				RefIds: request.txnRefIds,
				OfferType: []rewardsPb.RewardOfferType{
					rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER,
					rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_ACCELERATED_OFFER,
				},
				FetchDeletedProjections: shouldFetchDeletedRewardProjections,
			},
			PageCtxRequest: &rpc.PageContextRequest{
				PageSize: projectionRewardsPageSize,
			},
		})
		if acceleratedTxnErr != nil {
			return pkgErrors.Wrap(acceleratedTxnErr, "error in fetching accelerated txn projections")
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		return nil, err
	}

	baseRewards, acceleratedRewards := 0, 0
	if len(billRewards.GetRewards()) != 0 {
		for _, rewards := range billRewards.GetRewards() {
			switch rewards.GetOfferType() {
			case rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER, rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_BASE_OFFER, rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_BASE_OFFER:
				if isRewardOptionGenerated(rewards.GetRewardOptions().GetOptions()) {
					baseRewards = int(rewards.GetRewardOptions().GetOptions()[0].GetRewardUnitsCalculationInfo().GetRewardUnitsCalculationEntries()[0].GetRewardValue())
				}
			case rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER, rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_ACCELERATED_OFFER:
				if isRewardOptionGenerated(rewards.GetRewardOptions().GetOptions()) {
					acceleratedRewards = int(rewards.GetRewardOptions().GetOptions()[0].GetRewardUnitsCalculationInfo().GetRewardUnitsCalculationEntries()[0].GetRewardValue())
				}
			}
		}
	}
	totalRewardCoins := baseRewards + acceleratedRewards

	txnToRewardsMapping := make(map[string]int32)
	txnToRewardsMappingV2 := make(map[string]*rewardsUnitsInfo)
	// WE will populate the map in following order:
	// If we find an accelerated projection, we will use it.
	// If there is no accelerated projection for a txn, we will use base projections.
	// Assumption here will be that all rewards will be generated post bill gen event is passed and before rewards are fetched here.
	// Hence we will use the actual rewards contributions
	for _, projection := range acceleratedTxnProjections.GetIndividualProjections().GetProjections() {
		if len(projection.GetRewardContributions().GetRewardUnitsWithTypes()) > 0 {
			_, ok := txnToRewardsMappingV2[projection.GetRefId()]
			if !ok {
				txnToRewardsMappingV2[projection.GetRefId()] = &rewardsUnitsInfo{}
			}
			if projection.GetDeletedAt() != nil {
				txnToRewardsMapping[projection.GetRefId()] = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
				txnToRewardsMappingV2[projection.GetRefId()].fiCoinUnits = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
			} else {
				txnToRewardsMappingV2[projection.GetRefId()].fiPointUnits = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
			}
		}
	}

	for _, projection := range baseTxnProjections.GetIndividualProjections().GetProjections() {
		// if the txn is not already populated through the accelerated projections in the map, we then look for it
		// in the base txn projections.
		_, ok := txnToRewardsMappingV2[projection.GetRefId()]
		if !ok && len(projection.GetRewardContributions().GetRewardUnitsWithTypes()) > 0 {
			txnToRewardsMappingV2[projection.GetRefId()] = &rewardsUnitsInfo{}
			if projection.GetDeletedAt() != nil {
				txnToRewardsMapping[projection.GetRefId()] = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
				txnToRewardsMappingV2[projection.GetRefId()].fiCoinUnits = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
			} else {
				txnToRewardsMappingV2[projection.GetRefId()].fiPointUnits = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
			}
		}
	}

	// in case of deleted reward projections were not fetched i.e. statement version is either V0 or V2,
	// fi-coins value should be same as fi-points values to maintain backward and forward compatibility
	if !shouldFetchDeletedRewardProjections {
		for refId, rewardUnits := range txnToRewardsMappingV2 {
			txnToRewardsMapping[refId] = rewardUnits.fiPointUnits
			rewardUnits.fiCoinUnits = rewardUnits.fiPointUnits
		}
	}

	rewardsInfo := &ccFireflyBillingPb.RewardsInfo{
		// 1x Reward coins
		RewardCoinsEarned: int32(totalRewardCoins),
		ExtraRewardsConstructInfo: &ccFireflyBillingPb.ExtraRewardsConstructInfo{
			Projected_5XRewardsCoins: int32(acceleratedRewards),
			Total_1XRewardsCoins:     int32(baseRewards),
			TotalRewardCoins:         int32(totalRewardCoins),
		},
	}

	return &RewardCvpDetailsResponse{
		txnToRewardsMapping:   txnToRewardsMapping,
		rewardsInfo:           rewardsInfo,
		txnToRewardsMappingV2: txnToRewardsMappingV2,
	}, nil
}

func (p *Processor) getMassUnsecuredRewardCvpV1(ctx context.Context, request *RewardCvpDetailsRequest) (*RewardCvpDetailsResponse, error) {
	if request == nil || request.rewardCvpIdentifier != ccEnumsPb.RewardCvp_REWARD_CVP_MASS_UNSECURED_V1 {
		return nil, errors.New("invalid request to fetch unsecured reward v2 cvp")
	}

	var (
		billRewards                                     = &rewardsPb.RewardsResponse{}
		acceleratedTxnProjections                       = &rewardsProjectionPb.GetRewardsProjectionsResponse{}
		baseTxnProjections                              = &rewardsProjectionPb.GetRewardsProjectionsResponse{}
		baseTxnsErr, acceleratedTxnsErr, billRewardsErr error
		shouldFetchDeletedRewardProjections             = shouldFetchDeletedProjections(request.statementDate)
		projectionRewardsPageSize                       = uint32(len(request.txnRefIds))
	)
	if shouldFetchDeletedRewardProjections {
		// Although overall projections should not be more than 2*(number of txns in billing window)
		// we're fetching 3*(number of txns in billing window) to be on safer side
		projectionRewardsPageSize = 3 * projectionRewardsPageSize
	}

	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		billRewards, billRewardsErr = p.rpcHelper.GetRewards(grpCtx, &rewardsPb.RewardsByActorIdRequest{
			ActorId: request.creditAccount.GetActorId(),
			FiltersV2: &rewardsPb.RewardsByActorIdRequest_FiltersV2{
				AndFilter: &rewardsPb.RewardsByActorIdRequest_Filter{
					RefIds: []string{request.ccBillResp.GetCreditCardBill().GetId()},
				},
			},
			PageContext: &rpc.PageContextRequest{
				PageSize: uint32(10),
			},
		})
		if billRewardsErr != nil {
			return pkgErrors.Wrap(billRewardsErr, "error in fetching bill rewards")
		}
		return nil
	})
	grp.Go(func() error {
		if len(request.txnRefIds) == 0 {
			return nil
		}
		acceleratedTxnProjections, acceleratedTxnsErr = p.rpcHelper.GetRewardsProjection(grpCtx, &rewardsProjectionPb.GetRewardsProjectionsRequest{
			ActorId: request.creditAccount.GetActorId(),
			Filters: &rewardsProjectionPb.GetRewardsProjectionsRequest_Filters{
				RefIds: request.txnRefIds,
				OfferType: []rewardsPb.RewardOfferType{
					rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_ACCELERATED_OFFER,
				},
				FetchDeletedProjections: shouldFetchDeletedRewardProjections,
			},
			PageCtxRequest: &rpc.PageContextRequest{
				PageSize: projectionRewardsPageSize,
			},
		})
		if acceleratedTxnsErr != nil {
			return pkgErrors.Wrap(acceleratedTxnsErr, "error in fetching accelerated txn projections")
		}
		return nil
	})
	grp.Go(func() error {
		if len(request.txnRefIds) == 0 {
			return nil
		}
		baseTxnProjections, baseTxnsErr = p.rpcHelper.GetRewardsProjection(grpCtx, &rewardsProjectionPb.GetRewardsProjectionsRequest{
			ActorId: request.creditAccount.GetActorId(),
			Filters: &rewardsProjectionPb.GetRewardsProjectionsRequest_Filters{
				RefIds: request.txnRefIds,
				OfferType: []rewardsPb.RewardOfferType{
					rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER,
				},
				FetchDeletedProjections: shouldFetchDeletedRewardProjections,
			},
			PageCtxRequest: &rpc.PageContextRequest{
				PageSize: projectionRewardsPageSize,
			},
		})
		if baseTxnsErr != nil {
			return pkgErrors.Wrap(acceleratedTxnsErr, "error in fetching base txn projections")
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		return nil, err
	}

	baseRewards, acceleratedRewards := 0, 0
	if len(billRewards.GetRewards()) != 0 {
		for _, rewards := range billRewards.GetRewards() {
			switch rewards.GetOfferType() {
			case rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER:
				if isRewardOptionGenerated(rewards.GetRewardOptions().GetOptions()) {
					baseRewards = int(rewards.GetRewardOptions().GetOptions()[0].GetRewardUnitsCalculationInfo().GetRewardUnitsCalculationEntries()[0].GetRewardValue())
				}
			case rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_ACCELERATED_OFFER:
				if isRewardOptionGenerated(rewards.GetRewardOptions().GetOptions()) {
					acceleratedRewards = int(rewards.GetRewardOptions().GetOptions()[0].GetRewardUnitsCalculationInfo().GetRewardUnitsCalculationEntries()[0].GetRewardValue())
				}
			}
		}
	}
	totalRewardCoins := baseRewards + acceleratedRewards

	txnToRewardsMapping := make(map[string]int32)
	txnToRewardsMappingV2 := make(map[string]*rewardsUnitsInfo)
	for _, projection := range acceleratedTxnProjections.GetIndividualProjections().GetProjections() {
		if len(projection.GetRewardContributions().GetRewardUnitsWithTypes()) > 0 {
			_, ok := txnToRewardsMappingV2[projection.GetRefId()]
			if !ok {
				txnToRewardsMappingV2[projection.GetRefId()] = &rewardsUnitsInfo{}
			}
			if projection.GetDeletedAt() != nil {
				txnToRewardsMapping[projection.GetRefId()] = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
				txnToRewardsMappingV2[projection.GetRefId()].fiCoinUnits = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
			} else {
				txnToRewardsMappingV2[projection.GetRefId()].fiPointUnits = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
			}
		}
	}
	for _, projection := range baseTxnProjections.GetIndividualProjections().GetProjections() {
		_, ok := txnToRewardsMappingV2[projection.GetRefId()]
		if !ok && len(projection.GetRewardContributions().GetRewardUnitsWithTypes()) > 0 {
			txnToRewardsMappingV2[projection.GetRefId()] = &rewardsUnitsInfo{}
			if projection.GetDeletedAt() != nil {
				txnToRewardsMapping[projection.GetRefId()] = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
				txnToRewardsMappingV2[projection.GetRefId()].fiCoinUnits = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
			} else {
				txnToRewardsMappingV2[projection.GetRefId()].fiPointUnits = int32(projection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits())
			}
		}
	}

	// in case of deleted reward projections were not fetched i.e. statement version is either V0 or V2,
	// fi-coins value should be same as fi-points values to maintain backward and forward compatibility
	if !shouldFetchDeletedRewardProjections {
		for refId, rewardUnits := range txnToRewardsMappingV2 {
			txnToRewardsMapping[refId] = rewardUnits.fiPointUnits
			rewardUnits.fiCoinUnits = rewardUnits.fiPointUnits
		}
	}

	rewardsInfo := &ccFireflyBillingPb.RewardsInfo{
		RewardCoinsEarned: int32(totalRewardCoins),
		RewardsConstructInfo: &ccFireflyBillingPb.RewardsConstructInfo{
			ProjectedBaseRewardCoins:        int32(baseRewards),
			ProjectedAcceleratedRewardCoins: int32(acceleratedRewards),
			ProjectedTotalRewardCoins:       int32(totalRewardCoins),
		},
	}

	return &RewardCvpDetailsResponse{
		txnToRewardsMapping:   txnToRewardsMapping,
		rewardsInfo:           rewardsInfo,
		txnToRewardsMappingV2: txnToRewardsMappingV2,
	}, nil
}

func (p *Processor) getRewardCvpDetails(ctx context.Context, request *RewardCvpDetailsRequest) (*RewardCvpDetailsResponse, error) {
	if request == nil {
		return nil, errors.New("invalid request for cvp details")
	}
	switch request.rewardCvpIdentifier {
	case ccEnumsPb.RewardCvp_REWARD_CVP_SECURED_V1:
		return p.getSecuredRewardCvp(ctx, request)
	case ccEnumsPb.RewardCvp_REWARD_CVP_UNSECURED_V1:
		return p.getUnSecuredRewardCvpV1(ctx, request)
	case ccEnumsPb.RewardCvp_REWARD_CVP_UNSECURED_V2:
		return p.getUnSecuredRewardCvpV2(ctx, request)
	case ccEnumsPb.RewardCvp_REWARD_CVP_MASS_UNSECURED_V1:
		return p.getMassUnsecuredRewardCvpV1(ctx, request)
	}
	return nil, errors.New("no cvp found for identifier ")
}

func isRewardOptionGenerated(rewardOptions []*rewardsPb.RewardOption) bool {
	return len(rewardOptions) != 0 &&
		rewardOptions[0].GetRewardType() == rewardsPb.RewardType_FI_COINS &&
		len(rewardOptions[0].GetRewardUnitsCalculationInfo().GetRewardUnitsCalculationEntries()) != 0
}

// shouldFetchDeletedProjections determines whether deleted reward projections should be included
// when fetching rewards projections for transactions in a billing statement.
//
// This function is specifically designed to handle the Fi-Coins to Fi-Points migration period.
// During the migration, some reward projections (Fi-Coins) were deleted and recreated as new
// projections (Fi-Points). To ensure complete transaction reward history for statements, we need
// to include the deleted projections for certain statement periods.
//
// The function determines this based on the statement version logic:
//
// Returns true for statement version V1:
//   - V1 (Transition): Statement period spans migration - includes deleted projections
//     to capture both Fi-Coins (deleted) and Fi-Points (active) data for complete
//     transaction history during the migration window
//
// Returns false for statement versions V0 and V2:
//   - V0 (Legacy): Statement period ends before migration - deleted projections not needed
//     as only Fi-Coins projections exist and are still active
//   - V2 (Current): Statement period starts after migration - deleted projections not needed
//     as only Fi-Points projections exist and are active
//
// Implementation:
//  1. Calculates statement window (fromDate, toDate) from the statement generation date
//  2. Determines statement version based on relationship to migration time
//  3. Returns true for V1, false for V0/V2
//
// Parameters:
//   - statementDate: The statement generation date used to calculate the billing window
//
// Returns:
//   - true if deleted projections should be fetched (V1 statement version only)
//   - false if only active projections are needed (V0 or V2 statement versions)
func shouldFetchDeletedProjections(statementDate *date.Date) bool {
	statementVersion := determineStatementVersion(statementDate)
	if statementVersion == ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V1 {
		return true
	}
	return false
}
