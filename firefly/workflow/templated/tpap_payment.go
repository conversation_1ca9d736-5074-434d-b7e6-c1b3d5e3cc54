// nolint:dupl,funlen
package templated

import (
	"fmt"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	"github.com/epifi/be-common/pkg/logger"

	ffActivityPb "github.com/epifi/gamma/api/firefly/activity"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	paymentProvider "github.com/epifi/gamma/firefly/workflow/providers/tpap_payment"
	"github.com/epifi/gamma/firefly/workflow/stages"
)

func PerformCreditCardPaymentFromTpap(ctx workflow.Context, _ *workflowPb.Request) error {
	lg := workflow.GetLogger(ctx)
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID

	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, nil)
	if err != nil {
		lg.Error("failed to fetch workflow processing params", zap.Error(err))
		return err
	}

	// update card request to initiated state
	cardRequest, updateErr := updateCardRequest(ctx, &ffActivityPb.CardRequestUpdateRequest{
		RequestHeader: &activityPb.RequestHeader{ClientReqId: wfProcessingParams.GetClientReqId().GetId()},
		Status:        ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
		FieldMasks:    []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS},
	})
	if updateErr != nil {
		lg.Error("failed to update card request", zap.Error(updateErr))
		return updateErr
	}

	provider := paymentProvider.NewTpapPaymentProvider()
	payStageProviders := provider.GetStageProvider(ctx, cardRequest)

	for _, stageProv := range payStageProviders {
		stage := stageProv.GetStage(cardRequest.GetRequestDetails().GetCardProgram(), cardRequest.GetWorkflow())
		preProcessRes, preProcessErr := stage.PreProcess(ctx, &stages.PreProcessRequest{
			WfReqId:            wfReqID,
			WfProcessingParams: wfProcessingParams,
			StageName:          stage.GetStageName(),
			CelestialStage:     stage.GetCelestialStageName(),
			CardRequest:        cardRequest,
		})
		if preProcessErr != nil {
			lg.Error("failed to perform stage pre-processing", zap.String("stage", stage.GetStageName().String()), zap.Error(preProcessErr))
			return preProcessErr
		}

		lg.Info(fmt.Sprintf("PerformCreditCardPaymentFromTpap wf stage: %s INITIATED", stage.GetStageName().String()))
		performRes, performErr := stage.Perform(ctx, &stages.PerformRequest{
			WfReqId:            wfReqID,
			WfProcessingParams: wfProcessingParams,
			Request:            preProcessRes,
			CardRequest:        preProcessRes.CardRequest,
			CardRequestStage:   preProcessRes.CardRequestStage,
		})
		if performErr != nil {
			lg.Error("failed to perform stage execution", zap.String("stage", stage.GetStageName().String()), zap.Error(performErr))
			// not returning from here to perform post process
		}

		resp, postProcessErr := stage.PostProcess(ctx, &stages.PostProcessRequest{
			Request:            performRes,
			WfReqId:            wfReqID,
			WfProcessingParams: wfProcessingParams,
			CelestialStage:     stage.GetCelestialStageName(),
			NextAction:         performRes.GetNextAction(),
		})

		if postProcessErr != nil {
			lg.Error("error while performing post process stage:", zap.String(logger.CARD_REQUEST_ID, cardRequest.GetId()),
				zap.String(logger.STAGE, stage.GetStageName().String()), zap.Error(postProcessErr))
			return postProcessErr
		}

		if resp.ShouldNotContinueWorkflow {
			if isCardRequestStatusTerminal(resp.CardRequest) {
				lg.Info("card request already in terminal state, not marking as failed:", zap.String(logger.CARD_REQUEST_ID, resp.CardRequest.GetId()))
				return nil
			}
			crStatus := GetCardRequestStatusFromCardRequestStageStatus(resp.CardRequestStage)
			if crStatus == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_UNSPECIFIED {
				lg.Error("card request status could not be fetched from card request stage status", zap.String(logger.CARD_REQUEST_ID, resp.CardRequest.GetId()))
				return fmt.Errorf("card request status could not be fetched from card request stage status")
			}
			_, updateErr = updateCardRequest(ctx, &ffActivityPb.CardRequestUpdateRequest{
				RequestHeader: &activityPb.RequestHeader{ClientReqId: wfProcessingParams.GetClientReqId().GetId()},
				Status:        crStatus,
				FieldMasks:    []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS},
			})
			if updateErr != nil {
				lg.Error("failed to update card request", zap.Error(updateErr))
				return updateErr
			}
			lg.Info(fmt.Sprintf("PerformCreditCardPaymentFromTpap wf stage: %s FAILED", stage.GetStageName().String()))
			return fmt.Errorf("tpap payment wf stage failure, not continuing workflow: %s %s", cardRequest.GetId(), stage.GetStageName().String())
		}
		lg.Info(fmt.Sprintf("PerformCreditCardPaymentFromTpap wf stage: %s SUCCESSFUL", stage.GetStageName().String()))
	}

	cardRequest, updateErr = updateCardRequest(ctx, &ffActivityPb.CardRequestUpdateRequest{
		RequestHeader: &activityPb.RequestHeader{ClientReqId: wfProcessingParams.GetClientReqId().GetId()},
		Status:        ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS,
		FieldMasks:    []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS},
	})
	if updateErr != nil {
		lg.Error("failed to update card request", zap.Error(updateErr))
		return updateErr
	}

	return nil
}
