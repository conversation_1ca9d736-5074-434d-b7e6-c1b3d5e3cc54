// nolint:dupl,funlen
package templated

import (
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	"github.com/epifi/be-common/pkg/logger"

	ffActivityPb "github.com/epifi/gamma/api/firefly/activity"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	rteProvider "github.com/epifi/gamma/firefly/workflow/providers/realtime_eligibility"
	"github.com/epifi/gamma/firefly/workflow/stages"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

func RealtimeCardEligibilityCheckV2(ctx workflow.Context, _ *workflowPb.Request) error {
	lg := workflow.GetLogger(ctx)
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID

	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, nil)
	if err != nil {
		lg.Error("failed to fetch workflow processing params", zap.Error(err))
		return err
	}

	// update card request to initiated state
	cardRequest, updateErr := updateCardRequest(ctx, &ffActivityPb.CardRequestUpdateRequest{
		RequestHeader: &activityPb.RequestHeader{ClientReqId: wfProcessingParams.GetClientReqId().GetId()},
		Status:        ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
		FieldMasks:    []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS},
	})
	if updateErr != nil {
		lg.Error("failed to update card request", zap.Error(updateErr))
		return updateErr
	}

	provider := rteProvider.NewRealtimeEligibilityProvider()
	rteStages, err := provider.GetStages(ctx)
	if err != nil {
		lg.Error("error getting stages of auto payment provider",
			zap.Error(err),
			zap.String(logger.CLIENT_REQUEST_ID, wfProcessingParams.GetClientReqId().GetId()))
		return errors.Wrap(err, "error getting stages of auto payment wf provider")
	}
	for _, stage := range rteStages.Stages {
		deepLinkResp, dlErr := stage.GetDeeplink(ctx, &stages.GetDeeplinkRequest{
			WfProcessingParams:   wfProcessingParams,
			CardRequest:          cardRequest,
			CardRequestStageName: stage.GetStageName(),
		})
		if dlErr != nil {
			lg.Error("failed to get deeplink for stage", zap.String(logger.STAGE, stage.GetStageName().String()), zap.Error(dlErr))
			return dlErr
		}
		preProcessRes, preProcessErr := stage.PreProcess(ctx, &stages.PreProcessRequest{
			WfReqId:            wfReqID,
			WfProcessingParams: wfProcessingParams,
			StageName:          stage.GetStageName(),
			CelestialStage:     stage.GetCelestialStageName(),
			CardRequest:        cardRequest,
			Deeplink:           deepLinkResp.Deeplink,
		})
		if preProcessErr != nil {
			lg.Error("failed to perform stage pre-processing", zap.String(logger.STAGE, stage.GetStageName().String()), zap.Error(preProcessErr))
			return preProcessErr
		}

		lg.Info(fmt.Sprintf("starting eligibility stage: %s, of program: %s", stage.GetStageName().String(), ffPkg.GetCardProgramStringFromCardProgram(cardRequest.GetRequestDetails().GetCardProgram())))

		performRes, performErr := stage.Perform(ctx, &stages.PerformRequest{
			WfReqId:            wfReqID,
			WfProcessingParams: wfProcessingParams,
			Request:            preProcessRes,
			CardRequest:        preProcessRes.CardRequest,
			CardRequestStage:   preProcessRes.CardRequestStage,
		})
		if performErr != nil {
			lg.Error("failed to perform stage execution", zap.String(logger.STAGE, stage.GetStageName().String()), zap.Error(performErr))
			// not returning from here to perform post process
		}

		resp, postProcessErr := stage.PostProcess(ctx, &stages.PostProcessRequest{
			Request:            performRes,
			WfReqId:            wfReqID,
			WfProcessingParams: wfProcessingParams,
			CelestialStage:     stage.GetCelestialStageName(),
			NextAction:         performRes.NextAction,
		})

		if postProcessErr != nil {
			lg.Error("error while performing post process stage:", zap.String(logger.CARD_REQUEST_ID, cardRequest.GetId()),
				zap.String(logger.STAGE, stage.GetStageName().String()), zap.Error(postProcessErr))
			return postProcessErr
		}

		if resp.ShouldNotContinueWorkflow {
			if isCardRequestStatusTerminal(resp.CardRequest) {
				lg.Info("card request already in terminal state, not marking as failed:", zap.String(logger.CARD_REQUEST_ID, resp.CardRequest.GetId()))
				return nil
			}
			crStatus := GetCardRequestStatusFromCardRequestStageStatus(resp.CardRequestStage)
			if crStatus == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_UNSPECIFIED {
				lg.Error("card request status could not be fetched from card request stage status", zap.String(logger.CARD_REQUEST_ID, resp.CardRequest.GetId()))
				return fmt.Errorf("card request status could not be fetched from card request stage status")
			}
			_, updateErr = updateCardRequest(ctx, &ffActivityPb.CardRequestUpdateRequest{
				RequestHeader: &activityPb.RequestHeader{ClientReqId: wfProcessingParams.GetClientReqId().GetId()},
				Status:        crStatus,
				FieldMasks:    []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS},
			})
			if updateErr != nil {
				lg.Error("failed to update card request", zap.Error(updateErr))
				return updateErr
			}
			return fmt.Errorf("realtime eligibility check wf stage failure, not continuing workflow: %s %s", cardRequest.GetId(), stage.GetStageName().String())
		}
	}

	cardRequest, updateErr = updateCardRequest(ctx, &ffActivityPb.CardRequestUpdateRequest{
		RequestHeader: &activityPb.RequestHeader{ClientReqId: wfProcessingParams.GetClientReqId().GetId()},
		Status:        ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS,
		FieldMasks:    []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS},
	})
	if updateErr != nil {
		lg.Error("failed to update card request", zap.Error(updateErr))
		return updateErr
	}

	return nil
}
