package workflow_test

import (
	"testing"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/google/uuid"
	errorsPkg "github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	ffNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/firefly"

	ffV2ActPb "github.com/epifi/gamma/api/firefly/v2/activity"
	celestialActivityV2 "github.com/epifi/gamma/celestial/activity/v2"
	ffV2ActProcessor "github.com/epifi/gamma/firefly/v2/activity"
	ffV2Workflow "github.com/epifi/gamma/firefly/v2/workflow"
)

func TestPerformCreditCardDeliveryTracking(t *testing.T) {
	const (
		defaultWorkflowID = "default-test-workflow-id"
		ownership         = commontypes.Ownership_EPIFI_TECH
	)
	var (
		clientReqId           = uuid.New().String()
		wfProcessingParamsReq = &activityPb.GetWorkflowProcessingParamsV2Request{
			RequestHeader: &activityPb.RequestHeader{
				Ownership: ownership,
			},
			WfReqId: defaultWorkflowID,
		}
	)

	type mockGetWorkflowProcessingParams struct {
		enable bool
		res    *activityPb.GetWorkflowProcessingParamsV2Response
		err    error
	}

	type mockInitiateWorkflowStage struct {
		enable bool
		req    *activityPb.InitiateWorkflowStageV2Request
		err    error
	}

	type mockUpdateWorkflowStage struct {
		enable bool
		req    *activityPb.UpdateWorkflowStageRequest
		res    *activityPb.UpdateWorkflowStageResponse
		err    error
	}

	type mockPublishStageUpdateEvent struct {
		enable bool
		req    *activityPb.PublishWorkflowUpdateEventV2Request
		err    error
	}

	type mockCreateCreditCardTrackingRequest struct {
		enable bool
		req    *ffV2ActPb.CreateCreditCardTrackingRequestActivityRequest
		res    *ffV2ActPb.CreateCreditCardTrackingRequestActivityResponse
		err    error
	}

	type mockRegisterCreditCardTrackingDetailsAtShipway struct {
		enable bool
		req    *ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest
		res    *ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityResponse
		err    error
	}

	type mockPollCreditCardTrackingStatus struct {
		enable bool
		req    *ffV2ActPb.PollCreditCardTrackingStatusActivityRequest
		res    *ffV2ActPb.PollCreditCardTrackingStatusActivityResponse
		err    error
	}

	tests := []struct {
		name                                               string
		req                                                *workflowPb.Request
		mockGetWorkflowProcessingParams                    mockGetWorkflowProcessingParams
		mockInitiateWorkflowStageTrackingRequestCreation   mockInitiateWorkflowStage
		mockCreateCreditCardTrackingRequest                mockCreateCreditCardTrackingRequest
		mockUpdateWorkflowStageTrackingRequestCreation     mockUpdateWorkflowStage
		mockPublishTrackingRequestCreationStageUpdateEvent mockPublishStageUpdateEvent
		mockInitiateWorkflowStageShipwayRegistration       mockInitiateWorkflowStage
		mockRegisterCreditCardTrackingDetailsAtShipway     mockRegisterCreditCardTrackingDetailsAtShipway
		mockUpdateWorkflowStageShipwayRegistration         mockUpdateWorkflowStage
		mockUPublishShipwayRegistrationStageUpdateEvent    mockPublishStageUpdateEvent
		mockInitiateWorkflowStageDeliveryFulfillment       mockInitiateWorkflowStage
		mockPollCreditCardTrackingStatus                   mockPollCreditCardTrackingStatus
		mockUpdateWorkflowStageDeliveryFulfillment         mockUpdateWorkflowStage
		mockPublishStageDeliveryFulfillmentStageEvent      mockPublishStageUpdateEvent
		wantErr                                            bool
	}{
		{
			name: "successfully performed credit card delivery tracking workflow",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageTrackingRequestCreation: mockInitiateWorkflowStage{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.TrackingRequestCreation),
					Status:    stagePb.Status_INITIATED,
				},
				err: nil,
			},
			mockCreateCreditCardTrackingRequest: mockCreateCreditCardTrackingRequest{
				enable: true,
				req: &ffV2ActPb.CreateCreditCardTrackingRequestActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
				},
				res: &ffV2ActPb.CreateCreditCardTrackingRequestActivityResponse{},
				err: nil,
			},
			mockUpdateWorkflowStageTrackingRequestCreation: mockUpdateWorkflowStage{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.TrackingRequestCreation),
					Status:    stagePb.Status_SUCCESSFUL,
				},
				res: &activityPb.UpdateWorkflowStageResponse{
					ResponseHeader: &activityPb.ResponseHeader{},
				},
				err: nil,
			},
			mockPublishTrackingRequestCreationStageUpdateEvent: mockPublishStageUpdateEvent{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				err: nil,
			},
			mockInitiateWorkflowStageShipwayRegistration: mockInitiateWorkflowStage{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.ShipwayRegistration),
					Status:    stagePb.Status_INITIATED,
				},
				err: nil,
			},
			mockRegisterCreditCardTrackingDetailsAtShipway: mockRegisterCreditCardTrackingDetailsAtShipway{
				enable: true,
				req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
				},
				res: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityResponse{},
				err: nil,
			},
			mockUpdateWorkflowStageShipwayRegistration: mockUpdateWorkflowStage{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.ShipwayRegistration),
					Status:    stagePb.Status_SUCCESSFUL,
				},
				err: nil,
			},
			mockUPublishShipwayRegistrationStageUpdateEvent: mockPublishStageUpdateEvent{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				err: nil,
			},
			mockInitiateWorkflowStageDeliveryFulfillment: mockInitiateWorkflowStage{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.DeliveryFulfillment),
					Status:    stagePb.Status_INITIATED,
				},
				err: nil,
			},
			mockPollCreditCardTrackingStatus: mockPollCreditCardTrackingStatus{
				enable: true,
				req: &ffV2ActPb.PollCreditCardTrackingStatusActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
				},
				res: &ffV2ActPb.PollCreditCardTrackingStatusActivityResponse{},
				err: nil,
			},
			mockUpdateWorkflowStageDeliveryFulfillment: mockUpdateWorkflowStage{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.DeliveryFulfillment),
					Status:    stagePb.Status_SUCCESSFUL,
				},
				err: nil,
			},
			mockPublishStageDeliveryFulfillmentStageEvent: mockPublishStageUpdateEvent{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				err: nil,
			},
			wantErr: false,
		},
		{
			name: "error updating delivery fulfillment stage",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageTrackingRequestCreation: mockInitiateWorkflowStage{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.TrackingRequestCreation),
					Status:    stagePb.Status_INITIATED,
				},
				err: nil,
			},
			mockCreateCreditCardTrackingRequest: mockCreateCreditCardTrackingRequest{
				enable: true,
				req: &ffV2ActPb.CreateCreditCardTrackingRequestActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
				},
				res: &ffV2ActPb.CreateCreditCardTrackingRequestActivityResponse{},
				err: nil,
			},
			mockUpdateWorkflowStageTrackingRequestCreation: mockUpdateWorkflowStage{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.TrackingRequestCreation),
					Status:    stagePb.Status_SUCCESSFUL,
				},
				err: nil,
			},
			mockPublishTrackingRequestCreationStageUpdateEvent: mockPublishStageUpdateEvent{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				err: nil,
			},
			mockInitiateWorkflowStageShipwayRegistration: mockInitiateWorkflowStage{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.ShipwayRegistration),
					Status:    stagePb.Status_INITIATED,
				},
				err: nil,
			},
			mockRegisterCreditCardTrackingDetailsAtShipway: mockRegisterCreditCardTrackingDetailsAtShipway{
				enable: true,
				req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
				},
				res: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityResponse{},
				err: nil,
			},
			mockUpdateWorkflowStageShipwayRegistration: mockUpdateWorkflowStage{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.ShipwayRegistration),
					Status:    stagePb.Status_SUCCESSFUL,
				},
				err: nil,
			},
			mockUPublishShipwayRegistrationStageUpdateEvent: mockPublishStageUpdateEvent{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				err: nil,
			},
			mockInitiateWorkflowStageDeliveryFulfillment: mockInitiateWorkflowStage{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.DeliveryFulfillment),
					Status:    stagePb.Status_INITIATED,
				},
				err: nil,
			},
			mockPollCreditCardTrackingStatus: mockPollCreditCardTrackingStatus{
				enable: true,
				req: &ffV2ActPb.PollCreditCardTrackingStatusActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
				},
				res: &ffV2ActPb.PollCreditCardTrackingStatusActivityResponse{},
				err: nil,
			},
			mockUpdateWorkflowStageDeliveryFulfillment: mockUpdateWorkflowStage{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.DeliveryFulfillment),
					Status:    stagePb.Status_SUCCESSFUL,
				},
				err: errorsPkg.Wrap(epifierrors.ErrPermanent, "error"),
			},
			wantErr: true,
		},
		{
			name: "error in poll credit card tracking status activity",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageTrackingRequestCreation: mockInitiateWorkflowStage{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.TrackingRequestCreation),
					Status:    stagePb.Status_INITIATED,
				},
				err: nil,
			},
			mockCreateCreditCardTrackingRequest: mockCreateCreditCardTrackingRequest{
				enable: true,
				req: &ffV2ActPb.CreateCreditCardTrackingRequestActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
				},
				res: &ffV2ActPb.CreateCreditCardTrackingRequestActivityResponse{},
				err: nil,
			},
			mockUpdateWorkflowStageTrackingRequestCreation: mockUpdateWorkflowStage{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.TrackingRequestCreation),
					Status:    stagePb.Status_SUCCESSFUL,
				},
				err: nil,
			},
			mockPublishTrackingRequestCreationStageUpdateEvent: mockPublishStageUpdateEvent{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				err: nil,
			},
			mockInitiateWorkflowStageShipwayRegistration: mockInitiateWorkflowStage{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.ShipwayRegistration),
					Status:    stagePb.Status_INITIATED,
				},
				err: nil,
			},
			mockRegisterCreditCardTrackingDetailsAtShipway: mockRegisterCreditCardTrackingDetailsAtShipway{
				enable: true,
				req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
				},
				res: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityResponse{},
				err: nil,
			},
			mockUpdateWorkflowStageShipwayRegistration: mockUpdateWorkflowStage{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.ShipwayRegistration),
					Status:    stagePb.Status_SUCCESSFUL,
				},
				err: nil,
			},
			mockUPublishShipwayRegistrationStageUpdateEvent: mockPublishStageUpdateEvent{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				err: nil,
			},
			mockInitiateWorkflowStageDeliveryFulfillment: mockInitiateWorkflowStage{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.DeliveryFulfillment),
					Status:    stagePb.Status_INITIATED,
				},
				err: nil,
			},
			mockPollCreditCardTrackingStatus: mockPollCreditCardTrackingStatus{
				enable: true,
				req: &ffV2ActPb.PollCreditCardTrackingStatusActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
				},
				res: nil,
				err: epifitemporal.NewTransientError(errorsPkg.New("tracking in progress")),
			},
			mockUpdateWorkflowStageDeliveryFulfillment: mockUpdateWorkflowStage{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.DeliveryFulfillment),
					Status:    stagePb.Status_MANUAL_INTERVENTION,
				},
				err: nil,
			},
			mockPublishStageDeliveryFulfillmentStageEvent: mockPublishStageUpdateEvent{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				err: nil,
			},
			wantErr: true,
		},
		{
			name: "error in register credit card tracking details at shipway activity",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageTrackingRequestCreation: mockInitiateWorkflowStage{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.TrackingRequestCreation),
					Status:    stagePb.Status_INITIATED,
				},
				err: nil,
			},
			mockCreateCreditCardTrackingRequest: mockCreateCreditCardTrackingRequest{
				enable: true,
				req: &ffV2ActPb.CreateCreditCardTrackingRequestActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
				},
				res: &ffV2ActPb.CreateCreditCardTrackingRequestActivityResponse{},
				err: nil,
			},
			mockUpdateWorkflowStageTrackingRequestCreation: mockUpdateWorkflowStage{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.TrackingRequestCreation),
					Status:    stagePb.Status_SUCCESSFUL,
				},
				err: nil,
			},
			mockPublishTrackingRequestCreationStageUpdateEvent: mockPublishStageUpdateEvent{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				err: nil,
			},
			mockInitiateWorkflowStageShipwayRegistration: mockInitiateWorkflowStage{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.ShipwayRegistration),
					Status:    stagePb.Status_INITIATED,
				},
				err: nil,
			},
			mockRegisterCreditCardTrackingDetailsAtShipway: mockRegisterCreditCardTrackingDetailsAtShipway{
				enable: true,
				req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
				},
				res: nil,
				err: errorsPkg.Wrap(epifierrors.ErrPermanent, "error"),
			},
			mockUpdateWorkflowStageShipwayRegistration: mockUpdateWorkflowStage{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.ShipwayRegistration),
					Status:    stagePb.Status_MANUAL_INTERVENTION,
				},
				err: nil,
			},
			mockUPublishShipwayRegistrationStageUpdateEvent: mockPublishStageUpdateEvent{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				err: nil,
			},
			wantErr: true,
		},
		{
			name: "error in create credit card tracking request activity",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageTrackingRequestCreation: mockInitiateWorkflowStage{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.TrackingRequestCreation),
					Status:    stagePb.Status_INITIATED,
				},
				err: nil,
			},
			mockCreateCreditCardTrackingRequest: mockCreateCreditCardTrackingRequest{
				enable: true,
				req: &ffV2ActPb.CreateCreditCardTrackingRequestActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
				},
				res: nil,
				err: errorsPkg.Wrap(epifierrors.ErrPermanent, "error"),
			},
			mockUpdateWorkflowStageTrackingRequestCreation: mockUpdateWorkflowStage{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.TrackingRequestCreation),
					Status:    stagePb.Status_MANUAL_INTERVENTION,
				},
				err: nil,
			},
			wantErr: true,
		},
		{
			name: "error initiating tracking request creation stage",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageTrackingRequestCreation: mockInitiateWorkflowStage{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:   defaultWorkflowID,
					StageEnum: celestial.GetStageEnumFromStage(ffNs.TrackingRequestCreation),
					Status:    stagePb.Status_INITIATED,
				},
				err: errorsPkg.Wrap(epifierrors.ErrPermanent, "error"),
			},
			wantErr: true,
		},
		{
			name: "error in GetWorkflowProcessingParams",
			req:  &workflowPb.Request{},
			mockGetWorkflowProcessingParams: mockGetWorkflowProcessingParams{
				enable: true,
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					ResponseHeader: nil,
					WfReqParams:    nil,
				},
				err: errorsPkg.Wrap(epifierrors.ErrPermanent, "error"),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(ffV2Workflow.PerformCreditCardDeliveryTracking)
			env.RegisterActivity(&celestialActivityV2.Processor{})
			env.RegisterActivity(&ffV2ActProcessor.Processor{})

			if tt.mockGetWorkflowProcessingParams.enable {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, wfProcessingParamsReq).
					Return(tt.mockGetWorkflowProcessingParams.res, tt.mockGetWorkflowProcessingParams.err)
			}

			if tt.mockInitiateWorkflowStageTrackingRequestCreation.enable {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, tt.mockInitiateWorkflowStageTrackingRequestCreation.req).
					Return(tt.mockInitiateWorkflowStageTrackingRequestCreation.err)
			}

			if tt.mockCreateCreditCardTrackingRequest.enable {
				env.OnActivity(string(ffNs.CreateCreditCardTrackingRequest), mock.Anything, tt.mockCreateCreditCardTrackingRequest.req).
					Return(tt.mockCreateCreditCardTrackingRequest.res, tt.mockCreateCreditCardTrackingRequest.err)
			}

			if tt.mockUpdateWorkflowStageTrackingRequestCreation.enable {
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, tt.mockUpdateWorkflowStageTrackingRequestCreation.req).
					Return(tt.mockUpdateWorkflowStageTrackingRequestCreation.err)
			}

			if tt.mockPublishTrackingRequestCreationStageUpdateEvent.enable {
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, tt.mockPublishTrackingRequestCreationStageUpdateEvent.req).
					Return(tt.mockPublishTrackingRequestCreationStageUpdateEvent.err)
			}

			if tt.mockInitiateWorkflowStageShipwayRegistration.enable {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, tt.mockInitiateWorkflowStageShipwayRegistration.req).
					Return(tt.mockInitiateWorkflowStageShipwayRegistration.err)
			}

			if tt.mockRegisterCreditCardTrackingDetailsAtShipway.enable {
				env.OnActivity(string(ffNs.RegisterCreditCardTrackingDetailsAtShipway), mock.Anything, tt.mockRegisterCreditCardTrackingDetailsAtShipway.req).
					Return(tt.mockRegisterCreditCardTrackingDetailsAtShipway.res, tt.mockRegisterCreditCardTrackingDetailsAtShipway.err)
			}

			if tt.mockUpdateWorkflowStageShipwayRegistration.enable {
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, tt.mockUpdateWorkflowStageShipwayRegistration.req).
					Return(tt.mockUpdateWorkflowStageShipwayRegistration.err)
			}

			if tt.mockUPublishShipwayRegistrationStageUpdateEvent.enable {
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, tt.mockUPublishShipwayRegistrationStageUpdateEvent.req).
					Return(tt.mockUPublishShipwayRegistrationStageUpdateEvent.err)
			}

			if tt.mockInitiateWorkflowStageDeliveryFulfillment.enable {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, tt.mockInitiateWorkflowStageDeliveryFulfillment.req).
					Return(tt.mockInitiateWorkflowStageDeliveryFulfillment.err)
			}

			if tt.mockPollCreditCardTrackingStatus.enable {
				env.OnActivity(string(ffNs.PollCreditCardTrackingStatus), mock.Anything, tt.mockPollCreditCardTrackingStatus.req).
					Return(tt.mockPollCreditCardTrackingStatus.res, tt.mockPollCreditCardTrackingStatus.err)
			}

			if tt.mockUpdateWorkflowStageDeliveryFulfillment.enable {
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, tt.mockUpdateWorkflowStageDeliveryFulfillment.req).
					Return(tt.mockUpdateWorkflowStageDeliveryFulfillment.err)
			}

			if tt.mockPublishStageDeliveryFulfillmentStageEvent.enable {
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, tt.mockPublishStageDeliveryFulfillmentStageEvent.req).
					Return(tt.mockPublishStageDeliveryFulfillmentStageEvent.err)
			}

			env.ExecuteWorkflow(ffV2Workflow.PerformCreditCardDeliveryTracking, tt.req)
			assert.True(t, env.IsWorkflowCompleted())
			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("PerformCreditCardDeliveryTracking() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}
			env.AssertExpectations(t)
		})
	}
}
