// nolint:dupl
package workflow

import (
	"fmt"

	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	ffNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/firefly"
	"github.com/epifi/be-common/pkg/logger"

	ffV2ActPb "github.com/epifi/gamma/api/firefly/v2/activity"
)

func PerformCreditCardDeliveryTracking(ctx workflow.Context, _ *workflowPb.Request) error {
	var (
		wfStageStatus stagePb.Status
		wfReqID       = workflow.GetInfo(ctx).WorkflowExecution.ID
		lg            = workflow.GetLogger(ctx)
		err           error
	)

	//  ------------------  FETCH WORKFLOW PROCESSING PARAMS --------------------------------------
	wfProcessingParams := &activity.GetWorkflowProcessingParamsV2Response{}
	if err = activityPkg.Execute(ctx, epifitemporal.GetWorkflowProcessingParamsV2, wfProcessingParams, &activity.GetWorkflowProcessingParamsV2Request{
		RequestHeader: &activity.RequestHeader{},
		WfReqId:       wfReqID,
	}); err != nil {
		lg.Error("activity failed", zap.String(logger.ACTIVITY, string(epifitemporal.GetWorkflowProcessingParamsV2)), zap.Error(err))
		return err
	}

	// --------------------- CREATE TRACKING REQUEST ----------------------------------------
	wfStageStatus, err = performTrackingRequestCreationStage(ctx, wfProcessingParams.GetWfReqParams())
	if err != nil {
		lg.Error("error in TrackingRequestCreation stage", zap.Error(err))
		return err
	}
	if wfStageStatus != stagePb.Status_SUCCESSFUL {
		return fmt.Errorf("non success stage for TrackingRequestCreation stage: %s", wfStageStatus.String())
	}

	// --------------------- REGISTER DETAILS AT SHIPWAY ----------------------------------------
	wfStageStatus, err = performRegisterDetailsAtShipwayStage(ctx, wfProcessingParams.GetWfReqParams())
	if err != nil {
		lg.Error("error in ShipwayRegistration stage", zap.Error(err))
		return err
	}
	if wfStageStatus != stagePb.Status_SUCCESSFUL {
		return fmt.Errorf("non success stage for ShipwayRegistration stage: %s", wfStageStatus.String())
	}

	// --------------------- POLL DELIVERY TRACKING STATUS ----------------------------------------
	wfStageStatus, err = performPollTrackingStatusStage(ctx, wfProcessingParams.GetWfReqParams())
	if err != nil {
		lg.Error("error in DeliveryFulfillment stage", zap.Error(err))
		return err
	}
	if wfStageStatus != stagePb.Status_SUCCESSFUL {
		return fmt.Errorf("non success stage for DeliveryFulfillment stage: %s", wfStageStatus.String())
	}
	return nil
}

func performTrackingRequestCreationStage(ctx workflow.Context, wfProcessingParams *workflowPb.ProcessingParams) (stagePb.Status, error) {
	var (
		workflowReqStatus stagePb.Status
		clientReqId       = wfProcessingParams.GetClientReqId().GetId()
		err               error
		lg                = workflow.GetLogger(ctx)
	)

	err = celestial.InitiateWorkflowStage(ctx, ffNs.TrackingRequestCreation, stagePb.Status_INITIATED)
	if err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	actErr := activityPkg.Execute(ctx, ffNs.CreateCreditCardTrackingRequest, &ffV2ActPb.CreateCreditCardTrackingRequestActivityResponse{},
		&ffV2ActPb.CreateCreditCardTrackingRequestActivityRequest{
			RequestHeader: &activity.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
		})
	if actErr != nil {
		lg.Error("error in CreateCreditCardTrackingRequest activity", zap.Error(actErr))
	}

	workflowReqStatus = celestial.GetWorkflowStageStatusForErr(actErr)
	err = celestial.UpdateWorkflowStage(ctx, ffNs.TrackingRequestCreation, workflowReqStatus)
	if err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
	}
	return workflowReqStatus, nil
}

func performRegisterDetailsAtShipwayStage(ctx workflow.Context, wfProcessingParams *workflowPb.ProcessingParams) (stagePb.Status, error) {
	var (
		workflowReqStatus stagePb.Status
		clientReqId       = wfProcessingParams.GetClientReqId().GetId()
		err               error
		lg                = workflow.GetLogger(ctx)
	)

	err = celestial.InitiateWorkflowStage(ctx, ffNs.ShipwayRegistration, stagePb.Status_INITIATED)
	if err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	actErr := activityPkg.Execute(ctx, ffNs.RegisterCreditCardTrackingDetailsAtShipway, &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityResponse{},
		&ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
			RequestHeader: &activity.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
		})
	if actErr != nil {
		lg.Error("error in RegisterCreditCardTrackingDetailsAtShipway activity", zap.Error(actErr))
	}

	workflowReqStatus = celestial.GetWorkflowStageStatusForErr(actErr)
	err = celestial.UpdateWorkflowStage(ctx, ffNs.ShipwayRegistration, workflowReqStatus)
	if err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
	}
	return workflowReqStatus, nil
}

func performPollTrackingStatusStage(ctx workflow.Context, wfProcessingParams *workflowPb.ProcessingParams) (stagePb.Status, error) {
	var (
		workflowReqStatus stagePb.Status
		clientReqId       = wfProcessingParams.GetClientReqId().GetId()
		err               error
		lg                = workflow.GetLogger(ctx)
	)

	err = celestial.InitiateWorkflowStage(ctx, ffNs.DeliveryFulfillment, stagePb.Status_INITIATED)
	if err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	actErr := activityPkg.Execute(ctx, ffNs.PollCreditCardTrackingStatus, &ffV2ActPb.PollCreditCardTrackingStatusActivityResponse{},
		&ffV2ActPb.PollCreditCardTrackingStatusActivityRequest{
			RequestHeader: &activity.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
		})
	if actErr != nil {
		lg.Error("error in PollCreditCardTrackingStatus activity", zap.Error(actErr))
	}

	workflowReqStatus = celestial.GetWorkflowStageStatusForErr(actErr)
	err = celestial.UpdateWorkflowStage(ctx, ffNs.DeliveryFulfillment, workflowReqStatus)
	if err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
	}
	return workflowReqStatus, nil
}
