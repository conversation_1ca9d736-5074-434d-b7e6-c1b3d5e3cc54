package workflow_test

import (
	"strings"
	"testing"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/google/uuid"
	errorsPkg "github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/anypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	ffNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/firefly"

	ffV2ActPb "github.com/epifi/gamma/api/firefly/v2/activity"
	ffV2WfPayload "github.com/epifi/gamma/api/firefly/v2/workflow"
	celestialActivityV2 "github.com/epifi/gamma/celestial/activity/v2"
	ffV2ActProcessor "github.com/epifi/gamma/firefly/v2/activity"
	ffV2Workflow "github.com/epifi/gamma/firefly/v2/workflow"
)

func TestProcessCreditCardsDispatchedFile(t *testing.T) {
	const (
		defaultWorkflowID = "default-test-workflow-id"
		ownership         = commontypes.Ownership_EPIFI_TECH
	)
	var (
		clientReqId                     = uuid.New().String()
		cardsDispatchedFileS3Path       = "s3://bucket/cards-dispatched.csv"
		batchSize                 int64 = 50
		wfProcessingParamsV2Req         = &activityPb.GetWorkflowProcessingParamsV2Request{
			RequestHeader: &activityPb.RequestHeader{
				Ownership: ownership,
			},
			WfReqId: defaultWorkflowID,
		}

		payload, _ = protojson.Marshal(&ffV2WfPayload.ProcessCreditCardsDispatchedFilePayload{
			CardsDispatchedFileS3Path: cardsDispatchedFileS3Path,
			CursorPosition:            0,
			BatchSize:                 batchSize,
		})
	)

	type mockGetWorkflowProcessingParamsV2 struct {
		enable bool
		res    *activityPb.GetWorkflowProcessingParamsV2Response
		err    error
	}

	type mockInitiateCreditCardTrackingWorkflows struct {
		enable bool
		req    *ffV2ActPb.InitiateCreditCardTrackingWorkflowsActivityRequest
		res    *ffV2ActPb.InitiateCreditCardTrackingWorkflowsActivityResponse
		err    error
	}

	tests := []struct {
		name                                     string
		req                                      *workflowPb.Request
		mockGetWorkflowProcessingParamsV2        mockGetWorkflowProcessingParamsV2
		mockInitiateCreditCardTrackingWorkflows0 mockInitiateCreditCardTrackingWorkflows
		wantErr                                  bool
		expectContinueAsNew                      bool
	}{
		{
			name: "successfully processed credit cards dispatched file with single batch",
			req: &workflowPb.Request{
				Payload: func() *anypb.Any {
					anyPayload, _ := anypb.New(&ffV2WfPayload.ProcessCreditCardsDispatchedFilePayload{
						CardsDispatchedFileS3Path: cardsDispatchedFileS3Path,
						CursorPosition:            0,
						BatchSize:                 batchSize,
					})
					return anyPayload
				}(),
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: payload,
					},
				},
				err: nil,
			},
			mockInitiateCreditCardTrackingWorkflows0: mockInitiateCreditCardTrackingWorkflows{
				enable: true,
				req: &ffV2ActPb.InitiateCreditCardTrackingWorkflowsActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
					CardsDispatchedFileS3Path: cardsDispatchedFileS3Path,
					CursorPosition:            0,
					BatchSize:                 batchSize,
				},
				res: &ffV2ActPb.InitiateCreditCardTrackingWorkflowsActivityResponse{
					NextCursorPosition: -1, // indicates no more records to process
				},
				err: nil,
			},
			wantErr:             false,
			expectContinueAsNew: false,
		},
		{
			name: "error in initiate credit card tracking workflows activity",
			req: &workflowPb.Request{
				Payload: func() *anypb.Any {
					anyPayload, _ := anypb.New(&ffV2WfPayload.ProcessCreditCardsDispatchedFilePayload{
						CardsDispatchedFileS3Path: cardsDispatchedFileS3Path,
						CursorPosition:            0,
						BatchSize:                 batchSize,
					})
					return anyPayload
				}(),
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: payload,
					},
				},
				err: nil,
			},
			mockInitiateCreditCardTrackingWorkflows0: mockInitiateCreditCardTrackingWorkflows{
				enable: true,
				req: &ffV2ActPb.InitiateCreditCardTrackingWorkflowsActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
					CardsDispatchedFileS3Path: cardsDispatchedFileS3Path,
					CursorPosition:            0,
					BatchSize:                 batchSize,
				},
				res: nil,
				err: errorsPkg.Wrap(epifierrors.ErrPermanent, "error"),
			},
			wantErr:             true,
			expectContinueAsNew: false,
		},
		{
			name: "error unmarshalling workflow request payload",
			req: &workflowPb.Request{
				Payload: func() *anypb.Any {
					// Invalid payload that cannot be unmarshalled
					anyPayload, _ := anypb.New(&workflowPb.ProcessingParams{})
					return anyPayload
				}(),
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: payload,
					},
				},
				err: nil,
			},
			wantErr:             true,
			expectContinueAsNew: false,
		},
		{
			name: "error in GetWorkflowProcessingParamsV2",
			req: &workflowPb.Request{
				Payload: func() *anypb.Any {
					anyPayload, _ := anypb.New(&ffV2WfPayload.ProcessCreditCardsDispatchedFilePayload{
						CardsDispatchedFileS3Path: cardsDispatchedFileS3Path,
						CursorPosition:            0,
						BatchSize:                 batchSize,
					})
					return anyPayload
				}(),
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: payload,
					},
				},
				err: errorsPkg.Wrap(epifierrors.ErrPermanent, "error"),
			},
			wantErr:             true,
			expectContinueAsNew: false,
		},
		{
			name: "processing with cursor position -1 should terminate immediately",
			req: &workflowPb.Request{
				Payload: func() *anypb.Any {
					anyPayload, _ := anypb.New(&ffV2WfPayload.ProcessCreditCardsDispatchedFilePayload{
						CardsDispatchedFileS3Path: cardsDispatchedFileS3Path,
						CursorPosition:            -1, // Already at end
						BatchSize:                 batchSize,
					})
					return anyPayload
				}(),
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: func() []byte {
							payload, _ := protojson.Marshal(&ffV2WfPayload.ProcessCreditCardsDispatchedFilePayload{
								CardsDispatchedFileS3Path: cardsDispatchedFileS3Path,
								CursorPosition:            -1,
								BatchSize:                 batchSize,
							})
							return payload
						}(),
					},
				},
				err: nil,
			},
			wantErr:             false,
			expectContinueAsNew: false,
		},
		{
			name: "processing with continue as new workflow (hits 300 iteration limit)",
			req: &workflowPb.Request{
				Payload: func() *anypb.Any {
					anyPayload, _ := anypb.New(&ffV2WfPayload.ProcessCreditCardsDispatchedFilePayload{
						CardsDispatchedFileS3Path: cardsDispatchedFileS3Path,
						CursorPosition:            0,
						BatchSize:                 batchSize,
					})
					return anyPayload
				}(),
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: payload,
					},
				},
				err: nil,
			},
			mockInitiateCreditCardTrackingWorkflows0: mockInitiateCreditCardTrackingWorkflows{
				enable: true,
				req: &ffV2ActPb.InitiateCreditCardTrackingWorkflowsActivityRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   ownership,
					},
					CardsDispatchedFileS3Path: cardsDispatchedFileS3Path,
					CursorPosition:            0,
					BatchSize:                 batchSize,
				},
				res: &ffV2ActPb.InitiateCreditCardTrackingWorkflowsActivityResponse{
					NextCursorPosition: 51, // This will keep returning positive cursor positions to trigger continue as new
				},
				err: nil,
			},
			wantErr:             false,
			expectContinueAsNew: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(ffV2Workflow.ProcessCreditCardsDispatchedFile)
			env.RegisterActivity(&celestialActivityV2.Processor{})
			env.RegisterActivity(&ffV2ActProcessor.Processor{})

			// Mock the workflow processing params activity
			if tt.mockGetWorkflowProcessingParamsV2.enable {
				env.OnActivity(string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, wfProcessingParamsV2Req).
					Return(tt.mockGetWorkflowProcessingParamsV2.res, tt.mockGetWorkflowProcessingParamsV2.err)
			}

			// Dynamic mocking based on the expected behavior from mock configurations
			if tt.mockInitiateCreditCardTrackingWorkflows0.enable {
				currentCursor := tt.mockInitiateCreditCardTrackingWorkflows0.req.CursorPosition

				// Mock the first call
				env.OnActivity(string(ffNs.InitiateCreditCardTrackingWorkflows), mock.Anything, tt.mockInitiateCreditCardTrackingWorkflows0.req).
					Return(tt.mockInitiateCreditCardTrackingWorkflows0.res, tt.mockInitiateCreditCardTrackingWorkflows0.err)

				// If the first call returns an error, don't mock further calls
				if tt.mockInitiateCreditCardTrackingWorkflows0.err != nil {
					// No need to mock further calls for error cases
				} else {

					// Update cursor for next iteration
					if tt.mockInitiateCreditCardTrackingWorkflows0.res != nil {
						currentCursor = tt.mockInitiateCreditCardTrackingWorkflows0.res.NextCursorPosition
					}

					// Continue mocking subsequent calls based on cursor position
					for i := 1; i < 300 && currentCursor != -1; i++ {
						req := &ffV2ActPb.InitiateCreditCardTrackingWorkflowsActivityRequest{
							RequestHeader: &activityPb.RequestHeader{
								ClientReqId: clientReqId,
								Ownership:   ownership,
							},
							CardsDispatchedFileS3Path: cardsDispatchedFileS3Path,
							CursorPosition:            currentCursor,
							BatchSize:                 batchSize,
						}

						var res *ffV2ActPb.InitiateCreditCardTrackingWorkflowsActivityResponse
						var err error

						// For continue-as-new scenarios, keep returning positive cursor positions
						// until we hit the 300 iteration limit
						nextCursor := currentCursor + batchSize + 1
						res = &ffV2ActPb.InitiateCreditCardTrackingWorkflowsActivityResponse{
							NextCursorPosition: nextCursor,
						}
						err = nil

						env.OnActivity(string(ffNs.InitiateCreditCardTrackingWorkflows), mock.Anything, req).
							Return(res, err)

						// If there's an error or we've reached the end, stop mocking
						if err != nil || (res != nil && res.NextCursorPosition == -1) {
							break
						}
						// Update cursor for next iteration
						currentCursor = res.NextCursorPosition

					}
				}
			}

			env.ExecuteWorkflow(ffV2Workflow.ProcessCreditCardsDispatchedFile, tt.req)
			assert.True(t, env.IsWorkflowCompleted())

			workflowErr := env.GetWorkflowError()
			// Check if this is a continue-as-new scenario
			if tt.expectContinueAsNew {
				// For continue-as-new scenarios, we expect a specific type of "error" which is actually normal behavior
				if workflowErr != nil {
					// Check if the error message contains "continue as new" - this is expected behavior
					if !strings.Contains(workflowErr.Error(), "continue as new") {
						t.Errorf("ProcessCreditCardsDispatchedFile() expected continue-as-new but got different error = %v", workflowErr)
					}
					// Continue-as-new is expected, so this is success
				} else {
					t.Errorf("ProcessCreditCardsDispatchedFile() expected continue-as-new but workflow completed normally")
				}
			} else {
				// For regular scenarios, check errors normally
				if (workflowErr != nil) != tt.wantErr {
					t.Errorf("ProcessCreditCardsDispatchedFile() error = %v, wantErr %v", workflowErr, tt.wantErr)
				}
			}
			env.AssertExpectations(t)
		})
	}
}
