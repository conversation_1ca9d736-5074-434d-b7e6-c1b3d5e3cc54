package workflow_test

import (
	"os"
	"testing"

	epifitemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/firefly/v2/test"
)

var wts epifitemporalTest.WorkflowTestSuite

func TestMain(m *testing.M) {
	_, _, _, _, _, _, teardown := test.InitTestServerV2() // nolint:dogsled
	wts.SetLogger(epifitemporalLogging.NewZapAdapter(logger.Log))
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
