package v2

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/firefly"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"

	"go.uber.org/zap"

	fireflyPkg "github.com/epifi/gamma/pkg/firefly"

	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
)

func (s *Service) GetLandingInfo(ctx context.Context, req *ffBeV2Pb.GetLandingInfoRequest) (*ffBeV2Pb.GetLandingInfoResponse, error) {
	var (
		res = &ffBeV2Pb.GetLandingInfoResponse{}
	)

	onboardingRequests, err := s.cardRequestDao.GetByActorIdAndRequestType(ctx, req.GetActorId(), ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING)
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			isUserCcEligible, chkErr := s.isUserCcEligible(ctx, req.GetActorId())
			if chkErr != nil {
				logger.Error(ctx, "error checking CC v2 eligibility for user", zap.Error(chkErr))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			res.NextAction = s.getIntroScreenV2Deeplink(ccEnumsV2Pb.CardState_CARD_STATE_UNSPECIFIED, ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_UNSPECIFIED, isUserCcEligible)
			res.Status = rpc.StatusOk()
			return res, nil
		}
		logger.Error(ctx, "error while fetching cc onboarding requests for actor", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	latestOnboardingRequest := onboardingRequests[0]
	switch latestOnboardingRequest.GetStatus() {
	case ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS:
		res.NextAction, err = s.getNextActionForSuccessfullyOnboardedUser(ctx, req.GetActorId())
		if err != nil {
			logger.Error(ctx, "error while fetching next action for successfully onboarded user",
				zap.String(logger.CARD_REQUEST_ID, latestOnboardingRequest.GetId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.Status = rpc.StatusOk()
		return res, nil
	case ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED:
		if s.isOnbRetryCoolOffApplicable(latestOnboardingRequest) {
			res.NextAction = s.getIntroScreenV2Deeplink(ccEnumsV2Pb.CardState_CARD_STATE_UNSPECIFIED, latestOnboardingRequest.GetStatus(), false)
			res.Status = rpc.StatusOk()
			return res, nil
		}
		isUserCcEligible, chkErr := s.isUserCcEligible(ctx, req.GetActorId())
		if chkErr != nil {
			logger.Error(ctx, "error checking CC v2 eligibility for user", zap.Error(chkErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.NextAction = s.getIntroScreenV2Deeplink(ccEnumsV2Pb.CardState_CARD_STATE_UNSPECIFIED, ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_UNSPECIFIED, isUserCcEligible)
		res.Status = rpc.StatusOk()
		return res, nil

	case ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS:
		res.NextAction = s.getIntroScreenV2Deeplink(ccEnumsV2Pb.CardState_CARD_STATE_UNSPECIFIED, latestOnboardingRequest.GetStatus(), true)
		res.Status = rpc.StatusOk()
		return res, nil
	default:
		logger.Error(ctx, "unexpected onboarding request status",
			zap.String(logger.STATUS, latestOnboardingRequest.GetStatus().String()),
			zap.String(logger.REQUEST_ID, latestOnboardingRequest.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
}

func (s *Service) getNextActionForSuccessfullyOnboardedUser(ctx context.Context, actorId string) (*dlPb.Deeplink, error) {
	savedCards, err := s.creditCardDao.GetByActorId(ctx, actorId)
	if err != nil && !storagev2.IsRecordNotFoundError(err) {
		return nil, errors.Wrap(err, "error while fetching credit cards for actor")
	}
	if len(savedCards) > 0 {
		for _, card := range savedCards {
			if card.GetState() == ccEnumsV2Pb.CardState_CARD_STATE_CREATED {
				return &dlPb.Deeplink{Screen: dlPb.Screen_CREDIT_CARD_DASHBOARD_SCREEN_V2}, nil
			}
		}
		return s.getIntroScreenV2Deeplink(savedCards[0].GetState(), ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS, true), nil
	}
	return nil, errors.New("no credit card found for successfully onboarded user")
}

func (s *Service) getIntroScreenV2Deeplink(cardState ccEnumsV2Pb.CardState, onbReqStatus ccEnumsV2Pb.CardRequestStatus, isUserCcEligible bool) *dlPb.Deeplink {
	metadata := &firefly.CreditCardMetadata{
		Metadata: &firefly.CreditCardMetadata_IntroScreenV2Metadata{
			IntroScreenV2Metadata: &firefly.IntroScreenV2Metadata{
				CardState:               cardState.String(),
				OnboardingRequestStatus: onbReqStatus.String(),
				IsUserCcEligible:        isUserCcEligible,
			},
		},
	}

	introScreenV2Conf := s.conf.FireflyV2Config().IntroScreenV2Config()
	loaderAnimation := common.GetVisualElementLottieFromUrlHeightAndWidth(introScreenV2Conf.IntroScreenV2LoaderAnimation(), introScreenV2Conf.IntroScreenV2LoaderAnimationHeight(), introScreenV2Conf.IntroScreenV2LoaderAnimationWidth())
	if onbReqStatus == ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED {
		// skip showing loader animation if card request is failed
		loaderAnimation = nil
	}

	screenOption := &firefly.CcIntroScreenV2ScreenOptions{
		LoaderAnimation: loaderAnimation,
		BgImage:         common.GetVisualElementImageFromUrl(introScreenV2Conf.IntroScreenV2BgImage()),
		Metadata:        fireflyPkg.GetCardMetadataString(metadata),
	}

	return &dlPb.Deeplink{
		Screen:          dlPb.Screen_CC_INTRO_SCREEN_V2,
		ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(screenOption),
	}
}

func (s *Service) isUserCcEligible(ctx context.Context, actorId string) (bool, error) {
	if s.conf.FireflyV2Config().CcIneligibleSegments().Len() == 0 {
		return true, nil
	}

	membershipResp, err := s.segmentationClient.IsMember(ctx, &segmentPb.IsMemberRequest{
		ActorId:    actorId,
		SegmentIds: s.conf.FireflyV2Config().CcIneligibleSegments().ToStringArray(),
		LatestBy:   timestamppb.Now(),
	})
	if te := epifigrpc.RPCError(membershipResp, err); te != nil {
		return false, errors.Wrap(te, "error while fetching membership info for cc ineligible segments")
	}

	for segmentId, membershipInfo := range membershipResp.GetSegmentMembershipMap() {
		if membershipInfo.GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND && membershipInfo.GetIsActorMember() {
			logger.Debug(ctx, "user is part of cc ineligible segments", zap.String(logger.SEGMENT_ID, segmentId))
			return false, nil
		}
	}
	return true, nil
}

func (s *Service) isOnbRetryCoolOffApplicable(lastFailedOnbRequest *ffBeV2Pb.CardRequest) bool {
	onbFailedAt := lastFailedOnbRequest.GetUpdatedAt()
	return timestamppb.Now().AsTime().Before(onbFailedAt.AsTime().Add(s.conf.FireflyV2Config().OnboardingRetryCoolOff()))
}
