package activity

import (
	"context"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ffV2ActPb "github.com/epifi/gamma/api/firefly/v2/activity"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	"github.com/epifi/gamma/api/vendorgateway/shipway"
)

var (
	cardRequestTerminalStates = []ccEnumsV2Pb.CardRequestStatus{
		ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS,
		ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED,
	}
)

func (p *Processor) PollCreditCardTrackingStatus(ctx context.Context, req *ffV2ActPb.PollCreditCardTrackingStatusActivityRequest) (*ffV2ActPb.PollCreditCardTrackingStatusActivityResponse, error) {
	lg := activity.GetLogger(ctx)
	orchId := req.GetRequestHeader().GetClientReqId()

	trackingRequest, err := p.cardRequestDao.GetByClientReqId(ctx, orchId)
	if err != nil {
		lg.Error("error while fetching card tracking request by client request ID",
			zap.String(logger.ORCHESTRATION_ID, orchId),
			zap.String(logger.STAGE, ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_POLL_DELIVERY_STATUS.String()),
			zap.Error(err))
		if storagev2.IsRecordNotFoundError(err) {
			return nil, epifitemporal.NewPermanentError(err)
		}
		return nil, epifitemporal.NewTransientError(err)
	}

	pollTrackingStatusInfo, err := p.getOrCreateCardRequestStageInfo(ctx, trackingRequest, ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_POLL_DELIVERY_STATUS)
	if err != nil {
		lg.Error("error while fetching shipway registration stage info", zap.Error(err), zap.String(logger.CARD_REQUEST_ID, trackingRequest.GetId()))
		return nil, epifitemporal.NewTransientError(err)
	}

	switch pollTrackingStatusInfo.GetState() {
	case ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_SUCCESS:
		return &ffV2ActPb.PollCreditCardTrackingStatusActivityResponse{}, nil
	case ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_FAILED:
		lg.Error("delivery tracking request failed",
			zap.String(logger.CARD_REQUEST_ID, trackingRequest.GetId()),
			zap.String(logger.ORCHESTRATION_ID, orchId),
			zap.String(logger.STAGE, ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_POLL_DELIVERY_STATUS.String()))
		return nil, epifitemporal.NewPermanentError(errors.New("card request failed"))
	default:
	}

	// poll delivery status from shipway
	resp, err := p.shipwayClient.GetShipmentDetails(ctx, &shipway.GetShipmentDetailsRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_SHIPWAY,
		},
		OrderId: orchId,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		lg.Error("error while fetching card tracking status",
			zap.String(logger.CLIENT_REQUEST_ID, orchId),
			zap.String(logger.CARD_REQUEST_ID, trackingRequest.GetId()),
			zap.String(logger.STAGE, ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_POLL_DELIVERY_STATUS.String()),
			zap.Error(te))
		return nil, epifitemporal.NewTransientError(te)
	}

	crState, trackingStageState, trackingState := getCardDeliveryStateAndStageStatus(resp.GetCurrentState())
	updateMasks := make([]ccEnumsV2Pb.CardRequestFieldMask, 0)

	if trackingRequest.GetRequestDetails().GetCardDeliveryTrackingDetails().GetDeliveryState() != trackingState &&
		trackingState != ccEnumsV2Pb.CardTrackingDeliveryState_CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED {
		trackingRequest.GetRequestDetails().GetCardDeliveryTrackingDetails().DeliveryState = trackingState
		updateMasks = append(updateMasks, ccEnumsV2Pb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS)
	}
	if trackingRequest.GetStatus() != crState && crState != ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_UNSPECIFIED {
		trackingRequest.Status = crState
		updateMasks = append(updateMasks, ccEnumsV2Pb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS)
	}
	if pollTrackingStatusInfo.GetState() != trackingStageState && trackingStageState != ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_UNSPECIFIED {
		pollTrackingStatusInfo.State = trackingStageState
		pollTrackingStatusInfo.LastUpdatedAt = timestamp.Now()
		trackingRequest.GetStageDetails().GetStages()[ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_POLL_DELIVERY_STATUS.String()] = pollTrackingStatusInfo
		updateMasks = append(updateMasks, ccEnumsV2Pb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STAGE_DETAILS)
	}
	if len(trackingRequest.GetRequestDetails().GetCardDeliveryTrackingDetails().GetTrackingUrl()) == 0 {
		trackingRequest.GetRequestDetails().GetCardDeliveryTrackingDetails().TrackingUrl = resp.GetTrackingUrl()
		updateMasks = append(updateMasks, ccEnumsV2Pb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS)
	}
	if resp.GetExpectedDeliveryDate().IsValid() {
		trackingRequest.GetRequestDetails().GetCardDeliveryTrackingDetails().ExpectedDeliveryDate = resp.GetExpectedDeliveryDate()
		updateMasks = append(updateMasks, ccEnumsV2Pb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS)
	}

	updateMasks = lo.Uniq(updateMasks)
	updErr := p.cardRequestDao.UpdateIfUpdatedAtMatches(ctx, trackingRequest, updateMasks)
	if updErr != nil {
		lg.Error("error while updating card tracking request",
			zap.String(logger.CARD_REQUEST_ID, trackingRequest.GetId()),
			zap.String(logger.ORCHESTRATION_ID, orchId),
			zap.String(logger.STAGE, ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_POLL_DELIVERY_STATUS.String()),
			zap.Error(updErr))
		return nil, epifitemporal.NewTransientError(updErr)
	}

	if lo.Contains(cardRequestTerminalStates, trackingRequest.GetStatus()) {
		lg.Info("card delivery request moved to terminal state",
			zap.String(logger.CARD_REQUEST_ID, trackingRequest.GetId()),
			zap.String(logger.ORCHESTRATION_ID, orchId),
			zap.String(logger.STAGE, ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_POLL_DELIVERY_STATUS.String()),
			zap.String(logger.STATUS, trackingRequest.GetStatus().String()))
		return &ffV2ActPb.PollCreditCardTrackingStatusActivityResponse{}, nil
	}
	return nil, epifitemporal.NewTransientError(errors.New("delivery tracking is in progress"))
}

func (p *Processor) getOrCreateCardRequestStageInfo(ctx context.Context, cardRequest *ffBeV2Pb.CardRequest, stageName ccEnumsV2Pb.CardRequestStage) (*ffBeV2Pb.StageInfo, error) {
	if cardRequest == nil {
		return nil, errors.New("card request is nil")
	}
	stageInfo, isPresent := cardRequest.GetStageDetails().GetStages()[stageName.String()]
	if isPresent {
		return stageInfo, nil
	}

	if cardRequest.GetStageDetails() == nil {
		cardRequest.StageDetails = &ffBeV2Pb.CardRequestStageDetails{
			Stages: make(map[string]*ffBeV2Pb.StageInfo),
		}
	}

	cardRequest.GetStageDetails().GetStages()[stageName.String()] = &ffBeV2Pb.StageInfo{
		State:         ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED,
		LastUpdatedAt: timestamp.Now(),
		StartedAt:     timestamp.Now(),
	}
	err := p.cardRequestDao.UpdateIfUpdatedAtMatches(ctx, cardRequest, []ccEnumsV2Pb.CardRequestFieldMask{ccEnumsV2Pb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STAGE_DETAILS})
	if err != nil {
		return nil, errors.Wrap(err, "failed to update cardRequest stage details")
	}

	return cardRequest.GetStageDetails().GetStages()[stageName.String()], nil
}
func getCardDeliveryStateAndStageStatus(vgDeliveryState shipway.ShipmentState) (ccEnumsV2Pb.CardRequestStatus, ccEnumsV2Pb.CardRequestStageState, ccEnumsV2Pb.CardTrackingDeliveryState) {
	switch vgDeliveryState {
	case shipway.ShipmentState_DELIVERED:
		return ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS, ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_SUCCESS, ccEnumsV2Pb.CardTrackingDeliveryState_DELIVERED
	case shipway.ShipmentState_OUT_FOR_DELIVERY:
		return ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS, ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED, ccEnumsV2Pb.CardTrackingDeliveryState_OUT_FOR_DELIVERY
	case shipway.ShipmentState_IN_TRANSIT:
		return ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS, ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED, ccEnumsV2Pb.CardTrackingDeliveryState_IN_TRANSIT
	case shipway.ShipmentState_UNDELIVERED:
		return ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS, ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED, ccEnumsV2Pb.CardTrackingDeliveryState_CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED
	case shipway.ShipmentState_RETURN_TO_ORIGIN:
		return ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED, ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_FAILED, ccEnumsV2Pb.CardTrackingDeliveryState_RETURNED_TO_ORIGIN
	case shipway.ShipmentState_RETURN_TO_ORIGIN_DELIVERED:
		return ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED, ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_FAILED, ccEnumsV2Pb.CardTrackingDeliveryState_RETURNED_TO_ORIGIN
	case shipway.ShipmentState_CONSIGNEE_NOT_AVAILABLE:
		return ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS, ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED, ccEnumsV2Pb.CardTrackingDeliveryState_CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED
	}
	return ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS, ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_UNSPECIFIED, ccEnumsV2Pb.CardTrackingDeliveryState_CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED
}
