package activity

import (
	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/aws/v2/s3"

	actorPb "github.com/epifi/gamma/api/actor"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/shipway"
	"github.com/epifi/gamma/firefly/v2/dao"
)

type Processor struct {
	cardRequestDao    dao.CardRequestDao
	ccDao             dao.CreditCardDao
	shipwayClient     shipway.ShipwayClient
	ccTrackingInfoDao dao.CardTrackingInfoDao
	ccDocsClient      s3.S3Client
	userClient        userPb.UsersClient
	actorClient       actorPb.ActorClient
	celestialClient   celestialPb.CelestialClient
}

func NewProcessor(cardRequestDao dao.CardRequestDao, ccDao dao.CreditCardDao, ccTrackingInfoDao dao.CardTrackingInfoDao,
	shipwayClient shipway.ShipwayClient, ccDocsClient s3.S3Client, userClient userPb.UsersClient,
	actorClient actorPb.ActorClient, celestialClient celestialPb.CelestialClient) *Processor {
	return &Processor{
		cardRequestDao:    cardRequestDao,
		ccDao:             ccDao,
		shipwayClient:     shipwayClient,
		ccTrackingInfoDao: ccTrackingInfoDao,
		ccDocsClient:      ccDocsClient,
		userClient:        userClient,
		actorClient:       actorClient,
		celestialClient:   celestialClient,
	}
}
