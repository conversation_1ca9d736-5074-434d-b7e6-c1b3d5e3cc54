package activity

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"strings"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	ffNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/firefly"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	actorPb "github.com/epifi/gamma/api/actor"
	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ffV2ActPb "github.com/epifi/gamma/api/firefly/v2/activity"
	typesV2 "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
)

type TrackingDetails struct {
	PhoneNumber      string
	MaskedCardNumber string
	Carrier          string
	AWBNumber        string
}

var (
	columnSeparator rune = '|'

	// 19 columns
	// empty column names are the one which we don't need to validate as they don't have any dependency on the business logic
	csvColumnsInOrder = []string{
		"", "", "", "", "Ud_CardNo",
		"", "", "", "", "",
		"", "", "MobileNumber", "Ud_Courier", "UD_AwbNo",
		"", "", "", ""}
)

func (p *Processor) InitiateCreditCardTrackingWorkflows(ctx context.Context, req *ffV2ActPb.InitiateCreditCardTrackingWorkflowsActivityRequest) (*ffV2ActPb.InitiateCreditCardTrackingWorkflowsActivityResponse, error) {
	// fetch data from s3
	lg := activity.GetLogger(ctx)
	fileData, err := p.readAndParseCsvFileData(ctx, req.GetCardsDispatchedFileS3Path())
	if err != nil {
		lg.Error("Failed to read and parse csv file",
			zap.String(logger.ORCHESTRATION_ID, req.GetRequestHeader().GetClientReqId()),
			zap.String(logger.S3_PATH, req.GetCardsDispatchedFileS3Path()),
			zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	idx := int64(0)
	if activity.HasHeartbeatDetails(ctx) {
		// we will retry from a failed attempt if there is reported progress that we should resume from.
		var completedIdx int64
		if err = activity.GetHeartbeatDetails(ctx, &completedIdx); err == nil {
			idx = completedIdx + 1
			lg.Info("Resuming from failed attempt",
				zap.String(logger.ORCHESTRATION_ID, req.GetRequestHeader().GetClientReqId()),
				zap.String(logger.S3_PATH, req.GetCardsDispatchedFileS3Path()),
				zap.Int64(logger.ARRAY_INDEX, idx))
		} else {
			lg.Error("failed to get heartbeat details",
				zap.String(logger.ORCHESTRATION_ID, req.GetRequestHeader().GetClientReqId()),
				zap.String(logger.S3_PATH, req.GetCardsDispatchedFileS3Path()),
				zap.Error(err))
			return nil, epifitemporal.NewTransientError(errors.New("failed to get heartbeat details"))
		}
	}
	for ; idx < req.GetBatchSize(); idx++ {
		if req.GetCursorPosition()+idx >= int64(len(fileData)) {
			lg.Info("All records from batch have been processed",
				zap.String(logger.ORCHESTRATION_ID, req.GetRequestHeader().GetClientReqId()),
				zap.String(logger.S3_PATH, req.GetCardsDispatchedFileS3Path()),
				zap.Int64("LastRecordIdx", req.GetCursorPosition()+idx-1))
			break
		}
		processErr := p.processCardsDispatchedCsvFileRow(ctx, fileData[req.GetCursorPosition()+idx])
		if processErr != nil {
			lg.Error("Failed to process row",
				zap.String(logger.ORCHESTRATION_ID, req.GetRequestHeader().GetClientReqId()),
				zap.Int64(logger.ARRAY_INDEX, req.GetCursorPosition()+idx),
				zap.Error(processErr))
			return nil, epifitemporal.NewTransientError(processErr)
		}
		// Record activity heartbeat
		activity.RecordHeartbeat(ctx, idx)
	}

	return &ffV2ActPb.InitiateCreditCardTrackingWorkflowsActivityResponse{
		NextCursorPosition: int64(lo.Ternary(len(fileData) <= int(req.GetCursorPosition()+req.GetBatchSize()), -1, int(req.GetCursorPosition()+req.GetBatchSize()))),
	}, nil
}

func (p *Processor) readAndParseCsvFileData(ctx context.Context, s3RecordPath string) ([]*TrackingDetails, error) {
	lg := activity.GetLogger(ctx)

	// File extension check, expecting only csv file
	filePathParts := strings.Split(s3RecordPath, ".")
	if len(filePathParts) > 1 && filePathParts[len(filePathParts)-1] != "csv" {
		lg.Info("readAndParseCsvFileData: skipping non csv files", zap.String(logger.PATH, s3RecordPath))
		return nil, nil
	}

	// Read csv file from s3 bucket
	csvFileContents, readError := p.ccDocsClient.Read(ctx, s3RecordPath)
	if readError != nil {
		return nil, errors.Wrap(readError, "error while reading csv file from S3")
	}

	var trackingDetailsList []*TrackingDetails
	reader := csv.NewReader(bytes.NewBuffer(csvFileContents))
	reader.Comma = columnSeparator
	allRecords, err := reader.ReadAll()
	if err != nil {
		return nil, errors.Wrap(err, "error reading file data from csv file buffer")
	}
	if len(allRecords) == 0 {
		return nil, errors.New("empty file uploaded")
	}

	// making column validation as best effort, since some time file sent by vendor,
	// might not have the column names in the first row of csv file as we are expecting,
	// so to avoid such failure we will try to validate column with first record,
	// in case of any failures we will assume that the file is in correct format but does not have column name and
	// will start processing tracking data from the first record itself,
	// else if validation is successful we will continue processing tracking data from 2nd row onward
	firstRecord := allRecords[0]
	if len(firstRecord) < len(csvColumnsInOrder) {
		return nil, errors.New(fmt.Sprintf("unexpected number of columns: %v", len(firstRecord)))
	}
	status := validateColumnNames(ctx, firstRecord, csvColumnsInOrder)
	if !status.IsSuccess() {
		trackingDetails := &TrackingDetails{
			PhoneNumber:      strings.TrimSpace(firstRecord[12]),
			MaskedCardNumber: strings.TrimSpace(firstRecord[4]),
			Carrier:          strings.TrimSpace(firstRecord[13]),
			AWBNumber:        strings.TrimSpace(firstRecord[14]),
		}
		err = validateTrackingData(trackingDetails)
		if err != nil {
			lg.Error("tracking data validation failed",
				zap.Int(logger.ARRAY_INDEX, 0),
				zap.Error(err))
		} else {
			trackingDetailsList = append(trackingDetailsList, trackingDetails)
		}
	}

	// starting from index 1 since we already processed first record
	for idx := 1; idx < len(allRecords); idx++ {
		trackingDetails := &TrackingDetails{
			PhoneNumber:      strings.TrimSpace(allRecords[idx][12]),
			MaskedCardNumber: strings.TrimSpace(allRecords[idx][4]),
			Carrier:          strings.TrimSpace(allRecords[idx][13]),
			AWBNumber:        strings.TrimSpace(allRecords[idx][14]),
		}
		err = validateTrackingData(trackingDetails)
		if err != nil {
			lg.Error("tracking data validation failed",
				zap.Int(logger.ARRAY_INDEX, idx),
				zap.Error(err))
		} else {
			trackingDetailsList = append(trackingDetailsList, trackingDetails)
		}
	}

	return trackingDetailsList, nil
}

func (p *Processor) processCardsDispatchedCsvFileRow(ctx context.Context, details *TrackingDetails) error {
	// 1. get matching credit card
	savedCreditCard, err := p.getMatchingCreditCardForPhNo(ctx, details.PhoneNumber)
	if err != nil {
		return errors.Wrap(err, "failed to get credit card for phone number")
	}

	// 2. create tracking info and initiate tracking workflow
	trackingInfo, err := p.ccTrackingInfoDao.Create(ctx, &ffBeV2Pb.CardTrackingInfo{
		Awb:            details.AWBNumber,
		PrintingVendor: commonvgpb.Vendor_MCT,
		ActorId:        savedCreditCard.GetActorId(),
		Carrier:        details.Carrier,
	})
	if err != nil && !storagev2.IsDuplicateEntryError(err) {
		return errors.Wrap(err, "failed to create card tracking info")
	}

	if trackingInfo == nil {
		trackingInfo, err = p.ccTrackingInfoDao.GetByActorAwbPrintingVendor(ctx, savedCreditCard.GetActorId(), details.AWBNumber, commonvgpb.Vendor_MCT)
		if err != nil {
			return errors.Wrap(err, "failed to get card tracking info")
		}
	}

	// 3. initiate card tracking workflow
	initResp, err := p.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: savedCreditCard.GetActorId(),
			Version: workflowPb.Version_V0,
			Type:    celestial.GetTypeEnumFromWorkflowType(ffNs.PerformCreditCardDeliveryTracking),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     trackingInfo.GetId(),
				Client: workflowPb.Client_FIREFLY,
			},
			Ownership:        commontypes.Ownership_EPIFI_TECH,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})
	if te := epifigrpc.RPCError(initResp, err); te != nil && !initResp.GetStatus().IsAlreadyExists() {
		return errors.Wrap(te, "initiate workflow failed")
	}

	return nil
}

func (p *Processor) getMatchingCreditCardForPhNo(ctx context.Context, number string) (*ffBeV2Pb.CreditCard, error) {
	phNumber, err := commontypes.ParsePhoneNumber(number)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse phone number")
	}
	getUserRes, err := p.userClient.GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_PhoneNumber{
		PhoneNumber: phNumber,
	}})
	if te := epifigrpc.RPCError(getUserRes, err); te != nil {
		return nil, errors.Wrap(te, "error while fetching user with phone number")
	}
	userId := getUserRes.GetUser().GetId()

	getActorRes, err := p.actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     typesV2.Actor_USER,
		EntityId: userId,
	})
	if te := epifigrpc.RPCError(getActorRes, err); te != nil {
		return nil, errors.Wrap(te, "error while fetching actor from entity id")
	}

	savedCards, err := p.ccDao.GetByActorId(ctx, getActorRes.GetActor().GetId())
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("failed to get saved credit cards for actorId: %s", getActorRes.GetActor().GetId()))
	}
	// return the latest credit card by default
	return savedCards[0], nil
}

// validateColumnNames checks if the columns name are as expected and are in the same order
// we will skip validations for columns which are not part of the business logic
func validateColumnNames(ctx context.Context, uploadedFileColumnsInOrder []string, columnsInOrder []string) *rpc.Status {
	lg := activity.GetLogger(ctx)
	for index, columnName := range columnsInOrder {
		if columnName != "" && strings.TrimSpace(uploadedFileColumnsInOrder[index]) != columnName {
			lg.Debug(fmt.Sprintf("csv column name %s format does not match expected one %s",
				strings.TrimSpace(uploadedFileColumnsInOrder[index]), columnName))
			return rpc.StatusInternal()
		}
	}
	return rpc.StatusOk()
}

// validateTrackingData checks if all fields are present as expected in the csv file
func validateTrackingData(trackingDetails *TrackingDetails) error {
	// NOTE : Phone number will be of the format : 919090909090
	phoneNumber := trackingDetails.PhoneNumber
	if len(phoneNumber) != 12 {
		return errors.New(fmt.Sprintf("invalid phone number length %v awb %v\n", len(trackingDetails.PhoneNumber), trackingDetails.AWBNumber))
	}
	if len(trackingDetails.MaskedCardNumber) == 0 || len(trackingDetails.AWBNumber) == 0 || len(trackingDetails.Carrier) == 0 {
		return errors.New(fmt.Sprintf("invalid tracking details, maskedCardNumberLength %v carrier %v awb %v\n",
			len(trackingDetails.MaskedCardNumber), trackingDetails.Carrier, trackingDetails.AWBNumber))
	}
	_, err := commontypes.ParsePhoneNumber(phoneNumber)
	if err != nil {
		return errors.New(fmt.Sprintf("failed to parse phone number for awb %v err %v\n", trackingDetails.AWBNumber, err))
	}
	return nil
}
