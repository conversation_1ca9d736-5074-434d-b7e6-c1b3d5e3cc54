package activity_test

import (
	"errors"
	"testing"
	"time"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifitemporal"
	ffNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/firefly"

	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ffV2ActPb "github.com/epifi/gamma/api/firefly/v2/activity"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	"github.com/epifi/gamma/api/vendorgateway/shipway"
)

func TestProcessor_RegisterCreditCardTrackingDetailsAtShipway(t *testing.T) {
	now := time.Now()
	tests := []struct {
		name           string
		req            *ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest
		want           *ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityResponse
		wantErr        bool
		isRetryableErr bool
		setupMocks     func(md *mockDependencies)
	}{
		{
			name: "success - stage already successful",
			req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
				RequestHeader: &activityPb.RequestHeader{ClientReqId: "orch-id-1"},
			},
			want:           &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityResponse{},
			wantErr:        false,
			isRetryableErr: false,
			setupMocks: func(md *mockDependencies) {
				md.cardRequestDao.EXPECT().GetByClientReqId(gomock.Any(), "orch-id-1").
					Return(&ffBeV2Pb.CardRequest{
						Id:        "card-req-1",
						ActorId:   "actor-id-1",
						Type:      ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_DELIVERY_TRACKING,
						Status:    ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
						UpdatedAt: timestamppb.New(now),
						RequestDetails: &ffBeV2Pb.CardRequestDetails{
							Data: &ffBeV2Pb.CardRequestDetails_CardDeliveryTrackingDetails{
								CardDeliveryTrackingDetails: &ffBeV2Pb.CardDeliveryTrackingDetails{
									OrderId: "orch-id-1",
									Awb:     "AWB123456",
									Carrier: "BLUEDART",
								},
							},
						},
						StageDetails: &ffBeV2Pb.CardRequestStageDetails{
							Stages: map[string]*ffBeV2Pb.StageInfo{
								ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_REGISTER_TRACKING_DETAILS_AT_SHIPWAY.String(): &ffBeV2Pb.StageInfo{
									State:         ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_SUCCESS,
									LastUpdatedAt: timestamppb.New(now),
									StartedAt:     timestamppb.New(now),
								},
							},
						},
						ClientReqId: "orch-id-1",
					}, nil)
			},
		},
		{
			name: "error - stage already failed",
			req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
				RequestHeader: &activityPb.RequestHeader{ClientReqId: "orch-id-2"},
			},
			want:           nil,
			wantErr:        true,
			isRetryableErr: false,
			setupMocks: func(md *mockDependencies) {
				md.cardRequestDao.EXPECT().GetByClientReqId(gomock.Any(), "orch-id-2").
					Return(&ffBeV2Pb.CardRequest{
						Id:        "card-req-2",
						ActorId:   "actor-id-2",
						Type:      ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_DELIVERY_TRACKING,
						Status:    ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
						UpdatedAt: timestamppb.New(now),
						RequestDetails: &ffBeV2Pb.CardRequestDetails{
							Data: &ffBeV2Pb.CardRequestDetails_CardDeliveryTrackingDetails{
								CardDeliveryTrackingDetails: &ffBeV2Pb.CardDeliveryTrackingDetails{
									OrderId: "orch-id-2",
									Awb:     "AWB123456",
									Carrier: "BLUEDART",
								},
							},
						},
						StageDetails: &ffBeV2Pb.CardRequestStageDetails{
							Stages: map[string]*ffBeV2Pb.StageInfo{
								ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_REGISTER_TRACKING_DETAILS_AT_SHIPWAY.String(): &ffBeV2Pb.StageInfo{
									State:         ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_FAILED,
									LastUpdatedAt: timestamppb.New(now),
									StartedAt:     timestamppb.New(now),
								},
							},
						},
						ClientReqId: "orch-id-2",
					}, nil)
			},
		},
		{
			name: "success - upload shipment data successfully",
			req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
				RequestHeader: &activityPb.RequestHeader{ClientReqId: "orch-id-3"},
			},
			want:           &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityResponse{},
			wantErr:        false,
			isRetryableErr: false,
			setupMocks: func(md *mockDependencies) {
				md.cardRequestDao.EXPECT().GetByClientReqId(gomock.Any(), "orch-id-3").
					Return(&ffBeV2Pb.CardRequest{
						Id:        "card-req-3",
						ActorId:   "actor-id-3",
						Type:      ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_DELIVERY_TRACKING,
						Status:    ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
						UpdatedAt: timestamppb.New(now),
						RequestDetails: &ffBeV2Pb.CardRequestDetails{
							Data: &ffBeV2Pb.CardRequestDetails_CardDeliveryTrackingDetails{
								CardDeliveryTrackingDetails: &ffBeV2Pb.CardDeliveryTrackingDetails{
									OrderId: "orch-id-3",
									Awb:     "AWB123456",
									Carrier: "BLUEDART",
								},
							},
						},
						StageDetails: &ffBeV2Pb.CardRequestStageDetails{
							Stages: map[string]*ffBeV2Pb.StageInfo{
								ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_REGISTER_TRACKING_DETAILS_AT_SHIPWAY.String(): &ffBeV2Pb.StageInfo{
									State:         ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED,
									LastUpdatedAt: timestamppb.New(now),
									StartedAt:     timestamppb.New(now),
								},
							},
						},
						ClientReqId: "orch-id-3",
					}, nil)

				md.shipwayClient.EXPECT().UploadShipmentData(gomock.Any(), &shipway.UploadShipmentDataRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_SHIPWAY,
					},
					Carrier: "BLUEDART",
					Awb:     "AWB123456",
					OrderId: "orch-id-3",
				}).Return(&shipway.UploadShipmentDataResponse{
					Status: rpc.StatusOk(),
				}, nil)

				md.cardRequestDao.EXPECT().UpdateIfUpdatedAtMatches(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)
			},
		},
		{
			name: "success - shipment data already exists",
			req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
				RequestHeader: &activityPb.RequestHeader{ClientReqId: "orch-id-4"},
			},
			want:           &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityResponse{},
			wantErr:        false,
			isRetryableErr: false,
			setupMocks: func(md *mockDependencies) {
				md.cardRequestDao.EXPECT().GetByClientReqId(gomock.Any(), "orch-id-4").
					Return(&ffBeV2Pb.CardRequest{
						Id:        "card-req-2",
						ActorId:   "actor-id-2",
						Type:      ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_DELIVERY_TRACKING,
						Status:    ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
						UpdatedAt: timestamppb.New(now),
						RequestDetails: &ffBeV2Pb.CardRequestDetails{
							Data: &ffBeV2Pb.CardRequestDetails_CardDeliveryTrackingDetails{
								CardDeliveryTrackingDetails: &ffBeV2Pb.CardDeliveryTrackingDetails{
									OrderId: "orch-id-4",
									Awb:     "AWB123456",
									Carrier: "BLUEDART",
								},
							},
						},
						StageDetails: &ffBeV2Pb.CardRequestStageDetails{
							Stages: map[string]*ffBeV2Pb.StageInfo{
								ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_REGISTER_TRACKING_DETAILS_AT_SHIPWAY.String(): &ffBeV2Pb.StageInfo{
									State:         ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED,
									LastUpdatedAt: timestamppb.New(now),
									StartedAt:     timestamppb.New(now),
								},
							},
						},
						ClientReqId: "orch-id-4",
					}, nil)

				md.shipwayClient.EXPECT().UploadShipmentData(gomock.Any(), &shipway.UploadShipmentDataRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_SHIPWAY,
					},
					Carrier: "BLUEDART",
					Awb:     "AWB123456",
					OrderId: "orch-id-4",
				}).Return(&shipway.UploadShipmentDataResponse{
					Status: rpc.StatusAlreadyExists(),
				}, nil)

				md.cardRequestDao.EXPECT().UpdateIfUpdatedAtMatches(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)
			},
		},
		{
			name: "success - create new stage info",
			req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
				RequestHeader: &activityPb.RequestHeader{ClientReqId: "orch-id-5"},
			},
			want:           &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityResponse{},
			wantErr:        false,
			isRetryableErr: false,
			setupMocks: func(md *mockDependencies) {
				md.cardRequestDao.EXPECT().GetByClientReqId(gomock.Any(), "orch-id-5").
					Return(&ffBeV2Pb.CardRequest{
						Id:        "card-req-2",
						ActorId:   "actor-id-2",
						Type:      ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_DELIVERY_TRACKING,
						Status:    ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
						UpdatedAt: timestamppb.New(now),
						RequestDetails: &ffBeV2Pb.CardRequestDetails{
							Data: &ffBeV2Pb.CardRequestDetails_CardDeliveryTrackingDetails{
								CardDeliveryTrackingDetails: &ffBeV2Pb.CardDeliveryTrackingDetails{
									OrderId: "orch-id-5",
									Awb:     "AWB123456",
									Carrier: "BLUEDART",
								},
							},
						},
						StageDetails: &ffBeV2Pb.CardRequestStageDetails{
							Stages: make(map[string]*ffBeV2Pb.StageInfo),
						},
						ClientReqId: "orch-id-5",
					}, nil)

				// First update to create stage info
				md.cardRequestDao.EXPECT().UpdateIfUpdatedAtMatches(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)

				md.shipwayClient.EXPECT().UploadShipmentData(gomock.Any(), &shipway.UploadShipmentDataRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_SHIPWAY,
					},
					Carrier: "BLUEDART",
					Awb:     "AWB123456",
					OrderId: "orch-id-5",
				}).Return(&shipway.UploadShipmentDataResponse{
					Status: rpc.StatusOk(),
				}, nil)

				// Second update after shipway call
				md.cardRequestDao.EXPECT().UpdateIfUpdatedAtMatches(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)
			},
		},
		{
			name: "error - tracking request not found",
			req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
				RequestHeader: &activityPb.RequestHeader{ClientReqId: "orch-id-6"},
			},
			want:           nil,
			wantErr:        true,
			isRetryableErr: false,
			setupMocks: func(md *mockDependencies) {
				md.cardRequestDao.EXPECT().GetByClientReqId(gomock.Any(), "orch-id-6").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
		},
		{
			name: "error - tracking request dao error",
			req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
				RequestHeader: &activityPb.RequestHeader{ClientReqId: "orch-id-7"},
			},
			want:           nil,
			wantErr:        true,
			isRetryableErr: true,
			setupMocks: func(md *mockDependencies) {
				md.cardRequestDao.EXPECT().GetByClientReqId(gomock.Any(), "orch-id-7").
					Return(nil, errors.New("db error"))
			},
		},
		{
			name: "error - shipway upload error",
			req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
				RequestHeader: &activityPb.RequestHeader{ClientReqId: "orch-id-8"},
			},
			want:           nil,
			wantErr:        true,
			isRetryableErr: true,
			setupMocks: func(md *mockDependencies) {
				md.cardRequestDao.EXPECT().GetByClientReqId(gomock.Any(), "orch-id-8").
					Return(&ffBeV2Pb.CardRequest{
						Id:        "card-req-8",
						ActorId:   "actor-id-8",
						Type:      ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_DELIVERY_TRACKING,
						Status:    ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
						UpdatedAt: timestamppb.New(now),
						RequestDetails: &ffBeV2Pb.CardRequestDetails{
							Data: &ffBeV2Pb.CardRequestDetails_CardDeliveryTrackingDetails{
								CardDeliveryTrackingDetails: &ffBeV2Pb.CardDeliveryTrackingDetails{
									OrderId: "orch-id-8",
									Awb:     "AWB123456",
									Carrier: "BLUEDART",
								},
							},
						},
						StageDetails: &ffBeV2Pb.CardRequestStageDetails{
							Stages: map[string]*ffBeV2Pb.StageInfo{
								ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_REGISTER_TRACKING_DETAILS_AT_SHIPWAY.String(): &ffBeV2Pb.StageInfo{
									State:         ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED,
									LastUpdatedAt: timestamppb.New(now),
									StartedAt:     timestamppb.New(now),
								},
							},
						},
						ClientReqId: "orch-id-8",
					}, nil)

				md.shipwayClient.EXPECT().UploadShipmentData(gomock.Any(), &shipway.UploadShipmentDataRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_SHIPWAY,
					},
					Carrier: "BLUEDART",
					Awb:     "AWB123456",
					OrderId: "orch-id-8",
				}).Return(&shipway.UploadShipmentDataResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
		},
		{
			name: "error - stage creation update error",
			req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
				RequestHeader: &activityPb.RequestHeader{ClientReqId: "orch-id-9"},
			},
			want:           nil,
			wantErr:        true,
			isRetryableErr: true,
			setupMocks: func(md *mockDependencies) {
				md.cardRequestDao.EXPECT().GetByClientReqId(gomock.Any(), "orch-id-9").
					Return(&ffBeV2Pb.CardRequest{
						Id:        "card-req-2",
						ActorId:   "actor-id-2",
						Type:      ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_DELIVERY_TRACKING,
						Status:    ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
						UpdatedAt: timestamppb.New(now),
						RequestDetails: &ffBeV2Pb.CardRequestDetails{
							Data: &ffBeV2Pb.CardRequestDetails_CardDeliveryTrackingDetails{
								CardDeliveryTrackingDetails: &ffBeV2Pb.CardDeliveryTrackingDetails{
									OrderId: "orch-id-9",
									Awb:     "AWB123456",
									Carrier: "BLUEDART",
								},
							},
						},
						StageDetails: &ffBeV2Pb.CardRequestStageDetails{
							Stages: make(map[string]*ffBeV2Pb.StageInfo),
						},
						ClientReqId: "orch-id-9",
					}, nil)

				// First update to create stage info - error
				md.cardRequestDao.EXPECT().UpdateIfUpdatedAtMatches(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(errors.New("db error"))
			},
		},
		{
			name: "error - final update error",
			req: &ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityRequest{
				RequestHeader: &activityPb.RequestHeader{ClientReqId: "orch-id-10"},
			},
			want:           nil,
			wantErr:        true,
			isRetryableErr: true,
			setupMocks: func(md *mockDependencies) {
				md.cardRequestDao.EXPECT().GetByClientReqId(gomock.Any(), "orch-id-10").
					Return(&ffBeV2Pb.CardRequest{
						Id:        "card-req-10",
						ActorId:   "actor-id-10",
						Type:      ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_DELIVERY_TRACKING,
						Status:    ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
						UpdatedAt: timestamppb.New(now),
						RequestDetails: &ffBeV2Pb.CardRequestDetails{
							Data: &ffBeV2Pb.CardRequestDetails_CardDeliveryTrackingDetails{
								CardDeliveryTrackingDetails: &ffBeV2Pb.CardDeliveryTrackingDetails{
									OrderId: "orch-id-10",
									Awb:     "AWB123456",
									Carrier: "BLUEDART",
								},
							},
						},
						StageDetails: &ffBeV2Pb.CardRequestStageDetails{
							Stages: map[string]*ffBeV2Pb.StageInfo{
								ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_REGISTER_TRACKING_DETAILS_AT_SHIPWAY.String(): &ffBeV2Pb.StageInfo{
									State:         ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED,
									LastUpdatedAt: timestamppb.New(now),
									StartedAt:     timestamppb.New(now),
								},
							},
						},
						ClientReqId: "orch-id-10",
					}, nil)

				md.shipwayClient.EXPECT().UploadShipmentData(gomock.Any(), &shipway.UploadShipmentDataRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_SHIPWAY,
					},
					Carrier: "BLUEDART",
					Awb:     "AWB123456",
					OrderId: "orch-id-10",
				}).Return(&shipway.UploadShipmentDataResponse{
					Status: rpc.StatusOk(),
				}, nil)

				md.cardRequestDao.EXPECT().UpdateIfUpdatedAtMatches(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(errors.New("db error"))
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			p, md, assertTest := getFireflyV2ActProcessorWithMocks(t)
			defer assertTest()

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(md)
			var result *ffV2ActPb.RegisterCreditCardTrackingDetailsAtShipwayActivityResponse
			got, err := env.ExecuteActivity(ffNs.RegisterCreditCardTrackingDetailsAtShipway, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("RegisterCreditCardTrackingDetailsAtShipway() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("RegisterCreditCardTrackingDetailsAtShipway() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr:
				if epifitemporal.IsRetryableError(err) != tt.isRetryableErr {
					t.Errorf("RegisterCreditCardTrackingDetailsAtShipway() retryable mismatch error = %v, isRetryable %v", err, tt.isRetryableErr)
				}
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("RegisterCreditCardTrackingDetailsAtShipway() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
