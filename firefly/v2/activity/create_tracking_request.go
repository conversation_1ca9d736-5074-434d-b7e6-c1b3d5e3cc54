package activity

import (
	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ffV2ActPb "github.com/epifi/gamma/api/firefly/v2/activity"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
)

func (p *Processor) CreateCreditCardTrackingRequest(ctx context.Context, req *ffV2ActPb.CreateCreditCardTrackingRequestActivityRequest) (*ffV2ActPb.CreateCreditCardTrackingRequestActivityResponse, error) {
	lg := activity.GetLogger(ctx)
	orchId := req.GetRequestHeader().GetClientReqId()
	// fetch tracking request using client requestID
	trackingRequest, err := p.cardRequestDao.GetByClientReqId(ctx, orchId)
	if err != nil && !storagev2.IsRecordNotFoundError(err) {
		lg.Error("error while fetching card tracking request by client request ID", zap.Error(err),
			zap.String(logger.ORCHESTRATION_ID, orchId),
			zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}
	if trackingRequest != nil {
		lg.Info("credit card tracking request already exists",
			zap.String(logger.CARD_REQUEST_ID, trackingRequest.GetId()),
			zap.String(logger.ORCHESTRATION_ID, orchId))
		return &ffV2ActPb.CreateCreditCardTrackingRequestActivityResponse{}, nil
	}

	// fetch tracking info
	trackingInfo, err := p.ccTrackingInfoDao.GetById(ctx, orchId)
	if err != nil {
		lg.Error("error while fetching card tracking info by client request ID",
			zap.String(logger.ORCHESTRATION_ID, orchId), zap.Error(err))
		if storagev2.IsRecordNotFoundError(err) {
			return nil, epifitemporal.NewPermanentError(err)
		}
		return nil, epifitemporal.NewTransientError(err)
	}

	extUserId, err := p.getExternalUserIdForActorId(ctx, trackingInfo.GetActorId())
	if err != nil {
		lg.Error("error while fetching external user id",
			zap.String(logger.ORCHESTRATION_ID, orchId),
			zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	// create card tracking request
	_, err = p.cardRequestDao.Create(ctx, &ffBeV2Pb.CardRequest{
		ActorId: trackingInfo.GetActorId(),
		Type:    ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_DELIVERY_TRACKING,
		Status:  ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
		RequestDetails: &ffBeV2Pb.CardRequestDetails{
			Data: &ffBeV2Pb.CardRequestDetails_CardDeliveryTrackingDetails{
				CardDeliveryTrackingDetails: &ffBeV2Pb.CardDeliveryTrackingDetails{
					OrderId:        orchId,
					Awb:            trackingInfo.GetAwb(),
					Carrier:        trackingInfo.GetCarrier(),
					PrintingVendor: trackingInfo.GetPrintingVendor(),
				},
			},
		},
		Vendor:         commonvgpb.Vendor_SAVEN,
		ExternalUserId: extUserId,
		ClientReqId:    orchId,
	})
	if err != nil {
		lg.Error("error while creating card tracking request",
			zap.String(logger.ORCHESTRATION_ID, orchId),
			zap.Error(err))
		if storagev2.IsDuplicateEntryError(err) {
			return &ffV2ActPb.CreateCreditCardTrackingRequestActivityResponse{}, nil
		}
		return nil, epifitemporal.NewTransientError(err)
	}
	return &ffV2ActPb.CreateCreditCardTrackingRequestActivityResponse{}, nil
}

func (p *Processor) getExternalUserIdForActorId(ctx context.Context, actorId string) (string, error) {
	savedCards, err := p.ccDao.GetByActorId(ctx, actorId)
	if err != nil {
		return "", errors.Wrap(err, "failed to fetch credit card for actor id")
	}
	return savedCards[0].GetExternalUserId(), nil
}
