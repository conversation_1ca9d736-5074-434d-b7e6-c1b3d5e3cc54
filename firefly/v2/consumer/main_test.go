// nolint: dogsled
package consumer_test

import (
	"os"
	"testing"

	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	s3Mocks "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	"github.com/golang/mock/gomock"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	shipwayMocks "github.com/epifi/gamma/api/vendorgateway/shipway/mocks"
	"github.com/epifi/gamma/firefly/config"
	genConf "github.com/epifi/gamma/firefly/config/genconf"

	"github.com/epifi/gamma/firefly/v2/consumer"
	daoMocks "github.com/epifi/gamma/firefly/v2/dao/mocks"
	"github.com/epifi/gamma/firefly/v2/test"
)

var (
	ffConf    *config.Config
	ffGenConf *genConf.Config
)

func TestMain(m *testing.M) {
	conf, gconf, _, _, _, _, teardown := test.InitTestServerV2()
	ffConf = conf
	ffGenConf = gconf
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockDependencies struct {
	cardRequestDao      *daoMocks.MockCardRequestDao
	creditCardDao       *daoMocks.MockCreditCardDao
	creditCardOffersDao *daoMocks.MockCreditCardOffersDao
	ccDocsS3Client      *s3Mocks.MockS3Client
	actorClient         *actorMocks.MockActorClient
	userClient          *userMocks.MockUsersClient
	shipwayClient       *shipwayMocks.MockShipwayClient
	celestialClient     *celestialMocks.MockCelestialClient
}

func getFireflyV2SvcWithMocks(t *testing.T) (*consumer.Service, *mockDependencies, func()) {
	ctr := gomock.NewController(t)

	mockCardRequestDao := daoMocks.NewMockCardRequestDao(ctr)
	mockCreditCardDao := daoMocks.NewMockCreditCardDao(ctr)
	mockOffersDao := daoMocks.NewMockCreditCardOffersDao(ctr)
	mockCcDocsS3Client := s3Mocks.NewMockS3Client(ctr)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockUserClient := userMocks.NewMockUsersClient(ctr)
	mockShipwayClient := shipwayMocks.NewMockShipwayClient(ctr)
	mockCelestialClient := celestialMocks.NewMockCelestialClient(ctr)

	svc := consumer.NewService(mockCardRequestDao, mockCreditCardDao, mockOffersDao, nil,
		mockCcDocsS3Client, mockActorClient, mockUserClient, mockShipwayClient, mockCelestialClient, ffGenConf)

	md := &mockDependencies{
		cardRequestDao:      mockCardRequestDao,
		creditCardDao:       mockCreditCardDao,
		creditCardOffersDao: mockOffersDao,
		ccDocsS3Client:      mockCcDocsS3Client,
		actorClient:         mockActorClient,
		userClient:          mockUserClient,
		shipwayClient:       mockShipwayClient,
		celestialClient:     mockCelestialClient,
	}

	return svc, md, func() {
		ctr.Finish()
	}
}
