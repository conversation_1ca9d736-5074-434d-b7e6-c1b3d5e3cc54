//go:build wireinject
// +build wireinject

package wire

import (
	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	ffBePb "github.com/epifi/gamma/api/firefly"

	actorPb "github.com/epifi/gamma/api/actor"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/vendorgateway/shipway"
	"github.com/epifi/gamma/firefly/config/genconf"
	"github.com/epifi/gamma/firefly/v2/activity"

	"github.com/epifi/gamma/api/auth"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	savingsPb "github.com/epifi/gamma/api/savings"
	usersPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	ccVgPb2 "github.com/epifi/gamma/api/vendorgateway/creditcard"
	fireflyV2 "github.com/epifi/gamma/firefly/v2"
	consumerV2 "github.com/epifi/gamma/firefly/v2/consumer"
	fireflyV2Dao "github.com/epifi/gamma/firefly/v2/dao"
	types2 "github.com/epifi/gamma/firefly/v2/wire/types"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
)

func ReleaseConfigProvider(conf *genconf.Config) *releaseGenConf.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func InitialiseFireflyV2Svc(conf *genconf.Config, db types2.CreditCardFederalPGDB, usersClient usersPb.UsersClient,
	ccVgClient ccVgPb2.CreditCardClient, authClient auth.AuthClient, bankCustClient bankCustPb.BankCustomerServiceClient,
	onbClient onbPb.OnboardingClient, savingsClient savingsPb.SavingsClient,
	ccOnboardingStateUpdateEventPublisher types2.CcOnboardingStateUpdateEventPublisher,
	segmentClient segmentPb.SegmentationServiceClient, fireflyClient ffBePb.FireflyClient, actorClient actorPb.ActorClient, userGroupClient userGroupPb.GroupClient) *fireflyV2.Service {
	wire.Build(
		fireflyV2.NewService,
		idgen.NewClock,
		idgen.WireSet,
		fireflyV2Dao.CreditCardWireSet,
		fireflyV2Dao.CreditCardOffersWireSet,
		fireflyV2Dao.CardRequestWireSet,
		ReleaseConfigProvider,
		release.EvaluatorWireSet,
	)
	return &fireflyV2.Service{}
}

// config: {"ccDocsS3Client": "Buckets().CreditCardDocs"}
func InitialiseConsumerV2Service(db types2.CreditCardFederalPGDB, eventBroker events.Broker,
	ccDocsS3Client types2.CreditCardDocsS3Client, actorClient actorPb.ActorClient, userClient usersPb.UsersClient,
	shipwayClient shipway.ShipwayClient, celestialClient celestialPb.CelestialClient, gconf *genconf.Config) *consumerV2.Service {
	wire.Build(
		consumerV2.NewService,
		idgen.NewClock,
		idgen.WireSet,
		fireflyV2Dao.CardRequestWireSet,
		fireflyV2Dao.CreditCardOffersWireSet,
		fireflyV2Dao.CreditCardWireSet,
	)
	return &consumerV2.Service{}
}

func InitialiseActivityProcessor(db types2.CreditCardFederalPGDB, ccDocsClient s3.S3Client, userClient usersPb.UsersClient,
	actorClient actorPb.ActorClient, celestialClient celestialPb.CelestialClient, shipwayClient shipway.ShipwayClient) *activity.Processor {
	wire.Build(
		activity.NewProcessor,
		idgen.NewClock,
		idgen.WireSet,
		fireflyV2Dao.CardRequestWireSet,
		fireflyV2Dao.CreditCardWireSet,
		fireflyV2Dao.CreditCardTrackingInfoWireSet,
	)
	return &activity.Processor{}
}
