// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/creditcard"
	"github.com/epifi/gamma/api/vendorgateway/shipway"
	"github.com/epifi/gamma/firefly/config/genconf"
	"github.com/epifi/gamma/firefly/v2"
	"github.com/epifi/gamma/firefly/v2/activity"
	"github.com/epifi/gamma/firefly/v2/consumer"
	"github.com/epifi/gamma/firefly/v2/dao/impl"
	"github.com/epifi/gamma/firefly/v2/wire/types"
	"github.com/epifi/gamma/pkg/feature/release"
	genconf2 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
)

// Injectors from wire.go:

func InitialiseFireflyV2Svc(conf *genconf.Config, db types.CreditCardFederalPGDB, usersClient user.UsersClient, ccVgClient creditcard.CreditCardClient, authClient auth.AuthClient, bankCustClient bankcust.BankCustomerServiceClient, onbClient onboarding.OnboardingClient, savingsClient savings.SavingsClient, ccOnboardingStateUpdateEventPublisher types.CcOnboardingStateUpdateEventPublisher, segmentClient segment.SegmentationServiceClient, fireflyClient firefly.FireflyClient, actorClient actor.ActorClient, userGroupClient group.GroupClient) *v2.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	cardRequestDao := impl.NewCardRequestDao(db, domainIdGenerator)
	creditCardDao := impl.NewCreditCardDao(db, domainIdGenerator)
	creditCardOfferDao := impl.NewCreditCardOfferDao(db, domainIdGenerator)
	featureReleaseConfig := ReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	service := v2.NewService(conf, ccVgClient, authClient, cardRequestDao, creditCardDao, creditCardOfferDao, usersClient, bankCustClient, onbClient, savingsClient, ccOnboardingStateUpdateEventPublisher, segmentClient, fireflyClient, evaluator)
	return service
}

// config: {"ccDocsS3Client": "Buckets().CreditCardDocs"}
func InitialiseConsumerV2Service(db types.CreditCardFederalPGDB, eventBroker events.Broker, ccDocsS3Client types.CreditCardDocsS3Client, actorClient actor.ActorClient, userClient user.UsersClient, shipwayClient shipway.ShipwayClient, celestialClient celestial.CelestialClient, gconf *genconf.Config) *consumer.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	cardRequestDao := impl.NewCardRequestDao(db, domainIdGenerator)
	creditCardDao := impl.NewCreditCardDao(db, domainIdGenerator)
	creditCardOfferDao := impl.NewCreditCardOfferDao(db, domainIdGenerator)
	service := consumer.NewService(cardRequestDao, creditCardDao, creditCardOfferDao, eventBroker, ccDocsS3Client, actorClient, userClient, shipwayClient, celestialClient, gconf)
	return service
}

func InitialiseActivityProcessor(db types.CreditCardFederalPGDB, ccDocsClient s3.S3Client, userClient user.UsersClient, actorClient actor.ActorClient, celestialClient celestial.CelestialClient, shipwayClient shipway.ShipwayClient) *activity.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	cardRequestDao := impl.NewCardRequestDao(db, domainIdGenerator)
	creditCardDao := impl.NewCreditCardDao(db, domainIdGenerator)
	cardTrackingInfoDao := impl.NewCardTrackingInfoDao(db, domainIdGenerator)
	processor := activity.NewProcessor(cardRequestDao, creditCardDao, cardTrackingInfoDao, shipwayClient, ccDocsClient, userClient, actorClient, celestialClient)
	return processor
}

// wire.go:

func ReleaseConfigProvider(conf *genconf.Config) *genconf2.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}
