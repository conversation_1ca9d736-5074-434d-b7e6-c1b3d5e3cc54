package auth

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/vendorgateway"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	pkgerrors "github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/afu"
	authConsumerPb "github.com/epifi/gamma/api/auth/consumer"
	"github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/auth/notification"
	"github.com/epifi/gamma/api/bankcust"
	cardPb "github.com/epifi/gamma/api/card"
	cardProvisionPb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/frontend"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/kyc"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	persistentqueuePb "github.com/epifi/gamma/api/persistentqueue"
	productPb "github.com/epifi/gamma/api/product"
	riskpb "github.com/epifi/gamma/api/risk"
	cmPb "github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/gamma/api/risk/case_management/enums"
	riskEnums "github.com/epifi/gamma/api/risk/enums"
	"github.com/epifi/gamma/api/risk/screener"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	vg "github.com/epifi/gamma/api/vendorgateway/openbanking/auth"
	vgauthPb "github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	"github.com/epifi/gamma/auth/dao"
	"github.com/epifi/gamma/auth/dao/model"
	getCombination "github.com/epifi/gamma/auth/get_combination"
	"github.com/epifi/gamma/auth/metrics"
	acceptanceTypes "github.com/epifi/gamma/pkg/acceptance/types"
	"github.com/epifi/gamma/pkg/feature/release"
	kycPkg "github.com/epifi/gamma/pkg/kyc"
	"github.com/epifi/gamma/pkg/persistentqueue"
)

var (
	errReRegInitFailed        = errors.New("AFU DevReReg init failed")
	errUpdateVendorNotAllowed = errors.New("vendor state update not allowed")
	errReRegFailed            = errors.New("AFU DevReReg failed permanently")
	errReRegInProgress        = errors.New("AFU DevReReg is in progress at vendor")

	recoverAFUQueueDelay = 1 * time.Second

	afuVendor = frontend.Vendor_FEDERAL_BANK
	vendorMap = map[frontend.Vendor]commonvgpb.Vendor{
		frontend.Vendor_FEDERAL_BANK: commonvgpb.Vendor_FEDERAL_BANK,
	}

	txnRetries uint = 3

	actionsOrder = map[auth.AuthFactorUpdateAction]int8{
		auth.AuthFactorUpdateAction_LIVENESS_AND_FACEMATCH:       1,
		auth.AuthFactorUpdateAction_ATM_PIN_VALIDATION:           2,
		auth.AuthFactorUpdateAction_USER_CONFIRMATION:            3,
		auth.AuthFactorUpdateAction_DEVICE_REGISTRATION:          4,
		auth.AuthFactorUpdateAction_DEREGISTER_CURRENT_DEVICE:    5,
		auth.AuthFactorUpdateAction_VENDOR_UPDATE:                6,
		auth.AuthFactorUpdateAction_UPDATE_DEVICE:                7,
		auth.AuthFactorUpdateAction_UPDATE_USER_PROFILE:          8,
		auth.AuthFactorUpdateAction_PUBLISH_AFU_COMPLETION_EVENT: 9,
		auth.AuthFactorUpdateAction_NO_ACTION:                    10,
	}

	vendorUpdateProcessStateOrder = map[afu.UpdateVendorState]int8{
		afu.UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED: 0,
		afu.UpdateVendorState_INITIATED:                       1,
		afu.UpdateVendorState_DEACTIVATED:                     2,
		afu.UpdateVendorState_REREGISTRATION_REQUESTED:        3,
		afu.UpdateVendorState_REREGISTERED:                    4,
		afu.UpdateVendorState_AMBIGUOUS_REACTIVATION:          5,
		afu.UpdateVendorState_COMPLETED:                       6,
	}

	vendorUpdateFailureToStateMap = map[afu.UpdateVendorFailureType]afu.UpdateVendorState{
		afu.UpdateVendorFailureType_DEACTIVATION_FAILED:          afu.UpdateVendorState_INITIATED,
		afu.UpdateVendorFailureType_REREG_ENQUIRY_FAILED:         afu.UpdateVendorState_REREGISTRATION_REQUESTED,
		afu.UpdateVendorFailureType_REREGISTRATION_FAILED:        afu.UpdateVendorState_REREGISTRATION_REQUESTED,
		afu.UpdateVendorFailureType_REACTIVATION_FAILED:          afu.UpdateVendorState_REREGISTERED,
		afu.UpdateVendorFailureType_REACTIVATION_RECOVERY_FAILED: afu.UpdateVendorState_AMBIGUOUS_REACTIVATION,
	}

	successVendorStatusesForAFU = map[uint32]bool{
		uint32(vg.AuthFactorUpdateResponse_OK):                true,
		uint32(vg.AuthFactorUpdateResponse_CANCELLED):         true,
		uint32(vg.AuthFactorUpdateResponse_DEADLINE_EXCEEDED): true,
		uint32(vg.AuthFactorUpdateResponse_DUPLICATE_REQUEST): true,
	}
)

var (
	isDeviceValidatedToStatusMap = map[bool]afu.VerificationStatus{
		true:  afu.VerificationStatus_VERIFICATION_SUCCESS,
		false: afu.VerificationStatus_VERIFICATION_FAIL,
	}

	credToAction = map[afu.Credential]auth.AuthFactorUpdateAction{
		afu.Credential_LIVENESS_FM_VALIDATION: auth.AuthFactorUpdateAction_LIVENESS_AND_FACEMATCH,
		afu.Credential_ATM_PIN_VALIDATION:     auth.AuthFactorUpdateAction_ATM_PIN_VALIDATION,
	}
)

var (
	zapVendorStep = logger.ZapStep("UpdateVendor")
)

const (
	GetAuthFactorsForActorMaxRecordLimit = 30
	MaxSleepDelayForVendorCall           = 40 * time.Second
	caseManagementForAFUModel            = "case_management_afu_model"
)

var (
	actorAuthenticationTerminallyFailedError = fmt.Errorf("actor authentication terminally failed")
	skipActionError                          = errors.New("skip AFU action")
	noActionError                            = errors.New("no AFU action")
)

// RegisterAuthFactorUpdate creates a new record for auth factor update.
// It also returns the next credential that needs to be validated along with reference id.
//
//nolint:funlen
func (s *Service) RegisterAuthFactorUpdate(ctx context.Context, req *auth.RegisterAuthFactorUpdateRequest) (*auth.RegisterAuthFactorUpdateResponse, error) {
	errRes := func(status *rpc.Status, debug string) (*auth.RegisterAuthFactorUpdateResponse, error) {
		status.SetDebugMessage(debug)
		return &auth.RegisterAuthFactorUpdateResponse{
			Status: status,
		}, nil
	}

	// get device registration status. add to auth factors list if device needs to be updated as well.
	deviceRegCredStatus, err := s.deviceRegistrationStatus(ctx, req.ActorId, req.Device)
	if err != nil {
		return errRes(rpc.StatusInternal(), err.Error())
	}
	// add device to updating entity if it's not verified
	if deviceRegCredStatus.Status != afu.VerificationStatus_VERIFICATION_SUCCESS {
		req.AuthFactors = append(req.AuthFactors, afu.AuthFactor_DEVICE)
	}

	// dedupe auth factors
	req.AuthFactors = lo.Uniq(req.GetAuthFactors())

	// validate scenario
	comb, err := getCombination.GetScenario(req.AuthFactors)
	if err != nil {
		return errRes(rpc.StatusFailedPrecondition(), err.Error())
	}

	// validate new values to be set
	newAuthFactorValues, err := validatedNewValues(req.GetEmail(), req.GetPhoneNumber(), req.GetDevice(), req.GetAuthFactors())
	if err != nil {
		return errRes(rpc.StatusInvalidArgument(), err.Error())
	}
	currAuthFactorValues := &afu.AuthFactorValues{
		PhoneNumber: req.GetCurrentPhoneNumber(),
		Email:       req.GetCurrentEmail(),
	}
	// build update context
	afuContext := s.initContext()
	afuContext.AuthFactors = req.AuthFactors
	afuContext.CredentialStatuses = []*afu.CredentialStatus{deviceRegCredStatus}
	afuContext.NewValues = newAuthFactorValues
	afuContext.CurrentValues = currAuthFactorValues
	afuContext.NewDevice = req.Device
	afuContext.ActorAuthState = &afu.ActorAuthState{
		ActorAuthStatus: afu.ActorAuthStatus_ACTOR_AUTH_STATUS_IN_PROGRESS,
	}
	if req.IsCardActive {
		afuContext.CardState = afu.Context_CARD_STATE_ACTIVE
	} else {
		afuContext.CardState = afu.Context_CARD_STATE_INACTIVE
	}
	if isDeviceRegistrationRequired(req.GetAuthFactors()) {
		isDeviceRegAllowed, checkErr := s.isDeviceRegistrationAllowedOnIOS(ctx, req.GetDevice())
		if checkErr != nil {
			return errRes(rpc.StatusInternal(), err.Error())
		}
		// We used Muskan's device to test a prod issue(SMS not going for J&K) in another device. Now she is not able to log in to her own device due iOS version check.
		// Skipping this check to allow her to log in
		if req.GetActorId() == "AC220927n3Ro4YTxSZORj4yvaD93yA==" {
			isDeviceRegAllowed = true
		}
		if !isDeviceRegAllowed {
			return errRes(rpc.NewStatus(uint32(auth.RegisterAuthFactorUpdateResponse_IOS_OS_NOT_ALLOWED), "", ""), "")
		}
	}
	activeProducts, err := s.getActiveProducts(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in getting active products", zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}

	afuContext.ExistingActiveProducts = activeProducts

	// block AFU if there are any existing AFU's by auth factors with vendor update started
	if _, err = s.blockExistingAFUs(ctx, afu.FailureReason_AFU_CONFLICT, req.GetPhoneNumber(), req.GetEmail(), req.GetDevice().GetDeviceId()); err != nil {
		if errors.Is(err, epifierrors.ErrPermissionDenied) {
			return errRes(rpc.NewStatus(uint32(auth.RegisterAuthFactorUpdateResponse_AFU_CONFLICT), "", "AFU record exists with vendor update started"), "")
		}
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()), "")
	}
	if err = s.handleAFUCooldown(ctx, req.GetActorId(), req.GetAuthFactors(), req.GetPhoneNumber()); err != nil {
		return errRes(rpc.StatusFromErrorWithDefaultInternal(err), "")
	}

	// transaction to soft-delete previous record & creates a new one
	var record *afu.AuthFactorUpdate
	if err = storagev2.RunCRDBIdempotentTxn(ctx, txnRetries, func(ctx context.Context) error {
		// deactivate previous attempt (if any) as there can be only one active attempt per actor
		if err = s.afuDao.DeleteByActor(ctx, req.ActorId); err != nil {
			return err
		}

		// create new AFU record in DB
		record, err = s.afuDao.Create(ctx, &afu.AuthFactorUpdate{
			ActorId:       req.ActorId,
			OverallStatus: afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
			Context:       afuContext,
			VendorContext: &afu.VendorContext{},
		})
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in create: %v", err))
	}
	logger.Info(ctx, "new afu request initiated",
		zap.String(logger.AFU_ID, record.GetId()),
		zap.String(logger.REQUEST_TYPE, comb.String()),
		zap.Time(logger.START_TIME, record.GetCreatedAt().AsTime()))

	if cfg.IsSimulatedEnv(s.Conf.Application().Environment) {
		record, _ = s.maybeBypassAFU(ctx, record, req.GetAndroidSimSubIds())
	}

	// get next action to be performed by the user
	nextActionDetails, err := s.ProcessAFU(ctx, record)
	if err != nil {
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in nextClientAction: %v", err))
	}

	return &auth.RegisterAuthFactorUpdateResponse{
		Status:            rpc.StatusOk(),
		Id:                record.GetId(),
		NextActionDetails: nextActionDetails,
	}, nil
}

func (s *Service) getActiveProducts(ctx context.Context, actorId string) ([]productPb.ProductType, error) {
	productStatusRes, err := s.productClient.GetProductsStatus(ctx, &productPb.GetProductsStatusRequest{
		ActorId:      actorId,
		ProductTypes: getAllProductTypes(),
	})
	if rpcErr := epifigrpc.RPCError(productStatusRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting product status", zap.Error(rpcErr))
		return nil, rpcErr
	}
	activeProducts := make([]productPb.ProductType, 0)
	for k, v := range productStatusRes.GetProductInfoMap() {
		if v.GetProductStatus() == productPb.ProductStatus_PRODUCT_STATUS_ACTIVE {
			activeProducts = append(activeProducts, productPb.ProductType(productPb.ProductType_value[k]))
		}
	}
	if len(activeProducts) == 0 {
		logger.Error(ctx, "user entered AFU with no active products")
		return nil, fmt.Errorf("user entered AFU with no active products")
	}
	return activeProducts, nil
}

func getAllProductTypes() []productPb.ProductType {
	productTypes := make([]productPb.ProductType, 0)
	for k, v := range productPb.ProductType_name {
		if v != productPb.ProductType_PRODUCT_TYPE_UNSPECIFIED.String() {
			productTypes = append(productTypes, productPb.ProductType(k))
		}
	}
	return productTypes
}

func (s *Service) GetAuthFactorUpdateStatus(ctx context.Context, req *auth.GetAuthFactorUpdateStatusRequest) (*auth.GetAuthFactorUpdateStatusResponse, error) {
	errRes := func(status *rpc.Status, debug string) (*auth.GetAuthFactorUpdateStatusResponse, error) {
		status.SetDebugMessage(debug)
		return &auth.GetAuthFactorUpdateStatusResponse{
			Status: status,
		}, nil
	}
	record, err := s.afuDao.GetById(ctx, req.GetAuthFactorUpdateId())
	if storagev2.IsRecordNotFoundError(err) {
		return errRes(rpc.StatusRecordNotFound(), "")
	}
	if err != nil {
		logger.Error(ctx, "error in AFU GetById", zap.String("err", err.Error()))
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in AFU GetById: %v", err))
	}
	nextActionDetails, errProc := s.ProcessAFU(ctx, record)
	if errProc != nil {
		logger.Error(ctx, "error in get next action", zap.Error(errProc))
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in process AFU: %v", errProc))
	}

	// get revised record after processing of record
	record, err = s.afuDao.GetById(ctx, req.GetAuthFactorUpdateId())
	if storagev2.IsRecordNotFoundError(err) {
		return errRes(rpc.StatusRecordNotFound(), "")
	}

	reRegistrationDeviceVendorInitStatus := record.GetVendorContext().GetReRegisterDeviceVendorInitStatus()

	failureReason := getLatestFailureReason(record)
	// This flag returns whether the device re-registration can be retried or not based on failure reason,
	// and we also check whether the AFU attempt is done and update vendor state is reset for retry.
	isRetryableAFUFailure := isFailureReasonsRetryable[failureReason] && (record.GetVendorContext().GetState() == afu.UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED)

	return &auth.GetAuthFactorUpdateStatusResponse{
		Status:                               rpc.StatusOk(),
		NextActionDetails:                    nextActionDetails,
		OverallStatus:                        record.GetOverallStatus(),
		ReRegistrationDeviceVendorInitStatus: reRegistrationDeviceVendorInitStatus,
		IsDeviceRegistrationRetryable:        isRetryableAFUFailure,
		UpdateVendorState:                    record.GetVendorContext().GetState(),
	}, nil
}

// nolint: funlen
func (s *Service) ValidateAuthFactorUpdate(ctx context.Context, req *auth.ValidateAuthFactorUpdateRequest) (res *auth.ValidateAuthFactorUpdateResponse, err error) {
	successRes := func(status *rpc.Status, id string, nextAction auth.AuthFactorUpdateAction, nextSubActions []auth.AuthFactorUpdateAction, failureReason afu.FailureReason, deeplink *deeplinkPb.Deeplink) (*auth.ValidateAuthFactorUpdateResponse, error) {
		return &auth.ValidateAuthFactorUpdateResponse{
			Status: status,
			Id:     id,
			NextActionDetails: &auth.AuthFactorUpdateNextActionDetails{
				NextAction:     nextAction,
				NextSubActions: nextSubActions,
				FailureReason:  failureReason,
				Deeplink:       deeplink,
			}}, nil
	}
	errRes := func(status *rpc.Status, debug string) (*auth.ValidateAuthFactorUpdateResponse, error) {
		status.SetDebugMessage(debug)
		return &auth.ValidateAuthFactorUpdateResponse{
			Status: status,
		}, nil
	}

	actorId := req.GetActorId()
	if actorId == "" {
		logger.Error(ctx, "actorId not specified", zapAfu)
		return errRes(rpc.StatusInvalidArgument(), "actorId not present")
	}
	afuRecord, err := s.GetLastAFUForActor(ctx, actorId)
	if err == epifierrors.ErrRecordNotFound {
		return successRes(rpc.StatusOk(), "", auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, nil, afu.FailureReason_FAILURE_REASON_UNSPECIFIED, nil)
	}
	if err != nil {
		logger.Error(ctx, "error in GetByActor", zapAfu, zap.Error(err))
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in getByActor : %v", err))
	}

	switch afuRecord.OverallStatus {
	// last attempt is in terminal state, new attempt can be started
	case afu.OverallStatus_OVERALL_STATUS_COMPLETED,
		afu.OverallStatus_OVERALL_STATUS_FAILED:
		return successRes(rpc.StatusOk(), "", auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, nil, afu.FailureReason_FAILURE_REASON_UNSPECIFIED, nil)

	case afu.OverallStatus_OVERALL_STATUS_STUCK:
		logger.Info(ctx, "prev afu record in stuck state", zapAfu)
		return errRes(rpc.NewStatusWithoutDebug(uint32(auth.ValidateAuthFactorUpdateResponse_LAST_UPDATE_STUCK), ""),
			fmt.Sprintf("prev afu record in stuck state"))

	// last attempt was left in middle
	case afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
		afu.OverallStatus_OVERALL_STATUS_UNSPECIFIED:
		// check if new auth factors are same or different
		newAFEqual := AuthFactorValuesEqual(afuRecord.Context.GetNewValues(), &afu.AuthFactorValues{
			PhoneNumber: req.PhoneNumber,
			Email:       req.Email,
			DeviceId:    req.DeviceId,
		})

		// user is trying to update with other values than prev attempt
		if !newAFEqual {
			// if vendor update hasn't started, we can allow afu with different values
			if afuRecord.GetVendorContext().GetState() == afu.UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED {
				return successRes(rpc.StatusOk(), "", auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, nil, afu.FailureReason_FAILURE_REASON_UNSPECIFIED, nil)
			}
			// if vendor update is in progress, the user will have to wait for it go into terminal state
			return errRes(rpc.NewStatus(uint32(auth.ValidateAuthFactorUpdateResponse_UNEXPECTED_AUTH_FACTORS),
				"last afu attempt with other details is in progress", ""), "")
		}

		// user's last attempt was with same values and is in progress, resume it
		// get next action to be performed by the user
		nextActionDetails, err2 := s.ProcessAFU(ctx, afuRecord)
		if err2 != nil {
			return errRes(rpc.StatusInternal(), fmt.Sprintf("error in nextClientAction for prevRecord: %v", err2))
		}

		// get revised record after processing of record
		record, err := s.afuDao.GetById(ctx, afuRecord.GetId())
		if storagev2.IsRecordNotFoundError(err) {
			return errRes(rpc.StatusRecordNotFound(), "")
		}

		return successRes(rpc.NewStatusWithoutDebug(uint32(auth.ValidateAuthFactorUpdateResponse_UPDATE_VENDOR_IN_PROGRESS), ""),
			afuRecord.Id,
			nextActionDetails.GetNextAction(),
			nextActionDetails.GetNextSubActions(),
			record.GetFailureReason(),
			nextActionDetails.GetDeeplink(),
		)

	default:
		logger.Error(ctx, fmt.Sprintf("invalid afu overall status: %v", afuRecord.OverallStatus), zapAfu, zapAfuId(afuRecord.Id))
		return errRes(rpc.StatusInternal(), "invalid afu status")
	}
}

// auth factors sorted based on cooldown period (in decreasing order)
func (s *Service) getAuthFactorsByCooldown(authFactors []afu.AuthFactor) []afu.AuthFactor {
	sort.SliceStable(authFactors, func(i, j int) bool {
		t1, _ := s.Conf.AFU().AFUCooldown().CooldownDurationMap().Load(authFactors[i].String())
		t2, _ := s.Conf.AFU().AFUCooldown().CooldownDurationMap().Load(authFactors[j].String())
		return t1 > t2
	})
	return authFactors
}

func getCooldownStatusAsError(authFactor afu.AuthFactor) error {
	switch authFactor {
	case afu.AuthFactor_PHONE_NUM:
		return rpc.StatusAsError(rpc.NewStatus(uint32(auth.RegisterAuthFactorUpdateResponse_PHONE_UPDATE_COOLDOWN), "", "phone update cooldown"))
	case afu.AuthFactor_EMAIL:
		return rpc.StatusAsError(rpc.NewStatus(uint32(auth.RegisterAuthFactorUpdateResponse_EMAIL_UPDATE_COOLDOWN), "", "email update cooldown"))
	case afu.AuthFactor_DEVICE:
		return rpc.StatusAsError(rpc.NewStatus(uint32(auth.RegisterAuthFactorUpdateResponse_DEVICE_UPDATE_COOLDOWN), "", "device update cooldown"))
	case afu.AuthFactor_SIM:
		return rpc.StatusAsError(rpc.NewStatus(uint32(auth.RegisterAuthFactorUpdateResponse_SIM_UPDATE_COOLDOWN), "", "sim update cooldown"))
	default:
		return fmt.Errorf("no error handling for auth factor %v", authFactor)
	}
}

func (s *Service) cooldownChecker(ctx context.Context, afuSummaries []*afu.AFUSummary, authFactor afu.AuthFactor) error {
	var (
		err error
	)
	for _, summary := range afuSummaries {
		if summary.GetAfuStatus() != afu.OverallStatus_OVERALL_STATUS_COMPLETED {
			continue
		}
		cooldownDuration, ok := s.Conf.AFU().AFUCooldown().CooldownDurationMap().Load(authFactor.String())
		if !ok {
			logger.Error(ctx, "no cooldown duration found for auth factor", zap.String(logger.AUTH_TYPE, authFactor.String()))
			return fmt.Errorf("no cooldown duration found for %v", authFactor)
		}
		if lo.Contains(summary.GetAuthFactors(), authFactor) {
			if summary.GetUpdatedAt().AsTime().Add(cooldownDuration).After(s.TimeClient.Now()) {
				err = getCooldownStatusAsError(authFactor)
			}
		}
		if err != nil {
			logger.Info(ctx, "not eligible due to AFU cooldown",
				zap.String(logger.AUTH_TYPE, authFactor.String()),
				zap.Time(logger.OLDEST_TIME, summary.GetUpdatedAt().AsTime()),
				zap.String(logger.REQUEST_ID, summary.GetAfuId()))
			return err
		}
	}
	return nil
}

func (s *Service) handleAFUCooldown(ctx context.Context, actorId string, authFactors []afu.AuthFactor, currentPhoneNumber *commontypes.PhoneNumber) error {
	if !s.Conf.AFU().AFUCooldown().EnableCooldown() {
		return nil
	}
	if cfg.IsSimulatedEnv(s.Conf.Application().Environment) && lo.Contains(s.Conf.AFU().BypassCredentialVerificationForPhoneNumbers(), currentPhoneNumber.ToString()) {
		logger.Info(ctx, "skipping cooldown check for AFU bypassed number: "+currentPhoneNumber.ToString())
		return nil
	}
	afuSummaries, err := s.afuCache.GetAFUSummaries(ctx, actorId)
	if errors.Is(err, epifierrors.ErrRecordNotFound) || len(afuSummaries) == 0 {
		logger.Info(ctx, "no summaries found, skip cooldown check")
		return nil
	}
	if err != nil {
		logger.Error(ctx, "error in getting AFU summaires", zap.Error(err))
		return err
	}

	authFactorsByCooldown := s.getAuthFactorsByCooldown(authFactors)
	for _, authFactor := range authFactorsByCooldown {
		if err = s.cooldownChecker(ctx, afuSummaries, authFactor); err != nil {
			return err
		}
	}

	return nil
}

// IsLivenessManualReviewExpired checks if liveness is in failed state for more than 24 hours
func (s *Service) IsLivenessManualReviewExpired(_ context.Context, afuRecord *afu.AuthFactorUpdate) bool {
	creds := afuRecord.GetContext().GetCredentialStatuses()
	credStatusMap := credentialStatusMap(creds)
	livenessCredStatus := credStatusMap[afu.Credential_LIVENESS_FM_VALIDATION]
	if livenessCredStatus == afu.VerificationStatus_VERIFICATION_IN_REVIEW && time.Since(afuRecord.GetUpdatedAt().AsTime()) > s.Conf.AFU().LivenessManualReviewExpiryTime() {
		return true
	}
	return false
}

func (s *Service) UpdateAFUCredentialStatus(ctx context.Context, req *auth.UpdateAFUCredentialStatusRequest) (*auth.UpdateAFUCredentialStatusResponse, error) {
	errRes := func(rpcStatus *rpc.Status, msg string) (*auth.UpdateAFUCredentialStatusResponse, error) {
		rpcStatus.SetDebugMessage(msg)
		return &auth.UpdateAFUCredentialStatusResponse{
			Status: rpcStatus,
		}, nil
	}

	// Get AFU Record
	m, err := s.afuDao.GetById(ctx, req.AuthFactorUpdateId)
	if err != nil {
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in AFU get by id: %v", err))
	}

	actorAuthStatus, lastLevel, _ := getAFUActorAuthState(m.GetContext())
	if isAFUActorAuthTerminated(actorAuthStatus) {
		return errRes(rpc.StatusFailedPrecondition(), "afu actor authentication is not in progress")
	}
	req.CredentialStatus.Level = uint32(lastLevel)
	// Validate overall status
	if s.isTerminalState(m.GetOverallStatus()) {
		return errRes(rpc.StatusFailedPrecondition(), "overall status not in progress")
	}

	// upsert credential status in context
	updatedCreds, err := upsertCredentialStatus(m.Context.GetCredentialStatuses(), req.GetCredentialStatus(), false)

	switch {
	case errors.Is(err, identicalVerificationStatusError):
		// if the credential status is being updated to same state proceed for next action
		break
	case err != nil:
		return errRes(rpc.StatusInvalidArgument(), fmt.Sprintf("error in upsert cred status: %v", err))

	default:
		// save new status in DB
		m, err = s.afuDao.UpdateContextAndStatus(ctx, req.AuthFactorUpdateId, &afu.Context{
			CredentialStatuses: updatedCreds,
		}, m.OverallStatus)
		if err != nil {
			return errRes(rpc.StatusInternal(), fmt.Sprintf("error in update dao: %v", err))
		}
		logger.Info(ctx, fmt.Sprintf("upserted in cred %v, with status %v", req.GetCredentialStatus().GetCredential(), req.GetCredentialStatus().GetStatus()))
	}

	// get next action
	nextActionDetails, err := s.ProcessAFU(ctx, m)
	if err != nil {
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in next action 2: %v", err))
	}

	// success response
	return &auth.UpdateAFUCredentialStatusResponse{
		Status:            rpc.StatusOk(),
		OverallStatus:     m.GetOverallStatus(),
		NextActionDetails: nextActionDetails,
	}, nil
}

// nolint: funlen
func (s *Service) ConfirmAuthFactorUpdate(ctx context.Context, req *auth.ConfirmAuthFactorUpdateRequest) (*auth.ConfirmAuthFactorUpdateResponse, error) {
	errRes := func(rpcStatus *rpc.Status, msg string) (*auth.ConfirmAuthFactorUpdateResponse, error) {
		rpcStatus.SetDebugMessage(msg)
		return &auth.ConfirmAuthFactorUpdateResponse{
			Status: rpcStatus,
		}, nil
	}
	// for updates where cred block isn't needed, generate re-registration request Id
	if req.CredBlock == "" {
		req.CredBlockRequestId = genDeviceReRegRequestId()
	}

	if req.GetAuthFactorUpdateId() == "" {
		logger.Info(ctx, "empty afuID received")
		return errRes(rpc.StatusInvalidArgument(), "empty afuID received")
	}

	afuRecord, err := s.GetAFUByID(ctx, req.GetAuthFactorUpdateId())
	if err != nil {
		return errRes(rpc.StatusInternal(), err.Error())
	}

	if afuRecord.GetVendorContext().GetRequestId() != "" {
		logger.Info(ctx, fmt.Sprintf("requestId already exists returning next action"), logger.ZapAFUId(afuRecord.GetId()))
		nextActionDetails, processErr := s.ProcessAFU(ctx, afuRecord)
		if processErr != nil {
			logger.Error(ctx, "error in process AFU", zap.Error(processErr), logger.ZapAFUId(afuRecord.GetId()))
			return errRes(rpc.StatusInternal(), fmt.Sprintf("error in next action 2: %v", processErr))
		}
		return &auth.ConfirmAuthFactorUpdateResponse{
			Status:            rpc.StatusOk(),
			NextActionDetails: nextActionDetails,
		}, nil
	}

	// validation to check if afu attempt is already in terminal state
	if s.isTerminalState(afuRecord.GetOverallStatus()) {
		logger.Info(ctx, "afu is already in terminal state", logger.ZapAFUId(afuRecord.GetId()))
		return errRes(rpc.StatusPermissionDenied(), "afu is already in terminal state")
	}
	// update vendorCardId and credBlockReqId in afu record and fetch updated record
	m, err := s.afuDao.UpdateContextAndVendorContext(ctx, req.AuthFactorUpdateId, &afu.Context{
		VendorCardId: req.VendorCardId,
		CardForm:     req.GetCardForm(),
	},
		&afu.VendorContext{
			RequestId: req.CredBlockRequestId,
		})
	if err != nil {
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error updating vendorCardId and credBlockReqId in afu record : %v", err))
	}

	// If user confirmation status not already accepted, then process user confirmation
	if m.GetContext().GetUserConfirmationStatus() != afu.UserConfirmationStatus_USER_ACCEPTED {
		_, err = s.afuDao.UpdateContextAndStatus(ctx, req.AuthFactorUpdateId, &afu.Context{
			UserConfirmationStatus: afu.UserConfirmationStatus_USER_ACCEPTED,
		}, m.OverallStatus)
		if err != nil {
			logger.Error(ctx, "error in update afu", zap.Error(err), logger.ZapAFU(), logger.ZapAFUId(m.Id))
			return errRes(rpc.StatusInternal(), fmt.Sprintf("error in update afu: %v", err))
		}
	}

	m, err = s.afuDao.GetById(ctx, req.AuthFactorUpdateId)
	if err != nil {
		logger.Error(ctx, "error in get afu", zap.Error(err), logger.ZapAFU(), logger.ZapAFUId(req.AuthFactorUpdateId))
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in get afu: %v", err))
	}

	// Get next action after user confirmation
	nextActionDetails, err := s.ProcessAFU(ctx, m)
	if err != nil {
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in next action 2: %v", err))
	}
	action := nextActionDetails.GetNextAction()

	if action == auth.AuthFactorUpdateAction_DEREGISTER_CURRENT_DEVICE {
		action, err = s.ProcessDeviceDeletionAndGetNextAction(ctx, m)
		if err != nil {
			return errRes(rpc.StatusInternal(), err.Error())
		}
	}

	// If next action is VENDOR_UPDATE or any action above it and vendor update state is unspecified, then
	// produce a message in queue.
	if !nextActionChronologicallyValid(ctx, action, auth.AuthFactorUpdateAction_VENDOR_UPDATE) &&
		m.GetVendorContext().GetState() == afu.UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED {
		_, err = s.publishUpdateVendorMessage(ctx, m.Id, m.ActorId, req.CredBlock, "")
		if err != nil {
			return errRes(rpc.StatusInternal(), "error publishing message for vendor update")
		}
	}

	// success response
	return &auth.ConfirmAuthFactorUpdateResponse{
		Status:            rpc.StatusOk(),
		NextActionDetails: nextActionDetails,
	}, nil
}

// AuthFactorsProfileUpdate is called by the orchestrator(Frontend) for confirming that
// new auth factor values have been updated in epifi user profile.
func (s *Service) AuthFactorsProfileUpdate(ctx context.Context, req *auth.AuthFactorsProfileUpdateRequest) (*auth.AuthFactorsProfileUpdateResponse, error) {
	errRes := func(rpcStatus *rpc.Status, msg string) (*auth.AuthFactorsProfileUpdateResponse, error) {
		rpcStatus.SetDebugMessage(msg)
		return &auth.AuthFactorsProfileUpdateResponse{
			Status: rpcStatus,
		}, nil
	}

	// get AFU record
	m, err := s.GetAFUByID(ctx, req.AuthFactorUpdateId)
	if err != nil {
		return errRes(rpc.StatusInternal(), err.Error())
	}

	// validate next action
	nextActionDetails, err := s.ProcessAFU(ctx, m)
	if err != nil {
		return errRes(rpc.StatusInternal(), err.Error())
	}
	action := nextActionDetails.GetNextAction()
	if action != auth.AuthFactorUpdateAction_UPDATE_USER_PROFILE {
		return errRes(rpc.StatusFailedPrecondition(), fmt.Sprintf("unexpected state: %v", action))
	}

	// mark profile update status as success
	if _, err = s.afuDao.UpdateContextAndStatus(ctx, m.Id, &afu.Context{
		EpifiEmailPhoneNumUpdate: afu.RequestStatus_REQUEST_STATUS_SUCCESS,
	}, afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS); err != nil {
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in setting user confirmation: %v", err))
	}
	return &auth.AuthFactorsProfileUpdateResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) GetAuthFactorUpdateRecord(ctx context.Context, req *auth.GetAuthFactorUpdateRecordRequest) (*auth.GetAuthFactorUpdateRecordResponse, error) {
	errRes := func(rpcStatus *rpc.Status, msg string) (*auth.GetAuthFactorUpdateRecordResponse, error) {
		rpcStatus.SetDebugMessage(msg)
		return &auth.GetAuthFactorUpdateRecordResponse{
			Status: rpcStatus,
		}, nil
	}
	m, err := s.GetAFUByID(ctx, req.AuthFactorUpdateId)
	if err != nil {
		return errRes(rpc.StatusInternal(), err.Error())
	}
	return &auth.GetAuthFactorUpdateRecordResponse{
		Status: rpc.StatusOk(),
		Record: m,
	}, nil
}

// TODO(aditya): figure out retry mechanism for re registration call
//
//nolint:funlen
func (s *Service) ReRegisterDevice(ctx context.Context, req *auth.ReRegisterDeviceRequest) (*auth.ReRegisterDeviceResponse, error) {
	zapAFUId := logger.ZapAFUId(req.GetAuthFactorUpdateId())
	errRes := func(rpcStatus *rpc.Status, msg string) (*auth.ReRegisterDeviceResponse, error) {
		rpcStatus.SetDebugMessage(msg)
		return &auth.ReRegisterDeviceResponse{
			Status: rpcStatus,
		}, nil
	}
	// extract payload to send to the vendor
	payload, err := getPayloadWithoutPrefix(ctx, req.Payload)
	if err != nil {
		return errRes(rpc.StatusInvalidArgument(), "error in getPayloadWithoutPrefix")
	}

	if req.GetAuthFactorUpdateId() == "" {
		logger.Info(ctx, "empty afuID received")
		return errRes(rpc.StatusInvalidArgument(), "empty afuID received")
	}

	record, err := s.GetAFUByID(ctx, req.GetAuthFactorUpdateId())
	if err != nil {
		return errRes(rpc.StatusInternal(), err.Error())
	}

	// validation to check if afu attempt is already in terminal state
	if s.isTerminalState(record.GetOverallStatus()) {
		logger.Info(ctx, "afu is already in terminal state", logger.ZapAFUId(record.GetId()))
		return errRes(rpc.StatusPermissionDenied(), "afu is already in terminal state")
	}

	// confirm if register device is a valid step
	nextActionDetails, err := s.ProcessAFU(ctx, record)
	if err != nil {
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in checking action for dev registration: %v", err))
	}
	action := nextActionDetails.GetNextAction()

	// block if desired action is not Device registration
	if action != auth.AuthFactorUpdateAction_DEVICE_REGISTRATION {
		logger.Error(ctx, fmt.Sprintf("invalid call to ReRegisterDevice, desired action: %s", action), zapAFUId)
		return errRes(rpc.StatusFailedPrecondition(), fmt.Sprintf("invalid call to ReRegisterDevice API, desired action: %v", action))
	}

	// regulatory stuff.
	isAllowed, err := s.validateDeviceRegCount(ctx, req.GetDevice().GetDeviceId())
	if err != nil {
		logger.Error(ctx, "ReRegister error in validateDeviceRegCount", zap.Error(err), zapAFUId)
		return nil, fmt.Errorf("error in validateDeviceRegCount rereg: %v", err)
	}
	if !isAllowed {
		logger.Error(ctx, "ReRegister error : device re-registration has reached max allowed limit")
		return errRes(rpc.NewStatusWithoutDebug(uint32(auth.ReRegisterDeviceResponse_REGISTRATION_LIMIT), ""), "device re registration limit exceeded")
	}

	if s.Conf.Flags().EnableSMSAckListener() {
		if ack, wait := s.isSMSAcknowledged(ctx, req.GetActorId()); !ack && wait {
			return errRes(rpc.NewStatusWithoutDebug(uint32(auth.ReRegisterDeviceResponse_RETRYABLE_ERROR), ""), "sms not ack yet")
		}
	}

	// update afu record with encrypted payload, vendor card Id and cred block request Id
	afuRecord, err := s.afuDao.UpdateContextAndVendorContext(ctx, req.GetAuthFactorUpdateId(), &afu.Context{
		EncryptedPayload: payload,
		NewValues: &afu.AuthFactorValues{
			DeviceId: req.GetDevice().GetDeviceId(),
		},
		SimId: req.GetSimId(),
	}, &afu.VendorContext{
		RequestId: req.GetCredBlockRequestId(),
	})
	if err != nil {
		logger.Error(ctx, "ReRegister error updating request params", zap.Error(err), zapAFUId)
		return errRes(rpc.StatusInternal(), err.Error())
	}

	if !isDeviceRegistrationRequired(afuRecord.GetContext().GetAuthFactors()) {
		logger.Error(ctx, "ReRegister device called for invalid auth factors", zapAFUId)
		return errRes(rpc.StatusFailedPrecondition(), "ReRegister action not supported")
	}

	// If next action is VENDOR_UPDATE or any action above it and vendor update state is unspecified, then
	// produce a message in queue.
	// TODO(aditya): Do in a txn
	if afuRecord.GetVendorContext().GetState() == afu.UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED {
		afuRecord, err = s.publishUpdateVendorMessage(ctx, afuRecord.Id, afuRecord.ActorId, req.CredBlock, req.AccessToken)
		if err != nil {
			return errRes(rpc.StatusInternal(), "error publishing message for vendor update")
		}
	}
	nextActionDetails, err = s.ProcessAFU(ctx, afuRecord)
	if err != nil {
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in next action 2: %v", err))
	}
	return &auth.ReRegisterDeviceResponse{
		Status:            rpc.StatusOk(),
		NextActionDetails: nextActionDetails,
	}, nil
}

// nolint: funlen, govet
func (s *Service) RecoverAFUProcessForActor(ctx context.Context, req *auth.RecoverAFUProcessForActorRequest) (*auth.RecoverAFUProcessForActorResponse, error) {
	var (
		successRes = func(status *rpc.Status) (*auth.RecoverAFUProcessForActorResponse, error) {
			return &auth.RecoverAFUProcessForActorResponse{Status: status}, nil
		}

		errRes = func(status *rpc.Status, debug string) (*auth.RecoverAFUProcessForActorResponse, error) {
			status.SetDebugMessage(debug)
			return &auth.RecoverAFUProcessForActorResponse{
				Status: status,
			}, nil
		}
		newUpdateVendorState       afu.UpdateVendorState
		newUpdateVendorFailureType afu.UpdateVendorFailureType
		afuRecord                  *afu.AuthFactorUpdate
		err                        error
	)
	logger.Info(ctx, "received Recover AFU request", zap.String(logger.REQUEST, req.String()))
	actorId := req.GetActorId()
	if actorId == "" {
		actorId = req.GetActorIdV2()
	}
	switch {
	case req.GetDeviceReregRequestId() != "":
		afuRecord, err = s.afuDao.GetByDeviceReRegRequestId(ctx, req.GetDeviceReregRequestId())
		if err != nil {
			logger.Error(ctx, "error while fetching record from DB", zap.Error(err))
			return errRes(rpc.StatusInternal(), fmt.Sprintf("error while fetching afu record : %s", err.Error()))
		}
		actorId = afuRecord.GetActorId()
	case actorId != "":
		// fetch latest AFU record for actor
		afuRecord, err = s.GetLastAFUForActor(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "error while fetching record from DB", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return errRes(rpc.StatusInternal(), fmt.Sprintf("error while fetching afu record : %s", err.Error()))
		}
	default:
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error while fetching afu record : %s", err.Error()))
	}
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	if afuRecord.GetOverallStatus() == afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS {
		if afuRecord.GetContext().GetActorAuthState().GetActorAuthStatus() != afu.ActorAuthStatus_ACTOR_AUTH_STATUS_SUCCESS ||
			(isDeviceRegistrationRequired(afuRecord.GetContext().GetAuthFactors()) &&
				afuRecord.GetContext().GetEncryptedPayload() == "") {
			logger.Info(ctx, "no additional work required for recoverAFU")
			return successRes(rpc.StatusOk())
		}

		// if vendor request hasn't started, and no progress in 7 days
		// this implies ReRegister Device failed due to race condition,
		// and user is stuck in a limbo. As a way out, fail the attempt to allow fresh attempt
		if isDeviceRegistrationRequired(afuRecord.GetContext().GetAuthFactors()) &&
			!hasVendorCallsInitiated(afuRecord.GetVendorContext().GetState()) &&
			time.Since(afuRecord.GetCreatedAt().AsTime()) > 24*time.Hour {
			logger.Info(ctx, "marking afu attempt as failed due to race condition ReRegisterDevice didn't complete")
			_, err = s.afuDao.UpdateContextAndStatus(ctx, afuRecord.GetId(), afuRecord.GetContext(), afu.OverallStatus_OVERALL_STATUS_FAILED)
			if err != nil {
				logger.Error(ctx, "error marking overall state as failed", logger.ZapAFU(), logger.ZapAFUId(afuRecord.Id), zap.Error(err))
				return errRes(rpc.StatusInternal(), fmt.Sprintf("error marking attempt as failed : %s", err.Error()))
			}
			return successRes(rpc.StatusOk())
		}

		if msgId, err := s.AFUVendorUpdatePublisher.PublishWithDelay(ctx, &authConsumerPb.ProcessVendorUpdateRequest{
			AfuId:   afuRecord.Id,
			ActorId: afuRecord.ActorId,
		}, recoverAFUQueueDelay); err != nil {
			logger.Error(ctx, "error while publishing message to resume vendor update process", zapAfu,
				zapAfuId(afuRecord.Id), zap.Error(err))
			return errRes(rpc.StatusInternal(), fmt.Sprintf("error publishing vendor update event : %s", err.Error()))
		} else {
			logger.Info(ctx, "successfully published message to resume vendor update process", zapAfu,
				zapAfuId(afuRecord.Id), zap.String("messageId", msgId))
			return successRes(rpc.NewStatusWithoutDebug(uint32(auth.RecoverAFUProcessForActorResponse_VENDOR_UPDATE_EVENT_PUBLISHED),
				"vendor update event published"))
		}
	}

	if afuRecord.GetOverallStatus() != afu.OverallStatus_OVERALL_STATUS_STUCK {
		logger.Info(ctx, "overall process not in stuck state", zap.String(logger.ACTOR_ID_V2, actorId))
		return successRes(rpc.StatusOk())
	}

	if isDeviceRegistrationRequired(afuRecord.GetContext().GetAuthFactors()) &&
		afuRecord.GetContext().GetEncryptedPayload() == "" {
		_, err = s.afuDao.UpdateContextAndStatus(ctx, afuRecord.GetId(), afuRecord.GetContext(), afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS)
		if err != nil {
			logger.Error(ctx, "error updating overall status if device reg not complete", zap.Error(err))
			return errRes(rpc.StatusInternal(), fmt.Sprintf("error updating overall status : %s", err.Error()))
		}
		logger.Info(ctx, "marked state to in progress to allow dev reg")
		return successRes(rpc.StatusOk())
	}

	// if UpdateVendorState is `DEACTIVATED` that means process went into STUCK state while trying to reactivate device
	if afuRecord.GetVendorContext().GetState() == afu.UpdateVendorState_DEACTIVATED {
		_, err = s.afuDao.UpdateVendorContext(ctx, afuRecord.GetId(), &afu.VendorContext{
			State:       afu.UpdateVendorState_FAILED,
			FailureType: afu.UpdateVendorFailureType_REREGISTRATION_FAILED,
		})
		if err != nil {
			logger.Error(ctx, "error while updating vendor context", zap.String(logger.ACTOR_ID_V2, actorId))
			return errRes(rpc.StatusInternal(), fmt.Sprintf("error in UpdateVendorContext : %s", err.Error()))
		}
		afuRecord, err = s.GetAFUByID(ctx, afuRecord.Id)
		if err != nil {
			logger.Error(ctx, "error in GetAFUByID", zap.String(logger.ACTOR_ID_V2, actorId))
			return errRes(rpc.StatusInternal(), fmt.Sprintf("error in GetAFUByID : %s", err.Error()))
		}
	}

	switch afuRecord.GetVendorContext().GetFailureType() {
	case afu.UpdateVendorFailureType_REREGISTRATION_FAILED:
		newUpdateVendorState = afu.UpdateVendorState_FAILED
		newUpdateVendorFailureType = afu.UpdateVendorFailureType_REREGISTRATION_FAILED

	case afu.UpdateVendorFailureType_REREG_ENQUIRY_FAILED:
		// assuming that the failure cause has been rectified, so retrying enquiry
		newUpdateVendorState = afu.UpdateVendorState_REREGISTRATION_REQUESTED
		newUpdateVendorFailureType = afu.UpdateVendorFailureType_UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED

	case afu.UpdateVendorFailureType_REACTIVATION_FAILED, afu.UpdateVendorFailureType_REACTIVATION_RECOVERY_FAILED:
		// assuming reactivate device failure has been resolved
		newUpdateVendorState = afu.UpdateVendorState_REREGISTERED
		newUpdateVendorFailureType = afu.UpdateVendorFailureType_UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED

	case afu.UpdateVendorFailureType_UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED, afu.UpdateVendorFailureType_DEACTIVATION_FAILED:
		newUpdateVendorState = afuRecord.VendorContext.State
		newUpdateVendorFailureType = afuRecord.VendorContext.FailureType

	default:
		logger.Error(ctx, "unhandled case for RecoverAFUProcessForActor", zapAfu, zapAfuId(afuRecord.Id))
		return errRes(rpc.StatusInternal(),
			fmt.Sprintf("unhandled case for RecoverAFUProcessForActor : vendorUpdateFailure type is %s",
				afuRecord.GetVendorContext().GetFailureType().String()))
	}

	_, err = s.afuDao.UpdateVendorContextAndStatus(ctx, afuRecord.Id, &afu.VendorContext{
		State:                             newUpdateVendorState,
		FailureType:                       newUpdateVendorFailureType,
		InvalidTokenInReactivateAsSuccess: req.InvalidTokenInReactivateAsSuccess,
	}, afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS)
	if err != nil {
		logger.Error(ctx, "error while updating vendor context and overall status", zapAfuId(afuRecord.Id), zapAfu, zap.Error(err))
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in UpdateVendorContextAndStatus : %s", err.Error()))
	}

	if msgId, err := s.AFUVendorUpdatePublisher.PublishWithDelay(ctx, &authConsumerPb.ProcessVendorUpdateRequest{
		AfuId:   afuRecord.Id,
		ActorId: afuRecord.ActorId,
	}, recoverAFUQueueDelay); err != nil {
		logger.Error(ctx, "error while publishing message to resume vendor update process", zapAfu,
			zapAfuId(afuRecord.Id), zap.Error(err))
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error publishing vendor update event : %s", err.Error()))
	} else {
		logger.Info(ctx, "successfully published message to resume vendor update process", zapAfu,
			zapAfuId(afuRecord.Id), zap.String("messageId", msgId))
		return successRes(rpc.NewStatusWithoutDebug(uint32(auth.RecoverAFUProcessForActorResponse_VENDOR_UPDATE_EVENT_PUBLISHED),
			"vendor update event published"))
	}
}

func hasVendorCallsInitiated(status afu.UpdateVendorState) bool {
	return status != afu.UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED
}

func (s *Service) GetAuthFactorUpdatesForActor(ctx context.Context, req *auth.GetAuthFactorUpdatesForActorRequest) (*auth.GetAuthFactorUpdatesForActorResponse, error) {
	var (
		errRes = func(status *rpc.Status, debug string) (*auth.GetAuthFactorUpdatesForActorResponse, error) {
			status.SetDebugMessage(debug)
			return &auth.GetAuthFactorUpdatesForActorResponse{
				Status: status,
			}, nil
		}
		actorId = req.GetActorId()
		count   = req.GetCount()
		res     = &auth.GetAuthFactorUpdatesForActorResponse{
			Status:            rpc.StatusOk(),
			AuthFactorUpdates: make([]*afu.AuthFactorUpdate, 0),
		}
	)
	switch {
	case count <= 0:
		count = 1
	case count > GetAuthFactorsForActorMaxRecordLimit:
		count = GetAuthFactorsForActorMaxRecordLimit
	}
	afuRecords, err := s.afuDao.GetByActor(ctx, actorId, int(count), dao.WithOverallStatus(req.GetOverallStatus()))
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Info(ctx, "no record found", zapAfu)
		return errRes(rpc.StatusRecordNotFound(), "no record found")
	}
	if err != nil {
		logger.Error(ctx, "error while fetching afu records for actor", zapAfu, zap.Error(err))
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in GetByActor : %v", err.Error()))
	}
	res.AuthFactorUpdates = afuRecords
	return res, nil
}

// publishes a message for vendor update and changes vendor update state from unspecified to initiated.
func (s *Service) publishUpdateVendorMessage(ctx context.Context, afuId, actorId, credBlock, accessToken string) (*afu.AuthFactorUpdate, error) {
	msgId, err := s.AFUVendorUpdatePublisher.PublishWithDelay(ctx, &authConsumerPb.ProcessVendorUpdateRequest{
		AfuId:       afuId,
		ActorId:     actorId,
		CredBlock:   credBlock,
		AccessToken: accessToken,
	}, s.AFUVendorUpdatePublisherDelay)
	if err != nil {
		logger.Error(ctx, "error publishing vendor update message to queue", zap.Error(err),
			logger.ZapAFUId(afuId), logger.ZapAFU())
		return nil, err
	}
	logger.Info(ctx, "successfully published vendor update message to queue",
		zap.String("msgId", msgId), logger.ZapAFUId(afuId), logger.ZapAFU())

	afuRecord, err := s.updateVendorState(ctx, afuId, afu.UpdateVendorState_INITIATED)
	if err != nil {
		logger.Error(ctx, "error updating update_vendor_state", zap.Error(err), logger.ZapAFUId(afuId),
			logger.ZapAFU(), zapVendorStep)
		return nil, err
	}
	return afuRecord, nil
}

func (s *Service) ProcessDeviceDeletionAndGetNextAction(ctx context.Context, afuRecord *afu.AuthFactorUpdate) (auth.AuthFactorUpdateAction, error) {
	afuId := afuRecord.Id
	if err := s.deleteDeviceRegistration(ctx, afuRecord.ActorId, ""); err != nil {
		logger.Error(ctx, "error deleting device registration entry", zap.Error(err), zapAfu, zapAfuId(afuId))
		return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, pkgerrors.Wrap(err, "error in delete current device: %v")
	}
	afuRecord, err := s.afuDao.UpdateContextAndStatus(ctx, afuId, &afu.Context{
		DeregisterCurrentDevice: afu.RequestStatus_REQUEST_STATUS_SUCCESS,
	}, afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS)
	if err != nil {
		logger.Error(ctx, "error in UpdateContextAndStatus", zap.Error(err), zapAfuId(afuId), zapAfu)
		return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, pkgerrors.Wrap(err, "error while updaing afuRecord in DB")
	}
	nextActionDetails, err := s.ProcessAFU(ctx, afuRecord)
	if err != nil {
		logger.Error(ctx, "error in processAFU", zap.Error(err), zapAfu, zapAfuId(afuId))
		return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, pkgerrors.Wrap(err, "error in processAFU")
	}
	return nextActionDetails.GetNextAction(), nil
}

func (s *Service) ProcessUpdateDeviceAndGetNextAction(ctx context.Context, afuRecord *afu.AuthFactorUpdate, currDevice *commontypes.Device,
	updateDeviceRegStatus bool, accessToken string) (auth.AuthFactorUpdateAction, error) {
	var (
		err                error
		afuId              = afuRecord.GetId()
		afuVendorRequestId = afuRecord.GetVendorContext().GetRequestId()
		simId              = afuRecord.GetContext().GetSimId()
	)

	// update device registration DB
	if err = s.deleteOldAndRegisterNewDevice(ctx, afuRecord.GetActorId(), currDevice, afuVendorRequestId, simId); err != nil {
		logger.Error(ctx, "ReRegister error in deleteOldAndRegisterNewDevice", zap.Error(err), logger.ZapAFUId(afuId), logger.ZapAFU())
		return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, fmt.Errorf("error in redoDeviceReg: %v", err)
	}

	if updateDeviceRegStatus {
		// delete token using token value
		// prescribing client to create a higher level access token
		err = s.deleteToken(ctx, accessToken)
		if err != nil {
			logger.Error(ctx, "ReRegister error in deleting token", zap.Error(err), logger.ZapAFUId(afuId), logger.ZapAFU())
		}
	}

	// update afu Context in DB
	if afuRecord, err = s.afuDao.UpdateContextAndStatus(ctx, afuRecord.GetId(), &afu.Context{
		EpifiDeviceUpdate: afu.RequestStatus_REQUEST_STATUS_SUCCESS,
	}, afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS); err != nil {
		logger.Error(ctx, "ReRegister error in update ctx", zap.Error(err), logger.ZapAFUId(afuId), logger.ZapAFU())
		return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, fmt.Errorf("error in updateContextAndStatus: %v", err)
	}

	// get next action after epifi device update is set success
	nextActionDetails, err := s.ProcessAFU(ctx, afuRecord)
	if err != nil {
		logger.Error(ctx, "ReRegister error in ProcessAFU 2", zap.Error(err), logger.ZapAFUId(afuId), logger.ZapAFU())
		return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, err
	}

	return nextActionDetails.GetNextAction(), nil
}

func (s *Service) ProcessUserProfileUpdateAndGetNextAction(ctx context.Context, afuRecord *afu.AuthFactorUpdate) (auth.AuthFactorUpdateAction, error) {
	err := s.updateAuthFactorsInUserProfile(ctx, afuRecord)
	if err != nil {
		logger.Error(ctx, "ReRegister error in updateAuthFactorsInUserProfile", zap.Error(err), logger.ZapAFUId(afuRecord.GetId()), logger.ZapAFU())
		return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, err
	}

	// get latest updated AFU record
	afuRecord, err = s.GetAFUByID(ctx, afuRecord.GetId())
	if err != nil {
		logger.Error(ctx, "error in GetAFUByID after updating user profile", zap.Error(err), logger.ZapAFUId(afuRecord.GetId()), logger.ZapAFU())
		return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, err
	}

	nextActionDetails, err := s.ProcessAFU(ctx, afuRecord)
	if err != nil {
		logger.Error(ctx, "error in ProcessAFU after userProfile update", zap.Error(err), logger.ZapAFUId(afuRecord.GetId()), logger.ZapAFU())
		return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, err
	}

	return nextActionDetails.GetNextAction(), nil
}

// nolint:govet
func (s *Service) PublishAFUCompletionEventAndGetNextAction(ctx context.Context, afuRecord *afu.AuthFactorUpdate) (auth.AuthFactorUpdateAction, error) {
	afuId := afuRecord.Id
	var err error
	txnErr := storagev2.RunCRDBIdempotentTxn(ctx, 3, func(ctx context.Context) error {
		// get lock on afuRecord
		afuRecord, err = s.afuDao.GetByIdWithLock(ctx, afuId)
		if err != nil {
			return fmt.Errorf("error in GetByIdWithLock : %w", err)
		}
		// check if publish action still not performed
		nextActionDetails, procErr := s.ProcessAFU(ctx, afuRecord)
		if procErr != nil {
			logger.Error(ctx, "error in ProcessAFU", zapAfu, zapAfuId(afuId), zap.Error(err))
			return fmt.Errorf("error in processAFU : %w", err)
		}
		action := nextActionDetails.GetNextAction()

		if action != auth.AuthFactorUpdateAction_PUBLISH_AFU_COMPLETION_EVENT {
			return nil
		}
		// update afu Context in DB
		if _, err = s.afuDao.UpdateContextAndStatus(ctx, afuRecord.GetId(), &afu.Context{
			AfuCompletionEventPublished: true,
		}, afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS); err != nil {
			logger.Error(ctx, "error in update ctx", zapAfu, zapAfuId(afuId), zap.Error(err))
			return err
		}
		msgId, err := s.AuthFactorUpdatePublisher.Publish(ctx, &notification.AuthFactorUpdateEvent{
			ActorId:        afuRecord.ActorId,
			AuthFactors:    afuRecord.Context.AuthFactors,
			NewAuthFactors: afuRecord.Context.NewValues,
			OldAuthFactors: afuRecord.Context.CurrentValues,
		})
		if err != nil {
			logger.Error(ctx, "error while publishing afu completion event", zapAfu, zapAfuId(afuRecord.Id), zap.Error(err))
			return fmt.Errorf("error publishing afu completion event : %w", err)
		}
		logger.Info(ctx, "successfully published afu completion notification", zapAfu, zapAfuId(afuRecord.Id), zap.String("msgId", msgId))
		return nil
	})
	if txnErr != nil {
		return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, txnErr
	}
	s.sendAfuCompletedComms(ctx, afuRecord)

	// get latest updated AFU record
	afuRecord, err = s.GetAFUByID(ctx, afuRecord.GetId())
	if err != nil {
		logger.Error(ctx, "error in GetAFUByID after updating user profile", zap.Error(err), logger.ZapAFUId(afuRecord.GetId()), logger.ZapAFU())
		return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, err
	}
	nextActionDetails, err := s.ProcessAFU(ctx, afuRecord)
	if err != nil {
		logger.Error(ctx, "error in ProcessAFU after userProfile update", zap.Error(err), logger.ZapAFUId(afuRecord.GetId()), logger.ZapAFU())
		return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, err
	}
	return nextActionDetails.GetNextAction(), nil
}

func (s *Service) sendAfuCompletedComms(ctx context.Context, afuRecord *afu.AuthFactorUpdate) {
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		if isPhoneNumUpdate(afuRecord.GetContext().GetAuthFactors()) {
			_, _ = s.sendPhoneUpdateSms(ctx, afuRecord)
		}

		if isEmailUpdate(afuRecord.GetContext().GetAuthFactors()) {
			_, _ = s.sendEmailUpdateEmail(ctx, afuRecord)
		}
	})
}

// nolint: funlen
func (s *Service) updateAuthFactorsInUserProfile(ctx context.Context, afuRecord *afu.AuthFactorUpdate) error {
	// fetch actor to get user ID
	actorRes, err := s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{
		Id: afuRecord.GetActorId(),
	})
	if err != nil {
		return err
	}

	// update user profile with new values
	newValues := afuRecord.GetContext().GetNewValues()
	resp, err := s.UserClient.UpdateUser(ctx, &user.UpdateUserRequest{
		User: &user.User{
			Id: actorRes.GetActor().GetEntityId(),
			Profile: &user.Profile{
				Email:       newValues.GetEmail(),
				PhoneNumber: newValues.GetPhoneNumber(),
			},
		},
		UpdateMask: []user.UserFieldMask{
			user.UserFieldMask_EMAIL, user.UserFieldMask_PHONE_NUMBER,
		},
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error in updating user profile", zapAfu, zapAfuId(afuRecord.Id), zap.Error(err))
		return fmt.Errorf("error in UpdateUser: %w", err)
	}

	updateAccRes, err := s.savingsClient.UpdateAccount(ctx, &savings.UpdateAccountRequest{
		Identifier: &savings.UpdateAccountRequest_PrimaryAccountHolder{
			PrimaryAccountHolder: actorRes.GetActor().GetEntityId(),
		},
		PhoneNumber: newValues.GetPhoneNumber(),
		EmailId:     newValues.GetEmail(),
		UpdateMask: []savings.AccountFieldMask{
			savings.AccountFieldMask_EMAIL_ID,
			savings.AccountFieldMask_PHONE_NUMBER,
		},
	})
	if err != nil {
		logger.Error(ctx, "error updating savings accont details", zapAfu, zapAfuId(afuRecord.Id), zap.Error(err))
		return fmt.Errorf("error in UpdateUser: %w", err)
	}
	if updateAccRes.GetStatus().GetCode() != uint32(savings.UpdateAccountResponse_OK) {
		logger.Error(ctx, "received non-OK response in update savings account details", zapAfu, zapAfuId(afuRecord.Id),
			zap.Uint32("status", updateAccRes.GetStatus().GetCode()))
		return fmt.Errorf("received non-OK status in updateAccount response - %v", updateAccRes.GetStatus().GetCode())
	}

	// mark profile update as success
	res, err := s.AuthFactorsProfileUpdate(ctx, &auth.AuthFactorsProfileUpdateRequest{
		AuthFactorUpdateId: afuRecord.GetId(),
		RequestStatus:      afu.RequestStatus_REQUEST_STATUS_SUCCESS,
	})
	if err != nil {
		logger.Error(ctx, "error in updating afu profile update status", zap.Error(err))
		return fmt.Errorf("error in AuthFactorsProfileUpdate: %v", err)
	}
	if !res.GetStatus().IsSuccess() {
		return fmt.Errorf("non-ok code in AuthFactorsProfileUpdate: %v", res.GetStatus())
	}
	return nil
}

// nextClientAction evaluates the next action for the client in the AFU process in progress.
// If there are no client actions to take, returns AuthFactorUpdateAction_NO_ACTION.
func (s *Service) nextClientAction(ctx context.Context, afuRecord *afu.AuthFactorUpdate) (*auth.AuthFactorUpdateNextActionDetails, error) {
	res := func(nextAction auth.AuthFactorUpdateAction, nextSubActions []auth.AuthFactorUpdateAction, failureReason afu.FailureReason, deeplink *deeplinkPb.Deeplink, err error) (*auth.AuthFactorUpdateNextActionDetails, error) {
		return &auth.AuthFactorUpdateNextActionDetails{
			NextAction:     nextAction,
			NextSubActions: nextSubActions,
			FailureReason:  failureReason,
			Deeplink:       deeplink,
		}, err
	}
	afuId := afuRecord.GetId()
	afuCtx := afuRecord.GetContext()
	afuVCtx := afuRecord.GetVendorContext()

	nextAction, err := s.ekycHandler(ctx, afuRecord)
	if err == nil || !(errors.Is(err, skipActionError) || errors.Is(err, noActionError)) {
		return nextAction, err
	}

	nextCreds, err := s.nextCredsForActorAuthentication(ctx, afuId)
	if errors.Is(err, actorAuthenticationTerminallyFailedError) {
		logger.Info(ctx, "terminal error with authenticating actor", zap.Error(err))
		return res(auth.AuthFactorUpdateAction_SHOW_FAILURE, nil, afuRecord.GetFailureReason(), nil, nil)
	}
	if err != nil {
		logger.Error(ctx, "error with authenticating actor", zap.Error(err))
		return res(0, nil, 0, nil, err)
	}
	logger.Debug(ctx, fmt.Sprintf("next creds: %v", nextCreds))
	// when only one cred is to be verified to pass the level, next action can be directly served
	if len(nextCreds) == 1 {
		cred := nextCreds[0]
		action, ok := credToAction[cred]
		if !ok {
			logger.Error(ctx, fmt.Sprintf("invalid next credential: %v", cred))
			return res(0, nil, 0, nil, fmt.Errorf("invalid next credential: %v", cred))
		}
		return res(action, nil, 0, nil, nil)
	}
	if len(nextCreds) > 1 {
		nextSubActions := []auth.AuthFactorUpdateAction{}
		for _, cred := range nextCreds {
			action, ok := credToAction[cred]
			if !ok {
				logger.Error(ctx, "invalid next credential", zap.String("cred", cred.String()))
				return res(0, nil, 0, nil, fmt.Errorf("invalid next credential: %v", cred))
			}
			nextSubActions = append(nextSubActions, action)
		}
		return res(auth.AuthFactorUpdateAction_ACTOR_AUTHENTICATION_LEVEL, nextSubActions, 0, nil, nil)
	}

	// check user confirmation status
	if afuCtx.GetUserConfirmationStatus() != afu.UserConfirmationStatus_USER_ACCEPTED {
		return res(auth.AuthFactorUpdateAction_USER_CONFIRMATION, nil, 0, &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_AFU_FINAL_CONFIRMATION,
		}, nil)
	}

	// check device registration status
	if isDeviceRegistrationRequired(afuCtx.GetAuthFactors()) && !hasVendorCallsInitiated(afuVCtx.GetState()) &&
		afuCtx.EpifiDeviceUpdate != afu.RequestStatus_REQUEST_STATUS_SUCCESS {
		return res(auth.AuthFactorUpdateAction_DEVICE_REGISTRATION, nil, 0, s.afuDeviceRegistrationDeeplink(), nil)
	}

	return res(auth.AuthFactorUpdateAction_NO_ACTION, nil, 0, nil, nil)
}

// validateDeviceId calls Device Registration to fetch details with
// actorId and deviceId. Returns true if actor and device details match.
func (s *Service) validateDevice(ctx context.Context, actorId string, device *commontypes.Device) (isDeviceValidated bool, err error) {
	r, err := s.GetDeviceAuth(ctx, &auth.GetDeviceAuthRequest{
		Device:  device,
		ActorId: actorId,
	})
	if err != nil {
		return false, fmt.Errorf("error in GetRegDevice: %v", err)
	}
	if r.GetStatus().IsRecordNotFound() || r.DeviceRegStatus != auth.DeviceRegistrationStatus_REGISTERED {
		return false, nil
	}
	if r.GetActorId() == actorId {
		return true, nil
	}
	return false, nil
}

// deviceRegistrationStatus is adapter for validateDevice().
// maps validateDevice response to *afu.CredentialStatus.
func (s *Service) deviceRegistrationStatus(ctx context.Context, actorId string, device *commontypes.Device) (*afu.CredentialStatus, error) {
	isDeviceValidated, err := s.validateDevice(ctx, actorId, device)
	if err != nil {
		return nil, fmt.Errorf("error in validate device: %v", err)
	}
	return &afu.CredentialStatus{
		Credential: afu.Credential_REGISTERED_DEVICE_VALIDATION,
		Status:     isDeviceValidatedToStatusMap[isDeviceValidated],
	}, nil
}

func (s *Service) GetAFUByID(ctx context.Context, afuID string) (*afu.AuthFactorUpdate, error) {
	res, err := s.afuDao.GetById(ctx, afuID)
	if err != nil && !storagev2.IsRecordNotFoundError(err) {
		logger.Error(ctx, "error in get afu", zap.Error(err), logger.ZapAFU(), logger.ZapAFUId(afuID))
	}
	return res, err
}

func (s *Service) GetLastAFUForActor(ctx context.Context, actorId string) (*afu.AuthFactorUpdate, error) {
	records, err := s.afuDao.GetByActor(ctx, actorId, 1)
	if err != nil {
		return nil, err
	}
	return records[0], nil
}

func (s *Service) initContext() *afu.Context {
	return &afu.Context{
		AuthFactors:           []afu.AuthFactor{},
		CredentialStatuses:    []*afu.CredentialStatus{},
		NewValues:             &afu.AuthFactorValues{},
		VendorRequestStatuses: []*afu.VendorRequestStatus{},
		CurrentValues:         &afu.AuthFactorValues{},
	}
}

// isTerminalState tells whether the given status is eligible for further processing for AFU
func (s *Service) isTerminalState(status afu.OverallStatus) bool {
	return status == afu.OverallStatus_OVERALL_STATUS_COMPLETED ||
		status == afu.OverallStatus_OVERALL_STATUS_FAILED
}

// updateVendor signals UpdateVendorProcess to start the processing.
//
// It checks if the given AFU is in terminal state. If it is, it returns error.
// If it is not, it initiates AFU processing for vendor stages.
func (s *Service) updateVendor(ctx context.Context, afuRecord *afu.AuthFactorUpdate) error {
	vendorCtx := afuRecord.VendorContext
	currState := vendorCtx.State
	var err error
	vendorUpdateRecord := afuRecord
	afuId := afuRecord.GetId()
	if currState == afu.UpdateVendorState_FAILED {
		logger.Error(ctx, "update vendor process is already in failed state", logger.ZapAFUId(afuId), logger.ZapAFU(), zapVendorStep)
		return fmt.Errorf("error updating vendor : process already in failed state")
	}
	if currState == afu.UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED {
		vendorUpdateRecord, err = s.afuDao.UpdateVendorContext(ctx, afuId, &afu.VendorContext{
			State: afu.UpdateVendorState_INITIATED,
		})
		if err != nil {
			logger.Error(ctx, "error updating update_vendor_state", zap.Error(err), logger.ZapAFUId(afuId),
				logger.ZapAFU(), zapVendorStep)
			return err
		}
		logger.Info(ctx, "starting update vendor process", logger.ZapAFUId(afuId), logger.ZapAFU(), zapVendorStep)
	} else {
		logger.Info(ctx, "resuming update vendor process", logger.ZapAFUId(afuId), logger.ZapAFU(), zapVendorStep,
			zap.String("currState", currState.String()))
	}
	return s.UpdateVendorProcess(ctx, afuId, afuRecord.ActorId, afuRecord.GetContext(), vendorUpdateRecord.GetVendorContext(), afuRecord.GetAttemptsHistory(), nil)
}

// UpdateVendorProcess updates the vendor state for the given afuId by sending the vendor request to the vendor.
//
// And it subsequently updates the vendor context in the afu record.
//
//nolint:funlen
func (s *Service) UpdateVendorProcess(ctx context.Context, afuId, actorId string, afuCtx *afu.Context, vendorCtx *afu.VendorContext, afuAttempts *afu.AttemptsHistory, device *vgauthPb.Auth) error {
	var (
		err    error
		afuVal *afu.AuthFactorUpdate
	)
	logger.Info(ctx, "trying afu with UpdateVendorProcess", zap.String("vendor_state", vendorCtx.GetState().String()))
	updateState := vendorCtx.State
	if device == nil {
		device, err = s.fetchDeviceAuthDetails(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "error encountered while fetching device auth details", logger.ZapAFUId(afuId),
				logger.ZapAFU(), zap.Error(err), zapVendorStep)
			return err
		}
		afuCtx.CurrentValues.DeviceId = device.GetDeviceId()
	}

	switch updateState {

	case afu.UpdateVendorState_INITIATED:
		var retryableErr bool
		err, retryableErr = s.deactivateDevice(ctx, device, afuCtx.CurrentValues.PhoneNumber, afuCtx.CurrentValues.Email, afuId)
		if err != nil {
			if retryableErr {
				return err
			}
			_ = s.updateVendorStateToFailure(ctx, afuId, afu.UpdateVendorFailureType_DEACTIVATION_FAILED)
			return err
		}
		afuVal, err = s.updateVendorState(ctx, afuId, afu.UpdateVendorState_DEACTIVATED)
		if err != nil {
			return fmt.Errorf("error updating vendor context : %w", err)
		}
		return s.UpdateVendorProcess(ctx, afuId, actorId, afuCtx, afuVal.VendorContext, afuAttempts, device)

	case afu.UpdateVendorState_DEACTIVATED:
		if afuCtx.NewValues.DeviceId != "" {
			device.DeviceId = afuCtx.NewValues.DeviceId
		}
		err = s.ReregisterDeviceVendorInit(ctx, actorId, vendorCtx.RequestId, afuCtx, afuAttempts, afuCtx.CredBlock,
			device, afuCtx.NewValues.PhoneNumber, afuCtx.NewValues.Email, afuId,
			vendorCtx.GetVendorRequestStartedAt().AsTime())
		if err != nil {
			_ = s.updateVendorReRegistrationStatus(ctx, afuId, afu.RequestStatus_REQUEST_STATUS_FAIL)
			_ = s.updateVendorStateToFailure(ctx, afuId, afu.UpdateVendorFailureType_REREGISTRATION_FAILED)
			return err
		}
		afuVal, err = s.updateVendorState(ctx, afuId, afu.UpdateVendorState_REREGISTRATION_REQUESTED)
		if err != nil {
			return fmt.Errorf("error updating vendor context : %w", err)
		}
		return s.UpdateVendorProcess(ctx, afuId, actorId, afuCtx, afuVal.VendorContext, afuAttempts, device)

	case afu.UpdateVendorState_REREGISTRATION_REQUESTED:
		// waiting before enquiry to avoid race condition between callback and enquiry
		if vendorCtx.GetVendorRequestStartedAt().AsTime().Add(3 * time.Minute).After(time.Now()) {
			return errReRegInProgress
		}
		device.DeviceId = afuCtx.NewValues.DeviceId
		err = s.EnquireAndUpdateReRegStatus(ctx, vendorCtx.RequestId, device, afuCtx.NewValues.PhoneNumber, afuId)
		if err != nil {
			return err
		}
		afuVal, err = s.GetAFUByID(ctx, afuId)
		if err != nil {
			return err
		}
		return s.UpdateVendorProcess(ctx, afuId, actorId, afuCtx, afuVal.VendorContext, afuAttempts, device)

	case afu.UpdateVendorState_REREGISTERED:
		device.DeviceId = afuCtx.NewValues.DeviceId
		errRD, retryableErr, ambiguousResp := s.ReactivateDevice(ctx, device, afuCtx.NewValues.PhoneNumber, afuCtx.NewValues.Email, afuId, vendorCtx.InvalidTokenInReactivateAsSuccess)
		if errRD != nil && !ambiguousResp {
			if retryableErr {
				return errRD
			}
			_ = s.updateVendorStateToFailure(ctx, afuId, afu.UpdateVendorFailureType_REACTIVATION_FAILED)
			return errRD
		}
		if ambiguousResp {
			afuVal, err = s.updateVendorState(ctx, afuId, afu.UpdateVendorState_AMBIGUOUS_REACTIVATION)
		} else {
			afuVal, err = s.updateVendorState(ctx, afuId, afu.UpdateVendorState_COMPLETED)
		}
		if err != nil {
			return fmt.Errorf("error updating vendor context : %w", err)
		}
		return s.UpdateVendorProcess(ctx, afuId, actorId, afuCtx, afuVal.VendorContext, afuAttempts, device)

	case afu.UpdateVendorState_AMBIGUOUS_REACTIVATION:
		return s.recoverFromAmbiguousActivation(ctx, device, vendorCtx, afuCtx, afuId)

	case afu.UpdateVendorState_COMPLETED:
		s.signOutFromOldPhoneNumber(ctx, afuCtx)
		metrics.RecordBankUpdateTime(ctx, afuCtx, time.Since(vendorCtx.GetVendorRequestStartedAt().AsTime()))
		return nil

	default:
		logger.Error(ctx, "unknown state for vendor update", logger.ZapAFU(), logger.ZapAFUId(afuId),
			zap.String("state", updateState.String()), zapVendorStep)
		return fmt.Errorf("unknown state for vendor update, state : %v", updateState)
	}
}

func (s *Service) signOutFromOldPhoneNumber(ctx context.Context, afuCtx *afu.Context) {
	if !lo.Contains(afuCtx.GetAuthFactors(), afu.AuthFactor_PHONE_NUM) {
		return
	}
	_, err := s.tokenStoresDao.ExpireTokenByPhoneNumber(ctx, auth.TokenType_REFRESH_TOKEN, model.TokenDeleted, afuCtx.GetCurrentValues().GetPhoneNumber(), auth.TokenDeletionReason_TOKEN_DELETION_REASON_SIGN_OUT_OLD_PHONE_NUM_IN_AFU)
	if err != nil && !storagev2.IsRecordNotFoundError(err) {
		logger.Error(ctx, "error in deleting refresh tokens", zap.Error(err))
	}
}

func (s *Service) recoverFromAmbiguousActivation(ctx context.Context, device *vgauthPb.Auth, vendorCtx *afu.VendorContext, afuCtx *afu.Context, afuId string) error {
	reRegistrationStatus := vendorCtx.GetReRegistrationStatus()
	switch reRegistrationStatus {

	case afu.RequestStatus_REQUEST_STATUS_SUCCESS:
		// if re-reg is success, deactivation with new details to bring it to a known and unambiguous state
		err, retryableErr := s.deactivateDevice(ctx, device, afuCtx.GetNewValues().GetPhoneNumber(), afuCtx.GetNewValues().GetEmail(), afuId)
		if err != nil {
			if retryableErr {
				return err
			}
			_ = s.updateVendorStateToFailure(ctx, afuId, afu.UpdateVendorFailureType_REACTIVATION_RECOVERY_FAILED)
			return err
		}
		// since deactivation and device re-registration are successful,
		// therefore afu flow has now reached an equivalent REREGISTERED state
		_, err = s.updateVendorState(ctx, afuId, afu.UpdateVendorState_REREGISTERED)
		if err != nil {
			return fmt.Errorf("error updating vendor context : %w", err)
		}
		return fmt.Errorf("activation recovery by updating vendorCtx state to REREGISTERED")

	case afu.RequestStatus_REQUEST_STATUS_FAIL:
		// if re-reg is fail, marking vendor state as FAILED with failure_type as REREGISTRATION_FAILED
		// this allows reverting to old state, by deactivating and re-activating with old details
		err := s.updateVendorStateToFailure(ctx, afuId, afu.UpdateVendorFailureType_REREGISTRATION_FAILED)
		if err != nil {
			return fmt.Errorf("error updating vendor context : %w", err)
		}
		return fmt.Errorf("activation recovery by updating vendorCtx state to FAILED and proceeding to reactivate with old details")

	default:
		// if re-registration status is UNSPECIFIED or unexpected status proceeds to marks users as stuck
		if err := s.updateVendorStateToFailure(ctx, afuId, afu.UpdateVendorFailureType_REACTIVATION_FAILED); err != nil {
			return err
		}
		return fmt.Errorf("failed to recover ambgiuous reactivation as re-reg status is unspecified")
	}
}

// recoverActivationWithOldDetails reactivates device with current details that were present before afu attempt
// nolint:govet
func (s *Service) recoverActivationWithOldDetails(ctx context.Context, afuCtx *afu.Context, vendorCtx *afu.VendorContext, afuId string, device *vgauthPb.Auth) (err error, retryableErr bool, ambiguousResp bool) {
	// first attempting deactivation to restore device state in a known and unambiguous state
	err, retryableErr = s.deactivateDevice(ctx, device, afuCtx.CurrentValues.PhoneNumber, afuCtx.CurrentValues.Email, afuId)
	if err != nil {
		logger.Error(ctx, "deactivation failed while recovering activation", zap.Error(err), zapAfu, zapAfuId(afuId))
		return err, retryableErr, false
	}
	// once deactivation is success, attempting reactivation
	err, retryableErr, ambiguousResp = s.ReactivateDevice(ctx, device, afuCtx.CurrentValues.PhoneNumber, afuCtx.CurrentValues.Email, afuId, vendorCtx.InvalidTokenInReactivateAsSuccess)
	if err != nil {
		logger.Error(ctx, "reactivation failed while recovering activation", zap.Error(err), zapAfu, zapAfuId(afuId))
		return err, retryableErr, ambiguousResp
	}
	return nil, false, false
}

// updateVendorState updates vendor context state for the afu record
// nolint: govet
func (s *Service) updateVendorState(ctx context.Context, afuId string, newState afu.UpdateVendorState) (*afu.AuthFactorUpdate, error) {
	var (
		err       error
		afuRecord *afu.AuthFactorUpdate
	)
	err = storagev2.RunCRDBIdempotentTxn(ctx, 3, func(txnCtx context.Context) error {
		afuRecord, err = s.afuDao.GetByIdWithLock(txnCtx, afuId)
		if err != nil {
			return fmt.Errorf("error while fetching afuRecord : %w", err)
		}
		allowed, err := s.checkIfStateUpdateForVendorUpdateProcessRequired(newState, afu.UpdateVendorFailureType_UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED,
			afuRecord.GetVendorContext().GetState(), afuRecord.GetVendorContext().GetFailureType())
		if err != nil {
			return fmt.Errorf("error in checkIfStateUpdateForVendorUpdateProcessRequired : %w", err)
		}
		if !allowed {
			return nil
		}
		vCtx := &afu.VendorContext{
			State: newState,
		}
		if newState == afu.UpdateVendorState_INITIATED {
			vCtx.VendorRequestStartedAt = timestamppb.New(s.TimeClient.Now())
		}
		afuRecord, err = s.afuDao.UpdateVendorContext(txnCtx, afuId, vCtx)
		if err != nil {
			return fmt.Errorf("error while updating vendor_context : %w", err)
		}
		return nil
	})
	if err != nil {
		logger.Error(ctx, "error updating update_vendor_state", zap.Error(err),
			zap.String("state", newState.String()), logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
	} else {
		logger.Info(ctx, "successfully updating update_vendor_state", logger.ZapAFUId(afuId),
			zap.String("state", newState.String()), logger.ZapAFU(), zapVendorStep)
	}
	return afuRecord, err
}

func (s *Service) updateVendorStateToFailure(ctx context.Context, afuId string, failureType afu.UpdateVendorFailureType) error {
	err := storagev2.RunCRDBIdempotentTxn(ctx, 3, func(txnCtx context.Context) error {
		afuRecord, err := s.afuDao.GetByIdWithLock(txnCtx, afuId)
		if err != nil {
			return fmt.Errorf("error while fetching afuRecord : %w", err)
		}
		allowed, err := s.checkIfStateUpdateForVendorUpdateProcessRequired(afu.UpdateVendorState_FAILED, failureType,
			afuRecord.GetVendorContext().GetState(), afuRecord.GetVendorContext().GetFailureType())
		if err != nil {
			return fmt.Errorf("error in checkIfStateUpdateForVendorUpdateProcessRequired : %w", err)
		}
		if err = s.ensureAFUInProgress(txnCtx, afuRecord.GetId(), afuRecord.GetOverallStatus()); err != nil {
			return err
		}
		if !allowed {
			return nil
		}
		_, err = s.afuDao.UpdateVendorContext(txnCtx, afuId, &afu.VendorContext{
			State:       afu.UpdateVendorState_FAILED,
			FailureType: failureType,
		})
		if err != nil {
			return fmt.Errorf("error while updating vendor context : %w", err)
		}
		return nil
	})
	if err != nil {
		logger.Error(ctx, "error updating update_vendor_state", zap.Error(err), zap.String("failureType", failureType.String()),
			zap.String("state", afu.UpdateVendorState_FAILED.String()), logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
		return err
	} else {
		logger.Info(ctx, "successfully updated update_vendor_state", zap.String("failureType", failureType.String()),
			zap.String("state", afu.UpdateVendorState_FAILED.String()), logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
		return nil
	}
}

func (s *Service) updateATMPINCredStatusInCtx(ctx context.Context, afuId string, credStatus afu.VerificationStatus) error {
	afuRecord, err := s.GetAFUByID(ctx, afuId)
	if err != nil {
		return fmt.Errorf("error while fetching afuRecord : %w", err)
	}
	if afuRecord.GetFailureReason() != afu.FailureReason_FAILURE_REASON_UNSPECIFIED {
		isFailureRetryable, ok := isFailureReasonsRetryable[afuRecord.GetFailureReason()]
		if !ok || !isFailureRetryable {
			credStatus = afu.VerificationStatus_VERIFICATION_STATUS_TERMINALLY_FAILED
		}
	}
	uc, ucErr := upsertCredentialStatus(afuRecord.GetContext().GetCredentialStatuses(), &afu.CredentialStatus{
		Credential: afu.Credential_ATM_PIN_VALIDATION,
		Status:     credStatus,
	}, false)
	switch {
	case errors.Is(ucErr, identicalVerificationStatusError):
		// if the credential status is being updated to same state proceed for next action
		return nil
	case err != nil:
		return fmt.Errorf("error in upsert cred status: %w", err)
	default:
		_, err = s.afuDao.UpdateContextAndStatus(ctx, afuId, &afu.Context{
			CredentialStatuses: uc,
		}, afuRecord.GetOverallStatus())
		if err != nil {
			return fmt.Errorf("error while updating context and status : %w", err)
		}
	}
	return nil
}

func (s *Service) updateVendorStateAndAddDeviceTokenInTx(ctx context.Context, afuId string, newState afu.UpdateVendorState, deviceToken string) (*afu.AuthFactorUpdate, error) {
	var (
		att *afu.AuthFactorUpdate
		err error
	)
	if err = storagev2.RunCRDBIdempotentTxn(ctx, txnRetries, func(ctx context.Context) error {
		att, err = s.updateVendorStateAndAddDeviceToken(ctx, afuId, newState, deviceToken)
		return err
	}); err != nil {
		if !errors.Is(err, errUpdateVendorNotAllowed) {
			logger.Error(ctx, "AFU DevReReg error in update success state in txn", zap.Error(err), logger.ZapAFUId(afuId))
		}
		return nil, fmt.Errorf("error in update re reg success in txn: %w", err)
	}
	return att, nil
}

func (s *Service) updateVendorStateAndAddDeviceToken(ctx context.Context, afuId string, newState afu.UpdateVendorState, deviceToken string) (*afu.AuthFactorUpdate, error) {
	afuRecord, err := s.GetAFUByID(ctx, afuId)
	if err != nil {
		return nil, err
	}
	if err = s.canUpdateVendorState(ctx, afu.UpdateVendorState_REREGISTERED,
		0, afuRecord.GetVendorContext().GetState(), afuRecord.GetVendorContext().GetFailureType()); err != nil {
		return nil, err
	}
	if err = s.ensureAFUInProgress(ctx, afuRecord.GetId(), afuRecord.GetOverallStatus()); err != nil {
		return nil, err
	}
	afuRecord, err = s.afuDao.UpdateContextAndVendorContext(ctx, afuId, &afu.Context{
		NewValues: &afu.AuthFactorValues{
			DeviceToken: deviceToken,
		},
	}, &afu.VendorContext{
		State: newState,
	})
	if err != nil {
		logger.Error(ctx, "error updating update_vendor_state", zap.Error(err),
			zap.String("state", newState.String()), logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
	} else {
		logger.Info(ctx, "successfully updated update_vendor_state", zap.String("state", newState.String()),
			logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
	}
	return afuRecord, err
}

func (s *Service) updateVendorReRegistrationStatus(ctx context.Context, afuId string, newStatus afu.RequestStatus) error {
	var (
		err       error
		afuRecord *afu.AuthFactorUpdate
	)
	err = storagev2.RunCRDBIdempotentTxn(ctx, txnRetries, func(txnCtx context.Context) error {
		afuRecord, err = s.afuDao.GetByIdWithLock(txnCtx, afuId)
		if err != nil {
			return fmt.Errorf("error while fetching afuRecord : %w", err)
		}
		allowed := shouldUpdateVendorReRegState(newStatus, afuRecord.GetVendorContext().GetReRegistrationStatus())
		if !allowed {
			return nil
		}
		afuRecord, err = s.afuDao.UpdateVendorContext(txnCtx, afuId, &afu.VendorContext{
			ReRegistrationStatus: newStatus,
		})
		if err != nil {
			return fmt.Errorf("error while updating vendor_context : %w", err)
		}
		return nil
	})
	if err != nil {
		logger.Error(ctx, "error updating afu VendorContext ReRegistrationStatus", zap.Error(err),
			zap.String("status", newStatus.String()), logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
	} else {
		logger.Info(ctx, "successfully updating afu VendorContext ReRegistrationStatus", logger.ZapAFUId(afuId),
			zap.String("state", newStatus.String()), logger.ZapAFU(), zapVendorStep)
	}
	return err
}

func (s *Service) deactivateDevice(ctx context.Context, device *vgauthPb.Auth, phone *commontypes.PhoneNumber, emailId, afuId string) (err error, retryableErr bool) {
	vgRes, err := s.VendorGateway.DeRegisterDevice(ctx, &vg.DeRegisterDeviceRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendorMap[afuVendor],
		},
		DeviceToken:        device.GetDeviceToken(),
		DeviceId:           device.GetDeviceId(),
		UserProfileId:      device.GetUserProfileId(),
		Phone:              phone,
		Email:              emailId,
		DeRegistrationType: vg.DeRegisterDeviceRequest_TEMPORARY,
	})
	if err != nil {
		logger.Error(ctx, "error deactivating device", zap.Error(err), logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
		return fmt.Errorf("error deactivating device : %w", err), true
	}
	resStatus := vgRes.GetStatus()
	if resStatus.GetCode() == uint32(vg.DeRegisterDeviceResponse_OK) ||
		resStatus.GetCode() == uint32(vg.DeRegisterDeviceResponse_DEVICE_ALREADY_DEACTIVATED) {
		// wait for 5 seconds post deactivation to avoid race condition arising at vendor
		time.Sleep(5 * time.Second)
		logger.Info(ctx, "received OK response for device deactivation request", logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
		return nil, false
	}
	err = fmt.Errorf("DeRegisterDevice failed. code: %v, debug: %v", resStatus.GetCode(), resStatus.GetDebugMessage())
	logger.Error(ctx, "deactivate device failed", zap.Error(err), logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
	if resStatus.GetCode() == uint32(vg.DeRegisterDeviceResponse_INTERNAL) {
		return err, true
	} else {
		return err, false
	}
}

// isFailureReasonsRetryable is a map for failure reasons
// which are retryable in actor authentication v2 flow
var isFailureReasonsRetryable = map[afu.FailureReason]bool{
	afu.FailureReason_NO_SMS_RECEIVED:          true,
	afu.FailureReason_MOBILE_MISMATCH:          true,
	afu.FailureReason_PAYLOAD_MISMATCH:         true,
	afu.FailureReason_INTERNAL_ERROR_AT_VENDOR: true,
	afu.FailureReason_WRONG_ATM_PIN:            true,
}

// nolint: gocritic, funlen
func (s *Service) ReregisterDeviceVendorInit(ctx context.Context, actorId string, requestId string, afuCtx *afu.Context,
	afuAttempts *afu.AttemptsHistory, credBlock string, device *vgauthPb.Auth, phone *commontypes.PhoneNumber,
	emailId string, afuId string, vendorCallInitAt time.Time) (err error) {
	var (
		vgAuthFactors = map[afu.AuthFactor]vg.AuthFactor{
			afu.AuthFactor_DEVICE:    vg.AuthFactor_DEVICE,
			afu.AuthFactor_EMAIL:     vg.AuthFactor_EMAIL,
			afu.AuthFactor_PHONE_NUM: vg.AuthFactor_PHONE_NUM,
			afu.AuthFactor_SIM:       vg.AuthFactor_SIM,
		}

		failureReasons = map[vg.AuthFactorUpdateResponse_Status]afu.FailureReason{
			vg.AuthFactorUpdateResponse_NO_SMS_RECEIVED:            afu.FailureReason_NO_SMS_RECEIVED,
			vg.AuthFactorUpdateResponse_MOBILE_NUMBER_MISMATCH:     afu.FailureReason_MOBILE_MISMATCH,
			vg.AuthFactorUpdateResponse_ENCRYPTED_PAYLOAD_MISMATCH: afu.FailureReason_PAYLOAD_MISMATCH,
			vg.AuthFactorUpdateResponse_INTERNAL_ERROR_AT_VENDOR:   afu.FailureReason_INTERNAL_ERROR_AT_VENDOR,
			vg.AuthFactorUpdateResponse_INVALID_CRED_BLOCK:         afu.FailureReason_INVALID_CRED_BLOCK,
		}

		changingEntities []vg.AuthFactor
		vgRes            *vg.AuthFactorUpdateResponse
		backoff          time.Duration
		// setting default value true, such that first attempt can be made
		tryReRegInit = true

		passFlagForSingleAFU = func(afuCtx *afu.Context) (vg.AFUFlowType, error) {
			isAtmPinRequired, checkErr := s.isAtmPinRequired(ctx, afuCtx, actorId)
			if checkErr != nil {
				return 0, checkErr
			}
			if isAtmPinRequired {
				if credBlock != "" {
					return vg.AFUFlowType_AFU_FLOW_TYPE_SINGLE_AUTH_FACTOR_UPDATE_WITH_CREDBLOCK, nil
				} else {
					return vg.AFUFlowType_AFU_FLOW_TYPE_SINGLE_AUTH_FACTOR_UPDATE_WITHOUT_CREDBLOCK, nil
				}
			} else {
				return vg.AFUFlowType_AFU_FLOW_TYPE_SINGLE_AUTH_FACTOR_UPDATE_WITHOUT_CREDBLOCK, nil
			}
		}

		// Sorry about this hack. More details: https://monorail.pointz.in/p/fi-app/issues/detail?id=64751
		// TODO(aditya): remove this code once the user is unblocked
		overrideAFUFlowType = func(actorId string, authFactors []afu.AuthFactor) (vg.AFUFlowType, bool) {
			if actorId != "AC2107154LcDah5pScegtm4MId7JGA==" {
				return 0, false
			}
			comb, _ := getCombination.GetScenario(authFactors)
			// enabling this only for a week. User must complete the AFU in a week.
			isReqExpired := time.Since(time.Date(2024, 9, 24, 0, 0, 0, 0, datetime.IST)) > (30 * 24 * time.Hour)
			logger.Info(ctx, fmt.Sprintf("actor trying afu flow type override; comb: %v, expiry: %v", comb, isReqExpired))

			if (comb == afu.Combination_UPDATE_DEVICE_PHONE_NUM || comb == afu.Combination_UPDATE_PHONE_NUM) && !isReqExpired {
				logger.Info(ctx, "overriding afu flow type")
				return vg.AFUFlowType_AFU_FLOW_TYPE_SINGLE_AUTH_FACTOR_UPDATE_WITHOUT_CREDBLOCK, true
			}
			return 0, false
		}
	)

	for _, factor := range afuCtx.GetAuthFactors() {
		changingEntities = append(changingEntities, vgAuthFactors[factor])
	}

	afuFlowType, err := passFlagForSingleAFU(afuCtx)
	if err != nil {
		return err
	}

	if f, ok := overrideAFUFlowType(actorId, afuCtx.GetAuthFactors()); ok {
		afuFlowType = f
	}
	vendorReqID := requestId
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=80432
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=85009
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=89391
	// Appending mobile number in changed entities as per bank's suggestion
	isReqExpired := time.Since(s.Conf.AFU().AFUMobileUpdateOverrideConfig().ActiveFrom().In(datetime.IST)) > (15 * 24 * time.Hour)

	if lo.Contains(s.Conf.AFU().AFUMobileUpdateOverrideConfig().ActorIds().ToStringArray(), actorId) && !isReqExpired {
		logger.Info(ctx, "appending mobile to changed entities")
		changingEntities = append(changingEntities, vg.AuthFactor_PHONE_NUM)
		changingEntities = lo.Uniq(changingEntities)
	}
	for i := 1; tryReRegInit; i++ {
		vgRes, err = s.VendorGateway.AuthFactorUpdate(ctx, &vg.AuthFactorUpdateRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: vendorMap[afuVendor],
			},
			RequestId:        vendorReqID,
			DeviceDetails:    device,
			Phone:            phone,
			Email:            emailId,
			Payload:          afuCtx.EncryptedPayload,
			CredBlock:        credBlock,
			VendorCardId:     afuCtx.VendorCardId,
			ChangingEntities: changingEntities,
			AfuFlowType:      afuFlowType,
		})
		if err != nil {
			logger.Error(ctx, "error calling re-register device", zap.Error(err), logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
			return err
		}
		logger.Debug(ctx, fmt.Sprintf("time since vendor call init %v", time.Since(vendorCallInitAt)))
		tryReRegInit, backoff = s.isDevReRegInitRetryable(ctx, vg.AuthFactorUpdateResponse_Status(vgRes.
			GetStatus().GetCode()), afuFlowType, vendorCallInitAt)
		if tryReRegInit {
			// updating vendor request ID when auto-retrying to prevent duplicate request ID
			vendorReqID = requestId + "RT" + strconv.Itoa(i)
			logger.Info(ctx, fmt.Sprintf("retrying with DevReReg init call in %v", backoff),
				zap.String(logger.REQUEST_ID, vendorReqID))
		}
		logger.Debug(ctx, fmt.Sprintf("tryReRegInit: %v, backoff: %v", tryReRegInit, backoff))
		time.Sleep(backoff)
	}
	endpointPhoneNumber := s.getRecipientPhoneNumberRereg(ctx, afuId)
	if endpointPhoneNumber != nil {
		metrics.RecordDevRegStatusMetric(endpointPhoneNumber.ToString(), true, vgRes.GetStatus().GetCode())
	}
	if _, err = s.afuDao.UpdateVendorContext(ctx, afuId, &afu.VendorContext{
		ReRegisterDeviceVendorInitStatus: vgRes.GetStatus(),
		RequestId:                        vendorReqID,
	}); err != nil {
		logger.Error(ctx, "error while updating vendor context with ReRegisterDeviceVendorInitStatus", zap.Error(err))
		return err
	}
	// Enabling SMS acknowledgement creates device registration attempt with recipient phone number, this can be fetched from DB to record
	//	metrics for that number after reregistration vendor call

	if endpointPhoneNumber != nil && s.Conf.Flags().EnableSMSAckListener() && vgRes != nil {
		_ = s.addHealthMetric(ctx, endpointPhoneNumber, auth.DevRegistrationFlow_DEV_REGISTRATION_FLOW_REREGISTRATION, vgRes.GetStatus().GetCode())
	}
	status := vgRes.GetStatus().GetCode()
	if _, ok := successVendorStatusesForAFU[status]; ok {
		logger.Info(ctx, fmt.Sprintf("received %s status for device re-registration", vgRes.GetStatus().String()), logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
		return nil
	}
	// Add failure reason to DB
	reason, ok := failureReasons[vg.AuthFactorUpdateResponse_Status(vgRes.GetStatus().GetCode())]
	if !ok {
		logger.Error(ctx, fmt.Sprintf("unmapped devrereg init resp code: %v", vg.AuthFactorUpdateResponse_Status(vgRes.
			GetStatus().GetCode()).String()))
		reason = afu.FailureReason_UNMAPPED_DEVREG_INIT_FAILURE
	}
	logger.Info(ctx, fmt.Sprintf("AFU DevReReg Init failed with reason: %v from status: %v", reason, vgRes.GetStatus()), logger.ZapAFUId(afuId))
	if reason != afu.FailureReason_FAILURE_REASON_UNSPECIFIED {
		_ = s.afuDao.UpdateAFUByFields(ctx, &afu.AuthFactorUpdate{
			Id:            afuId,
			FailureReason: reason,
		}, []afu.AuthFactorUpdateFieldMask{
			afu.AuthFactorUpdateFieldMask_AUTH_FACTOR_UPDATE_FIELD_MASK_FAILURE_REASON,
		})
	}
	return errReRegInitFailed
}

func (s *Service) isDevReRegInitRetryable(ctx context.Context, status vg.AuthFactorUpdateResponse_Status,
	flowType vg.AFUFlowType,
	init time.Time) (bool, time.Duration) {
	var retryableFailures = map[vg.AuthFactorUpdateResponse_Status]bool{
		vg.AuthFactorUpdateResponse_NO_SMS_RECEIVED:            true,
		vg.AuthFactorUpdateResponse_ENCRYPTED_PAYLOAD_MISMATCH: true,
	}
	if s.Conf.AFU().EnableReRegInitRetry() &&
		retryableFailures[status] &&
		flowType == vg.AFUFlowType_AFU_FLOW_TYPE_SINGLE_AUTH_FACTOR_UPDATE_WITHOUT_CREDBLOCK &&
		time.Since(init) <= 20*time.Second {
		logger.Info(ctx, fmt.Sprintf("allowing devreg retry in afu as time since init: %v", time.Since(init)))
		return true, 3 * time.Second
	}
	return false, 0
}

// nolint: unparam
func (s *Service) isAtmPinRequiredByTransactionCriteria(ctx context.Context, afuCtx *afu.Context, actorId string) (bool, error) {
	if !s.Conf.AFU().EnableATMPinEligibilityCheck() {
		return true, nil
	}
	// todo(saiteja): remove below APIs can be simulated
	if cfg.IsTestEnv(s.Conf.Application().Environment) &&
		strings.EqualFold(epificontext.AcceptanceFlowFromContext(ctx), acceptanceTypes.AuthFactorUpdate.String()) {
		if afuCtx.GetNewValues().GetPhoneNumber().ToString() == "919999912345" {
			logger.Info(ctx, "number from acceptance to skip ATM PIN check")
			return false, nil
		}
		logger.Info(ctx, "skipping transaction aggregate check in acceptance to avoid dependency on pinot")
		return true, nil
	}
	actorRes, err := s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{
		Id: actorId,
	})
	if rpcErr := epifigrpc.RPCError(actorRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting actor", zap.Error(rpcErr))
		return false, rpcErr
	}
	fetchCardRes, err := s.cardProvisioningClient.FetchCards(ctx, &cardProvisionPb.FetchCardsRequest{
		Actor:        actorRes.GetActor(),
		IssuingBanks: []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
		CardStates: []cardPb.CardState{
			cardPb.CardState_ACTIVATED,
		},
		CardTypes: []cardPb.CardType{
			cardPb.CardType_DEBIT,
		},
		CardNetworkTypes: []cardPb.CardNetworkType{cardPb.CardNetworkType_VISA},
		CardForms: []cardPb.CardForm{
			cardPb.CardForm_PHYSICAL,
		},
		SortedBy: cardPb.CardFieldMask_CARD_UPDATED_AT,
	})
	if rpcErr := epifigrpc.RPCError(fetchCardRes, err); rpcErr != nil && !fetchCardRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in getting cards", zap.Error(rpcErr))
		return false, rpcErr
	}
	isDebitCardActive := lo.ContainsBy(fetchCardRes.GetCards(), func(card *cardPb.Card) bool {
		return card.GetIssuerBank() == vendorgateway.Vendor_FEDERAL_BANK &&
			card.GetType() == cardPb.CardType_DEBIT &&
			card.GetState() == cardPb.CardState_ACTIVATED
	})
	if !isDebitCardActive {
		logger.Info(ctx, "user do not have a physical active debit card skipping ATM pin validation")
		return false, nil
	}

	return s.isATMTransactionMade(ctx, actorId)
}

func (s *Service) isATMTransactionMade(ctx context.Context, actorId string) (bool, error) {
	// todo: make this config driven
	last7DaysTimestamp := timestamppb.New(time.Now().Add(-168 * time.Hour).In(datetime.IST))
	transactionAggregatesRes, err := s.payClient.GetTransactionAggregates(ctx, &payPb.GetTransactionAggregatesRequest{
		ActorId:             actorId,
		AccountingEntryType: payment.AccountingEntryType_DEBIT,
		FromTime:            last7DaysTimestamp,
		PaymentProtocol: []payment.PaymentProtocol{
			payment.PaymentProtocol_CARD,
		},
		TransactionsStatus: []payment.TransactionStatus{
			payment.TransactionStatus_SUCCESS,
		},
		Provenance: []orderPb.OrderProvenance{
			orderPb.OrderProvenance_POS,
			orderPb.OrderProvenance_ATM,
			orderPb.OrderProvenance_ECOMM,
		},
	})
	if rpcErr := epifigrpc.RPCError(transactionAggregatesRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting transaction aggregates", zap.Error(rpcErr))
		return false, rpcErr
	}
	if transactionAggregatesRes.GetTransactionAggregates().GetCount() == 0 {
		logger.Info(ctx, "no transaction aggregates found")
		return false, nil
	}
	return true, nil
}

// isAtmPinRequired returns true if atm card pin cred block is required
// nolint: unparam
func (s *Service) isAtmPinRequired(ctx context.Context, afuCtx *afu.Context, actorId string) (bool, error) {
	atmPinRequiredChecker := func() (bool, error) {
		afuLevelCredentials, err := s.getActorAuthCreds(ctx, afuCtx.GetAuthFactors())
		if err != nil {
			logger.Error(ctx, "error in getting actor auth creds", zap.Error(err))
			return false, err
		}
		if !isCredentialPresent(afuLevelCredentials, afu.Credential_ATM_PIN_VALIDATION) {
			return false, nil
		}
		// ignoring unspecified status to handle old requests
		if afuCtx.GetIsAtmPinRequired() == commontypes.BooleanEnum_TRUE {
			return true, nil
		}
		if afuCtx.GetIsAtmPinRequired() == commontypes.BooleanEnum_FALSE {
			return false, nil
		}
		if afuCtx.GetUserConfirmationStatus() == afu.UserConfirmationStatus_USER_ACCEPTED {
			logger.Info(ctx, "handling old requests to check if ATM PIN is required or not")
		}
		if s.Conf.AFU().ReadOnlyFromAtmPinCheckFlag() && afuCtx.GetUserConfirmationStatus() == afu.UserConfirmationStatus_USER_ACCEPTED {
			logger.Error(ctx, "request with no ATM pin required flag not set")
			return false, fmt.Errorf("request with no ATM pin required flag not set")
		}
		atmPinRequired, err := s.isAtmPinRequiredByTransactionCriteria(ctx, afuCtx, actorId)
		if err != nil {
			return false, err
		}
		logger.Info(ctx, "isAtmPinRequiredByTransactionCriteria check",
			zap.String(logger.RESULT, fmt.Sprintf("%v", atmPinRequired)),
			zap.String(logger.REQUEST_TYPE, getCombination.GetScenarioWithoutError(afuCtx.GetAuthFactors()).String()))
		return atmPinRequired, nil
	}
	comb := getCombination.GetScenarioWithoutError(afuCtx.GetAuthFactors())
	atmPinRequiredFlag, err := atmPinRequiredChecker()
	if err != nil {
		return false, err
	}
	logger.Info(ctx, "isAtmPinRequired check",
		zap.String(logger.RESULT, fmt.Sprintf("%v", atmPinRequiredFlag)),
		zap.String(logger.REQUEST_TYPE, comb.String()))
	return atmPinRequiredFlag, nil
}

func (s *Service) ReactivateDeviceForActor(ctx context.Context, req *auth.ReactivateDeviceForActorRequest) (*auth.ReactivateDeviceForActorResponse, error) {
	successRes := func(status *rpc.Status) (*auth.ReactivateDeviceForActorResponse, error) {
		return &auth.ReactivateDeviceForActorResponse{
			Status: status,
		}, nil
	}
	errRes := func(status *rpc.Status, debug string) (*auth.ReactivateDeviceForActorResponse, error) {
		status.SetDebugMessage(debug)
		return &auth.ReactivateDeviceForActorResponse{
			Status: status,
		}, nil
	}
	actorId := req.GetActorId()
	ctx = epificontext.CtxWithActorId(ctx, actorId)

	getActorRes, err := s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{
		Id: actorId,
	})
	if err = epifigrpc.RPCError(getActorRes, err); err != nil {
		logger.Error(ctx, "error in getting actor id", zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}

	userId := getActorRes.GetActor().GetEntityId()
	userDetails, err := s.UserClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_Id{
			Id: userId,
		},
	})
	if err = epifigrpc.RPCError(userDetails, err); err != nil {
		logger.Error(ctx, "error in fetching user", zap.Error(err))
		if userDetails.GetStatus().IsRecordNotFound() {
			return errRes(rpc.StatusRecordNotFound(), "")
		}
		return errRes(rpc.StatusInternal(), err.Error())
	}

	deviceAuthRes, err := s.GetDeviceAuth(ctx, &auth.GetDeviceAuthRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(deviceAuthRes, err); err != nil {
		logger.Error(ctx, "error in fetching device details", zap.Error(err))
		if deviceAuthRes.GetStatus().IsRecordNotFound() {
			return errRes(rpc.StatusRecordNotFound(), "no device linked to the actor")
		}
		return errRes(rpc.StatusInternal(), err.Error())
	}

	deviceDetails := &vgauthPb.Auth{
		DeviceId:      deviceAuthRes.GetDevice().GetDeviceId(),
		DeviceToken:   deviceAuthRes.GetDeviceToken(),
		UserProfileId: deviceAuthRes.GetUserProfileId(),
	}

	vgRes, err := s.reactivateDeviceAtVendor(ctx, deviceDetails, userDetails.GetUser().GetProfile().GetPhoneNumber(), userDetails.GetUser().GetProfile().GetEmail())
	if err != nil {
		logger.Error(ctx, "error reactivating device", zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}

	return successRes(vgRes.GetStatus())
}

func (s *Service) reactivateDeviceAtVendor(ctx context.Context, device *vgauthPb.Auth, phone *commontypes.PhoneNumber, emailId string) (*vg.ReactivateDeviceResponse, error) {
	return s.VendorGateway.ReactivateDevice(ctx, &vg.ReactivateDeviceRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendorMap[afuVendor],
		},
		Email:         emailId,
		Phone:         phone,
		DeviceToken:   device.GetDeviceToken(),
		DeviceId:      device.GetDeviceId(),
		UserProfileId: device.GetUserProfileId(),
	})
}

func (s *Service) ReactivateDevice(ctx context.Context, device *vgauthPb.Auth, phone *commontypes.PhoneNumber, emailId string, afuId string, invalidTokenAsSuccess bool) (err error, retryableErr bool, ambiguousResp bool) {
	vgRes, err := s.reactivateDeviceAtVendor(ctx, device, phone, emailId)
	if err != nil {
		logger.Error(ctx, "error reactivating device", zap.Error(err), logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
		return fmt.Errorf("error reactivating device : %w", err), true, false
	}
	resStatus := vgRes.GetStatus()
	if resStatus.GetCode() == uint32(vg.ReactivateDeviceResponse_OK) ||
		resStatus.GetCode() == uint32(vg.ReactivateDeviceResponse_DEVICE_ALREADY_ACTIVE) ||
		// Considering resp code "OBE0170" in reactivation as success as recovery is possible only after logging back in.
		// This fix allows bypassing blockage in reactivation which kept user in stuck state, and opens gate to
		// re-attempt AFU with the phone number which is registered on Federal's end.
		resStatus.GetCode() == uint32(vg.ReactivateDeviceResponse_DEVICE_TEMPORARILY_DEACTIVATED) {
		if resStatus.GetCode() == uint32(vg.ReactivateDeviceResponse_DEVICE_TEMPORARILY_DEACTIVATED) {
			logger.Info(ctx, "allowing reactivation for device temporarily deactivated by bank", logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
		}
		logger.Info(ctx, fmt.Sprintf("received OK response for device reactivation: %v", resStatus), logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
		return nil, false, false
	}

	err = fmt.Errorf("ReactivateDevice failed. code: %v, msg: %v", resStatus.GetCode(), resStatus.GetDebugMessage())

	if resStatus.GetCode() == uint32(vg.ReactivateDeviceResponse_INVALID_DEVICE_TOKEN) && invalidTokenAsSuccess {
		logger.Info(ctx, "considering invalid device token in afu reactivate as success", logger.ZapAFUId(afuId))
		return nil, false, false
	}
	if resStatus.GetCode() == uint32(vg.ReactivateDeviceResponse_INVALID_DEVICE_TOKEN) {
		logger.Info(ctx, "considering invalid device token in afu reactivate as ambiguous response", logger.ZapAFUId(afuId))
		return err, false, true
	}

	logger.Error(ctx, "reactivate device failed", zap.Error(err), logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
	if resStatus.GetCode() == uint32(vg.ReactivateDeviceResponse_INTERNAL) {
		return err, true, false
	} else {
		return err, false, false
	}
}

func (s *Service) EnquireAndUpdateReRegStatus(ctx context.Context, requestId string, device *vgauthPb.Auth, phone *commontypes.PhoneNumber, afuId string) error {
	vgRes, err := s.VendorGateway.GetAuthFactorUpdateStatus(ctx, &vg.GetAuthFactorUpdateStatusRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendorMap[afuVendor],
		},
		Phone:         phone,
		DeviceToken:   device.DeviceToken,
		DeviceId:      device.DeviceId,
		RequestId:     requestId,
		UserProfileId: device.UserProfileId,
	})
	if err = epifigrpc.RPCError(vgRes, err); err != nil {
		logger.Error(ctx, "error in re-registration enquiry", zap.Error(err), logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
		return fmt.Errorf("error enquiring re-reg status : %w", err)
	}

	return s.ProcessReRegStatusVendorRes(ctx, afuId, vgRes.GetAuthFactorUpdateStatus(), vgRes.GetVendorStatus(), vgRes.GetDeviceToken())
}

// ProcessReRegStatusVendorRes processes the vendor response of device re-registration
// enquiry or callback APIs and updates the state based on re-reg status:
// Success - mark vendor state afu.UpdateVendorState_REREGISTERED
// Pending - returns error errReRegInProgress
// Failure - mark vendor state afu.UpdateVendorState_FAILED, update failure type & reason; returns errReRegFailed
// nolint: funlen
func (s *Service) ProcessReRegStatusVendorRes(ctx context.Context, afuId string, reRegStatus *rpc.Status, vendorStatus *commonvgpb.VendorStatus, deviceToken string) error {
	// TODO(aditya): insert response in vendor store
	logger.Info(ctx, fmt.Sprintf("AFU DevReReg vendor response: %v, status: %v", vendorStatus, reRegStatus), logger.ZapAFUId(afuId))

	// re-registration status handling
	switch reRegStatus.GetCode() {
	// re-registration complete
	case uint32(vg.GetAuthFactorUpdateStatusResponse_AFU_OK):
		logger.Info(ctx, "received OK status for re-registration enquiry", logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
		if _, err := s.updateVendorStateAndAddDeviceTokenInTx(ctx, afuId, afu.UpdateVendorState_REREGISTERED, deviceToken); err != nil {
			return err
		}
		err := s.updateVendorReRegistrationStatus(ctx, afuId, afu.RequestStatus_REQUEST_STATUS_SUCCESS)
		return err

	// re-registration in-progress
	case uint32(vg.GetAuthFactorUpdateStatusResponse_AFU_REQUEST_UNPROCESSED),
		uint32(vg.GetAuthFactorUpdateStatusResponse_AFU_DEVICE_TEMPORARILY_DEACTIVATED):
		logger.Info(ctx, "re-registration enquiry is pending", logger.ZapAFU(), logger.ZapAFUId(afuId), zapVendorStep)
		return errReRegInProgress

	// re-registration failed
	default:
		break
	}
	// Handle failed device re-registration:

	// find failure reason
	failureReasons := map[vg.GetAuthFactorUpdateStatusResponse_AFUStatus]afu.FailureReason{
		vg.GetAuthFactorUpdateStatusResponse_AFU_WRONG_ATM_PIN:                    afu.FailureReason_WRONG_ATM_PIN,
		vg.GetAuthFactorUpdateStatusResponse_AFU_ATM_PIN_MAX_RETRIES:              afu.FailureReason_ATM_PIN_MAX_RETRIES,
		vg.GetAuthFactorUpdateStatusResponse_AFU_CARD_TXN_REQUIRED:                afu.FailureReason_CARD_TXN_REQUIRED,
		vg.GetAuthFactorUpdateStatusResponse_AFU_CARD_INACTIVE:                    afu.FailureReason_CARD_INACTIVE,
		vg.GetAuthFactorUpdateStatusResponse_AFU_KYC_NON_COMPLIANT:                afu.FailureReason_KYC_NON_COMPLIANT,
		vg.GetAuthFactorUpdateStatusResponse_AFU_CARD_NOT_LINKED_TO_OLD_NUMBER:    afu.FailureReason_CARD_NOT_LINKED_TO_OLD_NUMBER,
		vg.GetAuthFactorUpdateStatusResponse_AFU_LOST_CARD:                        afu.FailureReason_LOST_CARD,
		vg.GetAuthFactorUpdateStatusResponse_AFU_RECORD_NOT_FOUND:                 afu.FailureReason_REQUEST_DID_NOT_REACH_VENDOR,
		vg.GetAuthFactorUpdateStatusResponse_AFU_CARD_NOT_LINKED_TO_CUSTOMER:      afu.FailureReason_CARD_NOT_LINKED_TO_CUSTOMER,
		vg.GetAuthFactorUpdateStatusResponse_AFU_NO_CARD_RECORD:                   afu.FailureReason_NO_CARD_RECORD,
		vg.GetAuthFactorUpdateStatusResponse_AFU_ACCOUNT_INACTIVE:                 afu.FailureReason_ACCOUNT_INACTIVE,
		vg.GetAuthFactorUpdateStatusResponse_AFU_PERMISSION_DENIED:                afu.FailureReason_UNMAPPED_DEVREG_ENQUIRY_FAILURE,
		vg.GetAuthFactorUpdateStatusResponse_AFU_ENQUIRY_INTERNAL_ERROR_AT_VENDOR: afu.FailureReason_DEVREREG_ENQUIRY_INTERNAL_ERR_AT_VENDOR,
		vg.GetAuthFactorUpdateStatusResponse_AFU_FAILED_AT_VENDOR:                 afu.FailureReason_AFU_FAILED_AT_VENDOR,
	}
	reason, ok := failureReasons[vg.GetAuthFactorUpdateStatusResponse_AFUStatus(reRegStatus.GetCode())]
	if !ok {
		reason = afu.FailureReason_UNMAPPED_DEVREG_ENQUIRY_FAILURE
		logger.Info(ctx, fmt.Sprintf("devrereg enq unmapped failure resp: %v",
			vg.GetAuthFactorUpdateStatusResponse_AFUStatus(reRegStatus.GetCode()).String()))
		// we observed different responses from bank in reregistration callback and enquiry, we return early in case of an unhandled error, retry enquiry or wait for callback until
		// we get a valid failure reason, we can raise with bank in case user is stuck
		return fmt.Errorf("unknown failure reason in enquiry or callback %v", reRegStatus.GetCode())
	}
	logger.Info(ctx, fmt.Sprintf("AFU DevReReg: failed with reason: %v from status: %v", reason, reRegStatus), logger.ZapAFUId(afuId))

	// update failure type and reason in DB
	if err := s.updateVendorStateToFailure(ctx, afuId, afu.UpdateVendorFailureType_REREGISTRATION_FAILED); err != nil {
		return err
	}
	if err := s.updateVendorReRegistrationStatus(ctx, afuId, afu.RequestStatus_REQUEST_STATUS_FAIL); err != nil {
		return err
	}
	_ = s.afuDao.UpdateAFUByFields(ctx, &afu.AuthFactorUpdate{
		Id:            afuId,
		FailureReason: reason,
	}, []afu.AuthFactorUpdateFieldMask{
		afu.AuthFactorUpdateFieldMask_AUTH_FACTOR_UPDATE_FIELD_MASK_FAILURE_REASON,
	})

	// since the vendor is in failure state, we need to update the pin verification status to FAIL
	_ = s.updateATMPINCredStatusInCtx(ctx, afuId, afu.VerificationStatus_VERIFICATION_FAIL)

	return errReRegFailed
}

// canUpdateVendorState is a simple wrapper over checkIfStateUpdateForVendorUpdateProcessRequired.
// It combines (bool, error) response into single error response. Returns nil error if
// vendor update is required else returns errUpdateVendorNotAllowed error if it's not required.
// nolint: funlen
func (s *Service) canUpdateVendorState(ctx context.Context, newState afu.UpdateVendorState,
	newFailure afu.UpdateVendorFailureType, currState afu.UpdateVendorState, currFailure afu.UpdateVendorFailureType) error {
	canUpdate, err := s.checkIfStateUpdateForVendorUpdateProcessRequired(newState, newFailure, currState, currFailure)
	if err != nil {
		logger.Error(ctx, "afu error in check update vendor state", zap.Error(err))
		return err
	}
	if !canUpdate {
		logger.Info(ctx, fmt.Sprintf("afu update vendor state not allowed; new: %v, %v curr: %v, %v",
			newState, newFailure, currState, currFailure))
		return errUpdateVendorNotAllowed
	}
	return nil
}

// TODO(mohit) : Add unit test for method
// This function evaluates if an update to vendor process state will be valid.
// It takes the current state and failType of the vendor update process and the new state and failType we want to update it to.
// If the current state is equal or ahead of the new state then false is returned, else true is returned.
// nolint: funlen
func (s *Service) checkIfStateUpdateForVendorUpdateProcessRequired(newState afu.UpdateVendorState, newFailType afu.UpdateVendorFailureType,
	currState afu.UpdateVendorState, currFailType afu.UpdateVendorFailureType) (bool, error) {
	if currState == afu.UpdateVendorState_COMPLETED {
		return false, nil
	}
	if currState == afu.UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED {
		return true, nil
	}

	var (
		currStateLevel, newStateLevel, currFailedAfterStateLevel, newFailedAfterStateLevel int8
		currFailedAfterState, newFailedAfterState                                          afu.UpdateVendorState
		ok                                                                                 bool
	)

	if currState != afu.UpdateVendorState_FAILED {
		currStateLevel, ok = vendorUpdateProcessStateOrder[currState]
		if !ok {
			return false, fmt.Errorf("current state not found in vendor update state map")
		}
	} else {
		currFailedAfterState, ok = vendorUpdateFailureToStateMap[currFailType]
		if !ok {
			return false, fmt.Errorf("current fail type not found in failTypetoState map")
		}
		currFailedAfterStateLevel, ok = vendorUpdateProcessStateOrder[currFailedAfterState]
		if !ok {
			return false, fmt.Errorf("current failedAfterStateLevel not found in vendor update state map")
		}
	}

	if newState != afu.UpdateVendorState_FAILED {
		newStateLevel, ok = vendorUpdateProcessStateOrder[newState]
		if !ok {
			return false, fmt.Errorf("new state not found in vendor update state map")
		}
	} else {
		newFailedAfterState, ok = vendorUpdateFailureToStateMap[newFailType]
		if !ok {
			return false, fmt.Errorf("new fail type not found in failTypetoState map")
		}
		newFailedAfterStateLevel, ok = vendorUpdateProcessStateOrder[newFailedAfterState]
		if !ok {
			return false, fmt.Errorf("new failedAfterStateLevel not found in vendor update state map")
		}
	}

	switch {

	case newState == afu.UpdateVendorState_REREGISTERED && currState == afu.UpdateVendorState_AMBIGUOUS_REACTIVATION:
		return true, nil
	case newState != afu.UpdateVendorState_FAILED && currState != afu.UpdateVendorState_FAILED:
		if newStateLevel > currStateLevel {
			return true, nil
		}

	case newState != afu.UpdateVendorState_FAILED && currState == afu.UpdateVendorState_FAILED:
		if newStateLevel > currFailedAfterStateLevel {
			return true, nil
		}

	case newState == afu.UpdateVendorState_FAILED && currState != afu.UpdateVendorState_FAILED:
		if currState == afu.UpdateVendorState_AMBIGUOUS_REACTIVATION {
			return true, nil
		}
		if newFailedAfterStateLevel >= currStateLevel {
			return true, nil
		}

	case newState == afu.UpdateVendorState_FAILED && currState == afu.UpdateVendorState_FAILED:
		if newFailedAfterStateLevel > currFailedAfterStateLevel {
			return true, nil
		}
	}
	return false, nil
}

// Prevents backward or redundant updates to ReRegistration status in vendor context.
// Update is not required if status is already marked success, or different from current status.
func shouldUpdateVendorReRegState(newStatus afu.RequestStatus, currStatus afu.RequestStatus) bool {
	return currStatus != afu.RequestStatus_REQUEST_STATUS_SUCCESS && currStatus != newStatus
}

// ProcessAFU returns action as per status of AFU flow
//
// If AFU has reached terminal state, it will return NO_ACTION
//
// # If AFU is in progress, it will return next action to be performed as per the flow
//
// If all actions in flow are complete, it will update AFU record to complete status and return NO_ACTION
func (s *Service) ProcessAFU(ctx context.Context, o *afu.AuthFactorUpdate) (*auth.AuthFactorUpdateNextActionDetails, error) {
	return s.processAFUStateMachine(ctx, o)
}

// processAFUStateMachine checks and returns if any client action is left.
// If all client actions are complete, updates vendors and epifi systems.
// Performs all actions and updates OverallStatus to COMPLETED or FAILED
//
//nolint:funlen
func (s *Service) processAFUStateMachine(ctx context.Context, afuRecord *afu.AuthFactorUpdate) (*auth.AuthFactorUpdateNextActionDetails, error) {
	// todo(saiteja) : handle nil deeplinks
	afuCtx := afuRecord.Context
	afuId := afuRecord.Id

	if s.isTerminalState(afuRecord.GetOverallStatus()) {
		if afuRecord.GetOverallStatus() == afu.OverallStatus_OVERALL_STATUS_FAILED {
			return &auth.AuthFactorUpdateNextActionDetails{
				NextAction:    auth.AuthFactorUpdateAction_SHOW_FAILURE,
				FailureReason: afuRecord.GetFailureReason(),
			}, nil
		}
		return &auth.AuthFactorUpdateNextActionDetails{
			NextAction: auth.AuthFactorUpdateAction_NO_ACTION,
		}, nil
	}

	// check any action left for client to complete
	if nextActionDetails, err := s.nextClientAction(ctx, afuRecord); err != nil || nextActionDetails.GetNextAction() != auth.AuthFactorUpdateAction_NO_ACTION {
		return nextActionDetails, err
	}

	// check if vendor update is complete
	if isVendorUpdatePending(afuRecord) {
		return &auth.AuthFactorUpdateNextActionDetails{
			NextAction: auth.AuthFactorUpdateAction_VENDOR_UPDATE,
			Deeplink:   s.pollNextAction(ctx, afuId),
		}, nil
	}

	// check epifi device registration updated
	if isDeviceRegistrationRequired(afuCtx.GetAuthFactors()) && afuCtx.GetEpifiDeviceUpdate() != afu.RequestStatus_REQUEST_STATUS_SUCCESS {
		return &auth.AuthFactorUpdateNextActionDetails{
			NextAction: auth.AuthFactorUpdateAction_UPDATE_DEVICE,
			Deeplink:   s.pollNextAction(ctx, afuId),
		}, nil
	}

	// check epifi user profile updated for email & phone number
	if isUserProfileUpdateRequired(afuCtx.GetAuthFactors()) &&
		afuCtx.GetEpifiEmailPhoneNumUpdate() != afu.RequestStatus_REQUEST_STATUS_SUCCESS {
		return &auth.AuthFactorUpdateNextActionDetails{
			NextAction: auth.AuthFactorUpdateAction_UPDATE_USER_PROFILE,
			Deeplink:   s.pollNextAction(ctx, afuId),
		}, nil
	}

	isCardUpdateRequired, err := s.isCardStateUpdationRequired(ctx, afuCtx, afuRecord.GetActorId())
	if err != nil {
		return nil, err
	}
	if isCardUpdateRequired {
		return &auth.AuthFactorUpdateNextActionDetails{
			NextAction: auth.AuthFactorUpdateAction_UPDATE_USER_CARD_STATE,
		}, nil
	}

	// check if afu event has been published
	if !afuCtx.GetAfuCompletionEventPublished() {
		return &auth.AuthFactorUpdateNextActionDetails{
			NextAction: auth.AuthFactorUpdateAction_PUBLISH_AFU_COMPLETION_EVENT,
			Deeplink:   s.pollNextAction(ctx, afuId),
		}, nil
	}

	// add afu summary to cache
	if s.Conf.AFU().AuthFactorUpdateCacheConfig() != nil && s.Conf.AFU().AuthFactorUpdateCacheConfig().IsCachingEnabled() {
		if err := s.afuCache.UpdateAFUSummaries(ctx, afuRecord.GetActorId(), &afu.AFUSummary{
			CreatedAt:   afuRecord.GetCreatedAt(),
			UpdatedAt:   timestamppb.Now(),
			AfuId:       afuId,
			AfuStatus:   afu.OverallStatus_OVERALL_STATUS_COMPLETED,
			AuthFactors: afuRecord.GetContext().GetAuthFactors(),
		}); err != nil {
			logger.Error(ctx, "error in adding summary to cache", zap.Error(err))
			return nil, err
		}
	}

	// mark AFU as completed
	_, err = s.afuDao.UpdateContextAndStatus(ctx, afuId, &afu.Context{}, afu.OverallStatus_OVERALL_STATUS_COMPLETED)
	if err != nil {
		return nil, fmt.Errorf("error in mark afu complete: %v", err)
	}

	return &auth.AuthFactorUpdateNextActionDetails{
		NextAction: auth.AuthFactorUpdateAction_NO_ACTION,
	}, nil
}

func (s *Service) ekycHandler(ctx context.Context, afuRecord *afu.AuthFactorUpdate) (*auth.AuthFactorUpdateNextActionDetails, error) {
	res, err := s.checkEKYCStatus(ctx, afuRecord)
	if errors.Is(err, noActionError) {
		var updErr error
		afuRecord, updErr = s.updateCredentialStatus(ctx, afuRecord.GetId(), &afu.CredentialStatus{
			Credential: afu.Credential_AADHAAR_MOBILE_VALIDATION,
			Status:     afu.VerificationStatus_VERIFICATION_SUCCESS,
		}, false)
		if updErr != nil {
			return nil, updErr
		}
		return nil, err
	}
	return res, err
}

func (s *Service) updateCredentialStatus(ctx context.Context, afuId string, afuCredentialStatus *afu.CredentialStatus, overrideSuccess bool) (*afu.AuthFactorUpdate, error) {
	var (
		afuRecord *afu.AuthFactorUpdate
		err       error
	)
	if txnErr := storagev2.RunCRDBIdempotentTxn(ctx, 3, func(ctx context.Context) error {
		afuRecord, err = s.afuDao.GetById(ctx, afuId)
		if err != nil {
			logger.Error(ctx, "error in getting afu", zap.Error(err))
			return err
		}
		if afuRecord.GetContext().GetCredentialStatus(afuCredentialStatus.GetCredential()).GetStatus() == afuCredentialStatus.GetStatus() {
			return nil
		}
		credentialStatuses, upsertErr := upsertCredentialStatus(afuRecord.GetContext().GetCredentialStatuses(), afuCredentialStatus, overrideSuccess)
		if upsertErr != nil {
			logger.Error(ctx, "error in upserting credential status", zap.Error(upsertErr), zap.String(logger.REQUEST_TYPE, afuCredentialStatus.GetCredential().String()))
			return upsertErr
		}
		afuRecord.Context.CredentialStatuses = credentialStatuses
		logger.Debug(ctx, "updating credential statuses")
		if updErr := s.afuDao.UpdateAFUByFields(ctx, afuRecord, []afu.AuthFactorUpdateFieldMask{
			afu.AuthFactorUpdateFieldMask_AUTH_FACTOR_UPDATE_FIELD_MASK_CONTEXT,
		}); updErr != nil {
			logger.Error(ctx, "error in updating AFU credential status", zap.Error(updErr))
			return updErr
		}
		return nil
	}); txnErr != nil {
		return nil, txnErr
	}
	return afuRecord, nil
}

// nolint:funlen
func (s *Service) checkEKYCStatus(ctx context.Context, afuRecord *afu.AuthFactorUpdate) (*auth.AuthFactorUpdateNextActionDetails, error) {
	var (
		updErr error
	)
	if !s.Conf.Flags().EnableEKYCInPhoneUpdate() {
		return nil, skipActionError
	}
	afuCtx := afuRecord.GetContext()
	if !isPhoneNumUpdate(afuCtx.GetAuthFactors()) {
		return nil, skipActionError
	}

	// handles for backward compatible cases and make sure that vendor call is initiated only after eKYC risk check is successful
	if hasVendorCallsInitiated(afuRecord.GetVendorContext().GetState()) {
		return nil, skipActionError
	}

	// Mark credential verification status as in progress
	// Ignore if verification status is already successful
	// We process rest of the method even in case of successful verification status to check for status expiry
	if afuRecord.GetContext().GetCredentialStatus(afu.Credential_AADHAAR_MOBILE_VALIDATION).GetStatus() != afu.VerificationStatus_VERIFICATION_SUCCESS {
		afuRecord, updErr = s.updateCredentialStatus(ctx, afuRecord.GetId(), &afu.CredentialStatus{
			Credential: afu.Credential_AADHAAR_MOBILE_VALIDATION,
			Status:     afu.VerificationStatus_VERIFICATION_IN_PROGRESS,
		}, false)
		if updErr != nil {
			return nil, updErr
		}
	}
	if afuCtx.GetEkycInfo().GetClientRequestId() == "" {
		return s.startNewEKYCAttempt(ctx, afuRecord)
	}
	getKycRes, err := s.kycClient.GetKYCAttempt(ctx, &kyc.GetKYCAttemptRequest{
		Identifier: &kyc.GetKYCAttemptRequest_ClientReqIdType{
			ClientReqIdType: &kyc.GetKYCAttemptRequest_ClientReqIdAndType{
				ClientReqId: afuCtx.GetEkycInfo().GetClientRequestId(),
				KycType:     kyc.KycType_EKYC,
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(getKycRes, err); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
		logger.Error(ctx, "error in getting kyc attempt", zap.Error(rpcErr))
		return nil, rpcErr
	}
	if getKycRes.GetStatus().IsRecordNotFound() {
		return s.startNewEKYCAttempt(ctx, afuRecord)
	}

	kycAttempt := getKycRes.GetKycAttempt()
	logger.Info(ctx, "AFU EKYC state", zap.String(logger.STATUS, kycAttempt.GetState().String()))
	switch kycAttempt.GetState() {
	case kyc.KYCState_EKYC_INIT:
		// starting new attempt as current service logic creates new attempt without client requestID instead of resuming the attempt
		return s.startNewEKYCAttempt(ctx, afuRecord)
	case kyc.KYCState_EKYC_FAILED:
		failureReason := afu.FailureReason_UNEXPECTED_EKYC_FAILURE
		if lo.ContainsBy(kycAttempt.GetFailureReason().GetFailures(), func(failure *kyc.Failure) bool {
			return failure.GetType() == kyc.FailureType_AADHAAR_MOBILE_MISMATCH
		}) {
			failureReason = afu.FailureReason_AADHAAR_MOBILE_MISMATCH
		}
		if err = s.failAFUAttempt(ctx, failureReason, afuRecord); err != nil {
			return nil, err
		}
		logger.Info(ctx, fmt.Sprintf("AFU EKYC failure reason %v", getFailureReasonStrings(kycAttempt.GetFailureReason().GetFailures())))
		return &auth.AuthFactorUpdateNextActionDetails{
			NextAction:    auth.AuthFactorUpdateAction_SHOW_FAILURE,
			FailureReason: failureReason,
		}, nil
	case kyc.KYCState_EKYC_DATA_RECEIVED:
		isExpired, expErr := s.isEKYCExpired(ctx, kycAttempt.GetKycAttemptId())
		if expErr != nil {
			return nil, expErr
		}
		if isExpired {
			logger.Info(ctx, "AFU EKYC attempt expired",
				zap.String(logger.REQUEST_ID, afuRecord.GetContext().GetEkycInfo().GetClientRequestId()))
			afuRecord, updErr = s.updateCredentialStatus(ctx, afuRecord.GetId(), &afu.CredentialStatus{
				Credential: afu.Credential_AADHAAR_MOBILE_VALIDATION,
				Status:     afu.VerificationStatus_VERIFICATION_IN_PROGRESS,
			}, true)
			if updErr != nil {
				return nil, updErr
			}
			return s.startNewEKYCAttempt(ctx, afuRecord)
		}
		return nil, noActionError
	default:
		logger.Error(ctx, "unexpected EKYC state", zap.String(logger.STATE, kycAttempt.GetState().String()))
		return nil, fmt.Errorf("unexpected EKYC state")
	}
}

func getFailureReasonStrings(reasons []*kyc.Failure) []string {
	failureReasonStrings := make([]string, 0)
	for _, reason := range reasons {
		failureReasonStrings = append(failureReasonStrings, reason.GetType().String())
	}
	return failureReasonStrings
}

func (s *Service) isEKYCExpired(ctx context.Context, kycAttemptId string) (bool, error) {
	res, err := s.kycClient.GetKYCVendorData(ctx, &kyc.GetKYCVendorDataRequest{
		Identifier: &kyc.GetKYCVendorDataRequest_AttemptId{
			AttemptId: kycAttemptId,
		},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
		logger.Error(ctx, "error in getting KYC vendor data", zap.Error(rpcErr))
		return false, rpcErr
	}
	if res.GetStatus().IsRecordNotFound() {
		return true, nil
	}
	if s.TimeClient.Since(res.GetVendorData().GetCreatedAt().AsTime()) > s.Conf.AFU().ExpiryForEKYC() {
		return true, nil
	}
	return false, nil
}

func (s *Service) failAFUAttempt(ctx context.Context, failureReason afu.FailureReason, afuRecord *afu.AuthFactorUpdate) error {
	afuRecord.FailureReason = failureReason
	afuRecord.OverallStatus = afu.OverallStatus_OVERALL_STATUS_FAILED
	if err := s.afuDao.UpdateAFUByFields(ctx, afuRecord, []afu.AuthFactorUpdateFieldMask{
		afu.AuthFactorUpdateFieldMask_AUTH_FACTOR_UPDATE_FIELD_MASK_FAILURE_REASON,
		afu.AuthFactorUpdateFieldMask_AUTH_FACTOR_UPDATE_FIELD_MASK_OVERALL_STATUS,
	}); err != nil {
		logger.Error(ctx, "error in failing request from ekyc failure", zap.Error(err))
		return err
	}
	return nil
}

func (s *Service) getEKYCDeeplinkForAFU(ctx context.Context, actorId string) (*deeplinkPb.Deeplink, error) {
	res, err := s.bankCustClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "error in getting bank customer", zap.Error(rpcErr))
		return nil, rpcErr
	}

	return startEkycDl(res.GetBankCustomer().GetKycInfo().GetKycLevel()), nil
}

func startEkycDl(kycLevel kyc.KYCLevel) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_START_EKYC,
		ScreenOptions: &deeplinkPb.Deeplink_StartEkycOptions{
			StartEkycOptions: &deeplinkPb.StartEKYCOptions{
				KycLevel:   kycPkg.KycLevelMap[kycLevel],
				EkycSource: kyc.EkycSource_EKYC_SOURCE_FEDERAL_AFU_PHONE_UPDATE.String(),
				// todo(saiteja): send GNOA instead of CheckAFUStatus until CheckAFUStatus client side is resolved
				// https://monorail.pointz.in/p/fi-app/issues/detail?id=76441
				NextAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
				},
				Title:       commontypes.GetTextFromHtmlStringFontColourFontStyle("Use Aadhaar details to change your phone number", "#333333", commontypes.FontStyle_SUBTITLE_1),
				Image:       commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/onboarding/aadhar_illustration.png", 240, 240),
				Description: commontypes.GetTextFromHtmlStringFontColourFontStyle("Our partner Federal Bank will verify your KYC with Aadhaar. Fi does not store or use your Aadhaar details", "#878A8D", commontypes.FontStyle_BODY_S),
				ConsentText: commontypes.GetTextFromHtmlStringFontColourFontStyle("By proceeding, you consent to the above and agree that no other account has been or will be opened using OTP-based KYC with another Regulated Entity", "#646464", commontypes.FontStyle_BODY_XS),
			},
		},
	}
}

func (s *Service) startNewEKYCAttempt(ctx context.Context, afuRecord *afu.AuthFactorUpdate) (*auth.AuthFactorUpdateNextActionDetails, error) {
	if afuRecord.GetContext().GetEkycInfo() == nil {
		afuRecord.Context.EkycInfo = &afu.Context_EkycInfo{}
	}
	afuRecord.Context.EkycInfo.ClientRequestId = genEKYCRequestId()
	logger.Info(ctx, "new EKYC client request ID in AFU", zap.String(logger.REQUEST_ID, afuRecord.GetContext().GetEkycInfo().GetClientRequestId()))
	if err := s.afuDao.UpdateAFUByFields(ctx, afuRecord, []afu.AuthFactorUpdateFieldMask{
		afu.AuthFactorUpdateFieldMask_AUTH_FACTOR_UPDATE_FIELD_MASK_CONTEXT,
	}); err != nil {
		logger.Error(ctx, "error in updating ekycInfo in AFU context", zap.Error(err))
		return nil, err
	}
	dl, err := s.initEKYCForAFU(ctx, afuRecord.GetActorId(), afuRecord.GetContext().GetEkycInfo().GetClientRequestId())
	if err != nil {
		return nil, err
	}
	return &auth.AuthFactorUpdateNextActionDetails{
		NextAction: auth.AuthFactorUpdateAction_EKYC,
		Deeplink:   dl,
	}, nil
}

func (s *Service) initEKYCForAFU(ctx context.Context, actorId, kycClientReqId string) (*deeplinkPb.Deeplink, error) {
	initEKYCRes, initErr := s.kycClient.InitiateEKYC(ctx, &kyc.InitiateEKYCRequest{
		ActorId:         actorId,
		EkycSource:      kyc.EkycSource_EKYC_SOURCE_FEDERAL_AFU_PHONE_UPDATE,
		ClientRequestId: kycClientReqId,
		ForceRetry:      true,
	})
	if rpcErr := epifigrpc.RPCError(initEKYCRes, initErr); rpcErr != nil {
		logger.Error(ctx, "error in initiating EKYC", zap.Error(rpcErr))
		return nil, rpcErr
	}
	return s.getEKYCDeeplinkForAFU(ctx, actorId)
}

func (s *Service) BlockExistingAuthFactorUpdates(ctx context.Context, req *auth.BlockExistingAuthFactorUpdatesRequest) (*auth.BlockExistingAuthFactorUpdatesResponse, error) {
	errRes := func(status *rpc.Status, debug string) (*auth.BlockExistingAuthFactorUpdatesResponse, error) {
		status.SetDebugMessage(debug)
		return &auth.BlockExistingAuthFactorUpdatesResponse{
			Status: status,
		}, nil
	}

	if afuRecord, err := s.blockExistingAFUs(ctx, afu.FailureReason_ONBOARDING_CONFLICT, req.GetPhoneNumber(), req.GetEmail(), req.GetDevice()); err != nil {
		if errors.Is(err, epifierrors.ErrPermissionDenied) {
			return &auth.BlockExistingAuthFactorUpdatesResponse{
				Status:      rpc.StatusPermissionDenied(),
				AuthFactors: afuRecord.GetContext().GetAuthFactors(),
			}, nil
		}
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()), "")
	}
	logger.Debug(ctx, "no blockers for onboarding")
	return &auth.BlockExistingAuthFactorUpdatesResponse{
		Status: rpc.StatusOk(),
	}, nil

}

// blockExistingAFUs checks if onboarding or AFU is allowed for given set of auth factors
// If there is an AFU record whose vendor update has started, it returns PD along with corresponding afuRecord
// If no AFU has started vendor update, it marks all these AFUs with appropriate failure reason and returns nil, allowing onboarding/AFU
func (s *Service) blockExistingAFUs(ctx context.Context, reason afu.FailureReason, phoneNumber *commontypes.PhoneNumber, email string, device string) (*afu.AuthFactorUpdate, error) {
	if reason == afu.FailureReason_FAILURE_REASON_UNSPECIFIED {
		return nil, epifierrors.ErrInvalidArgument
	}
	// get pending AFUs for a given set of auth factors
	pendingAFUs, err := s.afuDao.GetByStatusAndAuthFactors(ctx, afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS, phoneNumber.ToString(), device, email)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			// if there are no pending AFUs for the given auth factors
			// we can proceed for onboarding/AFU without any blocker
			return nil, nil
		}
		logger.Error(ctx, "error in fetching the inprogress afu attempts", zap.Error(err))
		return nil, err
	}
	blockAfuIDs := make([]string, 0)

	for _, afuRecord := range pendingAFUs {
		if s.isVendorUpdateStarted(afuRecord) {
			logger.Info(ctx, "conflict in auth factors observed", logger.ZapAFUId(afuRecord.GetId()), zap.String("conflict_actorid", afuRecord.ActorId))
			return afuRecord, epifierrors.ErrPermissionDenied
		}

		blockAfuIDs = append(blockAfuIDs, afuRecord.GetId())
	}

	// if there are any AFUs pending with same auth factors as of onboarding user, we block the AFUs by marking them failed (if AFU request hasn't reached federal).
	// if there are any AFUs pending with same auth factors as of user attempting AFU, we block the AFUs by marking them failed
	if err = s.afuDao.UpdateStatusAndFailureReasonByAFUIDs(ctx, afu.OverallStatus_OVERALL_STATUS_FAILED, reason, blockAfuIDs); err != nil {
		logger.Error(ctx, "error in marking AFUs as failed", zap.Error(err))
		return nil, err
	}
	return nil, nil
}

// GetAFUSummaries gives the list of summaries of successful AFUs for an actor for a specific time frame
func (s *Service) GetAFUSummaries(ctx context.Context, req *auth.GetAFUSummariesRequest) (*auth.GetAFUSummariesResponse, error) {
	errRes := func(status *rpc.Status, debug string) (*auth.GetAFUSummariesResponse, error) {
		status.SetDebugMessage(debug)
		return &auth.GetAFUSummariesResponse{
			Status: status,
		}, nil
	}

	// todo: add validation on timestamp from config

	if len(req.GetStatuses()) != 1 || req.GetStatuses()[0] != afu.OverallStatus_OVERALL_STATUS_COMPLETED {
		return errRes(rpc.StatusFailedPrecondition(), "invalid statuses list passed")
	}

	afuSummaries := make([]*afu.AFUSummary, 0)

	summaries, err := s.afuCache.GetAFUSummaries(ctx, req.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return errRes(rpc.StatusRecordNotFound(), err.Error())
		}
		logger.Error(ctx, "error in getting AFUSummaries", zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}

	// including the AFUs which are done after the from-time
	for _, summary := range summaries {
		if summary.GetUpdatedAt().AsTime().After(req.GetFromTime().AsTime()) {
			afuSummaries = append(afuSummaries, summary)
		}
	}

	return &auth.GetAFUSummariesResponse{
		AfuSummaries: afuSummaries,
		Status:       rpc.StatusOk(),
	}, nil
}

func (s *Service) isCardStateUpdationRequired(ctx context.Context, afuCtx *afu.Context, actorId string) (bool, error) {
	// return early when ATM PIN validation is not required
	isAtmRequired, err := s.isAtmPinRequired(ctx, afuCtx, actorId)
	if err != nil {
		return false, err
	}
	if !isAtmRequired {
		return false, nil
	}

	// prevent card state update if the auth factor update was attempted without ATM PIN validation
	for _, cs := range afuCtx.GetCredentialStatuses() {
		if cs.GetCredential() == afu.Credential_ATM_PIN_VALIDATION &&
			cs.GetStatus() != afu.VerificationStatus_VERIFICATION_STATUS_RECORDED &&
			cs.GetStatus() != afu.VerificationStatus_VERIFICATION_SUCCESS {
			return false, nil
		}
	}
	return afuCtx.CardState == afu.Context_CARD_STATE_INACTIVE, nil
}

func (s *Service) isVendorUpdateStarted(afuRecord *afu.AuthFactorUpdate) bool {
	return afuRecord.GetVendorContext().GetState() != afu.UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED
}

func isVendorUpdatePending(afuRecord *afu.AuthFactorUpdate) bool {
	vendorReqs := afuRecord.GetContext().GetVendorRequestStatuses()

	// check if request status is already success
	for _, req := range vendorReqs {
		if req.GetVendor() != afuVendor {
			continue
		}
		if req.Status == afu.RequestStatus_REQUEST_STATUS_SUCCESS {
			return false
		}
	}

	return true
}

// PerformVendorUpdate performs vendor side updates for a given auth factor update
func (s *Service) PerformVendorUpdate(ctx context.Context, afuRecord *afu.AuthFactorUpdate) error {
	// update AFU call to vendor
	if err := s.updateVendor(ctx, afuRecord); err != nil {
		return fmt.Errorf("error in update vendor: %w", err)
	}

	vendorReqs := afuRecord.GetContext().GetVendorRequestStatuses()
	found := false
	for _, req := range vendorReqs {
		if req.GetVendor() != afuVendor {
			continue
		}
		found = true
		req.Status = afu.RequestStatus_REQUEST_STATUS_SUCCESS
	}
	if !found {
		vendorReqs = append(vendorReqs, &afu.VendorRequestStatus{
			Vendor: afuVendor,
			Status: afu.RequestStatus_REQUEST_STATUS_SUCCESS,
		})
	}

	if _, err := s.afuDao.UpdateContextAndStatus(ctx, afuRecord.Id, &afu.Context{
		VendorRequestStatuses: vendorReqs,
	}, afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS); err != nil {
		logger.Error(ctx, "error while updating vendor req status", zap.Error(err), logger.ZapAFU(),
			logger.ZapAFUId(afuRecord.Id), zapVendorStep)
		return fmt.Errorf("error in update dao: %v", err)
	}
	return nil
}

// isDeviceRegistrationRequired returns whether device registration
// is required or not. It's not required when user is updating
// only email. It's required when either of phone number or device are changed.
func isDeviceRegistrationRequired(authFactors []afu.AuthFactor) bool {
	for _, factor := range authFactors {
		if factor == afu.AuthFactor_DEVICE || factor == afu.AuthFactor_PHONE_NUM || factor == afu.AuthFactor_SIM {
			return true
		}
	}
	return false
}

func isUserProfileUpdateRequired(authFactors []afu.AuthFactor) bool {
	for _, factor := range authFactors {
		if factor == afu.AuthFactor_EMAIL || factor == afu.AuthFactor_PHONE_NUM {
			return true
		}
	}
	return false
}

func validatedNewValues(email string, number *commontypes.PhoneNumber, device *commontypes.Device, factors []afu.AuthFactor) (*afu.AuthFactorValues, error) {
	for _, factor := range factors {
		switch factor {
		case afu.AuthFactor_EMAIL:
			if email == "" {
				return nil, fmt.Errorf("email cannot be empty")
			}
		case afu.AuthFactor_PHONE_NUM:
			if number == nil {
				return nil, fmt.Errorf("phone num cannot be nil")
			}
		case afu.AuthFactor_DEVICE:
			if device.GetDeviceId() == "" {
				return nil, fmt.Errorf("device cannot be nil")
			}
		}
	}
	return &afu.AuthFactorValues{
		PhoneNumber: number,
		Email:       email,
		DeviceId:    device.GetDeviceId(),
	}, nil
}

// nextActionChronologicallyValid makes sure next action is progressing as per actionsOrder
func nextActionChronologicallyValid(_ context.Context, from, to auth.AuthFactorUpdateAction) bool {
	return actionsOrder[from] < actionsOrder[to]
}

// fetchDeviceAuthDetails fetches details from device registration and bank customer for a given actorId
func (s *Service) fetchDeviceAuthDetails(ctx context.Context, actorId string) (*vgauthPb.Auth, error) {
	deviceDetails, err := s.GetDeviceAuth(ctx,
		&auth.GetDeviceAuthRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(deviceDetails, err); rpcErr != nil {
		logger.Error(ctx, "Failed to get device details of user ", zap.Error(rpcErr))
		return nil, rpcErr
	}

	bankCustRes, bcErr := s.bankCustClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(bankCustRes, bcErr); rpcErr != nil {
		logger.Error(ctx, "error while fetching bank customer", zap.Error(rpcErr))
		return nil, rpcErr
	}

	device := &vgauthPb.Auth{
		DeviceId:      deviceDetails.GetDevice().GetDeviceId(),
		DeviceToken:   deviceDetails.GetDeviceToken(),
		UserProfileId: deviceDetails.GetUserProfileId(),
		CustomerId:    bankCustRes.GetBankCustomer().GetVendorCustomerId(),
	}

	return device, nil
}

// ensureAFUInProgress updates overall status to IN_PROGRESS if status is STUCK.
// returns errUpdateVendorNotAllowed if afu can't be transitioned as it's in terminal state.
func (s *Service) ensureAFUInProgress(ctx context.Context, afuId string, overallStatus afu.OverallStatus) error {
	if overallStatus == afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS {
		return nil
	}
	if err := checkOverallStatusForVendorUpdate(ctx, overallStatus); err != nil {
		logger.Info(ctx, fmt.Sprintf("invalid overall status for vendor update: %v", overallStatus), logger.ZapAFUId(afuId))
		return err
	}
	logger.Info(ctx, "afu not in progress, updating overall status to in-progress", logger.ZapAFUId(afuId))
	if err := s.afuDao.UpdateAFUByFields(ctx, &afu.AuthFactorUpdate{
		Id:            afuId,
		OverallStatus: afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
	}, []afu.AuthFactorUpdateFieldMask{
		afu.AuthFactorUpdateFieldMask_AUTH_FACTOR_UPDATE_FIELD_MASK_OVERALL_STATUS,
	}); err != nil {
		logger.Error(ctx, "error in update afu overall status", zap.Error(err), logger.ZapAFUId(afuId))
		return fmt.Errorf("error in update afu overall status: %w", err)
	}
	return nil
}

// checkOverallStatusForVendorUpdate ensures that overall status is favourable for update.
// update is not allowed if the attempt has already completed or failed.
func checkOverallStatusForVendorUpdate(_ context.Context, overallStatus afu.OverallStatus) error {
	switch overallStatus {
	// terminal states; vendor update not allowed
	case afu.OverallStatus_OVERALL_STATUS_COMPLETED,
		afu.OverallStatus_OVERALL_STATUS_FAILED:
		return errUpdateVendorNotAllowed

	default:
		return nil
	}
}

func (s *Service) sendPhoneUpdateSms(ctx context.Context, afuRecord *afu.AuthFactorUpdate) (string, error) {
	device, err := s.fetchDeviceAuthDetails(ctx, afuRecord.ActorId)
	if err != nil {
		logger.Error(ctx, "error in fetchDeviceAuthDetails", zapAfu, zapAfuId(afuRecord.Id), zap.Error(err))
		return "", fmt.Errorf("error in fetchDeviceAuthDetails : %v", err)
	}
	customerIdLast4Digits, err := getLastNCharacters(device.GetCustomerId(), 4)
	if err != nil {
		logger.Error(ctx, "error in getLastNCharacters", zapAfu, zapAfuId(afuRecord.Id), zap.Error(err))
		return "", fmt.Errorf("error in getLastNCharacters : %w", err)
	}
	smsReq := &comms.SendMessageRequest{
		Type:   comms.QoS_GUARANTEED,
		Medium: comms.Medium_SMS,
		UserIdentifier: &comms.SendMessageRequest_PhoneNumber{
			PhoneNumber: afuRecord.GetContext().GetNewValues().GetPhoneNumber().ToStringNationalNumber(),
		},
		Message: &comms.SendMessageRequest_Sms{
			Sms: &comms.SMSMessage{
				SmsOption: &comms.SmsOption{
					Option: &comms.SmsOption_MobileNumberModifySmsOption{
						MobileNumberModifySmsOption: &comms.MobileNumberModifySmsOption{
							SmsType: comms.SmsType_MOBILE_NUMBER_MODIFY,
							Option: &comms.MobileNumberModifySmsOption_MobileNumberModifySmsOptionV1{
								MobileNumberModifySmsOptionV1: &comms.MobileNumberModifySmsOptionV1{
									TemplateVersion:          comms.TemplateVersion_VERSION_V1,
									CustomerIdLastFourDigits: customerIdLast4Digits,
									RequestAcceptedTimestamp: timestamppb.New(afuRecord.GetCreatedAt().AsTime().In(datetime.IST)),
									NewMobileNumber:          afuRecord.GetContext().GetNewValues().GetPhoneNumber(),
									OldMobileNumber:          afuRecord.GetContext().GetCurrentValues().GetPhoneNumber(),
								},
							},
						},
					},
				},
			},
		},
	}
	sendSmsRes, err := s.SmsClient.SendMessage(ctx, smsReq)
	if rpcErr := epifigrpc.RPCError(sendSmsRes, err); rpcErr != nil {
		logger.Error(ctx, "Error in sending SMS: ", zapAfu, zapAfuId(afuRecord.Id), zap.Error(rpcErr))
		return "", fmt.Errorf("error sending sms : %w", rpcErr)
	}
	logger.Info(ctx, "sent phone number update SMS to user", zapAfu, zapAfuId(afuRecord.Id))
	return sendSmsRes.GetMessageId(), nil
}

func (s *Service) sendEmailUpdateEmail(ctx context.Context, afuRecord *afu.AuthFactorUpdate) (string, error) {
	userRes, err := s.UserClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: afuRecord.GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(userRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting user", zap.Error(rpcErr))
		return "", rpcErr
	}

	smsReq := &comms.SendMessageRequest{
		Type:   comms.QoS_GUARANTEED,
		Medium: comms.Medium_EMAIL,
		UserIdentifier: &comms.SendMessageRequest_EmailId{
			EmailId: afuRecord.GetContext().GetNewValues().GetEmail(),
		},
		Message: &comms.SendMessageRequest_Email{
			Email: &comms.EmailMessage{
				FromEmailId:   "<EMAIL>",
				FromEmailName: "Epifi Technologies",
				EmailOption: &comms.EmailOption{
					Option: &comms.EmailOption_EmailUpdateFederalEmailOption{
						EmailUpdateFederalEmailOption: &comms.EmailUpdateFederalEmailOption{
							EmailType: comms.EmailType_EMAIL_UPDATE_FEDERAL,
							Option: &comms.EmailUpdateFederalEmailOption_EmailUpdateFederalEmailV1{
								EmailUpdateFederalEmailV1: &comms.EmailUpdateFederalEmailV1{
									TemplateVersion: comms.TemplateVersion_VERSION_V1,
									FirstName:       userRes.GetUser().GetProfile().GetKycName().GetFirstName(),
									RequestedDate:   datetime.TimestampToString(afuRecord.GetCreatedAt(), "2 January, 2006", datetime.IST),
									RequestedTime:   datetime.TimestampToString(afuRecord.GetCreatedAt(), datetime.TWELVE_HOUR_LAYOUT, datetime.IST),
									NewEmail:        afuRecord.GetContext().GetNewValues().GetEmail(),
								},
							},
						},
					},
				},
			},
		},
	}
	sendSmsRes, err := s.SmsClient.SendMessage(ctx, smsReq)
	if rpcErr := epifigrpc.RPCError(sendSmsRes, err); rpcErr != nil {
		logger.Error(ctx, "error in sending email", zapAfu, zapAfuId(afuRecord.GetId()), zap.Error(rpcErr))
		return "", rpcErr
	}
	logger.Info(ctx, "sent email regarding email update to user", zapAfu, zapAfuId(afuRecord.GetId()))
	return sendSmsRes.GetMessageId(), nil
}

// nolint: funlen
func (s *Service) maybeBypassAFU(ctx context.Context, att *afu.AuthFactorUpdate, simSubIds []int32) (*afu.AuthFactorUpdate, error) {
	var (
		afuId        = att.Id
		userPh       = att.GetContext().GetCurrentValues().GetPhoneNumber()
		simId  int32 = 0
	)
	if len(simSubIds) > 0 {
		simId = simSubIds[0]
	}

	if !cfg.IsSimulatedEnv(s.Conf.Application().Environment) {
		return att, nil
	}

	bypass := false
	for _, bypassPh := range s.Conf.AFU().BypassCredentialVerificationForPhoneNumbers() {
		logger.Debug(ctx, fmt.Sprintf("comparing phone numbers: %v %v", userPh.ToString(), bypassPh))
		if userPh.ToString() == bypassPh {
			logger.Info(ctx, "skipping afu check for special phone number", zap.String(logger.PHONE_NUMBER, bypassPh))
			bypass = true
			break
		}
	}
	if !bypass {
		return att, nil
	}

	logger.Info(ctx, "starting bypass afu", zap.String(logger.PHONE_NUMBER, userPh.ToString()))

	// Simulate user steps:

	// 1. passing liveness check
	_, _ = s.UpdateAFUCredentialStatus(ctx, &auth.UpdateAFUCredentialStatusRequest{
		AuthFactorUpdateId: afuId,
		CredentialStatus: &afu.CredentialStatus{
			Credential: afu.Credential_LIVENESS_FM_VALIDATION,
			Status:     afu.VerificationStatus_VERIFICATION_SUCCESS,
		},
	})

	// 2. entering ATM PIN
	_, _ = s.UpdateAFUCredentialStatus(ctx, &auth.UpdateAFUCredentialStatusRequest{
		AuthFactorUpdateId: afuId,
		CredentialStatus: &afu.CredentialStatus{
			Credential: afu.Credential_ATM_PIN_VALIDATION,
			Status:     afu.VerificationStatus_VERIFICATION_STATUS_RECORDED,
		},
	})

	// 3. confirming Auth Factor Update
	_, _ = s.ConfirmAuthFactorUpdate(ctx, &auth.ConfirmAuthFactorUpdateRequest{
		AuthFactorUpdateId: afuId,
		CredBlock:          "cred block",
		CredBlockRequestId: genDeviceReRegRequestId(),
	})

	// 4. doing device registration
	_, _ = s.ReRegisterDevice(ctx, &auth.ReRegisterDeviceRequest{
		Device:             att.GetContext().GetNewDevice(),
		PhoneNumber:        userPh,
		Email:              att.GetContext().GetNewValues().GetEmail(),
		Payload:            "bypass afu payload",
		ActorId:            att.GetActorId(),
		AuthFactorUpdateId: afuId,
		CredBlock:          "bypass afu credblock",
		CredBlockRequestId: genDeviceReRegRequestId(),
		SimId:              strconv.Itoa(int(simId)),
	})

	// Get latest AFU
	return s.GetAFUByID(ctx, afuId)
}

func AuthFactorValuesEqual(af1, af2 *afu.AuthFactorValues) bool {
	if af1.DeviceId == af2.DeviceId && af1.Email == af2.Email && af1.PhoneNumber.ToString() == af2.PhoneNumber.ToString() {
		return true
	}
	return false
}

func genDeviceReRegRequestId() string {
	return idgen.FederalRandomSequence(DeviceReRegistrationRequestPrefix, 5)
}

func genEKYCRequestId() string {
	return idgen.FederalRandomSequence(EKYCRequestPrefix, 5)
}

// getLastNCharacters trims and returns last n characters of a string
func getLastNCharacters(data string, n int) (string, error) {
	if len(data) < n {
		return "", fmt.Errorf("invalid string %s", data)
	}
	return data[len(data)-n:], nil
}

func isPhoneNumUpdate(authFactors []afu.AuthFactor) bool {
	for _, af := range authFactors {
		if af == afu.AuthFactor_PHONE_NUM {
			return true
		}
	}
	return false
}

func isEmailUpdate(authFactors []afu.AuthFactor) bool {
	return lo.Contains(authFactors, afu.AuthFactor_EMAIL)
}

// nolint:govet
func (s *Service) ProcessUserCardStateUpdateAndGetNextAction(ctx context.Context, afuRecord *afu.AuthFactorUpdate) (auth.AuthFactorUpdateAction, error) {
	var handleCardError = func() (auth.AuthFactorUpdateAction, error) {
		// Card can be in an inconsistent state, as some card related actions happen independently at vendor.
		// Thus, if an AFU update which needed ATM PIN; is successful, then we can trust the debit card is active
		afuRecord, err := s.afuDao.UpdateContextAndStatus(ctx, afuRecord.GetId(), &afu.Context{
			CardState: afu.Context_CARD_STATE_ACTIVE,
		}, afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS)
		if err != nil {
			logger.Error(ctx, "error in updating afu record", zap.Error(err))
			return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, fmt.Errorf("ProcessUserCardStateUpdateAndGetNextAction error in afuDao UpdateContextAndStatus: %w", err)
		}
		nextActionDetails, aErr := s.ProcessAFU(ctx, afuRecord)
		if aErr != nil {
			logger.Error(ctx, "error in processing afu", zap.Error(aErr))
			return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, fmt.Errorf("ProcessUserCardStateUpdateAndGetNextAction error in ProcessAFU: %w", aErr)
		}
		return nextActionDetails.GetNextAction(), nil
	}
	// get actor from actor_id
	getActorRes, err := s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{
		Id: afuRecord.GetActorId(),
	})
	if err = epifigrpc.RPCError(getActorRes, err); err != nil {
		logger.Error(ctx, "error in getting actor", zap.Error(err))
		return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, fmt.Errorf("ProcessUserCardStateUpdateAndGetNextAction error in actor GetActorById: %w", err)
	}
	// current card status FetchCards
	cardsRes, err := s.cardProvisioningClient.FetchCards(ctx, &cardProvisionPb.FetchCardsRequest{
		Actor: getActorRes.GetActor(),
	})

	if err = epifigrpc.RPCError(cardsRes, err); err != nil {
		logger.Error(ctx, "non-blocking error in getting card status", zap.Error(err))
		return handleCardError()
	}

	if cards := cardsRes.GetCards(); len(cards) > 0 {
		currentCard := cards[0]
		cardUpdateRes, cErr := s.cardProvisioningClient.ProcessManualCardPinSet(ctx, &cardProvisionPb.ProcessManualCardPinSetRequest{
			CardId: currentCard.GetId(),
		})
		if cErr = epifigrpc.RPCError(cardUpdateRes, cErr); cErr != nil {
			logger.Error(ctx, "non-blocking error in processing card pin set", zap.Error(cErr))
			return handleCardError()
		}
		afuRecord, err = s.afuDao.UpdateContextAndStatus(ctx, afuRecord.GetId(), &afu.Context{
			CardState: afu.Context_CARD_STATE_UPDATED_TO_ACTIVE,
		}, afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS)
		if err != nil {
			logger.Error(ctx, "error in updating afu record", zap.Error(err))
			return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, fmt.Errorf("ProcessUserCardStateUpdateAndGetNextAction error in afuDao UpdateContextAndStatus: %w", err)
		}
		nextActionDetails, aErr := s.ProcessAFU(ctx, afuRecord)
		if aErr != nil {
			logger.Error(ctx, "error in processing afu", zap.Error(aErr))
			return auth.AuthFactorUpdateAction_ACTION_UNSPECIFIED, fmt.Errorf("ProcessUserCardStateUpdateAndGetNextAction error in ProcessAFU: %w", aErr)
		}
		return nextActionDetails.GetNextAction(), nil
	}
	logger.Error(ctx, "non-blocking error no cards found for actor")
	return handleCardError()
}

func getLatestFailureReason(record *afu.AuthFactorUpdate) afu.FailureReason {
	failureReason := record.GetFailureReason()

	attempts := record.GetAttemptsHistory().GetReRegAttempts()
	noOfAttempts := len(attempts)

	if noOfAttempts > 0 && failureReason == afu.FailureReason_FAILURE_REASON_UNSPECIFIED {
		failureReason = attempts[noOfAttempts-1].GetFailureReason()
	}

	return failureReason
}

func (s *Service) ProcessAFURiskVerdict(ctx context.Context, req *auth.ProcessAFURiskVerdictRequest) (*auth.ProcessAFURiskVerdictResponse, error) {
	if req.GetVerdict() == auth.ProcessAFURiskVerdictRequest_VERDICT_UNSPECIFIED {
		return &auth.ProcessAFURiskVerdictResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid verdict"),
		}, nil
	}

	afuRecord, err := s.GetAFUByID(ctx, req.GetAfuId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &auth.ProcessAFURiskVerdictResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error in getting afu record", zap.Error(err))
		return &auth.ProcessAFURiskVerdictResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	getSummaryRes, err := s.livenessClient.GetLivenessSummary(ctx, &liveness.GetLivenessSummaryRequest{
		ActorId:      afuRecord.GetActorId(),
		RequestId:    afuRecord.GetId(),
		LivenessFlow: liveness.LivenessFlow_AUTH_FACTOR_UPDATE,
	})
	if rpcErr := epifigrpc.RPCError(getSummaryRes, err); rpcErr != nil {
		// ignoring record not found and throwing an error instead, as record not found in this case is a valid error
		// and can be ignored by the caller of this API
		logger.Error(ctx, "error in fetching liveness summary details", zap.Error(rpcErr))
		return &auth.ProcessAFURiskVerdictResponse{
			Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
		}, nil
	}

	livSummary := getSummaryRes.GetSummary()
	if err = s.handleRiskVerdict(ctx, livSummary, req.GetVerdict(), req.GetCaseId()); err != nil {
		return &auth.ProcessAFURiskVerdictResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	if err = s.updateLivenessCredStatus(ctx, afuRecord); err != nil {
		return &auth.ProcessAFURiskVerdictResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &auth.ProcessAFURiskVerdictResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) handleRiskVerdict(ctx context.Context, livSummary *liveness.LivenessSummary, verdict auth.ProcessAFURiskVerdictRequest_Verdict, caseId string) error {
	// mark both liveness and facematch as failed
	if verdict == auth.ProcessAFURiskVerdictRequest_VERDICT_FAIL {
		if err := s.annotateLivenessAttempt(ctx, livSummary, verdict, caseId); err != nil {
			return err
		}

		if err := s.annotateFMAttempt(ctx, livSummary, verdict, caseId); err != nil {
			return err
		}
		// annotating only in best effort scneario as we don't want to mark as manually_passed for already passed liveness and facematch
	} else if verdict == auth.ProcessAFURiskVerdictRequest_VERDICT_PASS {
		// annotate only if facematch has failed
		if livSummary.IsSummaryFMFailed() {
			if err := s.annotateFMAttempt(ctx, livSummary, verdict, caseId); err != nil {
				return err
			}
		}

		// annotate liveness as this could be a liveness failure case getting passed/ risk screening case getting passed.
		if err := s.annotateLivenessAttempt(ctx, livSummary, verdict, caseId); err != nil {
			return err
		}

	}
	return nil
}

func (s *Service) updateLivenessCredStatus(ctx context.Context, afuRecord *afu.AuthFactorUpdate) error {
	// Get Latest liveness smmary status
	livSummaryStatus, err := s.livenessClient.GetLivenessSummaryStatus(ctx, &liveness.GetLivenessSummaryStatusRequest{
		ActorId:      afuRecord.GetActorId(),
		RequestId:    afuRecord.GetId(),
		LivenessFlow: liveness.LivenessFlow_AUTH_FACTOR_UPDATE,
	})
	if rpcErr := epifigrpc.RPCError(livSummaryStatus, err); rpcErr != nil {
		if livSummaryStatus.GetStatus().IsRecordNotFound() {
			return epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error in getting liveness summary status", zap.Error(rpcErr))
		return rpcErr
	}

	verificationStatus, failureReason := s.getLivenessVerificationStatus(ctx, livSummaryStatus, afuRecord.GetActorId(), afuRecord.GetId())
	if _, err = s.updateCredentialState(ctx, afuRecord, &afu.CredentialStatus{
		Credential: afu.Credential_LIVENESS_FM_VALIDATION,
		Status:     verificationStatus,
	}, failureReason, true); err != nil {
		logger.Error(ctx, "failed in updating AFU status", zap.Error(err))
		return err
	}

	return nil
}

func (s *Service) getAFURiskModelVerificationStatus(ctx context.Context, afuRecord *afu.AuthFactorUpdate, livSummarryStatus *liveness.GetLivenessSummaryStatusResponse) (afu.VerificationStatus, afu.FailureReason, error) {
	if livSummarryStatus.GetSummaryLivenessStatus() == liveness.SummaryLivenessStatus_SUMMARY_LIVENESS_MANUALLY_PASSED ||
		livSummarryStatus.GetSummaryFacematchStatus() == liveness.SummaryFacematchStatus_SUMMARY_FACEMATCH_MANUALLY_PASSED {
		logger.Info(ctx, "user already passed through manual review, not sending in persistent queue after afu risk check")
		return afu.VerificationStatus_VERIFICATION_SUCCESS, 0, nil
	}

	isNRUserRes, err := s.UserClient.IsNonResidentUser(ctx, &user.IsNonResidentUserRequest{
		Identifier: &user.IsNonResidentUserRequest_ActorId{
			ActorId: afuRecord.GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(isNRUserRes, err); rpcErr != nil {
		logger.Error(ctx, "error in IsNonResidentUser rpc", zap.Error(rpcErr))
		return 0, 0, rpcErr
	}
	if isNRUserRes.GetIsNonResidentUser().ToBool() && !s.Conf.Flags().EnableAFURiskScreeningForNRUser() {
		logger.Info(ctx, "ignoring AFU risk screening for NR user")
		return afu.VerificationStatus_VERIFICATION_SUCCESS, 0, nil
	}

	screenerStatusResp, err := s.riskClient.GetScreenerAttemptStatus(ctx, &riskpb.GetScreenerAttemptStatusRequest{
		AttemptIdentifier: &screener.AttemptIdentifier{
			Identifier: &screener.AttemptIdentifier_CriteriaClientReqId{
				CriteriaClientReqId: &screener.CriteriaAndClientReqIDIdentifier{
					ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_REOOBE,
					ClientRequestId:  afuRecord.GetId(),
				},
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(screenerStatusResp, err); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
		logger.Error(ctx, "error in getting risk screening status response", zap.Error(rpcErr))
		return 0, 0, rpcErr
	}

	if screenerStatusResp.GetStatus().IsRecordNotFound() {
		screenActorRes, screenErr := s.screenActor(ctx, afuRecord)
		if screenErr != nil {
			return 0, 0, screenErr
		}
		return s.getAFURiskStatusByScreenerStatus(ctx, screenActorRes.GetScreenerStatus(), screenActorRes.GetVerdict())
	}

	return s.getAFURiskStatusByScreenerStatus(ctx, screenerStatusResp.GetScreenerStatus(), screenerStatusResp.GetVerdict())
}

func (s *Service) getAFURiskStatusByScreenerStatus(ctx context.Context, status screener.ScreenerStatus, verdict screener.Verdict) (afu.VerificationStatus, afu.FailureReason, error) {
	logger.Info(ctx, "afu risk screening V2 status", zap.String(logger.STATUS, status.String()))
	switch status {
	case screener.ScreenerStatus_SCREENER_STATUS_IN_PROGRESS,
		screener.ScreenerStatus_SCREENER_STATUS_PROCESSING_FAILED:
		return afu.VerificationStatus_VERIFICATION_IN_PROGRESS, 0, nil
	case screener.ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW:
		// todo(saiteja): log users stuck in manual review beyond threshold
		return afu.VerificationStatus_VERIFICATION_IN_REVIEW, 0, nil
	case screener.ScreenerStatus_SCREENER_STATUS_DONE:
		return s.getAFURiskStatusByScreenerVerdict(ctx, verdict)
	default:
		return 0, 0, fmt.Errorf("unexpected screener status %v", status.String())
	}
}

func (s *Service) getAFURiskStatusByScreenerVerdict(ctx context.Context, verdict screener.Verdict) (afu.VerificationStatus, afu.FailureReason, error) {
	switch verdict {
	case screener.Verdict_VERDICT_PASS:
		logger.Info(ctx, "user passed AFU risk screening")
		return afu.VerificationStatus_VERIFICATION_SUCCESS, 0, nil
	case screener.Verdict_VERDICT_FAIL:
		logger.Info(ctx, "user failed AFU risk screening")
		return afu.VerificationStatus_VERIFICATION_STATUS_TERMINALLY_FAILED, afu.FailureReason_AFU_RISK_MODEL_FAILURE, nil
	default:
		logger.Error(ctx, "unexpected verdict", zap.String(logger.STATUS, verdict.String()))
		return 0, 0, fmt.Errorf("unexpected verdict %v", verdict.String())
	}
}

func (s *Service) screenActor(ctx context.Context, afuRecord *afu.AuthFactorUpdate) (*riskpb.ScreenActorResponse, error) {
	screenActorResp, err := s.riskClient.ScreenActor(ctx, &riskpb.ScreenActorRequest{
		ActorId:          afuRecord.GetActorId(),
		ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_REOOBE,
		ClientRequestId:  afuRecord.GetId(),
	})
	if rpcErr := epifigrpc.RPCError(screenActorResp, err); rpcErr != nil {
		logger.Error(ctx, "error in screen actor", zap.Error(rpcErr))
		return nil, rpcErr
	}
	return screenActorResp, nil
}

func (s *Service) insertElementIntoLivenessQueue(ctx context.Context, actorId, afuId string) error {
	summaryResp, err := s.livenessClient.GetLivenessSummary(ctx, &liveness.GetLivenessSummaryRequest{
		ActorId:      actorId,
		RequestId:    afuId,
		LivenessFlow: liveness.LivenessFlow_AUTH_FACTOR_UPDATE,
	})
	if rpcErr := epifigrpc.RPCError(summaryResp, err); rpcErr != nil {
		logger.Error(ctx, "error in get liveness summarry", zap.Error(rpcErr))
		return rpcErr
	}

	laResp, err := s.livenessClient.GetLivenessAttempt(ctx, &liveness.GetLivenessAttemptRequest{
		LivenessReqId: summaryResp.GetSummary().GetLivenessAttemptId(),
	})
	if rpcErr := epifigrpc.RPCError(laResp, err); rpcErr != nil {
		logger.Error(ctx, "error in get liveness attempt", zap.Error(rpcErr))
		return rpcErr
	}

	queueElement := &persistentqueue.QueueElement{
		ActorID:     actorId,
		PayloadType: persistentqueuePb.PayloadType_PAYLOAD_TYPE_AFU_LIVENESS,
		Payload: &persistentqueuePb.Payload{
			AfuLivenessReview: &persistentqueuePb.AFULivenessReview{
				ActorId:       actorId,
				RequestId:     laResp.GetLivenessAttempt().GetRequestId(),
				VideoLocation: laResp.GetLivenessAttempt().GetVideoLocation(),
			},
		},
	}

	if err = s.persistentQueue.InsertElement(ctx, queueElement); err != nil {
		logger.Error(ctx, "error in inserting element into persistent queue", zap.Error(err))
		return err
	}

	return nil
}

func (s *Service) getRecipientPhoneNumberRereg(ctx context.Context, afuId string) *commontypes.PhoneNumber {
	afuRecord, err := s.afuDao.GetById(ctx, afuId)
	if err != nil {
		logger.Error(ctx, "error in getting afu record", zap.Error(err))
		return nil
	}

	deviceAttempt, err := s.DeviceAttemptDao.GetLastAttemptByActor(ctx, afuRecord.GetActorId())
	if err != nil {
		logger.Info(ctx, "error in getting device registration attempt record", zap.Error(err))
		return nil
	}

	parsedPhoneNumber, err := commontypes.ParsePhoneNumber(deviceAttempt.GetPhoneNumber())
	if err != nil {
		logger.Error(ctx, "error in parsing phone number", zap.Error(err))
		return nil
	}
	return parsedPhoneNumber
}

func (s *Service) createAFUModelAlertInCaseManagement(ctx context.Context, afuRecord *afu.AuthFactorUpdate) error {
	resp, err := s.caseManagementClient.CreateAlerts(ctx, &cmPb.CreateAlertsRequest{
		Alerts: []*cmPb.RawAlert{
			{
				ActorId:    afuRecord.GetActorId(),
				EntityType: enums.EntityType_ENTITY_TYPE_AFU,
				EntityId:   afuRecord.GetId(),
				Identifier: &cmPb.RuleIdentifier{
					Identifier: &cmPb.RuleIdentifier_ExternalId{
						ExternalId: s.Conf.AFU().AFUModelExternalRuleId(),
					},
				},
			},
		},
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return fmt.Errorf("error in creating alert %w", err)
	}
	if resp.GetFailureCount() != 0 {
		return fmt.Errorf("failed to create alert")
	}
	return nil
}

func CheckReleaseStickinessConstraint(ctx context.Context, actorId, feature string, rolloutPercentage int) bool {
	featureAndActor := fmt.Sprintf("%v%v", feature, actorId)
	num, err := release.GetHashNum(featureAndActor)
	if err != nil {
		logger.Error(ctx, "error while generating hash for actor", zap.Error(err))
		return false
	}
	if num%100 < uint64(rolloutPercentage) {
		logger.Info(ctx, "StickinessConstraint evaluation returned true", zap.String(logger.FEATURE, feature))
		return true
	}
	return false
}
