package biometrics

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	biometricsPb "github.com/epifi/gamma/api/auth/biometrics"
	"github.com/epifi/gamma/auth/biometrics/dao/mocks"
)

func TestService_GetBiometricsDetails(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockBiometricDao := mocks.NewMockBiometricsDao(ctrl)

	type args struct {
		ctx context.Context
		req *biometricsPb.GetBiometricsDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		want       *biometricsPb.GetBiometricsDetailsResponse
		wantErr    bool
		setupMocks func()
	}{
		{
			name: "Successful GetBiometricsDetails",
			args: args{
				ctx: ctx,
				req: &biometricsPb.GetBiometricsDetailsRequest{
					ActorId: "actor-1",
				},
			},
			want: &biometricsPb.GetBiometricsDetailsResponse{
				Status:          rpc.StatusOk(),
				BiometricStatus: biometricsPb.BiometricStatus_BIOMETRIC_STATUS_VERIFIED,
				BiometricInfo: &biometricsPb.BiometricInfo{
					BiometricId: "biometric-1",
					AppPlatform: commontypes.Platform_ANDROID,
					DeviceId:    "device-1",
					AppVersion:  280,
				},
				BiometricTableId: "id-1",
			},
			wantErr: false,
			setupMocks: func() {
				mockBiometricDao.EXPECT().GetByActorId(gomock.Any(), "actor-1", nil).Return(&biometricsPb.Biometrics{
					Id:      "id-1",
					ActorId: "actor-1",
					BiometricInfo: &biometricsPb.BiometricInfo{
						BiometricId: "biometric-1",
						AppPlatform: commontypes.Platform_ANDROID,
						DeviceId:    "device-1",
						AppVersion:  280,
					},
					Status: biometricsPb.BiometricStatus_BIOMETRIC_STATUS_VERIFIED,
				}, nil).Times(1)
			},
		},
		{
			name: "Failed GetBiometricsDetails",
			args: args{
				ctx: ctx,
				req: &biometricsPb.GetBiometricsDetailsRequest{
					ActorId: "",
				},
			},
			want: &biometricsPb.GetBiometricsDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg("actorId cannot be empty"),
			},
			wantErr: false,
			setupMocks: func() {
				mockBiometricDao.EXPECT().GetByActorId(gomock.Any(), "", nil).
					Return(nil, errors.New("actorId cannot be empty")).Times(1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				biometricsDao: mockBiometricDao,
			}
			tt.setupMocks()

			got, err := s.GetBiometricsDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBiometricsDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetBiometricsDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_UpdateBiometricsDetails(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockBiometricDao := mocks.NewMockBiometricsDao(ctrl)

	type args struct {
		ctx context.Context
		req *biometricsPb.UpdateBiometricsDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		want       *biometricsPb.UpdateBiometricsDetailsResponse
		wantErr    bool
		setupMocks func()
	}{
		{
			name: "Successful UpdateBiometricsDetails",
			args: args{
				ctx: ctx,
				req: &biometricsPb.UpdateBiometricsDetailsRequest{
					ActorId:                "actor-1",
					UpdatedBiometricStatus: biometricsPb.BiometricStatus_BIOMETRIC_STATUS_VERIFIED,
				},
			},
			want: &biometricsPb.UpdateBiometricsDetailsResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
			setupMocks: func() {
				mockBiometricDao.EXPECT().GetByActorId(gomock.Any(), "actor-1", nil).Return(&biometricsPb.Biometrics{
					Id:      "id-1",
					ActorId: "actor-1",
					BiometricInfo: &biometricsPb.BiometricInfo{
						BiometricId: "biometric-1",
						AppPlatform: commontypes.Platform_IOS,
						DeviceId:    "device-id-1",
						AppVersion:  369,
					},
					Status: biometricsPb.BiometricStatus_BIOMETRIC_STATUS_REVOKED,
				}, nil).Times(1)

				mockBiometricDao.EXPECT().Update(ctx, gomock.Any(), gomock.Any()).Return(nil).Times(1)
			},
		},
		{
			name: "Failed UpdateBiometricsDetails",
			args: args{
				ctx: ctx,
				req: &biometricsPb.UpdateBiometricsDetailsRequest{
					ActorId:                "actor-1",
					UpdatedBiometricStatus: biometricsPb.BiometricStatus_BIOMETRIC_STATUS_VERIFIED,
				},
			},
			want: &biometricsPb.UpdateBiometricsDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
			setupMocks: func() {
				mockBiometricDao.EXPECT().GetByActorId(gomock.Any(), "actor-1", nil).
					Return(nil, epifierrors.ErrRecordNotFound).Times(1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				biometricsDao: mockBiometricDao,
			}
			tt.setupMocks()

			got, err := s.UpdateBiometricsDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateBiometricsDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateBiometricsDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}
