package dao

import (
	"context"

	"github.com/google/wire"

	biometricsPb "github.com/epifi/gamma/api/auth/biometrics"
)

//go:generate mockgen -source=./dao.go -destination=./mocks/mock_dao.go -package=mocks
//go:generate dao_metrics_gen .

var BiometricsDaoWireSet = wire.NewSet(
	NewBiometricsPgdb, wire.Bind(new(BiometricsDao), new(*BiometricsPgdb)),
)

type BiometricsDao interface {
	Create(ctx context.Context, biometric *biometricsPb.Biometrics) (*biometricsPb.Biometrics, error)
	GetById(ctx context.Context, id string, fields []biometricsPb.BiometricsFieldMask) (*biometricsPb.Biometrics, error)
	GetByActorId(ctx context.Context, actorId string, fields []biometricsPb.BiometricsFieldMask) (*biometricsPb.Biometrics, error)
	Update(ctx context.Context, biometrics *biometricsPb.Biometrics, updateMasks []biometricsPb.BiometricsFieldMask) error
	DeleteById(ctx context.Context, id string) error
}
