// nolint
package dao

import (
	"context"
	"time"

	"github.com/epifi/be-common/pkg/async/goroutine"
	dbTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	storage "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	biometricsPb "github.com/epifi/gamma/api/auth/biometrics"
	"github.com/epifi/gamma/auth/biometrics/dao/model"
	"github.com/epifi/gamma/auth/biometrics/metrics"
)

type BiometricsPgdb struct {
	// Dependencies are to be initialized or can be injected top down
	db    dbTypes.AuthPGDB
	idGen idgen.IdGenerator
}

func NewBiometricsPgdb(db dbTypes.AuthPGDB, idGen idgen.IdGenerator) *BiometricsPgdb {
	return &BiometricsPgdb{
		db:    db,
		idGen: idGen,
	}
}

var _ BiometricsDao = &BiometricsPgdb{}

var biometricsFieldMaskMap = map[biometricsPb.BiometricsFieldMask]string{
	biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_ID:             "id",
	biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_ACTOR_ID:       "actor_id",
	biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_BIOMETRIC_INFO: "biometrics_info",
	biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_STATUS:         "status",
	biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_VERIFIED_AT:    "verified_at",
	biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_CREATED_AT:     "created_at",
	biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_UPDATED_AT:     "updated_at",
	biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_DELETED_AT:     "deleted_at",
	biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_SUB_STATUS:     "sub_status",
}

func (b *BiometricsPgdb) Create(ctx context.Context, biometrics *biometricsPb.Biometrics) (*biometricsPb.Biometrics, error) {
	defer metric_util.TrackDuration("auth/biometrics/dao", "BiometricsPgdb", "Create", time.Now())
	id, err := b.idGen.Get(idgen.Biometrics)
	if err != nil {
		return nil, errors.Wrap(err, "id generation failed")
	}

	db := gormctxv2.FromContextOrDefault(ctx, b.db)
	biometricModel := model.NewBiometrics(biometrics)
	biometricModel.Id = id

	res := db.Create(biometricModel)
	if res.Error != nil {
		if storage.IsDuplicateRowError(res.Error) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, res.Error
	}

	return biometricModel.GetProto(), nil
}

func (b *BiometricsPgdb) GetById(ctx context.Context, id string, fields []biometricsPb.BiometricsFieldMask) (*biometricsPb.Biometrics, error) {
	defer metric_util.TrackDuration("auth/biometrics/dao", "BiometricsPgdb", "GetById", time.Now())
	if id == "" {
		return nil, errors.New("id cannot be empty")
	}

	db := gormctxv2.FromContextOrDefault(ctx, b.db)
	biometricsModel := model.Biometrics{}

	if len(fields) > 0 {
		db = db.Select(b.getSelectedColumns(fields))
	}
	err := db.Where("id = ?", id).First(&biometricsModel).Error
	if err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "failed to fetch biometrics by id")
	}

	return biometricsModel.GetProto(), nil
}

func (b *BiometricsPgdb) GetByActorId(ctx context.Context, actorId string, fields []biometricsPb.BiometricsFieldMask) (*biometricsPb.Biometrics, error) {
	defer metric_util.TrackDuration("auth/biometrics/dao", "BiometricsPgdb", "GetByActorId", time.Now())
	if actorId == "" {
		return nil, errors.New("actorId cannot be empty")
	}

	db := gormctxv2.FromContextOrDefault(ctx, b.db)
	biometricsModel := model.Biometrics{}
	if len(fields) > 0 {
		db = db.Select(b.getSelectedColumns(fields))
	}

	err := db.Where("actor_id = ?", actorId).Order("created_at DESC").Take(&biometricsModel).Error
	if err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "failed to fetch biometrics by actorId")
	}

	return biometricsModel.GetProto(), nil
}

func (b *BiometricsPgdb) DeleteById(ctx context.Context, id string) error {
	defer metric_util.TrackDuration("auth/biometrics/dao", "BiometricsPgdb", "DeleteById", time.Now())
	if id == "" {
		return errors.New("id cannot be empty")
	}

	db := gormctxv2.FromContextOrDefault(ctx, b.db)

	res := db.Delete(&model.Biometrics{Id: id})
	if res.Error != nil {
		return errors.Wrap(res.Error, "error deleting biometrics")
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrNoRowsAffected
	}
	return nil
}

func (b *BiometricsPgdb) Update(ctx context.Context, biometrics *biometricsPb.Biometrics, updateMasks []biometricsPb.BiometricsFieldMask) error {
	defer metric_util.TrackDuration("auth/biometrics/dao", "BiometricsPgdb", "Update", time.Now())
	if biometrics.GetId() == "" {
		return errors.New("id cannot be empty")
	}

	if len(updateMasks) == 0 {
		return errors.New("update field masks list cannot be empty")
	}

	updateColumns := b.getSelectedColumns(updateMasks)

	biometricsModel := model.NewBiometrics(biometrics)
	db := gormctxv2.FromContextOrDefault(ctx, b.db)

	res := db.Model(biometricsModel).Where("id = ?", biometrics.GetId()).Select(updateColumns).Updates(biometricsModel)
	if res.Error != nil {
		return errors.Wrap(res.Error, "error updating biometrics")
	}
	if res.RowsAffected == 0 {
		return errors.Wrap(epifierrors.ErrRowNotUpdated, "error updating record")
	}

	b.recordBiometricsMetrics(ctx, updateMasks, biometrics)

	return nil
}

func (b *BiometricsPgdb) getSelectedColumns(updateMasks []biometricsPb.BiometricsFieldMask) []string {
	selectedColumns := make([]string, 0)
	for _, mask := range updateMasks {
		if mask == biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_UNSPECIFIED {
			continue
		}
		selectedColumns = append(selectedColumns, biometricsFieldMaskMap[mask])
	}
	return selectedColumns
}

func (b *BiometricsPgdb) recordBiometricsMetrics(ctx context.Context, updateMasks []biometricsPb.BiometricsFieldMask, biometrics *biometricsPb.Biometrics) {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		if !lo.Contains(updateMasks, biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_VERIFIED_AT) {
			return
		}

		createdAt := biometrics.GetCreatedAt()

		if biometrics.GetCreatedAt() == nil {
			oldBiometrics, err := b.GetById(ctx, biometrics.GetId(), []biometricsPb.BiometricsFieldMask{biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_CREATED_AT})
			if err != nil {
				return
			}

			createdAt = oldBiometrics.GetCreatedAt()
		}

		metrics.RecordBiometricLivenessStatusTime(biometrics.GetStatus(), biometrics.GetVerifiedAt().AsTime().Sub(createdAt.AsTime()).Seconds())
	})
}
