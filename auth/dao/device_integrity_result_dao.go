package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	dbTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/auth/dao/model"
)

const (
	maxFetchLimit = 101
)

var (
	DeviceIntegrityResDeviceIdUnspecifiedErr = errors.New("deviceId not specified")
	DeviceIntegrityResActorIdUnspecifiedErr  = errors.New("actorId not specified")
	DeviceIntegrityResIdUnspecifiedErr       = errors.New("id not specified")
)

var DeviceIntegrityResFieldMaskMap = map[authPb.DeviceIntegrityResultFieldMask]string{
	authPb.DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_ID:             "id",
	authPb.DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_ACTOR_ID:       "actor_id",
	authPb.DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_DEVICE_ID:      "device_id",
	authPb.DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_ATTESTATION:    "attestation",
	authPb.DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_RESULT:         "result",
	authPb.DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_ADVICE:         "advice",
	authPb.DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_FAILURE_REASON: "failure_reason",
	authPb.DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_DEVICE_OS:      "device_os",
	authPb.DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_CREATED_AT:     "created_at",
	authPb.DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_UPDATED_AT:     "updated_at",
	authPb.DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_DELETED_AT:     "deleted_at",
}

type DeviceIntegrityResultDaoCrdb struct {
	db dbTypes.EpifiCRDB
}

func NewDeviceIntegrityResultDaoCrdb(db dbTypes.EpifiCRDB) *DeviceIntegrityResultDaoCrdb {
	return &DeviceIntegrityResultDaoCrdb{
		db: db,
	}
}

var _ DeviceIntegrityResultDao = &DeviceIntegrityResultDaoCrdb{}

func (crdb *DeviceIntegrityResultDaoCrdb) Create(ctx context.Context,
	res *authPb.DeviceIntegrityResult) (*authPb.DeviceIntegrityResult, error) {
	defer metric_util.TrackDuration("auth/dao", "DeviceIntegrityResultDaoCrdb", "Create", time.Now())
	if res.GetDeviceId() == "" {
		return nil, fmt.Errorf("error while creating device integrity result: %w", IosAttstDeviceIdUnspecifiedErr)
	}
	db := gormctxv2.FromContextOrDefault(ctx, crdb.db)
	entry := MarshalDeviceIntegrityResult(res)
	if err := db.Create(&entry).Error; err != nil {
		return nil, fmt.Errorf("failed to create device integrity result entry: %w", err)
	}
	return UnmarshalDeviceIntegrityResult(entry), nil
}

func (crdb *DeviceIntegrityResultDaoCrdb) GetLatestResultForActorAndDevice(ctx context.Context, actorId string,
	deviceId string) (*authPb.DeviceIntegrityResult, error) {
	defer metric_util.TrackDuration("auth/dao", "DeviceIntegrityResultDaoCrdb", "GetLatestResultForActorAndDevice", time.Now())
	var entry model.DeviceIntegrityResult
	if actorId == "" {
		return nil, fmt.Errorf("error fetching device integrity result: %w", DeviceIntegrityResActorIdUnspecifiedErr)
	}
	if deviceId == "" {
		return nil, fmt.Errorf("error fetching device integrity result: %w", DeviceIntegrityResDeviceIdUnspecifiedErr)
	}
	db := gormctxv2.FromContextOrDefault(ctx, crdb.db)
	db = db.Model(&model.DeviceIntegrityResult{}).Where(&model.DeviceIntegrityResult{
		ActorId:  actorId,
		DeviceId: deviceId,
	}).Order("created_at desc").Take(&entry)
	if db.Error != nil {
		if storagev2.IsRecordNotFoundError(db.Error) {
			return nil, db.Error
		}
		return nil, fmt.Errorf("error fetching device integrity result: %w", db.Error)
	}
	return UnmarshalDeviceIntegrityResult(&entry), nil
}

func (crdb *DeviceIntegrityResultDaoCrdb) Update(ctx context.Context, attempt *authPb.DeviceIntegrityResult,
	updateMask []authPb.DeviceIntegrityResultFieldMask) error {
	defer metric_util.TrackDuration("auth/dao", "DeviceIntegrityResultDaoCrdb", "Update", time.Now())
	if attempt.Id == "" {
		return DeviceIntegrityResIdUnspecifiedErr
	}
	if len(updateMask) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}
	updateColumns := SelectedColumnsForUpdate(updateMask)
	m := MarshalDeviceIntegrityResult(attempt)
	whereClause := &model.DeviceIntegrityResult{Id: attempt.Id}
	db := gormctxv2.FromContextOrDefault(ctx, crdb.db)
	return db.Model(m).Where(whereClause).Select(updateColumns).Updates(m).Error
}

func (crdb *DeviceIntegrityResultDaoCrdb) GetById(ctx context.Context, id string) (*authPb.DeviceIntegrityResult, error) {
	defer metric_util.TrackDuration("auth/dao", "DeviceIntegrityResultDaoCrdb", "GetById", time.Now())
	if id == "" {
		return nil, fmt.Errorf("id can't be empty: %w", epifierrors.ErrInvalidArgument)
	}

	var m model.DeviceIntegrityResult

	// If ctx is carrying the gorm transaction connection object, use it.
	// if not, use the gorm.DB connection provided by the dao.
	d := gormctxv2.FromContextOrDefault(ctx, crdb.db)

	if err := d.Where(&model.DeviceIntegrityResult{
		Id: id,
	}).Take(&m).Error; err != nil {
		return nil, err
	}

	return UnmarshalDeviceIntegrityResult(&m), nil
}

func (crdb *DeviceIntegrityResultDaoCrdb) GetResultByDeviceId(ctx context.Context, deviceId string) (*authPb.DeviceIntegrityResult, error) {
	defer metric_util.TrackDuration("auth/dao", "DeviceIntegrityResultDaoCrdb", "GetResultByDeviceId", time.Now())
	if deviceId == "" {
		return nil, fmt.Errorf("deviceId can't be empty: %w", epifierrors.ErrInvalidArgument)
	}
	var m model.DeviceIntegrityResult
	// If ctx is carrying the gorm transaction connection object, use it.
	// if not, use the gorm.DB connection provided by the dao.
	d := gormctxv2.FromContextOrDefault(ctx, crdb.db)

	if err := d.Where(&model.DeviceIntegrityResult{
		DeviceId: deviceId,
	}).Order("created_at desc").Limit(1).Take(&m).Error; err != nil {
		return nil, err
	}
	return UnmarshalDeviceIntegrityResult(&m), nil
}

func (crdb *DeviceIntegrityResultDaoCrdb) GetResultsByDeviceId(ctx context.Context, deviceId string, limit uint) ([]*authPb.DeviceIntegrityResult, error) {
	defer metric_util.TrackDuration("auth/dao", "DeviceIntegrityResultDaoCrdb", "GetResultsByDeviceId", time.Now())
	if deviceId == "" {
		return nil, fmt.Errorf("deviceId can't be empty: %w", epifierrors.ErrInvalidArgument)
	}
	if limit > maxFetchLimit {
		logger.Info(ctx, fmt.Sprintf("max limit is %v", maxFetchLimit))
		return nil, fmt.Errorf("max limit exceeded: %w", epifierrors.ErrInvalidArgument)
	}
	var m []*model.DeviceIntegrityResult
	// If ctx is carrying the gorm transaction connection object, use it.
	// if not, use the gorm.DB connection provided by the dao.
	d := gormctxv2.FromContextOrDefault(ctx, crdb.db)

	if err := d.Where(&model.DeviceIntegrityResult{
		DeviceId: deviceId,
	}).Order("created_at desc").Limit(int(limit)).Find(&m).Error; err != nil {
		return nil, err
	}
	deviceIntegrityResults := make([]*authPb.DeviceIntegrityResult, 0)
	for _, modelResult := range m {
		deviceIntegrityResults = append(deviceIntegrityResults, UnmarshalDeviceIntegrityResult(modelResult))
	}
	return deviceIntegrityResults, nil
}

func (crdb *DeviceIntegrityResultDaoCrdb) DeleteByDeviceId(ctx context.Context, deviceId string) error {
	defer metric_util.TrackDuration("auth/dao", "DeviceIntegrityResultDaoCrdb", "DeleteByDeviceId", time.Now())
	if deviceId == "" {
		return fmt.Errorf("deviceId can't be empty: %w", epifierrors.ErrInvalidArgument)
	}
	// If ctx is carrying the gorm transaction connection object, use it.
	// if not, use the gorm.DB connection provided by the dao.

	d := gormctxv2.FromContextOrDefault(ctx, crdb.db)
	return d.Where(&model.DeviceIntegrityResult{
		DeviceId: deviceId,
	}).Delete(&model.DeviceIntegrityResult{}).Error
}

// getSelectColumnsForUpdateOrder converts update mask to string slice with column
// names corresponding to field name enums
func SelectedColumnsForUpdate(updateMask []authPb.DeviceIntegrityResultFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMask {
		selectColumns = append(selectColumns, DeviceIntegrityResFieldMaskMap[field])
	}
	return selectColumns
}

func MarshalDeviceIntegrityResult(data *authPb.DeviceIntegrityResult) *model.DeviceIntegrityResult {
	return &model.DeviceIntegrityResult{
		Id:            data.GetId(),
		ActorId:       data.GetActorId(),
		DeviceId:      data.GetDeviceId(),
		Attestation:   data.GetAttestation(),
		Result:        data.GetResult(),
		FailureReason: data.GetFailureReason(),
		DeviceOs:      data.GetDeviceOs(),
		Advice:        data.GetAdvice(),
	}
}

func UnmarshalDeviceIntegrityResult(data *model.DeviceIntegrityResult) *authPb.DeviceIntegrityResult {
	var deletedAt *timestamppb.Timestamp
	if data.DeletedAt.Valid {
		deletedAt = timestamppb.New(data.DeletedAt.Time)
	}
	return &authPb.DeviceIntegrityResult{
		Id:            data.Id,
		ActorId:       data.ActorId,
		DeviceId:      data.DeviceId,
		Attestation:   data.Attestation,
		DeviceOs:      data.DeviceOs,
		Result:        data.Result,
		Advice:        data.Advice,
		FailureReason: data.FailureReason,
		CreatedAt:     timestamppb.New(data.CreatedAt),
		UpdatedAt:     timestamppb.New(data.UpdatedAt),
		DeletedAt:     deletedAt,
	}
}
