package config

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/frontend/app"
	sdkconfig "github.com/epifi/be-common/quest/sdk/config"

	"github.com/epifi/gamma/auth/keycloak"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(false)
	})

	if err != nil {
		return nil, err
	}

	return config, err
}

// LoadOnlyStaticConf loads configs only from static config files if not done already and returns it
func LoadOnlyStaticConf() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(true)
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}
	return config, nil
}

func loadConfig(onlyStaticFiles bool) (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.AUTH_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	if onlyStaticFiles {
		return conf, nil
	}
	if loadSecretsErr := cfg.LoadAllSecretsV3(conf, conf.Application.Environment, conf.Aws.Region); loadSecretsErr != nil {
		return nil, loadSecretsErr
	}
	if err = updatePlayIntegrityConfig(conf); err != nil {
		return nil, err
	}
	if err = updateKeycloakConfig(conf); err != nil {
		return nil, err
	}

	if len(conf.Application.SMSConfig.PhoneNumbers) == 0 {
		log.Fatal("Need atleast one phone number to be configured for sending device registration SMSes")
	}
	return conf, nil
}

func updatePlayIntegrityConfig(c *Config) error {
	c.PlayIntegrity = &PlayIntegrity{}
	if len(c.AuthSecrets.PlayIntegrityKeys) == 0 && !cfg.IsLocalEnv(c.Application.Environment) {
		return errors.New("play integrity keys not fetched from secrets manager")
	}
	if err := json.Unmarshal([]byte(c.AuthSecrets.PlayIntegrityKeys), c.PlayIntegrity); err != nil {
		panic(fmt.Errorf("unable to unmarshal username password json received from secrets manager: %w", err))
	}
	return nil
}

func updateKeycloakConfig(c *Config) error {
	c.Keycloak.Secrets = &keycloak.Secrets{}
	if val, ok := os.LookupEnv("KC_HOSTNAME_ADMIN"); ok && c.Application.Environment == cfg.TestEnv {
		c.Keycloak.BaseURL = "http://" + val + ":5400"
	}
	if len(c.AuthSecrets.KeycloakBKYC) == 0 && !cfg.IsLocalEnv(c.Application.Environment) {
		return errors.New("play integrity keys not fetched from secrets manager")
	}
	if err := json.Unmarshal([]byte(c.AuthSecrets.KeycloakBKYC), c.Keycloak.Secrets); err != nil {
		panic(fmt.Errorf("unable to unmarshal keycloak json json received from secrets manager: %w", err))
	}
	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

type OAuthVerifierType int

const (
	Real OAuthVerifierType = iota
	Stub
	Hybrid
)

// TODO(kunal): Add validate tags later and verify config struct post unmarshalling
//
//go:generate conf_gen github.com/epifi/gamma/auth/config Config
type Config struct {
	Application                            *Application
	Server                                 *Server
	Logging                                *cfg.Logging
	Aws                                    *Aws
	KarzaLivenessSubscriber                *cfg.SqsSubscriber `dynamic:"true"`
	LivenessStatusSubscriber               *cfg.SqsSubscriber `dynamic:"true"`
	LivenessStatusPublisher                *cfg.SqsPublisher
	LivenessManualReviewPublisher          *cfg.SnsPublisher
	LivenessSummaryCompletedEventPublisher *cfg.SnsPublisher
	BankConfig                             *BankConfig
	RudderStack                            *RudderBroker
	// TODO(Shivam): Rename to Secrets after removing the older secrets
	AuthSecrets *AuthSecrets

	Liveness *Liveness `dynamic:"true"`

	// Auth Factor Update
	AFU                                       *AFU   `dynamic:"true"`
	Flags                                     *Flags `dynamic:"true"`
	AFUVendorUpdatePublisher                  *cfg.SqsPublisher
	AuthFactorUpdatePublisher                 *cfg.SnsPublisher
	BiometricEventPublisher                   *cfg.SqsPublisher
	AuthTokenCreationPublisher                *cfg.SnsPublisher
	AFUVendorUpdateSubscriber                 *cfg.SqsSubscriber     `dynamic:"true"`
	DeviceReregCallbackSubscriber             *cfg.SqsSubscriber     `dynamic:"true"`
	DeviceRegSMSAckSubscriber                 *cfg.SqsSubscriber     `dynamic:"true"`
	ProcessPinAttemptsExceededEventSubscriber *cfg.SqsSubscriber     `dynamic:"true"`
	AFUManualReviewNotificationSubscriber     *cfg.SqsSubscriber     `dynamic:"true"`
	DeviceIntegrityConfig                     *DeviceIntegrityConfig `dynamic:"true"`
	ReturnOtpTokenOnError                     bool
	OtpConfig                                 *OtpConfig `dynamic:"true"`
	KeyCompromisedCheckConfig                 *KeyCompromisedCheckConfig
	RedisOptions                              *cfg.RedisOptions
	TokenStoresCacheConfig                    *TokenStoresCacheConfig          `dynamic:"true"`
	DeviceLocationCacheConfig                 *DeviceLocationCacheConfig       `dynamic:"true"`
	DeviceIntegrityNonceCacheConfig           *DeviceIntegrityNonceCacheConfig `dynamic:"true"`
	DeviceRegistrationCacheConfig             *DeviceRegistrationCacheConfig   `dynamic:"true"`
	Tracing                                   *cfg.Tracing
	Profiling                                 *cfg.Profiling
	GetSessionParams                          *GetSessionParams
	PlayIntegrity                             *PlayIntegrity
	DeviceRegistrationSMS                     *DeviceRegistrationSMSCfg `dynamic:"true"`
	DeviceRegistration                        *DeviceRegistration
	Keycloak                                  *keycloak.AdminClientConfig
	QuestSdk                                  *sdkconfig.Config   `dynamic:"true"`
	TokenManagerConfig                        *TokenManagerConfig `dynamic:"true"`
	NetworthMcpConfig                         *NetworthMcpConfig  `dynamic:"true"`
}

type NetworthMcpConfig struct {
	LoginUrl             string `dynamic:"true"`
	EnableSessionSigning bool   `dynamic:"true"`
}

type TokenManagerConfig struct {
	Enable                       string `dynamic:"true"`
	WhitelistedActorIds          []string
	WhitelistedPhoneNumberHashes []string
	// In case of cache miss, disable fetching tokens from DB
	// This is a fire-fighting tool. Use it only in dire situations when auth db is degraded and users are unable to login.
	DisableFallbackToDBForAccessTokens bool `dynamic:"true"`
}

type DeviceRegistration struct {
	SMSAckUrl string
}

type DeviceRegistrationSMSCfg struct {
	EndpointsEnabled              map[string]bool `dynamic:"true"`
	DownThrottleDisabledToPercent uint8           `dynamic:"true"`
	EnableV2SMSEndpointSelector   bool            `dynamic:"true"`
	// maximum number of device registration SMS payload user can generate in a day
	MaxDeviceRegAttemptPerDay int `dynamic:"true"`
}

type Liveness struct {
	OnboardingLivenessRuleId string `dynamic:"true"` // rule identifier id for creating alerts in case management in onboarding flow
}

type Application struct {
	Environment            string
	Name                   string
	AndroidClientSignature string
	// Android Client ID registered with Google OAuth service
	OAuthAndroidClientID string
	// iOS Client ID registered with Google OAuth service
	OAuthIOSClientID                     string
	UserAccessTokenSigningMethod         *TokenSigningMethod
	UserRefreshTokenSigningMethod        *TokenSigningMethod
	WaitlistUserAccessTokenSigningMethod *TokenSigningMethod
	AppInsightsAccessTokenSigningMethod  *TokenSigningMethod
	ChatbotAccessTokenSigningMethod      *TokenSigningMethod
	WebLiteAccessTokenSigningMethod      *TokenSigningMethod
	GenieRefreshTokenSigningMethod       *TokenSigningMethod
	GenieAccessTokenSigningMethod        *TokenSigningMethod
	// Web Closed Accounts Balance Transfer
	WebCABTRefreshTokenSigningMethod           *TokenSigningMethod
	WebCABTAccessTokenSigningMethod            *TokenSigningMethod
	RiskOutcallWebformAccessTokenSigningMethod *TokenSigningMethod
	NetworthMcpAccessTokenSigningMethod        *TokenSigningMethod

	BKYCHandshakeTokenSigningMethod       *TokenSigningMethod
	NuggetChatbotAccessTokenSigningMethod *TokenSigningMethod

	// Liveness
	UseFFMPEGToExtractFrame bool

	SMSConfig                    *DeviceRegistrationSMSConfig
	LivenessConfig               *LivenessConfig
	EnableHybridOAuthVerifier    bool
	NPCIDeviceBindingLimitPerDay int64
	SkipClientIDCheck            bool
	OAuthVerifierType            OAuthVerifierType
	// iOS Client ID registered with Apple OAuth services
	AppleOAuthIOSClientID              string
	AppleClientSecretValidityInMinutes time.Duration
}

type DeviceIntegrityConfig struct {
	// epifi android app's package name
	AndroidAppPackageNames []string
	// Base-64 encoded representation(s) of the SHA-256 hash of android release and debug app's signing certificate(s)
	AndroidApkCertificateDigestSha256 []string
	// Device Integrity nonce validity duration
	DeviceIntegrityNonceValidityInSecs int
	// do we need to validate ApkPackageName
	CheckApkPackageName bool
	// do we need to validate ApkCertificateDigestSha256
	CheckCertificateHash bool
	// required for verifying the validity of certificates
	// while verifying ios app attestation
	AppleAppAttestRootCertificate string
	// asn1 object identifier for the extension containing the nonce
	AppleCredCertOidExtensionSeq []int
	// epifi iOS app identifier
	IosAppIdentifiers []string
	// modify current time while verifying safetynet token cert chain
	AllowSafetynetCertChainVerificationWithModifiedCurrTime bool
	// modified current time while verifying safetynet token cert chain.
	// this value will only be considered when `AllowSafetynetCertChainVerificationWithModifiedCurrTime` flag is set to true
	CurrTimeOverrideForSafetynetCertVerification time.Duration
	// whitelisting of phone numbers to skip their id token
	// validation in simulated environment
	BypassTokenIdValidationForPhoneNumbers []string
	// min version to enable device attestation V2 flow
	DeviceAttestationV2Cfg *app.FeatureConfig `dynamic:"true"`
	// controlled rollout for async device integrity checks
	AsyncDeviceIntegrityRolloutPercentage int `dynamic:"true"`
	// expiry time for verified device integrity
	ExpiryTimeForDeviceIntegrity time.Duration `dynamic:"true"`
	// extended validity for device integrity v2 on app versions which perform on-device checks
	ExpiryTimeForDeviceIntegrityV2 time.Duration `dynamic:"true"`
	// Using random expiry time to avoid sudden expiry burst of tokens which creates alerts
	RandomExpiryInSecondsForDeviceIntegrityV2 int `dynamic:"true"`
	// app version configuration which have enabled on device integrity checks
	OnDeviceIntegrityCheckCfg *app.FeatureConfig `dynamic:"true"`

	MockSafetynetTokenResult map[string]string `dynamic:"true"`

	WhitelistedAttestationTokens []string
	// device integrity gets bypassed for these phone numbers
	BypassPhoneNumbers []string `dynamic:"true"`
}

type TokenSigningMethod struct {
	// Token validity in seconds
	Duration int
	// Inactivity timer after which the token would be marked as inactive/expired
	InActivityTimer int
	// Hashing algo used in JWT signing
	Algo string
	// A flag to determine if retired key is present
	IsRetiredKeyPresent bool
}

type LivenessConfig struct {
	FMThreshold                       float32
	LivenessThreshold                 float32
	LenientLivenessThreshold          float32
	MinimumLivenessThreshold          float32
	OTPThreshold                      float32
	LivenessThresholdRetry            float32
	OTPThresholdRetry                 float32
	InhouseLivenessTimeout            int32
	StrictnessLogicAFMThreshold       float32
	StrictnessLogicALivenessThreshold float32
	// probability for an element to enter merged(liveness and facematch) queue
	LivenessAndFacematchProbability int32
	UserBucketName                  string `iam:"s3-readwrite"`
}

type DeviceRegistrationSMSConfig struct {
	// List of valid National numbers on which the SMS for device registration can be sent
	PhoneNumbers             []uint64
	DeviceRegistrationPrefix string
}

type Server struct {
	Ports *cfg.ServerPorts
}

type Aws struct {
	Endpoint string
	Region   string
	S3       *S3
}

type S3 struct {
	LivenessBucketName string `iam:"s3-readwrite"`
}

type BankConfig struct {
	CardPinLength   uint32
	SecurePinLength uint32
	BankLogoUrl     string
	BankName        string
	VendorOtpLength uint32
}

type AFU struct {
	// Following are the 2 maps which conveys the order in which credentials
	// need to be validated.They are categorised by different auth factor
	// update scenarios.

	// Credentials validation order for Registered/Old device.
	// map[afu.Combination][]afu.Credential
	CredentialsOrder                map[string][][]string
	VendorUpdateProducerDelayInSecs int32 `dynamic:"true"`

	// for these phone numbers liveness, atm pin validation & device registration steps would be skipped.
	// the users would be redirected to Check-AFU-Status screen and then to home page.
	// Use case: help expedite ios app review process.
	// phone numbers include country code, e.g. ************
	BypassCredentialVerificationForPhoneNumbers []string
	ReRegInitIncrementalDelay                   time.Duration                `dynamic:"true"`
	LivenessManualReviewExpiryTime              time.Duration                `dynamic:"true"`
	MaxRecordsDepthForAFUTroubleshooter         int                          `dynamic:"true"`
	AuthFactorUpdateCacheConfig                 *AuthFactorUpdateCacheConfig `dynamic:"true"`
	MinVersionForAFURetry                       *app.FeatureConfig

	// Configs to control ingestion of afu cases to case management.

	// external rule id for afu liveness cases used by case management to create alerts
	AFULivenessExternalRuleId string `dynamic:"true"`
	// external rule id for afu model failed cases used by case management to create alerts
	AFUModelExternalRuleId string `dynamic:"true"`
	EnableReRegInitRetry   bool   `dynamic:"true"`
	// Expiry for EKYC attempt done in AFU
	// AFU Vendor update must start before EKYC expires
	ExpiryForEKYC                  time.Duration       `dynamic:"true"`
	VendorUpdateConsumerLockConfig *ConsumerLockConfig `dynamic:"true"`
	AFUCooldown                    *AFUCooldown        `dynamic:"true"`
	// To enable device registration OS version check for IOS devices
	EnableDeviceRegistrationOSValidation bool `dynamic:"true"`
	// ATM PIN validation is required only for some criteria, this flag is to enable that check
	EnableATMPinEligibilityCheck bool `dynamic:"true"`
	// As of now, we rely on existing business logic to handle old requests to check if ATM PIN is required or not.
	// Adding this flag to enable reads only from ATM PIN Check flag
	ReadOnlyFromAtmPinCheckFlag bool `dynamic:"true"`
}

type AFUCooldown struct {
	// AFU cooldown regulation: User is allowed to do AFU only after X consecutive days of a previous successful AFU with the common auth factor
	// EnableCooldown enables the mentioned cooldown logic
	EnableCooldown bool `dynamic:"true"`
	// Key maps to AuthFactor enum afu/auth/auth_factor_update.pb.go
	CooldownDurationMap      map[string]time.Duration `dynamic:"true"`
	CooldownForUPIRegulation time.Duration            `dynamic:"true"`
}

type ConsumerLockConfig struct {
	// Timeout sets the max duration for which the lock can be acquired by a request.
	// This should be little less than the request timeout for most cases.
	Timeout time.Duration `dynamic:"true"`
}

type RudderBroker struct {
	Host          string
	Key           string
	IntervalInSec time.Duration
	BatchSize     int
	Verbose       bool
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
	// A flag to disable iOS ID Token expiry check. This will help in adding tests as without this tests
	// will start failing after the token expiry,
	SkipIOSIdTokenExpiryCheck bool
	// A flag to enable id token verification using newer implementation for android client.
	// Currently newer implementation is only used for iOS clients.
	EnableV2IdTokenVerifierForAndroid bool
	// Flag for pushing manual review of liveness events to persistent queue
	EnableLivenessManualReviewInAfu bool `dynamic:"true"`
	// Flag to check for access revoke
	EnableCheckForAccessRevoke bool
	// Flag to disable change feed on liveness attempts table
	DisableLivenessAttemptChangeFeed bool
	// Flag to enable sms acknowledgement processing for device registration
	EnableSMSAckListener    bool `dynamic:"true"`
	EnableFMV2ForOnboarding bool `dynamic:"true"`
	EnableSmsHealthEngine   bool `dynamic:"true"`
	// Flag to enable signal driven liveness in auth orchestrator
	EnableOrchestratorLivenessV2 bool `dynamic:"true"`
	EnableEKYCInPhoneUpdate      bool `dynamic:"true"`
	// flag to enable liveness video compression before storing to S3
	UseFFMPEGToCompressVideo                 bool               `dynamic:"true"`
	EnableAFURiskScreeningForNRUser          bool               `dynamic:"true"`
	EnablePinRetriesExceededEventConsumer    bool               `dynamic:"true"`
	EnableDeviceRegSMSPayloadGenerationLimit *app.FeatureConfig `dynamic:"true"`
}

type OtpConfig struct {
	// duration after which otp sms can be resent
	OtpResendInterval time.Duration
	// this config defines the number of new otps that can be generated
	// for a phone number within different time durations
	// TODO (Rishu) (remove once CustomGenerateOtpLimits is stable)
	GenerateOtpLimits []*GenerateOtpLimit
	// We want to skip otp validation for few numbers in non-prod numbers for Apple's IOS app review
	SkipOTPValidationForNumbers []string `dynamic:"true"`
	// this config defines the number of new otps that can be generated
	// for a phone number within different time durations with given flow type
	CustomGenerateOtpLimits CustomGenerateOtpLimits
}

type CustomGenerateOtpLimits map[string][]*GenerateOtpLimit

type GenerateOtpLimit struct {
	// number of new otps that can be generated for a phone number within a duration
	Duration    time.Duration
	MaxAttempts int
}

// partner sdk bank key configs
type KeyCompromisedCheckConfig struct {
	IsKeyCompromisedCheckEnable       bool
	IsUserGroupEnabled                bool
	AllowedUserGroup                  []commontypes.UserGroup
	PartnerSdkKeyCompromisedStatusMap *PartnerSdkKeyCompromisedStatusMap
}

// struct to hold partner sdk key compromised status
type PartnerSdkKeyCompromisedStatusMap struct {
	// keyId to compromised status map for android partner sdk keys
	Android map[string]bool
	// keysId to compromised status map for IOS partner sdk keys
	Ios map[string]bool
}

type TokenStoresCacheConfig struct {
	IsCachingEnabled     bool          `dynamic:"true"`
	CacheTTL             time.Duration `dynamic:"true"`
	RefreshTokenCacheTTL time.Duration `dynamic:"true"`
	// prefix to be appended to id while creating the cache keys
	TokenStoresPrefix   string
	TokenStoresPrefixV2 string
	// prefix used to create cache id when storing last activity for a user token
	TokenLastActivityKeyPrefix string

	// DisableTokenStoreWrites is a fire-fighting tool. It disables all insert, update & delete queries on the table.
	// Use it only in dire situations when auth db is degraded and users are unable to login.
	DisableTokenStoreWrites bool `dynamic:"true"`
	// MakeTokenStoreWritesOptional is a fire-fighting tool. It makes insertion into token stores table non-blocking.
	// Use it only in dire situations when auth db is degraded and users are unable to login.
	MakeTokenStoreWritesOptional bool `dynamic:"true"`
	// This will allow DB writes only to unonboarded users, logic for this avoids writes without breaking other flows
	EnableWritesOnlyForUnOnboardedUsers bool `dynamic:"true"`

	// TODO(sayan): cleanup once stable
	// Flag to disable caching by token id
	// If set to true this will stop storing token in cache with token id
	DisableCachingByTokenIdForAccessTokens bool `dynamic:"true"`

	// TODO(sayan): cleanup once stable
	// Flag to enable caching by actor id and token type
	// If set to true, tokens will be stored in cache with actor id and token type
	// *NOTE* - This will not disable caching by token id
	EnableCachingBySubject bool `dynamic:"true"`

	// Map to store cache TTL for various token types
	TokenCacheTTLMap map[string]time.Duration
	// TODO(sayan): cleanup once stable
	// Flag to enable token stores dao v2
	UseTokenStoresDaoV2 bool `dynamic:"true"`
}

type DeviceLocationCacheConfig struct {
	IsCachingEnabled     bool          `dynamic:"true"`
	CacheTTL             time.Duration `dynamic:"true"`
	DeviceLocationPrefix string
}

type AuthFactorUpdateCacheConfig struct {
	// TTLs are set based on the cooldown periods for UPI and AFU regulations.
	IsCachingEnabled       bool `dynamic:"true"`
	AuthFactorUpdatePrefix string
}

type DeviceIntegrityNonceCacheConfig struct {
	IsCachingEnabled bool          `dynamic:"true"`
	CacheTTL         time.Duration `dynamic:"true"`
	ForceOnlyCache   bool          `dynamic:"true"`
}

type DeviceRegistrationCacheConfig struct {
	IsCachingEnabled bool          `dynamic:"true"`
	CacheTTL         time.Duration `dynamic:"true"`
}

type GetSessionParams struct {
	GetSessionParamsLockLeaseDuration time.Duration
	GetSessionParamsLock              string
}

type PlayIntegrity struct {
	DecryptionKey   string
	VerificationKey string
}

type AuthSecrets struct {
	SecretsKey                               string `iam:"sm-read"`
	ActiveUserAccessTokenSigningKey          string `field:"SecretsKey" jsonPath:"activeUserAccessTokenSigningKey"`
	ActiveUserRefreshTokenSigningKey         string `field:"SecretsKey" jsonPath:"activeUserRefreshTokenSigningKey"`
	ActiveWaitlistUserAccessTokenSigningKey  string `field:"SecretsKey" jsonPath:"activeWaitlistUserAccessTokenSigningKey"`
	ActiveAppInsightsAccessTokenSigningKey   string `field:"SecretsKey" jsonPath:"activeAppInsightsAccessTokenSigningKey"`
	ActiveChatbotAccessTokenSigningKey       string `field:"SecretsKey" jsonPath:"activeChatbotAccessTokenSigningKey"`
	ActiveWebLiteAccessTokenSigningKey       string `field:"SecretsKey" jsonPath:"activeWebLiteAccessTokenSigningKey"`
	ActiveDeviceIntegrityTokenSigningKey     string `field:"SecretsKey" jsonPath:"activeDeviceIntegrityTokenSigningKey"`
	RetiredUserAccessTokenSigningKey         string `field:"SecretsKey" jsonPath:"retiredUserAccessTokenSigningKey"`
	RetiredUserRefreshTokenSigningKey        string `field:"SecretsKey" jsonPath:"retiredUserRefreshTokenSigningKey"`
	RetiredWaitlistUserAccessTokenSigningKey string `field:"SecretsKey" jsonPath:"retiredWaitlistUserAccessTokenSigningKey"`
	AppleOAuthIds                            string `field:"SecretsKey" jsonPath:"appleOAuthIds"`
	DeviceIdsEnabledForSafetyNetV2           string `field:"SecretsKey" jsonPath:"deviceIdsEnabledForSafetyNetV2"`
	PlayIntegrityKeys                        string `field:"SecretsKey" jsonPath:"playIntegrityKeys"`
	KeycloakBKYC                             string `field:"SecretsKey" jsonPath:"keycloakBKYC"`
	BKYCHandshakeTokenSigningKey             string `field:"SecretsKey" jsonPath:"bkycHandshakeTokenSigningKey"`
	TotpSaltNwMcp                            string `field:"SecretsKey" jsonPath:"totpSaltNwMcp"`
	ActiveNwMcpAccessTokenSigningKey         string `field:"SecretsKey" jsonPath:"activeNwMcpAccessTokenSigningKey"`
	SessionSigningKeyNwMcp                   string `field:"SecretsKey" jsonPath:"sessionSigningKeyNwMcp"`
}
