// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "karzalivenesssubscriber":
		return obj.KarzaLivenessSubscriber.Get(dynamicFieldPath[1:])
	case "livenessstatussubscriber":
		return obj.LivenessStatusSubscriber.Get(dynamicFieldPath[1:])
	case "liveness":
		return obj.Liveness.Get(dynamicFieldPath[1:])
	case "afu":
		return obj.AFU.Get(dynamicFieldPath[1:])
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "afuvendorupdatesubscriber":
		return obj.AFUVendorUpdateSubscriber.Get(dynamicFieldPath[1:])
	case "devicereregcallbacksubscriber":
		return obj.DeviceReregCallbackSubscriber.Get(dynamicFieldPath[1:])
	case "deviceregsmsacksubscriber":
		return obj.DeviceRegSMSAckSubscriber.Get(dynamicFieldPath[1:])
	case "processpinattemptsexceededeventsubscriber":
		return obj.ProcessPinAttemptsExceededEventSubscriber.Get(dynamicFieldPath[1:])
	case "afumanualreviewnotificationsubscriber":
		return obj.AFUManualReviewNotificationSubscriber.Get(dynamicFieldPath[1:])
	case "deviceintegrityconfig":
		return obj.DeviceIntegrityConfig.Get(dynamicFieldPath[1:])
	case "otpconfig":
		return obj.OtpConfig.Get(dynamicFieldPath[1:])
	case "tokenstorescacheconfig":
		return obj.TokenStoresCacheConfig.Get(dynamicFieldPath[1:])
	case "devicelocationcacheconfig":
		return obj.DeviceLocationCacheConfig.Get(dynamicFieldPath[1:])
	case "deviceintegritynoncecacheconfig":
		return obj.DeviceIntegrityNonceCacheConfig.Get(dynamicFieldPath[1:])
	case "deviceregistrationcacheconfig":
		return obj.DeviceRegistrationCacheConfig.Get(dynamicFieldPath[1:])
	case "deviceregistrationsms":
		return obj.DeviceRegistrationSMS.Get(dynamicFieldPath[1:])
	case "questsdk":
		return obj.QuestSdk.Get(dynamicFieldPath[1:])
	case "tokenmanagerconfig":
		return obj.TokenManagerConfig.Get(dynamicFieldPath[1:])
	case "networthmcpconfig":
		return obj.NetworthMcpConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Liveness) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "onboardinglivenessruleid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OnboardingLivenessRuleId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OnboardingLivenessRuleId, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Liveness", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AFU) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "vendorupdateproducerdelayinsecs":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VendorUpdateProducerDelayInSecs\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VendorUpdateProducerDelayInSecs, nil
	case "maxrecordsdepthforafutroubleshooter":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxRecordsDepthForAFUTroubleshooter\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxRecordsDepthForAFUTroubleshooter, nil
	case "enablerereginitretry":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableReRegInitRetry\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableReRegInitRetry, nil
	case "enabledeviceregistrationosvalidation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableDeviceRegistrationOSValidation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableDeviceRegistrationOSValidation, nil
	case "enableatmpineligibilitycheck":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableATMPinEligibilityCheck\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableATMPinEligibilityCheck, nil
	case "readonlyfromatmpincheckflag":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ReadOnlyFromAtmPinCheckFlag\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ReadOnlyFromAtmPinCheckFlag, nil
	case "rereginitincrementaldelay":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ReRegInitIncrementalDelay\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ReRegInitIncrementalDelay, nil
	case "livenessmanualreviewexpirytime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LivenessManualReviewExpiryTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LivenessManualReviewExpiryTime, nil
	case "expiryforekyc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ExpiryForEKYC\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ExpiryForEKYC, nil
	case "afulivenessexternalruleid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AFULivenessExternalRuleId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AFULivenessExternalRuleId, nil
	case "afumodelexternalruleid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AFUModelExternalRuleId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AFUModelExternalRuleId, nil
	case "authfactorupdatecacheconfig":
		return obj.AuthFactorUpdateCacheConfig.Get(dynamicFieldPath[1:])
	case "vendorupdateconsumerlockconfig":
		return obj.VendorUpdateConsumerLockConfig.Get(dynamicFieldPath[1:])
	case "afucooldown":
		return obj.AFUCooldown.Get(dynamicFieldPath[1:])
	case "afumobileupdateoverrideconfig":
		return obj.AFUMobileUpdateOverrideConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AFU", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AuthFactorUpdateCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCachingEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AuthFactorUpdateCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ConsumerLockConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "timeout":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Timeout\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Timeout, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ConsumerLockConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AFUCooldown) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablecooldown":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableCooldown\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableCooldown, nil
	case "cooldownforupiregulation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CooldownForUPIRegulation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CooldownForUPIRegulation, nil
	case "cooldowndurationmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.CooldownDurationMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"CooldownDurationMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.CooldownDurationMap[dynamicFieldPath[1]], nil

		}
		return obj.CooldownDurationMap, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AFUCooldown", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AFUMobileUpdateOverride) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "actorids":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ActorIds\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ActorIds, nil
	case "activefrom":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ActiveFrom\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ActiveFrom, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AFUMobileUpdateOverride", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Flags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablelivenessmanualreviewinafu":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableLivenessManualReviewInAfu\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableLivenessManualReviewInAfu, nil
	case "enablesmsacklistener":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableSMSAckListener\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableSMSAckListener, nil
	case "enablefmv2foronboarding":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableFMV2ForOnboarding\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableFMV2ForOnboarding, nil
	case "enablesmshealthengine":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableSmsHealthEngine\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableSmsHealthEngine, nil
	case "enableorchestratorlivenessv2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableOrchestratorLivenessV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableOrchestratorLivenessV2, nil
	case "enableekycinphoneupdate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableEKYCInPhoneUpdate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableEKYCInPhoneUpdate, nil
	case "useffmpegtocompressvideo":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseFFMPEGToCompressVideo\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseFFMPEGToCompressVideo, nil
	case "enableafuriskscreeningfornruser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableAFURiskScreeningForNRUser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableAFURiskScreeningForNRUser, nil
	case "enablepinretriesexceededeventconsumer":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnablePinRetriesExceededEventConsumer\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnablePinRetriesExceededEventConsumer, nil
	case "enabledeviceregsmspayloadgenerationlimit":
		return obj.EnableDeviceRegSMSPayloadGenerationLimit.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Flags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DeviceIntegrityConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "asyncdeviceintegrityrolloutpercentage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AsyncDeviceIntegrityRolloutPercentage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AsyncDeviceIntegrityRolloutPercentage, nil
	case "randomexpiryinsecondsfordeviceintegrityv2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RandomExpiryInSecondsForDeviceIntegrityV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RandomExpiryInSecondsForDeviceIntegrityV2, nil
	case "expirytimefordeviceintegrity":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ExpiryTimeForDeviceIntegrity\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ExpiryTimeForDeviceIntegrity, nil
	case "expirytimefordeviceintegrityv2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ExpiryTimeForDeviceIntegrityV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ExpiryTimeForDeviceIntegrityV2, nil
	case "mocksafetynettokenresult":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.MockSafetynetTokenResult, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"MockSafetynetTokenResult\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.MockSafetynetTokenResult[dynamicFieldPath[1]], nil

		}
		return obj.MockSafetynetTokenResult, nil
	case "bypassphonenumbers":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BypassPhoneNumbers\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BypassPhoneNumbers, nil
	case "deviceattestationv2cfg":
		return obj.DeviceAttestationV2Cfg.Get(dynamicFieldPath[1:])
	case "ondeviceintegritycheckcfg":
		return obj.OnDeviceIntegrityCheckCfg.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DeviceIntegrityConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OtpConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "skipotpvalidationfornumbers":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipOTPValidationForNumbers\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SkipOTPValidationForNumbers, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OtpConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *TokenStoresCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCachingEnabled, nil
	case "disabletokenstorewrites":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableTokenStoreWrites\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableTokenStoreWrites, nil
	case "maketokenstorewritesoptional":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MakeTokenStoreWritesOptional\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MakeTokenStoreWritesOptional, nil
	case "enablewritesonlyforunonboardedusers":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableWritesOnlyForUnOnboardedUsers\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableWritesOnlyForUnOnboardedUsers, nil
	case "disablecachingbytokenidforaccesstokens":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableCachingByTokenIdForAccessTokens\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableCachingByTokenIdForAccessTokens, nil
	case "enablecachingbysubject":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableCachingBySubject\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableCachingBySubject, nil
	case "usetokenstoresdaov2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseTokenStoresDaoV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseTokenStoresDaoV2, nil
	case "cachettl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CacheTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CacheTTL, nil
	case "refreshtokencachettl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RefreshTokenCacheTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RefreshTokenCacheTTL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for TokenStoresCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DeviceLocationCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCachingEnabled, nil
	case "cachettl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CacheTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CacheTTL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DeviceLocationCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DeviceIntegrityNonceCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCachingEnabled, nil
	case "forceonlycache":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ForceOnlyCache\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ForceOnlyCache, nil
	case "cachettl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CacheTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CacheTTL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DeviceIntegrityNonceCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DeviceRegistrationCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCachingEnabled, nil
	case "cachettl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CacheTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CacheTTL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DeviceRegistrationCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DeviceRegistrationSMSCfg) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "downthrottledisabledtopercent":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DownThrottleDisabledToPercent\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DownThrottleDisabledToPercent, nil
	case "maxdeviceregattemptperday":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxDeviceRegAttemptPerDay\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxDeviceRegAttemptPerDay, nil
	case "enablev2smsendpointselector":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableV2SMSEndpointSelector\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableV2SMSEndpointSelector, nil
	case "endpointsenabled":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.EndpointsEnabled, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"EndpointsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.EndpointsEnabled[dynamicFieldPath[1]], nil

		}
		return obj.EndpointsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DeviceRegistrationSMSCfg", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *TokenManagerConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "disablefallbacktodbforaccesstokens":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableFallbackToDBForAccessTokens\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableFallbackToDBForAccessTokens, nil
	case "enable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Enable, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for TokenManagerConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *NetworthMcpConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablesessionsigning":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableSessionSigning\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableSessionSigning, nil
	case "loginurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LoginUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LoginUrl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for NetworthMcpConfig", strings.Join(dynamicFieldPath, "."))
	}
}
