// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	time "time"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	app "github.com/epifi/be-common/pkg/frontend/app"
	genapp "github.com/epifi/be-common/pkg/frontend/app/genconf"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	genconfig "github.com/epifi/be-common/quest/sdk/config/genconf"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/auth/config"
	keycloak "github.com/epifi/gamma/auth/keycloak"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_KarzaLivenessSubscriber                   *gencfg.SqsSubscriber
	_LivenessStatusSubscriber                  *gencfg.SqsSubscriber
	_Liveness                                  *Liveness
	_AFU                                       *AFU
	_Flags                                     *Flags
	_AFUVendorUpdateSubscriber                 *gencfg.SqsSubscriber
	_DeviceReregCallbackSubscriber             *gencfg.SqsSubscriber
	_DeviceRegSMSAckSubscriber                 *gencfg.SqsSubscriber
	_ProcessPinAttemptsExceededEventSubscriber *gencfg.SqsSubscriber
	_AFUManualReviewNotificationSubscriber     *gencfg.SqsSubscriber
	_DeviceIntegrityConfig                     *DeviceIntegrityConfig
	_OtpConfig                                 *OtpConfig
	_TokenStoresCacheConfig                    *TokenStoresCacheConfig
	_DeviceLocationCacheConfig                 *DeviceLocationCacheConfig
	_DeviceIntegrityNonceCacheConfig           *DeviceIntegrityNonceCacheConfig
	_DeviceRegistrationCacheConfig             *DeviceRegistrationCacheConfig
	_DeviceRegistrationSMS                     *DeviceRegistrationSMSCfg
	_QuestSdk                                  *genconfig.Config
	_TokenManagerConfig                        *TokenManagerConfig
	_NetworthMcpConfig                         *NetworthMcpConfig
	_Application                               *config.Application
	_Server                                    *config.Server
	_Logging                                   *cfg.Logging
	_Aws                                       *config.Aws
	_LivenessStatusPublisher                   *cfg.SqsPublisher
	_LivenessManualReviewPublisher             *cfg.SnsPublisher
	_LivenessSummaryCompletedEventPublisher    *cfg.SnsPublisher
	_BankConfig                                *config.BankConfig
	_RudderStack                               *config.RudderBroker
	_AuthSecrets                               *config.AuthSecrets
	_AFUVendorUpdatePublisher                  *cfg.SqsPublisher
	_AuthFactorUpdatePublisher                 *cfg.SnsPublisher
	_BiometricEventPublisher                   *cfg.SqsPublisher
	_AuthTokenCreationPublisher                *cfg.SnsPublisher
	_ReturnOtpTokenOnError                     bool
	_KeyCompromisedCheckConfig                 *config.KeyCompromisedCheckConfig
	_RedisOptions                              *cfg.RedisOptions
	_Tracing                                   *cfg.Tracing
	_Profiling                                 *cfg.Profiling
	_GetSessionParams                          *config.GetSessionParams
	_PlayIntegrity                             *config.PlayIntegrity
	_DeviceRegistration                        *config.DeviceRegistration
	_Keycloak                                  *keycloak.AdminClientConfig
}

func (obj *Config) KarzaLivenessSubscriber() *gencfg.SqsSubscriber {
	return obj._KarzaLivenessSubscriber
}
func (obj *Config) LivenessStatusSubscriber() *gencfg.SqsSubscriber {
	return obj._LivenessStatusSubscriber
}
func (obj *Config) Liveness() *Liveness {
	return obj._Liveness
}
func (obj *Config) AFU() *AFU {
	return obj._AFU
}
func (obj *Config) Flags() *Flags {
	return obj._Flags
}
func (obj *Config) AFUVendorUpdateSubscriber() *gencfg.SqsSubscriber {
	return obj._AFUVendorUpdateSubscriber
}
func (obj *Config) DeviceReregCallbackSubscriber() *gencfg.SqsSubscriber {
	return obj._DeviceReregCallbackSubscriber
}
func (obj *Config) DeviceRegSMSAckSubscriber() *gencfg.SqsSubscriber {
	return obj._DeviceRegSMSAckSubscriber
}
func (obj *Config) ProcessPinAttemptsExceededEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessPinAttemptsExceededEventSubscriber
}
func (obj *Config) AFUManualReviewNotificationSubscriber() *gencfg.SqsSubscriber {
	return obj._AFUManualReviewNotificationSubscriber
}
func (obj *Config) DeviceIntegrityConfig() *DeviceIntegrityConfig {
	return obj._DeviceIntegrityConfig
}
func (obj *Config) OtpConfig() *OtpConfig {
	return obj._OtpConfig
}
func (obj *Config) TokenStoresCacheConfig() *TokenStoresCacheConfig {
	return obj._TokenStoresCacheConfig
}
func (obj *Config) DeviceLocationCacheConfig() *DeviceLocationCacheConfig {
	return obj._DeviceLocationCacheConfig
}
func (obj *Config) DeviceIntegrityNonceCacheConfig() *DeviceIntegrityNonceCacheConfig {
	return obj._DeviceIntegrityNonceCacheConfig
}
func (obj *Config) DeviceRegistrationCacheConfig() *DeviceRegistrationCacheConfig {
	return obj._DeviceRegistrationCacheConfig
}
func (obj *Config) DeviceRegistrationSMS() *DeviceRegistrationSMSCfg {
	return obj._DeviceRegistrationSMS
}
func (obj *Config) QuestSdk() *genconfig.Config {
	return obj._QuestSdk
}
func (obj *Config) TokenManagerConfig() *TokenManagerConfig {
	return obj._TokenManagerConfig
}
func (obj *Config) NetworthMcpConfig() *NetworthMcpConfig {
	return obj._NetworthMcpConfig
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) Aws() *config.Aws {
	return obj._Aws
}
func (obj *Config) LivenessStatusPublisher() *cfg.SqsPublisher {
	return obj._LivenessStatusPublisher
}
func (obj *Config) LivenessManualReviewPublisher() *cfg.SnsPublisher {
	return obj._LivenessManualReviewPublisher
}
func (obj *Config) LivenessSummaryCompletedEventPublisher() *cfg.SnsPublisher {
	return obj._LivenessSummaryCompletedEventPublisher
}
func (obj *Config) BankConfig() *config.BankConfig {
	return obj._BankConfig
}
func (obj *Config) RudderStack() *config.RudderBroker {
	return obj._RudderStack
}
func (obj *Config) AuthSecrets() *config.AuthSecrets {
	return obj._AuthSecrets
}
func (obj *Config) AFUVendorUpdatePublisher() *cfg.SqsPublisher {
	return obj._AFUVendorUpdatePublisher
}
func (obj *Config) AuthFactorUpdatePublisher() *cfg.SnsPublisher {
	return obj._AuthFactorUpdatePublisher
}
func (obj *Config) BiometricEventPublisher() *cfg.SqsPublisher {
	return obj._BiometricEventPublisher
}
func (obj *Config) AuthTokenCreationPublisher() *cfg.SnsPublisher {
	return obj._AuthTokenCreationPublisher
}
func (obj *Config) ReturnOtpTokenOnError() bool {
	return obj._ReturnOtpTokenOnError
}
func (obj *Config) KeyCompromisedCheckConfig() *config.KeyCompromisedCheckConfig {
	return obj._KeyCompromisedCheckConfig
}
func (obj *Config) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Profiling() *cfg.Profiling {
	return obj._Profiling
}
func (obj *Config) GetSessionParams() *config.GetSessionParams {
	return obj._GetSessionParams
}
func (obj *Config) PlayIntegrity() *config.PlayIntegrity {
	return obj._PlayIntegrity
}
func (obj *Config) DeviceRegistration() *config.DeviceRegistration {
	return obj._DeviceRegistration
}
func (obj *Config) Keycloak() *keycloak.AdminClientConfig {
	return obj._Keycloak
}

type Liveness struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_OnboardingLivenessRuleId      string
	_OnboardingLivenessRuleIdMutex *sync.RWMutex
}

func (obj *Liveness) OnboardingLivenessRuleId() string {
	obj._OnboardingLivenessRuleIdMutex.RLock()
	defer obj._OnboardingLivenessRuleIdMutex.RUnlock()
	return obj._OnboardingLivenessRuleId
}

type AFU struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_VendorUpdateProducerDelayInSecs     int32
	_MaxRecordsDepthForAFUTroubleshooter int64
	_EnableReRegInitRetry                uint32
	// To enable device registration OS version check for IOS devices
	_EnableDeviceRegistrationOSValidation uint32
	// ATM PIN validation is required only for some criteria, this flag is to enable that check
	_EnableATMPinEligibilityCheck uint32
	// As of now, we rely on existing business logic to handle old requests to check if ATM PIN is required or not.
	// Adding this flag to enable reads only from ATM PIN Check flag
	_ReadOnlyFromAtmPinCheckFlag    uint32
	_ReRegInitIncrementalDelay      int64
	_LivenessManualReviewExpiryTime int64
	// Expiry for EKYC attempt done in AFU
	// AFU Vendor update must start before EKYC expires
	_ExpiryForEKYC int64
	// external rule id for afu liveness cases used by case management to create alerts
	_AFULivenessExternalRuleId      string
	_AFULivenessExternalRuleIdMutex *sync.RWMutex
	// external rule id for afu model failed cases used by case management to create alerts
	_AFUModelExternalRuleId                      string
	_AFUModelExternalRuleIdMutex                 *sync.RWMutex
	_AuthFactorUpdateCacheConfig                 *AuthFactorUpdateCacheConfig
	_VendorUpdateConsumerLockConfig              *ConsumerLockConfig
	_AFUCooldown                                 *AFUCooldown
	_AFUMobileUpdateOverrideConfig               *AFUMobileUpdateOverride
	_CredentialsOrder                            map[string][][]string
	_BypassCredentialVerificationForPhoneNumbers []string
	_MinVersionForAFURetry                       *app.FeatureConfig
}

func (obj *AFU) VendorUpdateProducerDelayInSecs() int32 {
	return int32(atomic.LoadInt32(&obj._VendorUpdateProducerDelayInSecs))
}
func (obj *AFU) MaxRecordsDepthForAFUTroubleshooter() int {
	return int(atomic.LoadInt64(&obj._MaxRecordsDepthForAFUTroubleshooter))
}
func (obj *AFU) EnableReRegInitRetry() bool {
	if atomic.LoadUint32(&obj._EnableReRegInitRetry) == 0 {
		return false
	} else {
		return true
	}
}

// To enable device registration OS version check for IOS devices
func (obj *AFU) EnableDeviceRegistrationOSValidation() bool {
	if atomic.LoadUint32(&obj._EnableDeviceRegistrationOSValidation) == 0 {
		return false
	} else {
		return true
	}
}

// ATM PIN validation is required only for some criteria, this flag is to enable that check
func (obj *AFU) EnableATMPinEligibilityCheck() bool {
	if atomic.LoadUint32(&obj._EnableATMPinEligibilityCheck) == 0 {
		return false
	} else {
		return true
	}
}

// As of now, we rely on existing business logic to handle old requests to check if ATM PIN is required or not.
// Adding this flag to enable reads only from ATM PIN Check flag
func (obj *AFU) ReadOnlyFromAtmPinCheckFlag() bool {
	if atomic.LoadUint32(&obj._ReadOnlyFromAtmPinCheckFlag) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AFU) ReRegInitIncrementalDelay() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ReRegInitIncrementalDelay))
}
func (obj *AFU) LivenessManualReviewExpiryTime() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._LivenessManualReviewExpiryTime))
}

// Expiry for EKYC attempt done in AFU
// AFU Vendor update must start before EKYC expires
func (obj *AFU) ExpiryForEKYC() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ExpiryForEKYC))
}

// external rule id for afu liveness cases used by case management to create alerts
func (obj *AFU) AFULivenessExternalRuleId() string {
	obj._AFULivenessExternalRuleIdMutex.RLock()
	defer obj._AFULivenessExternalRuleIdMutex.RUnlock()
	return obj._AFULivenessExternalRuleId
}

// external rule id for afu model failed cases used by case management to create alerts
func (obj *AFU) AFUModelExternalRuleId() string {
	obj._AFUModelExternalRuleIdMutex.RLock()
	defer obj._AFUModelExternalRuleIdMutex.RUnlock()
	return obj._AFUModelExternalRuleId
}
func (obj *AFU) AuthFactorUpdateCacheConfig() *AuthFactorUpdateCacheConfig {
	return obj._AuthFactorUpdateCacheConfig
}
func (obj *AFU) VendorUpdateConsumerLockConfig() *ConsumerLockConfig {
	return obj._VendorUpdateConsumerLockConfig
}
func (obj *AFU) AFUCooldown() *AFUCooldown {
	return obj._AFUCooldown
}
func (obj *AFU) AFUMobileUpdateOverrideConfig() *AFUMobileUpdateOverride {
	return obj._AFUMobileUpdateOverrideConfig
}
func (obj *AFU) CredentialsOrder() map[string][][]string {
	return obj._CredentialsOrder
}
func (obj *AFU) BypassCredentialVerificationForPhoneNumbers() []string {
	return obj._BypassCredentialVerificationForPhoneNumbers
}
func (obj *AFU) MinVersionForAFURetry() *app.FeatureConfig {
	return obj._MinVersionForAFURetry
}

type AuthFactorUpdateCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// TTLs are set based on the cooldown periods for UPI and AFU regulations.
	_IsCachingEnabled       uint32
	_AuthFactorUpdatePrefix string
}

// TTLs are set based on the cooldown periods for UPI and AFU regulations.
func (obj *AuthFactorUpdateCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AuthFactorUpdateCacheConfig) AuthFactorUpdatePrefix() string {
	return obj._AuthFactorUpdatePrefix
}

type ConsumerLockConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Timeout sets the max duration for which the lock can be acquired by a request.
	// This should be little less than the request timeout for most cases.
	_Timeout int64
}

// Timeout sets the max duration for which the lock can be acquired by a request.
// This should be little less than the request timeout for most cases.
func (obj *ConsumerLockConfig) Timeout() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._Timeout))
}

type AFUCooldown struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// AFU cooldown regulation: User is allowed to do AFU only after X consecutive days of a previous successful AFU with the common auth factor
	// EnableCooldown enables the mentioned cooldown logic
	_EnableCooldown           uint32
	_CooldownForUPIRegulation int64
	// Key maps to AuthFactor enum afu/auth/auth_factor_update.pb.go
	_CooldownDurationMap *syncmap.Map[string, time.Duration]
}

// AFU cooldown regulation: User is allowed to do AFU only after X consecutive days of a previous successful AFU with the common auth factor
// EnableCooldown enables the mentioned cooldown logic
func (obj *AFUCooldown) EnableCooldown() bool {
	if atomic.LoadUint32(&obj._EnableCooldown) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AFUCooldown) CooldownForUPIRegulation() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CooldownForUPIRegulation))
}

// Key maps to AuthFactor enum afu/auth/auth_factor_update.pb.go
func (obj *AFUCooldown) CooldownDurationMap() *syncmap.Map[string, time.Duration] {
	return obj._CooldownDurationMap
}

type AFUMobileUpdateOverride struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ActorIds        roarray.ROArray[string]
	_ActorIdsMutex   *sync.RWMutex
	_ActiveFrom      time.Time
	_ActiveFromMutex *sync.RWMutex
}

func (obj *AFUMobileUpdateOverride) ActorIds() roarray.ROArray[string] {
	obj._ActorIdsMutex.RLock()
	defer obj._ActorIdsMutex.RUnlock()
	return obj._ActorIds
}
func (obj *AFUMobileUpdateOverride) ActiveFrom() time.Time {
	obj._ActiveFromMutex.RLock()
	defer obj._ActiveFromMutex.RUnlock()
	return obj._ActiveFrom
}

type Flags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Flag for pushing manual review of liveness events to persistent queue
	_EnableLivenessManualReviewInAfu uint32
	// Flag to enable sms acknowledgement processing for device registration
	_EnableSMSAckListener    uint32
	_EnableFMV2ForOnboarding uint32
	_EnableSmsHealthEngine   uint32
	// Flag to enable signal driven liveness in auth orchestrator
	_EnableOrchestratorLivenessV2 uint32
	_EnableEKYCInPhoneUpdate      uint32
	// flag to enable liveness video compression before storing to S3
	_UseFFMPEGToCompressVideo                 uint32
	_EnableAFURiskScreeningForNRUser          uint32
	_EnablePinRetriesExceededEventConsumer    uint32
	_EnableDeviceRegSMSPayloadGenerationLimit *genapp.FeatureConfig
	_TrimDebugMessageFromStatus               bool
	_SkipIOSIdTokenExpiryCheck                bool
	_EnableV2IdTokenVerifierForAndroid        bool
	_EnableCheckForAccessRevoke               bool
	_DisableLivenessAttemptChangeFeed         bool
}

// Flag for pushing manual review of liveness events to persistent queue
func (obj *Flags) EnableLivenessManualReviewInAfu() bool {
	if atomic.LoadUint32(&obj._EnableLivenessManualReviewInAfu) == 0 {
		return false
	} else {
		return true
	}
}

// Flag to enable sms acknowledgement processing for device registration
func (obj *Flags) EnableSMSAckListener() bool {
	if atomic.LoadUint32(&obj._EnableSMSAckListener) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) EnableFMV2ForOnboarding() bool {
	if atomic.LoadUint32(&obj._EnableFMV2ForOnboarding) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) EnableSmsHealthEngine() bool {
	if atomic.LoadUint32(&obj._EnableSmsHealthEngine) == 0 {
		return false
	} else {
		return true
	}
}

// Flag to enable signal driven liveness in auth orchestrator
func (obj *Flags) EnableOrchestratorLivenessV2() bool {
	if atomic.LoadUint32(&obj._EnableOrchestratorLivenessV2) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) EnableEKYCInPhoneUpdate() bool {
	if atomic.LoadUint32(&obj._EnableEKYCInPhoneUpdate) == 0 {
		return false
	} else {
		return true
	}
}

// flag to enable liveness video compression before storing to S3
func (obj *Flags) UseFFMPEGToCompressVideo() bool {
	if atomic.LoadUint32(&obj._UseFFMPEGToCompressVideo) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) EnableAFURiskScreeningForNRUser() bool {
	if atomic.LoadUint32(&obj._EnableAFURiskScreeningForNRUser) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) EnablePinRetriesExceededEventConsumer() bool {
	if atomic.LoadUint32(&obj._EnablePinRetriesExceededEventConsumer) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) EnableDeviceRegSMSPayloadGenerationLimit() *genapp.FeatureConfig {
	return obj._EnableDeviceRegSMSPayloadGenerationLimit
}
func (obj *Flags) TrimDebugMessageFromStatus() bool {
	return obj._TrimDebugMessageFromStatus
}
func (obj *Flags) SkipIOSIdTokenExpiryCheck() bool {
	return obj._SkipIOSIdTokenExpiryCheck
}
func (obj *Flags) EnableV2IdTokenVerifierForAndroid() bool {
	return obj._EnableV2IdTokenVerifierForAndroid
}
func (obj *Flags) EnableCheckForAccessRevoke() bool {
	return obj._EnableCheckForAccessRevoke
}
func (obj *Flags) DisableLivenessAttemptChangeFeed() bool {
	return obj._DisableLivenessAttemptChangeFeed
}

type DeviceIntegrityConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// controlled rollout for async device integrity checks
	_AsyncDeviceIntegrityRolloutPercentage int64
	// Using random expiry time to avoid sudden expiry burst of tokens which creates alerts
	_RandomExpiryInSecondsForDeviceIntegrityV2 int64
	// expiry time for verified device integrity
	_ExpiryTimeForDeviceIntegrity int64
	// extended validity for device integrity v2 on app versions which perform on-device checks
	_ExpiryTimeForDeviceIntegrityV2 int64
	_MockSafetynetTokenResult       *syncmap.Map[string, string]
	// device integrity gets bypassed for these phone numbers
	_BypassPhoneNumbers                                      roarray.ROArray[string]
	_BypassPhoneNumbersMutex                                 *sync.RWMutex
	_DeviceAttestationV2Cfg                                  *genapp.FeatureConfig
	_OnDeviceIntegrityCheckCfg                               *genapp.FeatureConfig
	_AndroidAppPackageNames                                  []string
	_AndroidApkCertificateDigestSha256                       []string
	_DeviceIntegrityNonceValidityInSecs                      int
	_CheckApkPackageName                                     bool
	_CheckCertificateHash                                    bool
	_AppleAppAttestRootCertificate                           string
	_AppleCredCertOidExtensionSeq                            []int
	_IosAppIdentifiers                                       []string
	_AllowSafetynetCertChainVerificationWithModifiedCurrTime bool
	_CurrTimeOverrideForSafetynetCertVerification            time.Duration
	_BypassTokenIdValidationForPhoneNumbers                  []string
	_WhitelistedAttestationTokens                            []string
}

// controlled rollout for async device integrity checks
func (obj *DeviceIntegrityConfig) AsyncDeviceIntegrityRolloutPercentage() int {
	return int(atomic.LoadInt64(&obj._AsyncDeviceIntegrityRolloutPercentage))
}

// Using random expiry time to avoid sudden expiry burst of tokens which creates alerts
func (obj *DeviceIntegrityConfig) RandomExpiryInSecondsForDeviceIntegrityV2() int {
	return int(atomic.LoadInt64(&obj._RandomExpiryInSecondsForDeviceIntegrityV2))
}

// expiry time for verified device integrity
func (obj *DeviceIntegrityConfig) ExpiryTimeForDeviceIntegrity() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ExpiryTimeForDeviceIntegrity))
}

// extended validity for device integrity v2 on app versions which perform on-device checks
func (obj *DeviceIntegrityConfig) ExpiryTimeForDeviceIntegrityV2() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ExpiryTimeForDeviceIntegrityV2))
}
func (obj *DeviceIntegrityConfig) MockSafetynetTokenResult() *syncmap.Map[string, string] {
	return obj._MockSafetynetTokenResult
}

// device integrity gets bypassed for these phone numbers
func (obj *DeviceIntegrityConfig) BypassPhoneNumbers() roarray.ROArray[string] {
	obj._BypassPhoneNumbersMutex.RLock()
	defer obj._BypassPhoneNumbersMutex.RUnlock()
	return obj._BypassPhoneNumbers
}
func (obj *DeviceIntegrityConfig) DeviceAttestationV2Cfg() *genapp.FeatureConfig {
	return obj._DeviceAttestationV2Cfg
}
func (obj *DeviceIntegrityConfig) OnDeviceIntegrityCheckCfg() *genapp.FeatureConfig {
	return obj._OnDeviceIntegrityCheckCfg
}
func (obj *DeviceIntegrityConfig) AndroidAppPackageNames() []string {
	return obj._AndroidAppPackageNames
}
func (obj *DeviceIntegrityConfig) AndroidApkCertificateDigestSha256() []string {
	return obj._AndroidApkCertificateDigestSha256
}
func (obj *DeviceIntegrityConfig) DeviceIntegrityNonceValidityInSecs() int {
	return obj._DeviceIntegrityNonceValidityInSecs
}
func (obj *DeviceIntegrityConfig) CheckApkPackageName() bool {
	return obj._CheckApkPackageName
}
func (obj *DeviceIntegrityConfig) CheckCertificateHash() bool {
	return obj._CheckCertificateHash
}
func (obj *DeviceIntegrityConfig) AppleAppAttestRootCertificate() string {
	return obj._AppleAppAttestRootCertificate
}
func (obj *DeviceIntegrityConfig) AppleCredCertOidExtensionSeq() []int {
	return obj._AppleCredCertOidExtensionSeq
}
func (obj *DeviceIntegrityConfig) IosAppIdentifiers() []string {
	return obj._IosAppIdentifiers
}
func (obj *DeviceIntegrityConfig) AllowSafetynetCertChainVerificationWithModifiedCurrTime() bool {
	return obj._AllowSafetynetCertChainVerificationWithModifiedCurrTime
}
func (obj *DeviceIntegrityConfig) CurrTimeOverrideForSafetynetCertVerification() time.Duration {
	return obj._CurrTimeOverrideForSafetynetCertVerification
}
func (obj *DeviceIntegrityConfig) BypassTokenIdValidationForPhoneNumbers() []string {
	return obj._BypassTokenIdValidationForPhoneNumbers
}
func (obj *DeviceIntegrityConfig) WhitelistedAttestationTokens() []string {
	return obj._WhitelistedAttestationTokens
}

type OtpConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// We want to skip otp validation for few numbers in non-prod numbers for Apple's IOS app review
	_SkipOTPValidationForNumbers      roarray.ROArray[string]
	_SkipOTPValidationForNumbersMutex *sync.RWMutex
	_OtpResendInterval                time.Duration
	_GenerateOtpLimits                []*config.GenerateOtpLimit
	_CustomGenerateOtpLimits          config.CustomGenerateOtpLimits
}

// We want to skip otp validation for few numbers in non-prod numbers for Apple's IOS app review
func (obj *OtpConfig) SkipOTPValidationForNumbers() roarray.ROArray[string] {
	obj._SkipOTPValidationForNumbersMutex.RLock()
	defer obj._SkipOTPValidationForNumbersMutex.RUnlock()
	return obj._SkipOTPValidationForNumbers
}
func (obj *OtpConfig) OtpResendInterval() time.Duration {
	return obj._OtpResendInterval
}
func (obj *OtpConfig) GenerateOtpLimits() []*config.GenerateOtpLimit {
	return obj._GenerateOtpLimits
}
func (obj *OtpConfig) CustomGenerateOtpLimits() config.CustomGenerateOtpLimits {
	return obj._CustomGenerateOtpLimits
}

type TokenStoresCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCachingEnabled uint32
	// DisableTokenStoreWrites is a fire-fighting tool. It disables all insert, update & delete queries on the table.
	// Use it only in dire situations when auth db is degraded and users are unable to login.
	_DisableTokenStoreWrites uint32
	// MakeTokenStoreWritesOptional is a fire-fighting tool. It makes insertion into token stores table non-blocking.
	// Use it only in dire situations when auth db is degraded and users are unable to login.
	_MakeTokenStoreWritesOptional uint32
	// This will allow DB writes only to unonboarded users, logic for this avoids writes without breaking other flows
	_EnableWritesOnlyForUnOnboardedUsers uint32
	// TODO(sayan): cleanup once stable
	// Flag to disable caching by token id
	// If set to true this will stop storing token in cache with token id
	_DisableCachingByTokenIdForAccessTokens uint32
	// TODO(sayan): cleanup once stable
	// Flag to enable caching by actor id and token type
	// If set to true, tokens will be stored in cache with actor id and token type
	// *NOTE* - This will not disable caching by token id
	_EnableCachingBySubject uint32
	// TODO(sayan): cleanup once stable
	// Flag to enable token stores dao v2
	_UseTokenStoresDaoV2        uint32
	_CacheTTL                   int64
	_RefreshTokenCacheTTL       int64
	_TokenStoresPrefix          string
	_TokenStoresPrefixV2        string
	_TokenLastActivityKeyPrefix string
	_TokenCacheTTLMap           map[string]time.Duration
}

func (obj *TokenStoresCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// DisableTokenStoreWrites is a fire-fighting tool. It disables all insert, update & delete queries on the table.
// Use it only in dire situations when auth db is degraded and users are unable to login.
func (obj *TokenStoresCacheConfig) DisableTokenStoreWrites() bool {
	if atomic.LoadUint32(&obj._DisableTokenStoreWrites) == 0 {
		return false
	} else {
		return true
	}
}

// MakeTokenStoreWritesOptional is a fire-fighting tool. It makes insertion into token stores table non-blocking.
// Use it only in dire situations when auth db is degraded and users are unable to login.
func (obj *TokenStoresCacheConfig) MakeTokenStoreWritesOptional() bool {
	if atomic.LoadUint32(&obj._MakeTokenStoreWritesOptional) == 0 {
		return false
	} else {
		return true
	}
}

// This will allow DB writes only to unonboarded users, logic for this avoids writes without breaking other flows
func (obj *TokenStoresCacheConfig) EnableWritesOnlyForUnOnboardedUsers() bool {
	if atomic.LoadUint32(&obj._EnableWritesOnlyForUnOnboardedUsers) == 0 {
		return false
	} else {
		return true
	}
}

// TODO(sayan): cleanup once stable
// Flag to disable caching by token id
// If set to true this will stop storing token in cache with token id
func (obj *TokenStoresCacheConfig) DisableCachingByTokenIdForAccessTokens() bool {
	if atomic.LoadUint32(&obj._DisableCachingByTokenIdForAccessTokens) == 0 {
		return false
	} else {
		return true
	}
}

// TODO(sayan): cleanup once stable
// Flag to enable caching by actor id and token type
// If set to true, tokens will be stored in cache with actor id and token type
// *NOTE* - This will not disable caching by token id
func (obj *TokenStoresCacheConfig) EnableCachingBySubject() bool {
	if atomic.LoadUint32(&obj._EnableCachingBySubject) == 0 {
		return false
	} else {
		return true
	}
}

// TODO(sayan): cleanup once stable
// Flag to enable token stores dao v2
func (obj *TokenStoresCacheConfig) UseTokenStoresDaoV2() bool {
	if atomic.LoadUint32(&obj._UseTokenStoresDaoV2) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *TokenStoresCacheConfig) CacheTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CacheTTL))
}
func (obj *TokenStoresCacheConfig) RefreshTokenCacheTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._RefreshTokenCacheTTL))
}
func (obj *TokenStoresCacheConfig) TokenStoresPrefix() string {
	return obj._TokenStoresPrefix
}
func (obj *TokenStoresCacheConfig) TokenStoresPrefixV2() string {
	return obj._TokenStoresPrefixV2
}
func (obj *TokenStoresCacheConfig) TokenLastActivityKeyPrefix() string {
	return obj._TokenLastActivityKeyPrefix
}
func (obj *TokenStoresCacheConfig) TokenCacheTTLMap() map[string]time.Duration {
	return obj._TokenCacheTTLMap
}

type DeviceLocationCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCachingEnabled     uint32
	_CacheTTL             int64
	_DeviceLocationPrefix string
}

func (obj *DeviceLocationCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DeviceLocationCacheConfig) CacheTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CacheTTL))
}
func (obj *DeviceLocationCacheConfig) DeviceLocationPrefix() string {
	return obj._DeviceLocationPrefix
}

type DeviceIntegrityNonceCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCachingEnabled uint32
	_ForceOnlyCache   uint32
	_CacheTTL         int64
}

func (obj *DeviceIntegrityNonceCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DeviceIntegrityNonceCacheConfig) ForceOnlyCache() bool {
	if atomic.LoadUint32(&obj._ForceOnlyCache) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DeviceIntegrityNonceCacheConfig) CacheTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CacheTTL))
}

type DeviceRegistrationCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCachingEnabled uint32
	_CacheTTL         int64
}

func (obj *DeviceRegistrationCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DeviceRegistrationCacheConfig) CacheTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CacheTTL))
}

type DeviceRegistrationSMSCfg struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_DownThrottleDisabledToPercent uint32
	// maximum number of device registration SMS payload user can generate in a day
	_MaxDeviceRegAttemptPerDay   int64
	_EnableV2SMSEndpointSelector uint32
	_EndpointsEnabled            *syncmap.Map[string, bool]
}

func (obj *DeviceRegistrationSMSCfg) DownThrottleDisabledToPercent() uint8 {
	return uint8(atomic.LoadUint32(&obj._DownThrottleDisabledToPercent))
}

// maximum number of device registration SMS payload user can generate in a day
func (obj *DeviceRegistrationSMSCfg) MaxDeviceRegAttemptPerDay() int {
	return int(atomic.LoadInt64(&obj._MaxDeviceRegAttemptPerDay))
}
func (obj *DeviceRegistrationSMSCfg) EnableV2SMSEndpointSelector() bool {
	if atomic.LoadUint32(&obj._EnableV2SMSEndpointSelector) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DeviceRegistrationSMSCfg) EndpointsEnabled() *syncmap.Map[string, bool] {
	return obj._EndpointsEnabled
}

type TokenManagerConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// In case of cache miss, disable fetching tokens from DB
	// This is a fire-fighting tool. Use it only in dire situations when auth db is degraded and users are unable to login.
	_DisableFallbackToDBForAccessTokens uint32
	_Enable                             string
	_EnableMutex                        *sync.RWMutex
	_WhitelistedActorIds                []string
	_WhitelistedPhoneNumberHashes       []string
}

// In case of cache miss, disable fetching tokens from DB
// This is a fire-fighting tool. Use it only in dire situations when auth db is degraded and users are unable to login.
func (obj *TokenManagerConfig) DisableFallbackToDBForAccessTokens() bool {
	if atomic.LoadUint32(&obj._DisableFallbackToDBForAccessTokens) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *TokenManagerConfig) Enable() string {
	obj._EnableMutex.RLock()
	defer obj._EnableMutex.RUnlock()
	return obj._Enable
}
func (obj *TokenManagerConfig) WhitelistedActorIds() []string {
	return obj._WhitelistedActorIds
}
func (obj *TokenManagerConfig) WhitelistedPhoneNumberHashes() []string {
	return obj._WhitelistedPhoneNumberHashes
}

type NetworthMcpConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EnableSessionSigning uint32
	_LoginUrl             string
	_LoginUrlMutex        *sync.RWMutex
}

func (obj *NetworthMcpConfig) EnableSessionSigning() bool {
	if atomic.LoadUint32(&obj._EnableSessionSigning) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *NetworthMcpConfig) LoginUrl() string {
	obj._LoginUrlMutex.RLock()
	defer obj._LoginUrlMutex.RUnlock()
	return obj._LoginUrl
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_KarzaLivenessSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._KarzaLivenessSubscriber = _KarzaLivenessSubscriber
	helper.AddFieldSetters("karzalivenesssubscriber", _fieldSetters, _setters)
	_LivenessStatusSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._LivenessStatusSubscriber = _LivenessStatusSubscriber
	helper.AddFieldSetters("livenessstatussubscriber", _fieldSetters, _setters)
	_Liveness, _fieldSetters := NewLiveness()
	_obj._Liveness = _Liveness
	helper.AddFieldSetters("liveness", _fieldSetters, _setters)
	_AFU, _fieldSetters := NewAFU()
	_obj._AFU = _AFU
	helper.AddFieldSetters("afu", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_AFUVendorUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AFUVendorUpdateSubscriber = _AFUVendorUpdateSubscriber
	helper.AddFieldSetters("afuvendorupdatesubscriber", _fieldSetters, _setters)
	_DeviceReregCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DeviceReregCallbackSubscriber = _DeviceReregCallbackSubscriber
	helper.AddFieldSetters("devicereregcallbacksubscriber", _fieldSetters, _setters)
	_DeviceRegSMSAckSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DeviceRegSMSAckSubscriber = _DeviceRegSMSAckSubscriber
	helper.AddFieldSetters("deviceregsmsacksubscriber", _fieldSetters, _setters)
	_ProcessPinAttemptsExceededEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessPinAttemptsExceededEventSubscriber = _ProcessPinAttemptsExceededEventSubscriber
	helper.AddFieldSetters("processpinattemptsexceededeventsubscriber", _fieldSetters, _setters)
	_AFUManualReviewNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AFUManualReviewNotificationSubscriber = _AFUManualReviewNotificationSubscriber
	helper.AddFieldSetters("afumanualreviewnotificationsubscriber", _fieldSetters, _setters)
	_DeviceIntegrityConfig, _fieldSetters := NewDeviceIntegrityConfig()
	_obj._DeviceIntegrityConfig = _DeviceIntegrityConfig
	helper.AddFieldSetters("deviceintegrityconfig", _fieldSetters, _setters)
	_OtpConfig, _fieldSetters := NewOtpConfig()
	_obj._OtpConfig = _OtpConfig
	helper.AddFieldSetters("otpconfig", _fieldSetters, _setters)
	_TokenStoresCacheConfig, _fieldSetters := NewTokenStoresCacheConfig()
	_obj._TokenStoresCacheConfig = _TokenStoresCacheConfig
	helper.AddFieldSetters("tokenstorescacheconfig", _fieldSetters, _setters)
	_DeviceLocationCacheConfig, _fieldSetters := NewDeviceLocationCacheConfig()
	_obj._DeviceLocationCacheConfig = _DeviceLocationCacheConfig
	helper.AddFieldSetters("devicelocationcacheconfig", _fieldSetters, _setters)
	_DeviceIntegrityNonceCacheConfig, _fieldSetters := NewDeviceIntegrityNonceCacheConfig()
	_obj._DeviceIntegrityNonceCacheConfig = _DeviceIntegrityNonceCacheConfig
	helper.AddFieldSetters("deviceintegritynoncecacheconfig", _fieldSetters, _setters)
	_DeviceRegistrationCacheConfig, _fieldSetters := NewDeviceRegistrationCacheConfig()
	_obj._DeviceRegistrationCacheConfig = _DeviceRegistrationCacheConfig
	helper.AddFieldSetters("deviceregistrationcacheconfig", _fieldSetters, _setters)
	_DeviceRegistrationSMS, _fieldSetters := NewDeviceRegistrationSMSCfg()
	_obj._DeviceRegistrationSMS = _DeviceRegistrationSMS
	helper.AddFieldSetters("deviceregistrationsms", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_TokenManagerConfig, _fieldSetters := NewTokenManagerConfig()
	_obj._TokenManagerConfig = _TokenManagerConfig
	helper.AddFieldSetters("tokenmanagerconfig", _fieldSetters, _setters)
	_NetworthMcpConfig, _fieldSetters := NewNetworthMcpConfig()
	_obj._NetworthMcpConfig = _NetworthMcpConfig
	helper.AddFieldSetters("networthmcpconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_KarzaLivenessSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._KarzaLivenessSubscriber = _KarzaLivenessSubscriber
	helper.AddFieldSetters("karzalivenesssubscriber", _fieldSetters, _setters)
	_LivenessStatusSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._LivenessStatusSubscriber = _LivenessStatusSubscriber
	helper.AddFieldSetters("livenessstatussubscriber", _fieldSetters, _setters)
	_Liveness, _fieldSetters := NewLiveness()
	_obj._Liveness = _Liveness
	helper.AddFieldSetters("liveness", _fieldSetters, _setters)
	_AFU, _fieldSetters := NewAFU()
	_obj._AFU = _AFU
	helper.AddFieldSetters("afu", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_AFUVendorUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AFUVendorUpdateSubscriber = _AFUVendorUpdateSubscriber
	helper.AddFieldSetters("afuvendorupdatesubscriber", _fieldSetters, _setters)
	_DeviceReregCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DeviceReregCallbackSubscriber = _DeviceReregCallbackSubscriber
	helper.AddFieldSetters("devicereregcallbacksubscriber", _fieldSetters, _setters)
	_DeviceRegSMSAckSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DeviceRegSMSAckSubscriber = _DeviceRegSMSAckSubscriber
	helper.AddFieldSetters("deviceregsmsacksubscriber", _fieldSetters, _setters)
	_ProcessPinAttemptsExceededEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessPinAttemptsExceededEventSubscriber = _ProcessPinAttemptsExceededEventSubscriber
	helper.AddFieldSetters("processpinattemptsexceededeventsubscriber", _fieldSetters, _setters)
	_AFUManualReviewNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AFUManualReviewNotificationSubscriber = _AFUManualReviewNotificationSubscriber
	helper.AddFieldSetters("afumanualreviewnotificationsubscriber", _fieldSetters, _setters)
	_DeviceIntegrityConfig, _fieldSetters := NewDeviceIntegrityConfig()
	_obj._DeviceIntegrityConfig = _DeviceIntegrityConfig
	helper.AddFieldSetters("deviceintegrityconfig", _fieldSetters, _setters)
	_OtpConfig, _fieldSetters := NewOtpConfig()
	_obj._OtpConfig = _OtpConfig
	helper.AddFieldSetters("otpconfig", _fieldSetters, _setters)
	_TokenStoresCacheConfig, _fieldSetters := NewTokenStoresCacheConfig()
	_obj._TokenStoresCacheConfig = _TokenStoresCacheConfig
	helper.AddFieldSetters("tokenstorescacheconfig", _fieldSetters, _setters)
	_DeviceLocationCacheConfig, _fieldSetters := NewDeviceLocationCacheConfig()
	_obj._DeviceLocationCacheConfig = _DeviceLocationCacheConfig
	helper.AddFieldSetters("devicelocationcacheconfig", _fieldSetters, _setters)
	_DeviceIntegrityNonceCacheConfig, _fieldSetters := NewDeviceIntegrityNonceCacheConfig()
	_obj._DeviceIntegrityNonceCacheConfig = _DeviceIntegrityNonceCacheConfig
	helper.AddFieldSetters("deviceintegritynoncecacheconfig", _fieldSetters, _setters)
	_DeviceRegistrationCacheConfig, _fieldSetters := NewDeviceRegistrationCacheConfig()
	_obj._DeviceRegistrationCacheConfig = _DeviceRegistrationCacheConfig
	helper.AddFieldSetters("deviceregistrationcacheconfig", _fieldSetters, _setters)
	_DeviceRegistrationSMS, _fieldSetters := NewDeviceRegistrationSMSCfg()
	_obj._DeviceRegistrationSMS = _DeviceRegistrationSMS
	helper.AddFieldSetters("deviceregistrationsms", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_TokenManagerConfig, _fieldSetters := NewTokenManagerConfig()
	_obj._TokenManagerConfig = _TokenManagerConfig
	helper.AddFieldSetters("tokenmanagerconfig", _fieldSetters, _setters)
	_NetworthMcpConfig, _fieldSetters := NewNetworthMcpConfig()
	_obj._NetworthMcpConfig = _NetworthMcpConfig
	helper.AddFieldSetters("networthmcpconfig", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "karzalivenesssubscriber":
		return obj._KarzaLivenessSubscriber.Set(v.KarzaLivenessSubscriber, true, path)
	case "livenessstatussubscriber":
		return obj._LivenessStatusSubscriber.Set(v.LivenessStatusSubscriber, true, path)
	case "liveness":
		return obj._Liveness.Set(v.Liveness, true, path)
	case "afu":
		return obj._AFU.Set(v.AFU, true, path)
	case "flags":
		return obj._Flags.Set(v.Flags, true, path)
	case "afuvendorupdatesubscriber":
		return obj._AFUVendorUpdateSubscriber.Set(v.AFUVendorUpdateSubscriber, true, path)
	case "devicereregcallbacksubscriber":
		return obj._DeviceReregCallbackSubscriber.Set(v.DeviceReregCallbackSubscriber, true, path)
	case "deviceregsmsacksubscriber":
		return obj._DeviceRegSMSAckSubscriber.Set(v.DeviceRegSMSAckSubscriber, true, path)
	case "processpinattemptsexceededeventsubscriber":
		return obj._ProcessPinAttemptsExceededEventSubscriber.Set(v.ProcessPinAttemptsExceededEventSubscriber, true, path)
	case "afumanualreviewnotificationsubscriber":
		return obj._AFUManualReviewNotificationSubscriber.Set(v.AFUManualReviewNotificationSubscriber, true, path)
	case "deviceintegrityconfig":
		return obj._DeviceIntegrityConfig.Set(v.DeviceIntegrityConfig, true, path)
	case "otpconfig":
		return obj._OtpConfig.Set(v.OtpConfig, true, path)
	case "tokenstorescacheconfig":
		return obj._TokenStoresCacheConfig.Set(v.TokenStoresCacheConfig, true, path)
	case "devicelocationcacheconfig":
		return obj._DeviceLocationCacheConfig.Set(v.DeviceLocationCacheConfig, true, path)
	case "deviceintegritynoncecacheconfig":
		return obj._DeviceIntegrityNonceCacheConfig.Set(v.DeviceIntegrityNonceCacheConfig, true, path)
	case "deviceregistrationcacheconfig":
		return obj._DeviceRegistrationCacheConfig.Set(v.DeviceRegistrationCacheConfig, true, path)
	case "deviceregistrationsms":
		return obj._DeviceRegistrationSMS.Set(v.DeviceRegistrationSMS, true, path)
	case "questsdk":
		return obj._QuestSdk.Set(v.QuestSdk, true, path)
	case "tokenmanagerconfig":
		return obj._TokenManagerConfig.Set(v.TokenManagerConfig, true, path)
	case "networthmcpconfig":
		return obj._NetworthMcpConfig.Set(v.NetworthMcpConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj._KarzaLivenessSubscriber.Set(v.KarzaLivenessSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._LivenessStatusSubscriber.Set(v.LivenessStatusSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Liveness.Set(v.Liveness, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AFU.Set(v.AFU, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Flags.Set(v.Flags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AFUVendorUpdateSubscriber.Set(v.AFUVendorUpdateSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DeviceReregCallbackSubscriber.Set(v.DeviceReregCallbackSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DeviceRegSMSAckSubscriber.Set(v.DeviceRegSMSAckSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessPinAttemptsExceededEventSubscriber.Set(v.ProcessPinAttemptsExceededEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AFUManualReviewNotificationSubscriber.Set(v.AFUManualReviewNotificationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DeviceIntegrityConfig.Set(v.DeviceIntegrityConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OtpConfig.Set(v.OtpConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TokenStoresCacheConfig.Set(v.TokenStoresCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DeviceLocationCacheConfig.Set(v.DeviceLocationCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DeviceIntegrityNonceCacheConfig.Set(v.DeviceIntegrityNonceCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DeviceRegistrationCacheConfig.Set(v.DeviceRegistrationCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DeviceRegistrationSMS.Set(v.DeviceRegistrationSMS, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._QuestSdk.Set(v.QuestSdk, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TokenManagerConfig.Set(v.TokenManagerConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._NetworthMcpConfig.Set(v.NetworthMcpConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._Logging = v.Logging
	obj._Aws = v.Aws
	obj._LivenessStatusPublisher = v.LivenessStatusPublisher
	obj._LivenessManualReviewPublisher = v.LivenessManualReviewPublisher
	obj._LivenessSummaryCompletedEventPublisher = v.LivenessSummaryCompletedEventPublisher
	obj._BankConfig = v.BankConfig
	obj._RudderStack = v.RudderStack
	obj._AuthSecrets = v.AuthSecrets
	obj._AFUVendorUpdatePublisher = v.AFUVendorUpdatePublisher
	obj._AuthFactorUpdatePublisher = v.AuthFactorUpdatePublisher
	obj._BiometricEventPublisher = v.BiometricEventPublisher
	obj._AuthTokenCreationPublisher = v.AuthTokenCreationPublisher
	obj._ReturnOtpTokenOnError = v.ReturnOtpTokenOnError
	obj._KeyCompromisedCheckConfig = v.KeyCompromisedCheckConfig
	obj._RedisOptions = v.RedisOptions
	obj._Tracing = v.Tracing
	obj._Profiling = v.Profiling
	obj._GetSessionParams = v.GetSessionParams
	obj._PlayIntegrity = v.PlayIntegrity
	obj._DeviceRegistration = v.DeviceRegistration
	obj._Keycloak = v.Keycloak
	return nil
}

func NewLiveness() (_obj *Liveness, _setters map[string]dynconf.SetFunc) {
	_obj = &Liveness{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["onboardinglivenessruleid"] = _obj.SetOnboardingLivenessRuleId
	_obj._OnboardingLivenessRuleIdMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *Liveness) Init() {
	newObj, _ := NewLiveness()
	*obj = *newObj
}

func (obj *Liveness) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Liveness) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Liveness)
	if !ok {
		return fmt.Errorf("invalid data type %v *Liveness", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Liveness) setDynamicField(v *config.Liveness, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "onboardinglivenessruleid":
		return obj.SetOnboardingLivenessRuleId(v.OnboardingLivenessRuleId, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Liveness) setDynamicFields(v *config.Liveness, dynamic bool, path []string) (err error) {

	err = obj.SetOnboardingLivenessRuleId(v.OnboardingLivenessRuleId, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Liveness) setStaticFields(v *config.Liveness) error {

	return nil
}

func (obj *Liveness) SetOnboardingLivenessRuleId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Liveness.OnboardingLivenessRuleId", reflect.TypeOf(val))
	}
	obj._OnboardingLivenessRuleIdMutex.Lock()
	defer obj._OnboardingLivenessRuleIdMutex.Unlock()
	obj._OnboardingLivenessRuleId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "OnboardingLivenessRuleId")
	}
	return nil
}

func NewAFU() (_obj *AFU, _setters map[string]dynconf.SetFunc) {
	_obj = &AFU{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["vendorupdateproducerdelayinsecs"] = _obj.SetVendorUpdateProducerDelayInSecs
	_setters["maxrecordsdepthforafutroubleshooter"] = _obj.SetMaxRecordsDepthForAFUTroubleshooter
	_setters["enablerereginitretry"] = _obj.SetEnableReRegInitRetry
	_setters["enabledeviceregistrationosvalidation"] = _obj.SetEnableDeviceRegistrationOSValidation
	_setters["enableatmpineligibilitycheck"] = _obj.SetEnableATMPinEligibilityCheck
	_setters["readonlyfromatmpincheckflag"] = _obj.SetReadOnlyFromAtmPinCheckFlag
	_setters["rereginitincrementaldelay"] = _obj.SetReRegInitIncrementalDelay
	_setters["livenessmanualreviewexpirytime"] = _obj.SetLivenessManualReviewExpiryTime
	_setters["expiryforekyc"] = _obj.SetExpiryForEKYC
	_setters["afulivenessexternalruleid"] = _obj.SetAFULivenessExternalRuleId
	_obj._AFULivenessExternalRuleIdMutex = &sync.RWMutex{}
	_setters["afumodelexternalruleid"] = _obj.SetAFUModelExternalRuleId
	_obj._AFUModelExternalRuleIdMutex = &sync.RWMutex{}
	_AuthFactorUpdateCacheConfig, _fieldSetters := NewAuthFactorUpdateCacheConfig()
	_obj._AuthFactorUpdateCacheConfig = _AuthFactorUpdateCacheConfig
	helper.AddFieldSetters("authfactorupdatecacheconfig", _fieldSetters, _setters)
	_VendorUpdateConsumerLockConfig, _fieldSetters := NewConsumerLockConfig()
	_obj._VendorUpdateConsumerLockConfig = _VendorUpdateConsumerLockConfig
	helper.AddFieldSetters("vendorupdateconsumerlockconfig", _fieldSetters, _setters)
	_AFUCooldown, _fieldSetters := NewAFUCooldown()
	_obj._AFUCooldown = _AFUCooldown
	helper.AddFieldSetters("afucooldown", _fieldSetters, _setters)
	_AFUMobileUpdateOverrideConfig, _fieldSetters := NewAFUMobileUpdateOverride()
	_obj._AFUMobileUpdateOverrideConfig = _AFUMobileUpdateOverrideConfig
	helper.AddFieldSetters("afumobileupdateoverrideconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *AFU) Init() {
	newObj, _ := NewAFU()
	*obj = *newObj
}

func (obj *AFU) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AFU) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AFU)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFU", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AFU) setDynamicField(v *config.AFU, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "vendorupdateproducerdelayinsecs":
		return obj.SetVendorUpdateProducerDelayInSecs(v.VendorUpdateProducerDelayInSecs, true, nil)
	case "maxrecordsdepthforafutroubleshooter":
		return obj.SetMaxRecordsDepthForAFUTroubleshooter(v.MaxRecordsDepthForAFUTroubleshooter, true, nil)
	case "enablerereginitretry":
		return obj.SetEnableReRegInitRetry(v.EnableReRegInitRetry, true, nil)
	case "enabledeviceregistrationosvalidation":
		return obj.SetEnableDeviceRegistrationOSValidation(v.EnableDeviceRegistrationOSValidation, true, nil)
	case "enableatmpineligibilitycheck":
		return obj.SetEnableATMPinEligibilityCheck(v.EnableATMPinEligibilityCheck, true, nil)
	case "readonlyfromatmpincheckflag":
		return obj.SetReadOnlyFromAtmPinCheckFlag(v.ReadOnlyFromAtmPinCheckFlag, true, nil)
	case "rereginitincrementaldelay":
		return obj.SetReRegInitIncrementalDelay(v.ReRegInitIncrementalDelay, true, nil)
	case "livenessmanualreviewexpirytime":
		return obj.SetLivenessManualReviewExpiryTime(v.LivenessManualReviewExpiryTime, true, nil)
	case "expiryforekyc":
		return obj.SetExpiryForEKYC(v.ExpiryForEKYC, true, nil)
	case "afulivenessexternalruleid":
		return obj.SetAFULivenessExternalRuleId(v.AFULivenessExternalRuleId, true, nil)
	case "afumodelexternalruleid":
		return obj.SetAFUModelExternalRuleId(v.AFUModelExternalRuleId, true, nil)
	case "authfactorupdatecacheconfig":
		return obj._AuthFactorUpdateCacheConfig.Set(v.AuthFactorUpdateCacheConfig, true, path)
	case "vendorupdateconsumerlockconfig":
		return obj._VendorUpdateConsumerLockConfig.Set(v.VendorUpdateConsumerLockConfig, true, path)
	case "afucooldown":
		return obj._AFUCooldown.Set(v.AFUCooldown, true, path)
	case "afumobileupdateoverrideconfig":
		return obj._AFUMobileUpdateOverrideConfig.Set(v.AFUMobileUpdateOverrideConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AFU) setDynamicFields(v *config.AFU, dynamic bool, path []string) (err error) {

	err = obj.SetVendorUpdateProducerDelayInSecs(v.VendorUpdateProducerDelayInSecs, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxRecordsDepthForAFUTroubleshooter(v.MaxRecordsDepthForAFUTroubleshooter, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableReRegInitRetry(v.EnableReRegInitRetry, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableDeviceRegistrationOSValidation(v.EnableDeviceRegistrationOSValidation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableATMPinEligibilityCheck(v.EnableATMPinEligibilityCheck, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetReadOnlyFromAtmPinCheckFlag(v.ReadOnlyFromAtmPinCheckFlag, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetReRegInitIncrementalDelay(v.ReRegInitIncrementalDelay, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLivenessManualReviewExpiryTime(v.LivenessManualReviewExpiryTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetExpiryForEKYC(v.ExpiryForEKYC, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAFULivenessExternalRuleId(v.AFULivenessExternalRuleId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAFUModelExternalRuleId(v.AFUModelExternalRuleId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._AuthFactorUpdateCacheConfig.Set(v.AuthFactorUpdateCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VendorUpdateConsumerLockConfig.Set(v.VendorUpdateConsumerLockConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AFUCooldown.Set(v.AFUCooldown, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AFUMobileUpdateOverrideConfig.Set(v.AFUMobileUpdateOverrideConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AFU) setStaticFields(v *config.AFU) error {

	obj._CredentialsOrder = v.CredentialsOrder
	obj._BypassCredentialVerificationForPhoneNumbers = v.BypassCredentialVerificationForPhoneNumbers
	obj._MinVersionForAFURetry = v.MinVersionForAFURetry
	return nil
}

func (obj *AFU) SetVendorUpdateProducerDelayInSecs(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFU.VendorUpdateProducerDelayInSecs", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._VendorUpdateProducerDelayInSecs, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "VendorUpdateProducerDelayInSecs")
	}
	return nil
}
func (obj *AFU) SetMaxRecordsDepthForAFUTroubleshooter(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFU.MaxRecordsDepthForAFUTroubleshooter", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxRecordsDepthForAFUTroubleshooter, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxRecordsDepthForAFUTroubleshooter")
	}
	return nil
}
func (obj *AFU) SetEnableReRegInitRetry(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFU.EnableReRegInitRetry", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableReRegInitRetry, 1)
	} else {
		atomic.StoreUint32(&obj._EnableReRegInitRetry, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableReRegInitRetry")
	}
	return nil
}
func (obj *AFU) SetEnableDeviceRegistrationOSValidation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFU.EnableDeviceRegistrationOSValidation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableDeviceRegistrationOSValidation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableDeviceRegistrationOSValidation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableDeviceRegistrationOSValidation")
	}
	return nil
}
func (obj *AFU) SetEnableATMPinEligibilityCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFU.EnableATMPinEligibilityCheck", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableATMPinEligibilityCheck, 1)
	} else {
		atomic.StoreUint32(&obj._EnableATMPinEligibilityCheck, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableATMPinEligibilityCheck")
	}
	return nil
}
func (obj *AFU) SetReadOnlyFromAtmPinCheckFlag(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFU.ReadOnlyFromAtmPinCheckFlag", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ReadOnlyFromAtmPinCheckFlag, 1)
	} else {
		atomic.StoreUint32(&obj._ReadOnlyFromAtmPinCheckFlag, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ReadOnlyFromAtmPinCheckFlag")
	}
	return nil
}
func (obj *AFU) SetReRegInitIncrementalDelay(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFU.ReRegInitIncrementalDelay", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ReRegInitIncrementalDelay, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ReRegInitIncrementalDelay")
	}
	return nil
}
func (obj *AFU) SetLivenessManualReviewExpiryTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFU.LivenessManualReviewExpiryTime", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._LivenessManualReviewExpiryTime, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "LivenessManualReviewExpiryTime")
	}
	return nil
}
func (obj *AFU) SetExpiryForEKYC(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFU.ExpiryForEKYC", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ExpiryForEKYC, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ExpiryForEKYC")
	}
	return nil
}
func (obj *AFU) SetAFULivenessExternalRuleId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFU.AFULivenessExternalRuleId", reflect.TypeOf(val))
	}
	obj._AFULivenessExternalRuleIdMutex.Lock()
	defer obj._AFULivenessExternalRuleIdMutex.Unlock()
	obj._AFULivenessExternalRuleId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "AFULivenessExternalRuleId")
	}
	return nil
}
func (obj *AFU) SetAFUModelExternalRuleId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFU.AFUModelExternalRuleId", reflect.TypeOf(val))
	}
	obj._AFUModelExternalRuleIdMutex.Lock()
	defer obj._AFUModelExternalRuleIdMutex.Unlock()
	obj._AFUModelExternalRuleId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "AFUModelExternalRuleId")
	}
	return nil
}

func NewAuthFactorUpdateCacheConfig() (_obj *AuthFactorUpdateCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &AuthFactorUpdateCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	return _obj, _setters
}

func (obj *AuthFactorUpdateCacheConfig) Init() {
	newObj, _ := NewAuthFactorUpdateCacheConfig()
	*obj = *newObj
}

func (obj *AuthFactorUpdateCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AuthFactorUpdateCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AuthFactorUpdateCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *AuthFactorUpdateCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AuthFactorUpdateCacheConfig) setDynamicField(v *config.AuthFactorUpdateCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AuthFactorUpdateCacheConfig) setDynamicFields(v *config.AuthFactorUpdateCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AuthFactorUpdateCacheConfig) setStaticFields(v *config.AuthFactorUpdateCacheConfig) error {

	obj._AuthFactorUpdatePrefix = v.AuthFactorUpdatePrefix
	return nil
}

func (obj *AuthFactorUpdateCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AuthFactorUpdateCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}

func NewConsumerLockConfig() (_obj *ConsumerLockConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ConsumerLockConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["timeout"] = _obj.SetTimeout
	return _obj, _setters
}

func (obj *ConsumerLockConfig) Init() {
	newObj, _ := NewConsumerLockConfig()
	*obj = *newObj
}

func (obj *ConsumerLockConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ConsumerLockConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ConsumerLockConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ConsumerLockConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ConsumerLockConfig) setDynamicField(v *config.ConsumerLockConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "timeout":
		return obj.SetTimeout(v.Timeout, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ConsumerLockConfig) setDynamicFields(v *config.ConsumerLockConfig, dynamic bool, path []string) (err error) {

	err = obj.SetTimeout(v.Timeout, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ConsumerLockConfig) setStaticFields(v *config.ConsumerLockConfig) error {

	return nil
}

func (obj *ConsumerLockConfig) SetTimeout(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *ConsumerLockConfig.Timeout", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._Timeout, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Timeout")
	}
	return nil
}

func NewAFUCooldown() (_obj *AFUCooldown, _setters map[string]dynconf.SetFunc) {
	_obj = &AFUCooldown{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablecooldown"] = _obj.SetEnableCooldown
	_setters["cooldownforupiregulation"] = _obj.SetCooldownForUPIRegulation

	_obj._CooldownDurationMap = &syncmap.Map[string, time.Duration]{}
	_setters["cooldowndurationmap"] = _obj.SetCooldownDurationMap
	return _obj, _setters
}

func (obj *AFUCooldown) Init() {
	newObj, _ := NewAFUCooldown()
	*obj = *newObj
}

func (obj *AFUCooldown) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AFUCooldown) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AFUCooldown)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFUCooldown", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AFUCooldown) setDynamicField(v *config.AFUCooldown, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablecooldown":
		return obj.SetEnableCooldown(v.EnableCooldown, true, nil)
	case "cooldownforupiregulation":
		return obj.SetCooldownForUPIRegulation(v.CooldownForUPIRegulation, true, nil)
	case "cooldowndurationmap":
		return obj.SetCooldownDurationMap(v.CooldownDurationMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AFUCooldown) setDynamicFields(v *config.AFUCooldown, dynamic bool, path []string) (err error) {

	err = obj.SetEnableCooldown(v.EnableCooldown, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCooldownForUPIRegulation(v.CooldownForUPIRegulation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCooldownDurationMap(v.CooldownDurationMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AFUCooldown) setStaticFields(v *config.AFUCooldown) error {

	return nil
}

func (obj *AFUCooldown) SetEnableCooldown(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFUCooldown.EnableCooldown", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableCooldown, 1)
	} else {
		atomic.StoreUint32(&obj._EnableCooldown, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableCooldown")
	}
	return nil
}
func (obj *AFUCooldown) SetCooldownForUPIRegulation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFUCooldown.CooldownForUPIRegulation", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CooldownForUPIRegulation, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CooldownForUPIRegulation")
	}
	return nil
}
func (obj *AFUCooldown) SetCooldownDurationMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFUCooldown.CooldownDurationMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._CooldownDurationMap, v, path)
}

func NewAFUMobileUpdateOverride() (_obj *AFUMobileUpdateOverride, _setters map[string]dynconf.SetFunc) {
	_obj = &AFUMobileUpdateOverride{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["actorids"] = _obj.SetActorIds
	_obj._ActorIdsMutex = &sync.RWMutex{}
	_setters["activefrom"] = _obj.SetActiveFrom
	_obj._ActiveFromMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *AFUMobileUpdateOverride) Init() {
	newObj, _ := NewAFUMobileUpdateOverride()
	*obj = *newObj
}

func (obj *AFUMobileUpdateOverride) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AFUMobileUpdateOverride) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AFUMobileUpdateOverride)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFUMobileUpdateOverride", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AFUMobileUpdateOverride) setDynamicField(v *config.AFUMobileUpdateOverride, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "actorids":
		return obj.SetActorIds(v.ActorIds, true, path)
	case "activefrom":
		return obj.SetActiveFrom(v.ActiveFrom, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AFUMobileUpdateOverride) setDynamicFields(v *config.AFUMobileUpdateOverride, dynamic bool, path []string) (err error) {

	err = obj.SetActorIds(v.ActorIds, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetActiveFrom(v.ActiveFrom, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AFUMobileUpdateOverride) setStaticFields(v *config.AFUMobileUpdateOverride) error {

	return nil
}

func (obj *AFUMobileUpdateOverride) SetActorIds(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFUMobileUpdateOverride.ActorIds", reflect.TypeOf(val))
	}
	obj._ActorIdsMutex.Lock()
	defer obj._ActorIdsMutex.Unlock()
	obj._ActorIds = roarray.New[string](v)
	return nil
}
func (obj *AFUMobileUpdateOverride) SetActiveFrom(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Time)
	if !ok {
		return fmt.Errorf("invalid data type %v *AFUMobileUpdateOverride.ActiveFrom", reflect.TypeOf(val))
	}
	obj._ActiveFromMutex.Lock()
	defer obj._ActiveFromMutex.Unlock()
	obj._ActiveFrom = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ActiveFrom")
	}
	return nil
}

func NewFlags() (_obj *Flags, _setters map[string]dynconf.SetFunc) {
	_obj = &Flags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablelivenessmanualreviewinafu"] = _obj.SetEnableLivenessManualReviewInAfu
	_setters["enablesmsacklistener"] = _obj.SetEnableSMSAckListener
	_setters["enablefmv2foronboarding"] = _obj.SetEnableFMV2ForOnboarding
	_setters["enablesmshealthengine"] = _obj.SetEnableSmsHealthEngine
	_setters["enableorchestratorlivenessv2"] = _obj.SetEnableOrchestratorLivenessV2
	_setters["enableekycinphoneupdate"] = _obj.SetEnableEKYCInPhoneUpdate
	_setters["useffmpegtocompressvideo"] = _obj.SetUseFFMPEGToCompressVideo
	_setters["enableafuriskscreeningfornruser"] = _obj.SetEnableAFURiskScreeningForNRUser
	_setters["enablepinretriesexceededeventconsumer"] = _obj.SetEnablePinRetriesExceededEventConsumer
	_EnableDeviceRegSMSPayloadGenerationLimit, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableDeviceRegSMSPayloadGenerationLimit = _EnableDeviceRegSMSPayloadGenerationLimit
	helper.AddFieldSetters("enabledeviceregsmspayloadgenerationlimit", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *Flags) Init() {
	newObj, _ := NewFlags()
	*obj = *newObj
}

func (obj *Flags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Flags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Flags)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Flags) setDynamicField(v *config.Flags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablelivenessmanualreviewinafu":
		return obj.SetEnableLivenessManualReviewInAfu(v.EnableLivenessManualReviewInAfu, true, nil)
	case "enablesmsacklistener":
		return obj.SetEnableSMSAckListener(v.EnableSMSAckListener, true, nil)
	case "enablefmv2foronboarding":
		return obj.SetEnableFMV2ForOnboarding(v.EnableFMV2ForOnboarding, true, nil)
	case "enablesmshealthengine":
		return obj.SetEnableSmsHealthEngine(v.EnableSmsHealthEngine, true, nil)
	case "enableorchestratorlivenessv2":
		return obj.SetEnableOrchestratorLivenessV2(v.EnableOrchestratorLivenessV2, true, nil)
	case "enableekycinphoneupdate":
		return obj.SetEnableEKYCInPhoneUpdate(v.EnableEKYCInPhoneUpdate, true, nil)
	case "useffmpegtocompressvideo":
		return obj.SetUseFFMPEGToCompressVideo(v.UseFFMPEGToCompressVideo, true, nil)
	case "enableafuriskscreeningfornruser":
		return obj.SetEnableAFURiskScreeningForNRUser(v.EnableAFURiskScreeningForNRUser, true, nil)
	case "enablepinretriesexceededeventconsumer":
		return obj.SetEnablePinRetriesExceededEventConsumer(v.EnablePinRetriesExceededEventConsumer, true, nil)
	case "enabledeviceregsmspayloadgenerationlimit":
		return obj._EnableDeviceRegSMSPayloadGenerationLimit.Set(v.EnableDeviceRegSMSPayloadGenerationLimit, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Flags) setDynamicFields(v *config.Flags, dynamic bool, path []string) (err error) {

	err = obj.SetEnableLivenessManualReviewInAfu(v.EnableLivenessManualReviewInAfu, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableSMSAckListener(v.EnableSMSAckListener, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableFMV2ForOnboarding(v.EnableFMV2ForOnboarding, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableSmsHealthEngine(v.EnableSmsHealthEngine, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableOrchestratorLivenessV2(v.EnableOrchestratorLivenessV2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableEKYCInPhoneUpdate(v.EnableEKYCInPhoneUpdate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUseFFMPEGToCompressVideo(v.UseFFMPEGToCompressVideo, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableAFURiskScreeningForNRUser(v.EnableAFURiskScreeningForNRUser, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnablePinRetriesExceededEventConsumer(v.EnablePinRetriesExceededEventConsumer, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._EnableDeviceRegSMSPayloadGenerationLimit.Set(v.EnableDeviceRegSMSPayloadGenerationLimit, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Flags) setStaticFields(v *config.Flags) error {

	obj._TrimDebugMessageFromStatus = v.TrimDebugMessageFromStatus
	obj._SkipIOSIdTokenExpiryCheck = v.SkipIOSIdTokenExpiryCheck
	obj._EnableV2IdTokenVerifierForAndroid = v.EnableV2IdTokenVerifierForAndroid
	obj._EnableCheckForAccessRevoke = v.EnableCheckForAccessRevoke
	obj._DisableLivenessAttemptChangeFeed = v.DisableLivenessAttemptChangeFeed
	return nil
}

func (obj *Flags) SetEnableLivenessManualReviewInAfu(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableLivenessManualReviewInAfu", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableLivenessManualReviewInAfu, 1)
	} else {
		atomic.StoreUint32(&obj._EnableLivenessManualReviewInAfu, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableLivenessManualReviewInAfu")
	}
	return nil
}
func (obj *Flags) SetEnableSMSAckListener(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableSMSAckListener", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableSMSAckListener, 1)
	} else {
		atomic.StoreUint32(&obj._EnableSMSAckListener, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableSMSAckListener")
	}
	return nil
}
func (obj *Flags) SetEnableFMV2ForOnboarding(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableFMV2ForOnboarding", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableFMV2ForOnboarding, 1)
	} else {
		atomic.StoreUint32(&obj._EnableFMV2ForOnboarding, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableFMV2ForOnboarding")
	}
	return nil
}
func (obj *Flags) SetEnableSmsHealthEngine(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableSmsHealthEngine", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableSmsHealthEngine, 1)
	} else {
		atomic.StoreUint32(&obj._EnableSmsHealthEngine, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableSmsHealthEngine")
	}
	return nil
}
func (obj *Flags) SetEnableOrchestratorLivenessV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableOrchestratorLivenessV2", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableOrchestratorLivenessV2, 1)
	} else {
		atomic.StoreUint32(&obj._EnableOrchestratorLivenessV2, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableOrchestratorLivenessV2")
	}
	return nil
}
func (obj *Flags) SetEnableEKYCInPhoneUpdate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableEKYCInPhoneUpdate", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableEKYCInPhoneUpdate, 1)
	} else {
		atomic.StoreUint32(&obj._EnableEKYCInPhoneUpdate, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableEKYCInPhoneUpdate")
	}
	return nil
}
func (obj *Flags) SetUseFFMPEGToCompressVideo(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.UseFFMPEGToCompressVideo", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UseFFMPEGToCompressVideo, 1)
	} else {
		atomic.StoreUint32(&obj._UseFFMPEGToCompressVideo, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UseFFMPEGToCompressVideo")
	}
	return nil
}
func (obj *Flags) SetEnableAFURiskScreeningForNRUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableAFURiskScreeningForNRUser", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableAFURiskScreeningForNRUser, 1)
	} else {
		atomic.StoreUint32(&obj._EnableAFURiskScreeningForNRUser, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableAFURiskScreeningForNRUser")
	}
	return nil
}
func (obj *Flags) SetEnablePinRetriesExceededEventConsumer(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnablePinRetriesExceededEventConsumer", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnablePinRetriesExceededEventConsumer, 1)
	} else {
		atomic.StoreUint32(&obj._EnablePinRetriesExceededEventConsumer, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnablePinRetriesExceededEventConsumer")
	}
	return nil
}

func NewDeviceIntegrityConfig() (_obj *DeviceIntegrityConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DeviceIntegrityConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["asyncdeviceintegrityrolloutpercentage"] = _obj.SetAsyncDeviceIntegrityRolloutPercentage
	_setters["randomexpiryinsecondsfordeviceintegrityv2"] = _obj.SetRandomExpiryInSecondsForDeviceIntegrityV2
	_setters["expirytimefordeviceintegrity"] = _obj.SetExpiryTimeForDeviceIntegrity
	_setters["expirytimefordeviceintegrityv2"] = _obj.SetExpiryTimeForDeviceIntegrityV2

	_obj._MockSafetynetTokenResult = &syncmap.Map[string, string]{}
	_setters["mocksafetynettokenresult"] = _obj.SetMockSafetynetTokenResult
	_setters["bypassphonenumbers"] = _obj.SetBypassPhoneNumbers
	_obj._BypassPhoneNumbersMutex = &sync.RWMutex{}
	_DeviceAttestationV2Cfg, _fieldSetters := genapp.NewFeatureConfig()
	_obj._DeviceAttestationV2Cfg = _DeviceAttestationV2Cfg
	helper.AddFieldSetters("deviceattestationv2cfg", _fieldSetters, _setters)
	_OnDeviceIntegrityCheckCfg, _fieldSetters := genapp.NewFeatureConfig()
	_obj._OnDeviceIntegrityCheckCfg = _OnDeviceIntegrityCheckCfg
	helper.AddFieldSetters("ondeviceintegritycheckcfg", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *DeviceIntegrityConfig) Init() {
	newObj, _ := NewDeviceIntegrityConfig()
	*obj = *newObj
}

func (obj *DeviceIntegrityConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DeviceIntegrityConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DeviceIntegrityConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceIntegrityConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DeviceIntegrityConfig) setDynamicField(v *config.DeviceIntegrityConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "asyncdeviceintegrityrolloutpercentage":
		return obj.SetAsyncDeviceIntegrityRolloutPercentage(v.AsyncDeviceIntegrityRolloutPercentage, true, nil)
	case "randomexpiryinsecondsfordeviceintegrityv2":
		return obj.SetRandomExpiryInSecondsForDeviceIntegrityV2(v.RandomExpiryInSecondsForDeviceIntegrityV2, true, nil)
	case "expirytimefordeviceintegrity":
		return obj.SetExpiryTimeForDeviceIntegrity(v.ExpiryTimeForDeviceIntegrity, true, nil)
	case "expirytimefordeviceintegrityv2":
		return obj.SetExpiryTimeForDeviceIntegrityV2(v.ExpiryTimeForDeviceIntegrityV2, true, nil)
	case "mocksafetynettokenresult":
		return obj.SetMockSafetynetTokenResult(v.MockSafetynetTokenResult, true, path)
	case "bypassphonenumbers":
		return obj.SetBypassPhoneNumbers(v.BypassPhoneNumbers, true, path)
	case "deviceattestationv2cfg":
		return obj._DeviceAttestationV2Cfg.Set(v.DeviceAttestationV2Cfg, true, path)
	case "ondeviceintegritycheckcfg":
		return obj._OnDeviceIntegrityCheckCfg.Set(v.OnDeviceIntegrityCheckCfg, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DeviceIntegrityConfig) setDynamicFields(v *config.DeviceIntegrityConfig, dynamic bool, path []string) (err error) {

	err = obj.SetAsyncDeviceIntegrityRolloutPercentage(v.AsyncDeviceIntegrityRolloutPercentage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRandomExpiryInSecondsForDeviceIntegrityV2(v.RandomExpiryInSecondsForDeviceIntegrityV2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetExpiryTimeForDeviceIntegrity(v.ExpiryTimeForDeviceIntegrity, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetExpiryTimeForDeviceIntegrityV2(v.ExpiryTimeForDeviceIntegrityV2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMockSafetynetTokenResult(v.MockSafetynetTokenResult, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetBypassPhoneNumbers(v.BypassPhoneNumbers, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DeviceAttestationV2Cfg.Set(v.DeviceAttestationV2Cfg, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OnDeviceIntegrityCheckCfg.Set(v.OnDeviceIntegrityCheckCfg, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DeviceIntegrityConfig) setStaticFields(v *config.DeviceIntegrityConfig) error {

	obj._AndroidAppPackageNames = v.AndroidAppPackageNames
	obj._AndroidApkCertificateDigestSha256 = v.AndroidApkCertificateDigestSha256
	obj._DeviceIntegrityNonceValidityInSecs = v.DeviceIntegrityNonceValidityInSecs
	obj._CheckApkPackageName = v.CheckApkPackageName
	obj._CheckCertificateHash = v.CheckCertificateHash
	obj._AppleAppAttestRootCertificate = v.AppleAppAttestRootCertificate
	obj._AppleCredCertOidExtensionSeq = v.AppleCredCertOidExtensionSeq
	obj._IosAppIdentifiers = v.IosAppIdentifiers
	obj._AllowSafetynetCertChainVerificationWithModifiedCurrTime = v.AllowSafetynetCertChainVerificationWithModifiedCurrTime
	obj._CurrTimeOverrideForSafetynetCertVerification = v.CurrTimeOverrideForSafetynetCertVerification
	obj._BypassTokenIdValidationForPhoneNumbers = v.BypassTokenIdValidationForPhoneNumbers
	obj._WhitelistedAttestationTokens = v.WhitelistedAttestationTokens
	return nil
}

func (obj *DeviceIntegrityConfig) SetAsyncDeviceIntegrityRolloutPercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceIntegrityConfig.AsyncDeviceIntegrityRolloutPercentage", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._AsyncDeviceIntegrityRolloutPercentage, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AsyncDeviceIntegrityRolloutPercentage")
	}
	return nil
}
func (obj *DeviceIntegrityConfig) SetRandomExpiryInSecondsForDeviceIntegrityV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceIntegrityConfig.RandomExpiryInSecondsForDeviceIntegrityV2", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RandomExpiryInSecondsForDeviceIntegrityV2, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RandomExpiryInSecondsForDeviceIntegrityV2")
	}
	return nil
}
func (obj *DeviceIntegrityConfig) SetExpiryTimeForDeviceIntegrity(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceIntegrityConfig.ExpiryTimeForDeviceIntegrity", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ExpiryTimeForDeviceIntegrity, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ExpiryTimeForDeviceIntegrity")
	}
	return nil
}
func (obj *DeviceIntegrityConfig) SetExpiryTimeForDeviceIntegrityV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceIntegrityConfig.ExpiryTimeForDeviceIntegrityV2", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ExpiryTimeForDeviceIntegrityV2, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ExpiryTimeForDeviceIntegrityV2")
	}
	return nil
}
func (obj *DeviceIntegrityConfig) SetMockSafetynetTokenResult(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceIntegrityConfig.MockSafetynetTokenResult", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._MockSafetynetTokenResult, v, path)
}
func (obj *DeviceIntegrityConfig) SetBypassPhoneNumbers(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceIntegrityConfig.BypassPhoneNumbers", reflect.TypeOf(val))
	}
	obj._BypassPhoneNumbersMutex.Lock()
	defer obj._BypassPhoneNumbersMutex.Unlock()
	obj._BypassPhoneNumbers = roarray.New[string](v)
	return nil
}

func NewOtpConfig() (_obj *OtpConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OtpConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["skipotpvalidationfornumbers"] = _obj.SetSkipOTPValidationForNumbers
	_obj._SkipOTPValidationForNumbersMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *OtpConfig) Init() {
	newObj, _ := NewOtpConfig()
	*obj = *newObj
}

func (obj *OtpConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OtpConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OtpConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *OtpConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OtpConfig) setDynamicField(v *config.OtpConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "skipotpvalidationfornumbers":
		return obj.SetSkipOTPValidationForNumbers(v.SkipOTPValidationForNumbers, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OtpConfig) setDynamicFields(v *config.OtpConfig, dynamic bool, path []string) (err error) {

	err = obj.SetSkipOTPValidationForNumbers(v.SkipOTPValidationForNumbers, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OtpConfig) setStaticFields(v *config.OtpConfig) error {

	obj._OtpResendInterval = v.OtpResendInterval
	obj._GenerateOtpLimits = v.GenerateOtpLimits
	obj._CustomGenerateOtpLimits = v.CustomGenerateOtpLimits
	return nil
}

func (obj *OtpConfig) SetSkipOTPValidationForNumbers(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *OtpConfig.SkipOTPValidationForNumbers", reflect.TypeOf(val))
	}
	obj._SkipOTPValidationForNumbersMutex.Lock()
	defer obj._SkipOTPValidationForNumbersMutex.Unlock()
	obj._SkipOTPValidationForNumbers = roarray.New[string](v)
	return nil
}

func NewTokenStoresCacheConfig() (_obj *TokenStoresCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &TokenStoresCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	_setters["disabletokenstorewrites"] = _obj.SetDisableTokenStoreWrites
	_setters["maketokenstorewritesoptional"] = _obj.SetMakeTokenStoreWritesOptional
	_setters["enablewritesonlyforunonboardedusers"] = _obj.SetEnableWritesOnlyForUnOnboardedUsers
	_setters["disablecachingbytokenidforaccesstokens"] = _obj.SetDisableCachingByTokenIdForAccessTokens
	_setters["enablecachingbysubject"] = _obj.SetEnableCachingBySubject
	_setters["usetokenstoresdaov2"] = _obj.SetUseTokenStoresDaoV2
	_setters["cachettl"] = _obj.SetCacheTTL
	_setters["refreshtokencachettl"] = _obj.SetRefreshTokenCacheTTL
	return _obj, _setters
}

func (obj *TokenStoresCacheConfig) Init() {
	newObj, _ := NewTokenStoresCacheConfig()
	*obj = *newObj
}

func (obj *TokenStoresCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *TokenStoresCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.TokenStoresCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *TokenStoresCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *TokenStoresCacheConfig) setDynamicField(v *config.TokenStoresCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "disabletokenstorewrites":
		return obj.SetDisableTokenStoreWrites(v.DisableTokenStoreWrites, true, nil)
	case "maketokenstorewritesoptional":
		return obj.SetMakeTokenStoreWritesOptional(v.MakeTokenStoreWritesOptional, true, nil)
	case "enablewritesonlyforunonboardedusers":
		return obj.SetEnableWritesOnlyForUnOnboardedUsers(v.EnableWritesOnlyForUnOnboardedUsers, true, nil)
	case "disablecachingbytokenidforaccesstokens":
		return obj.SetDisableCachingByTokenIdForAccessTokens(v.DisableCachingByTokenIdForAccessTokens, true, nil)
	case "enablecachingbysubject":
		return obj.SetEnableCachingBySubject(v.EnableCachingBySubject, true, nil)
	case "usetokenstoresdaov2":
		return obj.SetUseTokenStoresDaoV2(v.UseTokenStoresDaoV2, true, nil)
	case "cachettl":
		return obj.SetCacheTTL(v.CacheTTL, true, nil)
	case "refreshtokencachettl":
		return obj.SetRefreshTokenCacheTTL(v.RefreshTokenCacheTTL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *TokenStoresCacheConfig) setDynamicFields(v *config.TokenStoresCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableTokenStoreWrites(v.DisableTokenStoreWrites, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMakeTokenStoreWritesOptional(v.MakeTokenStoreWritesOptional, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableWritesOnlyForUnOnboardedUsers(v.EnableWritesOnlyForUnOnboardedUsers, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableCachingByTokenIdForAccessTokens(v.DisableCachingByTokenIdForAccessTokens, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableCachingBySubject(v.EnableCachingBySubject, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUseTokenStoresDaoV2(v.UseTokenStoresDaoV2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCacheTTL(v.CacheTTL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRefreshTokenCacheTTL(v.RefreshTokenCacheTTL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *TokenStoresCacheConfig) setStaticFields(v *config.TokenStoresCacheConfig) error {

	obj._TokenStoresPrefix = v.TokenStoresPrefix
	obj._TokenStoresPrefixV2 = v.TokenStoresPrefixV2
	obj._TokenLastActivityKeyPrefix = v.TokenLastActivityKeyPrefix
	obj._TokenCacheTTLMap = v.TokenCacheTTLMap
	return nil
}

func (obj *TokenStoresCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TokenStoresCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *TokenStoresCacheConfig) SetDisableTokenStoreWrites(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TokenStoresCacheConfig.DisableTokenStoreWrites", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableTokenStoreWrites, 1)
	} else {
		atomic.StoreUint32(&obj._DisableTokenStoreWrites, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableTokenStoreWrites")
	}
	return nil
}
func (obj *TokenStoresCacheConfig) SetMakeTokenStoreWritesOptional(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TokenStoresCacheConfig.MakeTokenStoreWritesOptional", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._MakeTokenStoreWritesOptional, 1)
	} else {
		atomic.StoreUint32(&obj._MakeTokenStoreWritesOptional, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "MakeTokenStoreWritesOptional")
	}
	return nil
}
func (obj *TokenStoresCacheConfig) SetEnableWritesOnlyForUnOnboardedUsers(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TokenStoresCacheConfig.EnableWritesOnlyForUnOnboardedUsers", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableWritesOnlyForUnOnboardedUsers, 1)
	} else {
		atomic.StoreUint32(&obj._EnableWritesOnlyForUnOnboardedUsers, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableWritesOnlyForUnOnboardedUsers")
	}
	return nil
}
func (obj *TokenStoresCacheConfig) SetDisableCachingByTokenIdForAccessTokens(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TokenStoresCacheConfig.DisableCachingByTokenIdForAccessTokens", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableCachingByTokenIdForAccessTokens, 1)
	} else {
		atomic.StoreUint32(&obj._DisableCachingByTokenIdForAccessTokens, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableCachingByTokenIdForAccessTokens")
	}
	return nil
}
func (obj *TokenStoresCacheConfig) SetEnableCachingBySubject(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TokenStoresCacheConfig.EnableCachingBySubject", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableCachingBySubject, 1)
	} else {
		atomic.StoreUint32(&obj._EnableCachingBySubject, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableCachingBySubject")
	}
	return nil
}
func (obj *TokenStoresCacheConfig) SetUseTokenStoresDaoV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TokenStoresCacheConfig.UseTokenStoresDaoV2", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UseTokenStoresDaoV2, 1)
	} else {
		atomic.StoreUint32(&obj._UseTokenStoresDaoV2, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UseTokenStoresDaoV2")
	}
	return nil
}
func (obj *TokenStoresCacheConfig) SetCacheTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *TokenStoresCacheConfig.CacheTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CacheTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CacheTTL")
	}
	return nil
}
func (obj *TokenStoresCacheConfig) SetRefreshTokenCacheTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *TokenStoresCacheConfig.RefreshTokenCacheTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RefreshTokenCacheTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RefreshTokenCacheTTL")
	}
	return nil
}

func NewDeviceLocationCacheConfig() (_obj *DeviceLocationCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DeviceLocationCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	_setters["cachettl"] = _obj.SetCacheTTL
	return _obj, _setters
}

func (obj *DeviceLocationCacheConfig) Init() {
	newObj, _ := NewDeviceLocationCacheConfig()
	*obj = *newObj
}

func (obj *DeviceLocationCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DeviceLocationCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DeviceLocationCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceLocationCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DeviceLocationCacheConfig) setDynamicField(v *config.DeviceLocationCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "cachettl":
		return obj.SetCacheTTL(v.CacheTTL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DeviceLocationCacheConfig) setDynamicFields(v *config.DeviceLocationCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCacheTTL(v.CacheTTL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DeviceLocationCacheConfig) setStaticFields(v *config.DeviceLocationCacheConfig) error {

	obj._DeviceLocationPrefix = v.DeviceLocationPrefix
	return nil
}

func (obj *DeviceLocationCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceLocationCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *DeviceLocationCacheConfig) SetCacheTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceLocationCacheConfig.CacheTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CacheTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CacheTTL")
	}
	return nil
}

func NewDeviceIntegrityNonceCacheConfig() (_obj *DeviceIntegrityNonceCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DeviceIntegrityNonceCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	_setters["forceonlycache"] = _obj.SetForceOnlyCache
	_setters["cachettl"] = _obj.SetCacheTTL
	return _obj, _setters
}

func (obj *DeviceIntegrityNonceCacheConfig) Init() {
	newObj, _ := NewDeviceIntegrityNonceCacheConfig()
	*obj = *newObj
}

func (obj *DeviceIntegrityNonceCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DeviceIntegrityNonceCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DeviceIntegrityNonceCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceIntegrityNonceCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DeviceIntegrityNonceCacheConfig) setDynamicField(v *config.DeviceIntegrityNonceCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "forceonlycache":
		return obj.SetForceOnlyCache(v.ForceOnlyCache, true, nil)
	case "cachettl":
		return obj.SetCacheTTL(v.CacheTTL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DeviceIntegrityNonceCacheConfig) setDynamicFields(v *config.DeviceIntegrityNonceCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetForceOnlyCache(v.ForceOnlyCache, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCacheTTL(v.CacheTTL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DeviceIntegrityNonceCacheConfig) setStaticFields(v *config.DeviceIntegrityNonceCacheConfig) error {

	return nil
}

func (obj *DeviceIntegrityNonceCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceIntegrityNonceCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *DeviceIntegrityNonceCacheConfig) SetForceOnlyCache(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceIntegrityNonceCacheConfig.ForceOnlyCache", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ForceOnlyCache, 1)
	} else {
		atomic.StoreUint32(&obj._ForceOnlyCache, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ForceOnlyCache")
	}
	return nil
}
func (obj *DeviceIntegrityNonceCacheConfig) SetCacheTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceIntegrityNonceCacheConfig.CacheTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CacheTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CacheTTL")
	}
	return nil
}

func NewDeviceRegistrationCacheConfig() (_obj *DeviceRegistrationCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DeviceRegistrationCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	_setters["cachettl"] = _obj.SetCacheTTL
	return _obj, _setters
}

func (obj *DeviceRegistrationCacheConfig) Init() {
	newObj, _ := NewDeviceRegistrationCacheConfig()
	*obj = *newObj
}

func (obj *DeviceRegistrationCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DeviceRegistrationCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DeviceRegistrationCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceRegistrationCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DeviceRegistrationCacheConfig) setDynamicField(v *config.DeviceRegistrationCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "cachettl":
		return obj.SetCacheTTL(v.CacheTTL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DeviceRegistrationCacheConfig) setDynamicFields(v *config.DeviceRegistrationCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCacheTTL(v.CacheTTL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DeviceRegistrationCacheConfig) setStaticFields(v *config.DeviceRegistrationCacheConfig) error {

	return nil
}

func (obj *DeviceRegistrationCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceRegistrationCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *DeviceRegistrationCacheConfig) SetCacheTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceRegistrationCacheConfig.CacheTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CacheTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CacheTTL")
	}
	return nil
}

func NewDeviceRegistrationSMSCfg() (_obj *DeviceRegistrationSMSCfg, _setters map[string]dynconf.SetFunc) {
	_obj = &DeviceRegistrationSMSCfg{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["downthrottledisabledtopercent"] = _obj.SetDownThrottleDisabledToPercent
	_setters["maxdeviceregattemptperday"] = _obj.SetMaxDeviceRegAttemptPerDay
	_setters["enablev2smsendpointselector"] = _obj.SetEnableV2SMSEndpointSelector

	_obj._EndpointsEnabled = &syncmap.Map[string, bool]{}
	_setters["endpointsenabled"] = _obj.SetEndpointsEnabled
	return _obj, _setters
}

func (obj *DeviceRegistrationSMSCfg) Init() {
	newObj, _ := NewDeviceRegistrationSMSCfg()
	*obj = *newObj
}

func (obj *DeviceRegistrationSMSCfg) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DeviceRegistrationSMSCfg) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DeviceRegistrationSMSCfg)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceRegistrationSMSCfg", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DeviceRegistrationSMSCfg) setDynamicField(v *config.DeviceRegistrationSMSCfg, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "downthrottledisabledtopercent":
		return obj.SetDownThrottleDisabledToPercent(v.DownThrottleDisabledToPercent, true, nil)
	case "maxdeviceregattemptperday":
		return obj.SetMaxDeviceRegAttemptPerDay(v.MaxDeviceRegAttemptPerDay, true, nil)
	case "enablev2smsendpointselector":
		return obj.SetEnableV2SMSEndpointSelector(v.EnableV2SMSEndpointSelector, true, nil)
	case "endpointsenabled":
		return obj.SetEndpointsEnabled(v.EndpointsEnabled, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DeviceRegistrationSMSCfg) setDynamicFields(v *config.DeviceRegistrationSMSCfg, dynamic bool, path []string) (err error) {

	err = obj.SetDownThrottleDisabledToPercent(v.DownThrottleDisabledToPercent, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxDeviceRegAttemptPerDay(v.MaxDeviceRegAttemptPerDay, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableV2SMSEndpointSelector(v.EnableV2SMSEndpointSelector, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEndpointsEnabled(v.EndpointsEnabled, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DeviceRegistrationSMSCfg) setStaticFields(v *config.DeviceRegistrationSMSCfg) error {

	return nil
}

func (obj *DeviceRegistrationSMSCfg) SetDownThrottleDisabledToPercent(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint8)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceRegistrationSMSCfg.DownThrottleDisabledToPercent", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._DownThrottleDisabledToPercent, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DownThrottleDisabledToPercent")
	}
	return nil
}
func (obj *DeviceRegistrationSMSCfg) SetMaxDeviceRegAttemptPerDay(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceRegistrationSMSCfg.MaxDeviceRegAttemptPerDay", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxDeviceRegAttemptPerDay, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxDeviceRegAttemptPerDay")
	}
	return nil
}
func (obj *DeviceRegistrationSMSCfg) SetEnableV2SMSEndpointSelector(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceRegistrationSMSCfg.EnableV2SMSEndpointSelector", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableV2SMSEndpointSelector, 1)
	} else {
		atomic.StoreUint32(&obj._EnableV2SMSEndpointSelector, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableV2SMSEndpointSelector")
	}
	return nil
}
func (obj *DeviceRegistrationSMSCfg) SetEndpointsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeviceRegistrationSMSCfg.EndpointsEnabled", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._EndpointsEnabled, v, path)
}

func NewTokenManagerConfig() (_obj *TokenManagerConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &TokenManagerConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["disablefallbacktodbforaccesstokens"] = _obj.SetDisableFallbackToDBForAccessTokens
	_setters["enable"] = _obj.SetEnable
	_obj._EnableMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *TokenManagerConfig) Init() {
	newObj, _ := NewTokenManagerConfig()
	*obj = *newObj
}

func (obj *TokenManagerConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *TokenManagerConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.TokenManagerConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *TokenManagerConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *TokenManagerConfig) setDynamicField(v *config.TokenManagerConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "disablefallbacktodbforaccesstokens":
		return obj.SetDisableFallbackToDBForAccessTokens(v.DisableFallbackToDBForAccessTokens, true, nil)
	case "enable":
		return obj.SetEnable(v.Enable, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *TokenManagerConfig) setDynamicFields(v *config.TokenManagerConfig, dynamic bool, path []string) (err error) {

	err = obj.SetDisableFallbackToDBForAccessTokens(v.DisableFallbackToDBForAccessTokens, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnable(v.Enable, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *TokenManagerConfig) setStaticFields(v *config.TokenManagerConfig) error {

	obj._WhitelistedActorIds = v.WhitelistedActorIds
	obj._WhitelistedPhoneNumberHashes = v.WhitelistedPhoneNumberHashes
	return nil
}

func (obj *TokenManagerConfig) SetDisableFallbackToDBForAccessTokens(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TokenManagerConfig.DisableFallbackToDBForAccessTokens", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableFallbackToDBForAccessTokens, 1)
	} else {
		atomic.StoreUint32(&obj._DisableFallbackToDBForAccessTokens, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableFallbackToDBForAccessTokens")
	}
	return nil
}
func (obj *TokenManagerConfig) SetEnable(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *TokenManagerConfig.Enable", reflect.TypeOf(val))
	}
	obj._EnableMutex.Lock()
	defer obj._EnableMutex.Unlock()
	obj._Enable = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Enable")
	}
	return nil
}

func NewNetworthMcpConfig() (_obj *NetworthMcpConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &NetworthMcpConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablesessionsigning"] = _obj.SetEnableSessionSigning
	_setters["loginurl"] = _obj.SetLoginUrl
	_obj._LoginUrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *NetworthMcpConfig) Init() {
	newObj, _ := NewNetworthMcpConfig()
	*obj = *newObj
}

func (obj *NetworthMcpConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *NetworthMcpConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.NetworthMcpConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcpConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *NetworthMcpConfig) setDynamicField(v *config.NetworthMcpConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablesessionsigning":
		return obj.SetEnableSessionSigning(v.EnableSessionSigning, true, nil)
	case "loginurl":
		return obj.SetLoginUrl(v.LoginUrl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *NetworthMcpConfig) setDynamicFields(v *config.NetworthMcpConfig, dynamic bool, path []string) (err error) {

	err = obj.SetEnableSessionSigning(v.EnableSessionSigning, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLoginUrl(v.LoginUrl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *NetworthMcpConfig) setStaticFields(v *config.NetworthMcpConfig) error {

	return nil
}

func (obj *NetworthMcpConfig) SetEnableSessionSigning(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcpConfig.EnableSessionSigning", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableSessionSigning, 1)
	} else {
		atomic.StoreUint32(&obj._EnableSessionSigning, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableSessionSigning")
	}
	return nil
}
func (obj *NetworthMcpConfig) SetLoginUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcpConfig.LoginUrl", reflect.TypeOf(val))
	}
	obj._LoginUrlMutex.Lock()
	defer obj._LoginUrlMutex.Unlock()
	obj._LoginUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "LoginUrl")
	}
	return nil
}
