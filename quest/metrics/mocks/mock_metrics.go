// Code generated by MockGen. DO NOT EDIT.
// Source: quest/metrics/metrics.go

// Package mocks is a generated GoMock package.
package mocks

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	context "context"
	reflect "reflect"

	metrics "github.com/epifi/gamma/quest/metrics"
	gomock "github.com/golang/mock/gomock"
)

// MockResult is a mock of Result interface.
type MockResult struct {
	ctrl     *gomock.Controller
	recorder *MockResultMockRecorder
}

// MockResultMockRecorder is the mock recorder for MockResult.
type MockResultMockRecorder struct {
	mock *MockResult
}

// NewMockResult creates a new mock instance.
func NewMockResult(ctrl *gomock.Controller) *MockResult {
	mock := &MockResult{ctrl: ctrl}
	mock.recorder = &MockResultMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockResult) EXPECT() *MockResultMockRecorder {
	return m.recorder
}

// Rows mocks base method.
func (m *MockResult) Rows() (metrics.Rows, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Rows")
	ret0, _ := ret[0].(metrics.Rows)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Rows indicates an expected call of Rows.
func (mr *MockResultMockRecorder) Rows() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Rows", reflect.TypeOf((*MockResult)(nil).Rows))
}

// ScanRows mocks base method.
func (m *MockResult) ScanRows() ([][]any, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScanRows")
	ret0, _ := ret[0].([][]any)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScanRows indicates an expected call of ScanRows.
func (mr *MockResultMockRecorder) ScanRows() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScanRows", reflect.TypeOf((*MockResult)(nil).ScanRows))
}

// MockRows is a mock of Rows interface.
type MockRows struct {
	ctrl     *gomock.Controller
	recorder *MockRowsMockRecorder
}

// MockRowsMockRecorder is the mock recorder for MockRows.
type MockRowsMockRecorder struct {
	mock *MockRows
}

// NewMockRows creates a new mock instance.
func NewMockRows(ctrl *gomock.Controller) *MockRows {
	mock := &MockRows{ctrl: ctrl}
	mock.recorder = &MockRowsMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRows) EXPECT() *MockRowsMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockRows) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockRowsMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockRows)(nil).Close))
}

// Next mocks base method.
func (m *MockRows) Next() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Next")
	ret0, _ := ret[0].(bool)
	return ret0
}

// Next indicates an expected call of Next.
func (mr *MockRowsMockRecorder) Next() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Next", reflect.TypeOf((*MockRows)(nil).Next))
}

// NextRow mocks base method.
func (m *MockRows) NextRow() ([]any, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NextRow")
	ret0, _ := ret[0].([]any)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NextRow indicates an expected call of NextRow.
func (mr *MockRowsMockRecorder) NextRow() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NextRow", reflect.TypeOf((*MockRows)(nil).NextRow))
}

// MockDatasource is a mock of Datasource interface.
type MockDatasource struct {
	ctrl     *gomock.Controller
	recorder *MockDatasourceMockRecorder
}

// MockDatasourceMockRecorder is the mock recorder for MockDatasource.
type MockDatasourceMockRecorder struct {
	mock *MockDatasource
}

// NewMockDatasource creates a new mock instance.
func NewMockDatasource(ctrl *gomock.Controller) *MockDatasource {
	mock := &MockDatasource{ctrl: ctrl}
	mock.recorder = &MockDatasourceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDatasource) EXPECT() *MockDatasourceMockRecorder {
	return m.recorder
}

// Fetch mocks base method.
func (m *MockDatasource) Fetch(ctx context.Context, ownership commontypes.Ownership, query *metrics.Query, args ...any) (metrics.Result, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, ownership, query}
	for _, a := range args {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Fetch", varargs...)
	ret0, _ := ret[0].(metrics.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Fetch indicates an expected call of Fetch.
func (mr *MockDatasourceMockRecorder) Fetch(ctx, ownership, query interface{}, args ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, ownership, query}, args...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Fetch", reflect.TypeOf((*MockDatasource)(nil).Fetch), varargs...)
}

// PreDefinedQueries mocks base method.
func (m *MockDatasource) PreDefinedQueries(useAttemptID bool) *metrics.PreDefinedQueries {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PreDefinedQueries", useAttemptID)
	ret0, _ := ret[0].(*metrics.PreDefinedQueries)
	return ret0
}

// PreDefinedQueries indicates an expected call of PreDefinedQueries.
func (mr *MockDatasourceMockRecorder) PreDefinedQueries(useAttemptID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PreDefinedQueries", reflect.TypeOf((*MockDatasource)(nil).PreDefinedQueries), useAttemptID)
}
